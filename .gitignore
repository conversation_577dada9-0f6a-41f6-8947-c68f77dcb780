# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

.env
.env.debug
.env.oceanbase

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work
go.work.sum

# the result of the go build
backend/**/output*
output/*

# Files generated by IDEs
.idea/
*.iml

# Vim swap files
*.swp

# Vscode files
.vscode/settings.json
.vscode/launch.json

/patches
/oldimpl

.DS_Store

bin/*

docker/data/*

backend/static

node_modules
common/temp
.rush
.eslintcache


backend/conf/model/*.yaml
values-dev.yaml
**/conf/model_ark_doubao-seed-1.6.yaml

*.tsbuildinfo

