server {
    listen 80;
    server_name _;

    # Static resources
    location / {
        root   /usr/share/nginx/html/dist;
        try_files $uri $uri/ /index.html;
        index  index.html index.htm;
        autoindex on;
        autoindex_exact_size off;
        autoindex_localtime on;

        etag on;
        if_modified_since exact;
        add_header Cache-Control "no-cache";
        add_header Cache-Control "public, max-age=3600";
    }

    # API proxy
    location ~ ^/(api|v[1-3])/ {
        proxy_pass http://coze-server:8888;
        
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # SSL/TLS settings
        proxy_ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
        proxy_ssl_verify off;


        sub_filter ':8889' ':8888/local_storage';
        sub_filter 'minio:9000' '$http_host/local_storage';
        sub_filter_once off;
        sub_filter_types 'application/json' 'text/event-stream';
    }

    location /local_storage/ {
        if ($args ~* "(^|.*&)x-wf-file_name=[^&]*(&.*|$)") {
            set $args $1$2;
        }
        if ($args ~* "^x-wf-file_name=[^&]*$") {
            set $args "";
        }

        rewrite ^/local_storage/(.*)$ /$1 break;
        proxy_pass http://minio:9000;
        proxy_set_header Host minio:9000;
    }
}

# if you want to use https, you can uncomment the following code
# server {
#     listen 443 ssl;
#     server_name _;

#     ssl_certificate /etc/nginx/ssl/pem;
#     ssl_certificate_key /etc/nginx/ssl/key;
#     ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
#     ssl_prefer_server_ciphers on;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;

#     # Static resources
#     location / {
#         root   /usr/share/nginx/html;
#         try_files $uri $uri/ /index.html;
#         index  index.html index.htm;
#         autoindex on;
#         autoindex_exact_size off;
#         autoindex_localtime on;
#     }

#     # API proxy
#     location /api/ {
#         proxy_pass https://coze-server:8888;
        
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
        
#         # SSL/TLS settings
#         proxy_ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
#         proxy_ssl_verify off;
#     }
# }