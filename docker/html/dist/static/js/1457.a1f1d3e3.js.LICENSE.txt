/*!
 * Copyright 2025 ByteDance Ltd. and/or its affiliates
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*!
 * Portions of this file are modified from:
 * https://github.com/micromark/micromark-extension-math/blob/a271caac4e636325165803af13d60e6ea07201dd/dev/lib/math-flow.js
 * Copyright (c) 2020 Titus Wormer <<EMAIL>>
 * Licensed under the MIT License
 * https://github.com/micromark/micromark-extension-math
 */

/*!
 * Portions of this file are modified from:
 * https://github.com/micromark/micromark-extension-math/blob/a271caac4e636325165803af13d60e6ea07201dd/dev/lib/math-text.js
 * Copyright (c) 2020 Titus Wormer <<EMAIL>>
 * Licensed under the MIT License
 * https://github.com/micromark/micromark-extension-math
 */

/*! *****************************************************************************
Copyright (C) Microsoft. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */