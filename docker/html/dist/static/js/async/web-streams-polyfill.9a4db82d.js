/*! For license information please see web-streams-polyfill.9a4db82d.js.LICENSE.txt */
"use strict";(self.webpackChunk_coze_studio_app=self.webpackChunk_coze_studio_app||[]).push([["7499"],{985869:function(e,r,t){t.r(r),t.d(r,{createReadableStreamWrapper:function(){return h},createTransformStreamWrapper:function(){return R},createWrappingReadableSource:function(){return p},createWrappingTransformer:function(){return T},createWrappingWritableSink:function(){return S},createWritableStreamWrapper:function(){return g}});var n=function(e,r){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])})(e,r)};function o(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function t(){this.constructor=e}n(e,r),e.prototype=null===r?Object.create(r):(t.prototype=r.prototype,new t)}function i(e){if(!e)throw TypeError("Assertion failed")}function a(){}function u(e){return"object"==typeof e&&null!==e||"function"==typeof e}function l(e){if("function"!=typeof e)return!1;var r=!1;try{new e({start:function(){r=!0}})}catch(e){}return r}function s(e){return!!u(e)&&"function"==typeof e.getReader||!1}function c(e){return!!u(e)&&"function"==typeof e.getWriter||!1}function f(e){return!!(u(e)&&s(e.readable)&&c(e.writable))||!1}function d(e){try{return e.getReader({mode:"byob"}).releaseLock(),!0}catch(e){return!1}}function h(e){i(!!(l(r=e)&&s(new r))||!1);var r,t=function(e){try{return new e({type:"bytes"}),!0}catch(e){return!1}}(e);return function(r,n){var o=(void 0===n?{}:n).type;if("bytes"===(o=_(o))&&!t&&(o=void 0),r.constructor===e&&("bytes"!==o||d(r)))return r;if("bytes"===o){var i=p(r,{type:o});return new e(i)}var i=p(r);return new e(i)}}function p(e,r){var t,n=(void 0===r?{}:r).type;return i(s(e)),i(!1===e.locked),t="bytes"===(n=_(n))?new m(e):new y(e)}function _(e){var r=String(e);if("bytes"===r)return r;if(void 0===e)return e;throw RangeError("Invalid type is specified")}var b=function(){function e(e){this._underlyingReader=void 0,this._readerMode=void 0,this._readableStreamController=void 0,this._pendingRead=void 0,this._underlyingStream=e,this._attachDefaultReader()}return e.prototype.start=function(e){this._readableStreamController=e},e.prototype.cancel=function(e){return i(void 0!==this._underlyingReader),this._underlyingReader.cancel(e)},e.prototype._attachDefaultReader=function(){if("default"!==this._readerMode){this._detachReader();var e=this._underlyingStream.getReader();this._readerMode="default",this._attachReader(e)}},e.prototype._attachReader=function(e){var r=this;i(void 0===this._underlyingReader),this._underlyingReader=e;var t=this._underlyingReader.closed;if(!!t)t.then(function(){return r._finishPendingRead()}).then(function(){e===r._underlyingReader&&r._readableStreamController.close()},function(t){e===r._underlyingReader&&r._readableStreamController.error(t)}).catch(a)},e.prototype._detachReader=function(){if(void 0!==this._underlyingReader)this._underlyingReader.releaseLock(),this._underlyingReader=void 0,this._readerMode=void 0},e.prototype._pullWithDefaultReader=function(){var e=this;this._attachDefaultReader();var r=this._underlyingReader.read().then(function(r){var t=e._readableStreamController;r.done?e._tryClose():t.enqueue(r.value)});return this._setPendingRead(r),r},e.prototype._tryClose=function(){try{this._readableStreamController.close()}catch(e){}},e.prototype._setPendingRead=function(e){var r,t=this,n=function(){t._pendingRead===r&&(t._pendingRead=void 0)};this._pendingRead=r=e.then(n,n)},e.prototype._finishPendingRead=function(){var e=this;if(!!this._pendingRead){var r=function(){return e._finishPendingRead()};return this._pendingRead.then(r,r)}},e}(),y=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return o(r,e),r.prototype.pull=function(){return this._pullWithDefaultReader()},r}(b);function v(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}var m=function(e){function r(r){var t=this,n=d(r);return(t=e.call(this,r)||this)._supportsByob=n,t}return o(r,e),Object.defineProperty(r.prototype,"type",{get:function(){return"bytes"},enumerable:!1,configurable:!0}),r.prototype._attachByobReader=function(){if("byob"!==this._readerMode){i(this._supportsByob),this._detachReader();var e=this._underlyingStream.getReader({mode:"byob"});this._readerMode="byob",this._attachReader(e)}},r.prototype.pull=function(){if(this._supportsByob){var e=this._readableStreamController.byobRequest;if(e)return this._pullWithByobRequest(e)}return this._pullWithDefaultReader()},r.prototype._pullWithByobRequest=function(e){var r=this;this._attachByobReader();var t=new Uint8Array(e.view.byteLength),n=this._underlyingReader.read(t).then(function(t){r._readableStreamController,t.done?(r._tryClose(),e.respond(0)):(!function(e,r){var t=v(e);v(r).set(t,0)}(t.value,e.view),e.respond(t.value.byteLength))});return this._setPendingRead(n),n},r}(b);function g(e){var r;return i(!!(l(r=e)&&c(new r))||!1),function(r){return r.constructor===e?r:new e(S(r))}}function S(e){return i(c(e)),i(!1===e.locked),new w(e.getWriter())}var w=function(){function e(e){var r=this;this._writableStreamController=void 0,this._pendingWrite=void 0,this._state="writable",this._storedError=void 0,this._underlyingWriter=e,this._errorPromise=new Promise(function(e,t){r._errorPromiseReject=t}),this._errorPromise.catch(a)}return e.prototype.start=function(e){var r=this;this._writableStreamController=e,this._underlyingWriter.closed.then(function(){r._state="closed"}).catch(function(e){return r._finishErroring(e)})},e.prototype.write=function(e){var r=this,t=this._underlyingWriter;if(null===t.desiredSize)return t.ready;var n=t.write(e);n.catch(function(e){return r._finishErroring(e)}),t.ready.catch(function(e){return r._startErroring(e)});var o=Promise.race([n,this._errorPromise]);return this._setPendingWrite(o),o},e.prototype.close=function(){var e=this;return void 0===this._pendingWrite?this._underlyingWriter.close():this._finishPendingWrite().then(function(){return e.close()})},e.prototype.abort=function(e){if("errored"!==this._state)return this._underlyingWriter.abort(e)},e.prototype._setPendingWrite=function(e){var r,t=this,n=function(){t._pendingWrite===r&&(t._pendingWrite=void 0)};this._pendingWrite=r=e.then(n,n)},e.prototype._finishPendingWrite=function(){var e=this;if(void 0===this._pendingWrite)return Promise.resolve();var r=function(){return e._finishPendingWrite()};return this._pendingWrite.then(r,r)},e.prototype._startErroring=function(e){var r=this;if("writable"===this._state){this._state="erroring",this._storedError=e;var t=function(){return r._finishErroring(e)};void 0===this._pendingWrite?t():this._finishPendingWrite().then(t,t),this._writableStreamController.error(e)}},e.prototype._finishErroring=function(e){"writable"===this._state&&this._startErroring(e),"erroring"===this._state&&(this._state="errored",this._errorPromiseReject(this._storedError))},e}();function R(e){var r;return i(!!(l(r=e)&&f(new r))||!1),function(r){return r.constructor===e?r:new e(T(r))}}function T(e){i(f(e));var r,t=e.readable,n=e.writable;i(!1===t.locked),i(!1===n.locked);var o=t.getReader();try{r=n.getWriter()}catch(e){throw o.releaseLock(),e}return new P(o,r)}var P=function(){function e(e,r){var t=this;this._transformStreamController=void 0,this._onRead=function(e){if(!e.done)return t._transformStreamController.enqueue(e.value),t._reader.read().then(t._onRead)},this._onError=function(e){t._flushReject(e),t._transformStreamController.error(e),t._reader.cancel(e).catch(a),t._writer.abort(e).catch(a)},this._onTerminate=function(){t._flushResolve(),t._transformStreamController.terminate();var e=TypeError("TransformStream terminated");t._writer.abort(e).catch(a)},this._reader=e,this._writer=r,this._flushPromise=new Promise(function(e,r){t._flushResolve=e,t._flushReject=r})}return e.prototype.start=function(e){this._transformStreamController=e,this._reader.read().then(this._onRead).then(this._onTerminate,this._onError);var r=this._reader.closed;r&&r.then(this._onTerminate,this._onError)},e.prototype.transform=function(e){return this._writer.write(e)},e.prototype.flush=function(){var e=this;return this._writer.close().then(function(){return e._flushPromise})},e}()},962062:function(e,r,t){t.r(r),t.d(r,{ByteLengthQueuingStrategy:function(){return tf},CountQueuingStrategy:function(){return t_},ReadableByteStreamController:function(){return eC},ReadableStream:function(){return r7},ReadableStreamBYOBReader:function(){return e3},ReadableStreamBYOBRequest:function(){return eP},ReadableStreamDefaultController:function(){return rJ},ReadableStreamDefaultReader:function(){return eo},TransformStream:function(){return tv},TransformStreamDefaultController:function(){return tT},WritableStream:function(){return ri},WritableStreamDefaultController:function(){return rP},WritableStreamDefaultWriter:function(){return rv}});var n,o,i,a,u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:function(e){return"Symbol(".concat(e,")")};function l(e){var r="function"==typeof Symbol&&Symbol.iterator,t=r&&e[r],n=0;if(t)return t.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")}function s(e){return this instanceof s?(this.v=e,this):new s(e)}function c(){}function f(e){return"object"==typeof e&&null!==e||"function"==typeof e}"function"==typeof SuppressedError&&SuppressedError;function d(e,r){try{Object.defineProperty(e,"name",{value:r,configurable:!0})}catch(e){}}var h=Promise,p=Promise.prototype.then,_=Promise.reject.bind(h);function b(e){return new h(e)}function y(e){return b(function(r){return r(e)})}function v(e){return _(e)}function m(e,r,t){return p.call(e,r,t)}function g(e,r,t){m(m(e,r,t),void 0,c)}function S(e,r){g(e,r)}function w(e,r){g(e,void 0,r)}function R(e,r,t){return m(e,r,t)}function T(e){m(e,void 0,c)}var P=function(e){if("function"==typeof queueMicrotask)P=queueMicrotask;else{var r=y(void 0);P=function(e){return m(r,e)}}return P(e)};function C(e,r,t){if("function"!=typeof e)throw TypeError("Argument is not a function");return Function.prototype.apply.call(e,r,t)}function E(e,r,t){try{return y(C(e,r,t))}catch(e){return _(e)}}var q=function(){function e(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}return Object.defineProperty(e.prototype,"length",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.push=function(e){var r=this._back,t=r;16383===r._elements.length&&(t={_elements:[],_next:void 0}),r._elements.push(e),t!==r&&(this._back=t,r._next=t),++this._size},e.prototype.shift=function(){var e=this._front,r=e,t=this._cursor,n=t+1,o=e._elements,i=o[t];return 16384===n&&(r=e._next,n=0),--this._size,this._cursor=n,e!==r&&(this._front=r),o[t]=void 0,i},e.prototype.forEach=function(e){for(var r=this._cursor,t=this._front,n=t._elements;(r!==n.length||void 0!==t._next)&&(r!==n.length||(n=(t=t._next)._elements,r=0,0!==n.length));){;e(n[r]),++r}},e.prototype.peek=function(){var e=this._front,r=this._cursor;return e._elements[r]},e}(),j=u("[[AbortSteps]]"),O=u("[[ErrorSteps]]"),W=u("[[CancelSteps]]"),k=u("[[PullSteps]]"),A=u("[[ReleaseSteps]]");function z(e,r){e._ownerReadableStream=r,r._reader=e,"readable"===r._state?F(e):"closed"===r._state?function(e){F(e),D(e)}(e):function(e,r){F(e),x(e,r)}(e,r._storedError)}function B(e,r){return ti(e._ownerReadableStream,r)}function I(e){var r=e._ownerReadableStream;"readable"===r._state?x(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,r){var t,n;t=e,n=r,F(t),x(t,n)}(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),r._readableStreamController[A](),r._reader=void 0,e._ownerReadableStream=void 0}function L(e){return TypeError("Cannot "+e+" a stream using a released reader")}function F(e){e._closedPromise=b(function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t})}function M(e,r){F(e),x(e,r)}function x(e,r){if(void 0!==e._closedPromise_reject)T(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}function D(e){if(void 0!==e._closedPromise_resolve)e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}var N=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},Q=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function Y(e,r){var t;if(void 0!==e&&!("object"==typeof(t=e)||"function"==typeof t))throw TypeError("".concat(r," is not an object."))}function H(e,r){if("function"!=typeof e)throw TypeError("".concat(r," is not a function."))}function V(e,r){var t;if(!("object"==typeof(t=e)&&null!==t||"function"==typeof t))throw TypeError("".concat(r," is not an object."))}function U(e,r,t){if(void 0===e)throw TypeError("Parameter ".concat(r," is required in '").concat(t,"'."))}function G(e,r,t){if(void 0===e)throw TypeError("".concat(r," is required in '").concat(t,"'."))}function X(e){return Number(e)}function J(e){return 0===e?0:e}function K(e,r){var t,n,o=Number.MAX_SAFE_INTEGER,i=Number(e);if(!N(i=0===(t=i)?0:t))throw TypeError("".concat(r," is not a finite number"));if((i=0===(n=Q(i))?0:n)<0||i>o)throw TypeError("".concat(r," is outside the accepted range of ").concat(0," to ").concat(o,", inclusive"));return N(i)&&0!==i?i:0}function Z(e,r){if(!tn(e))throw TypeError("".concat(r," is not a ReadableStream."))}function $(e){return new eo(e)}function ee(e,r){e._reader._readRequests.push(r)}function er(e,r,t){var n=e._reader._readRequests.shift();t?n._closeSteps():n._chunkSteps(r)}function et(e){return e._reader._readRequests.length}function en(e){var r=e._reader;return!!(void 0!==r&&ei(r))||!1}var eo=function(){function e(e){if(U(e,1,"ReadableStreamDefaultReader"),Z(e,"First parameter"),to(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");z(this,e),this._readRequests=new q}return Object.defineProperty(e.prototype,"closed",{get:function(){if(!ei(this))return _(el("closed"));return this._closedPromise},enumerable:!1,configurable:!0}),e.prototype.cancel=function(e){if(void 0===e&&(e=void 0),!ei(this))return _(el("cancel"));if(void 0===this._ownerReadableStream)return _(L("cancel"));return B(this,e)},e.prototype.read=function(){if(!ei(this))return _(el("read"));if(void 0===this._ownerReadableStream)return _(L("read from"));var e,r,t=b(function(t,n){e=t,r=n});return ea(this,{_chunkSteps:function(r){return e({value:r,done:!1})},_closeSteps:function(){return e({value:void 0,done:!0})},_errorSteps:function(e){return r(e)}}),t},e.prototype.releaseLock=function(){if(!ei(this))throw el("releaseLock");if(void 0!==this._ownerReadableStream)(function(e){I(e),eu(e,TypeError("Reader was released"))})(this)},e}();function ei(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_readRequests"))&&e instanceof eo}function ea(e,r){var t=e._ownerReadableStream;t._disturbed=!0,"closed"===t._state?r._closeSteps():"errored"===t._state?r._errorSteps(t._storedError):t._readableStreamController[k](r)}Object.defineProperties(eo.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),d(eo.prototype.cancel,"cancel"),d(eo.prototype.read,"read"),d(eo.prototype.releaseLock,"releaseLock"),"symbol"==typeof u.toStringTag&&Object.defineProperty(eo.prototype,u.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});function eu(e,r){var t=e._readRequests;e._readRequests=new q,t.forEach(function(e){e._errorSteps(r)})}function el(e){return TypeError("ReadableStreamDefaultReader.prototype.".concat(e," can only be used on a ReadableStreamDefaultReader"))}"symbol"==typeof u.asyncIterator&&((i={})[u.asyncIterator]=function(){return this},Object.defineProperty(a=i,u.asyncIterator,{enumerable:!1}));var es=function(){function e(e,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=r}return e.prototype.next=function(){var e,r=this,t=function(){return r._nextSteps()};return this._ongoingPromise=this._ongoingPromise?(e=this._ongoingPromise,m(e,t,t)):t(),this._ongoingPromise},e.prototype.return=function(e){var r,t=this,n=function(){return t._returnSteps(e)};return this._ongoingPromise?(r=this._ongoingPromise,m(r,n,n)):n()},e.prototype._nextSteps=function(){var e,r,t=this;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});var n=this._reader,o=b(function(t,n){e=t,r=n});return ea(n,{_chunkSteps:function(r){t._ongoingPromise=void 0,P(function(){return e({value:r,done:!1})})},_closeSteps:function(){t._ongoingPromise=void 0,t._isFinished=!0,I(n),e({value:void 0,done:!0})},_errorSteps:function(e){t._ongoingPromise=void 0,t._isFinished=!0,I(n),r(e)}}),o},e.prototype._returnSteps=function(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;var r=this._reader;if(!this._preventCancel){var t,n=B(r,e);return I(r),m(n,function(){return{value:e,done:!0}},void 0)}return I(r),y({value:e,done:!0})},e}(),ec={next:function(){if(!ef(this))return _(ed("next"));return this._asyncIteratorImpl.next()},return:function(e){if(!ef(this))return _(ed("return"));return this._asyncIteratorImpl.return(e)}};void 0!==a&&Object.setPrototypeOf(ec,a);function ef(e){if(!f(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof es}catch(e){return!1}}function ed(e){return TypeError("ReadableStreamAsyncIterator.".concat(e," can only be used on a ReadableSteamAsyncIterator"))}var eh=Number.isNaN||function(e){return e!=e};function ep(e){return e.slice()}function e_(e,r,t,n,o){new Uint8Array(e).set(new Uint8Array(t,n,o),r)}var eb=function(e){return"function"==typeof e.transfer?eb=function(e){return e.transfer()}:"function"==typeof structuredClone?eb=function(e){return structuredClone(e,{transfer:[e]})}:eb=function(e){return e},eb(e)},ey=function(e){return(ey="boolean"==typeof e.detached?function(e){return e.detached}:function(e){return 0===e.byteLength})(e)};function ev(e,r,t){if(e.slice)return e.slice(r,t);var n=t-r,o=new ArrayBuffer(n);return e_(o,0,e,r,n),o}function em(e,r){var t=e[r];if(null!=t){if("function"!=typeof t)throw TypeError("".concat(String(r)," is not a function"));return t}}function eg(e){return new Uint8Array(ev(e.buffer,e.byteOffset,e.byteOffset+e.byteLength))}function eS(e){var r=e._queue.shift();return e._queueTotalSize-=r.size,e._queueTotalSize<0&&(e._queueTotalSize=0),r.value}function ew(e,r,t){var n;if(!(!("number"!=typeof(n=t)||eh(n))&&!(n<0))||t===1/0)throw RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:r,size:t}),e._queueTotalSize+=t}function eR(e){e._queue=new q,e._queueTotalSize=0}function eT(e){return e===DataView}var eP=function(){function e(){throw TypeError("Illegal constructor")}return Object.defineProperty(e.prototype,"view",{get:function(){if(!eq(this))throw e$("view");return this._view},enumerable:!1,configurable:!0}),e.prototype.respond=function(e){if(!eq(this))throw e$("respond");if(U(e,1,"respond"),e=K(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");if(ey(this._view.buffer))throw TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");eJ(this._associatedReadableByteStreamController,e)},e.prototype.respondWithNewView=function(e){if(!eq(this))throw e$("respondWithNewView");if(U(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");if(ey(e.buffer))throw TypeError("The given view's buffer has been detached and so cannot be used as a response");eK(this._associatedReadableByteStreamController,e)},e}();Object.defineProperties(eP.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),d(eP.prototype.respond,"respond"),d(eP.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof u.toStringTag&&Object.defineProperty(eP.prototype,u.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});var eC=function(){function e(){throw TypeError("Illegal constructor")}return Object.defineProperty(e.prototype,"byobRequest",{get:function(){if(!eE(this))throw e0("byobRequest");return eG(this)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"desiredSize",{get:function(){if(!eE(this))throw e0("desiredSize");return eX(this)},enumerable:!1,configurable:!0}),e.prototype.close=function(){if(!eE(this))throw e0("close");if(this._closeRequested)throw TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableByteStream._state;if("readable"!==e)throw TypeError("The stream (in ".concat(e," state) is not in the readable state and cannot be closed"));eY(this)},e.prototype.enqueue=function(e){if(!eE(this))throw e0("enqueue");if(U(e,1,"enqueue"),!ArrayBuffer.isView(e))throw TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw TypeError("stream is closed or draining");var r=this._controlledReadableByteStream._state;if("readable"!==r)throw TypeError("The stream (in ".concat(r," state) is not in the readable state and cannot be enqueued to"));eH(this,e)},e.prototype.error=function(e){if(void 0===e&&(e=void 0),!eE(this))throw e0("error");eV(this,e)},e.prototype[W]=function(e){eO(this),eR(this);var r=this._cancelAlgorithm(e);return eQ(this),r},e.prototype[k]=function(e){var r=this._controlledReadableByteStream;if(this._queueTotalSize>0){eU(this,e);return}var t=this._autoAllocateChunkSize;if(void 0!==t){var n=void 0;try{n=new ArrayBuffer(t)}catch(r){e._errorSteps(r);return}var o={buffer:n,bufferByteLength:t,byteOffset:0,byteLength:t,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}ee(r,e),ej(this)},e.prototype[A]=function(){if(this._pendingPullIntos.length>0){var e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new q,this._pendingPullIntos.push(e)}},e}();function eE(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream"))&&e instanceof eC}function eq(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController"))&&e instanceof eP}function ej(e){if(!!function(e){var r=e._controlledReadableByteStream;return"readable"===r._state&&!e._closeRequested&&!!e._started&&(!!(en(r)&&et(r)>0||e6(r)&&e2(r)>0||eX(e)>0)||!1)}(e)){if(e._pulling){e._pullAgain=!0;return}e._pulling=!0,g(e._pullAlgorithm(),function(){return e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,ej(e)),null},function(r){return eV(e,r),null})}}function eO(e){eM(e),e._pendingPullIntos=new q}function eW(e,r){var t=!1;"closed"===e._state&&(t=!0);var n=ek(r);"default"===r.readerType?er(e,n,t):function(e,r,t){var n=e._reader._readIntoRequests.shift();t?n._closeSteps(r):n._chunkSteps(r)}(e,n,t)}function ek(e){var r=e.bytesFilled,t=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,r/t)}function eA(e,r,t,n){e._queue.push({buffer:r,byteOffset:t,byteLength:n}),e._queueTotalSize+=n}function ez(e,r,t,n){var o;try{o=ev(r,t,t+n)}catch(r){throw eV(e,r),r}eA(e,o,0,n)}function eB(e,r){r.bytesFilled>0&&ez(e,r.buffer,r.byteOffset,r.bytesFilled),eN(e)}function eI(e,r){var t=Math.min(e._queueTotalSize,r.byteLength-r.bytesFilled),n=r.bytesFilled+t,o=t,i=!1,a=n%r.elementSize,u=n-a;u>=r.minimumFill&&(o=u-r.bytesFilled,i=!0);for(var l=e._queue;o>0;){var s=l.peek(),c=Math.min(o,s.byteLength),f=r.byteOffset+r.bytesFilled;e_(r.buffer,f,s.buffer,s.byteOffset,c),s.byteLength===c?l.shift():(s.byteOffset+=c,s.byteLength-=c),e._queueTotalSize-=c,eL(e,c,r),o-=c}return i}function eL(e,r,t){t.bytesFilled+=r}function eF(e){0===e._queueTotalSize&&e._closeRequested?(eQ(e),ta(e._controlledReadableByteStream)):ej(e)}function eM(e){if(null!==e._byobRequest)e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null}function ex(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;var r=e._pendingPullIntos.peek();eI(e,r)&&(eN(e),eW(e._controlledReadableByteStream,r))}}Object.defineProperties(eC.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),d(eC.prototype.close,"close"),d(eC.prototype.enqueue,"enqueue"),d(eC.prototype.error,"error"),"symbol"==typeof u.toStringTag&&Object.defineProperty(eC.prototype,u.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function eD(e,r){var t=e._pendingPullIntos.peek();eM(e),"closed"===e._controlledReadableByteStream._state?!function(e,r){"none"===r.readerType&&eN(e);var t=e._controlledReadableByteStream;if(e6(t))for(;e2(t)>0;)eW(t,eN(e))}(e,t):!function(e,r,t){if(eL(e,r,t),"none"===t.readerType){eB(e,t),ex(e);return}if(!(t.bytesFilled<t.minimumFill)){eN(e);var n=t.bytesFilled%t.elementSize;if(n>0){var o=t.byteOffset+t.bytesFilled;ez(e,t.buffer,o-n,n)}t.bytesFilled-=n,eW(e._controlledReadableByteStream,t),ex(e)}}(e,r,t),ej(e)}function eN(e){return e._pendingPullIntos.shift()}function eQ(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function eY(e){var r=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===r._state){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0){var t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){var n=TypeError("Insufficient bytes to fill elements in the given buffer");throw eV(e,n),n}}eQ(e),ta(r)}}function eH(e,r){var t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){var n=r.buffer,o=r.byteOffset,i=r.byteLength;if(ey(n))throw TypeError("chunk's buffer is detached and so cannot be enqueued");var a=eb(n);if(e._pendingPullIntos.length>0){var u=e._pendingPullIntos.peek();if(ey(u.buffer))throw TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");eM(e),u.buffer=eb(u.buffer),"none"===u.readerType&&eB(e,u)}en(t)?(!function(e){for(var r=e._controlledReadableByteStream._reader;r._readRequests.length>0;){if(0===e._queueTotalSize)return;eU(e,r._readRequests.shift())}}(e),0===et(t)?eA(e,a,o,i):(e._pendingPullIntos.length>0&&eN(e),er(t,new Uint8Array(a,o,i),!1))):e6(t)?(eA(e,a,o,i),ex(e)):eA(e,a,o,i),ej(e)}}function eV(e,r){var t=e._controlledReadableByteStream;if("readable"===t._state)eO(e),eR(e),eQ(e),tu(t,r)}function eU(e,r){var t=e._queue.shift();e._queueTotalSize-=t.byteLength,eF(e);var n=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);r._chunkSteps(n)}function eG(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){var r=e._pendingPullIntos.peek(),t=new Uint8Array(r.buffer,r.byteOffset+r.bytesFilled,r.byteLength-r.bytesFilled),n=Object.create(eP.prototype);(function(e,r,t){e._associatedReadableByteStreamController=r,e._view=t})(n,e,t),e._byobRequest=n}return e._byobRequest}function eX(e){var r=e._controlledReadableByteStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function eJ(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r)throw TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===r)throw TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(t.bytesFilled+r>t.byteLength)throw RangeError("bytesWritten out of range")}t.buffer=eb(t.buffer),eD(e,r)}function eK(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r.byteLength)throw TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===r.byteLength)throw TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(t.byteOffset+t.bytesFilled!==r.byteOffset)throw RangeError("The region specified by view does not match byobRequest");if(t.bufferByteLength!==r.buffer.byteLength)throw RangeError("The buffer of view has different capacity than byobRequest");if(t.bytesFilled+r.byteLength>t.byteLength)throw RangeError("The region specified by view is larger than byobRequest");var n=r.byteLength;t.buffer=eb(r.buffer),eD(e,n)}function eZ(e,r,t,n,o,i,a){r._controlledReadableByteStream=e,r._pullAgain=!1,r._pulling=!1,r._byobRequest=null,r._queue=r._queueTotalSize=void 0,eR(r),r._closeRequested=!1,r._started=!1,r._strategyHWM=i,r._pullAlgorithm=n,r._cancelAlgorithm=o,r._autoAllocateChunkSize=a,r._pendingPullIntos=new q,e._readableStreamController=r,g(y(t()),function(){return r._started=!0,ej(r),null},function(e){return eV(r,e),null})}function e$(e){return TypeError("ReadableStreamBYOBRequest.prototype.".concat(e," can only be used on a ReadableStreamBYOBRequest"))}function e0(e){return TypeError("ReadableByteStreamController.prototype.".concat(e," can only be used on a ReadableByteStreamController"))}function e1(e){return new e3(e)}function e8(e,r){e._reader._readIntoRequests.push(r)}function e2(e){return e._reader._readIntoRequests.length}function e6(e){var r=e._reader;return!!(void 0!==r&&e9(r))||!1}var e3=function(){function e(e){if(U(e,1,"ReadableStreamBYOBReader"),Z(e,"First parameter"),to(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");if(!eE(e._readableStreamController))throw TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");z(this,e),this._readIntoRequests=new q}return Object.defineProperty(e.prototype,"closed",{get:function(){if(!e9(this))return _(e7("closed"));return this._closedPromise},enumerable:!1,configurable:!0}),e.prototype.cancel=function(e){if(void 0===e&&(e=void 0),!e9(this))return _(e7("cancel"));if(void 0===this._ownerReadableStream)return _(L("cancel"));return B(this,e)},e.prototype.read=function(e,r){if(void 0===r&&(r={}),!e9(this))return _(e7("read"));if(!ArrayBuffer.isView(e))return _(TypeError("view must be an array buffer view"));if(0===e.byteLength)return _(TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return _(TypeError("view's buffer must have non-zero byteLength"));if(ey(e.buffer))return _(TypeError("view's buffer has been detached"));try{var t,n,o;t=r,n="options",Y(t,n),i={min:K(null!==(o=null==t?void 0:t.min)&&void 0!==o?o:1,"".concat(n," has member 'min' that"))}}catch(e){return _(e)}var i,a,u,l=i.min;if(0===l)return _(TypeError("options.min must be greater than 0"));if(eT(e.constructor)){if(l>e.byteLength)return _(RangeError("options.min must be less than or equal to view's byteLength"))}else if(l>e.length)return _(RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return _(L("read from"));var s=b(function(e,r){a=e,u=r});return e4(this,e,l,{_chunkSteps:function(e){return a({value:e,done:!1})},_closeSteps:function(e){return a({value:e,done:!0})},_errorSteps:function(e){return u(e)}}),s},e.prototype.releaseLock=function(){if(!e9(this))throw e7("releaseLock");if(void 0!==this._ownerReadableStream)(function(e){I(e),e5(e,TypeError("Reader was released"))})(this)},e}();function e9(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_readIntoRequests"))&&e instanceof e3}function e4(e,r,t,n){var o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?n._errorSteps(o._storedError):!function(e,r,t,n){var o,i,a=e._controlledReadableByteStream,u=r.constructor;var l=eT(o=u)?1:o.BYTES_PER_ELEMENT,s=r.byteOffset,c=r.byteLength;try{i=eb(r.buffer)}catch(e){n._errorSteps(e);return}var f={buffer:i,bufferByteLength:i.byteLength,byteOffset:s,byteLength:c,bytesFilled:0,minimumFill:t*l,elementSize:l,viewConstructor:u,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(f),e8(a,n);return}if("closed"===a._state){var d=new u(f.buffer,f.byteOffset,0);n._closeSteps(d);return}if(e._queueTotalSize>0){if(eI(e,f)){var h=ek(f);eF(e),n._chunkSteps(h);return}if(e._closeRequested){var p=TypeError("Insufficient bytes to fill elements in the given buffer");eV(e,p),n._errorSteps(p);return}}e._pendingPullIntos.push(f),e8(a,n),ej(e)}(o._readableStreamController,r,t,n)}Object.defineProperties(e3.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),d(e3.prototype.cancel,"cancel"),d(e3.prototype.read,"read"),d(e3.prototype.releaseLock,"releaseLock"),"symbol"==typeof u.toStringTag&&Object.defineProperty(e3.prototype,u.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});function e5(e,r){var t=e._readIntoRequests;e._readIntoRequests=new q,t.forEach(function(e){e._errorSteps(r)})}function e7(e){return TypeError("ReadableStreamBYOBReader.prototype.".concat(e," can only be used on a ReadableStreamBYOBReader"))}function re(e,r){var t=e.highWaterMark;if(void 0===t)return r;if(eh(t)||t<0)throw RangeError("Invalid highWaterMark");return t}function rr(e){var r=e.size;return r?r:function(){return 1}}function rt(e,r){Y(e,r);var t=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===t?void 0:X(t),size:void 0===n?void 0:function(e,r){return H(e,r),function(r){return X(e(r))}}(n,"".concat(r," has member 'size' that"))}}function rn(e,r){if(!rl(e))throw TypeError("".concat(r," is not a WritableStream."))}var ro="function"==typeof AbortController,ri=function(){function e(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:V(e,"First parameter");var t,n,o,i,a,u,l,s=rt(r,"Second parameter");var c=(n="First parameter",Y(t=e,n),o=null==t?void 0:t.abort,i=null==t?void 0:t.close,a=null==t?void 0:t.start,u=null==t?void 0:t.type,l=null==t?void 0:t.write,{abort:void 0===o?void 0:function(e,r,t){return H(e,t),function(t){return E(e,r,[t])}}(o,t,"".concat(n," has member 'abort' that")),close:void 0===i?void 0:function(e,r,t){return H(e,t),function(){return E(e,r,[])}}(i,t,"".concat(n," has member 'close' that")),start:void 0===a?void 0:function(e,r,t){return H(e,t),function(t){return C(e,r,[t])}}(a,t,"".concat(n," has member 'start' that")),write:void 0===l?void 0:function(e,r,t){return H(e,t),function(t,n){return E(e,r,[t,n])}}(l,t,"".concat(n," has member 'write' that")),type:u});if(ru(this),void 0!==c.type)throw RangeError("Invalid type is specified");var f=rr(s);(function(e,r,t,n){var o,i,a,u,l=Object.create(rP.prototype);o=void 0!==r.start?function(){return r.start(l)}:function(){},i=void 0!==r.write?function(e){return r.write(e,l)}:function(){return y(void 0)},a=void 0!==r.close?function(){return r.close()}:function(){return y(void 0)},rE(e,l,o,i,a,u=void 0!==r.abort?function(e){return r.abort(e)}:function(){return y(void 0)},t,n)})(this,c,re(s,1),f)}return Object.defineProperty(e.prototype,"locked",{get:function(){if(!rl(this))throw rz("locked");return rs(this)},enumerable:!1,configurable:!0}),e.prototype.abort=function(e){if(void 0===e&&(e=void 0),!rl(this))return _(rz("abort"));if(rs(this))return _(TypeError("Cannot abort a stream that already has a writer"));return rc(this,e)},e.prototype.close=function(){if(!rl(this))return _(rz("close"));if(rs(this))return _(TypeError("Cannot close a stream that already has a writer"));if(r_(this))return _(TypeError("Cannot close an already-closing stream"));return rf(this)},e.prototype.getWriter=function(){if(!rl(this))throw rz("getWriter");return function(e){return new rv(e)}(this)},e}();function ra(e){return new rv(e)}Object.defineProperties(ri.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),d(ri.prototype.abort,"abort"),d(ri.prototype.close,"close"),d(ri.prototype.getWriter,"getWriter"),"symbol"==typeof u.toStringTag&&Object.defineProperty(ri.prototype,u.toStringTag,{value:"WritableStream",configurable:!0});function ru(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new q,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function rl(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_writableStreamController"))&&e instanceof ri}function rs(e){return void 0!==e._writer&&!0}function rc(e,r){if("closed"===e._state||"errored"===e._state)return y(void 0);e._writableStreamController._abortReason=r,null===(t=e._writableStreamController._abortController)||void 0===t||t.abort(r);var t,n=e._state;if("closed"===n||"errored"===n)return y(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;var o=!1;"erroring"===n&&(o=!0,r=void 0);var i=b(function(t,n){e._pendingAbortRequest={_promise:void 0,_resolve:t,_reject:n,_reason:r,_wasAlreadyErroring:o}});return e._pendingAbortRequest._promise=i,!o&&rh(e,r),i}function rf(e){var r=e._state;if("closed"===r||"errored"===r)return _(TypeError("The stream (in ".concat(r," state) is not in the writable state and cannot be closed")));var t=b(function(r,t){e._closeRequest={_resolve:r,_reject:t}}),n=e._writer;return void 0!==n&&e._backpressure&&"writable"===r&&rV(n),function(e){ew(e,rT,0),rO(e)}(e._writableStreamController),t}function rd(e,r){if("writable"===e._state){rh(e,r);return}rp(e)}function rh(e,r){var t=e._writableStreamController;e._state="erroring",e._storedError=r;var n=e._writer;void 0!==n&&rS(n,r),!function(e){return(void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest)&&!0}(e)&&t._started&&rp(e)}function rp(e){e._state="errored",e._writableStreamController[O]();var r=e._storedError;if(e._writeRequests.forEach(function(e){e._reject(r)}),e._writeRequests=new q,void 0===e._pendingAbortRequest){rb(e);return}var t=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,t._wasAlreadyErroring){t._reject(r),rb(e);return}g(e._writableStreamController[j](t._reason),function(){return t._resolve(),rb(e),null},function(r){return t._reject(r),rb(e),null})}function r_(e){return(void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest)&&!0}function rb(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var r=e._writer;void 0!==r&&rx(r,e._storedError)}function ry(e,r){var t=e._writer;void 0!==t&&r!==e._backpressure&&(r?function(e){rN(e)}(t):rV(t)),e._backpressure=r}var rv=function(){function e(e){if(U(e,1,"WritableStreamDefaultWriter"),rn(e,"First parameter"),rs(e))throw TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;var r=e._state;if("writable"===r)!r_(e)&&e._backpressure?rN(this):function(e){rN(e),rV(e)}(this),rF(this);else if("erroring"===r)rQ(this,e._storedError),rF(this);else if("closed"===r)(function(e){rN(e),rV(e)})(this),function(e){rF(e),rD(e)}(this);else{var t=e._storedError;rQ(this,t),function(e,r){rF(e),rx(e,r)}(this,t)}}return Object.defineProperty(e.prototype,"closed",{get:function(){if(!rm(this))return _(rI("closed"));return this._closedPromise},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"desiredSize",{get:function(){if(!rm(this))throw rI("desiredSize");if(void 0===this._ownerWritableStream)throw rL("desiredSize");return function(e){var r=e._ownerWritableStream,t=r._state;return"errored"===t||"erroring"===t?null:"closed"===t?0:rj(r._writableStreamController)}(this)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ready",{get:function(){if(!rm(this))return _(rI("ready"));return this._readyPromise},enumerable:!1,configurable:!0}),e.prototype.abort=function(e){if(void 0===e&&(e=void 0),!rm(this))return _(rI("abort"));if(void 0===this._ownerWritableStream)return _(rL("abort"));return function(e,r){return rc(e._ownerWritableStream,r)}(this,e)},e.prototype.close=function(){if(!rm(this))return _(rI("close"));var e=this._ownerWritableStream;if(void 0===e)return _(rL("close"));if(r_(e))return _(TypeError("Cannot close an already-closing stream"));return rg(this)},e.prototype.releaseLock=function(){if(!rm(this))throw rI("releaseLock");if(void 0!==this._ownerWritableStream)rw(this)},e.prototype.write=function(e){if(void 0===e&&(e=void 0),!rm(this))return _(rI("write"));if(void 0===this._ownerWritableStream)return _(rL("write to"));return rR(this,e)},e}();function rm(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream"))&&e instanceof rv}Object.defineProperties(rv.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),d(rv.prototype.abort,"abort"),d(rv.prototype.close,"close"),d(rv.prototype.releaseLock,"releaseLock"),d(rv.prototype.write,"write"),"symbol"==typeof u.toStringTag&&Object.defineProperty(rv.prototype,u.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});function rg(e){return rf(e._ownerWritableStream)}function rS(e,r){"pending"===e._readyPromiseState?rH(e,r):function(e,r){rQ(e,r)}(e,r)}function rw(e){var r,t,n=e._ownerWritableStream,o=TypeError("Writer was released and can no longer be used to monitor the stream's closedness");rS(e,o),r=e,t=o,"pending"===r._closedPromiseState?rx(r,t):function(e,r){var t,n;t=e,n=r,rF(t),rx(t,n)}(r,t),n._writer=void 0,e._ownerWritableStream=void 0}function rR(e,r){var t,n=e._ownerWritableStream,o=n._writableStreamController,i=function(e,r){try{return e._strategySizeAlgorithm(r)}catch(r){return rW(e,r),1}}(o,r);if(n!==e._ownerWritableStream)return _(rL("write to"));var a=n._state;if("errored"===a)return _(n._storedError);if(r_(n)||"closed"===a)return _(TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return _(n._storedError);var u=(t=n,b(function(e,r){t._writeRequests.push({_resolve:e,_reject:r})}));return function(e,r,t){try{ew(e,r,t)}catch(r){rW(e,r);return}var n=e._controlledWritableStream;!r_(n)&&"writable"===n._state&&ry(n,rk(e)),rO(e)}(o,r,i),u}var rT={},rP=function(){function e(){throw TypeError("Illegal constructor")}return Object.defineProperty(e.prototype,"abortReason",{get:function(){if(!rC(this))throw rB("abortReason");return this._abortReason},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"signal",{get:function(){if(!rC(this))throw rB("signal");if(void 0===this._abortController)throw TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal},enumerable:!1,configurable:!0}),e.prototype.error=function(e){if(void 0===e&&(e=void 0),!rC(this))throw rB("error");if("writable"===this._controlledWritableStream._state)rA(this,e)},e.prototype[j]=function(e){var r=this._abortAlgorithm(e);return rq(this),r},e.prototype[O]=function(){eR(this)},e}();function rC(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream"))&&e instanceof rP}function rE(e,r,t,n,o,i,a,u){r._controlledWritableStream=e,e._writableStreamController=r,r._queue=void 0,r._queueTotalSize=void 0,eR(r),r._abortReason=void 0,r._abortController=function(){if(ro)return new AbortController}(),r._started=!1,r._strategySizeAlgorithm=u,r._strategyHWM=a,r._writeAlgorithm=n,r._closeAlgorithm=o,r._abortAlgorithm=i,ry(e,function(e){return 0>=rj(e)}(r)),g(y(t()),function(){return r._started=!0,rO(r),null},function(t){return r._started=!0,rd(e,t),null})}Object.defineProperties(rP.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof u.toStringTag&&Object.defineProperty(rP.prototype,u.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function rq(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function rj(e){return e._strategyHWM-e._queueTotalSize}function rO(e){var r=e._controlledWritableStream;if(!e._started||void 0!==r._inFlightWriteRequest)return;if("erroring"===r._state){rp(r);return}if(0!==e._queue.length){var t=e._queue.peek().value;t===rT?function(e){var r,t=e._controlledWritableStream;(r=t)._inFlightCloseRequest=r._closeRequest,r._closeRequest=void 0,eS(e);var n=e._closeAlgorithm();rq(e),g(n,function(){var e,r;return(e=t)._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed",void 0!==(r=e._writer)&&rD(r),null},function(e){var r,n;return r=t,n=e,r._inFlightCloseRequest._reject(n),r._inFlightCloseRequest=void 0,void 0!==r._pendingAbortRequest&&(r._pendingAbortRequest._reject(n),r._pendingAbortRequest=void 0),rd(r,n),null})}(e):function(e,r){var t,n=e._controlledWritableStream;(t=n)._inFlightWriteRequest=t._writeRequests.shift(),g(e._writeAlgorithm(r),function(){(r=n)._inFlightWriteRequest._resolve(void 0),r._inFlightWriteRequest=void 0;var r,t=n._state;return eS(e),!r_(n)&&"writable"===t&&ry(n,rk(e)),rO(e),null},function(r){var t,o;return"writable"===n._state&&rq(e),t=n,o=r,t._inFlightWriteRequest._reject(o),t._inFlightWriteRequest=void 0,rd(t,o),null})}(e,t)}}function rW(e,r){"writable"===e._controlledWritableStream._state&&rA(e,r)}function rk(e){return 0>=rj(e)}function rA(e,r){var t=e._controlledWritableStream;rq(e),rh(t,r)}function rz(e){return TypeError("WritableStream.prototype.".concat(e," can only be used on a WritableStream"))}function rB(e){return TypeError("WritableStreamDefaultController.prototype.".concat(e," can only be used on a WritableStreamDefaultController"))}function rI(e){return TypeError("WritableStreamDefaultWriter.prototype.".concat(e," can only be used on a WritableStreamDefaultWriter"))}function rL(e){return TypeError("Cannot "+e+" a stream using a released writer")}function rF(e){e._closedPromise=b(function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t,e._closedPromiseState="pending"})}function rM(e,r){rF(e),rx(e,r)}function rx(e,r){if(void 0!==e._closedPromise_reject)T(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected"}function rD(e){if(void 0!==e._closedPromise_resolve)e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved"}function rN(e){e._readyPromise=b(function(r,t){e._readyPromise_resolve=r,e._readyPromise_reject=t}),e._readyPromiseState="pending"}function rQ(e,r){rN(e),rH(e,r)}function rY(e){rN(e),rV(e)}function rH(e,r){if(void 0!==e._readyPromise_reject)T(e._readyPromise),e._readyPromise_reject(r),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected"}function rV(e){if(void 0!==e._readyPromise_resolve)e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled"}var rU=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof global)return global}();var rG=(!function(e){if(!("function"==typeof e||"object"==typeof e)||"DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(n=null==rU?void 0:rU.DOMException)?void 0:n)||(d(o=function(e,r){this.message=e||"",this.name=r||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)},"DOMException"),o.prototype=Object.create(Error.prototype),Object.defineProperty(o.prototype,"constructor",{value:o,writable:!0,configurable:!0}),o);function rX(e,r,t,n,o,i){var a=$(e),u=new rv(r);e._disturbed=!0;var l=!1,s=y(void 0);return b(function(f,d){var h;if(void 0!==i){if(h=function(){var t=void 0!==i.reason?i.reason:new rG("Aborted","AbortError"),a=[];!n&&a.push(function(){return"writable"===r._state?rc(r,t):y(void 0)}),!o&&a.push(function(){return"readable"===e._state?ti(e,t):y(void 0)}),R(function(){return Promise.all(a.map(function(e){return e()}))},!0,t)},i.aborted){h();return}i.addEventListener("abort",h)}if(S(e,a._closedPromise,function(e){return n?P(!0,e):R(function(){return rc(r,e)},!0,e),null}),S(r,u._closedPromise,function(r){return o?P(!0,r):R(function(){return ti(e,r)},!0,r),null}),function(e,r,t){if("closed"===e._state)t();else g(r,t)}(e,a._closedPromise,function(){return t?P():R(function(){return function(e){var r=e._ownerWritableStream,t=r._state;if(r_(r)||"closed"===t)return y(void 0);if("errored"===t)return _(r._storedError);return rg(e)}(u)}),null}),r_(r)||"closed"===r._state){var p=TypeError("the destination writable stream closed before all data could be piped to it");o?P(!0,p):R(function(){return ti(e,p)},!0,p)}function v(){var e=s;return m(s,function(){return e!==s?v():void 0})}function S(e,r,t){"errored"===e._state?t(e._storedError):w(r,t)}T(b(function(e,r){!function t(n){n?e():m(function(){return l?y(!0):m(u._readyPromise,function(){return b(function(e,r){ea(a,{_chunkSteps:function(r){s=m(rR(u,r),void 0,c),e(!1)},_closeSteps:function(){return e(!0)},_errorSteps:r})})})}(),t,r)}(!1)}));function R(e,t,n){if(!l)if(l=!0,"writable"!==r._state||r_(r))i();else{var o;o=v(),g(o,i)}function i(){return g(e(),function(){return C(t,n)},function(e){return C(!0,e)}),null}}function P(e,t){if(!l)if(l=!0,"writable"!==r._state||r_(r))C(e,t);else{var n;n=v(),g(n,function(){return C(e,t)})}}function C(e,r){return rw(u),I(a),void 0!==i&&i.removeEventListener("abort",h),e?d(r):f(void 0),null}})}var rJ=function(){function e(){throw TypeError("Illegal constructor")}return Object.defineProperty(e.prototype,"desiredSize",{get:function(){if(!rK(this))throw r4("desiredSize");return r6(this)},enumerable:!1,configurable:!0}),e.prototype.close=function(){if(!rK(this))throw r4("close");if(!r3(this))throw TypeError("The stream is not in a state that permits close");r1(this)},e.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!rK(this))throw r4("enqueue");if(!r3(this))throw TypeError("The stream is not in a state that permits enqueue");return r8(this,e)},e.prototype.error=function(e){if(void 0===e&&(e=void 0),!rK(this))throw r4("error");r2(this,e)},e.prototype[W]=function(e){eR(this);var r=this._cancelAlgorithm(e);return r0(this),r},e.prototype[k]=function(e){var r=this._controlledReadableStream;if(this._queue.length>0){var t=eS(this);this._closeRequested&&0===this._queue.length?(r0(this),ta(r)):rZ(this),e._chunkSteps(t)}else ee(r,e),rZ(this)},e.prototype[A]=function(){},e}();function rK(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream"))&&e instanceof rJ}function rZ(e){if(!!r$(e)){if(e._pulling){e._pullAgain=!0;return}e._pulling=!0,g(e._pullAlgorithm(),function(){return e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,rZ(e)),null},function(r){return r2(e,r),null})}}function r$(e){var r=e._controlledReadableStream;return!!r3(e)&&!!e._started&&(!!(to(r)&&et(r)>0||r6(e)>0)||!1)}function r0(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function r1(e){if(!!r3(e)){var r=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(r0(e),ta(r))}}function r8(e,r){if(!!r3(e)){var t=e._controlledReadableStream;if(to(t)&&et(t)>0)er(t,r,!1);else{var n=void 0;try{n=e._strategySizeAlgorithm(r)}catch(r){throw r2(e,r),r}try{ew(e,r,n)}catch(r){throw r2(e,r),r}}rZ(e)}}function r2(e,r){var t=e._controlledReadableStream;if("readable"===t._state)eR(e),r0(e),tu(t,r)}function r6(e){var r=e._controlledReadableStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}Object.defineProperties(rJ.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),d(rJ.prototype.close,"close"),d(rJ.prototype.enqueue,"enqueue"),d(rJ.prototype.error,"error"),"symbol"==typeof u.toStringTag&&Object.defineProperty(rJ.prototype,u.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});function r3(e){var r=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===r&&!0}function r9(e,r,t,n,o,i,a){r._controlledReadableStream=e,r._queue=void 0,r._queueTotalSize=void 0,eR(r),r._started=!1,r._closeRequested=!1,r._pullAgain=!1,r._pulling=!1,r._strategySizeAlgorithm=a,r._strategyHWM=i,r._pullAlgorithm=n,r._cancelAlgorithm=o,e._readableStreamController=r,g(y(t()),function(){return r._started=!0,rZ(r),null},function(e){return r2(r,e),null})}function r4(e){return TypeError("ReadableStreamDefaultController.prototype.".concat(e," can only be used on a ReadableStreamDefaultController"))}function r5(e,r){Y(e,r);var t=null==e?void 0:e.preventAbort,n=null==e?void 0:e.preventCancel,o=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,r){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw TypeError("".concat(r," is not an AbortSignal."))}(i,"".concat(r," has member 'signal' that")),{preventAbort:!!t,preventCancel:!!n,preventClose:!!o,signal:i}}var r7=function(){function e(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:V(e,"First parameter");var t=rt(r,"Second parameter");var n=(a="First parameter",Y(i=e,a),u=null==i?void 0:i.autoAllocateChunkSize,l=null==i?void 0:i.cancel,s=null==i?void 0:i.pull,c=null==i?void 0:i.start,f=null==i?void 0:i.type,{autoAllocateChunkSize:void 0===u?void 0:K(u,"".concat(a," has member 'autoAllocateChunkSize' that")),cancel:void 0===l?void 0:function(e,r,t){return H(e,t),function(t){return E(e,r,[t])}}(l,i,"".concat(a," has member 'cancel' that")),pull:void 0===s?void 0:function(e,r,t){return H(e,t),function(t){return E(e,r,[t])}}(s,i,"".concat(a," has member 'pull' that")),start:void 0===c?void 0:function(e,r,t){return H(e,t),function(t){return C(e,r,[t])}}(c,i,"".concat(a," has member 'start' that")),type:void 0===f?void 0:function(e,r){if("bytes"!==(e="".concat(e)))throw TypeError("".concat(r," '").concat(e,"' is not a valid enumeration value for ReadableStreamType"));return e}(f,"".concat(a," has member 'type' that"))});if(tt(this),"bytes"===n.type){if(void 0!==t.size)throw RangeError("The strategy for a byte stream cannot have a size function");var o=re(t,0);!function(e,r,t){var n,o,i,a=Object.create(eC.prototype);n=void 0!==r.start?function(){return r.start(a)}:function(){},o=void 0!==r.pull?function(){return r.pull(a)}:function(){return y(void 0)},i=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return y(void 0)};var u=r.autoAllocateChunkSize;if(0===u)throw TypeError("autoAllocateChunkSize must be greater than 0");eZ(e,a,n,o,i,t,u)}(this,n,o)}else{var i,a,u,l,s,c,f,d,h,p,_,b,v,m,g,S=rr(t),o=re(t,1);d=this,h=n,p=o,_=S,g=Object.create(rJ.prototype),b=void 0!==h.start?function(){return h.start(g)}:function(){},v=void 0!==h.pull?function(){return h.pull(g)}:function(){return y(void 0)},r9(d,g,b,v,m=void 0!==h.cancel?function(e){return h.cancel(e)}:function(){return y(void 0)},p,_)}}return Object.defineProperty(e.prototype,"locked",{get:function(){if(!tn(this))throw tl("locked");return to(this)},enumerable:!1,configurable:!0}),e.prototype.cancel=function(e){if(void 0===e&&(e=void 0),!tn(this))return _(tl("cancel"));if(to(this))return _(TypeError("Cannot cancel a stream that already has a reader"));return ti(this,e)},e.prototype.getReader=function(e){var r,t,n,o;if(void 0===e&&(e=void 0),!tn(this))throw tl("getReader");if(void 0===(t="First parameter",Y(r=e,t),{mode:void 0===(n=null==r?void 0:r.mode)?void 0:function(e,r){if("byob"!==(e="".concat(e)))throw TypeError("".concat(r," '").concat(e,"' is not a valid enumeration value for ReadableStreamReaderMode"));return e}(n,"".concat(t," has member 'mode' that"))}).mode)return $(this);return o=this,new e3(o)},e.prototype.pipeThrough=function(e,r){if(void 0===r&&(r={}),!tn(this))throw tl("pipeThrough");U(e,1,"pipeThrough");var t,n,o,i,a=(n="First parameter",Y(t=e,n),G(o=null==t?void 0:t.readable,"readable","ReadableWritablePair"),Z(o,"".concat(n," has member 'readable' that")),G(i=null==t?void 0:t.writable,"writable","ReadableWritablePair"),rn(i,"".concat(n," has member 'writable' that")),{readable:o,writable:i}),u=r5(r,"Second parameter");if(to(this))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(rs(a.writable))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return T(rX(this,a.writable,u.preventClose,u.preventAbort,u.preventCancel,u.signal)),a.readable},e.prototype.pipeTo=function(e,r){var t;if(void 0===r&&(r={}),!tn(this))return _(tl("pipeTo"));if(void 0===e)return _("Parameter 1 is required in 'pipeTo'.");if(!rl(e))return _(TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));try{t=r5(r,"Second parameter")}catch(e){return _(e)}if(to(this))return _(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream"));if(rs(e))return _(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream"));return rX(this,e,t.preventClose,t.preventAbort,t.preventCancel,t.signal)},e.prototype.tee=function(){if(!tn(this))throw tl("tee");var e,r,t=(e=this,eE(e._readableStreamController)?function(e){var r,t,n,o,i,a=$(e),u=!1,l=!1,s=!1,c=!1,f=!1,d=b(function(e){i=e});function h(e){w(e._closedPromise,function(r){return e!==a?null:(eV(n._readableStreamController,r),eV(o._readableStreamController,r),(!c||!f)&&i(void 0),null)})}function p(){e9(a)&&(I(a),h(a=$(e)));ea(a,{_chunkSteps:function(r){P(function(){l=!1,s=!1;var t=r;if(!c&&!f)try{t=eg(r)}catch(r){eV(n._readableStreamController,r),eV(o._readableStreamController,r),i(ti(e,r));return}!c&&eH(n._readableStreamController,r),!f&&eH(o._readableStreamController,t),u=!1,l?v():s&&m()})},_closeSteps:function(){u=!1,!c&&eY(n._readableStreamController),!f&&eY(o._readableStreamController),n._readableStreamController._pendingPullIntos.length>0&&eJ(n._readableStreamController,0),o._readableStreamController._pendingPullIntos.length>0&&eJ(o._readableStreamController,0),(!c||!f)&&i(void 0)},_errorSteps:function(){u=!1}})}function _(r,t){if(ei(a))I(a),h(a=new e3(e));var d=t?o:n,p=t?n:o;e4(a,r,1,{_chunkSteps:function(r){P(function(){l=!1,s=!1;var n=t?f:c;if(t?c:f)!n&&eK(d._readableStreamController,r);else{var o=void 0;try{o=eg(r)}catch(r){eV(d._readableStreamController,r),eV(p._readableStreamController,r),i(ti(e,r));return}!n&&eK(d._readableStreamController,r),eH(p._readableStreamController,o)}u=!1,l?v():s&&m()})},_closeSteps:function(e){u=!1;var r=t?f:c,n=t?c:f;!r&&eY(d._readableStreamController),!n&&eY(p._readableStreamController),void 0!==e&&(!r&&eK(d._readableStreamController,e),!n&&p._readableStreamController._pendingPullIntos.length>0&&eJ(p._readableStreamController,0)),(!r||!n)&&i(void 0)},_errorSteps:function(){u=!1}})}function v(){if(u)return l=!0,y(void 0);u=!0;var e=eG(n._readableStreamController);return null===e?p():_(e._view,!1),y(void 0)}function m(){if(u)return s=!0,y(void 0);u=!0;var e=eG(o._readableStreamController);return null===e?p():_(e._view,!0),y(void 0)}function g(){}return n=tr(g,v,function(n){if(c=!0,r=n,f){var o=ti(e,ep([r,t]));i(o)}return d}),o=tr(g,m,function(n){if(f=!0,t=n,c){var o=ti(e,ep([r,t]));i(o)}return d}),h(a),[n,o]}(e):function(e,r){var t,n,o,i,a,u=$(e),l=!1,s=!1,c=!1,f=!1,d=b(function(e){a=e});function h(){return l?(s=!0,y(void 0)):(l=!0,ea(u,{_chunkSteps:function(e){P(function(){s=!1;!c&&r8(o._readableStreamController,e),!f&&r8(i._readableStreamController,e),l=!1,s&&h()})},_closeSteps:function(){l=!1,!c&&r1(o._readableStreamController),!f&&r1(i._readableStreamController),(!c||!f)&&a(void 0)},_errorSteps:function(){l=!1}}),y(void 0))}function p(){}return o=te(p,h,function(r){if(c=!0,t=r,f){var o=ti(e,ep([t,n]));a(o)}return d}),i=te(p,h,function(r){if(f=!0,n=r,c){var o=ti(e,ep([t,n]));a(o)}return d}),w(u._closedPromise,function(e){return r2(o._readableStreamController,e),r2(i._readableStreamController,e),(!c||!f)&&a(void 0),null}),[o,i]}(e));return ep(t)},e.prototype.values=function(e){if(void 0===e&&(e=void 0),!tn(this))throw tl("values");var r,t,n,o,i,a,u=(t="First parameter",Y(r=e,t),{preventCancel:!!(null==r?void 0:r.preventCancel)});return n=this,o=u.preventCancel,i=new es($(n),o),(a=Object.create(ec))._asyncIteratorImpl=i,a},e.from=function(e){var r,t;return f(t=r=e)&&void 0!==t.getReader?function(e){var r;return r=te(c,function(){var t,n;try{t=e.read()}catch(e){return _(e)}return m(t,function(e){if(!f(e))throw TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)r1(r._readableStreamController);else{var t=e.value;r8(r._readableStreamController,t)}},void 0)},function(r){try{return y(e.cancel(r))}catch(e){return _(e)}},0)}(r.getReader()):function(e){var r,t=function e(r,t,n){if(void 0===t&&(t="sync"),void 0===n){if("async"===t){if(void 0===(n=em(r,u.asyncIterator))){var o,i,a,c,d,h=em(r,u.iterator);return o=e(r,"sync",h),a=((i={})[u.iterator]=function(){return o.iterator},i),d=(c=function(){return function(e,r,t){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,o=t.apply(e,r||[]),i=[];return n={},a("next"),a("throw"),a("return"),n[Symbol.asyncIterator]=function(){return this},n;function a(e){o[e]&&(n[e]=function(r){return new Promise(function(t,n){i.push([e,r,t,n])>1||u(e,r)})})}function u(e,r){try{(function(e){e.value instanceof s?Promise.resolve(e.value.v).then(l,c):f(i[0][2],e)})(o[e](r))}catch(e){f(i[0][3],e)}}function l(e){u("next",e)}function c(e){u("throw",e)}function f(e,r){e(r),i.shift(),i.length&&u(i[0][0],i[0][1])}}(this,arguments,function(){return function(e,r){var t,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(u){return function(l){return function(u){if(t)throw TypeError("Generator is already executing.");for(;i&&(i=0,u[0]&&(a=0)),a;)try{if(t=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,n=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=r.call(e,a)}catch(e){u=[6,e],n=0}finally{t=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,l])}}}(this,function(e){switch(e.label){case 0:return[5,l(function(e){var r,t;return r={},n("next"),n("throw",function(e){throw e}),n("return"),r[Symbol.iterator]=function(){return this},r;function n(n,o){r[n]=e[n]?function(r){return(t=!t)?{value:s(e[n](r)),done:!1}:o?o(r):r}:o}}(function(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var r,t=e[Symbol.asyncIterator];return t?t.call(e):(e=l(e),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(t){r[t]=e[t]&&function(r){return new Promise(function(n,o){(function(e,r,t,n){Promise.resolve(n).then(function(r){e({value:r,done:t})},r)})(n,o,(r=e[t](r)).done,r.value)})}}}(a)))];case 1:case 2:return[4,s.apply(void 0,[e.sent()])];case 3:return[2,e.sent()]}})})}()).next,{iterator:c,nextMethod:d,done:!1}}}else n=em(r,u.iterator)}if(void 0===n)throw TypeError("The object is not iterable");var p=C(n,r,[]);if(!f(p))throw TypeError("The iterator method must return an object");var _=p.next;return{iterator:p,nextMethod:_,done:!1}}(e,"async");return r=te(c,function(){var e,n;try{e=function(e){var r=C(e.nextMethod,e.iterator,[]);if(!f(r))throw TypeError("The iterator.next() method must return an object");return r}(t)}catch(e){return _(e)}return m(y(e),function(e){if(!f(e))throw TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(e.done)r1(r._readableStreamController);else{var t=e.value;r8(r._readableStreamController,t)}},void 0)},function(e){var r,n,o,i=t.iterator;try{n=em(i,"return")}catch(e){return _(e)}if(void 0===n)return y(void 0);try{o=C(n,i,[e])}catch(e){return _(e)}return m(y(o),function(e){if(!f(e))throw TypeError("The promise returned by the iterator.return() method must fulfill with an object")},void 0)},0)}(r)},e}();function te(e,r,t,n,o){void 0===n&&(n=1),void 0===o&&(o=function(){return 1});var i=Object.create(r7.prototype);return tt(i),r9(i,Object.create(rJ.prototype),e,r,t,n,o),i}function tr(e,r,t){var n=Object.create(r7.prototype);return tt(n),eZ(n,Object.create(eC.prototype),e,r,t,0,void 0),n}function tt(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function tn(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_readableStreamController"))&&e instanceof r7}function to(e){return void 0!==e._reader&&!0}function ti(e,r){if(e._disturbed=!0,"closed"===e._state)return y(void 0);if("errored"===e._state)return _(e._storedError);ta(e);var t,n=e._reader;if(void 0!==n&&e9(n)){var o=n._readIntoRequests;n._readIntoRequests=new q,o.forEach(function(e){e._closeSteps(void 0)})}return m(e._readableStreamController[W](r),c,void 0)}function ta(e){e._state="closed";var r=e._reader;if(void 0!==r){if(D(r),ei(r)){var t=r._readRequests;r._readRequests=new q,t.forEach(function(e){e._closeSteps()})}}}function tu(e,r){e._state="errored",e._storedError=r;var t=e._reader;if(void 0!==t)x(t,r),ei(t)?eu(t,r):e5(t,r)}function tl(e){return TypeError("ReadableStream.prototype.".concat(e," can only be used on a ReadableStream"))}function ts(e,r){Y(e,r);var t=null==e?void 0:e.highWaterMark;return G(t,"highWaterMark","QueuingStrategyInit"),{highWaterMark:X(t)}}Object.defineProperties(r7,{from:{enumerable:!0}}),Object.defineProperties(r7.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),d(r7.from,"from"),d(r7.prototype.cancel,"cancel"),d(r7.prototype.getReader,"getReader"),d(r7.prototype.pipeThrough,"pipeThrough"),d(r7.prototype.pipeTo,"pipeTo"),d(r7.prototype.tee,"tee"),d(r7.prototype.values,"values"),"symbol"==typeof u.toStringTag&&Object.defineProperty(r7.prototype,u.toStringTag,{value:"ReadableStream",configurable:!0}),"symbol"==typeof u.asyncIterator&&Object.defineProperty(r7.prototype,u.asyncIterator,{value:r7.prototype.values,writable:!0,configurable:!0});var tc=function(e){return e.byteLength};d(tc,"size");var tf=function(){function e(e){U(e,1,"ByteLengthQueuingStrategy"),e=ts(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(e.prototype,"highWaterMark",{get:function(){if(!th(this))throw td("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"size",{get:function(){if(!th(this))throw td("size");return tc},enumerable:!1,configurable:!0}),e}();function td(e){return TypeError("ByteLengthQueuingStrategy.prototype.".concat(e," can only be used on a ByteLengthQueuingStrategy"))}function th(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark"))&&e instanceof tf}Object.defineProperties(tf.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof u.toStringTag&&Object.defineProperty(tf.prototype,u.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});var tp=function(){return 1};d(tp,"size");var t_=function(){function e(e){U(e,1,"CountQueuingStrategy"),e=ts(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(e.prototype,"highWaterMark",{get:function(){if(!ty(this))throw tb("highWaterMark");return this._countQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"size",{get:function(){if(!ty(this))throw tb("size");return tp},enumerable:!1,configurable:!0}),e}();function tb(e){return TypeError("CountQueuingStrategy.prototype.".concat(e," can only be used on a CountQueuingStrategy"))}function ty(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark"))&&e instanceof t_}Object.defineProperties(t_.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof u.toStringTag&&Object.defineProperty(t_.prototype,u.toStringTag,{value:"CountQueuingStrategy",configurable:!0});var tv=function(){function e(e,r,t){void 0===e&&(e={}),void 0===r&&(r={}),void 0===t&&(t={}),void 0===e&&(e=null);var n,o,i,a,u,l,s,c,f,d=rt(r,"Second parameter"),h=rt(t,"Third parameter");var p=(o="First parameter",Y(n=e,o),i=null==n?void 0:n.cancel,a=null==n?void 0:n.flush,u=null==n?void 0:n.readableType,l=null==n?void 0:n.start,s=null==n?void 0:n.transform,c=null==n?void 0:n.writableType,{cancel:void 0===i?void 0:function(e,r,t){return H(e,t),function(t){return E(e,r,[t])}}(i,n,"".concat(o," has member 'cancel' that")),flush:void 0===a?void 0:function(e,r,t){return H(e,t),function(t){return E(e,r,[t])}}(a,n,"".concat(o," has member 'flush' that")),readableType:u,start:void 0===l?void 0:function(e,r,t){return H(e,t),function(t){return C(e,r,[t])}}(l,n,"".concat(o," has member 'start' that")),transform:void 0===s?void 0:function(e,r,t){return H(e,t),function(t,n){return E(e,r,[t,n])}}(s,n,"".concat(o," has member 'transform' that")),writableType:c});if(void 0!==p.readableType)throw RangeError("Invalid readableType specified");if(void 0!==p.writableType)throw RangeError("Invalid writableType specified");var v=re(h,0),S=rr(h),w=re(d,1),R=rr(d);(function(e,r,t,n,o,i){var a,u,l,s,c,f,d;function h(){return r}e._writable=(a=h,u=function(r){return function(e,r){var t,n=e._transformStreamController;if(e._backpressure){;return m(e._backpressureChangePromise,function(){var t=e._writable;if("erroring"===t._state)throw t._storedError;return tq(n,r)},void 0)}return tq(n,r)}(e,r)},l=function(){return function(e){var r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;var t=e._readable;r._finishPromise=b(function(e,t){r._finishPromise_resolve=e,r._finishPromise_reject=t});var n=r._flushAlgorithm();return tC(r),g(n,function(){return"errored"===t._state?tW(r,t._storedError):(r1(t._readableStreamController),tO(r)),null},function(e){return r2(t._readableStreamController,e),tW(r,e),null}),r._finishPromise}(e)},s=function(r){return function(e,r){var t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;var n=e._readable;t._finishPromise=b(function(e,r){t._finishPromise_resolve=e,t._finishPromise_reject=r});var o=t._cancelAlgorithm(r);return tC(t),g(o,function(){return"errored"===n._state?tW(t,n._storedError):(r2(n._readableStreamController,r),tO(t)),null},function(e){return r2(n._readableStreamController,e),tW(t,e),null}),t._finishPromise}(e,r)},void 0===(c=t)&&(c=1),void 0===(f=n)&&(f=function(){return 1}),ru(d=Object.create(ri.prototype)),rE(d,Object.create(rP.prototype),a,u,l,s,c,f),d);e._readable=te(h,function(){return function(e){return tR(e,!1),e._backpressureChangePromise}(e)},function(r){return function(e,r){var t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;var n=e._writable;t._finishPromise=b(function(e,r){t._finishPromise_resolve=e,t._finishPromise_reject=r});var o=t._cancelAlgorithm(r);return tC(t),g(o,function(){return"errored"===n._state?tW(t,n._storedError):(rW(n._writableStreamController,r),tw(e),tO(t)),null},function(r){return rW(n._writableStreamController,r),tw(e),tW(t,r),null}),t._finishPromise}(e,r)},o,i),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,tR(e,!0),e._transformStreamController=void 0})(this,b(function(e){f=e}),w,R,v,S),function(e,r){var t,n,o,i,a,u,l,s,c=Object.create(tT.prototype);u=void 0!==r.transform?function(e){return r.transform(e,c)}:function(e){try{return tE(c,e),y(void 0)}catch(e){return _(e)}},l=void 0!==r.flush?function(){return r.flush(c)}:function(){return y(void 0)},s=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return y(void 0)},t=e,n=c,o=u,i=l,a=s,n._controlledTransformStream=t,t._transformStreamController=n,n._transformAlgorithm=o,n._flushAlgorithm=i,n._cancelAlgorithm=a,n._finishPromise=void 0,n._finishPromise_resolve=void 0,n._finishPromise_reject=void 0}(this,p),void 0!==p.start?f(p.start(this._transformStreamController)):f(void 0)}return Object.defineProperty(e.prototype,"readable",{get:function(){if(!tm(this))throw tk("readable");return this._readable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"writable",{get:function(){if(!tm(this))throw tk("writable");return this._writable},enumerable:!1,configurable:!0}),e}();Object.defineProperties(tv.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof u.toStringTag&&Object.defineProperty(tv.prototype,u.toStringTag,{value:"TransformStream",configurable:!0});function tm(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_transformStreamController"))&&e instanceof tv}function tg(e,r){r2(e._readable._readableStreamController,r),tS(e,r)}function tS(e,r){tC(e._transformStreamController),rW(e._writable._writableStreamController,r),tw(e)}function tw(e){e._backpressure&&tR(e,!1)}function tR(e,r){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=b(function(r){e._backpressureChangePromise_resolve=r}),e._backpressure=r}var tT=function(){function e(){throw TypeError("Illegal constructor")}return Object.defineProperty(e.prototype,"desiredSize",{get:function(){if(!tP(this))throw tj("desiredSize");return r6(this._controlledTransformStream._readable._readableStreamController)},enumerable:!1,configurable:!0}),e.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!tP(this))throw tj("enqueue");tE(this,e)},e.prototype.error=function(e){if(void 0===e&&(e=void 0),!tP(this))throw tj("error");(function(e,r){tg(e._controlledTransformStream,r)})(this,e)},e.prototype.terminate=function(){if(!tP(this))throw tj("terminate");(function(e){var r=e._controlledTransformStream;r1(r._readable._readableStreamController),tS(r,TypeError("TransformStream terminated"))})(this)},e}();function tP(e){return!!(f(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream"))&&e instanceof tT}Object.defineProperties(tT.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),d(tT.prototype.enqueue,"enqueue"),d(tT.prototype.error,"error"),d(tT.prototype.terminate,"terminate"),"symbol"==typeof u.toStringTag&&Object.defineProperty(tT.prototype,u.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});function tC(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function tE(e,r){var t=e._controlledTransformStream,n=t._readable._readableStreamController;if(!r3(n))throw TypeError("Readable side is not in a state that permits enqueue");try{r8(n,r)}catch(e){throw tS(t,e),t._readable._storedError}(!r$(n)&&!0)!==t._backpressure&&tR(t,!0)}function tq(e,r){return m(e._transformAlgorithm(r),void 0,function(r){throw tg(e._controlledTransformStream,r),r})}function tj(e){return TypeError("TransformStreamDefaultController.prototype.".concat(e," can only be used on a TransformStreamDefaultController"))}function tO(e){if(void 0!==e._finishPromise_resolve)e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0}function tW(e,r){if(void 0!==e._finishPromise_reject)T(e._finishPromise),e._finishPromise_reject(r),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0}function tk(e){return TypeError("TransformStream.prototype.".concat(e," can only be used on a TransformStream"))}}}]);
//# sourceMappingURL=web-streams-polyfill.9a4db82d.js.map