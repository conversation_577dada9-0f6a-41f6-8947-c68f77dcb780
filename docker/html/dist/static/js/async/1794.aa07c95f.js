"use strict";(self.webpackChunk_coze_studio_app=self.webpackChunk_coze_studio_app||[]).push([["1794"],{381677:function(e,o,t){t.d(o,{Tj:function(){return i.Tj},_u:function(){return i._u},cC:function(){return i.cC},e2:function(){return i.e2}});var i=t(910376)},179164:function(e,o,t){t.d(o,{DT:()=>oJ,zU:()=>oV,sp:()=>eJ,sU:()=>tb});var i,r,a=t("825955"),n=t("808549"),l=t("120454"),d=t("151064"),c=t("157728"),s=t("455069"),u=t("664319"),g=t("735114"),p=t("136515"),m=t("252237"),h=t.n(m),b=t("336205"),v=t("298203"),f=t("775699"),x=t("189284"),M=t("458949"),j=t("382534"),_=t("581259"),k=t("228962"),I=t("44172"),y=t("407821"),N=t.n(y),w=t("472772"),D=t.n(w),z=t("395245"),T=t.n(z),E=t("297998"),A=t.n(E),S=t("646576"),C=t.n(S),L=t("606121"),O=t.n(L),Z=t("746509"),F={};F.styleTagTransform=O(),F.setAttributes=A(),F.insert=T().bind(null,"head"),F.domAPI=D(),F.insertStyleElement=C(),N()(Z.Z,F);var Y=Z.Z&&Z.Z.locals?Z.Z.locals:void 0,{Text:B,Paragraph:Q}=f.ZT,U=e=>{e.stopPropagation()},P={[M.ji.Disable]:(0,d.jsx)(I.A$t,{}),[M.ji.Enable]:(0,d.jsx)(I.YOo,{}),[M.ji.Default]:(0,d.jsx)(I.$5C,{})},G=e=>{var o,t=(0,k.av)(),{checkPluginIsLockedByOthers:i,wrapWithCheckLock:r}=(0,k.RQ)((0,u.N)(e=>({checkPluginIsLockedByOthers:e.checkPluginIsLockedByOthers,wrapWithCheckLock:e.wrapWithCheckLock}))),[n]=(0,x.V)(),{targetSwitchId:l,setTargetSwitchId:c,loading:s,canEdit:g,refreshPage:p,plugin_id:m,pluginInfo:y,updatedInfo:N,handleIdeJump:w,showDropdownItem:D,setShowDropDownItem:z,setCurAPIInfo:T,openExample:E,projectId:A,customRender:S}=null!=e?e:{};var C=(o=(0,a._)(function*(e){var{apiId:o,disabled:t}=e;(yield j.Js.UpdateAPI({plugin_id:m||"",api_id:o,edit_version:null==y?void 0:y.edit_version,disabled:t}))&&p()}),function(e){return o.apply(this,arguments)});return{getColumns:()=>[{title:b.o.t("plugin_api_list_table_toolname"),dataIndex:"name",className:Y["min-width-200"],render:(e,o)=>(0,d.jsxs)("div",{children:[(0,d.jsx)(Q,{strong:!0,ellipsis:{showTooltip:{opts:{style:{wordBreak:"break-word"}}}},children:o.name}),(0,d.jsx)(Q,{className:Y["tool-table-desc"],ellipsis:{showTooltip:{opts:{style:{wordBreak:"break-word",maxWidth:"560px"},stopPropagation:!0}}},children:o.desc})]})},{title:b.o.t("plugin_api_list_table_Parameter"),dataIndex:"request_params",width:200,render:(e,o)=>{if(!o.request_params||(null===(t=o.request_params)||void 0===t?void 0:t.length)===0)return"-";var t,i,r,a=null===(i=o.request_params)||void 0===i?void 0:i.map(e=>({tagName:e.name,key:e.id})),n=null===(r=o.request_params)||void 0===r?void 0:r.map(e=>e.name);return(0,d.jsx)(f.Dj,{items:a,overflowRenderer:e=>e.length?(0,d.jsx)(f.u,{style:{wordBreak:"break-word"},content:null==n?void 0:n.join("、"),children:(0,d.jsxs)(f.fS,{color:"grey",size:"small",style:{fontVariantNumeric:"tabular-nums"},children:["+",e.length]})}):null,visibleItemRenderer:e=>(0,d.jsx)(f.fS,{color:"grey",size:"small",style:{marginRight:"8px",width:"fit-content",minWidth:"fit-content"},children:e.tagName},e.key),collapseFrom:"end"})}},{title:b.o.t("plugin_service_status"),dataIndex:"online_status",width:130,render:(e,o)=>{if(!("online_status"in o)||void 0===o.online_status)return"-";var t=_._5.get(o.online_status);return(0,d.jsxs)(f.T,{spacing:4,children:[(0,d.jsx)("span",{className:Y["circle-point"],style:{background:null==t?void 0:t.color}}),null==t?void 0:t.label]})}},{title:b.o.t("plugin_api_list_table_status"),dataIndex:"plugin_type",width:130,render:(e,o)=>{if(!("debug_status"in o))return"-";var t=_.CG.get(o.debug_status||M.vw.DebugWaiting);return(0,d.jsx)(f.fS,{color:null==t?void 0:t.color,children:null==t?void 0:t.label})}},{title:b.o.t("plugin_api_list_table_botUsing"),dataIndex:"statistic_data",width:100,render:(e,o)=>{var t;return(null===(t=o.statistic_data)||void 0===t?void 0:t.bot_quote)?(0,d.jsx)(B,{style:{color:"rgba(28, 29, 35, 0.60)"},ellipsis:{showTooltip:!0},children:(0,v.uf)(o.statistic_data.bot_quote)}):"-"}},{title:b.o.t("plugin_api_list_table_Create_time"),dataIndex:"create_time",width:150,sorter:!0,render:(e,o)=>o.create_time?(0,d.jsx)("div",{children:(0,v.p6)(Number(o.create_time),"YYYY-MM-DD HH:mm")}):"-"},{title:b.o.t("dataset_detail_tableTitle_enable"),dataIndex:"disabled",width:80,render:(e,o)=>{var t;return(0,d.jsx)("div",{style:{display:"flex"},onClick:e=>{U(e)},children:(0,d.jsx)(f.rs,{loading:l===o.api_id&&s,disabled:!g,checked:!(null==o?void 0:o.disabled),onChange:(t=(0,a._)(function*(e,t){if(z(void 0),t.stopPropagation(),!(yield i()))c(o.api_id||""),C({apiId:o.api_id||"",disabled:!e})}),function(e,o){return t.apply(this,arguments)})})})}},{title:b.o.t("plugin_api_list_table_action"),dataIndex:"action",width:215,render:(e,o)=>{var i,l,c=(null==o?void 0:null===(i=o.response_params)||void 0===i?void 0:i.length)===0||!(null==y?void 0:y.published)||(null==y?void 0:y.status)&&(null==N?void 0:N.created_api_names)&&!!N.created_api_names.includes((null==o?void 0:o.name)||"");return(0,d.jsx)("div",{onClick:e=>{U(e)},children:(0,d.jsxs)(f.T,{spacing:16,children:[(0,d.jsx)(f.u,{content:b.o.t("Edit"),children:(0,d.jsx)(f._3,{type:"secondary",disabled:!g,icon:(0,d.jsx)(I.pV2,{}),className:h()(!g&&Y["icon-btn-disable"]),onClick:r(()=>{z(void 0),w(_.jG.SELECT_TOOL,o.api_id)})})}),null==S?void 0:S({pluginInfo:y,pluginApiInfo:o,canEdit:g,setShowDropDownItem:z}),(null==y?void 0:y.creation_method)!==M.x9.IDE&&(0,d.jsx)(f.u,{content:b.o.t("plugin_api_list_table_debugicon_tooltip"),children:(0,d.jsx)(f._3,{type:"secondary",disabled:!g,icon:(0,d.jsx)(I.OlV,{}),className:h()(!g&&Y["debug-btn-disable"]),onClick:r(()=>{if(z(void 0),null==o?void 0:o.api_id){var e;null===(e=t.tool)||void 0===e||e.call(t,o.api_id,{toStep:"3"})}})})}),"debug_example_status"in o?(0,d.jsx)(f.u,{content:b.o.t("plugin_edit_tool_test_run_example_tip"),children:(0,d.jsx)(f._3,{type:"secondary",disabled:!g,icon:P[null!==(l=null==o?void 0:o.debug_example_status)&&void 0!==l?l:""],className:h()(!g&&Y["icon-example-disabled"]),onClick:r(()=>{z(void 0),(null==o?void 0:o.api_id)&&E(o)})})}):null,(0,d.jsx)(f.Lt,{position:"bottomRight",zIndex:1010,trigger:A?"hover":"custom",visible:o.api_id===(null==D?void 0:D.api_id),render:(0,d.jsxs)(f.Lt.Menu,{className:"px-[4px]",children:[n["bot.devops.plugin_import_export"]?(0,d.jsx)(f.Lt.Item,{disabled:(null==y?void 0:y.plugin_type)===M.zV.LOCAL||(null==y?void 0:y.creation_method)===M.x9.IDE,className:"rounded-[4px]",onClick:()=>{z(void 0),T(o)},children:b.o.t("code_snippet")}):null,n["bot.devops.plugin_mockset"]?(0,d.jsx)(f.Lt.Item,{className:"rounded-[4px]",disabled:c,onClick:()=>{if(z(void 0),null==o?void 0:o.api_id){var e;null===(e=t.mocksetList)||void 0===e||e.call(t,o.api_id)}},children:(0,d.jsx)(f.u,{position:"left",style:{display:c?"block":"none"},content:b.o.t("cannot_enable_mock_set_due_empty_return"),children:(0,d.jsx)("span",{children:b.o.t("manage_mockset")})})}):null,(0,d.jsx)(f.Lt.Item,{className:"rounded-[4px]",disabled:!g||(null==y?void 0:y.plugin_product_status)===M.Pt.Listed||(null==y?void 0:y.creation_method)===M.x9.IDE,children:(0,d.jsx)(f.u,{position:"left",style:{display:(null==y?void 0:y.plugin_product_status)===M.Pt.Listed?"block":"none"},content:b.o.t("mkpl_plugin_disable_delete"),children:(0,d.jsx)(f.gn,{style:{width:400},okType:"danger",trigger:"click",onVisibleChange:e=>{e?z(o):z(void 0)},onConfirm:r((0,a._)(function*(){yield j.Js.DeleteAPI({plugin_id:o.plugin_id||"",api_id:o.api_id||"",edit_version:null==y?void 0:y.edit_version}),p()})),title:b.o.t("project_plugin_delete_modal_title",{pluginName:o.name}),content:b.o.t("project_plugin_delete_modal_description"),okText:b.o.t("Remove"),cancelText:b.o.t("Cancel"),disabled:!g||(null==y?void 0:y.plugin_product_status)===M.Pt.Listed||(null==y?void 0:y.creation_method)===M.x9.IDE,children:b.o.t("delete_tool")})})})]}),children:(0,d.jsx)(f._3,{theme:"borderless",className:Y["icon-more"],icon:(0,d.jsx)(I.NKR,{}),onClick:()=>{D&&(null==D?void 0:D.api_id)===(null==o?void 0:o.api_id)?z(void 0):z(o)}})})]})})}}]}},R=e=>{var{getColumns:o}=G(e);return{getColumns:o}},V=t("297158"),W=t("659596"),H=t("335740"),q="version_desc",K="version_name",J=e=>(0,d.jsxs)(W.l0,(0,l._)((0,n._)({},e),{children:[(0,d.jsx)(W.l0.Input,{noErrorMessage:!0,field:K,label:b.o.t("plugin_publish_form_version"),rules:[{required:!0}],maxLength:40}),(0,d.jsx)(W.l0.TextArea,{noErrorMessage:!0,field:q,label:b.o.t("plugin_publish_form_version_desc"),rules:[{required:!0}],maxLength:800})]})),X=e=>{var o,t;return!e||!(null===(o=e.version_desc)||void 0===o?void 0:o.trim())||!(null===(t=e.version_name)||void 0===t?void 0:t.trim())},$=e=>{var{onClickPublish:o,className:t,style:i,publishButtonProps:r,initialVersionName:a}=e,c=(0,s.useRef)(),[u,g]=(0,s.useState)();return(0,d.jsxs)("div",{className:t,style:i,children:[(0,d.jsx)(J,{onValueChange:e=>{g((0,H.Z)(e))},getFormApi:e=>{c.current=e},initValues:{version_name:a}}),(0,d.jsx)(W.zx,(0,l._)((0,n._)({className:"w-full mt-16px",disabled:X(u),onClick:()=>{var e,t=null===(e=c.current)||void 0===e?void 0:e.getValues();if(!!t)o({versionDescValue:t})}},r),{children:"发布"}))]})},ee=e=>{var o,{children:t,pluginId:i,spaceId:r,isPluginHasPublished:l,visible:c,onClickOutside:s,onPublishSuccess:u,pluginInfo:p,isInLibraryScope:m}=e,h=(0,k.av)(),{data:v,refresh:f}=(0,g.Z)((0,a._)(function*(){if(!!r)return(yield j.Js.GetPluginNextVersion({space_id:r,plugin_id:i})).next_version_name}),{ready:m});var{run:x,loading:M}=(0,g.Z)((o=(0,a._)(function*(e){var{versionDescValue:o}=e;return yield j.Js.PublishPlugin((0,n._)({plugin_id:i},o))}),function(e){return o.apply(this,arguments)}),{manual:!0,onSuccess:()=>{var e;u(),W.FN.success({content:b.o.t("Plugin_publish_update_toast_success"),showClose:!1}),null===(e=h.toResource)||void 0===e||e.call(h,"plugin"),f()},onError:(e,o)=>{var[t]=o;V.kg.persist.error({eventName:"fail_to_publish_plugin",error:e})}});return(0,d.jsx)(W.J2,{visible:c,onClickOutSide:s,trigger:"custom",content:(0,d.jsx)($,{onClickPublish:x,className:"w-[400px] px-20px pt-16px pb-20px",publishButtonProps:{loading:M},initialVersionName:v}),children:t})},eo=t("824113"),et=t("487377"),ei=t("458620"),er=t("214942"),ea=t("727914"),en=t("577413"),el=t("900021"),ed=t("364112"),ec=t("818373"),es={};es.styleTagTransform=O(),es.setAttributes=A(),es.insert=T().bind(null,"head"),es.domAPI=D(),es.insertStyleElement=C(),N()(ec.Z,es);var eu=ec.Z&&ec.Z.locals?ec.Z.locals:void 0,eg=e=>{var{value:o,onValueChange:t}=e,[i,r]=(0,s.useState)(!1);return(0,d.jsxs)("div",{className:eu.checkbox,children:[(0,d.jsxs)("div",{className:eu.content,children:[(0,d.jsx)(f.u,{content:b.o.t("plugin_edit_tool_test_run_cancel_example"),visible:i,trigger:"custom",children:(0,d.jsx)(f.XZ,{onChange:e=>{t(e.target.checked),!e.target.checked&&r(!1)},checked:o,onMouseEnter:()=>o&&r(!0),onMouseLeave:()=>r(!1)})}),(0,d.jsx)("div",{className:eu.label,children:b.o.t("plugin_edit_tool_test_run_save_results_as_example")})]}),(0,d.jsx)("div",{className:eu.line})]})},ep=e=>{var o,{btnLoading:t,apiInfo:i,dugStatus:r,loading:n,nextStep:l,editVersion:c}=e,[u,p]=(0,s.useState)((null==i?void 0:i.debug_example_status)===M.ji.Enable),[m,h]=(0,s.useState)(),{loading:v,runAsync:x}=(0,g.Z)(e=>j.Js.UpdateAPI(e),{manual:!0}),{pluginInfo:_}=(0,k.RQ)(e=>({pluginInfo:e.pluginInfo}));var I=(o=(0,a._)(function*(e){var o,t;e.stopPropagation(),yield x({plugin_id:null!==(o=null==_?void 0:_.plugin_id)&&void 0!==o?o:"",api_id:null!==(t=null==i?void 0:i.api_id)&&void 0!==t?t:"",edit_version:null!=c?c:null==_?void 0:_.edit_version,save_example:u,debug_example:m}),f.O$.success(b.o.t("Save_success")),l()}),function(e){return o.apply(this,arguments)});return(0,s.useEffect)(()=>{p((null==i?void 0:i.debug_example_status)===M.ji.Enable),h((null==i?void 0:i.debug_example_status)===M.ji.Enable?null==i?void 0:i.debug_example:void 0)},[i]),{debugFooterNode:(0,d.jsxs)(f.T,{spacing:12,children:[(0,d.jsx)(eg,{value:u,onValueChange:p}),(0,d.jsx)(f.y3,{disabled:n||r!==el.Q.PASS&&(null==_?void 0:_.plugin_type)!==M.zV.LOCAL,style:{minWidth:98,margin:0},loading:t||v,type:"primary",theme:"solid",onClick:I,children:b.o.t("Create_newtool_s4_done")})]}),debugExample:m,setDebugExample:h}},em=e=>{var o,{visible:t,onCancel:i,apiInfo:r,pluginId:a,pluginName:n,onSave:l}=e,[c,u]=(0,s.useState)(el.Q.FAIL),{debugFooterNode:g,setDebugExample:p,debugExample:m}=ep({apiInfo:r,loading:!1,dugStatus:c,btnLoading:!1,nextStep:()=>{null==l||l(),u(void 0)}});return(0,d.jsx)(f.M5,{title:b.o.t("plugin_edit_tool_edit_example"),visible:t,width:1280,style:{height:"calc(100vh - 140px)",minWidth:"1040px"},centered:!0,onCancel:()=>{i(),u(void 0)},footer:(0,d.jsx)("div",{children:g}),children:r?(0,d.jsx)(ed.Debug,{disabled:!1,isViewExample:!0,setDebugStatus:u,pluginId:a,apiId:null!==(o=null==r?void 0:r.api_id)&&void 0!==o?o:"",apiInfo:r,pluginName:n,setDebugExample:p,debugExample:m}):null})},eh=t("849201"),eb=e=>{var o,t,i,{onUpdate:r}=e,[a,c]=(0,s.useState)(!1),[u,g]=(0,s.useState)(),{pluginInfo:p}=(0,k.RQ)(e=>({pluginInfo:e.pluginInfo})),m=()=>{c(!1)};return(0,s.useEffect)(()=>{!a&&g(void 0)},[a]),{exampleNode:(0,d.jsx)(em,{visible:a,onCancel:m,pluginId:null!==(t=null==p?void 0:p.plugin_id)&&void 0!==t?t:"",apiInfo:u,pluginName:null!==(i=null==p?void 0:null===(o=p.meta_info)||void 0===o?void 0:o.name)&&void 0!==i?i:"",onSave:()=>{null==r||r(),m()}}),openExample:e=>{var o;if(c(!0),(null==e?void 0:null===(o=e.debug_example)||void 0===o?void 0:o.req_example)&&(null==e?void 0:e.debug_example_status)===M.ji.Enable){var t,i,r=(0,H.Z)(null!==(i=null==e?void 0:e.request_params)&&void 0!==i?i:[]);(0,eh.C$)(r,JSON.parse(null==e?void 0:null===(t=e.debug_example)||void 0===t?void 0:t.req_example)),(0,en.eO)(r),g((0,l._)((0,n._)({},e),{request_params:r}))}else(0,en.eO)(e.request_params),g(e)}}},ev=t("805366");let ef=(0,ev.A)(function(e){return s.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},e),s.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 23a11 11 0 1 0 0-22 11 11 0 0 0 0 22Zm5.88-13.18-6.2 7.6a1.5 1.5 0 0 1-2.37 0l-3.5-4a1.5 1.5 0 1 1 2.37-1.84l2.3 2.46L15.5 8a1.5 1.5 0 1 1 2.38 1.82Z",fill:"currentColor"}))},"tick_circle"),ex=(0,ev.A)(function(e){return s.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},e),s.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 23a11 11 0 1 0 0-22 11 11 0 0 0 0 22Zm1.5-16.5v4.88l3.56 3.56a1.5 1.5 0 0 1-2.12 2.12l-4-4A1.5 1.5 0 0 1 10.5 12V6.5a1.5 1.5 0 0 1 3 0Z",fill:"currentColor"}))},"clock");var eM=t("873183"),ej=t("537845"),e_=()=>{var{pluginInfo:e,canEdit:o}=(0,k.RQ)((0,u.N)(e=>({pluginInfo:e.pluginInfo,canEdit:e.canEdit}))),{data:t,refresh:i}=(0,g.Z)((0,a._)(function*(){return yield j.Js.GetOAuthStatus({plugin_id:(null==e?void 0:e.plugin_id)||""})}),{refreshDeps:[e],ready:(null==e?void 0:e.plugin_id)!==void 0&&o,refreshOnWindowFocus:!0}),{run:r,loading:n}=(0,g.Z)((0,a._)(function*(){try{yield j.Js.RevokeAuthToken({plugin_id:(null==e?void 0:e.plugin_id)||""}),i()}catch(e){V.kg.error(e)}}),{manual:!0,ready:o}),l=(0,s.useMemo)(()=>null==t?void 0:t.is_oauth,[t]),d=(0,s.useMemo)(()=>(null==t?void 0:t.status)===M.OZ.Authorized,[t]),c=(0,s.useMemo)(()=>null==t?void 0:t.content,[t]),p=(0,ej.Z)((0,a._)(function*(){yield r()}));return{canEdit:o,needAuth:l,isHasAuth:d,doCancelOauth:p,isUpdateLoading:n,doOauth:(0,ej.Z)(()=>{window.open(c,"_blank")})}},ek=e=>{f.u_.info({title:b.o.t("plugin_tool_config_auth_modal_auth_required"),content:b.o.t("plugin_tool_config_auth_modal_auth_required_desc"),onOk:e,okText:b.o.t("Confirm"),cancelText:b.o.t("Cancel")})},eI=e=>{f.u_.warning({title:b.o.t("plugin_tool_config_auth_modal_cancel_confirmation"),content:b.o.t("plugin_tool_config_auth_modal_cancel_confirmation_desc"),onOk:e,okText:b.o.t("Confirm"),cancelText:b.o.t("Cancel")})},ey=()=>{var{needAuth:e,isHasAuth:o,doCancelOauth:t,isUpdateLoading:i,doOauth:r,canEdit:a}=e_();if(!a)return(0,d.jsx)(d.Fragment,{});var n=e&&o,l=e&&!o;return(0,d.jsxs)(W.T,{spacing:8,children:[e?(0,d.jsx)("span",{className:"rounded-[4px] bg-[#EDD5FC] px-[8px] py-[2px] text-[#6C2CC6] text-[12px] font-medium leading-[16px]",children:b.o.t("plugin_mark_created_by_existing_services")}):null,e?(0,d.jsx)(W.ZT.Text,{disabled:i,onClick:()=>{if(l){ek(r);return}n&&eI(t)},icon:o?(0,d.jsx)(eM.P03,{}):void 0,className:h()("overflow-hidden text-[#4C54F0] overflow-ellipsis text-[14px] font-normal leading-[20px]",(l||n)&&"cursor-pointer"),children:o?b.o.t("plugin_tool_config_status_authorized"):b.o.t("plugin_tool_config_status_unauthorized")}):null,!o&&e?(0,d.jsx)(eM.x5d,{className:"w-[12px] h-[12px] ml-[-6px]"}):null]})},eN=e=>{var{className:o}=e,{needAuth:t,isHasAuth:i,doCancelOauth:r,isUpdateLoading:a,doOauth:n,canEdit:l}=e_();return l?t?(0,d.jsx)(W.ZT.Text,{disabled:a,onClick:e=>{e.preventDefault(),e.stopPropagation(),console.log("click"),i?eI(r):ek(n)},icon:i?(0,d.jsx)(eM.P03,{}):void 0,className:"overflow-hidden text-[#4C54F0] overflow-ellipsis text-[14px] font-normal leading-[20px] cursor-pointer px-[12px] py-[0] items-center ".concat(o),children:i?b.o.t("plugin_tool_config_status_authorized"):b.o.t("plugin_tool_config_status_unauthorized")}):null:(0,d.jsx)(d.Fragment,{})};M.zV.APP,b.o.t("plugin_type_app"),M.zV.PLUGIN,b.o.t("plugin_type_plugin"),M.zV.FUNC,b.o.t("plugin_type_func"),M.zV.WORKFLOW,b.o.t("plugin_type_workflow");var ew=new Map([[!1,{label:b.o.t("Unpublished_1"),color:"var(--coz-fg-secondary)"}],[!0,{label:b.o.t("Published_1"),color:"var(--coz-fg-hglt-green)"}]]),eD=t("84089"),ez={};ez.styleTagTransform=O(),ez.setAttributes=A(),ez.insert=T().bind(null,"head"),ez.domAPI=D(),ez.insertStyleElement=C(),N()(eD.Z,ez);var eT=eD.Z&&eD.Z.locals?eD.Z.locals:void 0,{Text:eE}=W.ZT,eA=e=>{var o,t,i,r,a,n,l,{pluginInfo:c,loading:s,canEdit:u,extraRight:g,onClickEdit:p}=e;return(0,d.jsxs)(W.T,{className:h()(eT["plugin-detail-info"],"w-full","px-[16px]","py-[16px]","shrink-0","grow-0"),spacing:20,children:[(0,d.jsxs)(W.T,{style:{flex:1},spacing:12,children:[(0,d.jsx)(W.qE,{className:h()(eT["plugin-detail-avatar"]),size:"medium",shape:"square",src:null==c?void 0:null===(t=c.meta_info)||void 0===t?void 0:null===(o=t.icon)||void 0===o?void 0:o.url}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{children:(0,d.jsxs)(W.T,{spacing:4,className:"mb-1",children:[(0,d.jsx)(eE,{ellipsis:{showTooltip:{opts:{style:{wordBreak:"break-word"}}}},className:h()(eT["plugin-detail-title"]),children:null==c?void 0:null===(i=c.meta_info)||void 0===i?void 0:i.name}),s?null:(0,d.jsx)(W.hU,{icon:u?(0,d.jsx)(I.yl5,{}):(0,d.jsx)(I.ia5,{}),size:"small",color:"secondary",className:h()(eT["edit-plugin-btn"],{[eT.edit]:u}),onClick:p})]})}),(0,d.jsxs)(W.T,{spacing:4,children:[(0,d.jsx)(W.Vp,{size:"mini",color:"primary",children:(0,d.jsxs)(W.T,{spacing:2,children:[(null==c?void 0:c.published)?(0,d.jsx)(ef,{size:"small",style:{color:null===(r=ew.get(!!c.published))||void 0===r?void 0:r.color}}):(0,d.jsx)(ex,{size:"small",style:{color:null===(a=ew.get(!!(null==c?void 0:c.published)))||void 0===a?void 0:a.color}}),null===(n=ew.get(!!(null==c?void 0:c.published)))||void 0===n?void 0:n.label]})}),(null==c?void 0:c.creation_method)===M.x9.IDE&&(0,d.jsx)(W.Vp,{size:"mini",color:"purple",children:b.o.t("plugin_mark_created_by_ide")}),(0,d.jsx)(eE,{ellipsis:{showTooltip:{opts:{style:{wordBreak:"break-word",maxWidth:"560px"}}}},className:h()(eT["plugin-detail-desc"]),children:null==c?void 0:null===(l=c.meta_info)||void 0===l?void 0:l.desc}),(0,d.jsx)(ey,{})]})]})]}),g?(0,d.jsx)(W.T,{spacing:12,children:g}):null]})},eS=t("381677"),eC=t("610382"),eL=t("156133"),eO=t("947578"),eZ=t("252793"),eF=e=>{var o,{space_id:t,pluginId:i,apiId:r="",baseInfo:c={},setApiId:u,showModal:g,disabled:p,showSecurityCheckFailedMsg:m,setShowSecurityCheckFailedMsg:h,editVersion:v,showFunctionName:x=!1,pluginType:_,onSuccess:k,renderEnhancedComponent:I}=e,y=(0,s.useRef)(null),[N,w]=(0,s.useState)(void 0);(0,s.useEffect)(()=>{var e;w(null==c?void 0:c.desc),null===(e=y.current)||void 0===e||e.formApi.setValues({name:c.name,desc:c.desc})},[c.name,c.desc,g,p]);var D=(0,ej.Z)(e=>{var o;null===(o=y.current)||void 0===o||o.formApi.setValue("desc",e)});var z=(o=(0,a._)(function*(){if(!(yield null===(e=y.current)||void 0===e?void 0:e.formApi.validate().then(()=>!0).catch(()=>!1)))return!1;var e,o,t,a=null===(o=y.current)||void 0===o?void 0:o.formApi.getValues(),d={plugin_id:i,name:a.name,desc:a.desc,edit_version:v,function_name:a.function_name};try{return r?t=yield j.Js.UpdateAPI((0,l._)((0,n._)({},d),{api_id:r}),{__disableErrorToast:!0}):(t=yield j.Js.CreateAPI((0,l._)((0,n._)({},d),{method:M.fM.POST,path:"/".concat(d.name)}),{__disableErrorToast:!0}),null==u||u(t.api_id||"")),null==k||k(t),!0}catch(e){var{code:c,msg:s}=e;return Number(c)===el.B.SAFE_CHECK?null==h||h(!0):f.FN.error({content:(0,eO.u)(s)}),!1}}),function(){return o.apply(this,arguments)}),T=()=>{m&&(null==h||h(!1))};return{submitBaseInfo:z,baseInfoNode:(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(f.l0,{showValidateIcon:!1,ref:y,disabled:p,className:eZ.Z["base-info-form"],children:()=>p?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(f.l0.Slot,{label:{text:b.o.t("Create_newtool_s1_name"),required:!0},children:c.name}),(0,d.jsx)(f.l0.Slot,{label:{text:b.o.t("Create_newtool_s1_dercribe"),required:!0},children:c.desc})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(f.P6,{"data-testid":"plugin-create-tool-base-info-name",className:eZ.Z["textarea-single-line"],field:"name",label:b.o.t("Create_newtool_s1_name"),placeholder:b.o.t("Create_newtool_s1_title_empty"),trigger:["blur","change"],maxCount:30,maxLength:30,rows:1,onKeyDown:e=>{var o=window.event||e;if("Enter"===o.key||"Enter"===o.code||13===o.keyCode)return o.returnValue=!1,!1},onChange:T,rules:[{required:!0,message:b.o.t("Create_newtool_s1_title_empty")},{pattern:/^[a-zA-Z0-9_]+$/,message:b.o.t("Create_newtool_s1_title_error1")}]}),(0,d.jsxs)("div",{className:"relative",children:[null==I?void 0:I({disabled:!N,originDesc:N,className:"absolute right-[0] top-[12px]",plugin_id:i,space_id:t,onSetDescription:D}),(0,d.jsx)(f.P6,{"data-testid":"plugin-create-tool-base-info-desc",field:"desc",label:b.o.t("Create_newtool_s1_dercribe"),placeholder:b.o.t("Create_newtool_s1_dercribe_error"),rows:2,trigger:["blur","change"],maxCount:600,maxLength:600,rules:[{required:!0,message:b.o.t("Create_newtool_s1_dercribe_empty")},!1],onChange:e=>{T(),w(e)}}),x&&_===M.zV.LOCAL?(0,d.jsx)(f.P6,{className:eZ.Z["textarea-single-line"],field:"function_name",label:b.o.t("create_local_plugin_basic_tool_function"),placeholder:b.o.t("create_local_plugin_basic_tool_function_input_placeholder"),rows:1,trigger:["blur","change"],maxCount:30,maxLength:30,rules:[{required:!0,message:b.o.t("create_local_plugin_basic_warning_no_tool_function_entered")}],onChange:T}):null]})]})})})}},eY=t("827666"),eB=t("3779"),eQ=t("966171"),eU={};eU.styleTagTransform=O(),eU.setAttributes=A(),eU.insert=T().bind(null,"head"),eU.domAPI=D(),eU.insertStyleElement=C(),N()(eQ.Z,eU);var eP=eQ.Z&&eQ.Z.locals?eQ.Z.locals:void 0,eG=e=>{var{step:o}=e;return(0,d.jsxs)("div",{className:eP["error-msg"],children:[o!==eY.dT?b.o.t("plugin_parameter_create_modal_safe_error"):b.o.t("plugin_tool_create_modal_safe_error"),(0,d.jsx)(eB.v,{})]})},eR=e=>{let o;var{text:t,plugin_id:i,onClickWrapper:r,onBeforeClick:c,disabled:g,isShowBtn:p=!0,space_id:m}=e,h=(0,k.av)(),[b,v]=(0,s.useState)(!1),[x,M]=(0,s.useState)(!1),[j,_]=(0,s.useState)(!1),[I,y]=(0,s.useState)(!1),{pluginInfo:N,unlockPlugin:w,setPluginInfo:D}=(0,k.RQ)((0,u.N)(e=>({pluginInfo:e.pluginInfo,unlockPlugin:e.unlockPlugin,setPluginInfo:e.setPluginInfo}))),{baseInfoNode:z,submitBaseInfo:T}=eF({pluginId:i||"",setApiId:e=>{var o;v(!1),null===(o=h.tool)||void 0===o||o.call(h,e,{toStep:"1"},{replace:!0})},showModal:!1,disabled:g,showSecurityCheckFailedMsg:I,setShowSecurityCheckFailedMsg:y,editVersion:null==N?void 0:N.edit_version,space_id:m||"",pluginType:null==N?void 0:N.plugin_type,showFunctionName:!0,onSuccess:e=>{D((0,l._)((0,n._)({},N),{edit_version:null==e?void 0:e.edit_version}))}}),E=()=>{var e=null==c?void 0:c();if(!(0,eL.Z)(e)||!!e)_(!0),M(!1)};return{content:(0,d.jsxs)(d.Fragment,{children:[p?(0,d.jsx)(W.zx,{disabled:g,loading:x,color:"primary",onClick:(o=r?r(E):E,()=>{M(!0),o()}),children:t}):null,(0,d.jsxs)(f.u_,{title:t,loading:b,visible:j,onOk:(0,a._)(function*(){v(!0),yield T(),w(),v(!1)}),onCancel:()=>{w(),_(!1)},closeOnEsc:!0,children:[z,I?(0,d.jsx)(eG,{step:eY.dT}):null]})]}),openModal:()=>{r?r(E)():E()},closeModal:()=>{w(),_(!1)}}},eV=e=>{var{content:o}=eR((0,n._)({},e));return(0,d.jsx)(d.Fragment,{children:o})},eW=t("452058"),eH={};eH.styleTagTransform=O(),eH.setAttributes=A(),eH.insert=T().bind(null,"head"),eH.domAPI=D(),eH.insertStyleElement=C(),N()(eW.Z,eH);var eq=eW.Z&&eW.Z.locals?eW.Z.locals:void 0,eK={[M.x9.COZE]:b.o.t("create_tool"),[M.x9.IDE]:b.o.t("plugin_creation_create_tool_in_ide")},eJ=e=>{var o,t,i,r,m,h,x,y,{projectId:N,keepDocTitle:w,renderHeaderSlot:D,usePreloadIDE:z}=e,T=(0,er.rY)(e=>e.space.id),{wrapWithCheckLock:E,checkPluginIsLockedByOthers:A,updatedInfo:S,pluginInfo:C,canEdit:L,initPlugin:O,unlockPlugin:Z,initSuccessed:F,pluginID:Y,version:B,updatePluginInfoByImmer:Q}=(0,k.RQ)((0,u.N)(e=>({wrapWithCheckLock:e.wrapWithCheckLock,checkPluginIsLockedByOthers:e.checkPluginIsLockedByOthers,updatedInfo:e.updatedInfo,pluginInfo:e.pluginInfo,canEdit:e.canEdit,initPlugin:e.initPlugin,unlockPlugin:e.unlockPlugin,initSuccessed:e.initSuccessed,pluginID:e.pluginId,version:e.version,updatePluginInfoByImmer:e.updatePluginInfoByImmer}))),U=(null==C?void 0:C.creation_method)===M.x9.IDE,P=(null==C?void 0:C.creation_method)===M.x9.COZE,G=void 0===N,H=(0,k.Np)(),{onStatusChange:q,onUpdateDisplayName:K}=(0,k.sF)(),J=(0,k.av)(),X=(0,c.useNavigate)(),$=(0,V.UQ)(),[en]=(0,c.useSearchParams)(),[el,ed]=(0,s.useState)(),[ec,es]=(0,s.useState)(!1),[eu,eg]=(0,s.useState)(!1),[ep,em]=(0,s.useState)({}),[eh,ev]=(0,s.useState)({page:1,size:10,plugin_id:Y,preview_version_ts:B}),[ef,ex]=(0,s.useState)(""),[eM,ej]=(0,s.useState)(),{modal:e_,setShowCodePluginModel:ek}=(0,eS.Tj)({modalProps:{onSuccess:()=>eO()}}),{modal:eI,setShowFormPluginModel:ey}=(0,eS.e2)({modalProps:{onSuccess:()=>eO()}}),{modal:eN,setShowImportToolModal:ew}=(0,eS.cC)({modalProps:{onSuccess:()=>eO()}});(0,k.A2)(Y);var{data:eD,loading:ez}=(0,g.Z)(()=>j.Js.GetPluginAPIs(eh),{refreshDeps:[eh],onError:e=>{$(new ea.sH(ei.b.PluginGetApis,"get Plugin Detail Error: ".concat(e.message)))}});(0,s.useEffect)(()=>{var e,o=en.get("edit_example_id"),t=en.get("edit_plugin"),i=null==eD?void 0:null===(e=eD.api_info)||void 0===e?void 0:e.find(e=>e.api_id===o);i&&o&&(eB(i),en.delete("edit_example_id"),X({search:en.toString()},{replace:!0})),eD&&t&&(eX(),en.delete("edit_plugin"),X({search:en.toString()},{replace:!0}))},[eD,en]),(0,s.useEffect)(()=>{var e,o;null==K||K(null!==(o=null==C?void 0:null===(e=C.meta_info)||void 0===e?void 0:e.name)&&void 0!==o?o:"")},[null==C?void 0:null===(m=C.meta_info)||void 0===m?void 0:m.name]),(0,et.a)({isLive:!!eD&&!ez,extra:{renderSize:"".concat(null==eD?void 0:null===(h=eD.api_info)||void 0===h?void 0:h.length)}});var eT=null==eD?void 0:eD.api_info;var eE=(o=(0,a._)(function*(){(yield j.Js.NoUpdatedPrompt({plugin_id:Y}))&&eO()}),function(){return o.apply(this,arguments)});var eL=(t=(0,a._)(function*(){if(!(null==C?void 0:C.published)){es(!0);return}var e=yield j.Js.GetUpdatedAPIs({plugin_id:Y});e.created_api_names&&e.created_api_names.length>0||e.deleted_api_names&&e.deleted_api_names.length>0||e.updated_api_names&&e.updated_api_names.length>0?(em(e),eg(!0)):es(!0)}),function(){return t.apply(this,arguments)}),eO=()=>{var e;null===(e=eG.current)||void 0===e||e.reset(),O(),ev(e=>(0,l._)((0,n._)({},e),{page:1,size:10}))},eZ=null==z?void 0:z({onBack:eO,pluginID:Y});(0,p.Z)(()=>{F?(null==q||q("normal"),U&&(null==eZ||eZ.handleInitIde(!L))):null==q||q("error")},[F]);var eF=function(){var e,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_.jG.DEFAULT,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";U?null==eZ||eZ.handleShowIde({initialAction:o,toolId:t}):t&&(null===(e=J.tool)||void 0===e||e.call(J,t))},{exampleNode:eY,openExample:eB}=eb({onUpdate:eO}),{getColumns:eQ,reactNode:eU}=R({targetSwitchId:ef,setTargetSwitchId:ex,loading:ez,canEdit:L,refreshPage:eO,plugin_id:Y,pluginInfo:C,updatedInfo:S,showDropdownItem:eM,setShowDropDownItem:ej,handleIdeJump:eF,setCurAPIInfo:ed,openExample:eB,projectId:N,unlockPlugin:Z}),eP=eQ(),eG=(0,s.useRef)(null),eW=eK[null==C?void 0:C.creation_method]||"",{openModal:eH,content:eJ}=eR({text:eW,isShowBtn:!1,disabled:!L,onClickWrapper:E,onBeforeClick:()=>{ej(void 0)},plugin_id:Y,space_id:T});var eX=(i=(0,a._)(function*(){if(ej(void 0),!(L&&(yield A())))ey(!0)}),function(){return i.apply(this,arguments)}),e$=!U,e0=L&&!!(null==eD?void 0:eD.total),e1=L&&!U,e2=e0&&G,e4=e2&&U;return(0,d.jsxs)("div",{className:eq["tool-wrapper"],children:[e_,eI,eN,eU,eY,(0,d.jsxs)(W.Ar,{className:"flex",title:(0,v.Z$)(b.o.t("tab_plugin_detail",{plugin_name:null!==(y=null==C?void 0:null===(x=C.meta_info)||void 0===x?void 0:x.name)&&void 0!==y?y:""})),keepDocTitle:w,children:[G?(0,d.jsx)(W.Ar.Header,{className:eq["layout-header"],breadcrumb:(0,d.jsx)(eo.g3,{showTooltip:{width:"300px"},pluginInfo:null==C?void 0:C.meta_info,compact:!1})}):null,(0,d.jsxs)(W.Ar.Content,{className:eq["layout-content"],children:[(null==C?void 0:C.status)&&(null==C?void 0:C.published)&&L&&G?(0,d.jsx)(W.jL,{className:eq.banner,type:"info",bordered:!0,fullMode:!1,description:(0,d.jsxs)("div",{children:[b.o.t("plugin_update_tip"),(0,d.jsx)(W.ZT.Text,{className:eq.notips,onClick:()=>{eE()},children:b.o.t("not_show_again")})]})}):null,C?(0,d.jsx)(eA,{pluginInfo:C,loading:ez,canEdit:L,onClickEdit:eX,extraRight:(0,d.jsxs)(d.Fragment,{children:[null==D?void 0:D({pluginInfo:C}),e$?(0,d.jsx)(W.u,{position:"left",content:b.o.t("Plugin_button_code_tooltip"),children:(0,d.jsx)(W.hU,{icon:(0,d.jsx)(I.WTs,{}),onClick:()=>{ej(void 0),ek(!0)}})}):null,e0?(0,d.jsx)(eV,{text:eW,disabled:!L,onClickWrapper:E,onBeforeClick:()=>(ej(void 0),!U||(null==eZ||eZ.handleShowIde({initialAction:_.jG.CREATE_TOOL,toolId:""}),!1)),plugin_id:Y,space_id:T}):null,e1?(0,d.jsx)(W.zx,{color:"primary",disabled:!L||(null==C?void 0:C.plugin_type)===M.zV.LOCAL,onClick:E(()=>{ej(void 0),ew(!0)}),children:b.o.t("import")}):null,e4?(0,d.jsx)(W.u,{position:"left",content:b.o.t("Plugin_button_publish_tooltip"),children:(0,d.jsx)(W.zx,{disabled:!(null==eD?void 0:eD.total),theme:"solid",onClick:()=>{ej(void 0),eF()},children:b.o.t("Publish")})}):null,e2&&P?(0,d.jsx)(W.gn,{visible:eu,onCancel:()=>eg(!1),onClickOutSide:()=>{eg(!1)},style:{width:400},trigger:"custom",onConfirm:()=>{eg(!1),es(!0)},title:b.o.t("Plugin_update_info_title"),content:(0,d.jsx)(d.Fragment,{children:(r=[...ep.created_api_names||[],...ep.deleted_api_names||[],...ep.updated_api_names||[]],b.o.t("Plugin_update_info_text",{number:r.length,array:r.join("、")}))}),okText:b.o.t("Confirm"),cancelText:b.o.t("Cancel"),children:(0,d.jsx)("span",{children:(0,d.jsx)(ee,{spaceId:T,pluginInfo:C,pluginId:Y,isInLibraryScope:G,isPluginHasPublished:!!C.published,visible:ec,onClickOutside:()=>es(!1),onPublishSuccess:()=>{var e;null===(e=H.current)||void 0===e||e.reload(),es(!1),Q(e=>{if(!!e)e.published=!0})},children:(0,d.jsx)("span",{children:(0,d.jsx)(W.u,{position:"left",content:b.o.t("Plugin_button_publish_tooltip"),children:(0,d.jsx)(W.zx,{disabled:!(null==eD?void 0:eD.total),theme:"solid",onClick:eL,children:b.o.t("Publish")})})})})})}):null]})}):null,!!(null==eT?void 0:eT.length)&&(0,d.jsx)("div",{className:"mb-[24px] mt-[36px] text-[18px] weight-[600]",children:b.o.t("plugin_api_list_table_name")}),(0,d.jsx)(W.iA,{ref:eG,offsetY:390,tableProps:{rowKey:"api_id",loading:ez,dataSource:eT,columns:eP,onRow:e=>({onClick:()=>{if(null==e?void 0:e.api_id){var o;if(ej(void 0),U){eF(_.jG.SELECT_TOOL,null==e?void 0:e.api_id);return}null===(o=J.tool)||void 0===o||o.call(J,e.api_id,L?{mode:"preview"}:{})}}}),onChange:e=>{var o;(null===(o=e.sorter)||void 0===o?void 0:o.sortOrder)&&ev(o=>{var t;return(0,l._)((0,n._)({},o),{page:1,size:10,order:{desc:(null===(t=e.sorter)||void 0===t?void 0:t.sortOrder)==="descend"}})})}},empty:(0,d.jsx)(f.YZ,{empty:{title:b.o.t("plugin_empty_desc"),btnText:L?eW:void 0,btnOnClick:()=>{if(U){null==eZ||eZ.handleShowIde({initialAction:_.jG.CREATE_TOOL,toolId:""});return}eH()}}}),enableLoad:!0,total:Number((null==eD?void 0:eD.total)||0),onLoad:()=>{ev(e=>{var o;return(0,l._)((0,n._)({},e),{page:(null!==(o=eh.page)&&void 0!==o?o:0)+1})})}}),eJ]})]}),(0,d.jsx)(eC.Hv,{visible:!!el,onCancel:()=>{ed(void 0)},pluginAPIInfo:el})]})},eX=t("716069"),e$=t("287487"),e0=t("378925"),e1=t("707107"),e2=t("178385"),e4=t("824833"),e3=t("583193"),e5=t("30961"),e6=t("11869"),e8=t("345822"),e9=t("473980"),e7=t("428468"),oe={};oe.styleTagTransform=O(),oe.setAttributes=A(),oe.insert=T().bind(null,"head"),oe.domAPI=D(),oe.insertStyleElement=C(),N()(e7.Z,oe);var oo=e7.Z&&e7.Z.locals?e7.Z.locals:void 0;function ot(e){var{children:o,ellipsis:t,tooltipText:i}=e,r=(0,e9._)(e,["children","ellipsis","tooltipText"]),a=!1===t?t:(0,n._)({showTooltip:{opts:{content:(0,d.jsx)(f.ZT.Text,{className:h()(oo["long-text-tooltip"],oo["long-text"]),onClick:e=>e.stopPropagation(),ellipsis:{showTooltip:!1,rows:16},children:i||e.children})}}},"object"!=typeof t?{}:t);return(0,d.jsx)(f.ZT.Text,(0,l._)((0,n._)({ellipsis:a},r),{children:e.children}))}var oi=t("972588"),or={};or.styleTagTransform=O(),or.setAttributes=A(),or.insert=T().bind(null,"head"),or.domAPI=D(),or.insertStyleElement=C(),N()(oi.Z,or);var oa=oi.Z&&oi.Z.locals?oi.Z.locals:void 0;function on(e){var{isFullHeader:o=!0,readOnly:t=!0,mockSetInfo:i,onUpdateMockSetInfo:r,bizCtx:a}=e,[n,l]=(0,s.useState)(!1);return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(f.T,{spacing:o?2:4,className:h()(oa["mock-set-intro-title"],o?oa["mock-set-intro-title_full"]:""),children:[(0,d.jsx)(ot,{className:h()(oa["mock-set-intro-name"],o?oa["mock-set-intro-name_full"]:""),children:i.name}),!t&&i.name?(0,d.jsx)(f._3,{icon:(0,d.jsx)(I.mGJ,{className:h()(oa["mock-set-intro-edit"],o?oa["mock-set-intro-edit_full"]:"")}),size:"small",theme:"borderless",onClick:()=>l(!0)}):null,(0,d.jsx)(e8.a,{visible:n,initialInfo:{bindSubjectInfo:i.mockSubject||{},bizCtx:a,id:String(i.id),name:i.name,desc:i.description},onSuccess:e=>{null==r||r(e),l(!1)},onCancel:()=>l(!1)})]}),i.description?(0,d.jsx)(ot,{className:h()(oa["mock-set-intro-desc"],oa["mock-set-intro-desc_priority"],o?oa["mock-set-intro-desc_full"]:""),children:i.description}):null]})}var ol=t("746278");function od(e){var o,{pluginId:t,apiInfo:i,mockSetInfo:r}=e,n=(0,e5.fR)(e5.GV.PLUGIN_MOCK_DATA),[l,c]=(0,s.useState)({name:null==n?void 0:n.pluginName});var u=(o=(0,a._)(function*(){try{var e=yield j.nA.GetPluginInfo({plugin_id:t||""},{__disableErrorToast:!0});(null==e?void 0:e.code)===0&&c(e.meta_info||{})}catch(e){V.kg.error({error:e,eventName:"get_plugin_info_fail"})}}),function(){return o.apply(this,arguments)});return(0,s.useEffect)(()=>{u()},[t]),(0,d.jsx)(f._n.Header,{className:oa["layout-header"],breadcrumb:(0,d.jsx)(eo.g3,{showTooltip:{width:"300px"},pluginInfo:l,pluginToolInfo:i,mockSetInfo:r,compact:!1})})}var oc=t("763055"),os=t("49695");function ou(e){var{height:o,width:t}=e;return(0,d.jsx)("div",{style:{width:t,height:o,display:t?"inline-block":"block"}})}var og=t("286518"),op=t("177625"),om=t("998600"),oh=t("966219"),ob=t("937373"),ov={};ov.styleTagTransform=O(),ov.setAttributes=A(),ov.insert=T().bind(null,"head"),ov.domAPI=D(),ov.insertStyleElement=C(),N()(ob.Z,ov);var of=ob.Z&&ob.Z.locals?ob.Z.locals:void 0,ox=t("792795"),oM={};oM.styleTagTransform=O(),oM.setAttributes=A(),oM.insert=T().bind(null,"head"),oM.domAPI=D(),oM.insertStyleElement=C(),N()(ox.Z,oM);var oj=ox.Z&&ox.Z.locals?ox.Z.locals:void 0,o_=(0,s.forwardRef)((e,o)=>{var t,i,r,{mockInfo:a,readOnly:n,className:l,onValidate:c,onEditorPaste:u,onEditorReady:g}=e,{mock:p,mergedResultExample:m,schema:b,incompatible:v}=a,[x,M]=(0,s.useState)(!1),j=(0,s.useRef)(null),_=(0,s.useRef)(null),k=(0,s.useCallback)(e=>{var o,t=e.map(e=>({message:e.message}));(null===(o=j.current)||void 0===o?void 0:o.getValue().trim().length)===0&&t.push({message:"no data"}),null==c||c(t)},[c]),{run:I}=(0,om.Z)(()=>{var e,o,t=null===(e=j.current)||void 0===e?void 0:e.getModel();if(null==t?void 0:t.id){;k((null===(o=_.current)||void 0===o?void 0:o.editor.getModelMarkers)(t.id).filter(e=>e.resource.path===t.id.replace("$model","/"))||[])}},{wait:200}),y=(0,s.useCallback)(()=>{var e;null===(e=j.current)||void 0===e||e.trigger("editor","editor.action.formatDocument",{})},[]),N=(e,o)=>{j.current=e,_.current=o;var t,i=b?(0,e3.KD)(b):void 0,r=e.getModel(),a=null==r?void 0:r.uri.toString();i&&a&&o.languages.json.jsonDefaults.setDiagnosticsOptions({validate:!0,schemaValidation:"error",schemas:[{uri:"https://plugin-mock-set/tool_schema_".concat(null==r?void 0:r.id),fileMatch:[a],schema:i}]}),e.onDidBlurEditorText(y),e.onDidPaste(()=>{!1!==(null==u?void 0:u())&&y()}),e.onDidChangeModelDecorations(I),null===(t=e.getModel())||void 0===t||t.updateOptions({tabSize:op.S9}),null==g||g(),setTimeout(()=>{M(!0)})};return(0,s.useImperativeHandle)(o,()=>({getValue:()=>{var e;return null===(e=j.current)||void 0===e?void 0:e.getValue()}})),(0,s.useEffect)(()=>{y()},[null==p?void 0:null===(t=p.responseExpect)||void 0===t?void 0:t.responseExpectRule,m]),(0,d.jsxs)("div",{className:h()(oj["editor-container"],n?oj["editor-container_disabled"]:"",of.light,l),children:[x?null:(0,d.jsx)(f.Od,{className:oj.skeleton,placeholder:(0,d.jsx)(f.Od.Image,{})}),v&&!n?(0,d.jsx)(oh.DiffEditor,{className:h()(oj.editor,x?"":oj.editor_hidden),theme:"vs-dark",original:null==p?void 0:null===(i=p.responseExpect)||void 0===i?void 0:i.responseExpectRule,modified:m,language:"json",options:{unicodeHighlight:{ambiguousCharacters:!1},wordWrap:"on",readOnly:n,formatOnPaste:!0,formatOnType:!0,minimap:{enabled:!1},scrollBeyondLastLine:!1,contextmenu:!1},onMount:(e,o)=>{N(e.getModifiedEditor(),o)},loading:null}):(0,d.jsx)(oh.Editor,{className:h()(oj.editor,x?"":oj.editor_hidden),theme:"vs-dark",language:"json",options:{unicodeHighlight:{ambiguousCharacters:!1},wordWrap:"on",readOnly:n,formatOnPaste:!0,formatOnType:!0,minimap:{enabled:!1},scrollBeyondLastLine:!1,scrollbar:{alwaysConsumeMouseWheel:!n},contextmenu:!1},value:(null==p?void 0:null===(r=p.responseExpect)||void 0===r?void 0:r.responseExpectRule)||m,onMount:N,loading:null})]})}),ok=t("209684"),oI={};oI.styleTagTransform=O(),oI.setAttributes=A(),oI.insert=T().bind(null,"head"),oI.domAPI=D(),oI.insertStyleElement=C(),N()(ok.Z,oI);var oy=ok.Z&&ok.Z.locals?ok.Z.locals:void 0,oN=(0,s.forwardRef)((e,o)=>{var{onValidate:t,mockInfo:i,environment:r,isCreateScene:a,className:c}=e,u=(0,e9._)(e,["onValidate","mockInfo","environment","isCreateScene","className"]),g=(0,s.useRef)([]),[p,m]=(0,s.useState)({current:0,valid:[!0],data:i.mergedResultExample?[i.mergedResultExample]:[]}),v=(e,o)=>{var i=p.valid.slice();i[o]=!e.length,m((0,l._)((0,n._)({},p),{valid:i})),null==t||t(i)},x=e=>{var o,t=null===(o=g.current[e])||void 0===o?void 0:o.getValue();if((t?(0,e3.Vv)(t):0)>op.Fc)return f.FN.error(b.o.t("mockset_toast_data_size_limit")),!1},M=()=>{var e;return null===(e=g.current)||void 0===e?void 0:e.slice(0,p.data.length).map(e=>(null==e?void 0:e.getValue())||"")};return(0,s.useImperativeHandle)(o,()=>({getValue:M})),(0,d.jsx)("div",{className:h()(c,oy["mock-tab-container"]),children:(0,d.jsx)("div",{className:oy["mock-tab-panels"],children:p.data.map((e,o)=>(0,d.jsx)(o_,(0,n._)({className:h()(o===p.current?oy["mock-tab-panel_visible"]:oy["mock-tab-panel_invisible"],0===o?oy["mock-tab-panel_static"]:oy["mock-tab-panel_absolute"]),mockInfo:(0,l._)((0,n._)({},i),{mergedResultExample:e}),ref:e=>g.current[o]=e,onValidate:e=>v(e,o),onEditorPaste:()=>x(o)},u),o))})})}),ow=t("939619"),oD=t("450599"),oz={schema:"",result:void 0},oT="response_for_model";function oE(e,o){var t,i=(t=e,(0,s.useMemo)(()=>{if(t&&oz.schema===t)return oz.result;if(t){oz.schema=t;var e=(0,e3.KD)(t),o=(0,e3.Zg)(op.dz,e);return oz.result=o,o}},[t])),{result:r,merged:a,incompatible:n,formatted:l}=(0,s.useMemo)(()=>{var{result:e,incompatible:t}=(0,oD.Lt)(i,o);if(!e)return{incompatible:t};var r,a=null===(r=(0,e3.Cu)(e,void 0!==o))||void 0===r?void 0:r[op.dz];return{merged:e,result:a,formatted:(0,e3._v)(a),incompatible:t}},[i,o]),d=(0,s.useCallback)(e=>{var o;if(null==i?void 0:null===(o=i.children)||void 0===o?void 0:o.some(e=>e.label===oT&&e.type===ow.U.STRING)){var t=(0,v.dj)(e);if("object"==typeof t&&("string"!=typeof t[oT]||0===t[oT].length))return!1}return!0},[i]);return{result:i,mergedResult:a,mergedResultExample:r,formattedResultExample:l,incompatible:n,isInit:void 0===o,testValueValid:d}}var oA=t("936721");var oS=((i={}).MODAL="modal",i.CARD="card",i);function oC(e){var{mode:o,mockInfo:t,visible:i,onCancel:r,onSuccess:u,bizCtx:p,forceGenerate:m}=e,{schema:h}=t||{},v=(0,s.useRef)(null),[x,M]=(0,s.useState)(!1),[_,k]=(0,s.useState)(!1),{testValueValid:I,formattedResultExample:y}=oE(h),{mock_set_id:N,tool_id:w}=(0,c.useParams)(),D=(0,er.rY)(e=>e.space.space_type)===og.Sn.Personal,z={environment:(0,e3.zj)(),workspace_id:p.bizSpaceID||"",workspace_type:D?"personal_workspace":"team_workspace",tool_id:w||"",mock_set_id:N||""},{save:T,loading:E}=function(e){var o,{mockSetId:t,basicParams:i,bizCtx:r,onSuccess:d,onError:c}=e;var{runAsync:s,loading:u}=(0,g.Z)((o=(0,a._)(function*(e,o){var s,u,g,p,m=e.map((s=(0,a._)(function*(e){var i={id:o,mocksetID:t,responseExpect:{responseExpectType:oA.K7.JSON,responseExpectRule:e}};try{var{id:a}=yield j.xd.SaveMockRule((0,n._)({bizCtx:r},i),{__disableErrorToast:!0});return(0,l._)((0,n._)({status:"success"},i),{id:a||o})}catch(e){return V.kg.error({error:e,eventName:"save_mock_info_fail"}),{status:"fail",error:e}}}),function(e){return s.apply(this,arguments)})),h=yield Promise.all(m),v=h.filter(e=>"success"===e.status),x=h.filter(e=>"success"!==e.status);v.length&&(0,e1.Gg)(e1.Kg.create_mock_front,(0,l._)((0,n._)({},i),{mock_counts:v.length,status:0})),x.length&&(0,e1.Gg)(e1.Kg.create_mock_front,(0,l._)((0,n._)({},i),{mock_counts:x.length,status:1,error:null===(u=x[0].error)||void 0===u?void 0:u.message})),0===v.length?(f.FN.error({content:(0,eO.u)((null===(p=x[0])||void 0===p?void 0:null===(g=p.error)||void 0===g?void 0:g.message)||b.o.t("error")),showClose:!1}),null==c||c()):null==d||d(v)}),function(e,t){return o.apply(this,arguments)}),{manual:!0});return{save:s,loading:u}}({mockSetId:N,basicParams:z,bizCtx:p,onSuccess:u}),A=()=>{var e,o,i=null===(e=v.current)||void 0===e?void 0:e.getValue();if(!!i){for(var r of i){if(!r){f.FN.error("no data");return}if((0,e3.Vv)(r)>op.Fc){f.FN.error({content:b.o.t("mockset_toast_data_size_limit"),showClose:!1});return}if(!I(r)){f.FN.error({content:b.o.t("mockdata_field_empty",{fieldName:oT}),showClose:!1});return}}T(i,String((null==t?void 0:null===(o=t.mock)||void 0===o?void 0:o.id)||0))}},S=e=>{M(e.some(e=>!e))};return(0,s.useEffect)(()=>{var e=e=>{var o=b.o.t("mockset_tip_data_will_lose");return e.preventDefault(),e.returnValue=o,o};return("modal"===o&&i||"card"===o)&&window.addEventListener("beforeunload",e),()=>{window.removeEventListener("beforeunload",e)}},[o,i]),(0,s.useEffect)(()=>{if(m){var e,o;null===(o=v.current)||void 0===o||null===(e=o.forceStartGenerate)||void 0===e||e.call(o,m.mode,m.count)}},[]),"modal"===o?(0,d.jsx)(f.M5,{visible:i,title:(null==t?void 0:t.mock)?b.o.t("edit_mock_data"):b.o.t("add_mock_data"),className:oa["mock-creation-modal"],keepDOM:!1,footer:(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{className:"mr-[8px]",children:b.o.t("mockset_save_description")}),(0,d.jsx)(f.iz,{layout:"vertical",margin:"0px"}),(0,d.jsx)(f.y3,{type:"tertiary",onClick:r,children:b.o.t("cancel")},"Cancel"),(0,d.jsx)(f.y3,{type:"primary",theme:"solid",onClick:A,loading:E,disabled:x||_,children:b.o.t("confirm")},"Confirm")]}),width:1e3,maskClosable:!1,onCancel:r,children:(0,d.jsx)(oN,{className:oa["mock-creation-modal-editor"],mockInfo:(0,n._)({mergedResultExample:y},t),readOnly:!1,ref:v,onValidate:S,environment:{spaceId:p.bizSpaceID,mockSetId:N,basicParams:z},isCreateScene:!(null==t?void 0:t.mock),onGenerationStatusChange:e=>k(e)})}):(0,d.jsxs)("div",{className:oa["mock-creation-card"],children:[(0,d.jsx)("div",{className:oa["mock-creation-card-editor"],children:(0,d.jsx)(oN,{mockInfo:(0,n._)({mergedResultExample:y},t),ref:v,onValidate:S,environment:{spaceId:p.bizSpaceID,mockSetId:N,basicParams:z},isCreateScene:!(null==t?void 0:t.mock),onGenerationStatusChange:e=>k(e)})}),(0,d.jsx)("div",{className:oa["mock-creation-card-operation"],children:(0,d.jsxs)(f.T,{children:[(0,d.jsx)(f.ZT.Text,{children:b.o.t("mockset_save_description")}),(0,d.jsx)(f.iz,{layout:"vertical",margin:"0px"}),(0,d.jsx)(f.y3,{type:"primary",theme:"solid",onClick:A,loading:E,disabled:x||_,children:b.o.t("mockset_save")})]})})]})}var oL=t("773568");var oO=((r={})[r.NONE=0]="NONE",r[r.VISIBLE=1]="VISIBLE",r[r.HALF=2]="HALF",r),oZ=t("393288"),oF={};oF.styleTagTransform=O(),oF.setAttributes=A(),oF.insert=T().bind(null,"head"),oF.domAPI=D(),oF.insertStyleElement=C(),N()(oZ.Z,oF);var oY=oZ.Z&&oZ.Z.locals?oZ.Z.locals:void 0;function oB(e){var o,t,{mock:i,readOnly:r,schema:a,onEdit:c,onRemove:u}=e,{formattedResultExample:g,incompatible:p,mergedResult:m}=oE(a,null==i?void 0:null===(o=i.responseExpect)||void 0===o?void 0:o.responseExpectRule),{branchInfo:v,prunedData:x}=function(e){var[o,t]=(0,s.useState)({}),[i,r]=(0,s.useState)(),a=e=>{if(!!(null==e?void 0:e.children)){var o=e.children.map(e=>e.type!==oL.U.ARRAY&&e.type!==oL.U.OBJECT?(0,n._)({},e):!1===e.isRequired&&e.status===oL.M.ADDED?(0,l._)((0,n._)({},e),{children:void 0}):a(e));return(0,l._)((0,n._)({},e),{children:o})}},d=function(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],t={};if(null==e?void 0:e.children){var{length:i}=e.children;null==e||e.children.forEach((e,r)=>{var a=r===i-1;t[e.key]={isLast:a,v:a&&o.length>0?[...o.slice(0,-1),2]:o},Object.assign(t,d(e,a&&o.length>0?[...o.slice(0,-1),0,1]:[...o,1]))})}return t};return(0,s.useEffect)(()=>{var o=a(e),i=d(o);r(o),t(i)},[e]),{branchInfo:o,prunedData:i}}(m),M=(e,o)=>{var t=(null==e?void 0:e.key)?v[e.key]:void 0;return(0,d.jsxs)("span",{className:oY["card-branches"],children:[null==t?void 0:t.v.map((e,o)=>(0,d.jsx)("span",{className:h()(oY["card-branch-v"],e!==oO.NONE?oY["card-branch-v_visible"]:"",e===oO.HALF?oY["card-branch-v_half"]:"")},o)),o?"":(0,d.jsx)("span",{className:h()(oY["card-branch-h"],(null==e?void 0:e.children)?"":oY["card-branch-h_long"])})]})},j=e=>{var o=(null==e?void 0:e.status)===oL.M.REMOVED;return(null==e?void 0:e.status)===oL.M.ADDED?(0,d.jsx)(oQ,{val:e.isRequired?b.o.t("mockset_field_is_required",{field:null==e?void 0:e.label}):void 0,className:h()("ms-[8px]",e.isRequired?oY["card-item-text_highlighted"]:"")}):((null==e?void 0:e.type)===oL.U.ARRAY||(null==e?void 0:e.type)===oL.U.OBJECT)&&(null==e?void 0:e.children)?"":(0,d.jsx)(oQ,{val:null==e?void 0:e.displayValue,className:h()("ms-[8px]",o?oY["card-item-text_highlighted"]:"")})},_=(e,o)=>{var t="".concat(op.dz,"-").concat(null==o?void 0:o.label)===(null==o?void 0:o.key),i=(null==o?void 0:o.status)===oL.M.REMOVED,r=(null==o?void 0:o.status)===oL.M.ADDED&&o.isRequired;return o?(0,d.jsxs)(d.Fragment,{children:[M(o,t),(0,d.jsxs)("span",{className:h()(oY["card-item"],i||r?oY["card-item-text_highlighted"]:"",i?oY["card-item_deleted"]:""),children:[(0,d.jsx)("span",{className:h()(oY["card-item-text"],t?oY["card-item-text_primary"]:"",i||r?oY["card-item-text_highlighted"]:""),children:null==o?void 0:o.label}),(null==o?void 0:o.isRequired)?(0,d.jsx)("span",{className:h()(oY["card-item-text"],oY["card-item-text_required"]),children:"*"}):null,i||r?null:(0,d.jsxs)("span",{className:h()(oY["card-item-tag"],"ms-[8px]"),children:[(0,oD.jy)(null==o?void 0:o.type),(null==o?void 0:o.type)===oL.U.ARRAY?"<".concat((0,oD.jy)(null==o?void 0:o.childrenType),">"):""]}),j(o)]})]}):""};return(null==i?void 0:null===(t=i.responseExpect)||void 0===t?void 0:t.responseExpectRule)?(0,d.jsxs)("div",{className:oY["mock-data-card"],children:[(0,d.jsx)("div",{className:oY["mock-data-content"],children:(()=>{var e;if((null==x?void 0:x.type)!==oL.U.ARRAY&&(null==x?void 0:x.type)!==oL.U.OBJECT)return(0,d.jsx)("div",{className:oY["card-non-tree-container"],children:(0,d.jsx)(oQ,{val:null==x?void 0:x.displayValue})});return(null===(e=x.children)||void 0===e?void 0:e.length)?(0,d.jsx)(f.mp,{defaultExpandAll:!0,treeData:x.children,renderLabel:_}):(0,d.jsx)("div",{className:oY["card-non-tree-container"],children:(0,d.jsx)("span",{className:h()(oY["card-item-text"],oY["card-item-text_invalid"]),children:"Empty"})})})()}),r?null:(0,d.jsxs)(f.T,{className:oY["mock-data-card-operations"],spacing:12,children:[(0,d.jsx)(f._3,{icon:(0,d.jsx)(I.mGJ,{className:oY["mock-data-card-edit"]}),size:"small",theme:"borderless",onClick:()=>{null==c||c({schema:a,mock:i,mergedResultExample:g,incompatible:p})}}),(0,d.jsx)(f._3,{icon:(0,d.jsx)(I.WDf,{className:oY["mock-data-card-edit"]}),size:"small",theme:"borderless",onClick:()=>{null==u||u({schema:a,mock:i})}})]})]}):null}var oQ=e=>e.val?(0,d.jsx)("span",{className:h()(e.className,oY["card-item-text"]),children:e.val}):(0,d.jsx)("span",{className:h()(e.className,oY["card-item-text"],oY["card-item-text_invalid"]),children:"Undefined"}),oU=(0,s.forwardRef)((e,o)=>{var t,i,{mockSetID:r,perm:u,toolSchema:g,bizCtx:p,onListUpdate:m}=e,[v,x]=(0,s.useState)(!1),[M,_]=(0,s.useState)([]),[k,I]=(0,s.useState)(!1),[y,N]=(0,s.useState)(!1),[w,D]=(0,s.useState)(!1),[z,T]=(0,s.useState)(),E=(0,e5.fR)(e5.GV.PLUGIN_MOCK_DATA),{mock_set_id:A,space_id:S,tool_id:C}=(0,c.useParams)(),L=(0,er.rY)(e=>e.space.space_type)===e2.Sn.Personal,O=e=>{T(e),I(!0)},Z=e=>{T(e),N(!0)};var F=(t=(0,a._)(function*(e){try{x(!0);var o,t=yield j.xd.MGetMockRule({bizCtx:p,mockSetID:A,orderBy:e4.d$.UpdateTime,desc:!0});_(t.mockRules||[]),null==m||m((null===(o=t.mockRules)||void 0===o?void 0:o.length)||0,e)}catch(e){V.kg.error({error:e,eventName:"get_mock_data_fail"})}finally{x(!1)}}),function(e){return t.apply(this,arguments)});var Y=(i=(0,a._)(function*(){var{mock:e}=z||{};if(!!e){var o={environment:(0,e3.zj)(),workspace_id:S||"",workspace_type:L?"personal_workspace":"team_workspace",tool_id:C||"",mock_set_id:A||"",mock_counts:1};try{D(!0),yield j.xd.DeleteMockRule({bizCtx:p,id:String(e.id)}),B(e,2),T(void 0),N(!1),(0,e1.Gg)(e1.Kg.del_mock_front,(0,l._)((0,n._)({},o),{status:0}))}catch(e){V.kg.error({error:e,eventName:"delete_mock_fail"}),(0,e1.Gg)(e1.Kg.del_mock_front,(0,l._)((0,n._)({},o),{status:1,error:e}))}finally{D(!1)}}}),function(){return i.apply(this,arguments)}),B=(e,o)=>{var t=0;0===o?F(!0):2===o?(t=M.length-1,_(o=>{var i=o.findIndex(o=>o.id===(null==e?void 0:e.id));return -1!==i&&o.splice(i,1),t=o.length,[...o]}),null==m||m(t)):(t=M.length,_(o=>{var i=o.findIndex(o=>o.id===(null==e?void 0:e.id));return -1!==i&&(o.splice(i,1),o.unshift(e)),t=o.length,[...o]}),null==m||m(t,!0))};return(0,s.useImperativeHandle)(o,()=>({update:F,create:()=>{T(void 0),I(!0)}})),(0,s.useEffect)(()=>{F()},[r]),(0,s.useEffect)(()=>{if(null==E?void 0:E.generationMode){var e=(0,l._)((0,n._)({},history.state),{usr:(0,l._)((0,n._)({},history.state.usr||{}),{generationMode:void 0})});history.replaceState(e,"")}},[]),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(ou,{height:24}),v||u.uninitialized?(0,d.jsx)("div",{className:oa["list-container-no-header_flexible"],children:(0,d.jsx)(f.yC,{size:"large",spinning:!0,style:{height:"80%",width:"100%"}})}):u.readOnly&&0===M.length?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("h1",{className:h()(oa["content-title"]),children:b.o.t("mockset_data")}),(0,d.jsx)("div",{className:oa["list-container_flexible"],children:(0,d.jsx)(f.HY,{className:oa.empty,image:(0,d.jsx)(oc.ts,{}),darkModeImage:(0,d.jsx)(oc.Id,{}),description:b.o.t("no_mock_yet")})})]}):u.readOnly||0!==M.length?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("h1",{className:h()(oa["content-title"]),children:b.o.t("mockset_data")}),(0,d.jsx)("div",{className:oa["list-container_scroll"],children:M.map(e=>(0,d.jsx)(oB,{readOnly:u.readOnly,mock:e,schema:g,onEdit:e=>O(e),onRemove:e=>Z(e),bizCtx:p},e.id))})]}):(0,d.jsx)("div",{className:oa["list-container-no-header_flexible"],children:(0,d.jsx)(oC,{mode:oS.CARD,mockInfo:{schema:g},onSuccess:e=>{e&&B(e[0],0)},bizCtx:p,forceGenerate:(null==E?void 0:E.generationMode)?{mode:E.generationMode,count:1}:void 0})}),(0,d.jsx)(oC,{mode:oS.MODAL,mockInfo:z||{schema:g},visible:k,onCancel:()=>{T(void 0),I(!1)},onSuccess:e=>{T(void 0),I(!1),(null==e?void 0:e[0])&&B(e[0],z?1:0)},bizCtx:p}),(0,d.jsx)(f.M5,{type:"info",icon:(0,d.jsx)(os.Z,{size:"extra-large",className:"inline-flex text-[#FF2710]"}),title:b.o.t("delete_mock_data"),visible:y,onCancel:()=>{T(void 0),N(!1)},okText:b.o.t("confirm"),cancelText:b.o.t("cancel"),confirmLoading:w,onOk:()=>Y(),okType:"danger",children:b.o.t("operation_cannot_be_reversed")})]})}),oP=t("928002"),oG={};oG.styleTagTransform=O(),oG.setAttributes=A(),oG.insert=T().bind(null,"head"),oG.domAPI=D(),oG.insertStyleElement=C(),N()(oP.Z,oG);var oR=oP.Z&&oP.Z.locals?oP.Z.locals:void 0,oV=e=>{var o,t,i,r,{toolID:u,mocksetID:g,pluginID:p,spaceID:m,version:x}=e,M=(0,s.useMemo)(()=>eX.Z.parse(location.search),[]),_=(0,e5.fR)(e5.GV.PLUGIN_MOCK_DATA),[k,y]=(0,s.useState)({name:null==_?void 0:_.toolName}),[N,w]=(0,s.useState)({id:g,name:null==_?void 0:_.mockSetName}),[D,z]=(0,s.useState)(""),[T,E]=(0,s.useState)({readOnly:!0,uninitialized:!0}),A=(0,s.useRef)(null),S=(0,s.useRef)(null),C=M.hideMenu?"full_page":"embed",L=(null==_?void 0:_.fromSource)?_.fromSource:"mock_set",[O,Z]=(0,s.useState)(0),F=e$.Z.useUserInfo(),Y=(0,e0.$_)(m),B=(null==Y?void 0:Y.space_type)===e2.Sn.Personal,Q=(0,c.useNavigate)(),U=(0,s.useMemo)(()=>({connectorID:ol.FQ,connectorUID:null==F?void 0:F.user_id_str,bizSpaceID:m}),[ol.FQ,F,m]),P=(0,s.useMemo)(()=>({componentType:e4.re.CozeTool,componentID:u,parentComponentType:e4.re.CozePlugin,parentComponentID:p}),[u,p]);var G=(o=(0,a._)(function*(){try{var{api_info:e=[]}=yield j.Js.GetPluginAPIs({plugin_id:p,api_ids:[u],preview_version_ts:x},{__disableErrorToast:!0});if(e.length>0){var o=e.length>0?e[0]:{};y(o)}}catch(e){V.kg.error({error:e,eventName:"get_tool_info_fail"})}}),function(){return o.apply(this,arguments)});var R=(t=(0,a._)(function*(){if(!!g)try{var e,o,t,i,r=yield j.xd.MGetMockSet({bizCtx:U,mockSubject:P,ids:[g],pageLimit:1});(null===(e=r.mockSets)||void 0===e?void 0:e[0])&&w(r.mockSets[0]),r.schema&&z(r.schema),E({readOnly:(null==F?void 0:F.user_id_str)!==(null===(i=r.mockSets)||void 0===i?void 0:null===(t=i[0])||void 0===t?void 0:null===(o=t.creator)||void 0===o?void 0:o.ID),uninitialized:!1})}catch(e){V.kg.error({error:e,eventName:"get_mockset_info_fail"})}}),function(){return t.apply(this,arguments)}),W=()=>{var e;null===(e=A.current)||void 0===e||e.create()};var H=(i=(0,a._)(function*(){var e=(0,e6.dj)((null==_?void 0:_.bizCtx)||""),o={environment:(0,e3.zj)(),workspace_id:m,workspace_type:B?"personal_workspace":"team_workspace",tool_id:u,status:1,mock_set_id:g,where:(0,e6.G)(null==e?void 0:e.trafficScene)};try{yield j.xd.BindMockSet({mockSetID:g,bizCtx:e,mockSubject:(0,e6.dj)((null==_?void 0:_.bindSubjectInfo)||"")}),(0,e1.Gg)(e1.Kg.use_mockset_front,(0,l._)((0,n._)({},o),{status:0})),Q(-1);var t=b.o.t("toolname_used_mockset_mocksetname",{toolName:(null==_?void 0:_.toolName)||"",mockSetName:N.name||""});t&&f.FN.success({content:t,showClose:!1})}catch(e){V.kg.error({error:e,eventName:"change_mockset_fail"}),(0,e1.Gg)(e1.Kg.use_mockset_front,(0,l._)((0,n._)({},o),{status:1,error:null==e?void 0:e.msg}))}}),function(){return i.apply(this,arguments)});return(0,s.useEffect)(()=>{R()},[]),(0,s.useEffect)(()=>{G()},[p,u]),(0,d.jsxs)(f._n,{title:(0,v.Z$)(N.name||b.o.t("mockset")),children:["embed"===C?(0,d.jsx)(od,{pluginId:p,apiInfo:k,mockSetInfo:N}):null,(0,d.jsxs)("div",{className:h()(oR["page-header"],"full_page"===C?oR["page-header_full"]:""),children:["full_page"===C?(0,d.jsx)(f.y3,{className:h()(oR["page-header-back"]),icon:(0,d.jsx)(I.s3j,{}),onClick:()=>{Q(-1)},theme:"borderless"}):null,(0,d.jsx)("div",{className:h()(oR["page-header-intro"],"full_page"===C?oR["page-header-intro_center"]:oR["page-header-intro_top"]),children:(0,d.jsx)(on,{isFullHeader:"full_page"===C,mockSetInfo:(0,n._)({mockSubject:P},N),bizCtx:U,readOnly:T.readOnly,onUpdateMockSetInfo:e=>{e&&w(o=>(0,n._)({},o,e))}})}),(0,d.jsx)(f.T,{className:h()(oR["page-header-operations"]),spacing:12,children:(r=[],!T.readOnly&&0!==O&&r.push({label:b.o.t("add_mock_data"),handler:W}),("bot"===L||"workflow"===L)&&r.push({label:b.o.t("bot"===L?"use_in_bot":"use_in_workflow"),handler:H,disabled:0===O}),r.map((e,o)=>(0,d.jsx)(f.y3,{type:o===r.length-1?"primary":"tertiary",theme:o===r.length-1?"solid":void 0,onClick:e.handler,disabled:e.disabled,children:e.label},e.label)))})]}),(0,d.jsx)(f._n.Content,{className:h()(oR["layout-content"]),ref:S,children:(0,d.jsx)(oU,{mockSetID:g,toolSchema:D,perm:T,ref:A,bizCtx:U,onListUpdate:(e,o)=>{if(Z(e),o){var t;null===(t=S.current)||void 0===t||t.scrollTo({top:0,behavior:"smooth"})}}})})]})},oW=e=>{var o,{visible:t,mockSetInfo:i,onSuccess:r,onCancel:c,zIndex:u,needResetPopoverContainer:p}=e,{detail:{id:m},ctx:h}=i||{},[v,x]=(0,s.useState)(-1),M=(0,er.rY)(e=>e.space.space_type)===e2.Sn.Personal,{run:_}=(0,g.Z)((0,a._)(function*(){try{var{spaceID:e}=(0,e3.MX)((null==h?void 0:h.bizCtx)||{},(null==h?void 0:h.mockSubjectInfo)||{}),{usersUsageCount:o}=yield j.xd.GetMockSetUsageInfo({mockSetID:m,spaceID:e});x(Number(null!=o?o:0))}catch(e){V.kg.error({error:e,eventName:"fetch_mockset_ref_fail"}),x(0)}}),{manual:!0});(0,s.useEffect)(()=>{_()},[i]);var k=v>0?b.o.t("people_using_mockset_delete",{num:v}):b.o.t("delete_the_mockset");var I=(o=(0,a._)(function*(){var{toolID:e,spaceID:o}=(0,e3.MX)((null==h?void 0:h.bizCtx)||{},(null==h?void 0:h.mockSubjectInfo)||{}),t={environment:(0,e3.zj)(),workspace_id:o||"",workspace_type:M?"personal_workspace":"team_workspace",tool_id:e||"",mock_set_id:String(m)||"",status:1};try{m&&(yield j.xd.DeleteMockSet({id:m,bizCtx:null==h?void 0:h.bizCtx})),null==r||r(),(0,e1.Gg)(e1.Kg.del_mockset_front,(0,l._)((0,n._)({},t),{status:0}))}catch(e){(0,e1.Gg)(e1.Kg.del_mockset_front,(0,l._)((0,n._)({},t),{status:1,error:null==e?void 0:e.message}))}}),function(){return o.apply(this,arguments)});return(0,d.jsx)(f.M5,{type:"info",zIndex:u,icon:(0,d.jsx)(os.Z,{size:"extra-large",className:"inline-flex text-[#FF2710]"}),title:k,visible:v>=0&&t,onCancel:c,onOk:I,getPopupContainer:p?()=>document.body:void 0,okType:"danger",children:b.o.t("operation_cannot_be_reversed")})},oH=t("751238"),oq={};oq.styleTagTransform=O(),oq.setAttributes=A(),oq.insert=T().bind(null,"head"),oq.domAPI=D(),oq.insertStyleElement=C(),N()(oH.Z,oq);var oK=oH.Z&&oH.Z.locals?oH.Z.locals:void 0,oJ=e=>{var o,t,i,r,c,p,{toolID:m}=e,x=(0,k.av)(),M=e$.Z.useUserInfo(),[_,y]=(0,s.useState)({pageSize:10,pageNo:1}),{pluginInfo:N,initPlugin:w,pluginID:D,spaceID:z,version:T}=(0,k.RQ)((0,u.N)(e=>({pluginInfo:e.pluginInfo,initPlugin:e.initPlugin,pluginID:e.pluginId,spaceID:e.spaceID,version:e.version}))),E=(0,e0.$_)(z),A=(null==E?void 0:E.space_type)===e2.Sn.Personal,[S,C]=(0,s.useState)(),[L,O]=(0,s.useState)(!1),[Z,F]=(0,s.useState)(),Y=(0,s.useRef)(),B=(0,s.useRef)(null),[Q,U]=(0,s.useState)(!1),P={bizCtx:{trafficScene:e4.zE.Undefined,connectorID:ol.FQ,bizSpaceID:z,connectorUID:null==M?void 0:M.user_id_str},mockSubject:{componentType:e4.re.CozeTool,componentID:m,parentComponentType:e4.re.CozePlugin,parentComponentID:D}};var G=[...(o=A,t=[{title:b.o.t("mockset_name"),dataIndex:"name",className:"min-w-[200px]",render:(e,o)=>(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(f.ZT.Text,{strong:!0,ellipsis:{showTooltip:{opts:{style:{wordBreak:"break-word"}}}},className:"min-w-[0px]",children:o.name||"-"}),(null==o?void 0:o.schemaIncompatible)?(0,d.jsx)(f.fS,{className:"ml-[10px]",shape:"circle",color:"orange",children:b.o.t("update_required")}):null]}),o.description?(0,d.jsx)(ot,{children:o.description}):"-"]})},{title:b.o.t("mock_data_counts"),dataIndex:"mockRuleQuantity",width:116,render:(e,o)=>void 0===o.mockRuleQuantity?"-":(0,d.jsx)(f.ZT.Text,{ellipsis:{showTooltip:!0},className:"text-[#1C1D2359]",children:(0,v.uf)(o.mockRuleQuantity)})},{title:b.o.t("edit_time"),dataIndex:"updateTimeInSec",width:150,sorter:!0,render:(e,o)=>o.updateTimeInSec?(0,d.jsx)("div",{children:(0,v.p6)(Number(o.updateTimeInSec),"YYYY-MM-DD HH:mm")}):"-"}],i=[{title:b.o.t("creators"),dataIndex:"creatorID",width:132,render:(e,o)=>{var t,i,r;return(null===(t=o.creator)||void 0===t?void 0:t.ID)?(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(f.qE,{src:null===(i=o.creator)||void 0===i?void 0:i.avatarUrl,size:"extra-extra-small",className:"mr-[8px]",alt:"User"}),(0,d.jsx)(f.ZT.Text,{ellipsis:{showTooltip:!0},className:"flex-1 text-[#1C1D2359]",children:null===(r=o.creator)||void 0===r?void 0:r.name})]}):"-"}}],o?t:[...t,...i]),{title:b.o.t("actions"),dataIndex:"action",width:108,render:(e,o)=>{var t,i=(null==M?void 0:M.user_id_str)===(null==o?void 0:null===(t=o.creator)||void 0===t?void 0:t.ID);return(0,d.jsx)("div",{onClick:e=>{e.stopPropagation()},children:(0,d.jsxs)(f.T,{spacing:16,children:[(0,d.jsx)(f.u,{content:b.o.t("Edit"),children:(0,d.jsx)(f._3,{disabled:!i||Q,icon:(0,d.jsx)(I.fkE,{}),onClick:()=>{W(o)},className:!i||Q?oK["icon-disabled"]:oK["icon-default"]})}),(0,d.jsx)(f.u,{content:b.o.t("Delete"),children:(0,d.jsx)(f._3,{icon:(0,d.jsx)(I.WDf,{}),className:h()(oK["icon-delete"],!i||Q?oK["icon-disabled"]:oK["icon-default"]),disabled:!i||Q,onClick:()=>{F(o)}})})]})})}}],R=()=>{O(!0)},W=(e,o)=>{var t,i,{id:r}=e||{};r&&(null===(i=x.mocksetDetail)||void 0===i||i.call(x,m,String(r),{},{state:{spaceId:z,pluginId:D,pluginName:null==N?void 0:null===(t=N.meta_info)||void 0===t?void 0:t.name,toolId:m,toolName:null==S?void 0:S.name,mockSetId:String(r),mockSetName:null==e?void 0:e.name,generationMode:null==o?void 0:o.generateMode}}))};var H=(r=(0,a._)(function*(){try{var{api_info:e=[]}=yield j.Js.GetPluginAPIs({plugin_id:D,api_ids:[m],preview_version_ts:T});if(e.length>0){var o=e.length>0?e[0]:{};C(o)}}catch(e){V.kg.error({error:e,eventName:"fetch_tool_info_fail"}),C({})}}),function(){return r.apply(this,arguments)}),{data:q,loading:K}=(0,g.Z)((0,a._)(function*(){if(!P.mockSubject.componentID||!P.mockSubject.parentComponentID)return{total:0,list:[]};try{var e,o,{mockSets:t,pageToken:i,count:r}=yield j.xd.MGetMockSet({bizCtx:P.bizCtx,mockSubject:P.mockSubject,pageLimit:_.pageSize,pageToken:_.pageToken,desc:null===(o=null===(e=_.order)||void 0===e?void 0:e.desc)||void 0===o||o,orderBy:e4.d$.UpdateTime});return Y.current=i,{total:r,list:t||[]}}catch(e){var{code:a}=e||{};return("600303107"===a||"600303108"===a)&&U(!0),{total:0,list:[]}}}),{refreshDeps:[_],onError:e=>{V.kg.error({error:e,eventName:"fetch_mockset_list_fail"})}}),J=()=>{var e;null===(e=B.current)||void 0===e||e.reset(),y(e=>(0,l._)((0,n._)({},e),{pageSize:10,pageToken:void 0,pageNo:1}))};return(0,s.useEffect)(()=>{w(),H()},[]),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:oK.page,children:(0,d.jsxs)(f._n,{title:(0,v.Z$)(b.o.t("manage_mockset")),children:[(0,d.jsx)(f._n.Header,{className:oK["layout-header"],breadcrumb:(0,d.jsx)(eo.g3,{showTooltip:{width:"300px"},pluginInfo:null==N?void 0:N.meta_info,pluginToolInfo:S,compact:!1,mockSetInfo:{}})}),(0,d.jsxs)(f._n.Content,{className:oK["layout-content"],children:[(0,d.jsxs)("div",{className:oK["header-info"],children:[(0,d.jsx)(f.ZT.Text,{className:oK["layout-header-title"],children:(null==N?void 0:null===(c=N.meta_info)||void 0===c?void 0:c.name)?b.o.t("mockset_of_toolname",{toolName:null==N?void 0:null===(p=N.meta_info)||void 0===p?void 0:p.name}):b.o.t("mockset")}),(0,d.jsx)(f.u,{style:{display:Q?"block":"none"},content:b.o.t("unreleased_plugins_tool_cannot_create_mockset"),children:(0,d.jsx)(f.y3,{onClick:R,theme:"solid",disabled:Q,children:b.o.t("create_mockset")})})]}),(0,d.jsx)(f.$,{ref:B,offsetY:207,tableProps:{loading:K,dataSource:(null==q?void 0:q.list)||[],columns:G,onRow:e=>({onClick:()=>{!Q&&W(e)}}),onChange:e=>{var o,t;(null===(o=e.sorter)||void 0===o?void 0:o.sortOrder)&&(null===(t=B.current)||void 0===t||t.reset(),y(o=>{var t;return(0,l._)((0,n._)({},o),{pageSize:10,pageNo:1,pageToken:void 0,order:{desc:(null===(t=e.sorter)||void 0===t?void 0:t.sortOrder)==="descend"}})}))}},empty:(0,d.jsx)(f.YZ,{empty:{title:b.o.t("no_mockset_yet"),description:Q?void 0:b.o.t("click_button_to_create_mockset"),btnText:Q?void 0:b.o.t("create_mockset"),btnOnClick:Q?void 0:R}}),enableLoad:!0,total:Number((null==q?void 0:q.total)||0),onLoad:()=>{y(e=>{var o;return(0,l._)((0,n._)({},e),{pageToken:Y.current,pageNo:(null!==(o=e.pageNo)&&void 0!==o?o:0)+1})})}})]})]})}),L?(0,d.jsx)(e8.a,{visible:L,initialInfo:{bizCtx:P.bizCtx,bindSubjectInfo:P.mockSubject,name:null==S?void 0:S.name},onSuccess:W,onCancel:()=>O(!1)}):null,Z?(0,d.jsx)(oW,{visible:!!Z,mockSetInfo:{detail:Z,ctx:{bizCtx:P.bizCtx,mockSubjectInfo:P.mockSubject}},onSuccess:()=>{F(void 0),J()},onCancel:()=>F(void 0)}):null]})},oX=t("962289"),o$=t("671261"),o0=t("62851"),o1=t("944970"),o2=t("437134"),o4=e=>(null==e?void 0:e.assist_type)?[eY.rN.DEFAULT,(0,eY.Eo)(e.assist_type)]:[e.type],{Text:o3}=f.ZT,o5=e=>{var o,{check:t=0,useBlockWrap:i=!1,record:r,disabled:a,selectCallback:n,enableFileType:l=!1}=e,[c,u]=(0,s.useState)(o4(r)),[g,p]=(0,s.useState)(0),m=r.name===eY.sp,v=(null!==(o=r.deep)&&void 0!==o?o:0)>1&&r.name!==eY.sp;(0,s.useEffect)(()=>{if(0!==t)x(c)},[t]);var x=e=>{p((null==e?void 0:e[0])?0:1)},M=(0,eY.YA)(m,l&&!v);return(0,d.jsxs)("span",{style:i?{display:"inline-block",width:"100%"}:{},children:[(0,d.jsx)(f.oL,{treeData:M,validateStatus:g?"error":"default",value:c,disabled:a,onChange:e=>{e[1]?n([e[0],(0,eY.RE)(e[1])]):n([e[0]]),u(e),x(e)},displayRender:(e,o)=>{var t=e[0];return c[1]&&(t=c[1]===eY.rN.DEFAULT?(0,eY.Zo)(eY.rN.DEFAULT,m):e[1]),(0,d.jsx)(o3,{ellipsis:{showTooltip:!0},children:t})},dropdownClassName:eZ.Z.cascaderDropdown,style:{width:"100%"}}),(0,d.jsx)("br",{}),0!==g&&(0,d.jsx)("div",{style:{position:"relative"},children:(0,d.jsxs)("span",{className:h()(eZ.Z["form-check-tip"],"errorClassTag",eZ.Z.w110),children:[(0,d.jsx)(os.Z,{className:eZ.Z["plugin-icon-error"]}),(0,d.jsx)(o3,{component:"span",ellipsis:{showTooltip:{type:"tooltip",opts:{style:{maxWidth:"100%"}}}},className:eZ.Z["plugin-tooltip-error"],children:1===g&&(0,d.jsx)("span",{children:b.o.t("plugin_Parameter_type")})})]})})]})},o6=e=>{var{record:o,disabled:t,data:i,setData:r,checkFlag:a,isResponse:c,updateNodeWithData:s,addChildNode:u,enableFileType:g=!1}=e,p=e=>{var o=(0,H.Z)(i);(0,en.Rm)(o,e[eY.Fc])&&r(o)};return t?(0,d.jsx)(d.Fragment,{children:(0,eY.lR)(o,o.name===eY.sp)}):(0,d.jsx)(o5,{check:a,record:o,enableFileType:g,selectCallback:e=>{var[t,i]=e,r=t;t===eY.rN.DEFAULT&&(r=M.rH.String),!c&&o.global_default&&s({record:o,key:["global_default","global_disable"],value:["",!1],updateData:!0});var a={record:o,key:["type","assist_type"],value:[r,null!=i?i:null]},d=null==o?void 0:o.type;r===M.rH.Array?(s((0,l._)((0,n._)({},a),{updateData:!0})),u({record:o,isArray:!0,type:r,recordType:d})):r===M.rH.Object?(s((0,l._)((0,n._)({},a),{updateData:!0})),u({record:o,isArray:!1,type:r,recordType:d})):(null==o?void 0:o.type)===M.rH.Array||(null==o?void 0:o.type)===M.rH.Object?(s(a),p(o)):s((0,l._)((0,n._)({},a),{updateData:!0}))}})},o8=e=>{var{data:o,checkFlag:t,isResponse:i=!1,disabled:r,setCheckFlag:a,setData:c,showSecurityCheckFailedMsg:s,setShowSecurityCheckFailedMsg:u,enableFileType:g=!1}=e,p=e=>{var{record:t,isArray:i=!1,type:r,recordType:d}=e,s=(0,H.Z)(o),u=(e,o)=>{var t=e[o[0]];t&&t.type===M.rH.Array?(t.global_default="",t.global_disable=!1):t&&t.sub_parameters&&u(t.sub_parameters,o.slice(1))};a(0);var g={};(0,en.bd)({data:o,callback:(e,o)=>{e[eY.Fc]===t[eY.Fc]&&(g=(0,l._)((0,n._)({},e),{path:o}),u(s,o))}});var p=((null==g?void 0:g.path)||[]).map(e=>[e,eY.Il]).flat();if(d){var m=(0,H.Z)(p);m.pop(),m.push("type"),(0,oX.Z)(s,m)!==d&&(0,en.Rm)(s,t[eY.Fc]),(0,o$.Z)(s,m,r)}Array.isArray((0,oX.Z)(s,p))?(0,o$.Z)(s,p,[...(0,oX.Z)(s,p),(0,en.ub)({isArray:i,iscChildren:!0,deep:t.deep+1})]):(0,o$.Z)(s,p,[(0,en.ub)({isArray:i,iscChildren:!0,deep:t.deep+1})]),c(s)},m=e=>{var t=(0,H.Z)(o);(0,en.IK)(t,e[eY.Fc])&&c(t)},h=e=>{var{record:t,key:i,value:r,updateData:a=!1,inherit:n=!1}=e;Array.isArray(i)?i.forEach((e,i)=>{(0,en.jd)({data:o,targetKey:t[eY.Fc],field:e,value:r[i]})}):(0,en.jd)({data:o,targetKey:t[eY.Fc],field:i,value:r,inherit:n}),a&&c((0,H.Z)(o))},x=[{title:()=>(0,d.jsx)(o1.vw,{name:b.o.t("Create_newtool_s3_table_name"),required:!0,toolTipText:i?b.o.t("Create_newtool_s3_table_name_tooltip"):b.o.t("Create_newtool_s2_table_name_tooltip")}),key:"name",className:eZ.Z["no-wrap-min-width"],render:e=>r?(0,d.jsx)(f.ZT.Text,{component:"span",ellipsis:{showTooltip:{type:"tooltip",opts:{style:{maxWidth:"100%"}}}},style:{maxWidth:"calc(100% - ".concat(20*(e.deep||1),"px)")},children:e.name}):(0,d.jsx)(o1.O6,{check:t,val:null==e?void 0:e.name,data:o,placeholder:b.o.t("Create_newtool_s2_table_name_empty"),useBlockWrap:!0,checkSame:!0,targetKey:e[eY.Fc],dynamicWidth:!0,deep:e.deep,callback:o=>{h({record:e,key:"name",value:o,updateData:!0}),s&&(null==u||u(!1))}})},{title:()=>(0,d.jsx)(o1.vw,{name:b.o.t("Create_newtool_s2_table_des"),required:!i,toolTipText:i?b.o.t("Create_newtool_s3_table_des_tooltip"):b.o.t("Create_newtool_s2_table_des_tooltip")}),key:"desc",render:e=>r?(0,d.jsx)(f.ZT.Text,{component:"div",ellipsis:{showTooltip:{opts:{style:{wordBreak:"break-word"}}}},style:{maxWidth:"100%"},children:e.desc}):(0,d.jsx)(o1.O6,{check:t,width:"100%",placeholder:b.o.t("plugin_Parameter_des"),val:null==e?void 0:e.desc,useCheck:!1,checkAscii:!0,filterSpace:!1,max:300,isRequired:!i,callback:o=>{h({record:e,key:"desc",value:o}),s&&(null==u||u(!1))}})},{title:()=>(0,d.jsx)(o1.vw,{name:b.o.t("Create_newtool_s3_table_type"),required:!0}),key:"type",width:120,render:e=>(0,d.jsx)(o6,{record:e,disabled:r,data:o,setData:c,checkFlag:t,updateNodeWithData:h,addChildNode:p,enableFileType:g})},{title:()=>(0,d.jsx)(o1.vw,{name:b.o.t("Create_newtool_s2_table_method"),required:!0}),key:"location",width:120,render:e=>{if(void 0===e.location)return(0,d.jsx)(d.Fragment,{});var o=v.gS.array2Map(eY.pq,"value","label");return r?o[e.location]:(0,d.jsx)(f.Cj,{theme:"light",defaultValue:e.location,onChange:o=>{h({record:e,key:"location",value:o,updateData:!0,inherit:!0})},style:{width:"100%"},children:eY.pq.map(o=>(0,d.jsx)(f.Cj.Option,{value:o.value,children:o.label},(null==e?void 0:e.id)+o.label))})}},{title:b.o.t("Create_newtool_s2_table_required"),width:80,key:"default",render:e=>(0,d.jsx)(f.XZ,{style:{position:"relative",left:18},disabled:r,defaultChecked:e.is_required,onChange:o=>{o.target.checked&&!e.global_default&&h({record:e,key:"global_disable",value:!1,updateData:!0,inherit:!0}),h({record:e,key:"is_required",value:o.target.checked,updateData:!0,inherit:!0})}})},{title:b.o.t("plugin_api_list_table_action"),key:"addChild",width:107,render:e=>(0,d.jsxs)(f.T,{children:[e.type===M.rH.Object&&(0,d.jsx)(f.u,{content:b.o.t("plugin_form_add_child_tooltip"),children:(0,d.jsx)(f._3,{disabled:r,style:{marginLeft:"8px"},onClick:()=>p({record:e}),icon:(0,d.jsx)(I.h16,{}),type:"secondary"})}),(0,en.Hn)(o,e[eY.Fc])&&(0,d.jsx)(f.u,{content:b.o.t("Delete"),children:(0,d.jsx)(f._3,{disabled:r,style:{marginLeft:"8px"},onClick:()=>m(e),icon:(0,d.jsx)(I.WDf,{}),type:"secondary"})})]})}];if(!i&&x.splice(-1,0,{title:()=>(0,d.jsx)(o1.vw,{name:b.o.t("plugin_edit_tool_default_value_config_item_default_value")}),key:"global_default",width:120,render:e=>(0,d.jsx)(o2.Z,{record:e,data:o,setData:c})},{title:(0,d.jsx)(o1.vw,{name:b.o.t("plugin_edit_tool_default_value_config_item_enable"),toolTipText:b.o.t("plugin_edit_tool_default_value_config_item_enable_tip")}),key:"global_disable",width:78,render:e=>{if(void 0===e.global_default)return(0,d.jsx)(d.Fragment,{});var o=(0,d.jsx)(f.rs,{style:{position:"relative",top:3,left:12},defaultChecked:!e.global_disable,disabled:e.is_required&&!e.global_default,onChange:o=>{h({record:e,key:"global_disable",value:!o,updateData:!0,inherit:!0})}});return e.is_required&&!e.global_default?(0,d.jsx)(f.u,{content:b.o.t("plugin_edit_tool_default_value_config_item_enable_disable_tip"),children:o}):o}}),i){var j=x.findIndex(e=>"default"===e.key);x.splice(j,1),x.splice(-1,0,{title:(0,d.jsx)(o1.vw,{name:b.o.t("plugin_edit_tool_default_value_config_item_enable"),toolTipText:b.o.t("plugin_edit_tool_output_param_enable_tip")}),key:"global_disable",width:78,render:e=>void 0===e.global_default?(0,d.jsx)(d.Fragment,{}):(0,d.jsx)(f.rs,{style:{position:"relative",top:3,left:12},defaultChecked:!e.global_disable,onChange:o=>{h({record:e,key:"global_disable",value:!o,updateData:!0,inherit:!0})}})})}return(0,o0.Z)(()=>x,e=>r?e.slice(0,i?-3:-4):e,e=>i?e.filter(e=>"location"!==e.key):e)()},o9=t("851645"),o7=e=>{var o,t,{apiInfo:i,pluginId:r,requestParams:n,responseParams:l,apiId:c,disabled:u,showSecurityCheckFailedMsg:g,setShowSecurityCheckFailedMsg:p,editVersion:m,pluginType:v,functionName:x,spaceID:_,onSuccess:k,renderEnhancedComponent:y}=e,[N,w]=(0,s.useState)(l||[]),[D,z]=(0,s.useState)(!1),[T,E]=(0,s.useState)(0),[A,S]=(0,s.useState)(!1),[C,L]=(0,s.useState)(!1),O=(0,ej.Z)(function(e){var o=!(arguments.length>1)||void 0===arguments[1]||arguments[1],t=e;o&&(t=(0,en.w)(e,"global_default")),w(t)});(0,s.useEffect)(()=>{if(!(Array.isArray(l)&&0===l.length&&Array.isArray(N))||0!==N.length)O(l||[])},[u,l]);var Z=o8({data:N,flag:D,checkFlag:T,setCheckFlag:E,setFlag:z,setData:O,isResponse:!0,disabled:u,showSecurityCheckFailedMsg:g,setShowSecurityCheckFailedMsg:p,enableFileType:!0});var F=(o=(0,a._)(function*(){if(E(T+1),yield(0,en._v)(100),!c||document.getElementsByClassName("errorClassTag").length>0)return(0,en.$S)(".errorClassTag"),f.FN.error({content:(0,eO.u)(b.o.t("tool_new_S2_feedback_failed")),duration:3,theme:"light",showClose:!1}),!1;if(!c)return!1;try{var e={plugin_id:r,api_id:c,response_params:(0,en.Vv)(N,!1),edit_version:m,function_name:x},o=yield j.Js.UpdateAPI(e,{__disableErrorToast:!0});return null==k||k(o),!0}catch(e){var{code:t,msg:i}=e;return Number(t)===el.B.SAFE_CHECK?null==p||p(!0):f.FN.error({content:(0,eO.u)(i)}),!1}}),function(){return o.apply(this,arguments)});var Y=(t=(0,a._)(function*(){try{L(!0);var e,o=yield j.Js.DebugAPI({plugin_id:r,api_id:c,parameters:JSON.stringify({}),operation:2});(null==o?void 0:o.success)&&(null==o?void 0:o.response_params)?(O(o.response_params),f.FN.success({content:b.o.t("plugin_s3_success"),duration:3,theme:"light",showClose:!1})):f.FN.error({content:(0,eO.u)(null!==(e=null==o?void 0:o.reason)&&void 0!==e?e:b.o.t("plugin_s3_failed")),duration:3,theme:"light",showClose:!1})}catch(e){f.FN.error({content:(0,eO.u)(b.o.t("plugin_s3_failed")),duration:3,theme:"light",showClose:!1}),V.kg.persist.error({message:"Custom Error: debug api failed",error:e})}L(!1)}),function(){return t.apply(this,arguments)}),B=(0,en.lf)(N);return{submitResponseParams:F,responseParamsNode:(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:eZ.Z["table-wrapper"],style:{minWidth:1008,overflowY:"auto"},children:[(0,d.jsx)(f.iA,{style:{minWidth:"calc(1008px + ".concat((B-6)*20,"px)")},pagination:!1,columns:Z,dataSource:N,rowKey:eY.Fc,childrenRecordName:eY.Il,expandAllRows:!0,className:h()(u?eZ.Z["request-params"]:eZ.Z["request-params-edit"],eZ.Z["table-style-list"]),empty:(0,d.jsx)("div",{})}),!u&&(0,d.jsx)("div",{className:eZ.Z["add-params-btn-wrap"],style:Array.isArray(N)&&0===N.length?{borderTop:0}:{},children:(0,d.jsx)(f.y3,{disabled:u,icon:(0,d.jsx)(I.N_R,{}),style:{marginTop:12},type:"tertiary",onClick:()=>{E(0);var e=(0,H.Z)(N);e.push((0,en.ub)()),O(e),z(!D),setTimeout(()=>{(0,en.Og)(document.getElementsByClassName("semi-table-body")[0])},100)},children:b.o.t("Create_newtool_s3_table_new")})})]}),(0,d.jsx)(f.M5,{visible:A,title:b.o.t("plugin_s3_Parse"),className:eZ.Z["input-modal"],keepDOM:!1,footer:(0,d.jsx)(d.Fragment,{}),width:800,maskClosable:!1,onCancel:()=>S(!1),children:(0,d.jsx)(o9.y,{disabled:u,pluginId:r,apiId:c,requestParams:n,operation:2,btnText:b.o.t("Create_newtool_s3_button_auto"),callback:e=>{var{response_params:o,status:t,failReason:i}=e;t===el.Q.PASS&&o?((0,en.eO)(o),O(o),f.FN.success({content:b.o.t("plugin_s3_success"),duration:3,theme:"light",showClose:!1})):f.FN.error({content:(0,eO.u)(null!=i?i:b.o.t("plugin_s3_failed")),duration:3,theme:"light",showClose:!1}),S(!1)}})})]}),extra:(0,d.jsxs)(d.Fragment,{children:[null==y?void 0:y({disabled:!(null==N?void 0:N.length)||u,src:"response",originParams:N,apiInfo:i,onSetParams:e=>O(e),spaceID:_,pluginId:r}),(0,d.jsx)(W.zx,{disabled:u||v===M.zV.LOCAL,className:"!mr-2",color:"primary",loading:C,onClick:e=>{e.stopPropagation(),Array.isArray(n)&&n.length>0?S(!0):Y()},children:C?b.o.t("plugin_s3_Parsing"):b.o.t("Create_newtool_s3_button_auto")})]})}},te=e=>{var o,{apiInfo:t,plugin_id:i,tool_id:r,editVersion:c,pluginInfo:u,canEdit:g,handleInit:p,wrapWithCheckLock:m,debugApiInfo:h,setDebugApiInfo:v,spaceID:f,onSuccess:x,renderParamsComponent:j}=e,[_,k]=(0,s.useState)(!1),[y,N]=(0,s.useState)(!0),{responseParamsNode:w,submitResponseParams:D,extra:z}=o7({apiInfo:t,pluginId:i||"",responseParams:null==t?void 0:t.response_params,requestParams:null==t?void 0:t.request_params,apiId:r||"",disabled:y,showSecurityCheckFailedMsg:_,setShowSecurityCheckFailedMsg:k,editVersion:c,pluginType:null==u?void 0:u.plugin_type,functionName:(null==u?void 0:u.plugin_type)===M.zV.LOCAL?null==t?void 0:t.function_name:void 0,spaceID:f,onSuccess:x,renderEnhancedComponent:j});return(0,s.useEffect)(()=>{!y&&v((0,l._)((0,n._)({},h),{debug_example:{},debug_example_status:M.ji.Disable}))},[y]),{isResponseParamsDisabled:y,header:b.o.t("Create_newtool_s3_Outputparameters"),itemKey:"response",extra:(0,d.jsxs)(d.Fragment,{children:[_?(0,d.jsx)(eG,{step:eY.TS}):null,!y&&g?(0,d.jsx)(eN,{}):null,y?null:z,y?null:(0,d.jsx)(W.zx,{onClick:e=>{e.stopPropagation(),N(!0)},color:"primary",className:"mr-2",children:b.o.t("project_plugin_setup_metadata_cancel")}),g&&!y?(0,d.jsx)(W.zx,{onClick:(o=(0,a._)(function*(e){e.stopPropagation(),(yield D())&&(p(),N(!0))}),function(e){return o.apply(this,arguments)}),className:"mr-2",children:b.o.t("project_plugin_setup_metadata_save")}):null,g&&y?(0,d.jsx)(W.zx,{icon:(0,d.jsx)(I.yl5,{className:"!pr-0"}),color:"primary",className:"!bg-transparent !coz-fg-secondary",onClick:e=>{var o,t=document.querySelector(".plugin-tool-detail-response .semi-collapsible-wrapper");0!==parseInt(null==t?void 0:null===(o=t.style)||void 0===o?void 0:o.height)&&e.stopPropagation(),m(()=>{N(!1)})()},children:b.o.t("project_plugin_setup_metadata_edit")}):null]}),content:w,classNameWrap:"plugin-tool-detail-response"}},to=e=>{var o,{apiInfo:t,pluginId:i,apiId:r,requestParams:n,disabled:l,showSecurityCheckFailedMsg:c,setShowSecurityCheckFailedMsg:u,editVersion:g,functionName:p,spaceID:m,onSuccess:v,renderEnhancedComponent:x}=e,[M,_]=(0,s.useState)(n||[]),k=function(e){var o=!(arguments.length>1)||void 0===arguments[1]||arguments[1],t=e;o&&(t=(0,en.w)(e,"global_default")),_(t)},[y,N]=(0,s.useState)(!1),[w,D]=(0,s.useState)(0),z=o8({data:M,flag:y,checkFlag:w,setCheckFlag:D,setFlag:N,setData:k,disabled:l,showSecurityCheckFailedMsg:c,setShowSecurityCheckFailedMsg:u,enableFileType:!0});(0,s.useEffect)(()=>{if(!(Array.isArray(n)&&0===n.length&&Array.isArray(M))||0!==M.length)k(n||[])},[l,n]);var T=(o=(0,a._)(function*(){D(w+1);if(yield(0,en._v)(100),!r||document.getElementsByClassName("errorClassTag").length>0)return(0,en.$S)(".errorClassTag"),f.FN.error({content:b.o.t("tool_new_S2_feedback_failed"),duration:3,theme:"light",showClose:!1}),!1;try{var e=yield j.Js.UpdateAPI({plugin_id:i,api_id:r,request_params:M,edit_version:g,function_name:p},{__disableErrorToast:!0});return null==v||v(e),!0}catch(e){var{code:o,msg:t}=e;return Number(o)===el.B.SAFE_CHECK?null==u||u(!0):f.FN.error({content:(0,eO.u)(t)}),!1}}),function(){return o.apply(this,arguments)}),E=(0,en.lf)(M);return{submitRequestParams:T,requestParamsNode:(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:eZ.Z["table-wrapper"],style:{minWidth:1008,overflowY:"auto"},children:[(0,d.jsx)(f.iA,{style:{minWidth:"calc(1008px + ".concat((E-4)*(E<13?19:40),"px)")},pagination:!1,columns:z,dataSource:M,rowKey:eY.Fc,childrenRecordName:eY.Il,expandAllRows:!0,className:h()(l?eZ.Z["request-params"]:eZ.Z["request-params-edit"],eZ.Z["table-style-list"]),empty:(0,d.jsx)("div",{})}),!l&&(0,d.jsx)("div",{style:Array.isArray(M)&&0===M.length?{borderTop:0}:{},className:eZ.Z["add-params-btn-wrap"],children:(0,d.jsx)(f.y3,{disabled:l,icon:(0,d.jsx)(I.N_R,{}),style:{marginTop:12},type:"tertiary",onClick:()=>{D(0);var e=(0,H.Z)(M);e.push((0,en.ub)()),k(e),setTimeout(()=>{(0,en.Og)(document.getElementsByClassName("semi-table-body")[0])},100)},children:b.o.t("Create_newtool_s3_table_new")})})]})}),nlTool:null==x?void 0:x({disabled:!(null==M?void 0:M.length)||l,src:"request",originParams:M,apiInfo:t,onSetParams:e=>_(e),spaceID:m,pluginId:i})}},tt=e=>{var o,{apiInfo:t,plugin_id:i,tool_id:r,pluginInfo:n,canEdit:l,handleInit:c,wrapWithCheckLock:u,editVersion:g,spaceID:p,onSuccess:m,renderParamsComponent:h}=e,[v,f]=(0,s.useState)(!1),[x,j]=(0,s.useState)(!0),{requestParamsNode:_,submitRequestParams:k,nlTool:y}=to({apiInfo:t,pluginId:i||"",requestParams:null==t?void 0:t.request_params,apiId:r,disabled:x,showSecurityCheckFailedMsg:v,setShowSecurityCheckFailedMsg:f,editVersion:g,functionName:(null==n?void 0:n.plugin_type)===M.zV.LOCAL?null==t?void 0:t.function_name:void 0,spaceID:p,onSuccess:m,renderEnhancedComponent:h});return{isRequestParamsDisabled:x,itemKey:"request",header:b.o.t("Create_newtool_s2"),extra:(0,d.jsxs)(d.Fragment,{children:[v?(0,d.jsx)(eG,{step:eY.TS}):null,x?null:y,x?null:(0,d.jsx)(W.zx,{onClick:e=>{e.stopPropagation(),j(!0)},color:"primary",className:"mr-2",children:b.o.t("project_plugin_setup_metadata_cancel")}),l&&!x?(0,d.jsx)(W.zx,{onClick:(o=(0,a._)(function*(e){e.stopPropagation(),(yield k())&&(c(),j(!0))}),function(e){return o.apply(this,arguments)}),className:"mr-2",children:b.o.t("project_plugin_setup_metadata_save")}):null,l&&x?(0,d.jsx)(W.zx,{icon:(0,d.jsx)(I.yl5,{className:"!pr-0"}),color:"primary",className:"!bg-transparent !coz-fg-secondary",onClick:e=>{var o,t=document.querySelector(".plugin-tool-detail-request .semi-collapsible-wrapper");0!==parseInt(null==t?void 0:null===(o=t.style)||void 0===o?void 0:o.height)&&e.stopPropagation(),u(()=>{j(!1)})()},children:b.o.t("project_plugin_setup_metadata_edit")}):null]}),content:_,classNameWrap:"plugin-tool-detail-request"}},ti=e=>{var o,{space_id:t,plugin_id:i,tool_id:r,apiInfo:n,canEdit:l,handleInit:c,wrapWithCheckLock:u,editVersion:g,renderDescComponent:p}=e,[m,h]=(0,s.useState)(!1),[v,f]=(0,s.useState)(!0),{baseInfoNode:x,submitBaseInfo:M}=eF({pluginId:i||"",apiId:r,baseInfo:n,showModal:!1,disabled:v,showSecurityCheckFailedMsg:m,setShowSecurityCheckFailedMsg:h,editVersion:g,space_id:t,renderEnhancedComponent:p});return{isBaseInfoDisabled:v,header:b.o.t("Create_newtool_s1_title"),itemKey:"baseInfo",extra:(0,d.jsxs)(d.Fragment,{children:[m?(0,d.jsx)(eG,{step:eY.dT}):null,!v&&(0,d.jsx)(W.zx,{color:"primary",className:"mr-2",onClick:e=>{e.stopPropagation(),f(!0)},children:b.o.t("project_plugin_setup_metadata_cancel")}),l&&!v?(0,d.jsx)(W.zx,{onClick:(o=(0,a._)(function*(e){e.stopPropagation(),(yield M())&&c(),f(!0)}),function(e){return o.apply(this,arguments)}),className:"mr-2",children:b.o.t("project_plugin_setup_metadata_save")}):null,l&&v?(0,d.jsx)(W.zx,{icon:(0,d.jsx)(I.yl5,{className:"!pr-0"}),color:"primary",className:"!bg-transparent !coz-fg-secondary",onClick:e=>{var o,t=document.querySelector(".plugin-tool-detail-baseInfo .semi-collapsible-wrapper");0!==parseInt(null==t?void 0:null===(o=t.style)||void 0===o?void 0:o.height)&&e.stopPropagation(),u(()=>{f(!1)})()},children:b.o.t("project_plugin_setup_metadata_edit")}):null]}),content:x,classNameWrap:"plugin-tool-detail-baseInfo"}},tr=t("625196"),{Option:ta}=f.N5,tn=e=>{var o,t,{pluginId:i,pluginMeta:r,apiId:n="",baseInfo:l={},showModal:c,disabled:u,showSecurityCheckFailedMsg:g,setShowSecurityCheckFailedMsg:p,editVersion:m,pluginType:h,onSuccess:v}=e,{url:x}=r,_=(0,s.useRef)(null);(0,s.useEffect)(()=>{var e,o;null===(o=_.current)||void 0===o||o.formApi.setValues({path:l.path,method:l.method||M.fM.GET,function_name:l.function_name,auth_mode:(null===(e=l.api_extend)||void 0===e?void 0:e.auth_mode)||M.ZZ.Required})},[l.path,c,u,r,l.method,l.function_name,null===(t=l.api_extend)||void 0===t?void 0:t.auth_mode]);var k=(o=(0,a._)(function*(){if(!(yield null===(e=_.current)||void 0===e?void 0:e.formApi.validate().then(()=>!0).catch(()=>!1))||!n)return!1;var e,o,t,r=null===(o=_.current)||void 0===o?void 0:o.formApi.getValues(),a={api_id:n,plugin_id:i,path:r.path,method:r.method,api_extend:{auth_mode:r.auth_mode},edit_version:m,function_name:r.function_name};try{return t=yield j.Js.UpdateAPI(a,{__disableErrorToast:!0}),null==v||v(t),!0}catch(e){var{code:l,msg:d}=e;return Number(l)===el.B.SAFE_CHECK?null==p||p(!0):f.FN.error({content:(0,eO.u)(d)}),!1}}),function(){return o.apply(this,arguments)}),y=()=>{g&&(null==p||p(!1))};return{submitBaseInfo:k,baseInfoNode:(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(f.l0,{showValidateIcon:!1,ref:_,disabled:u,className:eZ.Z["base-info-form"],children:()=>{var e,o,t,i;return u?(0,d.jsxs)(d.Fragment,{children:[h===M.zV.LOCAL&&(0,d.jsx)(f.l0.Slot,{label:{text:b.o.t("create_local_plugin_basic_tool_function"),required:!0},children:null!==(i=l.function_name)&&void 0!==i?i:"-"}),h===M.zV.PLUGIN&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(f.l0.Slot,{label:{text:b.o.t("Create_newtool_s1_url"),required:!0},children:String(x)+l.path}),(0,d.jsx)(f.l0.Slot,{label:{text:b.o.t("Create_newtool_s1_method"),required:!0,extra:(0,d.jsx)(tr.O,{data:eY.iq})},children:tl[(null==l?void 0:l.method)||M.fM.GET]})]}),(null==r?void 0:null===(e=r.auth_type)||void 0===e?void 0:e.includes(M.Mv.OAuth))?(0,d.jsx)(f.l0.Slot,{label:{text:b.o.t("plugin_edit_tool_oauth_enabled_title"),required:!0,extra:(0,d.jsx)(f.u,{content:b.o.t("plugin_edit_tool_oauth_enabled_title_hover_tip"),children:(0,d.jsx)(I.aUG,{style:{color:"rgba(28, 29, 35, 0.35)"}})})},children:td[(null===(o=l.api_extend)||void 0===o?void 0:o.auth_mode)||M.ZZ.Required]}):null]}):(0,d.jsxs)(d.Fragment,{children:[h===M.zV.LOCAL&&(0,d.jsx)(f.P6,{className:eZ.Z["textarea-single-line"],field:"function_name",label:b.o.t("create_local_plugin_basic_tool_function"),placeholder:b.o.t("create_local_plugin_basic_tool_function_input_placeholder"),rows:1,trigger:["blur","change"],maxCount:30,maxLength:30,rules:[{required:!0,message:b.o.t("create_local_plugin_basic_warning_no_tool_function_entered")}],onChange:y}),h===M.zV.PLUGIN&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(f.K9,{field:"path",label:{text:b.o.t("Create_newtool_s1_url")},trigger:["blur","change"],addonBefore:(0,d.jsx)("div",{className:eZ.Z["plugin-url-prefix"],children:(0,d.jsx)(f.ZT.Text,{ellipsis:{showTooltip:{type:"tooltip",opts:{content:x,style:{wordBreak:"break-word"}}}},children:x})}),style:{width:"100%"},className:eZ.Z["plugin-url-input"],placeholder:b.o.t("Create_newtool_s1_url_empty"),rules:[{required:!0,message:b.o.t("Create_newtool_s1_url_error2")},{pattern:/^\//,message:b.o.t("Create_newtool_s1_url_error1")},{pattern:/^[\x00-\x7F]+$/,message:b.o.t("tool_new_S1_URL_error")}]}),(0,d.jsx)(f.N5,{field:"method",initValue:M.fM.GET,label:{text:b.o.t("Create_newtool_s1_method"),extra:(0,d.jsx)(tr.O,{data:eY.iq})},showClear:!0,trigger:["blur","change"],style:{width:"100%",borderRadius:"8px"},placeholder:b.o.t("workflow_detail_condition_pleaseselect"),rules:[{required:!0,message:b.o.t("workflow_detail_condition_pleaseselect")}],children:[M.fM.GET,M.fM.POST,M.fM.PUT,M.fM.DELETE,M.fM.PATCH].map(e=>(0,d.jsx)(ta,{value:e,children:tl[e]},e))}),(null==r?void 0:null===(t=r.auth_type)||void 0===t?void 0:t.includes(M.Mv.OAuth))?(0,d.jsx)(f.N5,{field:"auth_mode",initValue:M.ZZ.Required,label:{text:b.o.t("plugin_edit_tool_oauth_enabled_title"),extra:(0,d.jsx)(f.u,{content:b.o.t("plugin_edit_tool_oauth_enabled_title_hover_tip"),children:(0,d.jsx)(I.aUG,{style:{color:"rgba(28, 29, 35, 0.35)"}})})},showClear:!0,trigger:["blur","change"],style:{width:"100%",borderRadius:"8px"},placeholder:b.o.t("workflow_detail_condition_pleaseselect"),rules:[{required:!0,message:b.o.t("workflow_detail_condition_pleaseselect")}],children:[M.ZZ.Required,M.ZZ.Supported,M.ZZ.Disable].map(e=>(0,d.jsx)(ta,{value:e,children:td[e]},e))}):null]})]})}})})}},tl={[M.fM.GET]:b.o.t("Create_newtool_s1_method_get"),[M.fM.POST]:b.o.t("Create_newtool_s1_method_post"),[M.fM.PUT]:b.o.t("Create_newtool_s1_method_put"),[M.fM.DELETE]:b.o.t("Create_newtool_s1_method_delete"),[M.fM.PATCH]:b.o.t("Create_tool_s1_method_patch_name")},td={[M.ZZ.Required]:b.o.t("plugin_edit_tool_oauth_enabled_status_auth_required"),[M.ZZ.Supported]:b.o.t("plugin_edit_tool_oauth_enabled_status_auth_optional"),[M.ZZ.Disable]:b.o.t("plugin_edit_tool_oauth_enabled_status_auth_disabled")},tc=e=>{var o,{plugin_id:t,pluginInfo:i,tool_id:r,apiInfo:n,space_id:l,canEdit:c,handleInit:u,wrapWithCheckLock:g,editVersion:p,onSuccess:m}=e,[h,v]=(0,s.useState)(!1),[f,x]=(0,s.useState)(!0),{baseInfoNode:M,submitBaseInfo:j}=tn({pluginId:t||"",pluginMeta:(null==i?void 0:i.meta_info)||{},apiId:r,baseInfo:n,showModal:!1,disabled:f,showSecurityCheckFailedMsg:h,setShowSecurityCheckFailedMsg:v,editVersion:p,pluginType:null==i?void 0:i.plugin_type,spaceId:l,onSuccess:m});return{isBaseMoreDisabled:f,header:b.o.t("project_plugin_setup_metadata_more_info"),itemKey:"baseMore",extra:(0,d.jsxs)(d.Fragment,{children:[h?(0,d.jsx)(eG,{step:eY.ls}):null,!f&&(0,d.jsx)(W.zx,{color:"primary",className:"mr-2",onClick:e=>{e.stopPropagation(),x(!0)},children:b.o.t("project_plugin_setup_metadata_cancel")}),c&&!f?(0,d.jsx)(W.zx,{onClick:(o=(0,a._)(function*(e){e.stopPropagation(),(yield j())&&u(),x(!0)}),function(e){return o.apply(this,arguments)}),className:"mr-2",children:b.o.t("project_plugin_setup_metadata_save")}):null,c&&f?(0,d.jsx)(W.zx,{icon:(0,d.jsx)(I.yl5,{className:"!pr-0"}),color:"primary",className:"!bg-transparent !coz-fg-secondary",onClick:e=>{var o,t=document.querySelector(".plugin-tool-detail-baseMore .semi-collapsible-wrapper");0!==parseInt(null==t?void 0:null===(o=t.style)||void 0===o?void 0:o.height)&&e.stopPropagation(),g(()=>{x(!1)})()},children:b.o.t("project_plugin_setup_metadata_edit")}):null]}),content:M,classNameWrap:"plugin-tool-detail-baseMore"}};let ts=(0,ev.A)(function(e){return s.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},e),s.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.28 4.24a1.5 1.5 0 0 1 0 2.12l-5.66 5.66 5.66 5.65a1.5 1.5 0 1 1-2.12 2.13l-6.72-6.72a1.5 1.5 0 0 1 0-2.12l6.72-6.72a1.5 1.5 0 0 1 2.12 0Z",fill:"currentColor"}))},"chevron_left");var tu=e=>{var o,t,{debugApiInfo:i,canEdit:r,plugin_id:a,tool_id:n,unlockPlugin:l,editVersion:c,pluginInfo:u,onDebugSuccessCallback:g}=e,p=(0,k.av)(),[m,h]=(0,s.useState)(),[v,f]=(0,s.useState)(!1),[x]=(0,s.useState)(!1),{debugFooterNode:M,setDebugExample:j,debugExample:_}=ep({apiInfo:i,loading:x,dugStatus:m,btnLoading:!1,nextStep:()=>{var e;null===(e=p.toResource)||void 0===e||e.call(p,"plugin",a),l()},editVersion:c});return{itemKey:"tool_debug",header:b.o.t("Create_newtool_s4_debug"),extra:(0,d.jsx)(d.Fragment,{children:r?M:null}),content:i&&n?(0,d.jsx)(ed.Debug,{pluginType:null==u?void 0:u.plugin_type,disabled:!1,setDebugStatus:h,pluginId:String(a),apiId:String(n),apiInfo:i,pluginName:String(null==u?void 0:null===(o=u.meta_info)||void 0===o?void 0:o.name),setDebugExample:j,debugExample:_}):(0,d.jsx)(d.Fragment,{}),modalContent:(0,d.jsxs)(d.Fragment,{children:[i&&n?(0,d.jsx)(W.zx,{onClick:()=>{f(!0)},icon:(0,d.jsx)(eM.NvC,{}),color:"highlight",children:b.o.t("project_plugin_testrun")}):null,(0,d.jsx)(W.u_,{title:b.o.t("project_plugin_testrun"),width:1e3,visible:v,onOk:()=>f(!1),onCancel:()=>f(!1),closeOnEsc:!0,footer:M,children:(0,d.jsx)(ed.Debug,{pluginType:null==u?void 0:u.plugin_type,disabled:!1,setDebugStatus:h,pluginId:String(a),apiId:String(n),apiInfo:i,pluginName:String(null==u?void 0:null===(t=u.meta_info)||void 0===t?void 0:t.name),setDebugExample:j,debugExample:_,onSuccessCallback:g})})]})}},tg=t("523878"),tp={};tp.styleTagTransform=O(),tp.setAttributes=A(),tp.insert=T().bind(null,"head"),tp.domAPI=D(),tp.insertStyleElement=C(),N()(tg.Z,tp);var tm=tg.Z&&tg.Z.locals?tg.Z.locals:void 0,th=e=>{var{space_id:o,plugin_id:t,unlockPlugin:i,tool_id:r,pluginInfo:a,updatedInfo:n,apiInfo:l,editVersion:c,canEdit:u,debugApiInfo:g,onDebugSuccessCallback:p}=e,m=(0,k.av)(),[h]=(0,x.V)(),v=(0,s.useMemo)(()=>(null==a?void 0:a.plugin_type)===M.zV.LOCAL||!(null==a?void 0:a.published)||(null==a?void 0:a.status)&&(null==n?void 0:n.created_api_names)&&!!n.created_api_names.includes((null==l?void 0:l.name)||""),[a,n,l]),{modalContent:f}=tu({debugApiInfo:g,canEdit:u,space_id:o||"",plugin_id:t||"",tool_id:r||"",unlockPlugin:i,editVersion:c,pluginInfo:a,onDebugSuccessCallback:p});return(0,d.jsx)("div",{className:tm.header,children:(0,d.jsxs)("div",{className:tm["simple-title"],children:[(0,d.jsx)(W.hU,{icon:(0,d.jsx)(ts,{style:{color:"rgba(29, 28, 35, 0.6)"}}),onClick:()=>{var e;null===(e=m.toResource)||void 0===e||e.call(m,"plugin",t),i()},size:"small",color:"secondary"}),(0,d.jsx)("span",{className:tm.title,children:b.o.t("plugin_edit_tool_title")}),(0,d.jsx)(eN,{}),h["bot.devops.plugin_mockset"]?(0,d.jsx)(W.u,{style:{display:v?"block":"none"},content:b.o.t("unreleased_plugins_tool_cannot_create_mockset"),position:"left",trigger:"hover",children:(0,d.jsx)(W.zx,{onClick:()=>{var e;null===(e=m.mocksetList)||void 0===e||e.call(m,r)},disabled:v,color:"primary",style:{marginRight:8},children:b.o.t("manage_mockset")})}):null,u?f:null]})})},tb=e=>{var o,{toolID:t,onDebugSuccessCallback:i,renderDescComponent:r,renderParamsComponent:c}=e,g=(0,V.UQ)(),[m,h]=(0,s.useState)(),[b,v]=(0,s.useState)(),[f,x]=(0,s.useState)(),[_,I]=(0,s.useState)(!0),{canEdit:y,init:N,pluginInfo:w,updatedInfo:D,wrapWithCheckLock:z,unlockPlugin:T,spaceID:E,pluginID:A,updatePluginInfoByImmer:S,version:C}=(0,k.RQ)((0,u.N)(e=>({canEdit:e.canEdit,init:e.init,pluginInfo:e.pluginInfo,updatedInfo:e.updatedInfo,wrapWithCheckLock:e.wrapWithCheckLock,unlockPlugin:e.unlockPlugin,spaceID:e.spaceID,pluginID:e.pluginId,updatePluginInfoByImmer:e.updatePluginInfoByImmer,version:e.version}))),L=e=>{S(o=>{if(!!o)o.edit_version=null==e?void 0:e.edit_version})},O=e=>{var o,t,i,r=(0,H.Z)(e.request_params);return(null==e?void 0:e.debug_example_status)===M.ji.Enable&&(null==e?void 0:null===(o=e.debug_example)||void 0===o?void 0:o.req_example)&&(0,eh.C$)(r,JSON.parse(null!==(i=null==e?void 0:null===(t=e.debug_example)||void 0===t?void 0:t.req_example)&&void 0!==i?i:"{}")),(0,en.eO)(r),r};var Z=(o=(0,a._)(function*(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];v((0,l._)((0,n._)({},b),{debug_example_status:M.ji.Disable})),e&&I(!0);try{var{api_info:o=[],msg:i,edit_version:r}=yield j.Js.GetPluginAPIs({plugin_id:A,api_ids:[t],preview_version_ts:C});if(o.length>0){var a=o.length>0?o[0]:{};x((0,l._)((0,n._)({},a),{request_params:O(a)})),(0,en.eO)(a.request_params),(0,en.eO)(a.response_params),v(a),h(r)}else g(new ea.sH(ei.b.responseValidation,i||"GetPluginAPIs error"))}catch(e){g(new ea.sH(ei.b.PluginInitError,"plugin init error: ".concat(e.message)))}e&&I(!1)}),function(){return o.apply(this,arguments)}),{isBaseInfoDisabled:F,header:Y,itemKey:B,extra:Q,content:U,classNameWrap:P}=ti({space_id:E,plugin_id:A,tool_id:t,apiInfo:b,canEdit:y,handleInit:Z,wrapWithCheckLock:z,editVersion:m,renderDescComponent:r}),{isBaseMoreDisabled:G,header:R,itemKey:q,extra:K,content:J,classNameWrap:X}=tc({plugin_id:A,pluginInfo:w,tool_id:t,apiInfo:b,canEdit:y,handleInit:Z,wrapWithCheckLock:z,editVersion:m,space_id:E,onSuccess:L}),{isRequestParamsDisabled:$,itemKey:ee,header:eo,extra:et,content:er,classNameWrap:el}=tt({apiInfo:b,plugin_id:A,tool_id:t,pluginInfo:w,canEdit:y,handleInit:Z,wrapWithCheckLock:z,editVersion:m,spaceID:E,onSuccess:L,renderParamsComponent:c}),{isResponseParamsDisabled:ed,itemKey:ec,header:es,extra:eu,content:eg,classNameWrap:ep}=te({apiInfo:b,plugin_id:A,tool_id:t,editVersion:m,pluginInfo:w,canEdit:y,handleInit:Z,wrapWithCheckLock:z,debugApiInfo:f,setDebugApiInfo:x,spaceID:E,onSuccess:L,renderParamsComponent:c}),em=[{header:Y,itemKey:B,extra:Q,content:U,classNameWrap:P},{header:R,itemKey:q,extra:K,content:J,classNameWrap:X},{header:eo,itemKey:ee,extra:et,content:er,classNameWrap:el},{header:es,itemKey:ec,extra:eu,content:eg,classNameWrap:ep}];return(0,s.useEffect)(()=>((0,a._)(function*(){yield N(),Z(!0)})(),()=>{T()}),[]),(0,p.Z)(()=>{if(!!F&&!!$&&!!ed&&!!G)T()},[F,$,ed,G]),_?(0,d.jsx)(W.yC,{size:"large",spinning:!0,style:{height:"100%",width:"100%"}}):(0,d.jsxs)("div",{className:tm.toolWrap,children:[(0,d.jsx)(th,{space_id:E,plugin_id:A,unlockPlugin:T,tool_id:t,pluginInfo:w,updatedInfo:D,apiInfo:b,editVersion:m||0,canEdit:y,debugApiInfo:f,onDebugSuccessCallback:i}),(0,d.jsx)(W.UO,{keepDOM:!0,defaultActiveKey:em.map(e=>e.itemKey),children:em.map((e,o)=>(0,d.jsx)(W.UO.Panel,{className:e.classNameWrap,header:e.header,itemKey:e.itemKey,extra:e.extra,children:e.content},"".concat(o,"collapse")))})]})}},746278:function(e,o,t){t.d(o,{FQ:function(){return a},aj:function(){return r}});var i=t(336205),r={id:"0",name:i.o.t("real_data")},a="10000010"},11869:function(e,o,t){t.d(o,{G:function(){return u},dj:function(){return d.dj},nr:function(){return c},sq:function(){return s}});var i=t(473980),r=t(615655),a=t(298203),n=t(824833),l=t(746278),d=t(450599);function c(e){return e.id===l.aj.id}function s(e,o){var{bindSubjectInfo:t,bizCtx:l}=e,{bindSubjectInfo:d,bizCtx:c}=o,s=(0,r.Z)(t,d),u=l||{},{ext:g}=u,p=(0,i._)(u,["ext"]),m=c||{},{ext:h}=m,b=function(e,o){return e.bizSpaceID===o.bizSpaceID&&e.trafficScene===o.trafficScene&&e.trafficCallerID===o.trafficCallerID}(p,(0,i._)(m,["ext"])),v=(null==l?void 0:l.trafficScene)!==n.zE.CozeWorkflowDebug||function(e,o){var t=(0,a.dj)(e||"{}"),i=(0,a.dj)(o||"{}");return(0,r.Z)(t,i)}((null==g?void 0:g.mockSubjectInfo)||"",(null==h?void 0:h.mockSubjectInfo)||"");return s&&b&&v}function u(e){switch(e){case n.zE.CozeSingleAgentDebug:return"bot";case n.zE.CozeMultiAgentDebug:return"agent";case n.zE.CozeWorkflowDebug:return"flow";case n.zE.CozeToolDebug:default:return"bot"}}},773568:function(e,o,t){t.d(o,{M:function(){return n},U:function(){return a}});var i,r,a=((i={}).STRING="string",i.INTEGER="integer",i.NUMBER="number",i.OBJECT="object",i.ARRAY="array",i.BOOLEAN="boolean",i);var n=((r={}).DEFAULT="default",r.REMOVED="removed",r.ADDED="added",r)},450599:function(e,o,t){t.d(o,{Lt:function(){return s},dj:function(){return c},jy:function(){return d}});var i=t(808549),r=t(120454),a=t(583193),n=t(177625),l=t(773568);function d(e){return e?"".concat(e.slice(0,1).toUpperCase()).concat(e.slice(1)):""}function c(e,o){try{return JSON.parse(e)}catch(e){return null==o?void 0:o()}}function s(e,o){var t=void 0===o;if(!e||t)return{result:e,incompatible:!1};var d="string"==typeof o&&c(o)||o,s=function e(o,t,i){var r,n,{defaultStatus:d=l.M.DEFAULT}=i||{},c=u(t);if(!!c){var[s,g]=(r=c,n=t,(0,a.Ki)(r,{getBooleanValue:()=>!!n,getNumberValue:()=>Number(n),getStringValue:()=>String(n)})),p=(null==i?void 0:i.keyPrefix)?"".concat(null==i?void 0:i.keyPrefix,"-").concat(o):o,m={label:o,realValue:s,displayValue:g,isRequired:!1,type:c,status:d,key:p};if(c===l.U.OBJECT){var h=[];for(var b of Object.keys(t))if(b){var v=e(b,t[b],{defaultStatus:d,keyPrefix:p});v&&h.push(v)}m.children=h}if(c===l.U.ARRAY){m.childrenType=u(t[0]);var f=[];for(var x in t)if(void 0!==t[x]){var M=e((0,a.pB)(x),t[x],{defaultStatus:d,keyPrefix:p});M&&f.push(M)}m.children=f}return m}}(n.dz,d,{defaultStatus:l.M.REMOVED}),{merged:g,incompatible:p}=m(e.children,null==s?void 0:s.children,e.type===l.U.ARRAY);return{result:(0,r._)((0,i._)({},e),{children:g}),incompatible:p}}function u(e){var o=typeof e;return e instanceof Array&&(o=l.U.ARRAY),o}function g(e,o){return e===l.U.NUMBER?o===l.U.NUMBER||o===l.U.INTEGER:e===o}function p(e,o){var t=!1,a=(0,r._)((0,i._)({},e),{key:o.key,label:o.label,realValue:o.realValue,displayValue:o.displayValue,status:l.M.DEFAULT});if(o.type===l.U.ARRAY||o.type===l.U.OBJECT){var{merged:n,incompatible:d}=m(e.children,o.children,e.type===l.U.ARRAY);a.children=n,t=t||d}return{result:a,incompatible:t}}function m(e,o){var t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return void 0===e||void 0===o?{merged:[...e||[],...o||[]],incompatible:e!==o}:0===o.length&&t?{merged:[],incompatible:!1}:function(e,o){var t=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=!1,r=[...o],a=[...e];for(var n in a)!function(e){if(a[e]){var o=a[e],t=r.findIndex(e=>e.label===o.label&&g(e.type,o.type)&&g(e.childrenType,o.childrenType));if(-1!==t){var{result:n,incompatible:l}=p(o,r.splice(t,1)[0]);a[e]=n,i=i||l}else o.isRequired&&(i=!0)}}(n);if(r.length&&t){var l=e[0];r.forEach(e=>{var{result:o,incompatible:t}=p(l,e);a.push(o),i=i||t}),r=[]}return r.length&&(i=!0),{merged:[...a,...r],incompatible:i}}(e,o,t)}},851645:function(e,o,t){t.d(o,{y:function(){return h}});var i=t(825955),r=t(151064),a=t(455069),n=t(947578),l=t(336205),d=t(775699),c=t(458949),s=t(382534),u=t(577413),g=t(900021),p=t(252793),m=t(541509),h=e=>{var o,{requestParams:t=[],pluginId:h,apiId:b,operation:v=1,btnText:f=l.o.t("Create_newtool_s4_run"),callback:x,disabled:M,debugExampleStatus:j=c.ji.Default,showExampleTag:_=!1,pluginType:k}=e,[I,y]=(0,a.useState)(!1),[N,w]=(0,a.useState)(0),D=(0,a.useRef)(null);var z=(o=(0,i._)(function*(){w(N+1),yield(0,u._v)(100);var e,o,t,i=document.getElementsByClassName("errorDebugClassTag");if(!b||i.length>0)return(0,u.$S)(".errorDebugClassTag"),d.FN.error({content:(0,n.u)(l.o.t("tool_new_S2_feedback_failed")),duration:3,theme:"light",showClose:!1}),!1;var r={};y(!0),Array.isArray(null===(e=D.current)||void 0===e?void 0:e.data)&&((null===(o=D.current)||void 0===o?void 0:o.data)||[]).length>0&&(r=(0,u.SR)(null===(t=D.current)||void 0===t?void 0:t.data));try{var a=yield s.Js.DebugAPI({plugin_id:h,api_id:b,parameters:JSON.stringify(r),operation:v});null==x||x({status:a.success?g.Q.PASS:g.Q.FAIL,request:a.raw_req,response:a.resp,failReason:a.reason,response_params:a.response_params,rawResp:a.raw_resp})}catch(e){null==x||x({status:g.Q.FAIL,request:JSON.stringify(r,null,2),response:l.o.t("plugin_exception"),failReason:l.o.t("plugin_exception")})}y(!1)}),function(){return o.apply(this,arguments)}),T=(0,a.useMemo)(()=>(0,u.qE)(t),[t]);return(0,r.jsxs)("div",{className:p.Z["debug-params-box"],children:[(0,r.jsx)(m.Z,{height:443,ref:D,requestParams:T,defaultKey:"global_default",disabled:M,check:N,debugExampleStatus:j,showExampleTag:_,supportFileTypeUpload:!0}),!M&&(0,r.jsx)("div",{className:p.Z.runbtn,children:(0,r.jsxs)(d.y3,{disabled:M||k===c.zV.LOCAL,style:{width:98},loading:I,type:"tertiary",onClick:z,children:[f===l.o.t("Create_newtool_s3_button_auto")&&(I?l.o.t("plugin_s3_Parsing"):l.o.t("Create_newtool_s3_button_auto")),f===l.o.t("Create_newtool_s4_run")&&(I?l.o.t("plugin_s3_running"):l.o.t("Create_newtool_s4_run"))]})})]})}},541509:function(e,o,t){t.d(o,{Z:()=>ec});var i=t("808549"),r=t("120454"),a=t("151064"),n=t("455069"),l=t("335740"),d=t("962289"),c=t("671261"),s=t("336205"),u=t("659596"),g=t("775699"),p=t("44172"),m=t("458949"),h=t("805366");let b=(0,h.A)(function(e){return n.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},e),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 5V4h4v1h-4ZM8 5V3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v2h4a1 1 0 1 1 0 2h-1v14a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7H4a1 1 0 0 1 0-2h4Zm7 2H7v13h10V7h-2ZM9 9.5c0-.28.22-.5.5-.5h1c.28 0 .5.22.5.5v7a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-7Zm4 0c0-.28.22-.5.5-.5h1c.28 0 .5.22.5.5v7a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-7Z",fill:"currentColor"}))},"delete_stroked");var v=t("71569"),f=t("577413"),x=t("827666"),M=e=>e.global_disable?"disable":"normal",j=t("252237"),_=t.n(j),k=t("407821"),I=t.n(k),y=t("472772"),N=t.n(y),w=t("395245"),D=t.n(w),z=t("297998"),T=t.n(z),E=t("646576"),A=t.n(E),S=t("606121"),C=t.n(S),L=t("216599"),O={};O.styleTagTransform=C(),O.setAttributes=T(),O.insert=D().bind(null,"head"),O.domAPI=N(),O.insertStyleElement=A(),I()(L.Z,O);var Z=L.Z&&L.Z.locals?L.Z.locals:void 0,F=e=>{var{withDescription:o=!1,tip:t=s.o.t("plugin_empty")}=e;return(0,a.jsx)("div",{className:Z["check-box"],children:(0,a.jsx)("span",{className:_()("whitespace-nowrap",Z["form-check-tip"],o?"!top-[16px]":"!top-0","errorDebugClassTag"),children:t})})},Y=t("187151"),B=t("372707"),Q={label:s.o.t("shortcut_modal_upload_component_file_format_table"),icon:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjYiIGQ9Ik0xIDQuOTk5OTZDMSAzLjE1OTAxIDIuNDkyMzggMS42NjY2MyA0LjMzMzMzIDEuNjY2NjNIMjAuMzA5NkMyMC43NTE3IDEuNjY2NjMgMjEuMTc1NiAxLjg0MjIyIDIxLjQ4ODIgMi4xNTQ3OEwzMC41MTE4IDExLjE3ODVDMzAuODI0NCAxMS40OTEgMzEgMTEuOTE1IDMxIDEyLjM1N1YzNUMzMSAzNi44NDA5IDI5LjUwNzYgMzguMzMzMyAyNy42NjY3IDM4LjMzMzNINC4zMzMzM0MyLjQ5MjM4IDM4LjMzMzMgMSAzNi44NDA5IDEgMzVWNC45OTk5NloiIGZpbGw9IiMzMkE2NDUiLz4KPHBhdGggaWQ9IlJlY3RhbmdsZSAyNTI3IiBvcGFjaXR5PSIwLjkiIGQ9Ik0yMSAyLjI3MDE4QzIxIDIuMDQ3NDUgMjEuMjY5MyAxLjkzNTkxIDIxLjQyNjggMi4wOTM0TDMwLjU3MzIgMTEuMjM5OEMzMC43MzA3IDExLjM5NzMgMzAuNjE5MiAxMS42NjY2IDMwLjM5NjQgMTEuNjY2NkgyNC4zMzMzQzIyLjQ5MjQgMTEuNjY2NiAyMSAxMC4xNzQyIDIxIDguMzMzMjlWMi4yNzAxOFoiIGZpbGw9IiMyNTg4MzIiLz4KPHBhdGggaWQ9Imljb25fZmlsZV9leGNlbF9ub3IiIGQ9Ik0xMC4yNDQzIDE2LjUxNTFIMTIuMDY0QzEyLjE0NDcgMTYuNTE1MSAxMi4yMjAyIDE2LjU1NDggMTIuMjY2IDE2LjYyMTJMMTUuNzYzNyAyMS42ODk4TDE5LjI3OTggMTYuNjIwN0MxOS4zMjU2IDE2LjU1NDYgMTkuNDAxIDE2LjUxNTEgMTkuNDgxNSAxNi41MTUxSDIxLjMwMDlDMjEuNDM2NSAxNi41MTUxIDIxLjU0NjQgMTYuNjI1IDIxLjU0NjQgMTYuNzYwNkMyMS41NDY0IDE2LjgxMTcgMjEuNTMwNCAxNi44NjE1IDIxLjUwMDggMTYuOTAzMUwxNi45NDU4IDIzLjI5MjVMMjEuODYzMiAzMC4yMTg1QzIxLjk0MTYgMzAuMzI5IDIxLjkxNTYgMzAuNDgyMyAyMS44MDUxIDMwLjU2MDdDMjEuNzYzNiAzMC41OTAyIDIxLjcxMzkgMzAuNjA2IDIxLjY2MyAzMC42MDZIMTkuODQzNEMxOS43NjI4IDMwLjYwNiAxOS42ODc0IDMwLjU2NjUgMTkuNjQxNiAzMC41MDAyTDE1Ljc2MzYgMjQuODk1NEwxMS45MDQyIDMwLjQ5OThDMTEuODU4NCAzMC41NjYzIDExLjc4MjggMzAuNjA2IDExLjcwMjEgMzAuNjA2SDkuODgyMTdDOS43NDY2MSAzMC42MDYgOS42MzY3MiAzMC40OTYyIDkuNjM2NzIgMzAuMzYwNkM5LjYzNjcyIDMwLjMxIDkuNjUyMzcgMzAuMjYwNiA5LjY4MTUzIDMwLjIxOTJMMTQuNTYyMyAyMy4yOTI1TDEwLjA0MzggMTYuOTAyM0M5Ljk2NTU4IDE2Ljc5MTYgOS45OTE4NiAxNi42Mzg0IDEwLjEwMjUgMTYuNTYwMkMxMC4xNDQgMTYuNTMwOSAxMC4xOTM1IDE2LjUxNTEgMTAuMjQ0MyAxNi41MTUxWiIgZmlsbD0id2hpdGUiLz4KPC9nPgo8L3N2Zz4K"},U={label:s.o.t("shortcut_modal_upload_component_file_format_doc"),icon:"data:image/svg+xml;base64,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"},P={[B.W.IMAGE]:{label:s.o.t("shortcut_modal_upload_component_file_format_img"),icon:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjgiIGQ9Ik0xIDQuOTk5OTZDMSAzLjE1OTAxIDIuNDkyMzggMS42NjY2MyA0LjMzMzMzIDEuNjY2NjNIMjAuMzA5NkMyMC43NTE3IDEuNjY2NjMgMjEuMTc1NiAxLjg0MjIyIDIxLjQ4ODIgMi4xNTQ3OEwzMC41MTE4IDExLjE3ODVDMzAuODI0NCAxMS40OTEgMzEgMTEuOTE1IDMxIDEyLjM1N1YzNUMzMSAzNi44NDA5IDI5LjUwNzYgMzguMzMzMyAyNy42NjY3IDM4LjMzMzNINC4zMzMzM0MyLjQ5MjM4IDM4LjMzMzMgMSAzNi44NDA5IDEgMzVWNC45OTk5NloiIGZpbGw9IiNGRkM2MEEiLz4KPHBhdGggaWQ9IlJlY3RhbmdsZSAyNTI5IiBvcGFjaXR5PSIwLjgiIGQ9Ik0yMSAyLjI3MDE4QzIxIDIuMDQ3NDUgMjEuMjY5MyAxLjkzNTkxIDIxLjQyNjggMi4wOTM0TDMwLjU3MzIgMTEuMjM5OEMzMC43MzA3IDExLjM5NzMgMzAuNjE5MiAxMS42NjY2IDMwLjM5NjQgMTEuNjY2NkgyNC4zMzMzQzIyLjQ5MjQgMTEuNjY2NiAyMSAxMC4xNzQyIDIxIDguMzMzMjlWMi4yNzAxOFoiIGZpbGw9IiNEOTk5MDQiLz4KPGcgaWQ9IiYjMjI5OyYjMTg5OyYjMTYyOyYjMjMxOyYjMTM4OyYjMTgyOyYjMjMxOyYjMTg3OyYjMTQ3OyYjMjI5OyYjMTQ0OyYjMTM2OyI+CjxwYXRoIGQ9Ik05Ljk1MzEyIDE2LjY2NjZDOS4wMzI2NSAxNi42NjY2IDguMjg2NDYgMTcuNDEyOCA4LjI4NjQ2IDE4LjMzMzNWMTguNjM2M0M4LjI4NjQ2IDE5LjU1NjggOS4wMzI2NSAyMC4zMDMgOS45NTMxMiAyMC4zMDNIMTAuMjU2MkMxMS4xNzY2IDIwLjMwMyAxMS45MjI4IDE5LjU1NjggMTEuOTIyOCAxOC42MzYzVjE4LjMzMzNDMTEuOTIyOCAxNy40MTI4IDExLjE3NjYgMTYuNjY2NiAxMC4yNTYyIDE2LjY2NjZIOS45NTMxMloiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yMy44MjU0IDIxLjI2NjNDMjQuNDQzNCAyMC41OTg5IDI1LjU1OTIgMjEuMDM2MiAyNS41NTkyIDIxLjk0NTdWMzAuODMzM0MyNS41NTkyIDMxLjI5MzUgMjUuMTg2MSAzMS42NjY2IDI0LjcyNTkgMzEuNjY2Nkw4LjMzNDk0IDMxLjY2NjZDNy43Njg3OSAzMS42NjY2IDcuNDYwMzMgMzEuMDA1NSA3LjgyNDA1IDMwLjU3MTZMMTIuNyAyNC43NTU0QzEzLjM2NjEgMjMuOTYwOSAxNC41ODgzIDIzLjk2MDkgMTUuMjU0NCAyNC43NTU0TDE3Ljc5MiAyNy43ODI0TDIzLjgyNTQgMjEuMjY2M1oiIGZpbGw9IndoaXRlIi8+CjwvZz4KPC9nPgo8L3N2Zz4K"},[B.W.EXCEL]:Q,[B.W.CSV]:Q,[B.W.PDF]:U,[B.W.DOCX]:U,[B.W.DEFAULT_UNKNOWN]:{label:s.o.t("plugin_file_unknown"),icon:"data:image/svg+xml;base64,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"},[B.W.AUDIO]:{label:s.o.t("shortcut_modal_upload_component_file_format_audio"),icon:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjYiIGQ9Ik0xIDQuOTk5OTZDMSAzLjE1OTAxIDIuNDkyMzggMS42NjY2MyA0LjMzMzMzIDEuNjY2NjNIMjAuMzA5NkMyMC43NTE3IDEuNjY2NjMgMjEuMTc1NiAxLjg0MjIyIDIxLjQ4ODIgMi4xNTQ3OEwzMC41MTE4IDExLjE3ODVDMzAuODI0NCAxMS40OTEgMzEgMTEuOTE1IDMxIDEyLjM1N1YzNUMzMSAzNi44NDA5IDI5LjUwNzYgMzguMzMzMyAyNy42NjY3IDM4LjMzMzNINC4zMzMzM0MyLjQ5MjM4IDM4LjMzMzMgMSAzNi44NDA5IDEgMzVWNC45OTk5NloiIGZpbGw9IiMzMkE2NDUiLz4KPHBhdGggaWQ9IlJlY3RhbmdsZSAyNTI3IiBvcGFjaXR5PSIwLjkiIGQ9Ik0yMSAyLjI3MDE4QzIxIDIuMDQ3NDUgMjEuMjY5MyAxLjkzNTkxIDIxLjQyNjggMi4wOTM0TDMwLjU3MzIgMTEuMjM5OEMzMC43MzA3IDExLjM5NzMgMzAuNjE5MiAxMS42NjY2IDMwLjM5NjQgMTEuNjY2NkgyNC4zMzMzQzIyLjQ5MjQgMTEuNjY2NiAyMSAxMC4xNzQyIDIxIDguMzMzMjlWMi4yNzAxOFoiIGZpbGw9IiMyNTg4MzIiLz4KPHBhdGggaWQ9Im11c2ljX25vdGUiIGQ9Ik0xMy4wODMzIDMxLjI1QzEyLjE2NjcgMzEuMjUgMTEuMzgxOSAzMC45MjM2IDEwLjcyOTIgMzAuMjcwOEMxMC4wNzY0IDI5LjYxODEgOS43NSAyOC44MzMzIDkuNzUgMjcuOTE2N0M5Ljc1IDI3IDEwLjA3NjQgMjYuMjE1MyAxMC43MjkyIDI1LjU2MjVDMTEuMzgxOSAyNC45MDk3IDEyLjE2NjcgMjQuNTgzMyAxMy4wODMzIDI0LjU4MzNDMTMuNDAyOCAyNC41ODMzIDEzLjY5NzkgMjQuNjIxNSAxMy45Njg4IDI0LjY5NzlDMTQuMjM5NiAyNC43NzQzIDE0LjUgMjQuODg4OSAxNC43NSAyNS4wNDE3VjE3LjVDMTQuNzUgMTYuODA5NiAxNS4zMDk2IDE2LjI1IDE2IDE2LjI1SDIxVjE4Ljc1SDE3LjA0MTdDMTYuNjk2NSAxOC43NSAxNi40MTY3IDE5LjAyOTggMTYuNDE2NyAxOS4zNzVWMjcuOTE2N0MxNi40MTY3IDI4LjgzMzMgMTYuMDkwMyAyOS42MTgxIDE1LjQzNzUgMzAuMjcwOEMxNC43ODQ3IDMwLjkyMzYgMTQgMzEuMjUgMTMuMDgzMyAzMS4yNVoiIGZpbGw9IndoaXRlIi8+CjwvZz4KPC9zdmc+Cg=="},[B.W.CODE]:{label:s.o.t("shortcut_modal_upload_component_file_format_code"),icon:"data:image/svg+xml;base64,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"},[B.W.ARCHIVE]:{label:s.o.t("shortcut_modal_upload_component_file_format_zip"),icon:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjgiIGQ9Ik0xIDQuOTk5OTdDMSAzLjE1OTAyIDIuNDkyMzggMS42NjY2NCA0LjMzMzMzIDEuNjY2NjRIMjAuMzA5NkMyMC43NTE3IDEuNjY2NjQgMjEuMTc1NiAxLjg0MjI0IDIxLjQ4ODIgMi4xNTQ4TDMwLjUxMTggMTEuMTc4NUMzMC44MjQ0IDExLjQ5MSAzMSAxMS45MTUgMzEgMTIuMzU3VjM1QzMxIDM2Ljg0MDkgMjkuNTA3NiAzOC4zMzMzIDI3LjY2NjcgMzguMzMzM0g0LjMzMzMzQzIuNDkyMzggMzguMzMzMyAxIDM2Ljg0MDkgMSAzNVY0Ljk5OTk3WiIgZmlsbD0iIzMzNkRGNCIvPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjkiIG9wYWNpdHk9IjAuNyIgZD0iTTIxIDIuMjcwMTlDMjEgMi4wNDc0NyAyMS4yNjkzIDEuOTM1OTMgMjEuNDI2OCAyLjA5MzQyTDMwLjU3MzIgMTEuMjM5OUMzMC43MzA3IDExLjM5NzQgMzAuNjE5MiAxMS42NjY2IDMwLjM5NjQgMTEuNjY2NkgyNC4zMzMzQzIyLjQ5MjQgMTEuNjY2NiAyMSAxMC4xNzQzIDIxIDguMzMzMzFWMi4yNzAxOVoiIGZpbGw9IiMwNDQyRDIiLz4KPHBhdGggaWQ9IiYjMjI5OyYjMTQ0OyYjMTM2OyYjMjI5OyYjMTg1OyYjMTgyOyYjMjI5OyYjMTg5OyYjMTYyOyYjMjMxOyYjMTM4OyYjMTgyOyIgZD0iTTIxLjcxNCAxNS44MzM0QzIyLjIzOTkgMTUuODMzNCAyMi42NjYzIDE2LjI1MzEgMjIuNjY2MyAxNi43NzA5VjI5Ljg5NTlDMjIuNjY2MyAzMC40MTM2IDIyLjIzOTkgMzAuODMzNCAyMS43MTQgMzAuODMzNEgxMC4yODU0QzkuNzU5NCAzMC44MzM0IDkuMzMzMDEgMzAuNDEzNiA5LjMzMzAxIDI5Ljg5NTlWMTYuNzcwOUM5LjMzMzAxIDE2LjI1MzEgOS43NTk0IDE1LjgzMzQgMTAuMjg1NCAxNS44MzM0SDIxLjcxNFpNMTcuOTA0NCAyNi4xNDU5SDE0LjA5NDlWMjkuNDI3MUgxNy45MDQ0VjI2LjE0NTlaTTE2Ljk1MjEgMjcuMDgzNFYyOC4xNjgySDE1LjA0NzNWMjcuMDgzNEgxNi45NTIxWk0xNy45NDg0IDE5LjU4MzRIMTUuOTk5N1YyMS40NTg0SDE0LjA5NDlWMjMuMzMzNEgxNS45OTk3VjI1LjIwODRIMTcuOTQ4NFYyMy4zMzM0SDE2LjA0MzZWMjEuNDU4NEgxNy45NDg0VjE5LjU4MzRaTTE1Ljk5OTcgMTcuNzA4NEgxNC4wOTQ5VjE5LjU4MzRIMTUuOTk5N1YxNy43MDg0WiIgZmlsbD0id2hpdGUiLz4KPC9nPgo8L3N2Zz4K"},[B.W.PPT]:{label:s.o.t("shortcut_modal_upload_component_file_format_ppt"),icon:"data:image/svg+xml;base64,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"},[B.W.VIDEO]:{label:s.o.t("shortcut_modal_upload_component_file_format_video"),icon:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjgiIGQ9Ik0xIDUuMDAwMDhDMSAzLjE1OTEzIDIuNDkyMzggMS42NjY3NSA0LjMzMzMzIDEuNjY2NzVIMjAuMzA5NkMyMC43NTE3IDEuNjY2NzUgMjEuMTc1NiAxLjg0MjM0IDIxLjQ4ODIgMi4xNTQ5TDMwLjUxMTggMTEuMTc4NkMzMC44MjQ0IDExLjQ5MTIgMzEgMTEuOTE1MSAzMSAxMi4zNTcxVjM1LjAwMDFDMzEgMzYuODQxIDI5LjUwNzYgMzguMzMzNCAyNy42NjY3IDM4LjMzMzRINC4zMzMzM0MyLjQ5MjM4IDM4LjMzMzQgMSAzNi44NDEgMSAzNS4wMDAxVjUuMDAwMDhaIiBmaWxsPSIjMzM2REY0Ii8+CjxwYXRoIGlkPSJSZWN0YW5nbGUgMjUyOSIgb3BhY2l0eT0iMC43IiBkPSJNMjEgMi4yNzAzQzIxIDIuMDQ3NTcgMjEuMjY5MyAxLjkzNjAzIDIxLjQyNjggMi4wOTM1MkwzMC41NzMyIDExLjI0QzMwLjczMDcgMTEuMzk3NSAzMC42MTkyIDExLjY2NjcgMzAuMzk2NCAxMS42NjY3SDI0LjMzMzNDMjIuNDkyNCAxMS42NjY3IDIxIDEwLjE3NDQgMjEgOC4zMzM0MVYyLjI3MDNaIiBmaWxsPSIjMDQ0MkQyIi8+CjxwYXRoIGlkPSImIzIyOTsmIzE4OTsmIzE2MjsmIzIzMTsmIzEzODsmIzE4MjsiIGQ9Ik03LjY2NDA2IDE3LjkxNjVDNy42NjQwNiAxNy4yMjYxIDguMjIzNzEgMTYuNjY2NSA4LjkxNDA2IDE2LjY2NjVIMTcuNjY0MUMxOC4zNTQ0IDE2LjY2NjUgMTguOTE0MSAxNy4yMjYxIDE4LjkxNDEgMTcuOTE2NVYxOS40MDA5TDIwLjgwMTIgMTguMzM5M0MyMS42MzQ1IDE3Ljg3MDYgMjIuNjY0MSAxOC40NzI4IDIyLjY2NDEgMTkuNDI4OFYyNS4xNTQyQzIyLjY2NDEgMjYuMTEwMiAyMS42MzQ1IDI2LjcxMjQgMjAuODAxMiAyNi4yNDM3TDE4LjkxNDEgMjUuMTgyMVYyNi42NjY1QzE4LjkxNDEgMjcuMzU2OSAxOC4zNTQ0IDI3LjkxNjUgMTcuNjY0MSAyNy45MTY1SDguOTE0MDZDOC4yMjM3MSAyNy45MTY1IDcuNjY0MDYgMjcuMzU2OSA3LjY2NDA2IDI2LjY2NjVWMTcuOTE2NVpNMTEuNDE0MSAyMS42NjY1QzEyLjEwNDQgMjEuNjY2NSAxMi42NjQxIDIxLjEwNjkgMTIuNjY0MSAyMC40MTY1QzEyLjY2NDEgMTkuNzI2MSAxMi4xMDQ0IDE5LjE2NjUgMTEuNDE0MSAxOS4xNjY1QzEwLjcyMzcgMTkuMTY2NSAxMC4xNjQxIDE5LjcyNjEgMTAuMTY0MSAyMC40MTY1QzEwLjE2NDEgMjEuMTA2OSAxMC43MjM3IDIxLjY2NjUgMTEuNDE0MSAyMS42NjY1WiIgZmlsbD0id2hpdGUiLz4KPC9nPgo8L3N2Zz4K"},[B.W.TXT]:{label:s.o.t("shortcut_modal_upload_component_file_format_txt"),icon:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjYiIGQ9Ik0xIDUuMDAwMDhDMSAzLjE1OTEzIDIuNDkyMzggMS42NjY3NSA0LjMzMzMzIDEuNjY2NzVIMjAuMzA5NkMyMC43NTE3IDEuNjY2NzUgMjEuMTc1NiAxLjg0MjM0IDIxLjQ4ODIgMi4xNTQ5TDMwLjUxMTggMTEuMTc4NkMzMC44MjQ0IDExLjQ5MTIgMzEgMTEuOTE1MSAzMSAxMi4zNTcxVjM1LjAwMDFDMzEgMzYuODQxIDI5LjUwNzYgMzguMzMzNCAyNy42NjY3IDM4LjMzMzRINC4zMzMzM0MyLjQ5MjM4IDM4LjMzMzQgMSAzNi44NDEgMSAzNS4wMDAxVjUuMDAwMDhaIiBmaWxsPSIjMzM2REY0Ii8+CjxwYXRoIGlkPSJSZWN0YW5nbGUgMjUyNyIgb3BhY2l0eT0iMC43IiBkPSJNMjEgMi4yNzAzQzIxIDIuMDQ3NTcgMjEuMjY5MyAxLjkzNjAzIDIxLjQyNjggMi4wOTM1MkwzMC41NzMyIDExLjI0QzMwLjczMDcgMTEuMzk3NSAzMC42MTkyIDExLjY2NjcgMzAuMzk2NCAxMS42NjY3SDI0LjMzMzNDMjIuNDkyNCAxMS42NjY3IDIxIDEwLjE3NDQgMjEgOC4zMzM0MVYyLjI3MDNaIiBmaWxsPSIjMDQ0MkQyIi8+CjxwYXRoIGlkPSJVbmlvbiIgZD0iTTE2LjkwNyAxNy44Nzg3VjMwLjM3ODdDMTYuOTA3IDMwLjUwNDIgMTYuODA1MiAzMC42MDYgMTYuNjc5NyAzMC42MDZIMTUuMzE2MUMxNS4xOTA1IDMwLjYwNiAxNS4wODg4IDMwLjUwNDIgMTUuMDg4OCAzMC4zNzg3VjE3Ljg3ODdIOS40MDY5NkM5LjI4MTQ0IDE3Ljg3ODcgOS4xNzk2OSAxNy43NzcgOS4xNzk2OSAxNy42NTE1VjE2LjI4NzhDOS4xNzk2OSAxNi4xNjIzIDkuMjgxNDQgMTYuMDYwNSA5LjQwNjk2IDE2LjA2MDVIMjIuNTg4OEMyMi43MTQzIDE2LjA2MDUgMjIuODE2MSAxNi4xNjIzIDIyLjgxNjEgMTYuMjg3OFYxNy42NTE1QzIyLjgxNjEgMTcuNzc3IDIyLjcxNDMgMTcuODc4NyAyMi41ODg4IDE3Ljg3ODdIMTYuOTA3WiIgZmlsbD0id2hpdGUiLz4KPC9nPgo8L3N2Zz4K"}},G=e=>{var{fileTypes:o}=x.w5[(0,x.Eo)(e)],t=null==o?void 0:o.reduce((e,o)=>{var t=Y.Co.find(e=>e.fileType===o);return t?e="".concat(e).concat(e?",":"").concat(t.accept.join(",")):e},"");if(!!t&&"*"!==t)return t},R=e=>{if(!e)return null;var o=(0,x.Eo)(e),t=Object.entries(x.w5).find(e=>{var[t]=e;return Number(t)===o});if(!t)return null;for(var i of t[1].fileTypes)if(P[i])return i;return null},V=t("825955"),W=t("871873"),H=t("61111"),q=t("287487"),K=t("298203"),J=e=>(0,W.Z)({uri:"",url:"",name:"",type:null,uploading:!1,abortSignal:new AbortController().signal},e),X=e=>{var o,{disabled:t=!1,uploadProps:l,render:d,onUploadSuccess:c,defaultUrl:g,defaultFileType:p}=e,m=q.Z.useUserInfo().user_id_str,[h,b]=(0,n.useReducer)((e,o)=>(0,H.Uy)(e,e=>{if(!!o)Object.keys(o).forEach(t=>{var i;e[t]=null!==(i=o[t])&&void 0!==i?i:e[t]})}),J({url:null!=g?g:"",type:null!=p?p:null}));var v=(o=(0,V._)(function*(e){var{file:o,fileInstance:t}=e,i=(0,Y.hr)(t).fileType;b({uploading:!0,url:o.url,name:o.name}),yield(0,K.ku)({userId:m,fileItemList:[{file:t,fileType:i===Y.Wz.IMAGE?"image":"object"}],signal:h.abortSignal,timeout:void 0,onSuccess:e=>{var o,t=null==e?void 0:null===(o=e.uploadResult)||void 0===o?void 0:o.Uri;if(!!t)b({uploading:!1,uri:t,type:i}),null==c||c(t)},onUploadError:()=>{b({uploading:!1})}})}),function(e){return o.apply(this,arguments)});return"function"!=typeof d?null:(0,a.jsx)(u.gq,(0,r._)((0,i._)({className:"w-full",draggable:!0,limit:1,disabled:t,onAcceptInvalid:()=>{u.FN.error(s.o.t("shortcut_Illegal_file_format"))},onSizeError:()=>{(null==l?void 0:l.maxSize)&&u.FN.error(s.o.t("file_too_large",{max_size:"".concat(l.maxSize/1024,"MB")}))},customRequest:v,showUploadList:!1},l),{children:d({fileState:h,clearFile:()=>b(J())})}))},$=t("391875"),ee={};ee.styleTagTransform=C(),ee.setAttributes=T(),ee.insert=D().bind(null,"head"),ee.domAPI=N(),ee.insertStyleElement=A(),I()($.Z,ee);var eo=$.Z&&$.Z.locals?$.Z.locals:void 0,{Text:et}=g.ZT,ei=P[Y.Wz.DEFAULT_UNKNOWN].icon,er=e=>{var{onChange:o,required:t=!1,withDescription:i=!1,check:r=0,defaultValue:l,disabled:d=!1,assistParameterType:c}=e,[u,h]=(0,n.useState)(!1),[b,v]=(0,n.useState)(l),f=R(c),x=c===m.K9.IMAGE,M=x?s.o.t("plugin_file_upload_image"):s.o.t("plugin_file_upload"),j=x?s.o.t("plugin_file_upload_mention_image"):s.o.t("plugin_file_upload_mention"),k=G(c);(0,n.useEffect)(()=>{if(0!==r)h(t&&!b)},[r]);var I=e=>{v(e),null==o||o(e),h(t&&!e)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(X,{defaultUrl:b,defaultFileType:f,onUploadSuccess:I,uploadProps:{accept:k,disabled:d,maxSize:20480},render:e=>{var{fileState:o,clearFile:t}=e,{uploading:i,uri:r,url:n,name:l,type:c}=o,u=!!n&&!r,m=n,h=(0,a.jsx)(g.y3,{icon:(0,a.jsx)(p.rRB,{className:eo.icon}),loading:i,disabled:d,className:"w-full",children:i?s.o.t("plugin_file_uploading"):M});if(i)return h;if(u&&c===Y.Wz.IMAGE)m=ei;else if(!x){var v,f=null===(v=P[c])||void 0===v?void 0:v.icon;m=f?f:void 0}return u||r?(0,a.jsxs)("div",{className:_()("flex items-center justify-between w-full h-[32px]",d?"cursor-not-allowed":""),children:[(0,a.jsxs)("div",{className:"flex items-center min-w-0",children:[m?(0,a.jsx)("img",{src:m,className:"w-[20px] h-[20px] mr-[5px] rounded-[0.5px]"}):null,(0,a.jsx)(et,{ellipsis:{showTooltip:!0},className:"mr-[2px]",children:u?b:l})]}),(0,a.jsx)(g._3,{icon:(0,a.jsx)(p.WDf,{}),disabled:d,onClick:e=>{e.stopPropagation(),t(),I("")}})]}):h}}),u?(0,a.jsx)(F,{withDescription:i,tip:j}):null]})},ea=e=>{var{val:o="",callback:t,check:r=0,width:l="100%",useCheck:d=!1,useBlockWrap:c=!1,disabled:s,desc:u}=e,[p,m]=(0,n.useState)(o),[h,b]=(0,n.useState)(!1);(0,n.useEffect)(()=>{if(0!==r&&p!==x.sp&&p!==x.UR)v(p)},[r]);var v=e=>{if(!!d)b(""===(e?String(e).replace(/\s+/g,""):""))};return(0,a.jsxs)("span",{style:(0,i._)({width:l},c?{display:"inline-block"}:{}),children:[(0,a.jsx)(g.u3,{disabled:s,value:p,validateStatus:h?"error":"default",onChange:e=>{m(e),t(e),v(e)}}),(0,a.jsx)("br",{}),h?(0,a.jsx)(F,{withDescription:!!u}):null]})},en=e=>{var{record:o,data:t,disabled:i=!1,check:r,needCheck:n,defaultKey:l,supportFileTypeUpload:d=!1}=e,c=!((null==o?void 0:o.type)===m.rH.Object||(null==o?void 0:o.type)===m.rH.Array||i&&void 0===o.value),s=(null==o?void 0:o.type)===m.rH.String&&!!(null==o?void 0:o.assist_type),u=(0,a.jsx)(a.Fragment,{});return d&&s?u=(0,a.jsx)(er,{defaultValue:o.value||(null==o?void 0:o[l]),assistParameterType:o.assist_type,onChange:e=>{(0,f.jd)({data:t,targetKey:o[x.Fc],field:"value",value:e||null})},withDescription:!!(null==o?void 0:o.desc),required:n||(null==o?void 0:o.is_required),check:r,disabled:i}):c&&(u=(0,a.jsx)("div",{className:M(o),children:(0,a.jsx)(ea,{disabled:i,useBlockWrap:!0,val:o.value||(null==o?void 0:o[l]),check:r,useCheck:n||(null==o?void 0:o.is_required),callback:e=>{(0,f.jd)({data:t,targetKey:o[x.Fc],field:"value",value:e}),(0,f.jd)({data:t,targetKey:o[x.Fc],field:l,value:e})},desc:o.desc})})),(0,a.jsxs)("div",{className:"mr-[3px]",children:[u,o.desc?(0,a.jsx)(g.ZT.Text,{size:"small",ellipsis:{showTooltip:{opts:{content:o.desc}}},style:{verticalAlign:c?"top":"middle"},children:o.desc}):null]})},el=e=>{var o=(0,x.lR)(e);return(0,a.jsxs)("span",{className:M(e),children:[(0,a.jsx)(g.ZT.Text,{component:"span",ellipsis:{showTooltip:{type:"tooltip",opts:{style:{maxWidth:"100%"}}}},style:{maxWidth:"calc(100% - ".concat(20*(e.deep||1)+49,"px)")},children:null==e?void 0:e.name}),(null==e?void 0:e.is_required)?(0,a.jsx)(g.ZT.Text,{style:{color:"red"},children:" * "}):null,o?(0,a.jsx)(u.Vp,{size:"mini",prefixIcon:null,className:"!coz-fg-color-blue !coz-mg-color-blue shrink-0 font-normal px-6px rounded-[36px] ml-4px align-middle",children:o}):null]})},ed=(e,o)=>e?(0,a.jsxs)(g.T,{children:[(0,a.jsx)("div",{children:s.o.t(o?"mkpl_plugin_tool_parameter_description":"Create_newtool_s4_value")}),(0,a.jsx)(g.fS,{children:s.o.t("plugin_edit_tool_test_run_example_tip")})]}):s.o.t(o?"mkpl_plugin_tool_parameter_description":"Create_newtool_s4_value"),ec=(0,n.forwardRef)((e,o)=>{var{requestParams:t,disabled:u,check:h,needCheck:j=!1,height:_=236,defaultKey:k="global_default",debugExampleStatus:I=m.ji.Default,showExampleTag:y=!1,supportFileTypeUpload:N=!1}=e,[w,D]=(0,n.useState)((0,l.Z)(t||[])),[z,T]=(0,n.useState)((0,l.Z)(t||[]));(0,n.useEffect)(()=>{D(t?(0,l.Z)(t):[]),T(t?(0,l.Z)(t):[])},[t]),(0,n.useImperativeHandle)(o,()=>({data:w}));var[E,A]=(0,n.useState)(!1),S=e=>{if(!!w){var o={};(0,f.bd)({data:w,callback:(t,a)=>{t[x.Fc]===e[x.Fc]&&(o=(0,r._)((0,i._)({},t),{path:a}))}});var t=((null==o?void 0:o.path)||[]).map(e=>[e,x.Il]).flat(),a=(0,f.UK)(z,t),n=(0,l.Z)(w);if(Array.isArray((0,d.Z)(n,t))){var s=(0,f.$F)((0,d.Z)(z,a)[0]);(0,c.Z)(n,t,[...(0,d.Z)(n,t),s])}D(n)}},C=u&&y&&I===m.ji.Enable,L=(0,f.lf)(w),O=[{title:s.o.t("Create_newtool_s4_name"),key:"name",className:v.Z["no-wrap"],width:180+20*(L-1),minWidth:220,render:e=>el(e)},{title:ed(C,u),key:"value",className:v.Z["no-wrap"],width:200,render:e=>(0,a.jsx)(en,{record:e,data:w,disabled:u,check:h,needCheck:j,defaultKey:k,supportFileTypeUpload:N})},{title:s.o.t("dataset_detail_tableTitle_actions"),key:"operation",width:120,render:e=>(0,a.jsxs)("div",{className:M(e),children:[(null==e?void 0:e.type)===m.rH.Array&&(0,a.jsx)(g.y3,{onClick:()=>{S(e),A(!E)},icon:(0,a.jsx)(p.h16,{}),type:"secondary",theme:"borderless"}),(null==e?void 0:e.name)===x.sp&&(0,f.Hn)(w,e[x.Fc])&&(0,a.jsx)(g.y3,{onClick:()=>{var o=(0,l.Z)(w);(null==e?void 0:e.id)&&((0,f.IK)(o,null==e?void 0:e.id),D(o))},icon:(0,a.jsx)(b,{}),type:"secondary",theme:"borderless"})]})}],Z=u||!(0,f.sl)(t)?O.filter(e=>"operation"!==e.key):O,F=(0,n.useMemo)(()=>({y:_,x:"100%"}),[]);return(0,a.jsx)(g.iA,{className:v.Z["debug-params-table"],pagination:!1,columns:Z,dataSource:w,rowKey:x.Fc,childrenRecordName:x.Il,expandAllRows:!0,scroll:F,empty:!u&&(0,a.jsx)("div",{className:v.Z.empty,children:s.o.t("plugin_form_no_result_desc")})})})},364112:function(e,o,t){t.r(o),t.d(o,{Debug:()=>D});var i,r=t("808549"),a=t("120454"),n=t("151064"),l=t("455069"),d=t("336205"),c=t("775699"),s=t("900021"),u=t("851645"),g=t("252237"),p=t.n(g),m=t("44172"),h=t("626389"),b=t.n(h),v=t("193893"),f=t("71569");var x=((i={})[i.Request=1]="Request",i[i.Response=2]="Response",i),M=e=>{var{markDown:o,headingType:t,rawResponse:i,showRaw:r}=e;return(0,n.jsxs)("div",{className:f.Z["mb-content"],children:[(0,n.jsx)("div",{className:f.Z["mb-header"],children:(0,n.jsxs)(c.T,{spacing:8,children:[(0,n.jsx)("span",{children:"Json"}),(0,n.jsx)(m.vUr,{className:f.Z["icon-copy"],onClick:()=>{b()(o),c.FN.success(d.o.t("copy_success"))}})]})}),(0,n.jsxs)("div",{className:f.Z["mb-main"],children:[(0,n.jsx)("div",{className:p()(f.Z["mb-left"],{[f.Z["half-width"]]:r&&2===t}),children:(0,n.jsx)(v.HK,{markDown:"```json\n".concat(o,"\n```")})}),r&&2===t?(0,n.jsx)("div",{className:f.Z["mb-right"],children:(0,n.jsx)(v.HK,{markDown:"```json\n".concat(i?i.length<3e4?i:"".concat(i.slice(0,3e4),"..."):"{}","\n```")})}):null]})]})},j=e=>{var{activeTab:o,setActiveTab:t,hideRawResponse:i,showRaw:r,setShowRaw:a}=e;return(0,n.jsxs)("div",{className:f.Z["debug-check-header"],children:[(0,n.jsxs)("div",{className:f.Z["debug-check-tab"],children:[(0,n.jsx)("div",{className:p()(f.Z["debug-check-tab-item"],{[f.Z["debug-check-tab-item-active"]]:o===x.Request}),onClick:()=>t(x.Request),children:"Request"}),(0,n.jsx)("div",{className:f.Z["debug-check-tab-line"]}),(0,n.jsx)("div",{className:p()(f.Z["debug-check-tab-item"],{[f.Z["debug-check-tab-item-active"]]:o===x.Response}),onClick:()=>t(x.Response),children:"Response"})]}),o!==x.Response||i?null:(0,n.jsxs)(c.T,{spacing:8,children:[(0,n.jsx)("span",{children:"Raw Response"}),(0,n.jsx)(m.VKQ,{className:p()(f.Z.icon,{[f.Z.open]:r}),onClick:()=>{a(!r)}})]})]})},_=e=>{var{children:o}=e;return(0,n.jsx)("div",{className:f.Z["process-content"],children:o})},k=e=>{var{toolMessageUnit:o}=e,{request:t,response:i,failReason:r,rawResp:a}=o,[s,u]=(0,l.useState)(1),[g,p]=(0,l.useState)(!1);return(0,n.jsx)(n.Fragment,{children:t||i?(0,n.jsxs)("div",{className:f.Z["debug-result-content"],children:[(0,n.jsx)(j,{activeTab:s,setActiveTab:u,hideRawResponse:!(!r&&a),showRaw:g,setShowRaw:p}),1===s?(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("div",{className:f.Z["llm-api-content"],children:(0,n.jsx)(M,{markDown:t?JSON.stringify(JSON.parse(t||"{}"),null,2):"",headingType:s,showRaw:g})})}):(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("div",{className:f.Z["llm-api-content"],children:r?(0,n.jsx)("div",{className:f.Z["error-reason-box"],children:(0,n.jsx)(c.jL,{className:f.Z["error-reason"],fullMode:!1,icon:null,closeIcon:null,type:"danger",description:(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{children:d.o.t("plugin_s4_debug_detail")}),(0,n.jsx)("div",{style:{wordBreak:"break-word"},children:r})]})})}):(0,n.jsx)(M,{headingType:s,markDown:JSON.stringify(JSON.parse(i||"{}"),null,2),rawResponse:JSON.stringify(JSON.parse(a||"{}"),null,2),showRaw:g})})})]}):(0,n.jsx)("div",{className:f.Z["llm-debug-empty"],children:(0,n.jsx)("div",{className:f.Z["llm-debug-empty-content"],children:d.o.t("plugin_s4_debug_empty")})})})},I=e=>{var{checkParams:o}=e;return(0,n.jsx)(_,{children:(0,n.jsx)(k,{toolMessageUnit:o})})},y=t("252793"),{Text:N}=c.ZT,w=(e,o,t)=>(0,n.jsxs)(N,{className:y.Z["card-title"],ellipsis:{showTooltip:{opts:{content:"".concat(e,".").concat(o),style:{wordBreak:"break-word"}}}},children:[e,".",o," ",d.o.t(t)]}),D=e=>{var{disabled:o,apiInfo:t,pluginId:i,apiId:g,pluginName:p,setDebugStatus:m,debugExample:h,setDebugExample:b,isViewExample:v=!1,pluginType:f,onSuccessCallback:x}=e,[M,j]=(0,l.useState)({}),[_,k]=(0,l.useState)();return(0,l.useEffect)(()=>{h?j((0,a._)((0,r._)({},M),{request:null==h?void 0:h.req_example,response:null==h?void 0:h.resp_example,failReason:""})):j({})},[h]),(0,n.jsx)("div",{className:y.Z["debug-check"],"data-testid":"plugin.tool.debug-modal-content",children:(0,n.jsxs)(c.X2,{gutter:16,children:[(0,n.jsx)(c.JX,{span:12,children:(0,n.jsxs)("div",{className:y.Z["main-container"],children:[(0,n.jsx)("div",{className:y.Z["card-header"],children:v?(0,n.jsx)(N,{className:y.Z["card-title"],children:d.o.t("Create_newtool_s4_title")}):w(p,t.name,"Create_newtool_s4_title")}),(0,n.jsx)("div",{style:{maxHeight:v?"calc(100% - 55px)":542,display:"flex"},children:(0,n.jsx)(u.y,{pluginType:f,disabled:o,pluginId:i,apiId:g,requestParams:null==t?void 0:t.request_params,callback:e=>{var{status:o,request:t,response:i,failReason:r,rawResp:a}=e;k(o),j({status:o,request:t,response:i,failReason:r,rawResp:a}),null==m||m(o),o===s.Q.PASS&&(null==b||b({req_example:t,resp_example:i})),o===s.Q.PASS&&(null==x||x())},debugExampleStatus:null==t?void 0:t.debug_example_status,showExampleTag:!v})})]})}),(0,n.jsx)(c.JX,{span:12,children:(0,n.jsxs)("div",{className:y.Z["main-container"],children:[(0,n.jsx)("div",{className:y.Z["card-header"],children:(0,n.jsxs)(c.T,{style:{width:"100%"},children:[v?(0,n.jsx)(N,{className:y.Z["card-title"],children:d.o.t("plugin_edit_tool_test_run_debugging_example")}):w(p,t.name,"Create_newtool_s4_result"),_===s.Q.PASS&&(0,n.jsx)(c.fS,{color:"green",children:d.o.t("plugin_s4_debug_pass")}),_===s.Q.FAIL&&(0,n.jsx)(c.fS,{color:"red",children:d.o.t("plugin_s4_debug_failed")})]})}),(0,n.jsx)("div",{className:y.Z["card-debug-check"],style:{height:v?"100%":542},children:(0,n.jsx)(I,{checkParams:M})})]})})]})})}},437134:function(e,o,t){t.d(o,{Z:()=>L});var i=t("151064"),r=t("455069"),a=t("335740"),n=t("947578"),l=t("336205"),d=t("775699"),c=t("44172"),s=t("458949"),u=t("577413"),g=t("407821"),p=t.n(g),m=t("472772"),h=t.n(m),b=t("395245"),v=t.n(b),f=t("297998"),x=t.n(f),M=t("646576"),j=t.n(M),_=t("606121"),k=t.n(_),I=t("326046"),y={};y.styleTagTransform=k(),y.setAttributes=x(),y.insert=v().bind(null,"head"),y.domAPI=h(),y.insertStyleElement=j(),p()(I.Z,y);var N=I.Z&&I.Z.locals?I.Z.locals:void 0,w=e=>{var{record:o,disabled:t,onSourceChange:r,onReferenceChange:a,onValueChange:n,referenceOption:c}=e;return(0,i.jsxs)(d.BZ,{style:{width:"100%",flexWrap:"nowrap"},children:[(0,i.jsx)(d.Cj,{theme:"light",className:N["action-input-value-pre"],value:o.default_param_source||s.qi.Input,disabled:t,optionList:[{label:l.o.t("bot_ide_plugin_setting_modal_default_value_select_mode_reference"),value:s.qi.Variable},{label:l.o.t("bot_ide_plugin_setting_modal_default_value_select_mode_input"),value:s.qi.Input}],onChange:e=>{null==r||r(Number(e)),null==a||a(""),null==n||n("")}}),o.default_param_source===s.qi.Variable?(0,i.jsx)(d.Cj,{theme:"light",disabled:t,style:{width:"100%",overflow:"hidden"},className:N["action-input-value-content"],placeholder:l.o.t("bot_ide_plugin_setting_modal_default_value_select_mode_reference_placeholder"),value:o.variable_ref,onChange:e=>{null==a||a(String(e))},children:null==c?void 0:c.map(e=>(0,i.jsx)(d.Cj.Option,{value:String(e.label),children:(0,i.jsxs)("div",{className:N["reference-option-item"],children:[(0,i.jsx)(d.ZT.Text,{className:N["reference-option-text"],ellipsis:{showTooltip:{opts:{content:e.label,style:{wordBreak:"break-word"}}}},children:e.label}),(0,i.jsx)(d.ZT.Text,{className:N["reference-option-subtext"],ellipsis:{showTooltip:{opts:{content:e.value,style:{wordBreak:"break-word"}}}},children:e.value})]})},String(e.label)))}):(0,i.jsx)(d.u3,{disabled:t,className:N["action-input-value-content"],placeholder:l.o.t("bot_ide_plugin_setting_modal_default_value_select_mode_input_placeholder"),value:o.local_default,onChange:e=>{null==n||n(String(e))}})]})},D=t("541509"),z=t("827666"),T=t("944970"),E=t("336940"),A={};A.styleTagTransform=k(),A.setAttributes=x(),A.insert=v().bind(null,"head"),A.domAPI=h(),A.insertStyleElement=j(),p()(E.Z,A);var S=E.Z&&E.Z.locals?E.Z.locals:void 0,C=e=>{var{record:o,defaultKey:t,disableKey:s,updateNodeAndData:g}=e,[p,m]=(0,r.useState)(0),[h,b]=(0,r.useState)(!1),v=(0,r.useRef)(null),[f,x]=(0,r.useState)({}),M=(0,r.useCallback)(()=>{b(!0);var e=(0,a.Z)(o);if(e[t]){var i=(0,u.aE)(JSON.parse(e[t]||"[]"),e.sub_parameters||[]);e.sub_parameters=i}x(e)},[o]),j=()=>{b(!1),x({})};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(d.y3,{disabled:o.is_required&&o[s],icon:(0,i.jsx)(c.yl5,{}),className:S["arr-edit-btn"],style:{width:"100%"},onClick:M,children:l.o.t("plugin_edit_tool_default_value_array_edit_button")}),h?(0,i.jsx)(d.M5,{title:l.o.t("plugin_edit_tool_default_value_array_edit_modal_title"),width:792,okText:l.o.t("Save"),visible:h,onCancel:j,hasCancel:!1,onOk:()=>{var e;if(m(p+1),document.getElementsByClassName("errorDebugClassTag").length>0){(0,u.$S)(".errorDebugClassTag"),d.FN.error({content:(0,n.u)(l.o.t("tool_new_S2_feedback_failed")),duration:3,theme:"light",showClose:!1});return}g(t,JSON.stringify(Object.values((0,u.SR)(null===(e=v.current)||void 0===e?void 0:e.data,!1))[0])),j()},zIndex:1050,children:(0,i.jsx)(D.Z,{ref:v,requestParams:[f],defaultKey:t,disabled:!1,check:p,needCheck:!1,height:400})}):null]})},L=e=>{var{record:o,data:t,setData:r,canReference:n=!1,defaultKey:d="global_default",disableKey:c="global_disable",referenceOption:g}=e,p=(e,i)=>{(0,u.jd)({data:t,targetKey:o[z.Fc],field:e,value:i}),r((0,a.Z)(t))};return void 0===o[d]?(0,i.jsx)(i.Fragment,{}):o.type===s.rH.Array?(0,i.jsx)("div",{className:S["modal-wrapper"],children:(0,i.jsx)(C,{record:o,defaultKey:d,disableKey:c,updateNodeAndData:p})}):(0,i.jsx)(i.Fragment,{children:n?(0,i.jsx)(w,{record:o,disabled:!!o[c],referenceOption:g,onSourceChange:e=>{p("default_param_source",e)},onReferenceChange:e=>{p("variable_ref",e)},onValueChange:e=>{p(d,e)}}):(0,i.jsx)(T.O6,{width:"100%",placeholder:l.o.t("plugin_edit_tool_default_value_input_placeholder"),max:2e3,val:o[d],useCheck:!1,filterSpace:!1,disabled:!!o[c],callback:e=>{p(d,e)}})})}},944970:function(e,o,t){t.d(o,{O6:function(){return p},vw:function(){return m}});var i=t(151064),r=t(455069),a=t(252237),n=t.n(a);t(336205);var l=t(775699),d=t(44172),c=t(458949),s=t(577413),u=t(252793),g=t(827666),p=e=>{var{val:o="",max:t=500,check:a=0,width:d=200,useCheck:p=!0,filterSpace:m=!0,placeholder:h,callback:b,targetKey:v="",checkSame:f=!1,checkAscii:x=!1,isRequired:M=!1,data:j,useBlockWrap:_=!1,disabled:k,dynamicWidth:I=!1,deep:y=1}=e,[N,w]=(0,r.useState)(o),[D,z]=(0,r.useState)(0);(0,r.useEffect)(()=>{w(o)},[o]),(0,r.useEffect)(()=>{if(0!==a&&N!==g.sp&&N!==g.UR)T(N)},[a]);var T=e=>{var o=""===e?g.Oz.NAME_EMPTY:g.Oz.NO_ERROR;if(M&&""===e){z(g.Oz.DESC_EMPTY);return}if(x){z(g.Oz.NO_ERROR);return}if(!!p)!o&&(o=/^[\w-]+$/.test(e)?g.Oz.NO_ERROR:g.Oz.CHINESE),!o&&j&&f&&(o=(0,s.aY)(j,v,e)?g.Oz.REPEAT:g.Oz.NO_ERROR),z(o)},E=e=>(m&&(e=e.replace(/\s+/g,"")),t>0&&(e=e.slice(0,t)),e),A=1!==y||(null==j?void 0:j.some(e=>e.type===c.rH.Array||e.type===c.rH.Object)),S=I?"calc(100% - ".concat(20*y,"px)"):d,C=I?"calc(100% - ".concat(20*y,"px - 8px)"):d,L=()=>(0,i.jsxs)(i.Fragment,{children:[I&&!A?(0,i.jsx)("span",{style:{display:"inline-block",width:22}}):null,(0,i.jsx)(l.ZT.Text,{component:"span",ellipsis:{showTooltip:{type:"tooltip",opts:{style:{maxWidth:"100%"}}}},className:u.Z["plugin-tooltip-error"],children:(0,i.jsx)("span",{children:g.JN[D]})})]});return(0,i.jsxs)("span",{style:_?{display:"inline-block",width:"100%"}:{},children:[I&&!A?(0,i.jsx)("span",{style:{display:"inline-block",width:20}}):null,(0,i.jsx)(l.u3,{placeholder:h,disabled:k||N===g.sp||N===g.UR,style:{width:S},value:N,validateStatus:D?"error":"default",onChange:e=>{var o=E(e);null==b||b(o),w(o),T(o)},onBlur:()=>{T(N)}}),(0,i.jsx)("br",{}),0!==D&&I?(0,i.jsx)("div",{className:u.Z["check-box"],style:{width:C},children:(0,i.jsx)("span",{className:n()(u.Z["form-check-tip"],"errorClassTag",u.Z.w110),children:L()})}):null,0!==D&&!I&&(0,i.jsx)("div",{className:u.Z["check-box"],style:{width:C},children:(0,i.jsx)("span",{style:{marginLeft:4,right:-15},className:n()(u.Z["form-check-tip"],"errorClassTag"),children:L()})})]})},m=e=>(0,i.jsxs)("div",{className:"whitespace-nowrap",children:[e.name,e.required?(0,i.jsx)(l.ZT.Text,{style:{color:"red",marginLeft:-3},children:" * "}):null,e.toolTipText?(0,i.jsx)(l.u,{content:e.toolTipText,children:(0,i.jsx)(d.aUG,{style:{color:"#5f5f5f9e",position:"relative",top:3,left:2}})}):null]})},517362:function(e,o,t){t.r(o),t.d(o,{default:()=>h});var i,r=t("808549"),a=t("151064"),n=t("455069"),l=t("517465"),d=t.n(l),c=t("228962"),s=t("149528"),u=t("66860"),g=t("179164");var p=((i={}).TOOL="tool",i.MOCKSET_LIST="mockset_list",i.MOCKSET_DETAIL="mockset_detail",i.CLOUD_IDE="cloud_ide",i),m=e=>{var{pluginID:o,spaceID:t,projectID:i,refetch:r,version:l,renderCustomContent:d}=e,u=(0,c.ob)(),m=(0,s.Qy)(),h=m.module,b=m.tool_id,v=m.mockset_id;if(h===p.MOCKSET_DETAIL&&(!b||!v)||h===p.MOCKSET_LIST&&!b||h===p.TOOL&&!b)throw Error("xxxxxxxx");var f=!h||![p.TOOL,p.MOCKSET_LIST,p.MOCKSET_DETAIL,p.CLOUD_IDE].includes(h);return(0,n.useEffect)(()=>{null==u||u.getState().init()},[]),(0,a.jsxs)(a.Fragment,{children:[f?(0,a.jsx)(g.sp,{projectId:i,keepDocTitle:!0}):null,h===p.TOOL&&b?(0,a.jsx)(g.sU,{toolID:b,onDebugSuccessCallback:()=>{r()}}):null,h===p.MOCKSET_LIST&&b?(0,a.jsx)(g.DT,{toolID:b}):null,h===p.MOCKSET_DETAIL&&b&&v?(0,a.jsx)(g.zU,{toolID:b,mocksetID:v,pluginID:o,spaceID:t,version:l}):null,null==d?void 0:d({moduleType:h})]})},h=e=>{var o=(0,s.aQ)(),t=(0,s.vp)(),i=(0,s.d0)(),{version:n}=(0,s.jo)(),{uri:l,widget:g}=(0,s.BX)(),p=(0,s.ZQ)(),h=(0,u.Un)(e=>e.refetch),b=null==l?void 0:l.displayName;if(!o||!b)throw Error("xxxxxxxx");var v="/plugin/".concat(b);return(0,a.jsx)(c.pn,{pluginID:b,spaceID:o,projectID:t,version:n,onUpdateDisplayName:e=>{g.setTitle(e),e&&e!==p&&h()},onStatusChange:e=>{g.setUIState(e)},resourceNavigate:{toResource:(e,o,t,r)=>o?i("/".concat(e,"/").concat(o,"?").concat(d().stringify(t)),r):"",tool:(e,o,t)=>i("".concat(v,"?module=tool&tool_id=").concat(e,"&").concat(d().stringify(o)),t),mocksetList:(e,o,t)=>i("".concat(v,"?module=mockset_list&tool_id=").concat(e,"&").concat(d().stringify(o)),t),mocksetDetail:(e,o,t,r)=>i("".concat(v,"?module=mockset_detail&tool_id=").concat(e,"&mockset_id=").concat(o,"&").concat(d().stringify(t)),r),cloudIDE:(e,o)=>i("".concat(v,"?module=cloud_ide&").concat(d().stringify(e)),o)},children:(0,a.jsx)(m,(0,r._)({pluginID:b,spaceID:o,projectID:t,refetch:h,version:n},e))})}},345822:function(e,o,t){t.d(o,{m:()=>S,a:()=>C});var i=t("825955"),r=t("808549"),a=t("120454"),n=t("151064"),l=t("455069"),d=t("947578"),c=t("336205"),s=t("707107"),u=t("214942"),g=t("775699"),p=t("178385"),m=t("382534"),h=t("583193"),b=t("177625"),v=t("407821"),f=t.n(v),x=t("472772"),M=t.n(x),j=t("395245"),_=t.n(j),k=t("297998"),I=t.n(k),y=t("646576"),N=t.n(y),w=t("606121"),D=t.n(w),z=t("565050"),T={};T.styleTagTransform=D(),T.setAttributes=I(),T.insert=_().bind(null,"head"),T.domAPI=M(),T.insertStyleElement=N(),f()(z.Z,T);var E=z.Z&&z.Z.locals?z.Z.locals:void 0,A={name:[{required:!0,message:c.o.t("please_enter_mockset_name")},{pattern:RegExp("^[\\w\\s\\u4e00-\\u9fa5]+$","u"),message:c.o.t("create_plugin_modal_nameerror_cn")}],desc:[]},S=e=>{g.O$.success(c.o.t("created_mockset_please_add_mock_data"))},C=e=>{var o,t,{visible:v,zIndex:f,disabled:x,initialInfo:M,onSuccess:j,onCancel:_,needResetPopoverContainer:k}=e,I=(0,l.useRef)(),y=!M.id,N=(0,u.rY)(e=>e.space.space_type)===p.Sn.Personal;var w=(o=(0,i._)(function*(e){var{id:o,name:t,desc:i,bindSubjectInfo:n,bizCtx:l}=e,{toolID:u,spaceID:p}=(0,h.MX)(l,n),v={environment:(0,h.zj)(),workspace_id:p||"",workspace_type:N?"personal_workspace":"team_workspace",tool_id:u||"",status:1,mock_set_id:"",auto_gen_mode:s.eM.MANUAL,mock_counts:1};try{var{id:f}=yield m.xd.SaveMockSet({name:t,description:i,mockSubject:(0,h.AX)(l,n),bizCtx:l,id:o||"0"},{__disableErrorToast:!0});null==j||j({id:f,name:t,description:i}),(0,s.Gg)(s.Kg.create_mockset_front,(0,a._)((0,r._)({},v),{status:0,mock_set_id:String(f)||""}))}catch(e){var x,{msg:M,code:_}=e,k=(0,a._)((0,r._)({},v),{status:1,error:null==e?void 0:e.msg,error_type:"unknown"});Number(_)===b.M3.REPEAT_NAME?(null===(x=I.current)||void 0===x||x.setError("name",c.o.t("name_already_taken")),(0,s.Gg)(s.Kg.create_mockset_front,(0,a._)((0,r._)({},k),{error_type:"repeat_name"}))):(g.O$.error({content:(0,d.u)(M)}),(0,s.Gg)(s.Kg.create_mockset_front,(0,a._)((0,r._)({},k),{error_type:"unknown"})))}}),function(e){return o.apply(this,arguments)});var D=(t=(0,i._)(function*(){var e;yield null===(e=I.current)||void 0===e?void 0:e.submitForm()}),function(){return t.apply(this,arguments)});return(0,n.jsx)(g.M5,{type:"action-small",zIndex:f,title:"".concat(y?c.o.t("create_mockset"):c.o.t("edit_mockset")),visible:v,getPopupContainer:k?()=>document.body:void 0,onCancel:_,okButtonProps:{onClick:D,disabled:x},children:(0,n.jsx)(g.l0,{getFormApi:e=>I.current=e,showValidateIcon:!1,initValues:y?(0,a._)((0,r._)({},M),{name:function(e){if(!!e){var o=Math.floor(90*Math.random()+10);return"".concat(e," mockset").concat(o)}}(M.name)}):M,onSubmit:e=>w(e),className:E["mockset-create-form"],children:e=>{var{formState:o}=e;return(0,n.jsxs)(n.Fragment,{children:[x?(0,n.jsx)(g.l0.Slot,{label:{text:c.o.t("mockset_name"),required:!0},children:(0,n.jsx)("div",{children:null==M?void 0:M.name})}):(0,n.jsx)(g.P6,{field:"name",label:c.o.t("mockset_name"),placeholder:c.o.t("good_mockset_name_descriptive_concise"),trigger:["blur","change"],maxCount:50,maxLength:50,rows:1,onBlur:()=>{var e,o,t;null===(t=I.current)||void 0===t||t.setValue("name",null===(o=I.current)||void 0===o?void 0:null===(e=o.getValue("name"))||void 0===e?void 0:e.trim())},rules:A.name}),x?(0,n.jsx)(g.l0.Slot,{label:{text:c.o.t("mockset_description")},children:(0,n.jsx)("div",{children:null==M?void 0:M.desc})}):(0,n.jsx)(g.P6,{field:"desc",label:{text:c.o.t("mockset_description")},trigger:["blur","change"],placeholder:c.o.t("describe_use_scenarios_of_mockset"),rows:2,maxCount:2e3,maxLength:2e3,rules:A.desc,onBlur:()=>{var e,o,t;null===(t=I.current)||void 0===t||t.setValue("desc",null===(o=I.current)||void 0===o?void 0:null===(e=o.getValue("desc"))||void 0===e?void 0:e.trim())}})]})}})})}},177625:function(e,o,t){t.d(o,{Fc:function(){return r},M3:function(){return d},Ow:function(){return a},S9:function(){return i},dz:function(){return l},fO:function(){return n}});var i=4,r=102400,a='"',n='"',l="mock",d={REPEAT_NAME:0x23c7e5fc}},939619:function(e,o,t){t.d(o,{M:function(){return n},U:function(){return a}});var i,r,a=((i={}).STRING="string",i.INTEGER="integer",i.NUMBER="number",i.OBJECT="object",i.ARRAY="array",i.BOOLEAN="boolean",i);var n=((r={}).DEFAULT="default",r.REMOVED="removed",r.ADDED="added",r)},583193:function(e,o,t){t.d(o,{AX:function(){return b},Cu:function(){return function e(o,t){var r,n;if(o.status===a.M.REMOVED)return{};switch(o.type){case a.U.ARRAY:return{[o.label]:null===(r=o.children)||void 0===r?void 0:r.reduce((o,i)=>{var r=e(i,t)[c(0)];return void 0!==r&&o.push(r),o},[])};case a.U.OBJECT:return{[o.label]:null===(n=o.children)||void 0===n?void 0:n.reduce((o,r)=>o=(0,i._)({},o,e(r,t)),{})};default:return{[o.label]:o.realValue}}}},KD:function(){return d},Ki:function(){return u},MX:function(){return h},Vv:function(){return s},Zg:function(){return function e(o,t,i){var r,n=p(null==t?void 0:t.type);if(!!t&&!!n){var{generateFn:l=g}=i||{},[d,s]=l(n),u=(null==i?void 0:i.keyPrefix)?"".concat(null==i?void 0:i.keyPrefix,"-").concat(o):o,m={label:o,realValue:d,displayValue:s,description:t.description,isRequired:(null==i?void 0:null===(r=i.required)||void 0===r?void 0:r.includes(o))||!1,type:n,status:a.M.ADDED,key:u};if(n===a.U.OBJECT){var h=[];for(var b in t.properties)if(b&&"object"==typeof t.properties[b]){var v=e(b,t.properties[b],{required:t.required,keyPrefix:u,generateFn:l});v&&h.push(v)}m.children=h}if(n===a.U.ARRAY&&"object"==typeof t.items){var f=t.items instanceof Array?t.items[0]:t.items;if("object"==typeof f){m.childrenType=p(f.type);var x=e(c(0),f,{required:t.required,keyPrefix:u,generateFn:l});x&&(m.children=[x])}}return m}}},_v:function(){return m},pB:function(){return c},zj:function(){return v}});var i=t(808549),r=t(824833),a=t(939619),n=t(177625);function l(e,o){try{return JSON.parse(e)}catch(e){return null==o?void 0:o()}}function d(e){return l(e)}function c(e){return"".concat("item_").concat(e)}var s=e=>{if(!e)return 0;var{size:o}=new Blob([e]);return o};function u(e,o){switch(e){case a.U.STRING:var t=o.getStringValue();return[t,"".concat(n.Ow).concat(t).concat(n.fO)];case a.U.BOOLEAN:var i=o.getBooleanValue();return[i,"".concat(i)];case a.U.NUMBER:case a.U.INTEGER:var r=o.getNumberValue();return[r,"".concat(r)];default:return[void 0,void 0]}}function g(e){return u(e,{getBooleanValue:()=>!1,getNumberValue:()=>0,getStringValue:()=>""})}function p(e){var o="object"==typeof e?e[0]:e;return"null"===o?void 0:o}function m(e){return JSON.stringify(e,null,n.S9)}function h(e,o){var{bizSpaceID:t,ext:i,trafficScene:a}=e||{},n=l((null==i?void 0:i.mockSubjectInfo)||"{}"),{componentID:d,parentComponentID:c}=o;switch(a){case r.zE.CozeWorkflowDebug:return{spaceID:t,toolID:null==n?void 0:n.componentID,pluginID:null==n?void 0:n.parentComponentID};case r.zE.CozeSingleAgentDebug:case r.zE.CozeMultiAgentDebug:case r.zE.CozeToolDebug:default:return{spaceID:t,toolID:d,pluginID:c}}}function b(e,o){var{ext:t,trafficScene:i}=e||{},a=l((null==t?void 0:t.mockSubjectInfo)||"{}");switch(i){case r.zE.CozeWorkflowDebug:return a;case r.zE.CozeSingleAgentDebug:case r.zE.CozeMultiAgentDebug:case r.zE.CozeToolDebug:default:return o}}function v(){return"cn-boe"}},71569:function(e,o,t){var i=t(407821),r=t.n(i),a=t(472772),n=t.n(a),l=t(395245),d=t.n(l),c=t(297998),s=t.n(c),u=t(646576),g=t.n(u),p=t(606121),m=t.n(p),h=t(575694),b={};b.styleTagTransform=m(),b.setAttributes=s(),b.insert=d().bind(null,"head"),b.domAPI=n(),b.insertStyleElement=g(),r()(h.Z,b),o.Z=h.Z&&h.Z.locals?h.Z.locals:void 0},252793:function(e,o,t){var i=t(407821),r=t.n(i),a=t(472772),n=t.n(a),l=t(395245),d=t.n(l),c=t(297998),s=t.n(c),u=t(646576),g=t.n(u),p=t(606121),m=t.n(p),h=t(200601),b={};b.styleTagTransform=m(),b.setAttributes=s(),b.insert=d().bind(null,"head"),b.domAPI=n(),b.insertStyleElement=g(),r()(h.Z,b),o.Z=h.Z&&h.Z.locals?h.Z.locals:void 0},743424:function(e){e.exports="data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20height%3D%223%22%20width%3D%2212%22%3E%3Cg%20fill%3D%22%236c6c6c%22%3E%3Ccircle%20cx%3D%221%22%20cy%3D%221%22%20r%3D%221%22%2F%3E%3Ccircle%20cx%3D%225%22%20cy%3D%221%22%20r%3D%221%22%2F%3E%3Ccircle%20cx%3D%229%22%20cy%3D%221%22%20r%3D%221%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E"},260827:function(e){e.exports="data:image/svg+xml,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20viewBox%3D%270%200%206%203%27%20enable-background%3D%27new%200%200%206%203%27%20height%3D%273%27%20width%3D%276%27%3E%3Cg%20fill%3D%27%231a85ff%27%3E%3Cpolygon%20points%3D%275.5%2C0%202.5%2C3%201.1%2C3%204.1%2C0%27%2F%3E%3Cpolygon%20points%3D%274%2C0%206%2C2%206%2C0.6%205.4%2C0%27%2F%3E%3Cpolygon%20points%3D%270%2C2%201%2C3%202.4%2C3%200%2C0.6%27%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E"},516254:function(e){e.exports="data:image/svg+xml,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20viewBox%3D%270%200%206%203%27%20enable-background%3D%27new%200%200%206%203%27%20height%3D%273%27%20width%3D%276%27%3E%3Cg%20fill%3D%27%23bf8803%27%3E%3Cpolygon%20points%3D%275.5%2C0%202.5%2C3%201.1%2C3%204.1%2C0%27%2F%3E%3Cpolygon%20points%3D%274%2C0%206%2C2%206%2C0.6%205.4%2C0%27%2F%3E%3Cpolygon%20points%3D%270%2C2%201%2C3%202.4%2C3%200%2C0.6%27%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E"},424013:function(e){e.exports="data:image/svg+xml,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20viewBox%3D%270%200%206%203%27%20enable-background%3D%27new%200%200%206%203%27%20height%3D%273%27%20width%3D%276%27%3E%3Cg%20fill%3D%27%23e51400%27%3E%3Cpolygon%20points%3D%275.5%2C0%202.5%2C3%201.1%2C3%204.1%2C0%27%2F%3E%3Cpolygon%20points%3D%274%2C0%206%2C2%206%2C0.6%205.4%2C0%27%2F%3E%3Cpolygon%20points%3D%270%2C2%201%2C3%202.4%2C3%200%2C0.6%27%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E"},966171:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".tool-wrap-SVtvNo{background-color:#f7f7fa;min-height:100%;padding-bottom:20px}.tool-wrap-SVtvNo .semi-table-row-head{font-size:12px;color:var(--light-usage-text-color-text-1,rgba(28,29,35,.8))!important}.tool-wrap-SVtvNo .semi-steps-item-title-text{font-size:14px}.tool-wrap-SVtvNo .semi-table-row.semi-table-row-expanded:last-child .semi-table-row-cell{border-bottom:1px solid transparent}.tool-wrap-SVtvNo .header-ykyZNP{border-bottom:1px solid rgba(29,28,35,.08);align-items:center;padding:16px 24px 12px;display:flex}.tool-wrap-SVtvNo .header-ykyZNP .simple-title-fUbscH{align-items:center;height:56px;display:flex}.tool-wrap-SVtvNo .header-ykyZNP .simple-title-fUbscH .title-UFFrpT{color:#1d1c23;flex:1;margin-left:12px;font-size:18px;font-weight:600;line-height:56px}.tool-wrap-SVtvNo .header-ykyZNP .preview-header-QC7twe{flex-direction:column;flex:1;display:flex}.tool-wrap-SVtvNo .header-ykyZNP .preview-header-QC7twe .simple-title-fUbscH .title-UFFrpT{margin-left:0}.tool-wrap-SVtvNo .header-ykyZNP .breadcrumb-pcXwfK{margin-bottom:24px}.tool-wrap-SVtvNo .content-yXX0wX{padding:36px 0 30px}.tool-wrap-SVtvNo .modal-steps-r3vVHh{width:1008px;margin:0 auto}.tool-wrap-SVtvNo .form-wrap-AYm8Tk{margin:42px auto 0}.tool-wrap-SVtvNo .form-wrap-AYm8Tk .semi-form-vertical .semi-form-field:last-child{padding-bottom:0}.tool-wrap-SVtvNo .tool-footer-hZxisL{text-align:right;width:calc(100% - 200px);min-width:1008px;margin:0 auto}.tool-wrap-SVtvNo .tool-footer-hZxisL.step-one-sG5K4l{max-width:1008px;margin:0 auto}.tool-wrap-SVtvNo .tool-footer-hZxisL .error-msg-jryXzt{color:#f93920;text-align:left;padding:8px 24px;font-size:14px;line-height:16px}.tool-wrap-SVtvNo .tool-footer-hZxisL .error-msg-jryXzt .link-ifngyY{color:#4d53e8;font-size:14px;font-weight:400;line-height:16px}.tool-wrap-SVtvNo .tool-footer-hZxisL .footer-content-ixqvj2{padding-top:24px}",""]),n.locals={"tool-wrap":"tool-wrap-SVtvNo",toolWrap:"tool-wrap-SVtvNo",header:"header-ykyZNP","simple-title":"simple-title-fUbscH",simpleTitle:"simple-title-fUbscH",title:"title-UFFrpT","preview-header":"preview-header-QC7twe",previewHeader:"preview-header-QC7twe",breadcrumb:"breadcrumb-pcXwfK",content:"content-yXX0wX","modal-steps":"modal-steps-r3vVHh",modalSteps:"modal-steps-r3vVHh","form-wrap":"form-wrap-AYm8Tk",formWrap:"form-wrap-AYm8Tk","tool-footer":"tool-footer-hZxisL",toolFooter:"tool-footer-hZxisL","step-one":"step-one-sG5K4l",stepOne:"step-one-sG5K4l","error-msg":"error-msg-jryXzt",errorMsg:"error-msg-jryXzt",link:"link-ifngyY","footer-content":"footer-content-ixqvj2",footerContent:"footer-content-ixqvj2"},o.Z=n},84089:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,'.plugin-detail-info-BjPmrA{padding-bottom:12px;position:relative}.plugin-detail-info-BjPmrA:after{content:"";background-color:rgba(29,28,35,.08);width:calc(100% + 48px);height:1px;position:absolute;bottom:0;left:-24px}.plugin-detail-info-BjPmrA .plugin-detail-title-thYnZ4{max-width:300px;font-size:18px;font-weight:600;line-height:24px}.plugin-detail-info-BjPmrA .plugin-detail-published-deiPpH{color:rgba(28,29,35,.6);font-size:12px;line-height:16px}.plugin-detail-info-BjPmrA .plugin-detail-avatar-Wmdwge{border-radius:6px;width:36px;height:36px}.plugin-detail-info-BjPmrA .plugin-detail-desc-tCqHvW{color:rgba(28,29,35,.8);max-width:300px;line-height:16px}',""]),n.locals={"plugin-detail-info":"plugin-detail-info-BjPmrA",pluginDetailInfo:"plugin-detail-info-BjPmrA","plugin-detail-title":"plugin-detail-title-thYnZ4",pluginDetailTitle:"plugin-detail-title-thYnZ4","plugin-detail-published":"plugin-detail-published-deiPpH",pluginDetailPublished:"plugin-detail-published-deiPpH","plugin-detail-avatar":"plugin-detail-avatar-Wmdwge",pluginDetailAvatar:"plugin-detail-avatar-Wmdwge","plugin-detail-desc":"plugin-detail-desc-tCqHvW",pluginDetailDesc:"plugin-detail-desc-tCqHvW"},o.Z=n},523878:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".tool-wrap-mFQZuZ{background-color:#f7f7fa;min-height:100%;padding-bottom:20px}.tool-wrap-mFQZuZ .semi-table-row-head{font-size:12px;color:var(--light-usage-text-color-text-1,rgba(28,29,35,.8))!important}.tool-wrap-mFQZuZ .semi-steps-item-title-text{font-size:14px}.tool-wrap-mFQZuZ .semi-table-row.semi-table-row-expanded:last-child .semi-table-row-cell{border-bottom:1px solid transparent}.tool-wrap-mFQZuZ .header-LkSgYi{border-bottom:1px solid rgba(29,28,35,.08);align-items:center;padding:12px 16px}.tool-wrap-mFQZuZ .header-LkSgYi .simple-title-GP82iK{align-items:center;height:32px;display:flex}.tool-wrap-mFQZuZ .header-LkSgYi .simple-title-GP82iK .title-z3P_6q{color:#1d1c23;flex:1;margin-left:12px;font-size:16px;font-weight:600;line-height:32px}.tool-wrap-mFQZuZ .header-LkSgYi .preview-header-dlLv_0{flex-direction:column;flex:1;display:flex}.tool-wrap-mFQZuZ .header-LkSgYi .preview-header-dlLv_0 .simple-title-GP82iK .title-z3P_6q{margin-left:0}.tool-wrap-mFQZuZ .header-LkSgYi .breadcrumb-LBkKoT{margin-bottom:24px}.tool-wrap-mFQZuZ .content-pqxJdN{padding:36px 0 30px}.tool-wrap-mFQZuZ .modal-steps-FUDFnm{width:1008px;margin:0 auto}.tool-wrap-mFQZuZ .form-wrap-Qc2ad_{margin:42px auto 0}.tool-wrap-mFQZuZ .form-wrap-Qc2ad_ .semi-form-vertical .semi-form-field:last-child{padding-bottom:0}.tool-wrap-mFQZuZ .tool-footer-nQuF01{text-align:right;width:calc(100% - 200px);min-width:1008px;margin:0 auto}.tool-wrap-mFQZuZ .tool-footer-nQuF01.step-one-hqF28Z{max-width:1008px;margin:0 auto}.tool-wrap-mFQZuZ .tool-footer-nQuF01 .error-msg-iwcU0F{color:#f93920;text-align:left;padding:8px 24px;font-size:14px;line-height:16px}.tool-wrap-mFQZuZ .tool-footer-nQuF01 .error-msg-iwcU0F .link-vljVkQ{color:#4d53e8;font-size:14px;font-weight:400;line-height:16px}.tool-wrap-mFQZuZ .tool-footer-nQuF01 .footer-content-pm_TUV{padding-top:24px}",""]),n.locals={"tool-wrap":"tool-wrap-mFQZuZ",toolWrap:"tool-wrap-mFQZuZ",header:"header-LkSgYi","simple-title":"simple-title-GP82iK",simpleTitle:"simple-title-GP82iK",title:"title-z3P_6q","preview-header":"preview-header-dlLv_0",previewHeader:"preview-header-dlLv_0",breadcrumb:"breadcrumb-LBkKoT",content:"content-pqxJdN","modal-steps":"modal-steps-FUDFnm",modalSteps:"modal-steps-FUDFnm","form-wrap":"form-wrap-Qc2ad_",formWrap:"form-wrap-Qc2ad_","tool-footer":"tool-footer-nQuF01",toolFooter:"tool-footer-nQuF01","step-one":"step-one-hqF28Z",stepOne:"step-one-hqF28Z","error-msg":"error-msg-iwcU0F",errorMsg:"error-msg-iwcU0F",link:"link-vljVkQ","footer-content":"footer-content-pm_TUV",footerContent:"footer-content-pm_TUV"},o.Z=n},928002:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".layout-content-rt1Z05{display:block;overflow:auto}.page-header-p0WE9H{border-bottom:1px solid rgba(29,28,35,.08);flex-direction:row;align-items:center;padding:0 24px 14px;display:flex}.page-header-p0WE9H .page-header-intro-OqbGcL{flex-direction:column;flex:1;height:56px;margin-right:20px;display:flex;overflow:hidden}.page-header-p0WE9H .page-header-intro_center-p_JuGp{justify-content:center;height:40px}.page-header-p0WE9H .page-header-intro_top-iBNEuZ{justify-content:flex-start}.page-header-p0WE9H .page-header-operations-pN8MBh{flex-grow:0;flex-shrink:0}.page-header-p0WE9H .page-header-back-yautuE{margin-right:12px}.page-header-p0WE9H .page-header-back-yautuE svg{width:16px;height:16px;color:var(--semi-color-text-2)!important}.page-header_full-LDpVLi{padding-top:16px;padding-bottom:16px}",""]),n.locals={"layout-content":"layout-content-rt1Z05",layoutContent:"layout-content-rt1Z05","page-header":"page-header-p0WE9H",pageHeader:"page-header-p0WE9H","page-header-intro":"page-header-intro-OqbGcL",pageHeaderIntro:"page-header-intro-OqbGcL","page-header-intro_center":"page-header-intro_center-p_JuGp",pageHeaderIntroCenter:"page-header-intro_center-p_JuGp","page-header-intro_top":"page-header-intro_top-iBNEuZ",pageHeaderIntroTop:"page-header-intro_top-iBNEuZ","page-header-operations":"page-header-operations-pN8MBh",pageHeaderOperations:"page-header-operations-pN8MBh","page-header-back":"page-header-back-yautuE",pageHeaderBack:"page-header-back-yautuE","page-header_full":"page-header_full-LDpVLi",pageHeaderFull:"page-header_full-LDpVLi"},o.Z=n},751238:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,'.page-VEvetO{color:#1c1d23;height:100%}.page-VEvetO .semi-table-row-head{font-size:12px;color:var(--light-usage-text-color-text-1,rgba(28,29,35,.8))!important}.page-VEvetO .semi-steps-item-title-text{font-size:14px}.page-VEvetO .semi-table-row.semi-table-row-expanded:last-child .semi-table-row-cell{border-bottom:1px solid transparent}.layout-content-sMJkhn .header-info-CkwBHj{justify-content:space-between;align-items:center;width:100%;margin-bottom:12px;padding-top:12px;padding-bottom:24px;display:flex;position:relative}.layout-content-sMJkhn .header-info-CkwBHj:after{content:"";background-color:rgba(29,28,35,.08);width:calc(100% + 48px);height:1px;position:absolute;bottom:0;left:-24px}.layout-header-title-oST5H3{font-size:18px;font-weight:600}.icon-disabled-w6xSVY svg{color:rgba(29,28,35,.2)}.icon-disabled-w6xSVY svg path{fill:currentColor}.icon-default-_y9GIE span{color:rgba(29,28,35,.6)}.icon-default-_y9GIE svg path{fill:currentColor;fill-opacity:1}.icon-delete-Lfpin4 .semi-icon{transform:scale(1.08)}',""]),n.locals={page:"page-VEvetO","layout-content":"layout-content-sMJkhn",layoutContent:"layout-content-sMJkhn","header-info":"header-info-CkwBHj",headerInfo:"header-info-CkwBHj","layout-header-title":"layout-header-title-oST5H3",layoutHeaderTitle:"layout-header-title-oST5H3","icon-disabled":"icon-disabled-w6xSVY",iconDisabled:"icon-disabled-w6xSVY","icon-default":"icon-default-_y9GIE",iconDefault:"icon-default-_y9GIE","icon-delete":"icon-delete-Lfpin4",iconDelete:"icon-delete-Lfpin4"},o.Z=n},452058:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,'.tool-wrapper-xjbzvK{color:#1c1d23;height:100%}.tool-wrapper-xjbzvK .layout-header-PTUzvC{padding:24px 24px 12px}.tool-wrapper-xjbzvK .plugin-detail-info-OPsTzx{margin-bottom:36px;padding-bottom:12px;position:relative}.tool-wrapper-xjbzvK .plugin-detail-info-OPsTzx:after{content:"";background-color:rgba(29,28,35,.08);width:calc(100% + 48px);height:1px;position:absolute;bottom:0;left:-24px}.tool-wrapper-xjbzvK .plugin-detail-info-OPsTzx .plugin-detail-title-rHFRkS{max-width:300px;font-size:18px;font-weight:600;line-height:24px}.tool-wrapper-xjbzvK .plugin-detail-info-OPsTzx .plugin-detail-published-QwVxJj{color:rgba(28,29,35,.6);font-size:12px;line-height:16px}.tool-wrapper-xjbzvK .plugin-detail-info-OPsTzx .plugin-detail-avatar-ZD4n7_{border-radius:6px;width:36px;height:36px}.tool-wrapper-xjbzvK .plugin-detail-info-OPsTzx .plugin-detail-desc-vZAxE4{color:rgba(28,29,35,.8);max-width:300px;line-height:16px}.tool-wrapper-xjbzvK .banner-iphPYt{margin-bottom:24px;box-shadow:0 4px 14px rgba(0,0,0,.1),0 0 1px rgba(0,0,0,.3)}.tool-wrapper-xjbzvK .notips-NaGS8p{cursor:pointer;color:#4062ff;margin-left:12px;font-weight:600}.tool-wrapper-xjbzvK .min-width-200-oRGZZJ{min-width:200px}.tool-wrapper-xjbzvK .tool-table-desc-GaYAW_{font-size:12px;line-height:18px}.tool-wrapper-xjbzvK .icon-delete-disabled-botFob path{fill:currentColor}.tool-wrapper-xjbzvK .icon-btn-disable-aQBaUc{color:var(--light-usage-text-color-text-2,rgba(28,29,35,.2))}.tool-wrapper-xjbzvK .debug-btn-disable-o0cMHm{color:#1d1c23}.tool-wrapper-xjbzvK .debug-btn-disable-o0cMHm path{fill:currentColor;fill-opacity:.2}.tool-wrapper-xjbzvK .icon-more-nB1cDH{color:rgba(29,28,35,.6)}.tool-wrapper-xjbzvK .icon-more-nB1cDH path{fill:currentColor}.tool-wrapper-xjbzvK .grey-light-Rirayb{background-color:#f0f0f5;border-radius:4px;height:16px}.tool-wrapper-xjbzvK .edit-plugin-btn-jImJ60{padding:0 8px}.tool-wrapper-xjbzvK .edit-plugin-btn-jImJ60.edit-lt20He .semi-button-content-right{display:none}.tool-wrapper-xjbzvK .circle-point-tq1XJm{border-radius:50%;width:10px;height:10px}.tool-wrapper-xjbzvK .plugin-method-tag-JKRQcZ{height:16px;color:var(--Light-color-violet---violet-6,#6430bf);background:var(--Light-color-violet---violet-1,#e9d6f9);border-radius:4px}.tool-wrapper-xjbzvK .icon-example-disabled-aLvvSl path{fill:currentColor!important}',""]),n.locals={"tool-wrapper":"tool-wrapper-xjbzvK",toolWrapper:"tool-wrapper-xjbzvK","layout-header":"layout-header-PTUzvC",layoutHeader:"layout-header-PTUzvC","plugin-detail-info":"plugin-detail-info-OPsTzx",pluginDetailInfo:"plugin-detail-info-OPsTzx","plugin-detail-title":"plugin-detail-title-rHFRkS",pluginDetailTitle:"plugin-detail-title-rHFRkS","plugin-detail-published":"plugin-detail-published-QwVxJj",pluginDetailPublished:"plugin-detail-published-QwVxJj","plugin-detail-avatar":"plugin-detail-avatar-ZD4n7_",pluginDetailAvatar:"plugin-detail-avatar-ZD4n7_","plugin-detail-desc":"plugin-detail-desc-vZAxE4",pluginDetailDesc:"plugin-detail-desc-vZAxE4",banner:"banner-iphPYt",notips:"notips-NaGS8p","min-width-200":"min-width-200-oRGZZJ",minWidth200:"min-width-200-oRGZZJ","tool-table-desc":"tool-table-desc-GaYAW_",toolTableDesc:"tool-table-desc-GaYAW_","icon-delete-disabled":"icon-delete-disabled-botFob",iconDeleteDisabled:"icon-delete-disabled-botFob","icon-btn-disable":"icon-btn-disable-aQBaUc",iconBtnDisable:"icon-btn-disable-aQBaUc","debug-btn-disable":"debug-btn-disable-o0cMHm",debugBtnDisable:"debug-btn-disable-o0cMHm","icon-more":"icon-more-nB1cDH",iconMore:"icon-more-nB1cDH","grey-light":"grey-light-Rirayb",greyLight:"grey-light-Rirayb","edit-plugin-btn":"edit-plugin-btn-jImJ60",editPluginBtn:"edit-plugin-btn-jImJ60",edit:"edit-lt20He","circle-point":"circle-point-tq1XJm",circlePoint:"circle-point-tq1XJm","plugin-method-tag":"plugin-method-tag-JKRQcZ",pluginMethodTag:"plugin-method-tag-JKRQcZ","icon-example-disabled":"icon-example-disabled-aLvvSl",iconExampleDisabled:"icon-example-disabled-aLvvSl"},o.Z=n},972588:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".layout-header-mTW2Nl{padding:16px 24px 24px}.content-title-POwBgy{max-width:1160px;color:var(--semi-color-text-0);margin:0 auto 16px;font-size:16px;font-weight:600;line-height:32px}.list-container_scroll-djYTQU{width:100%;max-width:1160px;margin-left:auto;margin-right:auto;padding-bottom:10px}.list-container_flexible-wJbt39,.list-container-no-header_flexible-IT21Zp{width:100%;max-width:1160px;margin-left:auto;margin-right:auto}.list-container_flexible-wJbt39{height:calc(100% - 72px)}.list-container-no-header_flexible-IT21Zp{height:100%}.mock-set-intro-title-RsGGBP{width:100%;margin-top:2px;margin-bottom:4px}.mock-set-intro-title-RsGGBP.mock-set-intro-title_full-kzaTz2{margin-top:0;margin-bottom:0}.mock-set-intro-title-RsGGBP .mock-set-intro-name-W4m3Rs{color:var(--semi-color-text-0);text-overflow:ellipsis;white-space:nowrap;font-size:18px;font-weight:600;line-height:32px;overflow:hidden}.mock-set-intro-title-RsGGBP .mock-set-intro-name_full-Egvkdn{font-size:14px;line-height:20px}.mock-set-intro-title-RsGGBP .mock-set-intro-edit-W5G3qu{width:16px;height:16px;color:var(--semi-color-text-2)}.mock-set-intro-title-RsGGBP .mock-set-intro-edit_full-tSGT7v,.mock-set-intro-title-RsGGBP .mock-set-intro-edit_full-tSGT7v svg{width:14px;height:14px}.mock-set-intro-desc_priority-ZGACbv.mock-set-intro-desc-EoC_Wi{width:100%;color:var(--semi-color-text-1);text-overflow:ellipsis;white-space:nowrap;font-size:12px;line-height:22px;overflow:hidden}.mock-set-intro-desc_priority-ZGACbv.mock-set-intro-desc_full-G7zqMi{line-height:16px}.mock-creation-modal-h4C8Z8 .semi-modal-content .semi-modal-body{overflow:unset}.mock-creation-modal-h4C8Z8 .semi-modal-footer .semi-button{margin-left:8px}.mock-creation-card-IVoJdm{height:calc(100% - 24px)}div.mock-creation-modal-editor-hA_Vzc{height:calc(100vh - 316px);max-height:500px}.mock-creation-card-editor-ZQvrCs{height:calc(100% - 120px);margin-bottom:40px}.mock-creation-card-operation-Zw2CVr{text-align:right}.skeleton-cEQH0j .semi-skeleton-image{width:100%;height:100px;margin-bottom:12px}.empty-Bp3ZWl{justify-content:center;height:calc(100% - 68px);padding-bottom:10%}.empty-Bp3ZWl .semi-empty-image svg{width:140px;height:140px}.empty-Bp3ZWl .semi-empty-description{font-weight:600}",""]),n.locals={"layout-header":"layout-header-mTW2Nl",layoutHeader:"layout-header-mTW2Nl","content-title":"content-title-POwBgy",contentTitle:"content-title-POwBgy","list-container_scroll":"list-container_scroll-djYTQU",listContainerScroll:"list-container_scroll-djYTQU","list-container_flexible":"list-container_flexible-wJbt39",listContainerFlexible:"list-container_flexible-wJbt39","list-container-no-header_flexible":"list-container-no-header_flexible-IT21Zp",listContainerNoHeaderFlexible:"list-container-no-header_flexible-IT21Zp","mock-set-intro-title":"mock-set-intro-title-RsGGBP",mockSetIntroTitle:"mock-set-intro-title-RsGGBP","mock-set-intro-title_full":"mock-set-intro-title_full-kzaTz2",mockSetIntroTitleFull:"mock-set-intro-title_full-kzaTz2","mock-set-intro-name":"mock-set-intro-name-W4m3Rs",mockSetIntroName:"mock-set-intro-name-W4m3Rs","mock-set-intro-name_full":"mock-set-intro-name_full-Egvkdn",mockSetIntroNameFull:"mock-set-intro-name_full-Egvkdn","mock-set-intro-edit":"mock-set-intro-edit-W5G3qu",mockSetIntroEdit:"mock-set-intro-edit-W5G3qu","mock-set-intro-edit_full":"mock-set-intro-edit_full-tSGT7v",mockSetIntroEditFull:"mock-set-intro-edit_full-tSGT7v","mock-set-intro-desc_priority":"mock-set-intro-desc_priority-ZGACbv",mockSetIntroDescPriority:"mock-set-intro-desc_priority-ZGACbv","mock-set-intro-desc":"mock-set-intro-desc-EoC_Wi",mockSetIntroDesc:"mock-set-intro-desc-EoC_Wi","mock-set-intro-desc_full":"mock-set-intro-desc_full-G7zqMi",mockSetIntroDescFull:"mock-set-intro-desc_full-G7zqMi","mock-creation-modal":"mock-creation-modal-h4C8Z8",mockCreationModal:"mock-creation-modal-h4C8Z8","mock-creation-card":"mock-creation-card-IVoJdm",mockCreationCard:"mock-creation-card-IVoJdm","mock-creation-modal-editor":"mock-creation-modal-editor-hA_Vzc",mockCreationModalEditor:"mock-creation-modal-editor-hA_Vzc","mock-creation-card-editor":"mock-creation-card-editor-ZQvrCs",mockCreationCardEditor:"mock-creation-card-editor-ZQvrCs","mock-creation-card-operation":"mock-creation-card-operation-Zw2CVr",mockCreationCardOperation:"mock-creation-card-operation-Zw2CVr",skeleton:"skeleton-cEQH0j",empty:"empty-Bp3ZWl"},o.Z=n},428468:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".long-text-Y9pFS8.long-text-tooltip-yeOdtg{color:var(--semi-color-bg-0);word-break:break-word;overflow-wrap:break-word}",""]),n.locals={"long-text":"long-text-Y9pFS8",longText:"long-text-Y9pFS8","long-text-tooltip":"long-text-tooltip-yeOdtg",longTextTooltip:"long-text-tooltip-yeOdtg"},o.Z=n},393288:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".mock-data-content-zMcISM{word-wrap:break-word;border:1px var(--semi-color-border)solid;border-radius:8px;min-height:64px;max-height:500px;overflow:auto}.mock-data-content-zMcISM .semi-tree-option-list-block .semi-tree-option-selected{background-color:transparent}.mock-data-content-zMcISM .semi-tree-option-list-block .semi-tree-option:hover{background-color:transparent}.mock-data-card-operations-W2EzZL{visibility:hidden;background:#f7f7fa;border-radius:8px;padding:4px;position:absolute;top:16px;right:16px;box-shadow:0 4px 8px rgba(0,0,0,.2)}.mock-data-card-ZDq6gF{margin-bottom:16px;position:relative}.mock-data-card-ZDq6gF:hover .mock-data-card-operations-W2EzZL{visibility:visible}.mock-data-card-edit-S1Fvyl,.mock-data-card-delete-sSdiaP{width:16px;height:16px}.mock-data-content-code-aADiqi{height:300px}.mock-data-banner-J4SLDS{width:60%;position:absolute;bottom:16px;left:20%}.mock-data-banner-J4SLDS .semi-banner-icon{align-items:center}.card-item-d8yT_u{display:inline-block;position:relative}.card-item_deleted-vFlgAx{text-decoration:line-through}.card-item-text-aW4zLM{color:#1d1c23;word-wrap:break-word;font-size:14px;font-weight:400;line-height:20px}.card-item-text_required-DV6rMW,.card-item-text_highlighted-II4CqT{color:var(--semi-color-danger)}.card-item-text_primary-t1wXew{font-weight:600}.card-item-text_invalid-XAR6NV{color:var(--semi-color-text-3)}.card-item-text_stretched-Iwi092{flex-grow:1;overflow:hidden}.card-item-text_wrap-jd4z4M{white-space:normal}.card-item-tag-TlAD7m{color:#6b6b75;word-wrap:break-word;background:rgba(46,46,56,.08);border-radius:6px;padding:2px 8px;font-size:12px;font-weight:500;line-height:16px}.card-branches-oDYeZA{pointer-events:none;width:100%;height:100%;position:absolute;top:0;left:8px}.card-branch-v-p9osl8{vertical-align:top;border-left:1px solid transparent;width:13px;height:100%;margin-left:6px;display:inline-block}.card-branch-v_visible-eMYCWE{border-color:#c6c6cd}.card-branch-v_half-WWxYv9{height:10px}.card-branch-h-dnwZKM{vertical-align:top;border:0 solid #c6c6cd;border-width:0 0 1px 1px;border-radius:0 0 0 4px;width:6px;height:15px;margin-left:-13px;display:inline-block}.card-branch-h_long-pPKBpo{width:20px}.card-non-tree-container-ZArpHq{padding:8px 30px}",""]),n.locals={"mock-data-content":"mock-data-content-zMcISM",mockDataContent:"mock-data-content-zMcISM","mock-data-card-operations":"mock-data-card-operations-W2EzZL",mockDataCardOperations:"mock-data-card-operations-W2EzZL","mock-data-card":"mock-data-card-ZDq6gF",mockDataCard:"mock-data-card-ZDq6gF","mock-data-card-edit":"mock-data-card-edit-S1Fvyl",mockDataCardEdit:"mock-data-card-edit-S1Fvyl","mock-data-card-delete":"mock-data-card-delete-sSdiaP",mockDataCardDelete:"mock-data-card-delete-sSdiaP","mock-data-content-code":"mock-data-content-code-aADiqi",mockDataContentCode:"mock-data-content-code-aADiqi","mock-data-banner":"mock-data-banner-J4SLDS",mockDataBanner:"mock-data-banner-J4SLDS","card-item":"card-item-d8yT_u",cardItem:"card-item-d8yT_u","card-item_deleted":"card-item_deleted-vFlgAx",cardItemDeleted:"card-item_deleted-vFlgAx","card-item-text":"card-item-text-aW4zLM",cardItemText:"card-item-text-aW4zLM","card-item-text_required":"card-item-text_required-DV6rMW",cardItemTextRequired:"card-item-text_required-DV6rMW","card-item-text_highlighted":"card-item-text_highlighted-II4CqT",cardItemTextHighlighted:"card-item-text_highlighted-II4CqT","card-item-text_primary":"card-item-text_primary-t1wXew",cardItemTextPrimary:"card-item-text_primary-t1wXew","card-item-text_invalid":"card-item-text_invalid-XAR6NV",cardItemTextInvalid:"card-item-text_invalid-XAR6NV","card-item-text_stretched":"card-item-text_stretched-Iwi092",cardItemTextStretched:"card-item-text_stretched-Iwi092","card-item-text_wrap":"card-item-text_wrap-jd4z4M",cardItemTextWrap:"card-item-text_wrap-jd4z4M","card-item-tag":"card-item-tag-TlAD7m",cardItemTag:"card-item-tag-TlAD7m","card-branches":"card-branches-oDYeZA",cardBranches:"card-branches-oDYeZA","card-branch-v":"card-branch-v-p9osl8",cardBranchV:"card-branch-v-p9osl8","card-branch-v_visible":"card-branch-v_visible-eMYCWE",cardBranchVVisible:"card-branch-v_visible-eMYCWE","card-branch-v_half":"card-branch-v_half-WWxYv9",cardBranchVHalf:"card-branch-v_half-WWxYv9","card-branch-h":"card-branch-h-dnwZKM",cardBranchH:"card-branch-h-dnwZKM","card-branch-h_long":"card-branch-h_long-pPKBpo",cardBranchHLong:"card-branch-h_long-pPKBpo","card-non-tree-container":"card-non-tree-container-ZArpHq",cardNonTreeContainer:"card-non-tree-container-ZArpHq"},o.Z=n},818373:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".checkbox-jdIbki{align-items:center;display:flex}.checkbox-jdIbki .content-Skwa2Z{align-items:center;gap:12px;display:flex}.checkbox-jdIbki .label-fXtvP6{color:var(--Light-usage-text---color-text-0,#1d1c24);font-size:14px;font-style:normal;font-weight:400;line-height:20px}.checkbox-jdIbki .line-k2GZRO{background-color:var(--Light-usage-border---color-border-1,rgba(29,28,37,.12));align-items:center;width:1px;height:16px;margin-left:12px}.checkbox-jdIbki .tip-b0RrOs{width:16px;height:16px;position:absolute;top:2px;left:0}",""]),n.locals={checkbox:"checkbox-jdIbki",content:"content-Skwa2Z",label:"label-fXtvP6",line:"line-k2GZRO",tip:"tip-b0RrOs"},o.Z=n},391875:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".icon-HWVZml>svg{width:16px;height:15px}",""]),n.locals={icon:"icon-HWVZml"},o.Z=n},216599:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".check-box-DZ400_{position:absolute}.form-check-tip-gccXJ5{transform-origin:0;color:var(--semi-color-danger);line-height:16px;display:inline-block;position:absolute;top:4px;left:0;right:0;font-size:12px!important}",""]),n.locals={"check-box":"check-box-DZ400_",checkBox:"check-box-DZ400_","form-check-tip":"form-check-tip-gccXJ5",formCheckTip:"form-check-tip-gccXJ5"},o.Z=n},575694:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,'.process-content-V3jtEI{white-space:break-spaces;background:var(--light-color-white-white,#fff);width:100%;height:100%;font-size:12px;font-weight:400;line-height:15px}.process-content-V3jtEI .debug-result-content-PPl7j5{flex-direction:column;height:100%;display:flex}.process-content-V3jtEI .debug-result-content-PPl7j5 .llm-api-content-eJW9aE{word-break:break-word;height:calc(100% - 40px)}.llm-debug-empty-_v9mkR{height:100%;color:var(--light-usage-text-color-text-3,var(--light-usage-disabled-color-disabled-text,rgba(28,31,35,.35)));justify-content:center;align-items:center;font-size:14px;display:flex}.llm-debug-empty-_v9mkR .llm-debug-empty-content-MR20uI{padding:16px}.error-reason-box-GrLuLI{border-top:1px solid rgba(29,28,36,.08);padding:12px}.mb-content-dLGdvZ{flex-direction:column;height:100%;display:flex}.mb-content-dLGdvZ .auto-hide-last-sibling-br>div{border-top-left-radius:0;border-top-right-radius:0}.mb-content-dLGdvZ .auto-hide-last-sibling-br>div>div:first-child{display:none}.mb-content-dLGdvZ .flow-markdown-body{flex:1}.mb-content-dLGdvZ .mb-header-Adjz56{color:#f7f7fa;background-color:#41414d;justify-content:space-between;align-items:center;height:40px;padding:0 12px;font-size:12px;line-height:40px;display:flex}.mb-content-dLGdvZ .mb-header-Adjz56 .icon-copy-X6T571{cursor:pointer;padding:6px}.mb-content-dLGdvZ .mb-header-Adjz56 .icon-copy-X6T571 svg{width:16px;height:16px}.mb-content-dLGdvZ .mb-header-Adjz56 .icon-copy-X6T571 svg path{fill:#fff;fill-opacity:1}.mb-content-dLGdvZ .mb-main-WzKrz4{background-color:#12131b;border-top-left-radius:0;border-top-right-radius:0;height:100%;display:flex;overflow-y:auto}.mb-content-dLGdvZ .mb-main-WzKrz4 .mb-left-L8YH5L{width:100%}.mb-content-dLGdvZ .mb-main-WzKrz4 .mb-left-L8YH5L.half-width-h9H0Gp{width:50%}.mb-content-dLGdvZ .mb-main-WzKrz4 .mb-right-bqZ0Ik{flex:none;width:50%;position:relative}.mb-content-dLGdvZ .mb-main-WzKrz4 .mb-right-bqZ0Ik:before{content:"";background-color:#565563;width:1px;position:absolute;top:0;bottom:0;left:0}.debug-params-table-DrAahj{flex:1;width:100%;display:flex;overflow:auto}.debug-params-table-DrAahj .empty-X0VDR7{margin-top:90px}.debug-params-table-DrAahj .semi-spin-block.semi-spin{display:flex}.debug-params-table-DrAahj .semi-spin-children{display:flex}.debug-params-table-DrAahj .semi-table-fixed-header{display:flex}.debug-params-table-DrAahj .semi-table-body{padding-bottom:12px;max-height:calc(100% - 40px)!important}.debug-params-table-DrAahj .semi-table-row:has(.disable){display:none}.debug-params-table-DrAahj .semi-table-header{position:relative}.debug-params-table-DrAahj .semi-table-header:after{content:"";background:var(--semi-color-border);width:calc(100% - 32px);height:1px;position:absolute;bottom:0;left:16px}.debug-params-table-DrAahj .semi-table-placeholder{border-bottom:0}.debug-params-table-DrAahj .semi-table-tbody>.semi-table-row>.semi-table-row-cell{vertical-align:top;border-bottom:none;padding:8px 16px}.debug-params-table-DrAahj .semi-table-thead>.semi-table-row>.semi-table-row-head{border-bottom:none;padding-top:9px;padding-bottom:9px}.debug-params-table-DrAahj .semi-table-tbody>.semi-table-row:hover>.semi-table-row-cell{background-color:transparent;background-image:none}.debug-check-header-RJbVVF{flex-shrink:0;justify-content:space-between;align-items:center;width:100%;height:40px;padding:0 16px;display:flex}.debug-check-header-RJbVVF .debug-check-tab-BDFxZ9{align-items:center;gap:12px;font-size:14px;font-style:normal;font-weight:600;line-height:20px;display:flex}.debug-check-header-RJbVVF .debug-check-tab-BDFxZ9 .debug-check-tab-line-kXLbEO{background:var(--Light-usage-border---color-border,rgba(28,29,37,.12));width:1px;height:16px}.debug-check-header-RJbVVF .debug-check-tab-BDFxZ9 .debug-check-tab-item-XT192G{cursor:pointer;color:var(--Light-usage-text---color-text-2,rgba(29,28,36,.6))}.debug-check-header-RJbVVF .debug-check-tab-BDFxZ9 .debug-check-tab-item-active-Zsr8_M{color:var(--Light-color-brand---brand-5,#4c54f0)}.debug-check-header-RJbVVF .icon-K15_kw{cursor:pointer;border:.75px solid rgba(29,28,36,.12);border-radius:6px;justify-content:center;align-items:center;width:32px;height:24px;display:flex}.debug-check-header-RJbVVF .icon-K15_kw svg{width:12px;height:12px;transform:rotate(-90deg)}.debug-check-header-RJbVVF .icon-K15_kw.open-vtnWZZ svg{transform:rotate(90deg)}',""]),n.locals={"process-content":"process-content-V3jtEI",processContent:"process-content-V3jtEI","debug-result-content":"debug-result-content-PPl7j5",debugResultContent:"debug-result-content-PPl7j5","llm-api-content":"llm-api-content-eJW9aE",llmApiContent:"llm-api-content-eJW9aE","llm-debug-empty":"llm-debug-empty-_v9mkR",llmDebugEmpty:"llm-debug-empty-_v9mkR","llm-debug-empty-content":"llm-debug-empty-content-MR20uI",llmDebugEmptyContent:"llm-debug-empty-content-MR20uI","error-reason-box":"error-reason-box-GrLuLI",errorReasonBox:"error-reason-box-GrLuLI","mb-content":"mb-content-dLGdvZ",mbContent:"mb-content-dLGdvZ","mb-header":"mb-header-Adjz56",mbHeader:"mb-header-Adjz56","icon-copy":"icon-copy-X6T571",iconCopy:"icon-copy-X6T571","mb-main":"mb-main-WzKrz4",mbMain:"mb-main-WzKrz4","mb-left":"mb-left-L8YH5L",mbLeft:"mb-left-L8YH5L","half-width":"half-width-h9H0Gp",halfWidth:"half-width-h9H0Gp","mb-right":"mb-right-bqZ0Ik",mbRight:"mb-right-bqZ0Ik","debug-params-table":"debug-params-table-DrAahj",debugParamsTable:"debug-params-table-DrAahj",empty:"empty-X0VDR7","debug-check-header":"debug-check-header-RJbVVF",debugCheckHeader:"debug-check-header-RJbVVF","debug-check-tab":"debug-check-tab-BDFxZ9",debugCheckTab:"debug-check-tab-BDFxZ9","debug-check-tab-line":"debug-check-tab-line-kXLbEO",debugCheckTabLine:"debug-check-tab-line-kXLbEO","debug-check-tab-item":"debug-check-tab-item-XT192G",debugCheckTabItem:"debug-check-tab-item-XT192G","debug-check-tab-item-active":"debug-check-tab-item-active-Zsr8_M",debugCheckTabItemActive:"debug-check-tab-item-active-Zsr8_M",icon:"icon-K15_kw",open:"open-vtnWZZ"},o.Z=n},200601:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,'.create-modal-pzySQx .semi-modal{max-width:1800px}.create-modal-pzySQx .semi-table-row-cell{overflow:hidden}.create-modal-pzySQx.big-modal-qaPc4l .modal-steps-nt2rbx{width:810px;margin:0 auto 24px}.textarea-single-line-QVMjQB .semi-input-textarea-counter{position:absolute;top:3px;right:0}.no-wrap-qLNXMk,.no-wrap-min-width-Q50Srr{white-space:nowrap}.params-layout-R3YGX6{justify-content:space-between;display:flex}.params-tag-_Ge8iA{margin-bottom:18px;padding-top:22px;font-size:18px;font-weight:600}.request-params-alN3YH .semi-table-placeholder{border-bottom:0;padding:1px 12px}.response-params-eA46dX .semi-table-placeholder{border-bottom:0;padding:1px 12px}.request-params-edit-nuqgXc .semi-table-thead .semi-table-row-head:first-child{padding-left:32px!important}.response-params-edit-ND7Hmz .semi-table-thead .semi-table-row-head:first-child{padding-left:32px!important}.request-params-edit-nuqgXc .semi-table-placeholder{border-bottom:0;padding:1px 12px}.response-params-edit-ND7Hmz .semi-table-placeholder{border-bottom:0;padding:1px 12px}.check-box-VG3Qxx{position:absolute}.form-check-tip-yT8BGr{transform-origin:0;color:var(--semi-color-danger);line-height:16px;display:inline-block;position:absolute;top:4px;left:0;right:0;font-size:12px!important}.w110-sD8csv{width:110%}.plugin-icon-error-c3Fxnp{margin-right:4px;font-size:13px;position:relative;top:2px}.plugin-tooltip-error-iJp55a{width:calc(100% - 20px);color:var(--semi-color-danger)!important;font-size:12px!important;line-height:16px!important}.add-params-btn-wrap-F6qzt5{border-top:1px solid var(--semi-color-border);margin:0 24px;padding-bottom:12px}.empty-content-y1oXMo{color:var(--light-usage-text-color-text-2,rgba(28,31,35,.6));text-align:center;margin:36px 0 54px;font-size:14px}.table-style-list-uzSG8W .semi-table-body{padding:12px 0}.table-style-list-uzSG8W .semi-select{border-radius:8px}.table-style-list-uzSG8W .semi-table-row-cell{padding:12px 2px!important}.table-style-list-uzSG8W .semi-table-expand-icon{margin-right:8px}.table-style-list-uzSG8W .semi-table-header{position:relative}.table-style-list-uzSG8W .semi-table-header:after{content:"";background:var(--semi-color-border);width:calc(100% - 48px);height:1px;position:absolute;bottom:0;left:24px}.table-style-list-uzSG8W .semi-table-thead .semi-table-row-head:first-child{padding-left:32px!important}.table-style-list-uzSG8W .semi-table-tbody>.semi-table-row>.semi-table-row-cell{border-bottom-color:transparent}.table-style-list-uzSG8W .semi-table-thead>.semi-table-row>.semi-table-row-head{color:var(--light-usage-text-color-text-1,rgba(28,29,35,.8));background:#f7f7fa;border-bottom:1px solid transparent;padding-left:10px;padding-right:10px;font-size:12px;font-weight:600}.table-style-list-uzSG8W .semi-table-row:hover>.semi-table-row-cell{background:0 0!important;border-bottom:1px solid transparent!important}.table-style-list-uzSG8W .semi-table-tbody>.semi-table-row{cursor:pointer;color:var(--light-usage-text-color-text-2,rgba(28,29,35,.6));background:#f7f7fa;font-size:12px;font-style:normal;font-weight:400}.table-style-list-uzSG8W .semi-table-tbody>.semi-table-row>.semi-table-cell-fixed-left{cursor:pointer;color:var(--light-usage-text-color-text-2,rgba(28,29,35,.6));background:#f7f7fa;font-size:12px;font-style:normal;font-weight:400}.table-style-list-uzSG8W .semi-table-tbody>.semi-table-row>.semi-table-cell-fixed-right{cursor:pointer;color:var(--light-usage-text-color-text-2,rgba(28,29,35,.6));background:#f7f7fa;font-size:12px;font-style:normal;font-weight:400}.table-style-list-uzSG8W .semi-table-thead>.semi-table-row>.semi-table-row-head.semi-table-cell-fixed-left:before{cursor:pointer;color:var(--light-usage-text-color-text-2,rgba(28,29,35,.6));background:#f7f7fa;font-size:12px;font-style:normal;font-weight:400}.table-style-list-uzSG8W .semi-table-thead>.semi-table-row>.semi-table-row-head.semi-table-cell-fixed-right:before{cursor:pointer;color:var(--light-usage-text-color-text-2,rgba(28,29,35,.6));background:#f7f7fa;font-size:12px;font-style:normal;font-weight:400}.table-style-list-uzSG8W .semi-spin-block.semi-spin{height:100%}.table-style-list-uzSG8W .semi-table-row:hover>.semi-table-row-cell:first-child{border-top-left-radius:8px!important;border-bottom-left-radius:8px!important}.table-style-list-uzSG8W .semi-table-row:hover>.semi-table-row-cell:last-child{border-top-right-radius:8px!important;border-bottom-right-radius:8px!important}.table-style-list-uzSG8W .semi-icon-chevron_down{opacity:.6}.table-style-list-uzSG8W.request-params-alN3YH .semi-table-tbody>.semi-table-row>.semi-table-row-cell{padding-left:16px!important}.table-style-list-uzSG8W.response-params-eA46dX .semi-table-tbody>.semi-table-row>.semi-table-row-cell{padding-left:16px!important}.input-modal-m6oSCe .runbtn-Q5S8QY{text-align:right;padding:12px}.input-modal-m6oSCe .semi-modal-footer{margin:0 0 12px}.input-modal-m6oSCe .debug-params-box-oHdlL1 .semi-table-thead>.semi-table-row>.semi-table-row-head{border-bottom-width:1px}.input-modal-m6oSCe .debug-params-box-oHdlL1 .semi-table-tbody>.semi-table-row:hover>.semi-table-row-cell{background:0 0!important;border-bottom:1px solid transparent!important}.debug-check-u44fx1{height:100%;padding-bottom:11px;overflow:hidden}.debug-check-u44fx1 .semi-row{height:100%}.debug-check-u44fx1 .semi-col{height:100%}.debug-check-u44fx1 .main-container-vNgRcA{flex-direction:column;max-width:100vw;height:100%;display:flex}.debug-check-u44fx1 .card-header-Yaroon{margin-bottom:14px;padding:8px 0}.debug-check-u44fx1 .card-title-fsPCJk{color:var(--light-usage-text-color-text-0,#1c1f23);text-overflow:ellipsis;font-size:18px;font-style:normal;font-weight:600}.debug-check-u44fx1 .card-debug-check-caBquS{border:1px solid var(--Light-usage-border---color-border,rgba(29,28,37,.08));background:#fff;border-radius:8px;height:100%;max-height:542px;overflow:auto}.debug-check-u44fx1 .card-debug-check-caBquS .markdown-body{overflow:hidden}.debug-check-u44fx1 .debug-params-box-oHdlL1{border:1px solid rgba(29,28,35,.08);border-radius:8px;flex-direction:column;width:100%;display:flex}.debug-check-u44fx1 .debug-params-box-oHdlL1 .runbtn-Q5S8QY{text-align:right;border-top:1px solid var(--semi-color-border);margin:0 16px;padding:12px 0}.debug-check-u44fx1 .debug-params-box-oHdlL1 .runbtn-Q5S8QY .semi-button.semi-button-loading{color:rgba(29,28,35,.2)}.safe-check-error-gid8KR{color:#f93920}.safe-check-error-gid8KR a{color:#4d53e8}.base-info-form-x1Lkuo .semi-icon-chevron_down{opacity:.6}.base-info-form-x1Lkuo .plugin-url-input-V6CAcc .semi-input-prepend{border:none}.base-info-form-x1Lkuo .plugin-url-prefix-ahens8{max-width:480px}.table-wrapper-RSx6UB{border:1px solid rgba(29,28,35,.08);border-radius:8px}.cascader-dropdown-R0RBcC .semi-cascader-option-label{color:#1d1c23;font-weight:400}',""]),n.locals={"create-modal":"create-modal-pzySQx",createModal:"create-modal-pzySQx","big-modal":"big-modal-qaPc4l",bigModal:"big-modal-qaPc4l","modal-steps":"modal-steps-nt2rbx",modalSteps:"modal-steps-nt2rbx","textarea-single-line":"textarea-single-line-QVMjQB",textareaSingleLine:"textarea-single-line-QVMjQB","no-wrap":"no-wrap-qLNXMk",noWrap:"no-wrap-qLNXMk","no-wrap-min-width":"no-wrap-min-width-Q50Srr",noWrapMinWidth:"no-wrap-min-width-Q50Srr","params-layout":"params-layout-R3YGX6",paramsLayout:"params-layout-R3YGX6","params-tag":"params-tag-_Ge8iA",paramsTag:"params-tag-_Ge8iA","request-params":"request-params-alN3YH",requestParams:"request-params-alN3YH","response-params":"response-params-eA46dX",responseParams:"response-params-eA46dX","request-params-edit":"request-params-edit-nuqgXc",requestParamsEdit:"request-params-edit-nuqgXc","response-params-edit":"response-params-edit-ND7Hmz",responseParamsEdit:"response-params-edit-ND7Hmz","check-box":"check-box-VG3Qxx",checkBox:"check-box-VG3Qxx","form-check-tip":"form-check-tip-yT8BGr",formCheckTip:"form-check-tip-yT8BGr",w110:"w110-sD8csv","plugin-icon-error":"plugin-icon-error-c3Fxnp",pluginIconError:"plugin-icon-error-c3Fxnp","plugin-tooltip-error":"plugin-tooltip-error-iJp55a",pluginTooltipError:"plugin-tooltip-error-iJp55a","add-params-btn-wrap":"add-params-btn-wrap-F6qzt5",addParamsBtnWrap:"add-params-btn-wrap-F6qzt5","empty-content":"empty-content-y1oXMo",emptyContent:"empty-content-y1oXMo","table-style-list":"table-style-list-uzSG8W",tableStyleList:"table-style-list-uzSG8W","input-modal":"input-modal-m6oSCe",inputModal:"input-modal-m6oSCe",runbtn:"runbtn-Q5S8QY","debug-params-box":"debug-params-box-oHdlL1",debugParamsBox:"debug-params-box-oHdlL1","debug-check":"debug-check-u44fx1",debugCheck:"debug-check-u44fx1","main-container":"main-container-vNgRcA",mainContainer:"main-container-vNgRcA","card-header":"card-header-Yaroon",cardHeader:"card-header-Yaroon","card-title":"card-title-fsPCJk",cardTitle:"card-title-fsPCJk","card-debug-check":"card-debug-check-caBquS",cardDebugCheck:"card-debug-check-caBquS","safe-check-error":"safe-check-error-gid8KR",safeCheckError:"safe-check-error-gid8KR","base-info-form":"base-info-form-x1Lkuo",baseInfoForm:"base-info-form-x1Lkuo","plugin-url-input":"plugin-url-input-V6CAcc",pluginUrlInput:"plugin-url-input-V6CAcc","plugin-url-prefix":"plugin-url-prefix-ahens8",pluginUrlPrefix:"plugin-url-prefix-ahens8","table-wrapper":"table-wrapper-RSx6UB",tableWrapper:"table-wrapper-RSx6UB","cascader-dropdown":"cascader-dropdown-R0RBcC",cascaderDropdown:"cascader-dropdown-R0RBcC"},o.Z=n},326046:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".action-input-value-pre-HV53h_{border-right:none;width:115px}.action-input-value-content-JU2Iqe{flex:1}.reference-option-item-F7rLY1{flex-wrap:nowrap;justify-content:space-between;width:200px;display:flex}.reference-option-item-F7rLY1 .reference-option-subtext-iN0fo3{color:rgba(29,28,35,.35)}",""]),n.locals={"action-input-value-pre":"action-input-value-pre-HV53h_",actionInputValuePre:"action-input-value-pre-HV53h_","action-input-value-content":"action-input-value-content-JU2Iqe",actionInputValueContent:"action-input-value-content-JU2Iqe","reference-option-item":"reference-option-item-F7rLY1",referenceOptionItem:"reference-option-item-F7rLY1","reference-option-subtext":"reference-option-subtext-iN0fo3",referenceOptionSubtext:"reference-option-subtext-iN0fo3"},o.Z=n},336940:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".modal-wrapper-w1Xryf .arr-edit-btn-_hSH1U{background-color:#f7f7fa!important;border-color:rgba(29,28,37,.12)!important}.modal-wrapper-w1Xryf .arr-edit-btn-_hSH1U svg path:first-child{stroke:#4d53e8;stroke-opacity:1}.modal-wrapper-w1Xryf .arr-edit-btn-_hSH1U svg path:last-child{fill:#4d53e8;fill-opacity:1}.modal-wrapper-w1Xryf .semi-button-disabled{color:rgba(56,55,67,.2)!important;background-color:rgba(75,74,88,.04)!important}.modal-wrapper-w1Xryf .semi-button-disabled svg path:first-child{stroke:rgba(56,55,67,.2)}.modal-wrapper-w1Xryf .semi-button-disabled svg path:last-child{fill:rgba(56,55,67,.2)}",""]),n.locals={"modal-wrapper":"modal-wrapper-w1Xryf",modalWrapper:"modal-wrapper-w1Xryf","arr-edit-btn":"arr-edit-btn-_hSH1U",arrEditBtn:"arr-edit-btn-_hSH1U"},o.Z=n},565050:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".mockset-create-form-oER7bM{font-size:14px}.mockset-create-form-oER7bM .semi-checkbox-addon{color:var(--semi-color-text-0);font-size:14px;font-weight:600}.mockset-create-form-oER7bM .semi-form-field{padding-top:0}",""]),n.locals={"mockset-create-form":"mockset-create-form-oER7bM",mocksetCreateForm:"mockset-create-form-oER7bM"},o.Z=n},209684:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".mock-tab-container-sZ0Rr9{height:100%;position:relative}.editor-with-header-hgnev7{height:calc(100% - 48px)}.mock-tab-BKzL93{cursor:pointer;width:32px;height:32px;color:var(--semi-color-text-0);text-align:center;border:1px solid var(--semi-color-border);background:#fff;border-radius:4px;margin-right:12px;font-size:14px;font-weight:600;line-height:30px;display:inline-block;position:relative}.mock-tab_active-VPtt9w{color:var(--semi-color-primary);border-color:var(--semi-color-primary);background:#fff}.mock-tab-error-r2ammJ{position:absolute;top:-7px;right:-7px}.mock-tab-error-r2ammJ svg{width:16px;height:16px}.mock-tab-header-I1dB4r{height:32px;margin-bottom:12px}.mock-tab-mask-gEk5gN{background-color:rgba(var(--semi-white),.8);border:1px solid transparent;border-radius:8px;flex-direction:column;justify-content:center;align-items:center;width:100%;height:calc(100% - 48px);display:flex;position:absolute;bottom:0;left:0}.mock-tab-mask-gEk5gN span{color:var(--semi-color-text-3);margin-top:8px;font-size:16px;line-height:24px}.mock-tab-panels-SMuVLk{width:100%;height:100%;position:relative}.mock-tab-panel_visible-v6SO_P{visibility:visible}.mock-tab-panel_invisible-_nhZK_{visibility:hidden}.mock-tab-panel_absolute-d2X2gL{width:100%;position:absolute;top:0;left:0}",""]),n.locals={"mock-tab-container":"mock-tab-container-sZ0Rr9",mockTabContainer:"mock-tab-container-sZ0Rr9","editor-with-header":"editor-with-header-hgnev7",editorWithHeader:"editor-with-header-hgnev7","mock-tab":"mock-tab-BKzL93",mockTab:"mock-tab-BKzL93","mock-tab_active":"mock-tab_active-VPtt9w",mockTabActive:"mock-tab_active-VPtt9w","mock-tab-error":"mock-tab-error-r2ammJ",mockTabError:"mock-tab-error-r2ammJ","mock-tab-header":"mock-tab-header-I1dB4r",mockTabHeader:"mock-tab-header-I1dB4r","mock-tab-mask":"mock-tab-mask-gEk5gN",mockTabMask:"mock-tab-mask-gEk5gN","mock-tab-panels":"mock-tab-panels-SMuVLk",mockTabPanels:"mock-tab-panels-SMuVLk","mock-tab-panel_visible":"mock-tab-panel_visible-v6SO_P",mockTabPanelVisible:"mock-tab-panel_visible-v6SO_P","mock-tab-panel_invisible":"mock-tab-panel_invisible-_nhZK_",mockTabPanelInvisible:"mock-tab-panel_invisible-_nhZK_","mock-tab-panel_absolute":"mock-tab-panel_absolute-d2X2gL",mockTabPanelAbsolute:"mock-tab-panel_absolute-d2X2gL"},o.Z=n},792795:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,".editor-container-I29hCX{height:100%;position:relative}.editor-vfOmJV{background-color:#fff;border:1px solid #eee;border-radius:8px;height:100%;padding:16px 0}.editor-vfOmJV .monaco-editor .scroll-decoration{box-shadow:unset}.editor-vfOmJV .semi-skeleton-image{width:100%;height:100px;margin-bottom:12px}.editor-container_disabled-wa3_6M .monaco-editor.no-user-select .view-lines{cursor:default}.editor_hidden-zAnGjM{z-index:-1;opacity:0;position:relative}.skeleton-DuSjuT{z-index:10;width:100%;height:100%;position:absolute;top:0;left:0}.skeleton-DuSjuT .semi-skeleton-image{width:100%;height:100%;margin-bottom:12px}",""]),n.locals={"editor-container":"editor-container-I29hCX",editorContainer:"editor-container-I29hCX",editor:"editor-vfOmJV","editor-container_disabled":"editor-container_disabled-wa3_6M",editorContainerDisabled:"editor-container_disabled-wa3_6M",editor_hidden:"editor_hidden-zAnGjM",editorHidden:"editor_hidden-zAnGjM",skeleton:"skeleton-DuSjuT"},o.Z=n},937373:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a),l=t(964824),d=t.n(l),c=new URL(t(424013),t.b),s=new URL(t(516254),t.b),u=new URL(t(260827),t.b),g=new URL(t(743424),t.b),p=n()(r()),m=d()(c),h=d()(s),b=d()(u),v=d()(g);p.push([e.id,`.light-wlgpfI .monaco-editor{--vscode-foreground:#616161;--vscode-disabledForeground:rgba(97,97,97,.5);--vscode-errorForeground:#a1260d;--vscode-descriptionForeground:#717171;--vscode-icon-foreground:#424242;--vscode-focusBorder:#0090f1;--vscode-textSeparator-foreground:rgba(0,0,0,.18);--vscode-textLink-foreground:#006ab1;--vscode-textLink-activeForeground:#006ab1;--vscode-textPreformat-foreground:#a31515;--vscode-textBlockQuote-background:rgba(127,127,127,.1);--vscode-textBlockQuote-border:rgba(0,122,204,.5);--vscode-textCodeBlock-background:rgba(220,220,220,.4);--vscode-widget-shadow:rgba(0,0,0,.16);--vscode-input-background:#fff;--vscode-input-foreground:#616161;--vscode-inputOption-activeBorder:#007acc;--vscode-inputOption-hoverBackground:rgba(184,184,184,.31);--vscode-inputOption-activeBackground:rgba(0,144,241,.2);--vscode-inputOption-activeForeground:#000;--vscode-input-placeholderForeground:rgba(97,97,97,.5);--vscode-inputValidation-infoBackground:#d6ecf2;--vscode-inputValidation-infoBorder:#007acc;--vscode-inputValidation-warningBackground:#f6f5d2;--vscode-inputValidation-warningBorder:#b89500;--vscode-inputValidation-errorBackground:#f2dede;--vscode-inputValidation-errorBorder:#be1100;--vscode-dropdown-background:#fff;--vscode-dropdown-foreground:#616161;--vscode-dropdown-border:#cecece;--vscode-button-foreground:#fff;--vscode-button-separator:rgba(255,255,255,.4);--vscode-button-background:#007acc;--vscode-button-hoverBackground:#0062a3;--vscode-button-secondaryForeground:#fff;--vscode-button-secondaryBackground:#5f6a79;--vscode-button-secondaryHoverBackground:#4c5561;--vscode-badge-background:#c4c4c4;--vscode-badge-foreground:#333;--vscode-scrollbar-shadow:#ddd;--vscode-scrollbarSlider-background:rgba(100,100,100,.4);--vscode-scrollbarSlider-hoverBackground:rgba(100,100,100,.7);--vscode-scrollbarSlider-activeBackground:rgba(0,0,0,.6);--vscode-progressBar-background:#0e70c0;--vscode-editorError-foreground:#e51400;--vscode-editorWarning-foreground:#bf8803;--vscode-editorInfo-foreground:#1a85ff;--vscode-editorHint-foreground:#6c6c6c;--vscode-sash-hoverBorder:#0090f1;--vscode-editor-background:#fffffe;--vscode-editor-foreground:#000;--vscode-editorStickyScroll-background:#fffffe;--vscode-editorStickyScrollHover-background:#f0f0f0;--vscode-editorWidget-background:#f3f3f3;--vscode-editorWidget-foreground:#616161;--vscode-editorWidget-border:#c8c8c8;--vscode-quickInput-background:#f3f3f3;--vscode-quickInput-foreground:#616161;--vscode-quickInputTitle-background:rgba(0,0,0,.06);--vscode-pickerGroup-foreground:#0066bf;--vscode-pickerGroup-border:#cccedb;--vscode-keybindingLabel-background:rgba(221,221,221,.4);--vscode-keybindingLabel-foreground:#555;--vscode-keybindingLabel-border:rgba(204,204,204,.4);--vscode-keybindingLabel-bottomBorder:rgba(187,187,187,.4);--vscode-editor-selectionBackground:#add6ff;--vscode-editor-inactiveSelectionBackground:#e5ebf1;--vscode-editor-selectionHighlightBackground:rgba(173,214,255,.3);--vscode-editor-findMatchBackground:#a8ac94;--vscode-editor-findMatchHighlightBackground:rgba(234,92,0,.33);--vscode-editor-findRangeHighlightBackground:rgba(180,180,180,.3);--vscode-searchEditor-findMatchBackground:rgba(234,92,0,.22);--vscode-search-resultsInfoForeground:#616161;--vscode-editor-hoverHighlightBackground:rgba(173,214,255,.15);--vscode-editorHoverWidget-background:#f3f3f3;--vscode-editorHoverWidget-foreground:#616161;--vscode-editorHoverWidget-border:#c8c8c8;--vscode-editorHoverWidget-statusBarBackground:#e7e7e7;--vscode-editorLink-activeForeground:#00f;--vscode-editorInlayHint-foreground:#969696;--vscode-editorInlayHint-background:rgba(196,196,196,.1);--vscode-editorInlayHint-typeForeground:#969696;--vscode-editorInlayHint-typeBackground:rgba(196,196,196,.1);--vscode-editorInlayHint-parameterForeground:#969696;--vscode-editorInlayHint-parameterBackground:rgba(196,196,196,.1);--vscode-editorLightBulb-foreground:#ddb100;--vscode-editorLightBulbAutoFix-foreground:#007acc;--vscode-diffEditor-insertedTextBackground:rgba(156,204,44,.25);--vscode-diffEditor-removedTextBackground:rgba(255,0,0,.2);--vscode-diffEditor-insertedLineBackground:rgba(155,185,85,.2);--vscode-diffEditor-removedLineBackground:rgba(255,0,0,.2);--vscode-diffEditor-diagonalFill:rgba(34,34,34,.2);--vscode-diffEditor-unchangedRegionBackground:#e4e4e4;--vscode-diffEditor-unchangedRegionForeground:#4d4c4c;--vscode-diffEditor-unchangedCodeBackground:rgba(184,184,184,.16);--vscode-list-focusOutline:#0090f1;--vscode-list-activeSelectionBackground:#0060c0;--vscode-list-activeSelectionForeground:#fff;--vscode-list-inactiveSelectionBackground:#e4e6f1;--vscode-list-hoverBackground:#f0f0f0;--vscode-list-dropBackground:#d6ebff;--vscode-list-highlightForeground:#0066bf;--vscode-list-focusHighlightForeground:#bbe7ff;--vscode-list-invalidItemForeground:#b89500;--vscode-list-errorForeground:#b01011;--vscode-list-warningForeground:#855f00;--vscode-listFilterWidget-background:#f3f3f3;--vscode-listFilterWidget-outline:transparent;--vscode-listFilterWidget-noMatchesOutline:#be1100;--vscode-listFilterWidget-shadow:rgba(0,0,0,.16);--vscode-list-filterMatchBackground:rgba(234,92,0,.33);--vscode-tree-indentGuidesStroke:#a9a9a9;--vscode-tree-inactiveIndentGuidesStroke:rgba(169,169,169,.4);--vscode-tree-tableColumnsBorder:rgba(97,97,97,.13);--vscode-tree-tableOddRowsBackground:rgba(97,97,97,.04);--vscode-list-deemphasizedForeground:#8e8e90;--vscode-checkbox-background:#fff;--vscode-checkbox-selectBackground:#f3f3f3;--vscode-checkbox-foreground:#616161;--vscode-checkbox-border:#cecece;--vscode-checkbox-selectBorder:#424242;--vscode-quickInputList-focusForeground:#fff;--vscode-quickInputList-focusBackground:#0060c0;--vscode-menu-foreground:#616161;--vscode-menu-background:#fff;--vscode-menu-selectionForeground:#fff;--vscode-menu-selectionBackground:#0060c0;--vscode-menu-separatorBackground:#d4d4d4;--vscode-toolbar-hoverBackground:rgba(184,184,184,.31);--vscode-toolbar-activeBackground:rgba(166,166,166,.31);--vscode-editor-snippetTabstopHighlightBackground:rgba(10,50,100,.2);--vscode-editor-snippetFinalTabstopHighlightBorder:rgba(10,50,100,.5);--vscode-breadcrumb-foreground:rgba(97,97,97,.8);--vscode-breadcrumb-background:#fffffe;--vscode-breadcrumb-focusForeground:#4e4e4e;--vscode-breadcrumb-activeSelectionForeground:#4e4e4e;--vscode-breadcrumbPicker-background:#f3f3f3;--vscode-merge-currentHeaderBackground:rgba(64,200,174,.5);--vscode-merge-currentContentBackground:rgba(64,200,174,.2);--vscode-merge-incomingHeaderBackground:rgba(64,166,255,.5);--vscode-merge-incomingContentBackground:rgba(64,166,255,.2);--vscode-merge-commonHeaderBackground:rgba(96,96,96,.4);--vscode-merge-commonContentBackground:rgba(96,96,96,.16);--vscode-editorOverviewRuler-currentContentForeground:rgba(64,200,174,.5);--vscode-editorOverviewRuler-incomingContentForeground:rgba(64,166,255,.5);--vscode-editorOverviewRuler-commonContentForeground:rgba(96,96,96,.4);--vscode-editorOverviewRuler-findMatchForeground:rgba(209,134,22,.49);--vscode-editorOverviewRuler-selectionHighlightForeground:rgba(160,160,160,.8);--vscode-minimap-findMatchHighlight:#d18616;--vscode-minimap-selectionOccurrenceHighlight:#c9c9c9;--vscode-minimap-selectionHighlight:#add6ff;--vscode-minimap-errorHighlight:rgba(255,18,18,.7);--vscode-minimap-warningHighlight:#bf8803;--vscode-minimap-foregroundOpacity:#000;--vscode-minimapSlider-background:rgba(100,100,100,.2);--vscode-minimapSlider-hoverBackground:rgba(100,100,100,.35);--vscode-minimapSlider-activeBackground:rgba(0,0,0,.3);--vscode-problemsErrorIcon-foreground:#e51400;--vscode-problemsWarningIcon-foreground:#bf8803;--vscode-problemsInfoIcon-foreground:#1a85ff;--vscode-charts-foreground:#616161;--vscode-charts-lines:rgba(97,97,97,.5);--vscode-charts-red:#e51400;--vscode-charts-blue:#1a85ff;--vscode-charts-yellow:#bf8803;--vscode-charts-orange:#d18616;--vscode-charts-green:#388a34;--vscode-charts-purple:#652d90;--vscode-diffEditor-move-border:rgba(139,139,139,.61);--vscode-diffEditor-moveActive-border:orange;--vscode-symbolIcon-arrayForeground:#616161;--vscode-symbolIcon-booleanForeground:#616161;--vscode-symbolIcon-classForeground:#d67e00;--vscode-symbolIcon-colorForeground:#616161;--vscode-symbolIcon-constantForeground:#616161;--vscode-symbolIcon-constructorForeground:#652d90;--vscode-symbolIcon-enumeratorForeground:#d67e00;--vscode-symbolIcon-enumeratorMemberForeground:#007acc;--vscode-symbolIcon-eventForeground:#d67e00;--vscode-symbolIcon-fieldForeground:#007acc;--vscode-symbolIcon-fileForeground:#616161;--vscode-symbolIcon-folderForeground:#616161;--vscode-symbolIcon-functionForeground:#652d90;--vscode-symbolIcon-interfaceForeground:#007acc;--vscode-symbolIcon-keyForeground:#616161;--vscode-symbolIcon-keywordForeground:#616161;--vscode-symbolIcon-methodForeground:#652d90;--vscode-symbolIcon-moduleForeground:#616161;--vscode-symbolIcon-namespaceForeground:#616161;--vscode-symbolIcon-nullForeground:#616161;--vscode-symbolIcon-numberForeground:#616161;--vscode-symbolIcon-objectForeground:#616161;--vscode-symbolIcon-operatorForeground:#616161;--vscode-symbolIcon-packageForeground:#616161;--vscode-symbolIcon-propertyForeground:#616161;--vscode-symbolIcon-referenceForeground:#616161;--vscode-symbolIcon-snippetForeground:#616161;--vscode-symbolIcon-stringForeground:#616161;--vscode-symbolIcon-structForeground:#616161;--vscode-symbolIcon-textForeground:#616161;--vscode-symbolIcon-typeParameterForeground:#616161;--vscode-symbolIcon-unitForeground:#616161;--vscode-symbolIcon-variableForeground:#007acc;--vscode-actionBar-toggledBackground:rgba(0,144,241,.2);--vscode-editor-lineHighlightBorder:#eee;--vscode-editor-rangeHighlightBackground:rgba(253,255,0,.2);--vscode-editor-symbolHighlightBackground:rgba(234,92,0,.33);--vscode-editorCursor-foreground:#000;--vscode-editorWhitespace-foreground:rgba(51,51,51,.2);--vscode-editorLineNumber-foreground:#237893;--vscode-editorIndentGuide-background:rgba(51,51,51,.2);--vscode-editorIndentGuide-activeBackground:rgba(51,51,51,.2);--vscode-editorIndentGuide-background1:#d3d3d3;--vscode-editorIndentGuide-background2:transparent;--vscode-editorIndentGuide-background3:transparent;--vscode-editorIndentGuide-background4:transparent;--vscode-editorIndentGuide-background5:transparent;--vscode-editorIndentGuide-background6:transparent;--vscode-editorIndentGuide-activeBackground1:#939393;--vscode-editorIndentGuide-activeBackground2:transparent;--vscode-editorIndentGuide-activeBackground3:transparent;--vscode-editorIndentGuide-activeBackground4:transparent;--vscode-editorIndentGuide-activeBackground5:transparent;--vscode-editorIndentGuide-activeBackground6:transparent;--vscode-editorActiveLineNumber-foreground:#0b216f;--vscode-editorLineNumber-activeForeground:#0b216f;--vscode-editorRuler-foreground:#d3d3d3;--vscode-editorCodeLens-foreground:#919191;--vscode-editorBracketMatch-background:rgba(0,100,0,.1);--vscode-editorBracketMatch-border:#b9b9b9;--vscode-editorOverviewRuler-border:rgba(127,127,127,.3);--vscode-editorGutter-background:#fffffe;--vscode-editorUnnecessaryCode-opacity:rgba(0,0,0,.47);--vscode-editorGhostText-foreground:rgba(0,0,0,.47);--vscode-editorOverviewRuler-rangeHighlightForeground:rgba(0,122,204,.6);--vscode-editorOverviewRuler-errorForeground:rgba(255,18,18,.7);--vscode-editorOverviewRuler-warningForeground:#bf8803;--vscode-editorOverviewRuler-infoForeground:#1a85ff;--vscode-editorBracketHighlight-foreground1:#0431fa;--vscode-editorBracketHighlight-foreground2:#319331;--vscode-editorBracketHighlight-foreground3:#7b3814;--vscode-editorBracketHighlight-foreground4:transparent;--vscode-editorBracketHighlight-foreground5:transparent;--vscode-editorBracketHighlight-foreground6:transparent;--vscode-editorBracketHighlight-unexpectedBracket-foreground:rgba(255,18,18,.8);--vscode-editorBracketPairGuide-background1:transparent;--vscode-editorBracketPairGuide-background2:transparent;--vscode-editorBracketPairGuide-background3:transparent;--vscode-editorBracketPairGuide-background4:transparent;--vscode-editorBracketPairGuide-background5:transparent;--vscode-editorBracketPairGuide-background6:transparent;--vscode-editorBracketPairGuide-activeBackground1:transparent;--vscode-editorBracketPairGuide-activeBackground2:transparent;--vscode-editorBracketPairGuide-activeBackground3:transparent;--vscode-editorBracketPairGuide-activeBackground4:transparent;--vscode-editorBracketPairGuide-activeBackground5:transparent;--vscode-editorBracketPairGuide-activeBackground6:transparent;--vscode-editorUnicodeHighlight-border:#cea33d;--vscode-editorUnicodeHighlight-background:rgba(206,163,61,.08);--vscode-editorOverviewRuler-bracketMatchForeground:#a0a0a0;--vscode-editor-linkedEditingBackground:rgba(255,0,0,.3);--vscode-editor-wordHighlightBackground:rgba(87,87,87,.25);--vscode-editor-wordHighlightStrongBackground:rgba(14,99,156,.25);--vscode-editor-wordHighlightTextBackground:rgba(87,87,87,.25);--vscode-editorOverviewRuler-wordHighlightForeground:rgba(160,160,160,.8);--vscode-editorOverviewRuler-wordHighlightStrongForeground:rgba(192,160,192,.8);--vscode-editorOverviewRuler-wordHighlightTextForeground:rgba(160,160,160,.8);--vscode-peekViewTitle-background:#f3f3f3;--vscode-peekViewTitleLabel-foreground:#000;--vscode-peekViewTitleDescription-foreground:#616161;--vscode-peekView-border:#1a85ff;--vscode-peekViewResult-background:#f3f3f3;--vscode-peekViewResult-lineForeground:#646465;--vscode-peekViewResult-fileForeground:#1e1e1e;--vscode-peekViewResult-selectionBackground:rgba(51,153,255,.2);--vscode-peekViewResult-selectionForeground:#6c6c6c;--vscode-peekViewEditor-background:#f2f8fc;--vscode-peekViewEditorGutter-background:#f2f8fc;--vscode-peekViewEditorStickyScroll-background:#f2f8fc;--vscode-peekViewResult-matchHighlightBackground:rgba(234,92,0,.3);--vscode-peekViewEditor-matchHighlightBackground:rgba(245,216,2,.87);--vscode-editorMarkerNavigationError-background:#e51400;--vscode-editorMarkerNavigationError-headerBackground:rgba(229,20,0,.1);--vscode-editorMarkerNavigationWarning-background:#bf8803;--vscode-editorMarkerNavigationWarning-headerBackground:rgba(191,136,3,.1);--vscode-editorMarkerNavigationInfo-background:#1a85ff;--vscode-editorMarkerNavigationInfo-headerBackground:rgba(26,133,255,.1);--vscode-editorMarkerNavigation-background:#fffffe;--vscode-editorHoverWidget-highlightForeground:#0066bf;--vscode-editorSuggestWidget-background:#f3f3f3;--vscode-editorSuggestWidget-border:#c8c8c8;--vscode-editorSuggestWidget-foreground:#000;--vscode-editorSuggestWidget-selectedForeground:#fff;--vscode-editorSuggestWidget-selectedBackground:#0060c0;--vscode-editorSuggestWidget-highlightForeground:#0066bf;--vscode-editorSuggestWidget-focusHighlightForeground:#bbe7ff;--vscode-editorSuggestWidgetStatus-foreground:rgba(0,0,0,.5);--vscode-editor-foldBackground:rgba(173,214,255,.3);--vscode-editorGutter-foldingControlForeground:#424242}.light-wlgpfI .monaco-diff-editor{--vscode-foreground:#616161;--vscode-disabledForeground:rgba(97,97,97,.5);--vscode-errorForeground:#a1260d;--vscode-descriptionForeground:#717171;--vscode-icon-foreground:#424242;--vscode-focusBorder:#0090f1;--vscode-textSeparator-foreground:rgba(0,0,0,.18);--vscode-textLink-foreground:#006ab1;--vscode-textLink-activeForeground:#006ab1;--vscode-textPreformat-foreground:#a31515;--vscode-textBlockQuote-background:rgba(127,127,127,.1);--vscode-textBlockQuote-border:rgba(0,122,204,.5);--vscode-textCodeBlock-background:rgba(220,220,220,.4);--vscode-widget-shadow:rgba(0,0,0,.16);--vscode-input-background:#fff;--vscode-input-foreground:#616161;--vscode-inputOption-activeBorder:#007acc;--vscode-inputOption-hoverBackground:rgba(184,184,184,.31);--vscode-inputOption-activeBackground:rgba(0,144,241,.2);--vscode-inputOption-activeForeground:#000;--vscode-input-placeholderForeground:rgba(97,97,97,.5);--vscode-inputValidation-infoBackground:#d6ecf2;--vscode-inputValidation-infoBorder:#007acc;--vscode-inputValidation-warningBackground:#f6f5d2;--vscode-inputValidation-warningBorder:#b89500;--vscode-inputValidation-errorBackground:#f2dede;--vscode-inputValidation-errorBorder:#be1100;--vscode-dropdown-background:#fff;--vscode-dropdown-foreground:#616161;--vscode-dropdown-border:#cecece;--vscode-button-foreground:#fff;--vscode-button-separator:rgba(255,255,255,.4);--vscode-button-background:#007acc;--vscode-button-hoverBackground:#0062a3;--vscode-button-secondaryForeground:#fff;--vscode-button-secondaryBackground:#5f6a79;--vscode-button-secondaryHoverBackground:#4c5561;--vscode-badge-background:#c4c4c4;--vscode-badge-foreground:#333;--vscode-scrollbar-shadow:#ddd;--vscode-scrollbarSlider-background:rgba(100,100,100,.4);--vscode-scrollbarSlider-hoverBackground:rgba(100,100,100,.7);--vscode-scrollbarSlider-activeBackground:rgba(0,0,0,.6);--vscode-progressBar-background:#0e70c0;--vscode-editorError-foreground:#e51400;--vscode-editorWarning-foreground:#bf8803;--vscode-editorInfo-foreground:#1a85ff;--vscode-editorHint-foreground:#6c6c6c;--vscode-sash-hoverBorder:#0090f1;--vscode-editor-background:#fffffe;--vscode-editor-foreground:#000;--vscode-editorStickyScroll-background:#fffffe;--vscode-editorStickyScrollHover-background:#f0f0f0;--vscode-editorWidget-background:#f3f3f3;--vscode-editorWidget-foreground:#616161;--vscode-editorWidget-border:#c8c8c8;--vscode-quickInput-background:#f3f3f3;--vscode-quickInput-foreground:#616161;--vscode-quickInputTitle-background:rgba(0,0,0,.06);--vscode-pickerGroup-foreground:#0066bf;--vscode-pickerGroup-border:#cccedb;--vscode-keybindingLabel-background:rgba(221,221,221,.4);--vscode-keybindingLabel-foreground:#555;--vscode-keybindingLabel-border:rgba(204,204,204,.4);--vscode-keybindingLabel-bottomBorder:rgba(187,187,187,.4);--vscode-editor-selectionBackground:#add6ff;--vscode-editor-inactiveSelectionBackground:#e5ebf1;--vscode-editor-selectionHighlightBackground:rgba(173,214,255,.3);--vscode-editor-findMatchBackground:#a8ac94;--vscode-editor-findMatchHighlightBackground:rgba(234,92,0,.33);--vscode-editor-findRangeHighlightBackground:rgba(180,180,180,.3);--vscode-searchEditor-findMatchBackground:rgba(234,92,0,.22);--vscode-search-resultsInfoForeground:#616161;--vscode-editor-hoverHighlightBackground:rgba(173,214,255,.15);--vscode-editorHoverWidget-background:#f3f3f3;--vscode-editorHoverWidget-foreground:#616161;--vscode-editorHoverWidget-border:#c8c8c8;--vscode-editorHoverWidget-statusBarBackground:#e7e7e7;--vscode-editorLink-activeForeground:#00f;--vscode-editorInlayHint-foreground:#969696;--vscode-editorInlayHint-background:rgba(196,196,196,.1);--vscode-editorInlayHint-typeForeground:#969696;--vscode-editorInlayHint-typeBackground:rgba(196,196,196,.1);--vscode-editorInlayHint-parameterForeground:#969696;--vscode-editorInlayHint-parameterBackground:rgba(196,196,196,.1);--vscode-editorLightBulb-foreground:#ddb100;--vscode-editorLightBulbAutoFix-foreground:#007acc;--vscode-diffEditor-insertedTextBackground:rgba(156,204,44,.25);--vscode-diffEditor-removedTextBackground:rgba(255,0,0,.2);--vscode-diffEditor-insertedLineBackground:rgba(155,185,85,.2);--vscode-diffEditor-removedLineBackground:rgba(255,0,0,.2);--vscode-diffEditor-diagonalFill:rgba(34,34,34,.2);--vscode-diffEditor-unchangedRegionBackground:#e4e4e4;--vscode-diffEditor-unchangedRegionForeground:#4d4c4c;--vscode-diffEditor-unchangedCodeBackground:rgba(184,184,184,.16);--vscode-list-focusOutline:#0090f1;--vscode-list-activeSelectionBackground:#0060c0;--vscode-list-activeSelectionForeground:#fff;--vscode-list-inactiveSelectionBackground:#e4e6f1;--vscode-list-hoverBackground:#f0f0f0;--vscode-list-dropBackground:#d6ebff;--vscode-list-highlightForeground:#0066bf;--vscode-list-focusHighlightForeground:#bbe7ff;--vscode-list-invalidItemForeground:#b89500;--vscode-list-errorForeground:#b01011;--vscode-list-warningForeground:#855f00;--vscode-listFilterWidget-background:#f3f3f3;--vscode-listFilterWidget-outline:transparent;--vscode-listFilterWidget-noMatchesOutline:#be1100;--vscode-listFilterWidget-shadow:rgba(0,0,0,.16);--vscode-list-filterMatchBackground:rgba(234,92,0,.33);--vscode-tree-indentGuidesStroke:#a9a9a9;--vscode-tree-inactiveIndentGuidesStroke:rgba(169,169,169,.4);--vscode-tree-tableColumnsBorder:rgba(97,97,97,.13);--vscode-tree-tableOddRowsBackground:rgba(97,97,97,.04);--vscode-list-deemphasizedForeground:#8e8e90;--vscode-checkbox-background:#fff;--vscode-checkbox-selectBackground:#f3f3f3;--vscode-checkbox-foreground:#616161;--vscode-checkbox-border:#cecece;--vscode-checkbox-selectBorder:#424242;--vscode-quickInputList-focusForeground:#fff;--vscode-quickInputList-focusBackground:#0060c0;--vscode-menu-foreground:#616161;--vscode-menu-background:#fff;--vscode-menu-selectionForeground:#fff;--vscode-menu-selectionBackground:#0060c0;--vscode-menu-separatorBackground:#d4d4d4;--vscode-toolbar-hoverBackground:rgba(184,184,184,.31);--vscode-toolbar-activeBackground:rgba(166,166,166,.31);--vscode-editor-snippetTabstopHighlightBackground:rgba(10,50,100,.2);--vscode-editor-snippetFinalTabstopHighlightBorder:rgba(10,50,100,.5);--vscode-breadcrumb-foreground:rgba(97,97,97,.8);--vscode-breadcrumb-background:#fffffe;--vscode-breadcrumb-focusForeground:#4e4e4e;--vscode-breadcrumb-activeSelectionForeground:#4e4e4e;--vscode-breadcrumbPicker-background:#f3f3f3;--vscode-merge-currentHeaderBackground:rgba(64,200,174,.5);--vscode-merge-currentContentBackground:rgba(64,200,174,.2);--vscode-merge-incomingHeaderBackground:rgba(64,166,255,.5);--vscode-merge-incomingContentBackground:rgba(64,166,255,.2);--vscode-merge-commonHeaderBackground:rgba(96,96,96,.4);--vscode-merge-commonContentBackground:rgba(96,96,96,.16);--vscode-editorOverviewRuler-currentContentForeground:rgba(64,200,174,.5);--vscode-editorOverviewRuler-incomingContentForeground:rgba(64,166,255,.5);--vscode-editorOverviewRuler-commonContentForeground:rgba(96,96,96,.4);--vscode-editorOverviewRuler-findMatchForeground:rgba(209,134,22,.49);--vscode-editorOverviewRuler-selectionHighlightForeground:rgba(160,160,160,.8);--vscode-minimap-findMatchHighlight:#d18616;--vscode-minimap-selectionOccurrenceHighlight:#c9c9c9;--vscode-minimap-selectionHighlight:#add6ff;--vscode-minimap-errorHighlight:rgba(255,18,18,.7);--vscode-minimap-warningHighlight:#bf8803;--vscode-minimap-foregroundOpacity:#000;--vscode-minimapSlider-background:rgba(100,100,100,.2);--vscode-minimapSlider-hoverBackground:rgba(100,100,100,.35);--vscode-minimapSlider-activeBackground:rgba(0,0,0,.3);--vscode-problemsErrorIcon-foreground:#e51400;--vscode-problemsWarningIcon-foreground:#bf8803;--vscode-problemsInfoIcon-foreground:#1a85ff;--vscode-charts-foreground:#616161;--vscode-charts-lines:rgba(97,97,97,.5);--vscode-charts-red:#e51400;--vscode-charts-blue:#1a85ff;--vscode-charts-yellow:#bf8803;--vscode-charts-orange:#d18616;--vscode-charts-green:#388a34;--vscode-charts-purple:#652d90;--vscode-diffEditor-move-border:rgba(139,139,139,.61);--vscode-diffEditor-moveActive-border:orange;--vscode-symbolIcon-arrayForeground:#616161;--vscode-symbolIcon-booleanForeground:#616161;--vscode-symbolIcon-classForeground:#d67e00;--vscode-symbolIcon-colorForeground:#616161;--vscode-symbolIcon-constantForeground:#616161;--vscode-symbolIcon-constructorForeground:#652d90;--vscode-symbolIcon-enumeratorForeground:#d67e00;--vscode-symbolIcon-enumeratorMemberForeground:#007acc;--vscode-symbolIcon-eventForeground:#d67e00;--vscode-symbolIcon-fieldForeground:#007acc;--vscode-symbolIcon-fileForeground:#616161;--vscode-symbolIcon-folderForeground:#616161;--vscode-symbolIcon-functionForeground:#652d90;--vscode-symbolIcon-interfaceForeground:#007acc;--vscode-symbolIcon-keyForeground:#616161;--vscode-symbolIcon-keywordForeground:#616161;--vscode-symbolIcon-methodForeground:#652d90;--vscode-symbolIcon-moduleForeground:#616161;--vscode-symbolIcon-namespaceForeground:#616161;--vscode-symbolIcon-nullForeground:#616161;--vscode-symbolIcon-numberForeground:#616161;--vscode-symbolIcon-objectForeground:#616161;--vscode-symbolIcon-operatorForeground:#616161;--vscode-symbolIcon-packageForeground:#616161;--vscode-symbolIcon-propertyForeground:#616161;--vscode-symbolIcon-referenceForeground:#616161;--vscode-symbolIcon-snippetForeground:#616161;--vscode-symbolIcon-stringForeground:#616161;--vscode-symbolIcon-structForeground:#616161;--vscode-symbolIcon-textForeground:#616161;--vscode-symbolIcon-typeParameterForeground:#616161;--vscode-symbolIcon-unitForeground:#616161;--vscode-symbolIcon-variableForeground:#007acc;--vscode-actionBar-toggledBackground:rgba(0,144,241,.2);--vscode-editor-lineHighlightBorder:#eee;--vscode-editor-rangeHighlightBackground:rgba(253,255,0,.2);--vscode-editor-symbolHighlightBackground:rgba(234,92,0,.33);--vscode-editorCursor-foreground:#000;--vscode-editorWhitespace-foreground:rgba(51,51,51,.2);--vscode-editorLineNumber-foreground:#237893;--vscode-editorIndentGuide-background:rgba(51,51,51,.2);--vscode-editorIndentGuide-activeBackground:rgba(51,51,51,.2);--vscode-editorIndentGuide-background1:#d3d3d3;--vscode-editorIndentGuide-background2:transparent;--vscode-editorIndentGuide-background3:transparent;--vscode-editorIndentGuide-background4:transparent;--vscode-editorIndentGuide-background5:transparent;--vscode-editorIndentGuide-background6:transparent;--vscode-editorIndentGuide-activeBackground1:#939393;--vscode-editorIndentGuide-activeBackground2:transparent;--vscode-editorIndentGuide-activeBackground3:transparent;--vscode-editorIndentGuide-activeBackground4:transparent;--vscode-editorIndentGuide-activeBackground5:transparent;--vscode-editorIndentGuide-activeBackground6:transparent;--vscode-editorActiveLineNumber-foreground:#0b216f;--vscode-editorLineNumber-activeForeground:#0b216f;--vscode-editorRuler-foreground:#d3d3d3;--vscode-editorCodeLens-foreground:#919191;--vscode-editorBracketMatch-background:rgba(0,100,0,.1);--vscode-editorBracketMatch-border:#b9b9b9;--vscode-editorOverviewRuler-border:rgba(127,127,127,.3);--vscode-editorGutter-background:#fffffe;--vscode-editorUnnecessaryCode-opacity:rgba(0,0,0,.47);--vscode-editorGhostText-foreground:rgba(0,0,0,.47);--vscode-editorOverviewRuler-rangeHighlightForeground:rgba(0,122,204,.6);--vscode-editorOverviewRuler-errorForeground:rgba(255,18,18,.7);--vscode-editorOverviewRuler-warningForeground:#bf8803;--vscode-editorOverviewRuler-infoForeground:#1a85ff;--vscode-editorBracketHighlight-foreground1:#0431fa;--vscode-editorBracketHighlight-foreground2:#319331;--vscode-editorBracketHighlight-foreground3:#7b3814;--vscode-editorBracketHighlight-foreground4:transparent;--vscode-editorBracketHighlight-foreground5:transparent;--vscode-editorBracketHighlight-foreground6:transparent;--vscode-editorBracketHighlight-unexpectedBracket-foreground:rgba(255,18,18,.8);--vscode-editorBracketPairGuide-background1:transparent;--vscode-editorBracketPairGuide-background2:transparent;--vscode-editorBracketPairGuide-background3:transparent;--vscode-editorBracketPairGuide-background4:transparent;--vscode-editorBracketPairGuide-background5:transparent;--vscode-editorBracketPairGuide-background6:transparent;--vscode-editorBracketPairGuide-activeBackground1:transparent;--vscode-editorBracketPairGuide-activeBackground2:transparent;--vscode-editorBracketPairGuide-activeBackground3:transparent;--vscode-editorBracketPairGuide-activeBackground4:transparent;--vscode-editorBracketPairGuide-activeBackground5:transparent;--vscode-editorBracketPairGuide-activeBackground6:transparent;--vscode-editorUnicodeHighlight-border:#cea33d;--vscode-editorUnicodeHighlight-background:rgba(206,163,61,.08);--vscode-editorOverviewRuler-bracketMatchForeground:#a0a0a0;--vscode-editor-linkedEditingBackground:rgba(255,0,0,.3);--vscode-editor-wordHighlightBackground:rgba(87,87,87,.25);--vscode-editor-wordHighlightStrongBackground:rgba(14,99,156,.25);--vscode-editor-wordHighlightTextBackground:rgba(87,87,87,.25);--vscode-editorOverviewRuler-wordHighlightForeground:rgba(160,160,160,.8);--vscode-editorOverviewRuler-wordHighlightStrongForeground:rgba(192,160,192,.8);--vscode-editorOverviewRuler-wordHighlightTextForeground:rgba(160,160,160,.8);--vscode-peekViewTitle-background:#f3f3f3;--vscode-peekViewTitleLabel-foreground:#000;--vscode-peekViewTitleDescription-foreground:#616161;--vscode-peekView-border:#1a85ff;--vscode-peekViewResult-background:#f3f3f3;--vscode-peekViewResult-lineForeground:#646465;--vscode-peekViewResult-fileForeground:#1e1e1e;--vscode-peekViewResult-selectionBackground:rgba(51,153,255,.2);--vscode-peekViewResult-selectionForeground:#6c6c6c;--vscode-peekViewEditor-background:#f2f8fc;--vscode-peekViewEditorGutter-background:#f2f8fc;--vscode-peekViewEditorStickyScroll-background:#f2f8fc;--vscode-peekViewResult-matchHighlightBackground:rgba(234,92,0,.3);--vscode-peekViewEditor-matchHighlightBackground:rgba(245,216,2,.87);--vscode-editorMarkerNavigationError-background:#e51400;--vscode-editorMarkerNavigationError-headerBackground:rgba(229,20,0,.1);--vscode-editorMarkerNavigationWarning-background:#bf8803;--vscode-editorMarkerNavigationWarning-headerBackground:rgba(191,136,3,.1);--vscode-editorMarkerNavigationInfo-background:#1a85ff;--vscode-editorMarkerNavigationInfo-headerBackground:rgba(26,133,255,.1);--vscode-editorMarkerNavigation-background:#fffffe;--vscode-editorHoverWidget-highlightForeground:#0066bf;--vscode-editorSuggestWidget-background:#f3f3f3;--vscode-editorSuggestWidget-border:#c8c8c8;--vscode-editorSuggestWidget-foreground:#000;--vscode-editorSuggestWidget-selectedForeground:#fff;--vscode-editorSuggestWidget-selectedBackground:#0060c0;--vscode-editorSuggestWidget-highlightForeground:#0066bf;--vscode-editorSuggestWidget-focusHighlightForeground:#bbe7ff;--vscode-editorSuggestWidgetStatus-foreground:rgba(0,0,0,.5);--vscode-editor-foldBackground:rgba(173,214,255,.3);--vscode-editorGutter-foldingControlForeground:#424242}.light-wlgpfI .monaco-editor .mtk1{color:#000}.light-wlgpfI .monaco-diff-editor .mtk1{color:#000}.light-wlgpfI .monaco-editor .mtk9{color:#000}.light-wlgpfI .monaco-diff-editor .mtk9{color:#000}.light-wlgpfI .monaco-editor .mtk2{color:#fffffe}.light-wlgpfI .monaco-diff-editor .mtk2{color:#fffffe}.light-wlgpfI .monaco-editor .mtk3{color:gray}.light-wlgpfI .monaco-diff-editor .mtk3{color:gray}.light-wlgpfI .monaco-editor .mtk4{color:#a31515}.light-wlgpfI .monaco-diff-editor .mtk4{color:#a31515}.light-wlgpfI .monaco-editor .mtk20{color:#a31515}.light-wlgpfI .monaco-diff-editor .mtk20{color:#a31515}.light-wlgpfI .monaco-editor .mtk5{color:#0451a5}.light-wlgpfI .monaco-diff-editor .mtk5{color:#0451a5}.light-wlgpfI .monaco-editor .mtk6{color:#098658}.light-wlgpfI .monaco-diff-editor .mtk6{color:#098658}.light-wlgpfI .monaco-editor .mtk7{color:#098658}.light-wlgpfI .monaco-diff-editor .mtk7{color:#098658}.light-wlgpfI .monaco-editor .mtk8{color:green}.light-wlgpfI .monaco-diff-editor .mtk8{color:green}.light-wlgpfI .monaco-editor .mtk10{color:#383838}.light-wlgpfI .monaco-diff-editor .mtk10{color:#383838}.light-wlgpfI .monaco-editor .mtk11{color:#cd3131}.light-wlgpfI .monaco-diff-editor .mtk11{color:#cd3131}.light-wlgpfI .monaco-editor .mtk12{color:#863b00}.light-wlgpfI .monaco-diff-editor .mtk12{color:#863b00}.light-wlgpfI .monaco-editor .mtk13{color:#af00db}.light-wlgpfI .monaco-diff-editor .mtk13{color:#af00db}.light-wlgpfI .monaco-editor .mtk14{color:maroon}.light-wlgpfI .monaco-diff-editor .mtk14{color:maroon}.light-wlgpfI .monaco-editor .mtk15{color:#e00000}.light-wlgpfI .monaco-diff-editor .mtk15{color:#e00000}.light-wlgpfI .monaco-editor .mtk16{color:#3030c0}.light-wlgpfI .monaco-diff-editor .mtk16{color:#3030c0}.light-wlgpfI .monaco-editor .mtk17{color:#666}.light-wlgpfI .monaco-diff-editor .mtk17{color:#666}.light-wlgpfI .monaco-editor .mtk18{color:#789}.light-wlgpfI .monaco-diff-editor .mtk18{color:#789}.light-wlgpfI .monaco-editor .mtk19{color:#c700c7}.light-wlgpfI .monaco-diff-editor .mtk19{color:#c700c7}.light-wlgpfI .monaco-editor .mtk21{color:#4f76ac}.light-wlgpfI .monaco-diff-editor .mtk21{color:#4f76ac}.light-wlgpfI .monaco-editor .mtk22{color:teal}.light-wlgpfI .monaco-diff-editor .mtk22{color:teal}.light-wlgpfI .monaco-editor .mtk23{color:#018}.light-wlgpfI .monaco-diff-editor .mtk23{color:#018}.light-wlgpfI .monaco-editor .mtk24{color:#4864aa}.light-wlgpfI .monaco-diff-editor .mtk24{color:#4864aa}.light-wlgpfI .monaco-editor .inputarea.ime-input{color:#000!important;background-color:#fffffe!important}.light-wlgpfI .monaco-diff-editor .inputarea.ime-input{color:#000!important;background-color:#fffffe!important}.light-wlgpfI .monaco-editor .view-overlays .current-line{border:2px solid #eee}.light-wlgpfI .monaco-diff-editor .view-overlays .current-line{border:2px solid #eee}.light-wlgpfI .monaco-editor .margin-view-overlays .current-line-margin{border:2px solid #eee}.light-wlgpfI .monaco-diff-editor .margin-view-overlays .current-line-margin{border:2px solid #eee}.light-wlgpfI .monaco-editor .bracket-indent-guide.lvl-0{--guide-color:rgba(4,49,250,.3);--guide-color-active:#0431fa}.light-wlgpfI .monaco-diff-editor .bracket-indent-guide.lvl-0{--guide-color:rgba(4,49,250,.3);--guide-color-active:#0431fa}.light-wlgpfI .monaco-editor .bracket-indent-guide.lvl-3{--guide-color:rgba(4,49,250,.3);--guide-color-active:#0431fa}.light-wlgpfI .monaco-diff-editor .bracket-indent-guide.lvl-3{--guide-color:rgba(4,49,250,.3);--guide-color-active:#0431fa}.light-wlgpfI .monaco-editor .bracket-indent-guide.lvl-6{--guide-color:rgba(4,49,250,.3);--guide-color-active:#0431fa}.light-wlgpfI .monaco-diff-editor .bracket-indent-guide.lvl-6{--guide-color:rgba(4,49,250,.3);--guide-color-active:#0431fa}.light-wlgpfI .monaco-editor .bracket-indent-guide.lvl-9{--guide-color:rgba(4,49,250,.3);--guide-color-active:#0431fa}.light-wlgpfI .monaco-diff-editor .bracket-indent-guide.lvl-9{--guide-color:rgba(4,49,250,.3);--guide-color-active:#0431fa}.light-wlgpfI .monaco-editor .bracket-indent-guide.lvl-1{--guide-color:rgba(49,147,49,.3);--guide-color-active:#319331}.light-wlgpfI .monaco-diff-editor .bracket-indent-guide.lvl-1{--guide-color:rgba(49,147,49,.3);--guide-color-active:#319331}.light-wlgpfI .monaco-editor .bracket-indent-guide.lvl-4{--guide-color:rgba(49,147,49,.3);--guide-color-active:#319331}.light-wlgpfI .monaco-diff-editor .bracket-indent-guide.lvl-4{--guide-color:rgba(49,147,49,.3);--guide-color-active:#319331}.light-wlgpfI .monaco-editor .bracket-indent-guide.lvl-7{--guide-color:rgba(49,147,49,.3);--guide-color-active:#319331}.light-wlgpfI .monaco-diff-editor .bracket-indent-guide.lvl-7{--guide-color:rgba(49,147,49,.3);--guide-color-active:#319331}.light-wlgpfI .monaco-editor .bracket-indent-guide.lvl-10{--guide-color:rgba(49,147,49,.3);--guide-color-active:#319331}.light-wlgpfI .monaco-diff-editor .bracket-indent-guide.lvl-10{--guide-color:rgba(49,147,49,.3);--guide-color-active:#319331}.light-wlgpfI .monaco-editor .bracket-indent-guide.lvl-2{--guide-color:rgba(123,56,20,.3);--guide-color-active:#7b3814}.light-wlgpfI .monaco-diff-editor .bracket-indent-guide.lvl-2{--guide-color:rgba(123,56,20,.3);--guide-color-active:#7b3814}.light-wlgpfI .monaco-editor .bracket-indent-guide.lvl-5{--guide-color:rgba(123,56,20,.3);--guide-color-active:#7b3814}.light-wlgpfI .monaco-diff-editor .bracket-indent-guide.lvl-5{--guide-color:rgba(123,56,20,.3);--guide-color-active:#7b3814}.light-wlgpfI .monaco-editor .bracket-indent-guide.lvl-8{--guide-color:rgba(123,56,20,.3);--guide-color-active:#7b3814}.light-wlgpfI .monaco-diff-editor .bracket-indent-guide.lvl-8{--guide-color:rgba(123,56,20,.3);--guide-color-active:#7b3814}.light-wlgpfI .monaco-editor .bracket-indent-guide.lvl-11{--guide-color:rgba(123,56,20,.3);--guide-color-active:#7b3814}.light-wlgpfI .monaco-diff-editor .bracket-indent-guide.lvl-11{--guide-color:rgba(123,56,20,.3);--guide-color-active:#7b3814}.light-wlgpfI .monaco-editor .lines-content .core-guide.core-guide-indent[class*=lvl-]{--indent-color:#d3d3d3;--indent-color-active:#939393}.light-wlgpfI .monaco-diff-editor .lines-content .core-guide.core-guide-indent[class*=lvl-]{--indent-color:#d3d3d3;--indent-color-active:#939393}.light-wlgpfI .monaco-editor .lines-content .core-guide-indent{box-shadow:1px 0 0 0 var(--indent-color)inset}.light-wlgpfI .monaco-diff-editor .lines-content .core-guide-indent{box-shadow:1px 0 0 0 var(--indent-color)inset}.light-wlgpfI .monaco-editor .lines-content .core-guide-indent.indent-active{box-shadow:1px 0 0 0 var(--indent-color-active)inset}.light-wlgpfI .monaco-diff-editor .lines-content .core-guide-indent.indent-active{box-shadow:1px 0 0 0 var(--indent-color-active)inset}.light-wlgpfI .monaco-editor .line-numbers.dimmed-line-number{color:rgba(35,120,147,.4)}.light-wlgpfI .monaco-diff-editor .line-numbers.dimmed-line-number{color:rgba(35,120,147,.4)}.light-wlgpfI .monaco-editor .cursors-layer .cursor{color:#fff;background-color:#000;border-color:#000}.light-wlgpfI .monaco-diff-editor .cursors-layer .cursor{color:#fff;background-color:#000;border-color:#000}.light-wlgpfI .monaco-editor .bracket-highlighting-0{color:#0431fa}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-0{color:#0431fa}.light-wlgpfI .monaco-editor .bracket-highlighting-3{color:#0431fa}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-3{color:#0431fa}.light-wlgpfI .monaco-editor .bracket-highlighting-6{color:#0431fa}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-6{color:#0431fa}.light-wlgpfI .monaco-editor .bracket-highlighting-9{color:#0431fa}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-9{color:#0431fa}.light-wlgpfI .monaco-editor .bracket-highlighting-12{color:#0431fa}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-12{color:#0431fa}.light-wlgpfI .monaco-editor .bracket-highlighting-1{color:#319331}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-1{color:#319331}.light-wlgpfI .monaco-editor .bracket-highlighting-4{color:#319331}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-4{color:#319331}.light-wlgpfI .monaco-editor .bracket-highlighting-7{color:#319331}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-7{color:#319331}.light-wlgpfI .monaco-editor .bracket-highlighting-10{color:#319331}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-10{color:#319331}.light-wlgpfI .monaco-editor .bracket-highlighting-13{color:#319331}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-13{color:#319331}.light-wlgpfI .monaco-editor .bracket-highlighting-2{color:#7b3814}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-2{color:#7b3814}.light-wlgpfI .monaco-editor .bracket-highlighting-5{color:#7b3814}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-5{color:#7b3814}.light-wlgpfI .monaco-editor .bracket-highlighting-8{color:#7b3814}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-8{color:#7b3814}.light-wlgpfI .monaco-editor .bracket-highlighting-11{color:#7b3814}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-11{color:#7b3814}.light-wlgpfI .monaco-editor .bracket-highlighting-14{color:#7b3814}.light-wlgpfI .monaco-diff-editor .bracket-highlighting-14{color:#7b3814}.light-wlgpfI .monaco-editor .squiggly-error{background:url(${m}) 0 100% repeat-x}.light-wlgpfI .monaco-diff-editor .squiggly-error{background:url(${m}) 0 100% repeat-x}.light-wlgpfI .monaco-editor .squiggly-warning{background:url(${h}) 0 100% repeat-x}.light-wlgpfI .monaco-diff-editor .squiggly-warning{background:url(${h}) 0 100% repeat-x}.light-wlgpfI .monaco-editor .squiggly-info{background:url(${b}) 0 100% repeat-x}.light-wlgpfI .monaco-diff-editor .squiggly-info{background:url(${b}) 0 100% repeat-x}.light-wlgpfI .monaco-editor .squiggly-hint{background:url(${v}) 0 100% no-repeat}.light-wlgpfI .monaco-diff-editor .squiggly-hint{background:url(${v}) 0 100% no-repeat}.light-wlgpfI .monaco-editor .showUnused .squiggly-inline-unnecessary{opacity:.467}.light-wlgpfI .monaco-diff-editor .showUnused .squiggly-inline-unnecessary{opacity:.467}.light-wlgpfI .monaco-editor .selectionHighlight{background-color:rgba(173,214,255,.15)}.light-wlgpfI .monaco-diff-editor .selectionHighlight{background-color:rgba(173,214,255,.15)}.light-wlgpfI .monaco-editor .diagonal-fill{background-image:linear-gradient(-45deg,rgba(34,34,34,.2) 12.5%,transparent 12.5%,transparent 50%,rgba(34,34,34,.2) 50%,rgba(34,34,34,.2) 62.5%,transparent 62.5%,transparent 100%);background-size:8px 8px}.light-wlgpfI .monaco-diff-editor .diagonal-fill{background-image:linear-gradient(-45deg,rgba(34,34,34,.2) 12.5%,transparent 12.5%,transparent 50%,rgba(34,34,34,.2) 50%,rgba(34,34,34,.2) 62.5%,transparent 62.5%,transparent 100%);background-size:8px 8px}.light-wlgpfI .monaco-editor .findMatch{background-color:rgba(234,92,0,.33)}.light-wlgpfI .monaco-diff-editor .findMatch{background-color:rgba(234,92,0,.33)}.light-wlgpfI .monaco-editor .currentFindMatch{background-color:#a8ac94}.light-wlgpfI .monaco-diff-editor .currentFindMatch{background-color:#a8ac94}.light-wlgpfI .monaco-editor .findScope{background-color:rgba(180,180,180,.3)}.light-wlgpfI .monaco-diff-editor .findScope{background-color:rgba(180,180,180,.3)}.light-wlgpfI .monaco-editor .find-widget{color:#616161;background-color:#f3f3f3;border:none;box-shadow:0 0 8px 2px rgba(0,0,0,.16)}.light-wlgpfI .monaco-diff-editor .find-widget{color:#616161;background-color:#f3f3f3;border:none;box-shadow:0 0 8px 2px rgba(0,0,0,.16)}.light-wlgpfI .monaco-editor .find-widget.no-results .matchesCount{color:#a1260d}.light-wlgpfI .monaco-diff-editor .find-widget.no-results .matchesCount{color:#a1260d}.light-wlgpfI .monaco-editor .find-widget .monaco-sash{background-color:#c8c8c8}.light-wlgpfI .monaco-diff-editor .find-widget .monaco-sash{background-color:#c8c8c8}.light-wlgpfI .monaco-editor .find-widget .button:not(.disabled):hover{background-color:rgba(184,184,184,.31)!important}.light-wlgpfI .monaco-diff-editor .find-widget .button:not(.disabled):hover{background-color:rgba(184,184,184,.31)!important}.light-wlgpfI .monaco-editor .find-widget .codicon-find-selection:hover{background-color:rgba(184,184,184,.31)!important}.light-wlgpfI .monaco-diff-editor .find-widget .codicon-find-selection:hover{background-color:rgba(184,184,184,.31)!important}.light-wlgpfI .monaco-editor .find-widget .monaco-inputbox.synthetic-focus{outline-color:#0090f1}.light-wlgpfI .monaco-diff-editor .find-widget .monaco-inputbox.synthetic-focus{outline-color:#0090f1}.light-wlgpfI .monaco-editor .monaco-hover .hover-row:not(:-webkit-any(:first-child,:empty)){border-top:1px solid rgba(200,200,200,.5)}.light-wlgpfI .monaco-editor .monaco-hover .hover-row:not(:-moz-any(:first-child,:empty)){border-top:1px solid rgba(200,200,200,.5)}.light-wlgpfI .monaco-editor .monaco-hover .hover-row:not(:is(:first-child,:empty)){border-top:1px solid rgba(200,200,200,.5)}.light-wlgpfI .monaco-diff-editor .monaco-hover .hover-row:not(:-webkit-any(:first-child,:empty)){border-top:1px solid rgba(200,200,200,.5)}.light-wlgpfI .monaco-diff-editor .monaco-hover .hover-row:not(:-moz-any(:first-child,:empty)){border-top:1px solid rgba(200,200,200,.5)}.light-wlgpfI .monaco-diff-editor .monaco-hover .hover-row:not(:is(:first-child,:empty)){border-top:1px solid rgba(200,200,200,.5)}.light-wlgpfI .monaco-editor .monaco-hover hr{border-top:1px solid rgba(200,200,200,.5);border-bottom:0 solid rgba(200,200,200,.5)}.light-wlgpfI .monaco-diff-editor .monaco-hover hr{border-top:1px solid rgba(200,200,200,.5);border-bottom:0 solid rgba(200,200,200,.5)}.light-wlgpfI .monaco-editor .marker-widget{background-color:#fffffe!important}.light-wlgpfI .monaco-diff-editor .marker-widget{background-color:#fffffe!important}.light-wlgpfI .monaco-editor .peekview-title .filename{color:#000!important}.light-wlgpfI .monaco-diff-editor .peekview-title .filename{color:#000!important}.light-wlgpfI .monaco-editor .peekview-title .dirname{color:#616161!important}.light-wlgpfI .monaco-diff-editor .peekview-title .dirname{color:#616161!important}`,""]),p.locals={light:"light-wlgpfI"},o.Z=p},746509:function(e,o,t){var i=t(125161),r=t.n(i),a=t(404442),n=t.n(a)()(r());n.push([e.id,'.tool-wrapper-u6Ufm0{color:#1c1d23;height:100%}.tool-wrapper-u6Ufm0 .layout-header-bQD8A8{padding:24px 24px 12px}.tool-wrapper-u6Ufm0 .plugin-detail-info-v3aOnS{margin-bottom:36px;padding-bottom:12px;position:relative}.tool-wrapper-u6Ufm0 .plugin-detail-info-v3aOnS:after{content:"";background-color:rgba(29,28,35,.08);width:calc(100% + 48px);height:1px;position:absolute;bottom:0;left:-24px}.tool-wrapper-u6Ufm0 .plugin-detail-info-v3aOnS .plugin-detail-title-WDLcxn{max-width:300px;font-size:18px;font-weight:600;line-height:24px}.tool-wrapper-u6Ufm0 .plugin-detail-info-v3aOnS .plugin-detail-published-UDK2He{color:rgba(28,29,35,.6);font-size:12px;line-height:16px}.tool-wrapper-u6Ufm0 .plugin-detail-info-v3aOnS .plugin-detail-avatar-r4HWPt{border-radius:6px;width:36px;height:36px}.tool-wrapper-u6Ufm0 .plugin-detail-info-v3aOnS .plugin-detail-desc-GUS_oT{color:rgba(28,29,35,.8);max-width:300px;line-height:16px}.tool-wrapper-u6Ufm0 .banner-JQoOaH{margin-bottom:24px;box-shadow:0 4px 14px rgba(0,0,0,.1),0 0 1px rgba(0,0,0,.3)}.tool-wrapper-u6Ufm0 .notips-tYDOzt{cursor:pointer;color:#4062ff;margin-left:12px;font-weight:600}.tool-wrapper-u6Ufm0 .min-width-200-MhmjtE{min-width:200px}.tool-wrapper-u6Ufm0 .tool-table-desc-HVWvgw{font-size:12px;line-height:18px}.tool-wrapper-u6Ufm0 .icon-delete-disabled-K0CgNF path{fill:currentColor}.tool-wrapper-u6Ufm0 .icon-btn-disable-An1pSO{color:var(--light-usage-text-color-text-2,rgba(28,29,35,.2))}.tool-wrapper-u6Ufm0 .debug-btn-disable-A1NQyM{color:#1d1c23}.tool-wrapper-u6Ufm0 .debug-btn-disable-A1NQyM path{fill:currentColor;fill-opacity:.2}.tool-wrapper-u6Ufm0 .icon-more-UbVlXd{color:rgba(29,28,35,.6)}.tool-wrapper-u6Ufm0 .icon-more-UbVlXd path{fill:currentColor}.tool-wrapper-u6Ufm0 .grey-light-CHxMoV{background-color:#f0f0f5;border-radius:4px;height:16px}.tool-wrapper-u6Ufm0 .edit-plugin-btn-BieQhl{padding:0 8px}.tool-wrapper-u6Ufm0 .edit-plugin-btn-BieQhl.edit-mVpQEb .semi-button-content-right{display:none}.tool-wrapper-u6Ufm0 .circle-point-athd35{border-radius:50%;width:10px;height:10px}.tool-wrapper-u6Ufm0 .plugin-method-tag-K37DzN{height:16px;color:var(--Light-color-violet---violet-6,#6430bf);background:var(--Light-color-violet---violet-1,#e9d6f9);border-radius:4px}.tool-wrapper-u6Ufm0 .icon-example-disabled-QVOpbn path{fill:currentColor!important}',""]),n.locals={"tool-wrapper":"tool-wrapper-u6Ufm0",toolWrapper:"tool-wrapper-u6Ufm0","layout-header":"layout-header-bQD8A8",layoutHeader:"layout-header-bQD8A8","plugin-detail-info":"plugin-detail-info-v3aOnS",pluginDetailInfo:"plugin-detail-info-v3aOnS","plugin-detail-title":"plugin-detail-title-WDLcxn",pluginDetailTitle:"plugin-detail-title-WDLcxn","plugin-detail-published":"plugin-detail-published-UDK2He",pluginDetailPublished:"plugin-detail-published-UDK2He","plugin-detail-avatar":"plugin-detail-avatar-r4HWPt",pluginDetailAvatar:"plugin-detail-avatar-r4HWPt","plugin-detail-desc":"plugin-detail-desc-GUS_oT",pluginDetailDesc:"plugin-detail-desc-GUS_oT",banner:"banner-JQoOaH",notips:"notips-tYDOzt","min-width-200":"min-width-200-MhmjtE",minWidth200:"min-width-200-MhmjtE","tool-table-desc":"tool-table-desc-HVWvgw",toolTableDesc:"tool-table-desc-HVWvgw","icon-delete-disabled":"icon-delete-disabled-K0CgNF",iconDeleteDisabled:"icon-delete-disabled-K0CgNF","icon-btn-disable":"icon-btn-disable-An1pSO",iconBtnDisable:"icon-btn-disable-An1pSO","debug-btn-disable":"debug-btn-disable-A1NQyM",debugBtnDisable:"debug-btn-disable-A1NQyM","icon-more":"icon-more-UbVlXd",iconMore:"icon-more-UbVlXd","grey-light":"grey-light-CHxMoV",greyLight:"grey-light-CHxMoV","edit-plugin-btn":"edit-plugin-btn-BieQhl",editPluginBtn:"edit-plugin-btn-BieQhl",edit:"edit-mVpQEb","circle-point":"circle-point-athd35",circlePoint:"circle-point-athd35","plugin-method-tag":"plugin-method-tag-K37DzN",pluginMethodTag:"plugin-method-tag-K37DzN","icon-example-disabled":"icon-example-disabled-QVOpbn",iconExampleDisabled:"icon-example-disabled-QVOpbn"},o.Z=n}}]);
//# sourceMappingURL=1794.aa07c95f.js.map