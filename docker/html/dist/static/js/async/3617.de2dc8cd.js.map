{"version": 3, "file": "static/js/async/3617.de2dc8cd.js", "sources": ["webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/state-local@1.0.7/node_modules/state-local/lib/es/state-local.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/curry.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/validators/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/isObject.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/compose.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/loader/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/config/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.2.0_react@18.2.0/node_modules/@monaco-editor/react/dist/index.mjs"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nexport { _arrayLikeToArray as arrayLikeToArray, _arrayWithHoles as arrayWithHoles, _defineProperty as defineProperty, _iterableToArrayLimit as iterableToArrayLimit, _nonIterableRest as nonIterableRest, _objectSpread2 as objectSpread2, _objectWithoutProperties as objectWithoutProperties, _objectWithoutPropertiesLoose as objectWithoutPropertiesLoose, _slicedToArray as slicedToArray, _unsupportedIterableToArray as unsupportedIterableToArray };\n", "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n}\n\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        nextArgs[_key3] = arguments[_key3];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nfunction isEmpty(obj) {\n  return !Object.keys(obj).length;\n}\n\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\nfunction hasOwnProperty(object, property) {\n  return Object.prototype.hasOwnProperty.call(object, property);\n}\n\nfunction validateChanges(initial, changes) {\n  if (!isObject(changes)) errorHandler('changeType');\n  if (Object.keys(changes).some(function (field) {\n    return !hasOwnProperty(initial, field);\n  })) errorHandler('changeField');\n  return changes;\n}\n\nfunction validateSelector(selector) {\n  if (!isFunction(selector)) errorHandler('selectorType');\n}\n\nfunction validateHandler(handler) {\n  if (!(isFunction(handler) || isObject(handler))) errorHandler('handlerType');\n  if (isObject(handler) && Object.values(handler).some(function (_handler) {\n    return !isFunction(_handler);\n  })) errorHandler('handlersType');\n}\n\nfunction validateInitial(initial) {\n  if (!initial) errorHandler('initialIsRequired');\n  if (!isObject(initial)) errorHandler('initialType');\n  if (isEmpty(initial)) errorHandler('initialContent');\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  initialIsRequired: 'initial state is required',\n  initialType: 'initial state should be an object',\n  initialContent: 'initial state shouldn\\'t be an empty object',\n  handlerType: 'handler should be an object or a function',\n  handlersType: 'all handlers should be a functions',\n  selectorType: 'selector should be a function',\n  changeType: 'provided value of changes should be an object',\n  changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n  \"default\": 'an unknown error accured in `state-local` package'\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  changes: validateChanges,\n  selector: validateSelector,\n  handler: validateHandler,\n  initial: validateInitial\n};\n\nfunction create(initial) {\n  var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  validators.initial(initial);\n  validators.handler(handler);\n  var state = {\n    current: initial\n  };\n  var didUpdate = curry(didStateUpdate)(state, handler);\n  var update = curry(updateState)(state);\n  var validate = curry(validators.changes)(initial);\n  var getChanges = curry(extractChanges)(state);\n\n  function getState() {\n    var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function (state) {\n      return state;\n    };\n    validators.selector(selector);\n    return selector(state.current);\n  }\n\n  function setState(causedChanges) {\n    compose(didUpdate, update, validate, getChanges)(causedChanges);\n  }\n\n  return [getState, setState];\n}\n\nfunction extractChanges(state, causedChanges) {\n  return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\n\nfunction updateState(state, changes) {\n  state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n  return changes;\n}\n\nfunction didStateUpdate(state, handler, changes) {\n  isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function (field) {\n    var _handler$field;\n\n    return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n  });\n  return changes;\n}\n\nvar index = {\n  create: create\n};\n\nexport default index;\n", "function curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nexport default curry;\n", "import curry from '../utils/curry.js';\nimport isObject from '../utils/isObject.js';\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!isObject(config)) errorHandler('configType');\n\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n\n  return config;\n}\n/**\n * logs deprecation message\n */\n\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\n\nexport default validators;\nexport { errorHandler, errorMessages };\n", "function isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nexport default isObject;\n", "var compose = function compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n};\n\nexport default compose;\n", "// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n  type: 'cancelation',\n  msg: 'operation is manually canceled'\n};\n\nfunction makeCancelable(promise) {\n  var hasCanceled_ = false;\n  var wrappedPromise = new Promise(function (resolve, reject) {\n    promise.then(function (val) {\n      return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n    });\n    promise[\"catch\"](reject);\n  });\n  return wrappedPromise.cancel = function () {\n    return hasCanceled_ = true;\n  }, wrappedPromise;\n}\n\nexport default makeCancelable;\nexport { CANCELATION_MESSAGE };\n", "import { slicedToArray as _slicedToArray, objectWithoutProperties as _objectWithoutProperties } from '../_virtual/_rollupPluginBabelHelpers.js';\nimport state from 'state-local';\nimport config$1 from '../config/index.js';\nimport validators from '../validators/index.js';\nimport compose from '../utils/compose.js';\nimport merge from '../utils/deepMerge.js';\nimport makeCancelable from '../utils/makeCancelable.js';\n\n/** the local state of the module */\n\nvar _state$create = state.create({\n  config: config$1,\n  isInitialized: false,\n  resolve: null,\n  reject: null,\n  monaco: null\n}),\n    _state$create2 = _slicedToArray(_state$create, 2),\n    getState = _state$create2[0],\n    setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */\n\n\nfunction config(globalConfig) {\n  var _validators$config = validators.config(globalConfig),\n      monaco = _validators$config.monaco,\n      config = _objectWithoutProperties(_validators$config, [\"monaco\"]);\n\n  setState(function (state) {\n    return {\n      config: merge(state.config, config),\n      monaco: monaco\n    };\n  });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */\n\n\nfunction init() {\n  var state = getState(function (_ref) {\n    var monaco = _ref.monaco,\n        isInitialized = _ref.isInitialized,\n        resolve = _ref.resolve;\n    return {\n      monaco: monaco,\n      isInitialized: isInitialized,\n      resolve: resolve\n    };\n  });\n\n  if (!state.isInitialized) {\n    setState({\n      isInitialized: true\n    });\n\n    if (state.monaco) {\n      state.resolve(state.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    if (window.monaco && window.monaco.editor) {\n      storeMonacoInstance(window.monaco);\n      state.resolve(window.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    compose(injectScripts, getMonacoLoaderScript)(configureLoader);\n  }\n\n  return makeCancelable(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */\n\n\nfunction injectScripts(script) {\n  return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */\n\n\nfunction createScript(src) {\n  var script = document.createElement('script');\n  return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */\n\n\nfunction getMonacoLoaderScript(configureLoader) {\n  var state = getState(function (_ref2) {\n    var config = _ref2.config,\n        reject = _ref2.reject;\n    return {\n      config: config,\n      reject: reject\n    };\n  });\n  var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n\n  loaderScript.onload = function () {\n    return configureLoader();\n  };\n\n  loaderScript.onerror = state.reject;\n  return loaderScript;\n}\n/**\n * configures the monaco loader\n */\n\n\nfunction configureLoader() {\n  var state = getState(function (_ref3) {\n    var config = _ref3.config,\n        resolve = _ref3.resolve,\n        reject = _ref3.reject;\n    return {\n      config: config,\n      resolve: resolve,\n      reject: reject\n    };\n  });\n  var require = window.require;\n\n  require.config(state.config);\n\n  require(['vs/editor/editor.main'], function (monaco) {\n    storeMonacoInstance(monaco);\n    state.resolve(monaco);\n  }, function (error) {\n    state.reject(error);\n  });\n}\n/**\n * store monaco instance in local state\n */\n\n\nfunction storeMonacoInstance(monaco) {\n  if (!getState().monaco) {\n    setState({\n      monaco: monaco\n    });\n  }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */\n\n\nfunction __getMonacoInstance() {\n  return getState(function (_ref4) {\n    var monaco = _ref4.monaco;\n    return monaco;\n  });\n}\n\nvar wrapperPromise = new Promise(function (resolve, reject) {\n  return setState({\n    resolve: resolve,\n    reject: reject\n  });\n});\nvar loader = {\n  config: config,\n  init: init,\n  __getMonacoInstance: __getMonacoInstance\n};\n\nexport default loader;\n", "var config = {\n  paths: {\n    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'\n  }\n};\n\nexport default config;\n", "import { objectSpread2 as _objectSpread2 } from '../_virtual/_rollupPluginBabelHelpers.js';\n\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return _objectSpread2(_objectSpread2({}, target), source);\n}\n\nexport default merge;\n", "import _t from\"@monaco-editor/loader\";import{memo as Te}from\"react\";import ke,{useState as re,useRef as S,useCallback as oe,useEffect as ne}from\"react\";import Se from\"@monaco-editor/loader\";import{memo as ye}from\"react\";import K from\"react\";var le={wrapper:{display:\"flex\",position:\"relative\",textAlign:\"initial\"},fullWidth:{width:\"100%\"},hide:{display:\"none\"}},v=le;import me from\"react\";var ae={container:{display:\"flex\",height:\"100%\",width:\"100%\",justifyContent:\"center\",alignItems:\"center\"}},Y=ae;function Me({children:e}){return me.createElement(\"div\",{style:Y.container},e)}var Z=Me;var $=Z;function Ee({width:e,height:r,isEditorReady:n,loading:t,_ref:a,className:m,wrapperProps:E}){return K.createElement(\"section\",{style:{...v.wrapper,width:e,height:r},...E},!n&&K.createElement($,null,t),K.createElement(\"div\",{ref:a,style:{...v.fullWidth,...!n&&v.hide},className:m}))}var ee=Ee;var H=ye(ee);import{useEffect as xe}from\"react\";function Ce(e){xe(e,[])}var k=Ce;import{useEffect as ge,useRef as Re}from\"react\";function he(e,r,n=!0){let t=Re(!0);ge(t.current||!n?()=>{t.current=!1}:e,r)}var l=he;function D(){}function h(e,r,n,t){return De(e,t)||be(e,r,n,t)}function De(e,r){return e.editor.getModel(te(e,r))}function be(e,r,n,t){return e.editor.createModel(r,n,t?te(e,t):void 0)}function te(e,r){return e.Uri.parse(r)}function Oe({original:e,modified:r,language:n,originalLanguage:t,modifiedLanguage:a,originalModelPath:m,modifiedModelPath:E,keepCurrentOriginalModel:g=!1,keepCurrentModifiedModel:N=!1,theme:x=\"light\",loading:P=\"Loading...\",options:y={},height:V=\"100%\",width:z=\"100%\",className:F,wrapperProps:j={},beforeMount:A=D,onMount:q=D}){let[M,O]=re(!1),[T,s]=re(!0),u=S(null),c=S(null),w=S(null),d=S(q),o=S(A),b=S(!1);k(()=>{let i=Se.init();return i.then(f=>(c.current=f)&&s(!1)).catch(f=>f?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",f)),()=>u.current?I():i.cancel()}),l(()=>{if(u.current&&c.current){let i=u.current.getOriginalEditor(),f=h(c.current,e||\"\",t||n||\"text\",m||\"\");f!==i.getModel()&&i.setModel(f)}},[m],M),l(()=>{if(u.current&&c.current){let i=u.current.getModifiedEditor(),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");f!==i.getModel()&&i.setModel(f)}},[E],M),l(()=>{let i=u.current.getModifiedEditor();i.getOption(c.current.editor.EditorOption.readOnly)?i.setValue(r||\"\"):r!==i.getValue()&&(i.executeEdits(\"\",[{range:i.getModel().getFullModelRange(),text:r||\"\",forceMoveMarkers:!0}]),i.pushUndoStop())},[r],M),l(()=>{u.current?.getModel()?.original.setValue(e||\"\")},[e],M),l(()=>{let{original:i,modified:f}=u.current.getModel();c.current.editor.setModelLanguage(i,t||n||\"text\"),c.current.editor.setModelLanguage(f,a||n||\"text\")},[n,t,a],M),l(()=>{c.current?.editor.setTheme(x)},[x],M),l(()=>{u.current?.updateOptions(y)},[y],M);let L=oe(()=>{if(!c.current)return;o.current(c.current);let i=h(c.current,e||\"\",t||n||\"text\",m||\"\"),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");u.current?.setModel({original:i,modified:f})},[n,r,a,e,t,m,E]),U=oe(()=>{!b.current&&w.current&&(u.current=c.current.editor.createDiffEditor(w.current,{automaticLayout:!0,...y}),L(),c.current?.editor.setTheme(x),O(!0),b.current=!0)},[y,x,L]);ne(()=>{M&&d.current(u.current,c.current)},[M]),ne(()=>{!T&&!M&&U()},[T,M,U]);function I(){let i=u.current?.getModel();g||i?.original?.dispose(),N||i?.modified?.dispose(),u.current?.dispose()}return ke.createElement(H,{width:z,height:V,isEditorReady:M,loading:P,_ref:w,className:F,wrapperProps:j})}var ie=Oe;var we=Te(ie);import{useState as Ie}from\"react\";import ce from\"@monaco-editor/loader\";function Pe(){let[e,r]=Ie(ce.__getMonacoInstance());return k(()=>{let n;return e||(n=ce.init(),n.then(t=>{r(t)})),()=>n?.cancel()}),e}var Le=Pe;import{memo as ze}from\"react\";import We,{useState as ue,useEffect as W,useRef as C,useCallback as _e}from\"react\";import Ne from\"@monaco-editor/loader\";import{useEffect as Ue,useRef as ve}from\"react\";function He(e){let r=ve();return Ue(()=>{r.current=e},[e]),r.current}var se=He;var _=new Map;function Ve({defaultValue:e,defaultLanguage:r,defaultPath:n,value:t,language:a,path:m,theme:E=\"light\",line:g,loading:N=\"Loading...\",options:x={},overrideServices:P={},saveViewState:y=!0,keepCurrentModel:V=!1,width:z=\"100%\",height:F=\"100%\",className:j,wrapperProps:A={},beforeMount:q=D,onMount:M=D,onChange:O,onValidate:T=D}){let[s,u]=ue(!1),[c,w]=ue(!0),d=C(null),o=C(null),b=C(null),L=C(M),U=C(q),I=C(),i=C(t),f=se(m),Q=C(!1),B=C(!1);k(()=>{let p=Ne.init();return p.then(R=>(d.current=R)&&w(!1)).catch(R=>R?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",R)),()=>o.current?pe():p.cancel()}),l(()=>{let p=h(d.current,e||t||\"\",r||a||\"\",m||n||\"\");p!==o.current?.getModel()&&(y&&_.set(f,o.current?.saveViewState()),o.current?.setModel(p),y&&o.current?.restoreViewState(_.get(m)))},[m],s),l(()=>{o.current?.updateOptions(x)},[x],s),l(()=>{!o.current||t===void 0||(o.current.getOption(d.current.editor.EditorOption.readOnly)?o.current.setValue(t):t!==o.current.getValue()&&(B.current=!0,o.current.executeEdits(\"\",[{range:o.current.getModel().getFullModelRange(),text:t,forceMoveMarkers:!0}]),o.current.pushUndoStop(),B.current=!1))},[t],s),l(()=>{let p=o.current?.getModel();p&&a&&d.current?.editor.setModelLanguage(p,a)},[a],s),l(()=>{g!==void 0&&o.current?.revealLine(g)},[g],s),l(()=>{d.current?.editor.setTheme(E)},[E],s);let X=_e(()=>{if(!(!b.current||!d.current)&&!Q.current){U.current(d.current);let p=m||n,R=h(d.current,t||e||\"\",r||a||\"\",p||\"\");o.current=d.current?.editor.create(b.current,{model:R,automaticLayout:!0,...x},P),y&&o.current.restoreViewState(_.get(p)),d.current.editor.setTheme(E),g!==void 0&&o.current.revealLine(g),u(!0),Q.current=!0}},[e,r,n,t,a,m,x,P,y,E,g]);W(()=>{s&&L.current(o.current,d.current)},[s]),W(()=>{!c&&!s&&X()},[c,s,X]),i.current=t,W(()=>{s&&O&&(I.current?.dispose(),I.current=o.current?.onDidChangeModelContent(p=>{B.current||O(o.current.getValue(),p)}))},[s,O]),W(()=>{if(s){let p=d.current.editor.onDidChangeMarkers(R=>{let G=o.current.getModel()?.uri;if(G&&R.find(J=>J.path===G.path)){let J=d.current.editor.getModelMarkers({resource:G});T?.(J)}});return()=>{p?.dispose()}}return()=>{}},[s,T]);function pe(){I.current?.dispose(),V?y&&_.set(m,o.current.saveViewState()):o.current.getModel()?.dispose(),o.current.dispose()}return We.createElement(H,{width:z,height:F,isEditorReady:s,loading:N,_ref:b,className:j,wrapperProps:A})}var fe=Ve;var de=ze(fe);var Ft=de;export{we as DiffEditor,de as Editor,Ft as default,_t as loader,Le as useMonaco};\n//# sourceMappingURL=index.mjs.map"], "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "symbols", "sym", "_objectSpread2", "target", "i", "arguments", "source", "key", "obj", "value", "_arrayLikeToArray", "arr", "len", "arr2", "Array", "curry", "fn", "curried", "_this", "_len2", "args", "_key2", "_len3", "nextArgs", "_key3", "isObject", "isFunction", "<PERSON><PERSON><PERSON><PERSON>", "errorMessages", "type", "Error", "initial", "changes", "field", "property", "selector", "handler", "_handler", "extractChanges", "state", "<PERSON><PERSON><PERSON><PERSON>", "updateState", "didStateUpdate", "_handler$field", "_len", "_key", "config", "informAboutDeprecation", "console", "fns", "x", "y", "f", "CANCELATION_MESSAGE", "promise", "hasCanceled_", "wrappedPromise", "Promise", "resolve", "reject", "val", "_state$create2", "_arrayWithHoles", "undefined", "validators", "didUpdate", "update", "validate", "getChanges", "_iterableToArrayLimit", "Symbol", "_arr", "_n", "_d", "_e", "_s", "_i", "err", "_unsupportedIterableToArray", "o", "minLen", "n", "_nonIterableRest", "TypeError", "injectScripts", "script", "document", "getMonacoLoaderScript", "configure<PERSON><PERSON><PERSON>", "src", "_ref2", "loaderScript", "_ref3", "require", "window", "monaco", "storeMonacoInstance", "error", "wrapperPromise", "globalConfig", "_validators$config", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "sourceKeys", "sourceSymbolKeys", "merge", "_ref", "makeCancelable", "_ref4", "v", "$", "e", "H", "r", "t", "a", "m", "E", "k", "l", "D", "h", "De", "te", "be", "we", "g", "N", "P", "V", "z", "F", "j", "A", "q", "M", "O", "T", "s", "u", "c", "w", "d", "b", "I", "L", "U", "Le", "se", "_", "Map", "de", "Q", "B", "p", "R", "pe", "X", "G", "J", "Ft"], "mappings": "8VAeA,SAASA,EAAQC,CAAM,CAAEC,CAAc,EACrC,IAAIC,EAAOC,OAAO,IAAI,CAACH,GAEvB,GAAIG,OAAO,qBAAqB,CAAE,CAChC,IAAIC,EAAUD,OAAO,qBAAqB,CAACH,GACvCC,GAAgBG,CAAAA,EAAUA,EAAQ,MAAM,CAAC,SAAUC,CAAG,EACxD,OAAOF,OAAO,wBAAwB,CAACH,EAAQK,GAAK,UAAU,AAChE,EAAC,EACDH,EAAK,IAAI,CAAC,KAAK,CAACA,EAAME,EACxB,CAEA,OAAOF,CACT,CAEA,SAASI,EAAeC,CAAM,EAC5B,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAU,MAAM,CAAED,IAAK,CACzC,IAAIE,EAASD,AAAgB,MAAhBA,SAAS,CAACD,EAAE,CAAWC,SAAS,CAACD,EAAE,CAAG,CAAC,CAEhDA,CAAAA,EAAI,EACNT,EAAQI,OAAOO,GAAS,IAAM,OAAO,CAAC,SAAUC,CAAG,MAlChCC,EAAKD,EAAKE,EAAVD,EAmCDL,EAnCMI,EAmCEA,EAnCGE,EAmCEH,CAAM,CAACC,EAAI,CAlC1CA,KAAOC,EACTT,OAAO,cAAc,CAACS,EAAKD,EAAK,CAC9B,MAAOE,EACP,WAAY,GACZ,aAAc,GACd,SAAU,EACZ,GAEAD,CAAG,CAACD,EAAI,CAAGE,CA2BT,GACSV,OAAO,yBAAyB,CACzCA,OAAO,gBAAgB,CAACI,EAAQJ,OAAO,yBAAyB,CAACO,IAEjEX,EAAQI,OAAOO,IAAS,OAAO,CAAC,SAAUC,CAAG,EAC3CR,OAAO,cAAc,CAACI,EAAQI,EAAKR,OAAO,wBAAwB,CAACO,EAAQC,GAC7E,EAEJ,CAEA,OAAOJ,CACT,CAkFA,SAASO,EAAkBC,CAAG,CAAEC,CAAG,EAC7BA,CAAAA,AAAO,MAAPA,GAAeA,EAAMD,EAAI,MAAM,AAAD,GAAGC,CAAAA,EAAMD,EAAI,MAAM,AAAD,EAEpD,IAAK,IAAIP,EAAI,EAAGS,EAAO,AAAIC,MAAMF,GAAMR,EAAIQ,EAAKR,IAAKS,CAAI,CAACT,EAAE,CAAGO,CAAG,CAACP,EAAE,CAErE,OAAOS,CACT,CCxHA,SAAS,EAAQjB,CAAM,CAAEC,CAAc,EACrC,IAAIC,EAAOC,OAAO,IAAI,CAACH,GAEvB,GAAIG,OAAO,qBAAqB,CAAE,CAChC,IAAIC,EAAUD,OAAO,qBAAqB,CAACH,GACvCC,GAAgBG,CAAAA,EAAUA,EAAQ,MAAM,CAAC,SAAUC,CAAG,EACxD,OAAOF,OAAO,wBAAwB,CAACH,EAAQK,GAAK,UAAU,AAChE,EAAC,EACDH,EAAK,IAAI,CAAC,KAAK,CAACA,EAAME,EACxB,CAEA,OAAOF,CACT,CAEA,SAAS,EAAeK,CAAM,EAC5B,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAU,MAAM,CAAED,IAAK,CACzC,IAAIE,EAASD,AAAgB,MAAhBA,SAAS,CAACD,EAAE,CAAWC,SAAS,CAACD,EAAE,CAAG,CAAC,CAEhDA,CAAAA,EAAI,EACN,EAAQL,OAAOO,GAAS,IAAM,OAAO,CAAC,SAAUC,CAAG,MAlChCC,EAAKD,EAAKE,EAAVD,EAmCDL,EAnCMI,EAmCEA,EAnCGE,EAmCEH,CAAM,CAACC,EAAI,CAlC1CA,KAAOC,EACTT,OAAO,cAAc,CAACS,EAAKD,EAAK,CAC9B,MAAOE,EACP,WAAY,GACZ,aAAc,GACd,SAAU,EACZ,GAEAD,CAAG,CAACD,EAAI,CAAGE,CA2BT,GACSV,OAAO,yBAAyB,CACzCA,OAAO,gBAAgB,CAACI,EAAQJ,OAAO,yBAAyB,CAACO,IAEjE,EAAQP,OAAOO,IAAS,OAAO,CAAC,SAAUC,CAAG,EAC3CR,OAAO,cAAc,CAACI,EAAQI,EAAKR,OAAO,wBAAwB,CAACO,EAAQC,GAC7E,EAEJ,CAEA,OAAOJ,CACT,CAcA,SAASY,EAAMC,CAAE,EACf,OAAO,SAASC,IAGd,IAAK,IAFDC,EAAQ,IAAI,CAEPC,EAAQd,UAAU,MAAM,CAAEe,EAAO,AAAIN,MAAMK,GAAQE,EAAQ,EAAGA,EAAQF,EAAOE,IACpFD,CAAI,CAACC,EAAM,CAAGhB,SAAS,CAACgB,EAAM,CAGhC,OAAOD,EAAK,MAAM,EAAIJ,EAAG,MAAM,CAAGA,EAAG,KAAK,CAAC,IAAI,CAAEI,GAAQ,WACvD,IAAK,IAAIE,EAAQjB,UAAU,MAAM,CAAEkB,EAAW,AAAIT,MAAMQ,GAAQE,EAAQ,EAAGA,EAAQF,EAAOE,IACxFD,CAAQ,CAACC,EAAM,CAAGnB,SAAS,CAACmB,EAAM,CAGpC,OAAOP,EAAQ,KAAK,CAACC,EAAO,EAAE,CAAC,MAAM,CAACE,EAAMG,GAC9C,CACF,CACF,CAEA,SAASE,EAAShB,CAAK,EACrB,MAAO,EAAC,GAAE,QAAQ,CAAC,IAAI,CAACA,GAAO,QAAQ,CAAC,SAC1C,CAMA,SAASiB,EAAWjB,CAAK,EACvB,MAAO,AAAiB,YAAjB,OAAOA,CAChB,CA8CA,ICvIeO,EFqFSL,EAAKP,ECkDzBuB,EAAeZ,EAfnB,SAAoBa,CAAa,CAAEC,CAAI,EACrC,MAAM,AAAIC,MAAMF,CAAa,CAACC,EAAK,EAAID,EAAc,OAAU,CACjE,GAEoB,CAClB,kBAAmB,4BACnB,YAAa,oCACb,eAAgB,6CAChB,YAAa,4CACb,aAAc,qCACd,aAAc,gCACd,WAAY,gDACZ,YAAa,iGACb,QAAW,mDACb,KAvCA,SAAyBG,CAAO,CAAEC,CAAO,EAKvC,MAJI,CAACP,EAASO,IAAUL,EAAa,cACjC5B,OAAO,IAAI,CAACiC,GAAS,IAAI,CAAC,SAAUC,CAAK,MANvBrC,EAAQsC,EAO5B,OAPoBtC,EAOGmC,EAPKG,EAOID,GAN3BlC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACH,EAAQsC,EAOpD,IAAIP,EAAa,eACVK,CACT,IAEA,SAA0BG,CAAQ,EAC5B,CAACT,EAAWS,IAAWR,EAAa,eAC1C,IAEA,SAAyBS,CAAO,EAC1B,CAAEV,CAAAA,EAAWU,IAAYX,EAASW,EAAO,GAAIT,EAAa,eAC1DF,EAASW,IAAYrC,OAAO,MAAM,CAACqC,GAAS,IAAI,CAAC,SAAUC,CAAQ,EACrE,MAAO,CAACX,EAAWW,EACrB,IAAIV,EAAa,eACnB,IAEA,SAAyBI,CAAO,EAG9B,GAFI,CAACA,GAASJ,EAAa,qBACvB,CAACF,EAASM,IAAUJ,EAAa,eAhC9B,CAAC5B,OAAO,IAAI,CAiCPgC,GAjCa,MAAM,CAiCTJ,EAAa,iBACrC,EAoDA,SAASW,EAAeC,CAAK,CAAEC,CAAa,EAC1C,OAAOd,EAAWc,GAAiBA,EAAcD,EAAM,OAAO,EAAIC,CACpE,CAEA,SAASC,EAAYF,CAAK,CAAEP,CAAO,EAEjC,OADAO,EAAM,OAAO,CAAG,EAAe,EAAe,CAAC,EAAGA,EAAM,OAAO,EAAGP,GAC3DA,CACT,CAEA,SAASU,EAAeH,CAAK,CAAEH,CAAO,CAAEJ,CAAO,EAM7C,OALAN,EAAWU,GAAWA,EAAQG,EAAM,OAAO,EAAIxC,OAAO,IAAI,CAACiC,GAAS,OAAO,CAAC,SAAUC,CAAK,EACzF,IAAIU,EAEJ,OAAO,AAAsC,OAArCA,CAAAA,EAAiBP,CAAO,CAACH,EAAM,AAAD,GAAeU,AAAmB,KAAK,IAAxBA,EAA4B,KAAK,EAAIA,EAAe,IAAI,CAACP,EAASG,EAAM,OAAO,CAACN,EAAM,CAC7I,GACOD,CACT,CErJA,IAAI,EAAgB,CAClB,iBAAkB,uCAClB,WAAY,+CACZ,QAAW,8DACX,YAAa,+SACf,EACA,IAAI,EAAe,CD3CJhB,ECiCf,SAAoBY,CAAa,CAAEC,CAAI,EACrC,MAAM,AAAIC,MAAMF,CAAa,CAACC,EAAK,EAAID,EAAc,OAAU,CACjE,EDlCS,SAASX,IAGd,IAAK,IAFDC,EAAQ,IAAI,CAEP0B,EAAOvC,UAAU,MAAM,CAAEe,EAAO,AAAIN,MAAM8B,GAAOC,EAAO,EAAGA,EAAOD,EAAMC,IAC/EzB,CAAI,CAACyB,EAAK,CAAGxC,SAAS,CAACwC,EAAK,CAG9B,OAAOzB,EAAK,MAAM,EAAIJ,EAAG,MAAM,CAAGA,EAAG,KAAK,CAAC,IAAI,CAAEI,GAAQ,WACvD,IAAK,IAAID,EAAQd,UAAU,MAAM,CAAEkB,EAAW,AAAIT,MAAMK,GAAQE,EAAQ,EAAGA,EAAQF,EAAOE,IACxFE,CAAQ,CAACF,EAAM,CAAGhB,SAAS,CAACgB,EAAM,CAGpC,OAAOJ,EAAQ,KAAK,CAACC,EAAO,EAAE,CAAC,MAAM,CAACE,EAAMG,GAC9C,CACF,GC4BmC,KAlCrC,SAAwBuB,CAAM,MCTZrC,EDWhB,GADI,CAACqC,GAAQ,EAAa,oBCVVrC,EDWFqC,GCVP,EAAC,GAAE,QAAQ,CAAC,IAAI,CAACrC,GAAO,QAAQ,CAAC,UDUjB,EAAa,qBAEpC,AAAIqC,EAAO,IAAI,EACbC,AAeJ,WACEC,QAAQ,IAAI,CAAC,EAAc,WAAW,CACxC,IAhBW,CACL,MAAO,CACL,GAAIF,EAAO,IAAI,CAAC,UAAU,AAC5B,CACF,GAGKA,CACT,EEXA,EAZc,WACZ,IAAK,IAAIF,EAAOvC,UAAU,MAAM,CAAE4C,EAAM,AAAInC,MAAM8B,GAAOC,EAAO,EAAGA,EAAOD,EAAMC,IAC9EI,CAAG,CAACJ,EAAK,CAAGxC,SAAS,CAACwC,EAAK,CAG7B,OAAO,SAAUK,CAAC,EAChB,OAAOD,EAAI,WAAW,CAAC,SAAUE,CAAC,CAAEC,CAAC,EACnC,OAAOA,EAAED,EACX,EAAGD,EACL,CACF,ECTIG,EAAsB,CACxB,KAAM,cACN,IAAK,gCACP,EAeA,EAbA,SAAwBC,CAAO,EAC7B,IAAIC,EAAe,GACfC,EAAiB,IAAIC,QAAQ,SAAUC,CAAO,CAAEC,CAAM,EACxDL,EAAQ,IAAI,CAAC,SAAUM,CAAG,EACxB,OAAOL,EAAeI,EAAON,GAAuBK,EAAQE,EAC9D,GACAN,EAAQ,KAAQ,CAACK,EACnB,GACA,OAAOH,EAAe,MAAM,CAAG,WAC7B,OAAOD,EAAe,EACxB,EAAGC,CACL,ECPA,IAOIK,GPoEyBzD,EOpEsB,EPqE1C0D,AAGT,SAAyBnD,CAAG,EAC1B,GAAIG,MAAM,OAAO,CAACH,GAAM,OAAOA,CACjC,EANwBA,EO3EJ,ANkLR,EACV,OA9CF,SAAgBoB,CAAO,EACrB,IAAIK,EAAU/B,UAAU,MAAM,CAAG,GAAKA,AAAiB0D,KAAAA,IAAjB1D,SAAS,CAAC,EAAE,CAAiBA,SAAS,CAAC,EAAE,CAAG,CAAC,EACnF2D,EAAmBjC,GACnBiC,EAAmB5B,GACnB,IAAIG,EAAQ,CACV,QAASR,CACX,EACIkC,EAAYlD,EAAM2B,GAAgBH,EAAOH,GACzC8B,EAASnD,EAAM0B,GAAaF,GAC5B4B,EAAWpD,KAA0BgB,GACrCqC,EAAarD,EAAMuB,GAAgBC,GAcvC,MAAO,CAZP,WACE,IAAIJ,EAAW9B,UAAU,MAAM,CAAG,GAAKA,AAAiB0D,KAAAA,IAAjB1D,SAAS,CAAC,EAAE,CAAiBA,SAAS,CAAC,EAAE,CAAG,SAAUkC,CAAK,EAChG,OAAOA,CACT,EAEA,OADAyB,EAAoB7B,GACbA,EAASI,EAAM,OAAO,CAC/B,EAEA,SAAkBC,CAAa,EAC7B,AAnHJ,YACE,IAAK,IAAII,EAAOvC,UAAU,MAAM,CAAE4C,EAAM,AAAInC,MAAM8B,GAAOC,EAAO,EAAGA,EAAOD,EAAMC,IAC9EI,CAAG,CAACJ,EAAK,CAAGxC,SAAS,CAACwC,EAAK,CAG7B,OAAO,SAAUK,CAAC,EAChB,OAAOD,EAAI,WAAW,CAAC,SAAUE,CAAC,CAAEC,CAAC,EACnC,OAAOA,EAAED,EACX,EAAGD,EACL,CACF,GAyGYe,EAAWC,EAAQC,EAAUC,GAAY5B,EACnD,EAE2B,AAC7B,CAsBA,GMpLoB,MAAY,CAAC,CAC/B,OCXW,CACX,MAAO,CACL,GAAI,0DACN,CACF,EDQE,cAAe,GACf,QAAS,KACT,OAAQ,KACR,OAAQ,IACV,KPsEiC6B,AAOjC,SAA+B1D,CAAG,CAAEP,CAAC,EACnC,GAAI,AAAkB,aAAlB,OAAOkE,QAA4BA,OAAO,QAAQ,IAAIvE,OAAOY,IACjE,IAAI4D,EAAO,EAAE,CACTC,EAAK,GACLC,EAAK,GACLC,EAAKX,KAAAA,EAET,GAAI,CACF,IAAK,IAAiCY,EAA7BC,EAAKjE,CAAG,CAAC2D,OAAO,QAAQ,CAAC,GAAQ,CAAEE,CAAAA,EAAK,AAACG,CAAAA,EAAKC,EAAG,IAAI,EAAC,EAAG,IAAI,AAAD,IACnEL,EAAK,IAAI,CAACI,EAAG,KAAK,EAEdvE,CAAAA,GAAKmE,EAAK,MAAM,GAAKnE,GAH8CoE,EAAK,IAKhF,CAAE,MAAOK,EAAK,CACZJ,EAAK,GACLC,EAAKG,CACP,QAAU,CACR,GAAI,CACE,CAACL,GAAMI,AAAgB,MAAhBA,EAAG,MAAS,EAAUA,EAAG,MAAS,EAC/C,QAAU,CACR,GAAIH,EAAI,MAAMC,CAChB,CACF,CAEA,OAAOH,EACT,EAhCuD5D,EOrEJ,IPqEemE,AAkClE,SAAqCC,CAAC,CAAEC,CAAM,EAC5C,GAAKD,GACL,GAAI,AAAa,UAAb,OAAOA,EAAgB,OAAOrE,EAAkBqE,EAAGC,GACvD,IAAIC,EAAIlF,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAACgF,GAAG,KAAK,CAAC,EAAG,IAEnD,GADU,WAANE,GAAkBF,EAAE,WAAW,EAAEE,CAAAA,EAAIF,EAAE,WAAW,CAAC,IAAI,AAAD,EACtDE,AAAM,QAANA,GAAeA,AAAM,QAANA,EAAa,OAAOnE,MAAM,IAAI,CAACiE,GAClD,GAAIE,AAAM,cAANA,GAAqB,2CAA2C,IAAI,CAACA,GAAI,OAAOvE,EAAkBqE,EAAGC,GAC3G,EAzC8FrE,EAAKP,IAAM8E,AAmDzG,WACE,MAAM,AAAIC,UAAU,4IACtB,KOzHI,EAAWtB,CAAc,CAAC,EAAE,CAC5B,EAAWA,CAAc,CAAC,EAAE,CAiEhC,SAASuB,EAAcC,CAAM,EAC3B,OAAOC,SAAS,IAAI,CAAC,WAAW,CAACD,EACnC,CAkBA,SAASE,EAAsBC,CAAe,EAC5C,IAXoBC,EAChBJ,EAUA9C,EAAQ,EAAS,SAAUmD,CAAK,EAGlC,MAAO,CACL,OAHWA,EAAM,MAAM,CAIvB,OAHWA,EAAM,MAAM,AAIzB,CACF,GACA,IAAIC,GAnBgBF,EAmBY,GAAG,MAAM,CAAClD,EAAM,MAAM,CAAC,KAAK,CAAC,EAAE,CAAE,cAlB7D8C,EAASC,SAAS,aAAa,CAAC,UAC7BG,GAAQJ,CAAAA,EAAO,GAAG,CAAGI,CAAE,EAAIJ,GAwBlC,OALAM,EAAa,MAAM,CAAG,WACpB,OAAOH,GACT,EAEAG,EAAa,OAAO,CAAGpD,EAAM,MAAM,CAC5BoD,CACT,CAMA,SAAS,IACP,IAAIpD,EAAQ,EAAS,SAAUqD,CAAK,EAIlC,MAAO,CACL,OAJWA,EAAM,MAAM,CAKvB,QAJYA,EAAM,OAAO,CAKzB,OAJWA,EAAM,MAAM,AAKzB,CACF,GACIC,EAAUC,OAAO,OAAO,CAE5BD,EAAQ,MAAM,CAACtD,EAAM,MAAM,EAE3BsD,EAAQ,CAAC,wBAAwB,CAAE,SAAUE,CAAM,EACjDC,EAAoBD,GACpBxD,EAAM,OAAO,CAACwD,EAChB,EAAG,SAAUE,CAAK,EAChB1D,EAAM,MAAM,CAAC0D,EACf,EACF,CAMA,SAASD,EAAoBD,CAAM,EAC7B,CAAC,IAAW,MAAM,EACpB,EAAS,CACP,OAAQA,CACV,EAEJ,CAeA,IAAIG,EAAiB,IAAIzC,QAAQ,SAAUC,CAAO,CAAEC,CAAM,EACxD,OAAO,EAAS,CACd,QAASD,EACT,OAAQC,CACV,EACF,GAOA,EANa,CACX,OA5JF,SAAgBwC,CAAY,EAC1B,IAAIC,EAAqB,EAAkBD,GACvCJ,EAASK,EAAmB,MAAM,CAClCtD,EAASuD,APmCf,SAAkC/F,CAAM,CAAEgG,CAAQ,EAChD,GAAIhG,AAAU,MAAVA,EAAgB,MAAO,CAAC,EAE5B,IAEIC,EAAKH,EAFLD,EAASoG,AAlBf,SAAuCjG,CAAM,CAAEgG,CAAQ,EACrD,GAAIhG,AAAU,MAAVA,EAAgB,MAAO,CAAC,EAC5B,IAEIC,EAAKH,EAFLD,EAAS,CAAC,EACVqG,EAAazG,OAAO,IAAI,CAACO,GAG7B,IAAKF,EAAI,EAAGA,EAAIoG,EAAW,MAAM,CAAEpG,IACjCG,EAAMiG,CAAU,CAACpG,EAAE,EACfkG,CAAAA,EAAS,OAAO,CAAC/F,IAAQ,IAC7BJ,CAAAA,CAAM,CAACI,EAAI,CAAGD,CAAM,CAACC,EAAI,AAAD,EAG1B,OAAOJ,CACT,EAK6CG,EAAQgG,GAInD,GAAIvG,OAAO,qBAAqB,CAAE,CAChC,IAAI0G,EAAmB1G,OAAO,qBAAqB,CAACO,GAEpD,IAAKF,EAAI,EAAGA,EAAIqG,EAAiB,MAAM,CAAErG,IAAK,CAE5C,GADAG,EAAMkG,CAAgB,CAACrG,EAAE,EACrBkG,CAAAA,EAAS,OAAO,CAAC/F,IAAQ,GACxBR,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAACO,EAAQC,IACxDJ,CAAAA,CAAM,CAACI,EAAI,CAAGD,CAAM,CAACC,EAAI,AAAD,CAC1B,CACF,CAEA,OAAOJ,CACT,EOtDwCiG,EAAoB,CAAC,SAAS,EAEpE,EAAS,SAAU7D,CAAK,EACtB,MAAO,CACL,OAAQ,AE/Bd,SAASmE,EAAMvG,CAAM,CAAEG,CAAM,EAQ3B,OAPAP,OAAO,IAAI,CAACO,GAAQ,OAAO,CAAC,SAAUC,CAAG,EACnCD,CAAM,CAACC,EAAI,WAAYR,QACrBI,CAAM,CAACI,EAAI,EACbR,OAAO,MAAM,CAACO,CAAM,CAACC,EAAI,CAAEmG,EAAMvG,CAAM,CAACI,EAAI,CAAED,CAAM,CAACC,EAAI,EAG/D,GACOL,EAAeA,EAAe,CAAC,EAAGC,GAASG,EACpD,EFsBoBiC,EAAM,MAAM,CAAEO,GAC5B,OAAQiD,CACV,CACF,EACF,EAkJE,KA3IF,WACE,IAAIxD,EAAQ,EAAS,SAAUoE,CAAI,EAIjC,MAAO,CACL,OAJWA,EAAK,MAAM,CAKtB,cAJkBA,EAAK,aAAa,CAKpC,QAJYA,EAAK,OAAO,AAK1B,CACF,GAEA,GAAI,CAACpE,EAAM,aAAa,CAAE,CAKxB,GAJA,EAAS,CACP,cAAe,EACjB,GAEIA,EAAM,MAAM,CAEd,OADAA,EAAM,OAAO,CAACA,EAAM,MAAM,EACnBqE,EAAeV,GAGxB,GAAIJ,OAAO,MAAM,EAAIA,OAAO,MAAM,CAAC,MAAM,CAGvC,OAFAE,EAAoBF,OAAO,MAAM,EACjCvD,EAAM,OAAO,CAACuD,OAAO,MAAM,EACpBc,EAAeV,GAGxB,EAAQd,EAAeG,GAAuB,EAChD,CAEA,OAAOqB,EAAeV,EACxB,EA4GE,oBAhBF,WACE,OAAO,EAAS,SAAUW,CAAK,EAE7B,OADaA,EAAM,MAAM,AAE3B,EACF,CAYA,E,cGzL0WC,EAAlH,CAAC,QAAQ,CAAC,QAAQ,OAAO,SAAS,WAAW,UAAU,SAAS,EAAE,UAAU,CAAC,MAAM,MAAM,EAAE,KAAK,CAAC,QAAQ,MAAM,CAAC,IAA+C,CAAC,QAAQ,OAAO,OAAO,OAAO,MAAM,OAAO,eAAe,SAAS,WAAW,QAAQ,EAAoGC,EAA5F,SAAY,CAAC,SAASC,CAAC,CAAC,EAAE,OAAO,eAAgB,CAAC,MAAM,CAAC,KAAK,EAAY,EAAEA,EAAE,EAAyTC,EAAE,WAAzS,SAAY,CAAC,MAAMD,CAAC,CAAC,OAAOE,CAAC,CAAC,cAAcjC,CAAC,CAAC,QAAQkC,CAAC,CAAC,KAAKC,CAAC,CAAC,UAAUC,CAAC,CAAC,aAAaC,CAAC,CAAC,EAAE,OAAO,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,GAAGR,EAAE,OAAO,CAAC,MAAME,EAAE,OAAOE,CAAC,EAAE,GAAGI,CAAC,EAAE,CAACrC,GAAG,eAAe,CAAC8B,EAAE,KAAKI,GAAG,eAAe,CAAC,MAAM,CAAC,IAAIC,EAAE,MAAM,CAAC,GAAGN,EAAE,SAAS,CAAC,GAAG,CAAC7B,GAAG6B,EAAE,IAAI,EAAE,UAAUO,CAAC,GAAG,GAAuFE,EAA5B,SAAYP,CAAC,EAAE,gBAAGA,EAAE,EAAE,CAAC,EAA0IQ,EAAhF,SAAYR,CAAC,CAACE,CAAC,CAACjC,EAAE,CAAC,CAAC,EAAE,IAAIkC,EAAE,aAAG,CAAC,GAAG,gBAAGA,EAAE,OAAO,EAAE,CAAClC,EAAE,KAAKkC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAEH,EAAEE,EAAE,EAAU,SAASO,IAAI,CAAC,SAASC,EAAEV,CAAC,CAACE,CAAC,CAACjC,CAAC,CAACkC,CAAC,EAAE,OAAOQ,AAAqB,SAAYX,CAAC,CAACE,CAAC,EAAE,OAAOF,EAAE,MAAM,CAAC,QAAQ,CAACY,EAAGZ,EAAEE,GAAG,EAApEF,EAAEG,IAAIU,AAA+D,SAAYb,CAAC,CAACE,CAAC,CAACjC,CAAC,CAACkC,CAAC,EAAE,OAAOH,EAAE,MAAM,CAAC,WAAW,CAACE,EAAEjC,EAAEkC,EAAES,EAAGZ,EAAEG,GAAG,KAAK,EAAE,EAAlIH,EAAEE,EAAEjC,EAAEkC,EAAE,CAA2H,SAASS,EAAGZ,CAAC,CAACE,CAAC,EAAE,OAAOF,EAAE,GAAG,CAAC,KAAK,CAACE,EAAE,CAA+lE,IAAIY,EAAG,WAArmE,SAAY,CAAC,SAASd,CAAC,CAAC,SAASE,CAAC,CAAC,SAASjC,CAAC,CAAC,iBAAiBkC,CAAC,CAAC,iBAAiBC,CAAC,CAAC,kBAAkBC,CAAC,CAAC,kBAAkBC,CAAC,CAAC,yBAAyBS,EAAE,CAAC,CAAC,CAAC,yBAAyBC,EAAE,CAAC,CAAC,CAAC,MAAM9E,EAAE,OAAO,CAAC,QAAQ+E,EAAE,YAAY,CAAC,QAAQ9E,EAAE,CAAC,CAAC,CAAC,OAAO+E,EAAE,MAAM,CAAC,MAAMC,EAAE,MAAM,CAAC,UAAUC,CAAC,CAAC,aAAaC,EAAE,CAAC,CAAC,CAAC,YAAYC,EAAEb,CAAC,CAAC,QAAQc,EAAEd,CAAC,CAAC,EAAE,GAAG,CAACe,EAAEC,EAAE,CAAC,eAAG,CAAC,GAAG,CAACC,EAAEC,EAAE,CAAC,eAAG,CAAC,GAAGC,EAAE,aAAE,MAAMC,EAAE,aAAE,MAAMC,EAAE,aAAE,MAAMC,EAAE,aAAER,GAAGxD,EAAE,aAAEuD,GAAGU,EAAE,aAAE,CAAC,EAAGzB,CAAAA,EAAE,KAAK,IAAInH,EAAE,MAAO,GAAG,OAAOA,EAAE,IAAI,CAACgD,GAAG,AAACyF,CAAAA,EAAE,OAAO,CAACzF,CAAAA,GAAIuF,EAAE,CAAC,IAAI,KAAK,CAACvF,GAAGA,GAAG,OAAO,eAAeJ,QAAQ,KAAK,CAAC,gCAAgCI,IAAI,IAAIwF,EAAE,OAAO,CAACK,AAAg0C,WAAa,IAAI7I,EAAEwI,EAAE,OAAO,EAAE,UAAWb,CAAAA,GAAG3H,GAAG,UAAU,UAAU4H,GAAG5H,GAAG,UAAU,UAAUwI,EAAE,OAAO,EAAE,SAAS,IAA76CxI,EAAE,MAAM,EAAE,GAAGoH,EAAE,KAAK,GAAGoB,EAAE,OAAO,EAAEC,EAAE,OAAO,CAAC,CAAC,IAAIzI,EAAEwI,EAAE,OAAO,CAAC,iBAAiB,GAAGxF,EAAEsE,EAAEmB,EAAE,OAAO,CAAC7B,GAAG,GAAGG,GAAGlC,GAAG,OAAOoC,GAAG,GAAIjE,CAAAA,IAAIhD,EAAE,QAAQ,IAAIA,EAAE,QAAQ,CAACgD,EAAE,CAAC,EAAE,CAACiE,EAAE,CAACmB,GAAGhB,EAAE,KAAK,GAAGoB,EAAE,OAAO,EAAEC,EAAE,OAAO,CAAC,CAAC,IAAIzI,EAAEwI,EAAE,OAAO,CAAC,iBAAiB,GAAGxF,EAAEsE,EAAEmB,EAAE,OAAO,CAAC3B,GAAG,GAAGE,GAAGnC,GAAG,OAAOqC,GAAG,GAAIlE,CAAAA,IAAIhD,EAAE,QAAQ,IAAIA,EAAE,QAAQ,CAACgD,EAAE,CAAC,EAAE,CAACkE,EAAE,CAACkB,GAAGhB,EAAE,KAAK,IAAIpH,EAAEwI,EAAE,OAAO,CAAC,iBAAiB,EAAGxI,CAAAA,EAAE,SAAS,CAACyI,EAAE,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAEzI,EAAE,QAAQ,CAAC8G,GAAG,IAAIA,IAAI9G,EAAE,QAAQ,IAAKA,CAAAA,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,MAAMA,EAAE,QAAQ,GAAG,iBAAiB,GAAG,KAAK8G,GAAG,GAAG,iBAAiB,CAAC,CAAC,EAAE,EAAE9G,EAAE,YAAY,EAAC,CAAE,EAAE,CAAC8G,EAAE,CAACsB,GAAGhB,EAAE,KAAKoB,EAAE,OAAO,EAAE,YAAY,SAAS,SAAS5B,GAAG,GAAG,EAAE,CAACA,EAAE,CAACwB,GAAGhB,EAAE,KAAK,GAAG,CAAC,SAASpH,CAAC,CAAC,SAASgD,CAAC,CAAC,CAACwF,EAAE,OAAO,CAAC,QAAQ,EAAGC,CAAAA,EAAE,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAACzI,EAAE+G,GAAGlC,GAAG,QAAQ4D,EAAE,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAACzF,EAAEgE,GAAGnC,GAAG,OAAO,EAAE,CAACA,EAAEkC,EAAEC,EAAE,CAACoB,GAAGhB,EAAE,KAAKqB,EAAE,OAAO,EAAE,OAAO,SAAS3F,EAAE,EAAE,CAACA,EAAE,CAACsF,GAAGhB,EAAE,KAAKoB,EAAE,OAAO,EAAE,cAAczF,EAAE,EAAE,CAACA,EAAE,CAACqF,GAAG,IAAIU,EAAE,kBAAG,KAAK,GAAG,CAACL,EAAE,OAAO,CAAC,OAAO9D,EAAE,OAAO,CAAC8D,EAAE,OAAO,EAAE,IAAIzI,EAAEsH,EAAEmB,EAAE,OAAO,CAAC7B,GAAG,GAAGG,GAAGlC,GAAG,OAAOoC,GAAG,IAAIjE,EAAEsE,EAAEmB,EAAE,OAAO,CAAC3B,GAAG,GAAGE,GAAGnC,GAAG,OAAOqC,GAAG,GAAIsB,CAAAA,EAAE,OAAO,EAAE,SAAS,CAAC,SAASxI,EAAE,SAASgD,CAAC,EAAE,EAAE,CAAC6B,EAAEiC,EAAEE,EAAEJ,EAAEG,EAAEE,EAAEC,EAAE,EAAE6B,EAAE,kBAAG,KAAK,CAACH,EAAE,OAAO,EAAEF,EAAE,OAAO,EAAGF,CAAAA,EAAE,OAAO,CAACC,EAAE,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAACC,EAAE,OAAO,CAAC,CAAC,gBAAgB,CAAC,EAAE,GAAG3F,CAAC,GAAG+F,IAAIL,EAAE,OAAO,EAAE,OAAO,SAAS3F,GAAGuF,EAAE,CAAC,GAAGO,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC7F,EAAED,EAAEgG,EAAE,QAAE,gBAAG,KAAKV,GAAGO,EAAE,OAAO,CAACH,EAAE,OAAO,CAACC,EAAE,OAAO,CAAC,EAAE,CAACL,EAAE,EAAE,gBAAG,KAAK,AAACE,GAAIF,GAAGW,GAAG,EAAE,CAACT,EAAEF,EAAEW,EAAE,EAA2H,eAAgB,CAAClC,EAAE,CAAC,MAAMkB,EAAE,OAAOD,EAAE,cAAcM,EAAE,QAAQP,EAAE,KAAKa,EAAE,UAAUV,EAAE,aAAaC,CAAC,EAAE,GAA2Oe,EAA1I,WAAc,GAAG,CAACpC,EAAEE,EAAE,CAAC,eAAG,qBAAsB,IAAI,OAAOK,EAAE,KAAK,IAAItC,EAAE,OAAO+B,GAAI/B,AAAYA,CAAZA,EAAE,MAAO,EAAC,EAAI,IAAI,CAACkC,IAAID,EAAEC,EAAE,GAAI,IAAIlC,GAAG,QAAQ,GAAG+B,CAAC,EAA2RqC,EAAzE,SAAYrC,CAAC,EAAE,IAAIE,EAAE,eAAK,MAAO,gBAAG,KAAKA,EAAE,OAAO,CAACF,CAAC,EAAE,CAACA,EAAE,EAAEE,EAAE,OAAO,EAAeoC,EAAE,IAAIC,IAAu2EC,EAAG,WAAt2E,SAAY,CAAC,aAAaxC,CAAC,CAAC,gBAAgBE,CAAC,CAAC,YAAYjC,CAAC,CAAC,MAAMkC,CAAC,CAAC,SAASC,CAAC,CAAC,KAAKC,CAAC,CAAC,MAAMC,EAAE,OAAO,CAAC,KAAKS,CAAC,CAAC,QAAQC,EAAE,YAAY,CAAC,QAAQ9E,EAAE,CAAC,CAAC,CAAC,iBAAiB+E,EAAE,CAAC,CAAC,CAAC,cAAc9E,EAAE,CAAC,CAAC,CAAC,iBAAiB+E,EAAE,CAAC,CAAC,CAAC,MAAMC,EAAE,MAAM,CAAC,OAAOC,EAAE,MAAM,CAAC,UAAUC,CAAC,CAAC,aAAaC,EAAE,CAAC,CAAC,CAAC,YAAYC,EAAEd,CAAC,CAAC,QAAQe,EAAEf,CAAC,CAAC,SAASgB,CAAC,CAAC,WAAWC,EAAEjB,CAAC,CAAC,EAAE,GAAG,CAACkB,EAAEC,EAAE,CAAC,eAAG,CAAC,GAAG,CAACC,EAAEC,EAAE,CAAC,eAAG,CAAC,GAAGC,EAAE,aAAE,MAAMhE,EAAE,aAAE,MAAMiE,EAAE,aAAE,MAAME,EAAE,aAAEV,GAAGW,EAAE,aAAEZ,GAAGU,EAAE,eAAI7I,EAAE,aAAE+G,GAAG/D,EAAEiG,EAAGhC,GAAGoC,EAAE,aAAE,CAAC,GAAGC,EAAE,aAAE,CAAC,EAAGnC,CAAAA,EAAE,KAAK,IAAIoC,EAAE,MAAO,GAAG,OAAOA,EAAE,IAAI,CAACC,GAAG,AAACb,CAAAA,EAAE,OAAO,CAACa,CAAAA,GAAId,EAAE,CAAC,IAAI,KAAK,CAACc,GAAGA,GAAG,OAAO,eAAe5G,QAAQ,KAAK,CAAC,gCAAgC4G,IAAI,IAAI7E,EAAE,OAAO,CAAC8E,AAAyhD,WAAcZ,EAAE,OAAO,EAAE,UAAUf,EAAE/E,GAAGmG,EAAE,GAAG,CAACjC,EAAEtC,EAAE,OAAO,CAAC,aAAa,IAAIA,EAAE,OAAO,CAAC,QAAQ,IAAI,UAAUA,EAAE,OAAO,CAAC,OAAO,EAAE,IAAlpD4E,EAAE,MAAM,EAAE,GAAGnC,EAAE,KAAK,IAAImC,EAAEjC,EAAEqB,EAAE,OAAO,CAAC/B,GAAGG,GAAG,GAAGD,GAAGE,GAAG,GAAGC,GAAGpC,GAAG,GAAI0E,CAAAA,IAAI5E,EAAE,OAAO,EAAE,YAAa5B,CAAAA,GAAGmG,EAAE,GAAG,CAAClG,EAAE2B,EAAE,OAAO,EAAE,iBAAiBA,EAAE,OAAO,EAAE,SAAS4E,GAAGxG,GAAG4B,EAAE,OAAO,EAAE,iBAAiBuE,EAAE,GAAG,CAACjC,GAAE,CAAE,EAAE,CAACA,EAAE,CAACsB,GAAGnB,EAAE,KAAKzC,EAAE,OAAO,EAAE,cAAc7B,EAAE,EAAE,CAACA,EAAE,CAACyF,GAAGnB,EAAE,KAAK,AAACzC,EAAE,OAAO,EAAEoC,AAAI,KAAK,IAATA,GAAapC,CAAAA,EAAE,OAAO,CAAC,SAAS,CAACgE,EAAE,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAEhE,EAAE,OAAO,CAAC,QAAQ,CAACoC,GAAGA,IAAIpC,EAAE,OAAO,CAAC,QAAQ,IAAK2E,CAAAA,EAAE,OAAO,CAAC,CAAC,EAAE3E,EAAE,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,MAAMA,EAAE,OAAO,CAAC,QAAQ,GAAG,iBAAiB,GAAG,KAAKoC,EAAE,iBAAiB,CAAC,CAAC,EAAE,EAAEpC,EAAE,OAAO,CAAC,YAAY,GAAG2E,EAAE,OAAO,CAAC,CAAC,EAAC,CAAE,EAAE,CAACvC,EAAE,CAACwB,GAAGnB,EAAE,KAAK,IAAImC,EAAE5E,EAAE,OAAO,EAAE,UAAW4E,CAAAA,GAAGvC,GAAG2B,EAAE,OAAO,EAAE,OAAO,iBAAiBY,EAAEvC,EAAE,EAAE,CAACA,EAAE,CAACuB,GAAGnB,EAAE,KAAKO,AAAI,KAAK,IAATA,GAAYhD,EAAE,OAAO,EAAE,WAAWgD,EAAE,EAAE,CAACA,EAAE,CAACY,GAAGnB,EAAE,KAAKuB,EAAE,OAAO,EAAE,OAAO,SAASzB,EAAE,EAAE,CAACA,EAAE,CAACqB,GAAG,IAAImB,EAAE,kBAAG,KAAK,GAAG,CAAE,EAACd,EAAE,OAAO,EAAE,CAACD,EAAE,OAAO,AAAD,GAAI,CAACU,EAAE,OAAO,CAAC,CAACN,EAAE,OAAO,CAACJ,EAAE,OAAO,EAAE,IAAIY,EAAEtC,GAAGpC,EAAE2E,EAAElC,EAAEqB,EAAE,OAAO,CAAC5B,GAAGH,GAAG,GAAGE,GAAGE,GAAG,GAAGuC,GAAG,GAAI5E,CAAAA,EAAE,OAAO,CAACgE,EAAE,OAAO,EAAE,OAAO,OAAOC,EAAE,OAAO,CAAC,CAAC,MAAMY,EAAE,gBAAgB,CAAC,EAAE,GAAG1G,CAAC,EAAE+E,GAAG9E,GAAG4B,EAAE,OAAO,CAAC,gBAAgB,CAACuE,EAAE,GAAG,CAACK,IAAIZ,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAACzB,GAAGS,AAAI,KAAK,IAATA,GAAYhD,EAAE,OAAO,CAAC,UAAU,CAACgD,GAAGa,EAAE,CAAC,GAAGa,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAACzC,EAAEE,EAAEjC,EAAEkC,EAAEC,EAAEC,EAAEnE,EAAE+E,EAAE9E,EAAEmE,EAAES,EAAE,QAAE,gBAAE,KAAKY,GAAGO,EAAE,OAAO,CAACnE,EAAE,OAAO,CAACgE,EAAE,OAAO,CAAC,EAAE,CAACJ,EAAE,EAAE,gBAAE,KAAK,AAACE,GAAIF,GAAGmB,GAAG,EAAE,CAACjB,EAAEF,EAAEmB,EAAE,EAAE1J,EAAE,OAAO,CAAC+G,EAAE,gBAAE,KAAKwB,GAAGF,GAAIQ,CAAAA,EAAE,OAAO,EAAE,UAAUA,EAAE,OAAO,CAAClE,EAAE,OAAO,EAAE,wBAAwB4E,IAAID,EAAE,OAAO,EAAEjB,EAAE1D,EAAE,OAAO,CAAC,QAAQ,GAAG4E,EAAE,EAAC,CAAE,EAAE,CAAChB,EAAEF,EAAE,EAAE,gBAAE,KAAK,GAAGE,EAAE,CAAC,IAAIgB,EAAEZ,EAAE,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAACa,IAAI,IAAIG,EAAEhF,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI,GAAGgF,GAAGH,EAAE,IAAI,CAACI,GAAGA,EAAE,IAAI,GAAGD,EAAE,IAAI,EAAE,CAAC,IAAIC,EAAEjB,EAAE,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,SAASgB,CAAC,GAAGrB,IAAIsB,EAAE,CAAC,GAAG,MAAM,KAAKL,GAAG,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAChB,EAAED,EAAE,EAAwI,eAAgB,CAACzB,EAAE,CAAC,MAAMkB,EAAE,OAAOC,EAAE,cAAcO,EAAE,QAAQX,EAAE,KAAKgB,EAAE,UAAUX,EAAE,aAAaC,CAAC,EAAE,GAA6B2B,EAAGT,C"}