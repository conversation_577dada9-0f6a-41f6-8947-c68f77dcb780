{"version": 3, "file": "static/js/async/web-streams-polyfill.9a4db82d.js", "sources": ["webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@mattiasbuelens+web-streams-adapter@0.1.0/node_modules/@mattiasbuelens/web-streams-adapter/dist/web-streams-adapter.mjs", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/web-streams-polyfill@3.3.2_patch_hash=bpavmlj77dxbzumakoebasbssm/node_modules/web-streams-polyfill/dist/ponyfill.mjs"], "sourcesContent": ["/*! *****************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise */\n\nvar extendStatics = function(d, b) {\n    extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n    return extendStatics(d, b);\n};\n\nfunction __extends(d, b) {\n    if (typeof b !== \"function\" && b !== null)\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() { this.constructor = d; }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nfunction assert(test) {\n    if (!test) {\n        throw new TypeError('Assertion failed');\n    }\n}\n\nfunction noop() {\n    return;\n}\nfunction typeIsObject(x) {\n    return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nfunction isStreamConstructor(ctor) {\n    if (typeof ctor !== 'function') {\n        return false;\n    }\n    var startCalled = false;\n    try {\n        new ctor({\n            start: function () {\n                startCalled = true;\n            }\n        });\n    }\n    catch (e) {\n        // ignore\n    }\n    return startCalled;\n}\nfunction isReadableStream(readable) {\n    if (!typeIsObject(readable)) {\n        return false;\n    }\n    if (typeof readable.getReader !== 'function') {\n        return false;\n    }\n    return true;\n}\nfunction isReadableStreamConstructor(ctor) {\n    if (!isStreamConstructor(ctor)) {\n        return false;\n    }\n    if (!isReadableStream(new ctor())) {\n        return false;\n    }\n    return true;\n}\nfunction isWritableStream(writable) {\n    if (!typeIsObject(writable)) {\n        return false;\n    }\n    if (typeof writable.getWriter !== 'function') {\n        return false;\n    }\n    return true;\n}\nfunction isWritableStreamConstructor(ctor) {\n    if (!isStreamConstructor(ctor)) {\n        return false;\n    }\n    if (!isWritableStream(new ctor())) {\n        return false;\n    }\n    return true;\n}\nfunction isTransformStream(transform) {\n    if (!typeIsObject(transform)) {\n        return false;\n    }\n    if (!isReadableStream(transform.readable)) {\n        return false;\n    }\n    if (!isWritableStream(transform.writable)) {\n        return false;\n    }\n    return true;\n}\nfunction isTransformStreamConstructor(ctor) {\n    if (!isStreamConstructor(ctor)) {\n        return false;\n    }\n    if (!isTransformStream(new ctor())) {\n        return false;\n    }\n    return true;\n}\nfunction supportsByobReader(readable) {\n    try {\n        var reader = readable.getReader({ mode: 'byob' });\n        reader.releaseLock();\n        return true;\n    }\n    catch (_a) {\n        return false;\n    }\n}\nfunction supportsByteSource(ctor) {\n    try {\n        new ctor({ type: 'bytes' });\n        return true;\n    }\n    catch (_a) {\n        return false;\n    }\n}\n\nfunction createReadableStreamWrapper(ctor) {\n    assert(isReadableStreamConstructor(ctor));\n    var byteSourceSupported = supportsByteSource(ctor);\n    return function (readable, _a) {\n        var _b = _a === void 0 ? {} : _a, type = _b.type;\n        type = parseReadableType(type);\n        if (type === 'bytes' && !byteSourceSupported) {\n            type = undefined;\n        }\n        if (readable.constructor === ctor) {\n            if (type !== 'bytes' || supportsByobReader(readable)) {\n                return readable;\n            }\n        }\n        if (type === 'bytes') {\n            var source = createWrappingReadableSource(readable, { type: type });\n            return new ctor(source);\n        }\n        else {\n            var source = createWrappingReadableSource(readable);\n            return new ctor(source);\n        }\n    };\n}\nfunction createWrappingReadableSource(readable, _a) {\n    var _b = _a === void 0 ? {} : _a, type = _b.type;\n    assert(isReadableStream(readable));\n    assert(readable.locked === false);\n    type = parseReadableType(type);\n    var source;\n    if (type === 'bytes') {\n        source = new WrappingReadableByteStreamSource(readable);\n    }\n    else {\n        source = new WrappingReadableStreamDefaultSource(readable);\n    }\n    return source;\n}\nfunction parseReadableType(type) {\n    var typeString = String(type);\n    if (typeString === 'bytes') {\n        return typeString;\n    }\n    else if (type === undefined) {\n        return type;\n    }\n    else {\n        throw new RangeError('Invalid type is specified');\n    }\n}\nvar AbstractWrappingReadableStreamSource = /** @class */ (function () {\n    function AbstractWrappingReadableStreamSource(underlyingStream) {\n        this._underlyingReader = undefined;\n        this._readerMode = undefined;\n        this._readableStreamController = undefined;\n        this._pendingRead = undefined;\n        this._underlyingStream = underlyingStream;\n        // always keep a reader attached to detect close/error\n        this._attachDefaultReader();\n    }\n    AbstractWrappingReadableStreamSource.prototype.start = function (controller) {\n        this._readableStreamController = controller;\n    };\n    AbstractWrappingReadableStreamSource.prototype.cancel = function (reason) {\n        assert(this._underlyingReader !== undefined);\n        return this._underlyingReader.cancel(reason);\n    };\n    AbstractWrappingReadableStreamSource.prototype._attachDefaultReader = function () {\n        if (this._readerMode === \"default\" /* DEFAULT */) {\n            return;\n        }\n        this._detachReader();\n        var reader = this._underlyingStream.getReader();\n        this._readerMode = \"default\" /* DEFAULT */;\n        this._attachReader(reader);\n    };\n    AbstractWrappingReadableStreamSource.prototype._attachReader = function (reader) {\n        var _this = this;\n        assert(this._underlyingReader === undefined);\n        this._underlyingReader = reader;\n        var closed = this._underlyingReader.closed;\n        if (!closed) {\n            return;\n        }\n        closed\n            .then(function () { return _this._finishPendingRead(); })\n            .then(function () {\n            if (reader === _this._underlyingReader) {\n                _this._readableStreamController.close();\n            }\n        }, function (reason) {\n            if (reader === _this._underlyingReader) {\n                _this._readableStreamController.error(reason);\n            }\n        })\n            .catch(noop);\n    };\n    AbstractWrappingReadableStreamSource.prototype._detachReader = function () {\n        if (this._underlyingReader === undefined) {\n            return;\n        }\n        this._underlyingReader.releaseLock();\n        this._underlyingReader = undefined;\n        this._readerMode = undefined;\n    };\n    AbstractWrappingReadableStreamSource.prototype._pullWithDefaultReader = function () {\n        var _this = this;\n        this._attachDefaultReader();\n        // TODO Backpressure?\n        var read = this._underlyingReader.read()\n            .then(function (result) {\n            var controller = _this._readableStreamController;\n            if (result.done) {\n                _this._tryClose();\n            }\n            else {\n                controller.enqueue(result.value);\n            }\n        });\n        this._setPendingRead(read);\n        return read;\n    };\n    AbstractWrappingReadableStreamSource.prototype._tryClose = function () {\n        try {\n            this._readableStreamController.close();\n        }\n        catch (_a) {\n            // already errored or closed\n        }\n    };\n    AbstractWrappingReadableStreamSource.prototype._setPendingRead = function (readPromise) {\n        var _this = this;\n        var pendingRead;\n        var finishRead = function () {\n            if (_this._pendingRead === pendingRead) {\n                _this._pendingRead = undefined;\n            }\n        };\n        this._pendingRead = pendingRead = readPromise.then(finishRead, finishRead);\n    };\n    AbstractWrappingReadableStreamSource.prototype._finishPendingRead = function () {\n        var _this = this;\n        if (!this._pendingRead) {\n            return undefined;\n        }\n        var afterRead = function () { return _this._finishPendingRead(); };\n        return this._pendingRead.then(afterRead, afterRead);\n    };\n    return AbstractWrappingReadableStreamSource;\n}());\nvar WrappingReadableStreamDefaultSource = /** @class */ (function (_super) {\n    __extends(WrappingReadableStreamDefaultSource, _super);\n    function WrappingReadableStreamDefaultSource() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    WrappingReadableStreamDefaultSource.prototype.pull = function () {\n        return this._pullWithDefaultReader();\n    };\n    return WrappingReadableStreamDefaultSource;\n}(AbstractWrappingReadableStreamSource));\nfunction toUint8Array(view) {\n    return new Uint8Array(view.buffer, view.byteOffset, view.byteLength);\n}\nfunction copyArrayBufferView(from, to) {\n    var fromArray = toUint8Array(from);\n    var toArray = toUint8Array(to);\n    toArray.set(fromArray, 0);\n}\nvar WrappingReadableByteStreamSource = /** @class */ (function (_super) {\n    __extends(WrappingReadableByteStreamSource, _super);\n    function WrappingReadableByteStreamSource(underlyingStream) {\n        var _this = this;\n        var supportsByob = supportsByobReader(underlyingStream);\n        _this = _super.call(this, underlyingStream) || this;\n        _this._supportsByob = supportsByob;\n        return _this;\n    }\n    Object.defineProperty(WrappingReadableByteStreamSource.prototype, \"type\", {\n        get: function () {\n            return 'bytes';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    WrappingReadableByteStreamSource.prototype._attachByobReader = function () {\n        if (this._readerMode === \"byob\" /* BYOB */) {\n            return;\n        }\n        assert(this._supportsByob);\n        this._detachReader();\n        var reader = this._underlyingStream.getReader({ mode: 'byob' });\n        this._readerMode = \"byob\" /* BYOB */;\n        this._attachReader(reader);\n    };\n    WrappingReadableByteStreamSource.prototype.pull = function () {\n        if (this._supportsByob) {\n            var byobRequest = this._readableStreamController.byobRequest;\n            if (byobRequest) {\n                return this._pullWithByobRequest(byobRequest);\n            }\n        }\n        return this._pullWithDefaultReader();\n    };\n    WrappingReadableByteStreamSource.prototype._pullWithByobRequest = function (byobRequest) {\n        var _this = this;\n        this._attachByobReader();\n        // reader.read(view) detaches the input view, therefore we cannot pass byobRequest.view directly\n        // create a separate buffer to read into, then copy that to byobRequest.view\n        var buffer = new Uint8Array(byobRequest.view.byteLength);\n        // TODO Backpressure?\n        var read = this._underlyingReader.read(buffer)\n            .then(function (result) {\n            _this._readableStreamController;\n            if (result.done) {\n                _this._tryClose();\n                byobRequest.respond(0);\n            }\n            else {\n                copyArrayBufferView(result.value, byobRequest.view);\n                byobRequest.respond(result.value.byteLength);\n            }\n        });\n        this._setPendingRead(read);\n        return read;\n    };\n    return WrappingReadableByteStreamSource;\n}(AbstractWrappingReadableStreamSource));\n\nfunction createWritableStreamWrapper(ctor) {\n    assert(isWritableStreamConstructor(ctor));\n    return function (writable) {\n        if (writable.constructor === ctor) {\n            return writable;\n        }\n        var sink = createWrappingWritableSink(writable);\n        return new ctor(sink);\n    };\n}\nfunction createWrappingWritableSink(writable) {\n    assert(isWritableStream(writable));\n    assert(writable.locked === false);\n    var writer = writable.getWriter();\n    return new WrappingWritableStreamSink(writer);\n}\nvar WrappingWritableStreamSink = /** @class */ (function () {\n    function WrappingWritableStreamSink(underlyingWriter) {\n        var _this = this;\n        this._writableStreamController = undefined;\n        this._pendingWrite = undefined;\n        this._state = \"writable\" /* WRITABLE */;\n        this._storedError = undefined;\n        this._underlyingWriter = underlyingWriter;\n        this._errorPromise = new Promise(function (resolve, reject) {\n            _this._errorPromiseReject = reject;\n        });\n        this._errorPromise.catch(noop);\n    }\n    WrappingWritableStreamSink.prototype.start = function (controller) {\n        var _this = this;\n        this._writableStreamController = controller;\n        this._underlyingWriter.closed\n            .then(function () {\n            _this._state = \"closed\" /* CLOSED */;\n        })\n            .catch(function (reason) { return _this._finishErroring(reason); });\n    };\n    WrappingWritableStreamSink.prototype.write = function (chunk) {\n        var _this = this;\n        var writer = this._underlyingWriter;\n        // Detect past errors\n        if (writer.desiredSize === null) {\n            return writer.ready;\n        }\n        var writeRequest = writer.write(chunk);\n        // Detect future errors\n        writeRequest.catch(function (reason) { return _this._finishErroring(reason); });\n        writer.ready.catch(function (reason) { return _this._startErroring(reason); });\n        // Reject write when errored\n        var write = Promise.race([writeRequest, this._errorPromise]);\n        this._setPendingWrite(write);\n        return write;\n    };\n    WrappingWritableStreamSink.prototype.close = function () {\n        var _this = this;\n        if (this._pendingWrite === undefined) {\n            return this._underlyingWriter.close();\n        }\n        return this._finishPendingWrite().then(function () { return _this.close(); });\n    };\n    WrappingWritableStreamSink.prototype.abort = function (reason) {\n        if (this._state === \"errored\" /* ERRORED */) {\n            return undefined;\n        }\n        var writer = this._underlyingWriter;\n        return writer.abort(reason);\n    };\n    WrappingWritableStreamSink.prototype._setPendingWrite = function (writePromise) {\n        var _this = this;\n        var pendingWrite;\n        var finishWrite = function () {\n            if (_this._pendingWrite === pendingWrite) {\n                _this._pendingWrite = undefined;\n            }\n        };\n        this._pendingWrite = pendingWrite = writePromise.then(finishWrite, finishWrite);\n    };\n    WrappingWritableStreamSink.prototype._finishPendingWrite = function () {\n        var _this = this;\n        if (this._pendingWrite === undefined) {\n            return Promise.resolve();\n        }\n        var afterWrite = function () { return _this._finishPendingWrite(); };\n        return this._pendingWrite.then(afterWrite, afterWrite);\n    };\n    WrappingWritableStreamSink.prototype._startErroring = function (reason) {\n        var _this = this;\n        if (this._state === \"writable\" /* WRITABLE */) {\n            this._state = \"erroring\" /* ERRORING */;\n            this._storedError = reason;\n            var afterWrite = function () { return _this._finishErroring(reason); };\n            if (this._pendingWrite === undefined) {\n                afterWrite();\n            }\n            else {\n                this._finishPendingWrite().then(afterWrite, afterWrite);\n            }\n            this._writableStreamController.error(reason);\n        }\n    };\n    WrappingWritableStreamSink.prototype._finishErroring = function (reason) {\n        if (this._state === \"writable\" /* WRITABLE */) {\n            this._startErroring(reason);\n        }\n        if (this._state === \"erroring\" /* ERRORING */) {\n            this._state = \"errored\" /* ERRORED */;\n            this._errorPromiseReject(this._storedError);\n        }\n    };\n    return WrappingWritableStreamSink;\n}());\n\nfunction createTransformStreamWrapper(ctor) {\n    assert(isTransformStreamConstructor(ctor));\n    return function (transform) {\n        if (transform.constructor === ctor) {\n            return transform;\n        }\n        var transformer = createWrappingTransformer(transform);\n        return new ctor(transformer);\n    };\n}\nfunction createWrappingTransformer(transform) {\n    assert(isTransformStream(transform));\n    var readable = transform.readable, writable = transform.writable;\n    assert(readable.locked === false);\n    assert(writable.locked === false);\n    var reader = readable.getReader();\n    var writer;\n    try {\n        writer = writable.getWriter();\n    }\n    catch (e) {\n        reader.releaseLock(); // do not leak reader\n        throw e;\n    }\n    return new WrappingTransformStreamTransformer(reader, writer);\n}\nvar WrappingTransformStreamTransformer = /** @class */ (function () {\n    function WrappingTransformStreamTransformer(reader, writer) {\n        var _this = this;\n        this._transformStreamController = undefined;\n        this._onRead = function (result) {\n            if (result.done) {\n                return;\n            }\n            _this._transformStreamController.enqueue(result.value);\n            return _this._reader.read().then(_this._onRead);\n        };\n        this._onError = function (reason) {\n            _this._flushReject(reason);\n            _this._transformStreamController.error(reason);\n            _this._reader.cancel(reason).catch(noop);\n            _this._writer.abort(reason).catch(noop);\n        };\n        this._onTerminate = function () {\n            _this._flushResolve();\n            _this._transformStreamController.terminate();\n            var error = new TypeError('TransformStream terminated');\n            _this._writer.abort(error).catch(noop);\n        };\n        this._reader = reader;\n        this._writer = writer;\n        this._flushPromise = new Promise(function (resolve, reject) {\n            _this._flushResolve = resolve;\n            _this._flushReject = reject;\n        });\n    }\n    WrappingTransformStreamTransformer.prototype.start = function (controller) {\n        this._transformStreamController = controller;\n        this._reader.read()\n            .then(this._onRead)\n            .then(this._onTerminate, this._onError);\n        var readerClosed = this._reader.closed;\n        if (readerClosed) {\n            readerClosed\n                .then(this._onTerminate, this._onError);\n        }\n    };\n    WrappingTransformStreamTransformer.prototype.transform = function (chunk) {\n        return this._writer.write(chunk);\n    };\n    WrappingTransformStreamTransformer.prototype.flush = function () {\n        var _this = this;\n        return this._writer.close()\n            .then(function () { return _this._flushPromise; });\n    };\n    return WrappingTransformStreamTransformer;\n}());\n\nexport { createReadableStreamWrapper, createTransformStreamWrapper, createWrappingReadableSource, createWrappingTransformer, createWrappingWritableSink, createWritableStreamWrapper };\n//# sourceMappingURL=web-streams-adapter.mjs.map\n", "/**\n * @license\n * web-streams-polyfill v3.3.2\n * Copyright 2024 <PERSON>, <PERSON><PERSON><PERSON> and other contributors.\n * This code is released under the MIT license.\n * SPDX-License-Identifier: MIT\n */\n/// <reference lib=\"es2015.symbol\" />\nvar SymbolPolyfill = typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ?\n    Symbol :\n    function (description) { return \"Symbol(\".concat(description, \")\"); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRA<PERSON>IES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nfunction __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nfunction __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nfunction __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nfunction __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nfunction __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nfunction __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nfunction noop() {\n    return undefined;\n}\n\nfunction typeIsObject(x) {\n    return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\nvar rethrowAssertionErrorRejection = noop;\nfunction setFunctionName(fn, name) {\n    try {\n        Object.defineProperty(fn, 'name', {\n            value: name,\n            configurable: true\n        });\n    }\n    catch (_a) {\n        // This property is non-configurable in older browsers, so ignore if this throws.\n        // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/name#browser_compatibility\n    }\n}\n\nvar originalPromise = Promise;\nvar originalPromiseThen = Promise.prototype.then;\nvar originalPromiseReject = Promise.reject.bind(originalPromise);\n// https://webidl.spec.whatwg.org/#a-new-promise\nfunction newPromise(executor) {\n    return new originalPromise(executor);\n}\n// https://webidl.spec.whatwg.org/#a-promise-resolved-with\nfunction promiseResolvedWith(value) {\n    return newPromise(function (resolve) { return resolve(value); });\n}\n// https://webidl.spec.whatwg.org/#a-promise-rejected-with\nfunction promiseRejectedWith(reason) {\n    return originalPromiseReject(reason);\n}\nfunction PerformPromiseThen(promise, onFulfilled, onRejected) {\n    // There doesn't appear to be any way to correctly emulate the behaviour from JavaScript, so this is just an\n    // approximation.\n    return originalPromiseThen.call(promise, onFulfilled, onRejected);\n}\n// Bluebird logs a warning when a promise is created within a fulfillment handler, but then isn't returned\n// from that handler. To prevent this, return null instead of void from all handlers.\n// http://bluebirdjs.com/docs/warning-explanations.html#warning-a-promise-was-created-in-a-handler-but-was-not-returned-from-it\nfunction uponPromise(promise, onFulfilled, onRejected) {\n    PerformPromiseThen(PerformPromiseThen(promise, onFulfilled, onRejected), undefined, rethrowAssertionErrorRejection);\n}\nfunction uponFulfillment(promise, onFulfilled) {\n    uponPromise(promise, onFulfilled);\n}\nfunction uponRejection(promise, onRejected) {\n    uponPromise(promise, undefined, onRejected);\n}\nfunction transformPromiseWith(promise, fulfillmentHandler, rejectionHandler) {\n    return PerformPromiseThen(promise, fulfillmentHandler, rejectionHandler);\n}\nfunction setPromiseIsHandledToTrue(promise) {\n    PerformPromiseThen(promise, undefined, rethrowAssertionErrorRejection);\n}\nvar _queueMicrotask = function (callback) {\n    if (typeof queueMicrotask === 'function') {\n        _queueMicrotask = queueMicrotask;\n    }\n    else {\n        var resolvedPromise_1 = promiseResolvedWith(undefined);\n        _queueMicrotask = function (cb) { return PerformPromiseThen(resolvedPromise_1, cb); };\n    }\n    return _queueMicrotask(callback);\n};\nfunction reflectCall(F, V, args) {\n    if (typeof F !== 'function') {\n        throw new TypeError('Argument is not a function');\n    }\n    return Function.prototype.apply.call(F, V, args);\n}\nfunction promiseCall(F, V, args) {\n    try {\n        return promiseResolvedWith(reflectCall(F, V, args));\n    }\n    catch (value) {\n        return promiseRejectedWith(value);\n    }\n}\n\n// Original from Chromium\n// https://chromium.googlesource.com/chromium/src/+/0aee4434a4dba42a42abaea9bfbc0cd196a63bc1/third_party/blink/renderer/core/streams/SimpleQueue.js\nvar QUEUE_MAX_ARRAY_SIZE = 16384;\n/**\n * Simple queue structure.\n *\n * Avoids scalability issues with using a packed array directly by using\n * multiple arrays in a linked list and keeping the array size bounded.\n */\nvar SimpleQueue = /** @class */ (function () {\n    function SimpleQueue() {\n        this._cursor = 0;\n        this._size = 0;\n        // _front and _back are always defined.\n        this._front = {\n            _elements: [],\n            _next: undefined\n        };\n        this._back = this._front;\n        // The cursor is used to avoid calling Array.shift().\n        // It contains the index of the front element of the array inside the\n        // front-most node. It is always in the range [0, QUEUE_MAX_ARRAY_SIZE).\n        this._cursor = 0;\n        // When there is only one node, size === elements.length - cursor.\n        this._size = 0;\n    }\n    Object.defineProperty(SimpleQueue.prototype, \"length\", {\n        get: function () {\n            return this._size;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    // For exception safety, this method is structured in order:\n    // 1. Read state\n    // 2. Calculate required state mutations\n    // 3. Perform state mutations\n    SimpleQueue.prototype.push = function (element) {\n        var oldBack = this._back;\n        var newBack = oldBack;\n        if (oldBack._elements.length === QUEUE_MAX_ARRAY_SIZE - 1) {\n            newBack = {\n                _elements: [],\n                _next: undefined\n            };\n        }\n        // push() is the mutation most likely to throw an exception, so it\n        // goes first.\n        oldBack._elements.push(element);\n        if (newBack !== oldBack) {\n            this._back = newBack;\n            oldBack._next = newBack;\n        }\n        ++this._size;\n    };\n    // Like push(), shift() follows the read -> calculate -> mutate pattern for\n    // exception safety.\n    SimpleQueue.prototype.shift = function () { // must not be called on an empty queue\n        var oldFront = this._front;\n        var newFront = oldFront;\n        var oldCursor = this._cursor;\n        var newCursor = oldCursor + 1;\n        var elements = oldFront._elements;\n        var element = elements[oldCursor];\n        if (newCursor === QUEUE_MAX_ARRAY_SIZE) {\n            newFront = oldFront._next;\n            newCursor = 0;\n        }\n        // No mutations before this point.\n        --this._size;\n        this._cursor = newCursor;\n        if (oldFront !== newFront) {\n            this._front = newFront;\n        }\n        // Permit shifted element to be garbage collected.\n        elements[oldCursor] = undefined;\n        return element;\n    };\n    // The tricky thing about forEach() is that it can be called\n    // re-entrantly. The queue may be mutated inside the callback. It is easy to\n    // see that push() within the callback has no negative effects since the end\n    // of the queue is checked for on every iteration. If shift() is called\n    // repeatedly within the callback then the next iteration may return an\n    // element that has been removed. In this case the callback will be called\n    // with undefined values until we either \"catch up\" with elements that still\n    // exist or reach the back of the queue.\n    SimpleQueue.prototype.forEach = function (callback) {\n        var i = this._cursor;\n        var node = this._front;\n        var elements = node._elements;\n        while (i !== elements.length || node._next !== undefined) {\n            if (i === elements.length) {\n                node = node._next;\n                elements = node._elements;\n                i = 0;\n                if (elements.length === 0) {\n                    break;\n                }\n            }\n            callback(elements[i]);\n            ++i;\n        }\n    };\n    // Return the element that would be returned if shift() was called now,\n    // without modifying the queue.\n    SimpleQueue.prototype.peek = function () { // must not be called on an empty queue\n        var front = this._front;\n        var cursor = this._cursor;\n        return front._elements[cursor];\n    };\n    return SimpleQueue;\n}());\n\nvar AbortSteps = SymbolPolyfill('[[AbortSteps]]');\nvar ErrorSteps = SymbolPolyfill('[[ErrorSteps]]');\nvar CancelSteps = SymbolPolyfill('[[CancelSteps]]');\nvar PullSteps = SymbolPolyfill('[[PullSteps]]');\nvar ReleaseSteps = SymbolPolyfill('[[ReleaseSteps]]');\n\nfunction ReadableStreamReaderGenericInitialize(reader, stream) {\n    reader._ownerReadableStream = stream;\n    stream._reader = reader;\n    if (stream._state === 'readable') {\n        defaultReaderClosedPromiseInitialize(reader);\n    }\n    else if (stream._state === 'closed') {\n        defaultReaderClosedPromiseInitializeAsResolved(reader);\n    }\n    else {\n        defaultReaderClosedPromiseInitializeAsRejected(reader, stream._storedError);\n    }\n}\n// A client of ReadableStreamDefaultReader and ReadableStreamBYOBReader may use these functions directly to bypass state\n// check.\nfunction ReadableStreamReaderGenericCancel(reader, reason) {\n    var stream = reader._ownerReadableStream;\n    return ReadableStreamCancel(stream, reason);\n}\nfunction ReadableStreamReaderGenericRelease(reader) {\n    var stream = reader._ownerReadableStream;\n    if (stream._state === 'readable') {\n        defaultReaderClosedPromiseReject(reader, new TypeError(\"Reader was released and can no longer be used to monitor the stream's closedness\"));\n    }\n    else {\n        defaultReaderClosedPromiseResetToRejected(reader, new TypeError(\"Reader was released and can no longer be used to monitor the stream's closedness\"));\n    }\n    stream._readableStreamController[ReleaseSteps]();\n    stream._reader = undefined;\n    reader._ownerReadableStream = undefined;\n}\n// Helper functions for the readers.\nfunction readerLockException(name) {\n    return new TypeError('Cannot ' + name + ' a stream using a released reader');\n}\n// Helper functions for the ReadableStreamDefaultReader.\nfunction defaultReaderClosedPromiseInitialize(reader) {\n    reader._closedPromise = newPromise(function (resolve, reject) {\n        reader._closedPromise_resolve = resolve;\n        reader._closedPromise_reject = reject;\n    });\n}\nfunction defaultReaderClosedPromiseInitializeAsRejected(reader, reason) {\n    defaultReaderClosedPromiseInitialize(reader);\n    defaultReaderClosedPromiseReject(reader, reason);\n}\nfunction defaultReaderClosedPromiseInitializeAsResolved(reader) {\n    defaultReaderClosedPromiseInitialize(reader);\n    defaultReaderClosedPromiseResolve(reader);\n}\nfunction defaultReaderClosedPromiseReject(reader, reason) {\n    if (reader._closedPromise_reject === undefined) {\n        return;\n    }\n    setPromiseIsHandledToTrue(reader._closedPromise);\n    reader._closedPromise_reject(reason);\n    reader._closedPromise_resolve = undefined;\n    reader._closedPromise_reject = undefined;\n}\nfunction defaultReaderClosedPromiseResetToRejected(reader, reason) {\n    defaultReaderClosedPromiseInitializeAsRejected(reader, reason);\n}\nfunction defaultReaderClosedPromiseResolve(reader) {\n    if (reader._closedPromise_resolve === undefined) {\n        return;\n    }\n    reader._closedPromise_resolve(undefined);\n    reader._closedPromise_resolve = undefined;\n    reader._closedPromise_reject = undefined;\n}\n\n/// <reference lib=\"es2015.core\" />\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isFinite#Polyfill\nvar NumberIsFinite = Number.isFinite || function (x) {\n    return typeof x === 'number' && isFinite(x);\n};\n\n/// <reference lib=\"es2015.core\" />\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/trunc#Polyfill\nvar MathTrunc = Math.trunc || function (v) {\n    return v < 0 ? Math.ceil(v) : Math.floor(v);\n};\n\n// https://heycam.github.io/webidl/#idl-dictionaries\nfunction isDictionary(x) {\n    return typeof x === 'object' || typeof x === 'function';\n}\nfunction assertDictionary(obj, context) {\n    if (obj !== undefined && !isDictionary(obj)) {\n        throw new TypeError(\"\".concat(context, \" is not an object.\"));\n    }\n}\n// https://heycam.github.io/webidl/#idl-callback-functions\nfunction assertFunction(x, context) {\n    if (typeof x !== 'function') {\n        throw new TypeError(\"\".concat(context, \" is not a function.\"));\n    }\n}\n// https://heycam.github.io/webidl/#idl-object\nfunction isObject(x) {\n    return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\nfunction assertObject(x, context) {\n    if (!isObject(x)) {\n        throw new TypeError(\"\".concat(context, \" is not an object.\"));\n    }\n}\nfunction assertRequiredArgument(x, position, context) {\n    if (x === undefined) {\n        throw new TypeError(\"Parameter \".concat(position, \" is required in '\").concat(context, \"'.\"));\n    }\n}\nfunction assertRequiredField(x, field, context) {\n    if (x === undefined) {\n        throw new TypeError(\"\".concat(field, \" is required in '\").concat(context, \"'.\"));\n    }\n}\n// https://heycam.github.io/webidl/#idl-unrestricted-double\nfunction convertUnrestrictedDouble(value) {\n    return Number(value);\n}\nfunction censorNegativeZero(x) {\n    return x === 0 ? 0 : x;\n}\nfunction integerPart(x) {\n    return censorNegativeZero(MathTrunc(x));\n}\n// https://heycam.github.io/webidl/#idl-unsigned-long-long\nfunction convertUnsignedLongLongWithEnforceRange(value, context) {\n    var lowerBound = 0;\n    var upperBound = Number.MAX_SAFE_INTEGER;\n    var x = Number(value);\n    x = censorNegativeZero(x);\n    if (!NumberIsFinite(x)) {\n        throw new TypeError(\"\".concat(context, \" is not a finite number\"));\n    }\n    x = integerPart(x);\n    if (x < lowerBound || x > upperBound) {\n        throw new TypeError(\"\".concat(context, \" is outside the accepted range of \").concat(lowerBound, \" to \").concat(upperBound, \", inclusive\"));\n    }\n    if (!NumberIsFinite(x) || x === 0) {\n        return 0;\n    }\n    // TODO Use BigInt if supported?\n    // let xBigInt = BigInt(integerPart(x));\n    // xBigInt = BigInt.asUintN(64, xBigInt);\n    // return Number(xBigInt);\n    return x;\n}\n\nfunction assertReadableStream(x, context) {\n    if (!IsReadableStream(x)) {\n        throw new TypeError(\"\".concat(context, \" is not a ReadableStream.\"));\n    }\n}\n\n// Abstract operations for the ReadableStream.\nfunction AcquireReadableStreamDefaultReader(stream) {\n    return new ReadableStreamDefaultReader(stream);\n}\n// ReadableStream API exposed for controllers.\nfunction ReadableStreamAddReadRequest(stream, readRequest) {\n    stream._reader._readRequests.push(readRequest);\n}\nfunction ReadableStreamFulfillReadRequest(stream, chunk, done) {\n    var reader = stream._reader;\n    var readRequest = reader._readRequests.shift();\n    if (done) {\n        readRequest._closeSteps();\n    }\n    else {\n        readRequest._chunkSteps(chunk);\n    }\n}\nfunction ReadableStreamGetNumReadRequests(stream) {\n    return stream._reader._readRequests.length;\n}\nfunction ReadableStreamHasDefaultReader(stream) {\n    var reader = stream._reader;\n    if (reader === undefined) {\n        return false;\n    }\n    if (!IsReadableStreamDefaultReader(reader)) {\n        return false;\n    }\n    return true;\n}\n/**\n * A default reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nvar ReadableStreamDefaultReader = /** @class */ (function () {\n    function ReadableStreamDefaultReader(stream) {\n        assertRequiredArgument(stream, 1, 'ReadableStreamDefaultReader');\n        assertReadableStream(stream, 'First parameter');\n        if (IsReadableStreamLocked(stream)) {\n            throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n        }\n        ReadableStreamReaderGenericInitialize(this, stream);\n        this._readRequests = new SimpleQueue();\n    }\n    Object.defineProperty(ReadableStreamDefaultReader.prototype, \"closed\", {\n        /**\n         * Returns a promise that will be fulfilled when the stream becomes closed,\n         * or rejected if the stream ever errors or the reader's lock is released before the stream finishes closing.\n         */\n        get: function () {\n            if (!IsReadableStreamDefaultReader(this)) {\n                return promiseRejectedWith(defaultReaderBrandCheckException('closed'));\n            }\n            return this._closedPromise;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n     */\n    ReadableStreamDefaultReader.prototype.cancel = function (reason) {\n        if (reason === void 0) { reason = undefined; }\n        if (!IsReadableStreamDefaultReader(this)) {\n            return promiseRejectedWith(defaultReaderBrandCheckException('cancel'));\n        }\n        if (this._ownerReadableStream === undefined) {\n            return promiseRejectedWith(readerLockException('cancel'));\n        }\n        return ReadableStreamReaderGenericCancel(this, reason);\n    };\n    /**\n     * Returns a promise that allows access to the next chunk from the stream's internal queue, if available.\n     *\n     * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n     */\n    ReadableStreamDefaultReader.prototype.read = function () {\n        if (!IsReadableStreamDefaultReader(this)) {\n            return promiseRejectedWith(defaultReaderBrandCheckException('read'));\n        }\n        if (this._ownerReadableStream === undefined) {\n            return promiseRejectedWith(readerLockException('read from'));\n        }\n        var resolvePromise;\n        var rejectPromise;\n        var promise = newPromise(function (resolve, reject) {\n            resolvePromise = resolve;\n            rejectPromise = reject;\n        });\n        var readRequest = {\n            _chunkSteps: function (chunk) { return resolvePromise({ value: chunk, done: false }); },\n            _closeSteps: function () { return resolvePromise({ value: undefined, done: true }); },\n            _errorSteps: function (e) { return rejectPromise(e); }\n        };\n        ReadableStreamDefaultReaderRead(this, readRequest);\n        return promise;\n    };\n    /**\n     * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n     * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n     * from now on; otherwise, the reader will appear closed.\n     *\n     * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n     * the reader's {@link ReadableStreamDefaultReader.read | read()} method has not yet been settled. Attempting to\n     * do so will throw a `TypeError` and leave the reader locked to the stream.\n     */\n    ReadableStreamDefaultReader.prototype.releaseLock = function () {\n        if (!IsReadableStreamDefaultReader(this)) {\n            throw defaultReaderBrandCheckException('releaseLock');\n        }\n        if (this._ownerReadableStream === undefined) {\n            return;\n        }\n        ReadableStreamDefaultReaderRelease(this);\n    };\n    return ReadableStreamDefaultReader;\n}());\nObject.defineProperties(ReadableStreamDefaultReader.prototype, {\n    cancel: { enumerable: true },\n    read: { enumerable: true },\n    releaseLock: { enumerable: true },\n    closed: { enumerable: true }\n});\nsetFunctionName(ReadableStreamDefaultReader.prototype.cancel, 'cancel');\nsetFunctionName(ReadableStreamDefaultReader.prototype.read, 'read');\nsetFunctionName(ReadableStreamDefaultReader.prototype.releaseLock, 'releaseLock');\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(ReadableStreamDefaultReader.prototype, SymbolPolyfill.toStringTag, {\n        value: 'ReadableStreamDefaultReader',\n        configurable: true\n    });\n}\n// Abstract operations for the readers.\nfunction IsReadableStreamDefaultReader(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_readRequests')) {\n        return false;\n    }\n    return x instanceof ReadableStreamDefaultReader;\n}\nfunction ReadableStreamDefaultReaderRead(reader, readRequest) {\n    var stream = reader._ownerReadableStream;\n    stream._disturbed = true;\n    if (stream._state === 'closed') {\n        readRequest._closeSteps();\n    }\n    else if (stream._state === 'errored') {\n        readRequest._errorSteps(stream._storedError);\n    }\n    else {\n        stream._readableStreamController[PullSteps](readRequest);\n    }\n}\nfunction ReadableStreamDefaultReaderRelease(reader) {\n    ReadableStreamReaderGenericRelease(reader);\n    var e = new TypeError('Reader was released');\n    ReadableStreamDefaultReaderErrorReadRequests(reader, e);\n}\nfunction ReadableStreamDefaultReaderErrorReadRequests(reader, e) {\n    var readRequests = reader._readRequests;\n    reader._readRequests = new SimpleQueue();\n    readRequests.forEach(function (readRequest) {\n        readRequest._errorSteps(e);\n    });\n}\n// Helper functions for the ReadableStreamDefaultReader.\nfunction defaultReaderBrandCheckException(name) {\n    return new TypeError(\"ReadableStreamDefaultReader.prototype.\".concat(name, \" can only be used on a ReadableStreamDefaultReader\"));\n}\n\n/// <reference lib=\"es2018.asynciterable\" />\nvar _a;\nvar AsyncIteratorPrototype;\nif (typeof SymbolPolyfill.asyncIterator === 'symbol') {\n    // We're running inside a ES2018+ environment, but we're compiling to an older syntax.\n    // We cannot access %AsyncIteratorPrototype% without non-ES2018 syntax, but we can re-create it.\n    AsyncIteratorPrototype = (_a = {},\n        // 25.1.3.1 %AsyncIteratorPrototype% [ @@asyncIterator ] ( )\n        // https://tc39.github.io/ecma262/#sec-asynciteratorprototype-asynciterator\n        _a[SymbolPolyfill.asyncIterator] = function () {\n            return this;\n        },\n        _a);\n    Object.defineProperty(AsyncIteratorPrototype, SymbolPolyfill.asyncIterator, { enumerable: false });\n}\n\n/// <reference lib=\"es2018.asynciterable\" />\nvar ReadableStreamAsyncIteratorImpl = /** @class */ (function () {\n    function ReadableStreamAsyncIteratorImpl(reader, preventCancel) {\n        this._ongoingPromise = undefined;\n        this._isFinished = false;\n        this._reader = reader;\n        this._preventCancel = preventCancel;\n    }\n    ReadableStreamAsyncIteratorImpl.prototype.next = function () {\n        var _this = this;\n        var nextSteps = function () { return _this._nextSteps(); };\n        this._ongoingPromise = this._ongoingPromise ?\n            transformPromiseWith(this._ongoingPromise, nextSteps, nextSteps) :\n            nextSteps();\n        return this._ongoingPromise;\n    };\n    ReadableStreamAsyncIteratorImpl.prototype.return = function (value) {\n        var _this = this;\n        var returnSteps = function () { return _this._returnSteps(value); };\n        return this._ongoingPromise ?\n            transformPromiseWith(this._ongoingPromise, returnSteps, returnSteps) :\n            returnSteps();\n    };\n    ReadableStreamAsyncIteratorImpl.prototype._nextSteps = function () {\n        var _this = this;\n        if (this._isFinished) {\n            return Promise.resolve({ value: undefined, done: true });\n        }\n        var reader = this._reader;\n        var resolvePromise;\n        var rejectPromise;\n        var promise = newPromise(function (resolve, reject) {\n            resolvePromise = resolve;\n            rejectPromise = reject;\n        });\n        var readRequest = {\n            _chunkSteps: function (chunk) {\n                _this._ongoingPromise = undefined;\n                // This needs to be delayed by one microtask, otherwise we stop pulling too early which breaks a test.\n                // FIXME Is this a bug in the specification, or in the test?\n                _queueMicrotask(function () { return resolvePromise({ value: chunk, done: false }); });\n            },\n            _closeSteps: function () {\n                _this._ongoingPromise = undefined;\n                _this._isFinished = true;\n                ReadableStreamReaderGenericRelease(reader);\n                resolvePromise({ value: undefined, done: true });\n            },\n            _errorSteps: function (reason) {\n                _this._ongoingPromise = undefined;\n                _this._isFinished = true;\n                ReadableStreamReaderGenericRelease(reader);\n                rejectPromise(reason);\n            }\n        };\n        ReadableStreamDefaultReaderRead(reader, readRequest);\n        return promise;\n    };\n    ReadableStreamAsyncIteratorImpl.prototype._returnSteps = function (value) {\n        if (this._isFinished) {\n            return Promise.resolve({ value: value, done: true });\n        }\n        this._isFinished = true;\n        var reader = this._reader;\n        if (!this._preventCancel) {\n            var result = ReadableStreamReaderGenericCancel(reader, value);\n            ReadableStreamReaderGenericRelease(reader);\n            return transformPromiseWith(result, function () { return ({ value: value, done: true }); });\n        }\n        ReadableStreamReaderGenericRelease(reader);\n        return promiseResolvedWith({ value: value, done: true });\n    };\n    return ReadableStreamAsyncIteratorImpl;\n}());\nvar ReadableStreamAsyncIteratorPrototype = {\n    next: function () {\n        if (!IsReadableStreamAsyncIterator(this)) {\n            return promiseRejectedWith(streamAsyncIteratorBrandCheckException('next'));\n        }\n        return this._asyncIteratorImpl.next();\n    },\n    return: function (value) {\n        if (!IsReadableStreamAsyncIterator(this)) {\n            return promiseRejectedWith(streamAsyncIteratorBrandCheckException('return'));\n        }\n        return this._asyncIteratorImpl.return(value);\n    }\n};\nif (AsyncIteratorPrototype !== undefined) {\n    Object.setPrototypeOf(ReadableStreamAsyncIteratorPrototype, AsyncIteratorPrototype);\n}\n// Abstract operations for the ReadableStream.\nfunction AcquireReadableStreamAsyncIterator(stream, preventCancel) {\n    var reader = AcquireReadableStreamDefaultReader(stream);\n    var impl = new ReadableStreamAsyncIteratorImpl(reader, preventCancel);\n    var iterator = Object.create(ReadableStreamAsyncIteratorPrototype);\n    iterator._asyncIteratorImpl = impl;\n    return iterator;\n}\nfunction IsReadableStreamAsyncIterator(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_asyncIteratorImpl')) {\n        return false;\n    }\n    try {\n        // noinspection SuspiciousTypeOfGuard\n        return x._asyncIteratorImpl instanceof\n            ReadableStreamAsyncIteratorImpl;\n    }\n    catch (_a) {\n        return false;\n    }\n}\n// Helper functions for the ReadableStream.\nfunction streamAsyncIteratorBrandCheckException(name) {\n    return new TypeError(\"ReadableStreamAsyncIterator.\".concat(name, \" can only be used on a ReadableSteamAsyncIterator\"));\n}\n\n/// <reference lib=\"es2015.core\" />\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isNaN#Polyfill\nvar NumberIsNaN = Number.isNaN || function (x) {\n    // eslint-disable-next-line no-self-compare\n    return x !== x;\n};\n\nfunction CreateArrayFromList(elements) {\n    // We use arrays to represent lists, so this is basically a no-op.\n    // Do a slice though just in case we happen to depend on the unique-ness.\n    return elements.slice();\n}\nfunction CopyDataBlockBytes(dest, destOffset, src, srcOffset, n) {\n    new Uint8Array(dest).set(new Uint8Array(src, srcOffset, n), destOffset);\n}\nvar TransferArrayBuffer = function (O) {\n    if (typeof O.transfer === 'function') {\n        TransferArrayBuffer = function (buffer) { return buffer.transfer(); };\n    }\n    else if (typeof structuredClone === 'function') {\n        TransferArrayBuffer = function (buffer) { return structuredClone(buffer, { transfer: [buffer] }); };\n    }\n    else {\n        // Not implemented correctly\n        TransferArrayBuffer = function (buffer) { return buffer; };\n    }\n    return TransferArrayBuffer(O);\n};\nvar IsDetachedBuffer = function (O) {\n    if (typeof O.detached === 'boolean') {\n        IsDetachedBuffer = function (buffer) { return buffer.detached; };\n    }\n    else {\n        // Not implemented correctly\n        IsDetachedBuffer = function (buffer) { return buffer.byteLength === 0; };\n    }\n    return IsDetachedBuffer(O);\n};\nfunction ArrayBufferSlice(buffer, begin, end) {\n    // ArrayBuffer.prototype.slice is not available on IE10\n    // https://www.caniuse.com/mdn-javascript_builtins_arraybuffer_slice\n    if (buffer.slice) {\n        return buffer.slice(begin, end);\n    }\n    var length = end - begin;\n    var slice = new ArrayBuffer(length);\n    CopyDataBlockBytes(slice, 0, buffer, begin, length);\n    return slice;\n}\nfunction GetMethod(receiver, prop) {\n    var func = receiver[prop];\n    if (func === undefined || func === null) {\n        return undefined;\n    }\n    if (typeof func !== 'function') {\n        throw new TypeError(\"\".concat(String(prop), \" is not a function\"));\n    }\n    return func;\n}\nfunction CreateAsyncFromSyncIterator(syncIteratorRecord) {\n    // Instead of re-implementing CreateAsyncFromSyncIterator and %AsyncFromSyncIteratorPrototype%,\n    // we use yield* inside an async generator function to achieve the same result.\n    var _a;\n    // Wrap the sync iterator inside a sync iterable, so we can use it with yield*.\n    var syncIterable = (_a = {},\n        _a[SymbolPolyfill.iterator] = function () { return syncIteratorRecord.iterator; },\n        _a);\n    // Create an async generator function and immediately invoke it.\n    var asyncIterator = (function () {\n        return __asyncGenerator(this, arguments, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [5 /*yield**/, __values(__asyncDelegator(__asyncValues(syncIterable)))];\n                    case 1: return [4 /*yield*/, __await.apply(void 0, [_a.sent()])];\n                    case 2: return [4 /*yield*/, __await.apply(void 0, [_a.sent()])];\n                    case 3: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    }());\n    // Return as an async iterator record.\n    var nextMethod = asyncIterator.next;\n    return { iterator: asyncIterator, nextMethod: nextMethod, done: false };\n}\nfunction GetIterator(obj, hint, method) {\n    if (hint === void 0) { hint = 'sync'; }\n    if (method === undefined) {\n        if (hint === 'async') {\n            method = GetMethod(obj, SymbolPolyfill.asyncIterator);\n            if (method === undefined) {\n                var syncMethod = GetMethod(obj, SymbolPolyfill.iterator);\n                var syncIteratorRecord = GetIterator(obj, 'sync', syncMethod);\n                return CreateAsyncFromSyncIterator(syncIteratorRecord);\n            }\n        }\n        else {\n            method = GetMethod(obj, SymbolPolyfill.iterator);\n        }\n    }\n    if (method === undefined) {\n        throw new TypeError('The object is not iterable');\n    }\n    var iterator = reflectCall(method, obj, []);\n    if (!typeIsObject(iterator)) {\n        throw new TypeError('The iterator method must return an object');\n    }\n    var nextMethod = iterator.next;\n    return { iterator: iterator, nextMethod: nextMethod, done: false };\n}\nfunction IteratorNext(iteratorRecord) {\n    var result = reflectCall(iteratorRecord.nextMethod, iteratorRecord.iterator, []);\n    if (!typeIsObject(result)) {\n        throw new TypeError('The iterator.next() method must return an object');\n    }\n    return result;\n}\nfunction IteratorComplete(iterResult) {\n    return Boolean(iterResult.done);\n}\nfunction IteratorValue(iterResult) {\n    return iterResult.value;\n}\n\nfunction IsNonNegativeNumber(v) {\n    if (typeof v !== 'number') {\n        return false;\n    }\n    if (NumberIsNaN(v)) {\n        return false;\n    }\n    if (v < 0) {\n        return false;\n    }\n    return true;\n}\nfunction CloneAsUint8Array(O) {\n    var buffer = ArrayBufferSlice(O.buffer, O.byteOffset, O.byteOffset + O.byteLength);\n    return new Uint8Array(buffer);\n}\n\nfunction DequeueValue(container) {\n    var pair = container._queue.shift();\n    container._queueTotalSize -= pair.size;\n    if (container._queueTotalSize < 0) {\n        container._queueTotalSize = 0;\n    }\n    return pair.value;\n}\nfunction EnqueueValueWithSize(container, value, size) {\n    if (!IsNonNegativeNumber(size) || size === Infinity) {\n        throw new RangeError('Size must be a finite, non-NaN, non-negative number.');\n    }\n    container._queue.push({ value: value, size: size });\n    container._queueTotalSize += size;\n}\nfunction PeekQueueValue(container) {\n    var pair = container._queue.peek();\n    return pair.value;\n}\nfunction ResetQueue(container) {\n    container._queue = new SimpleQueue();\n    container._queueTotalSize = 0;\n}\n\nfunction isDataViewConstructor(ctor) {\n    return ctor === DataView;\n}\nfunction isDataView(view) {\n    return isDataViewConstructor(view.constructor);\n}\nfunction arrayBufferViewElementSize(ctor) {\n    if (isDataViewConstructor(ctor)) {\n        return 1;\n    }\n    return ctor.BYTES_PER_ELEMENT;\n}\n\n/**\n * A pull-into request in a {@link ReadableByteStreamController}.\n *\n * @public\n */\nvar ReadableStreamBYOBRequest = /** @class */ (function () {\n    function ReadableStreamBYOBRequest() {\n        throw new TypeError('Illegal constructor');\n    }\n    Object.defineProperty(ReadableStreamBYOBRequest.prototype, \"view\", {\n        /**\n         * Returns the view for writing in to, or `null` if the BYOB request has already been responded to.\n         */\n        get: function () {\n            if (!IsReadableStreamBYOBRequest(this)) {\n                throw byobRequestBrandCheckException('view');\n            }\n            return this._view;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ReadableStreamBYOBRequest.prototype.respond = function (bytesWritten) {\n        if (!IsReadableStreamBYOBRequest(this)) {\n            throw byobRequestBrandCheckException('respond');\n        }\n        assertRequiredArgument(bytesWritten, 1, 'respond');\n        bytesWritten = convertUnsignedLongLongWithEnforceRange(bytesWritten, 'First parameter');\n        if (this._associatedReadableByteStreamController === undefined) {\n            throw new TypeError('This BYOB request has been invalidated');\n        }\n        if (IsDetachedBuffer(this._view.buffer)) {\n            throw new TypeError(\"The BYOB request's buffer has been detached and so cannot be used as a response\");\n        }\n        ReadableByteStreamControllerRespond(this._associatedReadableByteStreamController, bytesWritten);\n    };\n    ReadableStreamBYOBRequest.prototype.respondWithNewView = function (view) {\n        if (!IsReadableStreamBYOBRequest(this)) {\n            throw byobRequestBrandCheckException('respondWithNewView');\n        }\n        assertRequiredArgument(view, 1, 'respondWithNewView');\n        if (!ArrayBuffer.isView(view)) {\n            throw new TypeError('You can only respond with array buffer views');\n        }\n        if (this._associatedReadableByteStreamController === undefined) {\n            throw new TypeError('This BYOB request has been invalidated');\n        }\n        if (IsDetachedBuffer(view.buffer)) {\n            throw new TypeError('The given view\\'s buffer has been detached and so cannot be used as a response');\n        }\n        ReadableByteStreamControllerRespondWithNewView(this._associatedReadableByteStreamController, view);\n    };\n    return ReadableStreamBYOBRequest;\n}());\nObject.defineProperties(ReadableStreamBYOBRequest.prototype, {\n    respond: { enumerable: true },\n    respondWithNewView: { enumerable: true },\n    view: { enumerable: true }\n});\nsetFunctionName(ReadableStreamBYOBRequest.prototype.respond, 'respond');\nsetFunctionName(ReadableStreamBYOBRequest.prototype.respondWithNewView, 'respondWithNewView');\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(ReadableStreamBYOBRequest.prototype, SymbolPolyfill.toStringTag, {\n        value: 'ReadableStreamBYOBRequest',\n        configurable: true\n    });\n}\n/**\n * Allows control of a {@link ReadableStream | readable byte stream}'s state and internal queue.\n *\n * @public\n */\nvar ReadableByteStreamController = /** @class */ (function () {\n    function ReadableByteStreamController() {\n        throw new TypeError('Illegal constructor');\n    }\n    Object.defineProperty(ReadableByteStreamController.prototype, \"byobRequest\", {\n        /**\n         * Returns the current BYOB pull request, or `null` if there isn't one.\n         */\n        get: function () {\n            if (!IsReadableByteStreamController(this)) {\n                throw byteStreamControllerBrandCheckException('byobRequest');\n            }\n            return ReadableByteStreamControllerGetBYOBRequest(this);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(ReadableByteStreamController.prototype, \"desiredSize\", {\n        /**\n         * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n         * over-full. An underlying byte source ought to use this information to determine when and how to apply backpressure.\n         */\n        get: function () {\n            if (!IsReadableByteStreamController(this)) {\n                throw byteStreamControllerBrandCheckException('desiredSize');\n            }\n            return ReadableByteStreamControllerGetDesiredSize(this);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n     * the stream, but once those are read, the stream will become closed.\n     */\n    ReadableByteStreamController.prototype.close = function () {\n        if (!IsReadableByteStreamController(this)) {\n            throw byteStreamControllerBrandCheckException('close');\n        }\n        if (this._closeRequested) {\n            throw new TypeError('The stream has already been closed; do not close it again!');\n        }\n        var state = this._controlledReadableByteStream._state;\n        if (state !== 'readable') {\n            throw new TypeError(\"The stream (in \".concat(state, \" state) is not in the readable state and cannot be closed\"));\n        }\n        ReadableByteStreamControllerClose(this);\n    };\n    ReadableByteStreamController.prototype.enqueue = function (chunk) {\n        if (!IsReadableByteStreamController(this)) {\n            throw byteStreamControllerBrandCheckException('enqueue');\n        }\n        assertRequiredArgument(chunk, 1, 'enqueue');\n        if (!ArrayBuffer.isView(chunk)) {\n            throw new TypeError('chunk must be an array buffer view');\n        }\n        if (chunk.byteLength === 0) {\n            throw new TypeError('chunk must have non-zero byteLength');\n        }\n        if (chunk.buffer.byteLength === 0) {\n            throw new TypeError(\"chunk's buffer must have non-zero byteLength\");\n        }\n        if (this._closeRequested) {\n            throw new TypeError('stream is closed or draining');\n        }\n        var state = this._controlledReadableByteStream._state;\n        if (state !== 'readable') {\n            throw new TypeError(\"The stream (in \".concat(state, \" state) is not in the readable state and cannot be enqueued to\"));\n        }\n        ReadableByteStreamControllerEnqueue(this, chunk);\n    };\n    /**\n     * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n     */\n    ReadableByteStreamController.prototype.error = function (e) {\n        if (e === void 0) { e = undefined; }\n        if (!IsReadableByteStreamController(this)) {\n            throw byteStreamControllerBrandCheckException('error');\n        }\n        ReadableByteStreamControllerError(this, e);\n    };\n    /** @internal */\n    ReadableByteStreamController.prototype[CancelSteps] = function (reason) {\n        ReadableByteStreamControllerClearPendingPullIntos(this);\n        ResetQueue(this);\n        var result = this._cancelAlgorithm(reason);\n        ReadableByteStreamControllerClearAlgorithms(this);\n        return result;\n    };\n    /** @internal */\n    ReadableByteStreamController.prototype[PullSteps] = function (readRequest) {\n        var stream = this._controlledReadableByteStream;\n        if (this._queueTotalSize > 0) {\n            ReadableByteStreamControllerFillReadRequestFromQueue(this, readRequest);\n            return;\n        }\n        var autoAllocateChunkSize = this._autoAllocateChunkSize;\n        if (autoAllocateChunkSize !== undefined) {\n            var buffer = void 0;\n            try {\n                buffer = new ArrayBuffer(autoAllocateChunkSize);\n            }\n            catch (bufferE) {\n                readRequest._errorSteps(bufferE);\n                return;\n            }\n            var pullIntoDescriptor = {\n                buffer: buffer,\n                bufferByteLength: autoAllocateChunkSize,\n                byteOffset: 0,\n                byteLength: autoAllocateChunkSize,\n                bytesFilled: 0,\n                minimumFill: 1,\n                elementSize: 1,\n                viewConstructor: Uint8Array,\n                readerType: 'default'\n            };\n            this._pendingPullIntos.push(pullIntoDescriptor);\n        }\n        ReadableStreamAddReadRequest(stream, readRequest);\n        ReadableByteStreamControllerCallPullIfNeeded(this);\n    };\n    /** @internal */\n    ReadableByteStreamController.prototype[ReleaseSteps] = function () {\n        if (this._pendingPullIntos.length > 0) {\n            var firstPullInto = this._pendingPullIntos.peek();\n            firstPullInto.readerType = 'none';\n            this._pendingPullIntos = new SimpleQueue();\n            this._pendingPullIntos.push(firstPullInto);\n        }\n    };\n    return ReadableByteStreamController;\n}());\nObject.defineProperties(ReadableByteStreamController.prototype, {\n    close: { enumerable: true },\n    enqueue: { enumerable: true },\n    error: { enumerable: true },\n    byobRequest: { enumerable: true },\n    desiredSize: { enumerable: true }\n});\nsetFunctionName(ReadableByteStreamController.prototype.close, 'close');\nsetFunctionName(ReadableByteStreamController.prototype.enqueue, 'enqueue');\nsetFunctionName(ReadableByteStreamController.prototype.error, 'error');\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(ReadableByteStreamController.prototype, SymbolPolyfill.toStringTag, {\n        value: 'ReadableByteStreamController',\n        configurable: true\n    });\n}\n// Abstract operations for the ReadableByteStreamController.\nfunction IsReadableByteStreamController(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableByteStream')) {\n        return false;\n    }\n    return x instanceof ReadableByteStreamController;\n}\nfunction IsReadableStreamBYOBRequest(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_associatedReadableByteStreamController')) {\n        return false;\n    }\n    return x instanceof ReadableStreamBYOBRequest;\n}\nfunction ReadableByteStreamControllerCallPullIfNeeded(controller) {\n    var shouldPull = ReadableByteStreamControllerShouldCallPull(controller);\n    if (!shouldPull) {\n        return;\n    }\n    if (controller._pulling) {\n        controller._pullAgain = true;\n        return;\n    }\n    controller._pulling = true;\n    // TODO: Test controller argument\n    var pullPromise = controller._pullAlgorithm();\n    uponPromise(pullPromise, function () {\n        controller._pulling = false;\n        if (controller._pullAgain) {\n            controller._pullAgain = false;\n            ReadableByteStreamControllerCallPullIfNeeded(controller);\n        }\n        return null;\n    }, function (e) {\n        ReadableByteStreamControllerError(controller, e);\n        return null;\n    });\n}\nfunction ReadableByteStreamControllerClearPendingPullIntos(controller) {\n    ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n    controller._pendingPullIntos = new SimpleQueue();\n}\nfunction ReadableByteStreamControllerCommitPullIntoDescriptor(stream, pullIntoDescriptor) {\n    var done = false;\n    if (stream._state === 'closed') {\n        done = true;\n    }\n    var filledView = ReadableByteStreamControllerConvertPullIntoDescriptor(pullIntoDescriptor);\n    if (pullIntoDescriptor.readerType === 'default') {\n        ReadableStreamFulfillReadRequest(stream, filledView, done);\n    }\n    else {\n        ReadableStreamFulfillReadIntoRequest(stream, filledView, done);\n    }\n}\nfunction ReadableByteStreamControllerConvertPullIntoDescriptor(pullIntoDescriptor) {\n    var bytesFilled = pullIntoDescriptor.bytesFilled;\n    var elementSize = pullIntoDescriptor.elementSize;\n    return new pullIntoDescriptor.viewConstructor(pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, bytesFilled / elementSize);\n}\nfunction ReadableByteStreamControllerEnqueueChunkToQueue(controller, buffer, byteOffset, byteLength) {\n    controller._queue.push({ buffer: buffer, byteOffset: byteOffset, byteLength: byteLength });\n    controller._queueTotalSize += byteLength;\n}\nfunction ReadableByteStreamControllerEnqueueClonedChunkToQueue(controller, buffer, byteOffset, byteLength) {\n    var clonedChunk;\n    try {\n        clonedChunk = ArrayBufferSlice(buffer, byteOffset, byteOffset + byteLength);\n    }\n    catch (cloneE) {\n        ReadableByteStreamControllerError(controller, cloneE);\n        throw cloneE;\n    }\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, clonedChunk, 0, byteLength);\n}\nfunction ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller, firstDescriptor) {\n    if (firstDescriptor.bytesFilled > 0) {\n        ReadableByteStreamControllerEnqueueClonedChunkToQueue(controller, firstDescriptor.buffer, firstDescriptor.byteOffset, firstDescriptor.bytesFilled);\n    }\n    ReadableByteStreamControllerShiftPendingPullInto(controller);\n}\nfunction ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor) {\n    var maxBytesToCopy = Math.min(controller._queueTotalSize, pullIntoDescriptor.byteLength - pullIntoDescriptor.bytesFilled);\n    var maxBytesFilled = pullIntoDescriptor.bytesFilled + maxBytesToCopy;\n    var totalBytesToCopyRemaining = maxBytesToCopy;\n    var ready = false;\n    var remainderBytes = maxBytesFilled % pullIntoDescriptor.elementSize;\n    var maxAlignedBytes = maxBytesFilled - remainderBytes;\n    // A descriptor for a read() request that is not yet filled up to its minimum length will stay at the head\n    // of the queue, so the underlying source can keep filling it.\n    if (maxAlignedBytes >= pullIntoDescriptor.minimumFill) {\n        totalBytesToCopyRemaining = maxAlignedBytes - pullIntoDescriptor.bytesFilled;\n        ready = true;\n    }\n    var queue = controller._queue;\n    while (totalBytesToCopyRemaining > 0) {\n        var headOfQueue = queue.peek();\n        var bytesToCopy = Math.min(totalBytesToCopyRemaining, headOfQueue.byteLength);\n        var destStart = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n        CopyDataBlockBytes(pullIntoDescriptor.buffer, destStart, headOfQueue.buffer, headOfQueue.byteOffset, bytesToCopy);\n        if (headOfQueue.byteLength === bytesToCopy) {\n            queue.shift();\n        }\n        else {\n            headOfQueue.byteOffset += bytesToCopy;\n            headOfQueue.byteLength -= bytesToCopy;\n        }\n        controller._queueTotalSize -= bytesToCopy;\n        ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesToCopy, pullIntoDescriptor);\n        totalBytesToCopyRemaining -= bytesToCopy;\n    }\n    return ready;\n}\nfunction ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, size, pullIntoDescriptor) {\n    pullIntoDescriptor.bytesFilled += size;\n}\nfunction ReadableByteStreamControllerHandleQueueDrain(controller) {\n    if (controller._queueTotalSize === 0 && controller._closeRequested) {\n        ReadableByteStreamControllerClearAlgorithms(controller);\n        ReadableStreamClose(controller._controlledReadableByteStream);\n    }\n    else {\n        ReadableByteStreamControllerCallPullIfNeeded(controller);\n    }\n}\nfunction ReadableByteStreamControllerInvalidateBYOBRequest(controller) {\n    if (controller._byobRequest === null) {\n        return;\n    }\n    controller._byobRequest._associatedReadableByteStreamController = undefined;\n    controller._byobRequest._view = null;\n    controller._byobRequest = null;\n}\nfunction ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller) {\n    while (controller._pendingPullIntos.length > 0) {\n        if (controller._queueTotalSize === 0) {\n            return;\n        }\n        var pullIntoDescriptor = controller._pendingPullIntos.peek();\n        if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n            ReadableByteStreamControllerShiftPendingPullInto(controller);\n            ReadableByteStreamControllerCommitPullIntoDescriptor(controller._controlledReadableByteStream, pullIntoDescriptor);\n        }\n    }\n}\nfunction ReadableByteStreamControllerProcessReadRequestsUsingQueue(controller) {\n    var reader = controller._controlledReadableByteStream._reader;\n    while (reader._readRequests.length > 0) {\n        if (controller._queueTotalSize === 0) {\n            return;\n        }\n        var readRequest = reader._readRequests.shift();\n        ReadableByteStreamControllerFillReadRequestFromQueue(controller, readRequest);\n    }\n}\nfunction ReadableByteStreamControllerPullInto(controller, view, min, readIntoRequest) {\n    var stream = controller._controlledReadableByteStream;\n    var ctor = view.constructor;\n    var elementSize = arrayBufferViewElementSize(ctor);\n    var byteOffset = view.byteOffset, byteLength = view.byteLength;\n    var minimumFill = min * elementSize;\n    var buffer;\n    try {\n        buffer = TransferArrayBuffer(view.buffer);\n    }\n    catch (e) {\n        readIntoRequest._errorSteps(e);\n        return;\n    }\n    var pullIntoDescriptor = {\n        buffer: buffer,\n        bufferByteLength: buffer.byteLength,\n        byteOffset: byteOffset,\n        byteLength: byteLength,\n        bytesFilled: 0,\n        minimumFill: minimumFill,\n        elementSize: elementSize,\n        viewConstructor: ctor,\n        readerType: 'byob'\n    };\n    if (controller._pendingPullIntos.length > 0) {\n        controller._pendingPullIntos.push(pullIntoDescriptor);\n        // No ReadableByteStreamControllerCallPullIfNeeded() call since:\n        // - No change happens on desiredSize\n        // - The source has already been notified of that there's at least 1 pending read(view)\n        ReadableStreamAddReadIntoRequest(stream, readIntoRequest);\n        return;\n    }\n    if (stream._state === 'closed') {\n        var emptyView = new ctor(pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, 0);\n        readIntoRequest._closeSteps(emptyView);\n        return;\n    }\n    if (controller._queueTotalSize > 0) {\n        if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n            var filledView = ReadableByteStreamControllerConvertPullIntoDescriptor(pullIntoDescriptor);\n            ReadableByteStreamControllerHandleQueueDrain(controller);\n            readIntoRequest._chunkSteps(filledView);\n            return;\n        }\n        if (controller._closeRequested) {\n            var e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n            ReadableByteStreamControllerError(controller, e);\n            readIntoRequest._errorSteps(e);\n            return;\n        }\n    }\n    controller._pendingPullIntos.push(pullIntoDescriptor);\n    ReadableStreamAddReadIntoRequest(stream, readIntoRequest);\n    ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\nfunction ReadableByteStreamControllerRespondInClosedState(controller, firstDescriptor) {\n    if (firstDescriptor.readerType === 'none') {\n        ReadableByteStreamControllerShiftPendingPullInto(controller);\n    }\n    var stream = controller._controlledReadableByteStream;\n    if (ReadableStreamHasBYOBReader(stream)) {\n        while (ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n            var pullIntoDescriptor = ReadableByteStreamControllerShiftPendingPullInto(controller);\n            ReadableByteStreamControllerCommitPullIntoDescriptor(stream, pullIntoDescriptor);\n        }\n    }\n}\nfunction ReadableByteStreamControllerRespondInReadableState(controller, bytesWritten, pullIntoDescriptor) {\n    ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesWritten, pullIntoDescriptor);\n    if (pullIntoDescriptor.readerType === 'none') {\n        ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller, pullIntoDescriptor);\n        ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n        return;\n    }\n    if (pullIntoDescriptor.bytesFilled < pullIntoDescriptor.minimumFill) {\n        // A descriptor for a read() request that is not yet filled up to its minimum length will stay at the head\n        // of the queue, so the underlying source can keep filling it.\n        return;\n    }\n    ReadableByteStreamControllerShiftPendingPullInto(controller);\n    var remainderSize = pullIntoDescriptor.bytesFilled % pullIntoDescriptor.elementSize;\n    if (remainderSize > 0) {\n        var end = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n        ReadableByteStreamControllerEnqueueClonedChunkToQueue(controller, pullIntoDescriptor.buffer, end - remainderSize, remainderSize);\n    }\n    pullIntoDescriptor.bytesFilled -= remainderSize;\n    ReadableByteStreamControllerCommitPullIntoDescriptor(controller._controlledReadableByteStream, pullIntoDescriptor);\n    ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n}\nfunction ReadableByteStreamControllerRespondInternal(controller, bytesWritten) {\n    var firstDescriptor = controller._pendingPullIntos.peek();\n    ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n    var state = controller._controlledReadableByteStream._state;\n    if (state === 'closed') {\n        ReadableByteStreamControllerRespondInClosedState(controller, firstDescriptor);\n    }\n    else {\n        ReadableByteStreamControllerRespondInReadableState(controller, bytesWritten, firstDescriptor);\n    }\n    ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\nfunction ReadableByteStreamControllerShiftPendingPullInto(controller) {\n    var descriptor = controller._pendingPullIntos.shift();\n    return descriptor;\n}\nfunction ReadableByteStreamControllerShouldCallPull(controller) {\n    var stream = controller._controlledReadableByteStream;\n    if (stream._state !== 'readable') {\n        return false;\n    }\n    if (controller._closeRequested) {\n        return false;\n    }\n    if (!controller._started) {\n        return false;\n    }\n    if (ReadableStreamHasDefaultReader(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n        return true;\n    }\n    if (ReadableStreamHasBYOBReader(stream) && ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n        return true;\n    }\n    var desiredSize = ReadableByteStreamControllerGetDesiredSize(controller);\n    if (desiredSize > 0) {\n        return true;\n    }\n    return false;\n}\nfunction ReadableByteStreamControllerClearAlgorithms(controller) {\n    controller._pullAlgorithm = undefined;\n    controller._cancelAlgorithm = undefined;\n}\n// A client of ReadableByteStreamController may use these functions directly to bypass state check.\nfunction ReadableByteStreamControllerClose(controller) {\n    var stream = controller._controlledReadableByteStream;\n    if (controller._closeRequested || stream._state !== 'readable') {\n        return;\n    }\n    if (controller._queueTotalSize > 0) {\n        controller._closeRequested = true;\n        return;\n    }\n    if (controller._pendingPullIntos.length > 0) {\n        var firstPendingPullInto = controller._pendingPullIntos.peek();\n        if (firstPendingPullInto.bytesFilled % firstPendingPullInto.elementSize !== 0) {\n            var e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n            ReadableByteStreamControllerError(controller, e);\n            throw e;\n        }\n    }\n    ReadableByteStreamControllerClearAlgorithms(controller);\n    ReadableStreamClose(stream);\n}\nfunction ReadableByteStreamControllerEnqueue(controller, chunk) {\n    var stream = controller._controlledReadableByteStream;\n    if (controller._closeRequested || stream._state !== 'readable') {\n        return;\n    }\n    var buffer = chunk.buffer, byteOffset = chunk.byteOffset, byteLength = chunk.byteLength;\n    if (IsDetachedBuffer(buffer)) {\n        throw new TypeError('chunk\\'s buffer is detached and so cannot be enqueued');\n    }\n    var transferredBuffer = TransferArrayBuffer(buffer);\n    if (controller._pendingPullIntos.length > 0) {\n        var firstPendingPullInto = controller._pendingPullIntos.peek();\n        if (IsDetachedBuffer(firstPendingPullInto.buffer)) {\n            throw new TypeError('The BYOB request\\'s buffer has been detached and so cannot be filled with an enqueued chunk');\n        }\n        ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n        firstPendingPullInto.buffer = TransferArrayBuffer(firstPendingPullInto.buffer);\n        if (firstPendingPullInto.readerType === 'none') {\n            ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller, firstPendingPullInto);\n        }\n    }\n    if (ReadableStreamHasDefaultReader(stream)) {\n        ReadableByteStreamControllerProcessReadRequestsUsingQueue(controller);\n        if (ReadableStreamGetNumReadRequests(stream) === 0) {\n            ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n        }\n        else {\n            if (controller._pendingPullIntos.length > 0) {\n                ReadableByteStreamControllerShiftPendingPullInto(controller);\n            }\n            var transferredView = new Uint8Array(transferredBuffer, byteOffset, byteLength);\n            ReadableStreamFulfillReadRequest(stream, transferredView, false);\n        }\n    }\n    else if (ReadableStreamHasBYOBReader(stream)) {\n        // TODO: Ideally in this branch detaching should happen only if the buffer is not consumed fully.\n        ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n        ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n    }\n    else {\n        ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    }\n    ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\nfunction ReadableByteStreamControllerError(controller, e) {\n    var stream = controller._controlledReadableByteStream;\n    if (stream._state !== 'readable') {\n        return;\n    }\n    ReadableByteStreamControllerClearPendingPullIntos(controller);\n    ResetQueue(controller);\n    ReadableByteStreamControllerClearAlgorithms(controller);\n    ReadableStreamError(stream, e);\n}\nfunction ReadableByteStreamControllerFillReadRequestFromQueue(controller, readRequest) {\n    var entry = controller._queue.shift();\n    controller._queueTotalSize -= entry.byteLength;\n    ReadableByteStreamControllerHandleQueueDrain(controller);\n    var view = new Uint8Array(entry.buffer, entry.byteOffset, entry.byteLength);\n    readRequest._chunkSteps(view);\n}\nfunction ReadableByteStreamControllerGetBYOBRequest(controller) {\n    if (controller._byobRequest === null && controller._pendingPullIntos.length > 0) {\n        var firstDescriptor = controller._pendingPullIntos.peek();\n        var view = new Uint8Array(firstDescriptor.buffer, firstDescriptor.byteOffset + firstDescriptor.bytesFilled, firstDescriptor.byteLength - firstDescriptor.bytesFilled);\n        var byobRequest = Object.create(ReadableStreamBYOBRequest.prototype);\n        SetUpReadableStreamBYOBRequest(byobRequest, controller, view);\n        controller._byobRequest = byobRequest;\n    }\n    return controller._byobRequest;\n}\nfunction ReadableByteStreamControllerGetDesiredSize(controller) {\n    var state = controller._controlledReadableByteStream._state;\n    if (state === 'errored') {\n        return null;\n    }\n    if (state === 'closed') {\n        return 0;\n    }\n    return controller._strategyHWM - controller._queueTotalSize;\n}\nfunction ReadableByteStreamControllerRespond(controller, bytesWritten) {\n    var firstDescriptor = controller._pendingPullIntos.peek();\n    var state = controller._controlledReadableByteStream._state;\n    if (state === 'closed') {\n        if (bytesWritten !== 0) {\n            throw new TypeError('bytesWritten must be 0 when calling respond() on a closed stream');\n        }\n    }\n    else {\n        if (bytesWritten === 0) {\n            throw new TypeError('bytesWritten must be greater than 0 when calling respond() on a readable stream');\n        }\n        if (firstDescriptor.bytesFilled + bytesWritten > firstDescriptor.byteLength) {\n            throw new RangeError('bytesWritten out of range');\n        }\n    }\n    firstDescriptor.buffer = TransferArrayBuffer(firstDescriptor.buffer);\n    ReadableByteStreamControllerRespondInternal(controller, bytesWritten);\n}\nfunction ReadableByteStreamControllerRespondWithNewView(controller, view) {\n    var firstDescriptor = controller._pendingPullIntos.peek();\n    var state = controller._controlledReadableByteStream._state;\n    if (state === 'closed') {\n        if (view.byteLength !== 0) {\n            throw new TypeError('The view\\'s length must be 0 when calling respondWithNewView() on a closed stream');\n        }\n    }\n    else {\n        if (view.byteLength === 0) {\n            throw new TypeError('The view\\'s length must be greater than 0 when calling respondWithNewView() on a readable stream');\n        }\n    }\n    if (firstDescriptor.byteOffset + firstDescriptor.bytesFilled !== view.byteOffset) {\n        throw new RangeError('The region specified by view does not match byobRequest');\n    }\n    if (firstDescriptor.bufferByteLength !== view.buffer.byteLength) {\n        throw new RangeError('The buffer of view has different capacity than byobRequest');\n    }\n    if (firstDescriptor.bytesFilled + view.byteLength > firstDescriptor.byteLength) {\n        throw new RangeError('The region specified by view is larger than byobRequest');\n    }\n    var viewByteLength = view.byteLength;\n    firstDescriptor.buffer = TransferArrayBuffer(view.buffer);\n    ReadableByteStreamControllerRespondInternal(controller, viewByteLength);\n}\nfunction SetUpReadableByteStreamController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, autoAllocateChunkSize) {\n    controller._controlledReadableByteStream = stream;\n    controller._pullAgain = false;\n    controller._pulling = false;\n    controller._byobRequest = null;\n    // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n    controller._queue = controller._queueTotalSize = undefined;\n    ResetQueue(controller);\n    controller._closeRequested = false;\n    controller._started = false;\n    controller._strategyHWM = highWaterMark;\n    controller._pullAlgorithm = pullAlgorithm;\n    controller._cancelAlgorithm = cancelAlgorithm;\n    controller._autoAllocateChunkSize = autoAllocateChunkSize;\n    controller._pendingPullIntos = new SimpleQueue();\n    stream._readableStreamController = controller;\n    var startResult = startAlgorithm();\n    uponPromise(promiseResolvedWith(startResult), function () {\n        controller._started = true;\n        ReadableByteStreamControllerCallPullIfNeeded(controller);\n        return null;\n    }, function (r) {\n        ReadableByteStreamControllerError(controller, r);\n        return null;\n    });\n}\nfunction SetUpReadableByteStreamControllerFromUnderlyingSource(stream, underlyingByteSource, highWaterMark) {\n    var controller = Object.create(ReadableByteStreamController.prototype);\n    var startAlgorithm;\n    var pullAlgorithm;\n    var cancelAlgorithm;\n    if (underlyingByteSource.start !== undefined) {\n        startAlgorithm = function () { return underlyingByteSource.start(controller); };\n    }\n    else {\n        startAlgorithm = function () { return undefined; };\n    }\n    if (underlyingByteSource.pull !== undefined) {\n        pullAlgorithm = function () { return underlyingByteSource.pull(controller); };\n    }\n    else {\n        pullAlgorithm = function () { return promiseResolvedWith(undefined); };\n    }\n    if (underlyingByteSource.cancel !== undefined) {\n        cancelAlgorithm = function (reason) { return underlyingByteSource.cancel(reason); };\n    }\n    else {\n        cancelAlgorithm = function () { return promiseResolvedWith(undefined); };\n    }\n    var autoAllocateChunkSize = underlyingByteSource.autoAllocateChunkSize;\n    if (autoAllocateChunkSize === 0) {\n        throw new TypeError('autoAllocateChunkSize must be greater than 0');\n    }\n    SetUpReadableByteStreamController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, autoAllocateChunkSize);\n}\nfunction SetUpReadableStreamBYOBRequest(request, controller, view) {\n    request._associatedReadableByteStreamController = controller;\n    request._view = view;\n}\n// Helper functions for the ReadableStreamBYOBRequest.\nfunction byobRequestBrandCheckException(name) {\n    return new TypeError(\"ReadableStreamBYOBRequest.prototype.\".concat(name, \" can only be used on a ReadableStreamBYOBRequest\"));\n}\n// Helper functions for the ReadableByteStreamController.\nfunction byteStreamControllerBrandCheckException(name) {\n    return new TypeError(\"ReadableByteStreamController.prototype.\".concat(name, \" can only be used on a ReadableByteStreamController\"));\n}\n\nfunction convertReaderOptions(options, context) {\n    assertDictionary(options, context);\n    var mode = options === null || options === void 0 ? void 0 : options.mode;\n    return {\n        mode: mode === undefined ? undefined : convertReadableStreamReaderMode(mode, \"\".concat(context, \" has member 'mode' that\"))\n    };\n}\nfunction convertReadableStreamReaderMode(mode, context) {\n    mode = \"\".concat(mode);\n    if (mode !== 'byob') {\n        throw new TypeError(\"\".concat(context, \" '\").concat(mode, \"' is not a valid enumeration value for ReadableStreamReaderMode\"));\n    }\n    return mode;\n}\nfunction convertByobReadOptions(options, context) {\n    var _a;\n    assertDictionary(options, context);\n    var min = (_a = options === null || options === void 0 ? void 0 : options.min) !== null && _a !== void 0 ? _a : 1;\n    return {\n        min: convertUnsignedLongLongWithEnforceRange(min, \"\".concat(context, \" has member 'min' that\"))\n    };\n}\n\n// Abstract operations for the ReadableStream.\nfunction AcquireReadableStreamBYOBReader(stream) {\n    return new ReadableStreamBYOBReader(stream);\n}\n// ReadableStream API exposed for controllers.\nfunction ReadableStreamAddReadIntoRequest(stream, readIntoRequest) {\n    stream._reader._readIntoRequests.push(readIntoRequest);\n}\nfunction ReadableStreamFulfillReadIntoRequest(stream, chunk, done) {\n    var reader = stream._reader;\n    var readIntoRequest = reader._readIntoRequests.shift();\n    if (done) {\n        readIntoRequest._closeSteps(chunk);\n    }\n    else {\n        readIntoRequest._chunkSteps(chunk);\n    }\n}\nfunction ReadableStreamGetNumReadIntoRequests(stream) {\n    return stream._reader._readIntoRequests.length;\n}\nfunction ReadableStreamHasBYOBReader(stream) {\n    var reader = stream._reader;\n    if (reader === undefined) {\n        return false;\n    }\n    if (!IsReadableStreamBYOBReader(reader)) {\n        return false;\n    }\n    return true;\n}\n/**\n * A BYOB reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nvar ReadableStreamBYOBReader = /** @class */ (function () {\n    function ReadableStreamBYOBReader(stream) {\n        assertRequiredArgument(stream, 1, 'ReadableStreamBYOBReader');\n        assertReadableStream(stream, 'First parameter');\n        if (IsReadableStreamLocked(stream)) {\n            throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n        }\n        if (!IsReadableByteStreamController(stream._readableStreamController)) {\n            throw new TypeError('Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte ' +\n                'source');\n        }\n        ReadableStreamReaderGenericInitialize(this, stream);\n        this._readIntoRequests = new SimpleQueue();\n    }\n    Object.defineProperty(ReadableStreamBYOBReader.prototype, \"closed\", {\n        /**\n         * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n         * the reader's lock is released before the stream finishes closing.\n         */\n        get: function () {\n            if (!IsReadableStreamBYOBReader(this)) {\n                return promiseRejectedWith(byobReaderBrandCheckException('closed'));\n            }\n            return this._closedPromise;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n     */\n    ReadableStreamBYOBReader.prototype.cancel = function (reason) {\n        if (reason === void 0) { reason = undefined; }\n        if (!IsReadableStreamBYOBReader(this)) {\n            return promiseRejectedWith(byobReaderBrandCheckException('cancel'));\n        }\n        if (this._ownerReadableStream === undefined) {\n            return promiseRejectedWith(readerLockException('cancel'));\n        }\n        return ReadableStreamReaderGenericCancel(this, reason);\n    };\n    ReadableStreamBYOBReader.prototype.read = function (view, rawOptions) {\n        if (rawOptions === void 0) { rawOptions = {}; }\n        if (!IsReadableStreamBYOBReader(this)) {\n            return promiseRejectedWith(byobReaderBrandCheckException('read'));\n        }\n        if (!ArrayBuffer.isView(view)) {\n            return promiseRejectedWith(new TypeError('view must be an array buffer view'));\n        }\n        if (view.byteLength === 0) {\n            return promiseRejectedWith(new TypeError('view must have non-zero byteLength'));\n        }\n        if (view.buffer.byteLength === 0) {\n            return promiseRejectedWith(new TypeError(\"view's buffer must have non-zero byteLength\"));\n        }\n        if (IsDetachedBuffer(view.buffer)) {\n            return promiseRejectedWith(new TypeError('view\\'s buffer has been detached'));\n        }\n        var options;\n        try {\n            options = convertByobReadOptions(rawOptions, 'options');\n        }\n        catch (e) {\n            return promiseRejectedWith(e);\n        }\n        var min = options.min;\n        if (min === 0) {\n            return promiseRejectedWith(new TypeError('options.min must be greater than 0'));\n        }\n        if (!isDataView(view)) {\n            if (min > view.length) {\n                return promiseRejectedWith(new RangeError('options.min must be less than or equal to view\\'s length'));\n            }\n        }\n        else if (min > view.byteLength) {\n            return promiseRejectedWith(new RangeError('options.min must be less than or equal to view\\'s byteLength'));\n        }\n        if (this._ownerReadableStream === undefined) {\n            return promiseRejectedWith(readerLockException('read from'));\n        }\n        var resolvePromise;\n        var rejectPromise;\n        var promise = newPromise(function (resolve, reject) {\n            resolvePromise = resolve;\n            rejectPromise = reject;\n        });\n        var readIntoRequest = {\n            _chunkSteps: function (chunk) { return resolvePromise({ value: chunk, done: false }); },\n            _closeSteps: function (chunk) { return resolvePromise({ value: chunk, done: true }); },\n            _errorSteps: function (e) { return rejectPromise(e); }\n        };\n        ReadableStreamBYOBReaderRead(this, view, min, readIntoRequest);\n        return promise;\n    };\n    /**\n     * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n     * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n     * from now on; otherwise, the reader will appear closed.\n     *\n     * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n     * the reader's {@link ReadableStreamBYOBReader.read | read()} method has not yet been settled. Attempting to\n     * do so will throw a `TypeError` and leave the reader locked to the stream.\n     */\n    ReadableStreamBYOBReader.prototype.releaseLock = function () {\n        if (!IsReadableStreamBYOBReader(this)) {\n            throw byobReaderBrandCheckException('releaseLock');\n        }\n        if (this._ownerReadableStream === undefined) {\n            return;\n        }\n        ReadableStreamBYOBReaderRelease(this);\n    };\n    return ReadableStreamBYOBReader;\n}());\nObject.defineProperties(ReadableStreamBYOBReader.prototype, {\n    cancel: { enumerable: true },\n    read: { enumerable: true },\n    releaseLock: { enumerable: true },\n    closed: { enumerable: true }\n});\nsetFunctionName(ReadableStreamBYOBReader.prototype.cancel, 'cancel');\nsetFunctionName(ReadableStreamBYOBReader.prototype.read, 'read');\nsetFunctionName(ReadableStreamBYOBReader.prototype.releaseLock, 'releaseLock');\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(ReadableStreamBYOBReader.prototype, SymbolPolyfill.toStringTag, {\n        value: 'ReadableStreamBYOBReader',\n        configurable: true\n    });\n}\n// Abstract operations for the readers.\nfunction IsReadableStreamBYOBReader(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_readIntoRequests')) {\n        return false;\n    }\n    return x instanceof ReadableStreamBYOBReader;\n}\nfunction ReadableStreamBYOBReaderRead(reader, view, min, readIntoRequest) {\n    var stream = reader._ownerReadableStream;\n    stream._disturbed = true;\n    if (stream._state === 'errored') {\n        readIntoRequest._errorSteps(stream._storedError);\n    }\n    else {\n        ReadableByteStreamControllerPullInto(stream._readableStreamController, view, min, readIntoRequest);\n    }\n}\nfunction ReadableStreamBYOBReaderRelease(reader) {\n    ReadableStreamReaderGenericRelease(reader);\n    var e = new TypeError('Reader was released');\n    ReadableStreamBYOBReaderErrorReadIntoRequests(reader, e);\n}\nfunction ReadableStreamBYOBReaderErrorReadIntoRequests(reader, e) {\n    var readIntoRequests = reader._readIntoRequests;\n    reader._readIntoRequests = new SimpleQueue();\n    readIntoRequests.forEach(function (readIntoRequest) {\n        readIntoRequest._errorSteps(e);\n    });\n}\n// Helper functions for the ReadableStreamBYOBReader.\nfunction byobReaderBrandCheckException(name) {\n    return new TypeError(\"ReadableStreamBYOBReader.prototype.\".concat(name, \" can only be used on a ReadableStreamBYOBReader\"));\n}\n\nfunction ExtractHighWaterMark(strategy, defaultHWM) {\n    var highWaterMark = strategy.highWaterMark;\n    if (highWaterMark === undefined) {\n        return defaultHWM;\n    }\n    if (NumberIsNaN(highWaterMark) || highWaterMark < 0) {\n        throw new RangeError('Invalid highWaterMark');\n    }\n    return highWaterMark;\n}\nfunction ExtractSizeAlgorithm(strategy) {\n    var size = strategy.size;\n    if (!size) {\n        return function () { return 1; };\n    }\n    return size;\n}\n\nfunction convertQueuingStrategy(init, context) {\n    assertDictionary(init, context);\n    var highWaterMark = init === null || init === void 0 ? void 0 : init.highWaterMark;\n    var size = init === null || init === void 0 ? void 0 : init.size;\n    return {\n        highWaterMark: highWaterMark === undefined ? undefined : convertUnrestrictedDouble(highWaterMark),\n        size: size === undefined ? undefined : convertQueuingStrategySize(size, \"\".concat(context, \" has member 'size' that\"))\n    };\n}\nfunction convertQueuingStrategySize(fn, context) {\n    assertFunction(fn, context);\n    return function (chunk) { return convertUnrestrictedDouble(fn(chunk)); };\n}\n\nfunction convertUnderlyingSink(original, context) {\n    assertDictionary(original, context);\n    var abort = original === null || original === void 0 ? void 0 : original.abort;\n    var close = original === null || original === void 0 ? void 0 : original.close;\n    var start = original === null || original === void 0 ? void 0 : original.start;\n    var type = original === null || original === void 0 ? void 0 : original.type;\n    var write = original === null || original === void 0 ? void 0 : original.write;\n    return {\n        abort: abort === undefined ?\n            undefined :\n            convertUnderlyingSinkAbortCallback(abort, original, \"\".concat(context, \" has member 'abort' that\")),\n        close: close === undefined ?\n            undefined :\n            convertUnderlyingSinkCloseCallback(close, original, \"\".concat(context, \" has member 'close' that\")),\n        start: start === undefined ?\n            undefined :\n            convertUnderlyingSinkStartCallback(start, original, \"\".concat(context, \" has member 'start' that\")),\n        write: write === undefined ?\n            undefined :\n            convertUnderlyingSinkWriteCallback(write, original, \"\".concat(context, \" has member 'write' that\")),\n        type: type\n    };\n}\nfunction convertUnderlyingSinkAbortCallback(fn, original, context) {\n    assertFunction(fn, context);\n    return function (reason) { return promiseCall(fn, original, [reason]); };\n}\nfunction convertUnderlyingSinkCloseCallback(fn, original, context) {\n    assertFunction(fn, context);\n    return function () { return promiseCall(fn, original, []); };\n}\nfunction convertUnderlyingSinkStartCallback(fn, original, context) {\n    assertFunction(fn, context);\n    return function (controller) { return reflectCall(fn, original, [controller]); };\n}\nfunction convertUnderlyingSinkWriteCallback(fn, original, context) {\n    assertFunction(fn, context);\n    return function (chunk, controller) { return promiseCall(fn, original, [chunk, controller]); };\n}\n\nfunction assertWritableStream(x, context) {\n    if (!IsWritableStream(x)) {\n        throw new TypeError(\"\".concat(context, \" is not a WritableStream.\"));\n    }\n}\n\nfunction isAbortSignal(value) {\n    if (typeof value !== 'object' || value === null) {\n        return false;\n    }\n    try {\n        return typeof value.aborted === 'boolean';\n    }\n    catch (_a) {\n        // AbortSignal.prototype.aborted throws if its brand check fails\n        return false;\n    }\n}\nvar supportsAbortController = typeof AbortController === 'function';\n/**\n * Construct a new AbortController, if supported by the platform.\n *\n * @internal\n */\nfunction createAbortController() {\n    if (supportsAbortController) {\n        return new AbortController();\n    }\n    return undefined;\n}\n\n/**\n * A writable stream represents a destination for data, into which you can write.\n *\n * @public\n */\nvar WritableStream = /** @class */ (function () {\n    function WritableStream(rawUnderlyingSink, rawStrategy) {\n        if (rawUnderlyingSink === void 0) { rawUnderlyingSink = {}; }\n        if (rawStrategy === void 0) { rawStrategy = {}; }\n        if (rawUnderlyingSink === undefined) {\n            rawUnderlyingSink = null;\n        }\n        else {\n            assertObject(rawUnderlyingSink, 'First parameter');\n        }\n        var strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n        var underlyingSink = convertUnderlyingSink(rawUnderlyingSink, 'First parameter');\n        InitializeWritableStream(this);\n        var type = underlyingSink.type;\n        if (type !== undefined) {\n            throw new RangeError('Invalid type is specified');\n        }\n        var sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n        var highWaterMark = ExtractHighWaterMark(strategy, 1);\n        SetUpWritableStreamDefaultControllerFromUnderlyingSink(this, underlyingSink, highWaterMark, sizeAlgorithm);\n    }\n    Object.defineProperty(WritableStream.prototype, \"locked\", {\n        /**\n         * Returns whether or not the writable stream is locked to a writer.\n         */\n        get: function () {\n            if (!IsWritableStream(this)) {\n                throw streamBrandCheckException$2('locked');\n            }\n            return IsWritableStreamLocked(this);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Aborts the stream, signaling that the producer can no longer successfully write to the stream and it is to be\n     * immediately moved to an errored state, with any queued-up writes discarded. This will also execute any abort\n     * mechanism of the underlying sink.\n     *\n     * The returned promise will fulfill if the stream shuts down successfully, or reject if the underlying sink signaled\n     * that there was an error doing so. Additionally, it will reject with a `TypeError` (without attempting to cancel\n     * the stream) if the stream is currently locked.\n     */\n    WritableStream.prototype.abort = function (reason) {\n        if (reason === void 0) { reason = undefined; }\n        if (!IsWritableStream(this)) {\n            return promiseRejectedWith(streamBrandCheckException$2('abort'));\n        }\n        if (IsWritableStreamLocked(this)) {\n            return promiseRejectedWith(new TypeError('Cannot abort a stream that already has a writer'));\n        }\n        return WritableStreamAbort(this, reason);\n    };\n    /**\n     * Closes the stream. The underlying sink will finish processing any previously-written chunks, before invoking its\n     * close behavior. During this time any further attempts to write will fail (without erroring the stream).\n     *\n     * The method returns a promise that will fulfill if all remaining chunks are successfully written and the stream\n     * successfully closes, or rejects if an error is encountered during this process. Additionally, it will reject with\n     * a `TypeError` (without attempting to cancel the stream) if the stream is currently locked.\n     */\n    WritableStream.prototype.close = function () {\n        if (!IsWritableStream(this)) {\n            return promiseRejectedWith(streamBrandCheckException$2('close'));\n        }\n        if (IsWritableStreamLocked(this)) {\n            return promiseRejectedWith(new TypeError('Cannot close a stream that already has a writer'));\n        }\n        if (WritableStreamCloseQueuedOrInFlight(this)) {\n            return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n        }\n        return WritableStreamClose(this);\n    };\n    /**\n     * Creates a {@link WritableStreamDefaultWriter | writer} and locks the stream to the new writer. While the stream\n     * is locked, no other writer can be acquired until this one is released.\n     *\n     * This functionality is especially useful for creating abstractions that desire the ability to write to a stream\n     * without interruption or interleaving. By getting a writer for the stream, you can ensure nobody else can write at\n     * the same time, which would cause the resulting written data to be unpredictable and probably useless.\n     */\n    WritableStream.prototype.getWriter = function () {\n        if (!IsWritableStream(this)) {\n            throw streamBrandCheckException$2('getWriter');\n        }\n        return AcquireWritableStreamDefaultWriter(this);\n    };\n    return WritableStream;\n}());\nObject.defineProperties(WritableStream.prototype, {\n    abort: { enumerable: true },\n    close: { enumerable: true },\n    getWriter: { enumerable: true },\n    locked: { enumerable: true }\n});\nsetFunctionName(WritableStream.prototype.abort, 'abort');\nsetFunctionName(WritableStream.prototype.close, 'close');\nsetFunctionName(WritableStream.prototype.getWriter, 'getWriter');\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(WritableStream.prototype, SymbolPolyfill.toStringTag, {\n        value: 'WritableStream',\n        configurable: true\n    });\n}\n// Abstract operations for the WritableStream.\nfunction AcquireWritableStreamDefaultWriter(stream) {\n    return new WritableStreamDefaultWriter(stream);\n}\n// Throws if and only if startAlgorithm throws.\nfunction CreateWritableStream(startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm) {\n    if (highWaterMark === void 0) { highWaterMark = 1; }\n    if (sizeAlgorithm === void 0) { sizeAlgorithm = function () { return 1; }; }\n    var stream = Object.create(WritableStream.prototype);\n    InitializeWritableStream(stream);\n    var controller = Object.create(WritableStreamDefaultController.prototype);\n    SetUpWritableStreamDefaultController(stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm);\n    return stream;\n}\nfunction InitializeWritableStream(stream) {\n    stream._state = 'writable';\n    // The error that will be reported by new method calls once the state becomes errored. Only set when [[state]] is\n    // 'erroring' or 'errored'. May be set to an undefined value.\n    stream._storedError = undefined;\n    stream._writer = undefined;\n    // Initialize to undefined first because the constructor of the controller checks this\n    // variable to validate the caller.\n    stream._writableStreamController = undefined;\n    // This queue is placed here instead of the writer class in order to allow for passing a writer to the next data\n    // producer without waiting for the queued writes to finish.\n    stream._writeRequests = new SimpleQueue();\n    // Write requests are removed from _writeRequests when write() is called on the underlying sink. This prevents\n    // them from being erroneously rejected on error. If a write() call is in-flight, the request is stored here.\n    stream._inFlightWriteRequest = undefined;\n    // The promise that was returned from writer.close(). Stored here because it may be fulfilled after the writer\n    // has been detached.\n    stream._closeRequest = undefined;\n    // Close request is removed from _closeRequest when close() is called on the underlying sink. This prevents it\n    // from being erroneously rejected on error. If a close() call is in-flight, the request is stored here.\n    stream._inFlightCloseRequest = undefined;\n    // The promise that was returned from writer.abort(). This may also be fulfilled after the writer has detached.\n    stream._pendingAbortRequest = undefined;\n    // The backpressure signal set by the controller.\n    stream._backpressure = false;\n}\nfunction IsWritableStream(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_writableStreamController')) {\n        return false;\n    }\n    return x instanceof WritableStream;\n}\nfunction IsWritableStreamLocked(stream) {\n    if (stream._writer === undefined) {\n        return false;\n    }\n    return true;\n}\nfunction WritableStreamAbort(stream, reason) {\n    var _a;\n    if (stream._state === 'closed' || stream._state === 'errored') {\n        return promiseResolvedWith(undefined);\n    }\n    stream._writableStreamController._abortReason = reason;\n    (_a = stream._writableStreamController._abortController) === null || _a === void 0 ? void 0 : _a.abort(reason);\n    // TypeScript narrows the type of `stream._state` down to 'writable' | 'erroring',\n    // but it doesn't know that signaling abort runs author code that might have changed the state.\n    // Widen the type again by casting to WritableStreamState.\n    var state = stream._state;\n    if (state === 'closed' || state === 'errored') {\n        return promiseResolvedWith(undefined);\n    }\n    if (stream._pendingAbortRequest !== undefined) {\n        return stream._pendingAbortRequest._promise;\n    }\n    var wasAlreadyErroring = false;\n    if (state === 'erroring') {\n        wasAlreadyErroring = true;\n        // reason will not be used, so don't keep a reference to it.\n        reason = undefined;\n    }\n    var promise = newPromise(function (resolve, reject) {\n        stream._pendingAbortRequest = {\n            _promise: undefined,\n            _resolve: resolve,\n            _reject: reject,\n            _reason: reason,\n            _wasAlreadyErroring: wasAlreadyErroring\n        };\n    });\n    stream._pendingAbortRequest._promise = promise;\n    if (!wasAlreadyErroring) {\n        WritableStreamStartErroring(stream, reason);\n    }\n    return promise;\n}\nfunction WritableStreamClose(stream) {\n    var state = stream._state;\n    if (state === 'closed' || state === 'errored') {\n        return promiseRejectedWith(new TypeError(\"The stream (in \".concat(state, \" state) is not in the writable state and cannot be closed\")));\n    }\n    var promise = newPromise(function (resolve, reject) {\n        var closeRequest = {\n            _resolve: resolve,\n            _reject: reject\n        };\n        stream._closeRequest = closeRequest;\n    });\n    var writer = stream._writer;\n    if (writer !== undefined && stream._backpressure && state === 'writable') {\n        defaultWriterReadyPromiseResolve(writer);\n    }\n    WritableStreamDefaultControllerClose(stream._writableStreamController);\n    return promise;\n}\n// WritableStream API exposed for controllers.\nfunction WritableStreamAddWriteRequest(stream) {\n    var promise = newPromise(function (resolve, reject) {\n        var writeRequest = {\n            _resolve: resolve,\n            _reject: reject\n        };\n        stream._writeRequests.push(writeRequest);\n    });\n    return promise;\n}\nfunction WritableStreamDealWithRejection(stream, error) {\n    var state = stream._state;\n    if (state === 'writable') {\n        WritableStreamStartErroring(stream, error);\n        return;\n    }\n    WritableStreamFinishErroring(stream);\n}\nfunction WritableStreamStartErroring(stream, reason) {\n    var controller = stream._writableStreamController;\n    stream._state = 'erroring';\n    stream._storedError = reason;\n    var writer = stream._writer;\n    if (writer !== undefined) {\n        WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, reason);\n    }\n    if (!WritableStreamHasOperationMarkedInFlight(stream) && controller._started) {\n        WritableStreamFinishErroring(stream);\n    }\n}\nfunction WritableStreamFinishErroring(stream) {\n    stream._state = 'errored';\n    stream._writableStreamController[ErrorSteps]();\n    var storedError = stream._storedError;\n    stream._writeRequests.forEach(function (writeRequest) {\n        writeRequest._reject(storedError);\n    });\n    stream._writeRequests = new SimpleQueue();\n    if (stream._pendingAbortRequest === undefined) {\n        WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n        return;\n    }\n    var abortRequest = stream._pendingAbortRequest;\n    stream._pendingAbortRequest = undefined;\n    if (abortRequest._wasAlreadyErroring) {\n        abortRequest._reject(storedError);\n        WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n        return;\n    }\n    var promise = stream._writableStreamController[AbortSteps](abortRequest._reason);\n    uponPromise(promise, function () {\n        abortRequest._resolve();\n        WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n        return null;\n    }, function (reason) {\n        abortRequest._reject(reason);\n        WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n        return null;\n    });\n}\nfunction WritableStreamFinishInFlightWrite(stream) {\n    stream._inFlightWriteRequest._resolve(undefined);\n    stream._inFlightWriteRequest = undefined;\n}\nfunction WritableStreamFinishInFlightWriteWithError(stream, error) {\n    stream._inFlightWriteRequest._reject(error);\n    stream._inFlightWriteRequest = undefined;\n    WritableStreamDealWithRejection(stream, error);\n}\nfunction WritableStreamFinishInFlightClose(stream) {\n    stream._inFlightCloseRequest._resolve(undefined);\n    stream._inFlightCloseRequest = undefined;\n    var state = stream._state;\n    if (state === 'erroring') {\n        // The error was too late to do anything, so it is ignored.\n        stream._storedError = undefined;\n        if (stream._pendingAbortRequest !== undefined) {\n            stream._pendingAbortRequest._resolve();\n            stream._pendingAbortRequest = undefined;\n        }\n    }\n    stream._state = 'closed';\n    var writer = stream._writer;\n    if (writer !== undefined) {\n        defaultWriterClosedPromiseResolve(writer);\n    }\n}\nfunction WritableStreamFinishInFlightCloseWithError(stream, error) {\n    stream._inFlightCloseRequest._reject(error);\n    stream._inFlightCloseRequest = undefined;\n    // Never execute sink abort() after sink close().\n    if (stream._pendingAbortRequest !== undefined) {\n        stream._pendingAbortRequest._reject(error);\n        stream._pendingAbortRequest = undefined;\n    }\n    WritableStreamDealWithRejection(stream, error);\n}\n// TODO(ricea): Fix alphabetical order.\nfunction WritableStreamCloseQueuedOrInFlight(stream) {\n    if (stream._closeRequest === undefined && stream._inFlightCloseRequest === undefined) {\n        return false;\n    }\n    return true;\n}\nfunction WritableStreamHasOperationMarkedInFlight(stream) {\n    if (stream._inFlightWriteRequest === undefined && stream._inFlightCloseRequest === undefined) {\n        return false;\n    }\n    return true;\n}\nfunction WritableStreamMarkCloseRequestInFlight(stream) {\n    stream._inFlightCloseRequest = stream._closeRequest;\n    stream._closeRequest = undefined;\n}\nfunction WritableStreamMarkFirstWriteRequestInFlight(stream) {\n    stream._inFlightWriteRequest = stream._writeRequests.shift();\n}\nfunction WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream) {\n    if (stream._closeRequest !== undefined) {\n        stream._closeRequest._reject(stream._storedError);\n        stream._closeRequest = undefined;\n    }\n    var writer = stream._writer;\n    if (writer !== undefined) {\n        defaultWriterClosedPromiseReject(writer, stream._storedError);\n    }\n}\nfunction WritableStreamUpdateBackpressure(stream, backpressure) {\n    var writer = stream._writer;\n    if (writer !== undefined && backpressure !== stream._backpressure) {\n        if (backpressure) {\n            defaultWriterReadyPromiseReset(writer);\n        }\n        else {\n            defaultWriterReadyPromiseResolve(writer);\n        }\n    }\n    stream._backpressure = backpressure;\n}\n/**\n * A default writer vended by a {@link WritableStream}.\n *\n * @public\n */\nvar WritableStreamDefaultWriter = /** @class */ (function () {\n    function WritableStreamDefaultWriter(stream) {\n        assertRequiredArgument(stream, 1, 'WritableStreamDefaultWriter');\n        assertWritableStream(stream, 'First parameter');\n        if (IsWritableStreamLocked(stream)) {\n            throw new TypeError('This stream has already been locked for exclusive writing by another writer');\n        }\n        this._ownerWritableStream = stream;\n        stream._writer = this;\n        var state = stream._state;\n        if (state === 'writable') {\n            if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._backpressure) {\n                defaultWriterReadyPromiseInitialize(this);\n            }\n            else {\n                defaultWriterReadyPromiseInitializeAsResolved(this);\n            }\n            defaultWriterClosedPromiseInitialize(this);\n        }\n        else if (state === 'erroring') {\n            defaultWriterReadyPromiseInitializeAsRejected(this, stream._storedError);\n            defaultWriterClosedPromiseInitialize(this);\n        }\n        else if (state === 'closed') {\n            defaultWriterReadyPromiseInitializeAsResolved(this);\n            defaultWriterClosedPromiseInitializeAsResolved(this);\n        }\n        else {\n            var storedError = stream._storedError;\n            defaultWriterReadyPromiseInitializeAsRejected(this, storedError);\n            defaultWriterClosedPromiseInitializeAsRejected(this, storedError);\n        }\n    }\n    Object.defineProperty(WritableStreamDefaultWriter.prototype, \"closed\", {\n        /**\n         * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n         * the writer’s lock is released before the stream finishes closing.\n         */\n        get: function () {\n            if (!IsWritableStreamDefaultWriter(this)) {\n                return promiseRejectedWith(defaultWriterBrandCheckException('closed'));\n            }\n            return this._closedPromise;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(WritableStreamDefaultWriter.prototype, \"desiredSize\", {\n        /**\n         * Returns the desired size to fill the stream’s internal queue. It can be negative, if the queue is over-full.\n         * A producer can use this information to determine the right amount of data to write.\n         *\n         * It will be `null` if the stream cannot be successfully written to (due to either being errored, or having an abort\n         * queued up). It will return zero if the stream is closed. And the getter will throw an exception if invoked when\n         * the writer’s lock is released.\n         */\n        get: function () {\n            if (!IsWritableStreamDefaultWriter(this)) {\n                throw defaultWriterBrandCheckException('desiredSize');\n            }\n            if (this._ownerWritableStream === undefined) {\n                throw defaultWriterLockException('desiredSize');\n            }\n            return WritableStreamDefaultWriterGetDesiredSize(this);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(WritableStreamDefaultWriter.prototype, \"ready\", {\n        /**\n         * Returns a promise that will be fulfilled when the desired size to fill the stream’s internal queue transitions\n         * from non-positive to positive, signaling that it is no longer applying backpressure. Once the desired size dips\n         * back to zero or below, the getter will return a new promise that stays pending until the next transition.\n         *\n         * If the stream becomes errored or aborted, or the writer’s lock is released, the returned promise will become\n         * rejected.\n         */\n        get: function () {\n            if (!IsWritableStreamDefaultWriter(this)) {\n                return promiseRejectedWith(defaultWriterBrandCheckException('ready'));\n            }\n            return this._readyPromise;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * If the reader is active, behaves the same as {@link WritableStream.abort | stream.abort(reason)}.\n     */\n    WritableStreamDefaultWriter.prototype.abort = function (reason) {\n        if (reason === void 0) { reason = undefined; }\n        if (!IsWritableStreamDefaultWriter(this)) {\n            return promiseRejectedWith(defaultWriterBrandCheckException('abort'));\n        }\n        if (this._ownerWritableStream === undefined) {\n            return promiseRejectedWith(defaultWriterLockException('abort'));\n        }\n        return WritableStreamDefaultWriterAbort(this, reason);\n    };\n    /**\n     * If the reader is active, behaves the same as {@link WritableStream.close | stream.close()}.\n     */\n    WritableStreamDefaultWriter.prototype.close = function () {\n        if (!IsWritableStreamDefaultWriter(this)) {\n            return promiseRejectedWith(defaultWriterBrandCheckException('close'));\n        }\n        var stream = this._ownerWritableStream;\n        if (stream === undefined) {\n            return promiseRejectedWith(defaultWriterLockException('close'));\n        }\n        if (WritableStreamCloseQueuedOrInFlight(stream)) {\n            return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n        }\n        return WritableStreamDefaultWriterClose(this);\n    };\n    /**\n     * Releases the writer’s lock on the corresponding stream. After the lock is released, the writer is no longer active.\n     * If the associated stream is errored when the lock is released, the writer will appear errored in the same way from\n     * now on; otherwise, the writer will appear closed.\n     *\n     * Note that the lock can still be released even if some ongoing writes have not yet finished (i.e. even if the\n     * promises returned from previous calls to {@link WritableStreamDefaultWriter.write | write()} have not yet settled).\n     * It’s not necessary to hold the lock on the writer for the duration of the write; the lock instead simply prevents\n     * other producers from writing in an interleaved manner.\n     */\n    WritableStreamDefaultWriter.prototype.releaseLock = function () {\n        if (!IsWritableStreamDefaultWriter(this)) {\n            throw defaultWriterBrandCheckException('releaseLock');\n        }\n        var stream = this._ownerWritableStream;\n        if (stream === undefined) {\n            return;\n        }\n        WritableStreamDefaultWriterRelease(this);\n    };\n    WritableStreamDefaultWriter.prototype.write = function (chunk) {\n        if (chunk === void 0) { chunk = undefined; }\n        if (!IsWritableStreamDefaultWriter(this)) {\n            return promiseRejectedWith(defaultWriterBrandCheckException('write'));\n        }\n        if (this._ownerWritableStream === undefined) {\n            return promiseRejectedWith(defaultWriterLockException('write to'));\n        }\n        return WritableStreamDefaultWriterWrite(this, chunk);\n    };\n    return WritableStreamDefaultWriter;\n}());\nObject.defineProperties(WritableStreamDefaultWriter.prototype, {\n    abort: { enumerable: true },\n    close: { enumerable: true },\n    releaseLock: { enumerable: true },\n    write: { enumerable: true },\n    closed: { enumerable: true },\n    desiredSize: { enumerable: true },\n    ready: { enumerable: true }\n});\nsetFunctionName(WritableStreamDefaultWriter.prototype.abort, 'abort');\nsetFunctionName(WritableStreamDefaultWriter.prototype.close, 'close');\nsetFunctionName(WritableStreamDefaultWriter.prototype.releaseLock, 'releaseLock');\nsetFunctionName(WritableStreamDefaultWriter.prototype.write, 'write');\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(WritableStreamDefaultWriter.prototype, SymbolPolyfill.toStringTag, {\n        value: 'WritableStreamDefaultWriter',\n        configurable: true\n    });\n}\n// Abstract operations for the WritableStreamDefaultWriter.\nfunction IsWritableStreamDefaultWriter(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_ownerWritableStream')) {\n        return false;\n    }\n    return x instanceof WritableStreamDefaultWriter;\n}\n// A client of WritableStreamDefaultWriter may use these functions directly to bypass state check.\nfunction WritableStreamDefaultWriterAbort(writer, reason) {\n    var stream = writer._ownerWritableStream;\n    return WritableStreamAbort(stream, reason);\n}\nfunction WritableStreamDefaultWriterClose(writer) {\n    var stream = writer._ownerWritableStream;\n    return WritableStreamClose(stream);\n}\nfunction WritableStreamDefaultWriterCloseWithErrorPropagation(writer) {\n    var stream = writer._ownerWritableStream;\n    var state = stream._state;\n    if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n        return promiseResolvedWith(undefined);\n    }\n    if (state === 'errored') {\n        return promiseRejectedWith(stream._storedError);\n    }\n    return WritableStreamDefaultWriterClose(writer);\n}\nfunction WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer, error) {\n    if (writer._closedPromiseState === 'pending') {\n        defaultWriterClosedPromiseReject(writer, error);\n    }\n    else {\n        defaultWriterClosedPromiseResetToRejected(writer, error);\n    }\n}\nfunction WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, error) {\n    if (writer._readyPromiseState === 'pending') {\n        defaultWriterReadyPromiseReject(writer, error);\n    }\n    else {\n        defaultWriterReadyPromiseResetToRejected(writer, error);\n    }\n}\nfunction WritableStreamDefaultWriterGetDesiredSize(writer) {\n    var stream = writer._ownerWritableStream;\n    var state = stream._state;\n    if (state === 'errored' || state === 'erroring') {\n        return null;\n    }\n    if (state === 'closed') {\n        return 0;\n    }\n    return WritableStreamDefaultControllerGetDesiredSize(stream._writableStreamController);\n}\nfunction WritableStreamDefaultWriterRelease(writer) {\n    var stream = writer._ownerWritableStream;\n    var releasedError = new TypeError(\"Writer was released and can no longer be used to monitor the stream's closedness\");\n    WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, releasedError);\n    // The state transitions to \"errored\" before the sink abort() method runs, but the writer.closed promise is not\n    // rejected until afterwards. This means that simply testing state will not work.\n    WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer, releasedError);\n    stream._writer = undefined;\n    writer._ownerWritableStream = undefined;\n}\nfunction WritableStreamDefaultWriterWrite(writer, chunk) {\n    var stream = writer._ownerWritableStream;\n    var controller = stream._writableStreamController;\n    var chunkSize = WritableStreamDefaultControllerGetChunkSize(controller, chunk);\n    if (stream !== writer._ownerWritableStream) {\n        return promiseRejectedWith(defaultWriterLockException('write to'));\n    }\n    var state = stream._state;\n    if (state === 'errored') {\n        return promiseRejectedWith(stream._storedError);\n    }\n    if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n        return promiseRejectedWith(new TypeError('The stream is closing or closed and cannot be written to'));\n    }\n    if (state === 'erroring') {\n        return promiseRejectedWith(stream._storedError);\n    }\n    var promise = WritableStreamAddWriteRequest(stream);\n    WritableStreamDefaultControllerWrite(controller, chunk, chunkSize);\n    return promise;\n}\nvar closeSentinel = {};\n/**\n * Allows control of a {@link WritableStream | writable stream}'s state and internal queue.\n *\n * @public\n */\nvar WritableStreamDefaultController = /** @class */ (function () {\n    function WritableStreamDefaultController() {\n        throw new TypeError('Illegal constructor');\n    }\n    Object.defineProperty(WritableStreamDefaultController.prototype, \"abortReason\", {\n        /**\n         * The reason which was passed to `WritableStream.abort(reason)` when the stream was aborted.\n         *\n         * @deprecated\n         *  This property has been removed from the specification, see https://github.com/whatwg/streams/pull/1177.\n         *  Use {@link WritableStreamDefaultController.signal}'s `reason` instead.\n         */\n        get: function () {\n            if (!IsWritableStreamDefaultController(this)) {\n                throw defaultControllerBrandCheckException$2('abortReason');\n            }\n            return this._abortReason;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(WritableStreamDefaultController.prototype, \"signal\", {\n        /**\n         * An `AbortSignal` that can be used to abort the pending write or close operation when the stream is aborted.\n         */\n        get: function () {\n            if (!IsWritableStreamDefaultController(this)) {\n                throw defaultControllerBrandCheckException$2('signal');\n            }\n            if (this._abortController === undefined) {\n                // Older browsers or older Node versions may not support `AbortController` or `AbortSignal`.\n                // We don't want to bundle and ship an `AbortController` polyfill together with our polyfill,\n                // so instead we only implement support for `signal` if we find a global `AbortController` constructor.\n                throw new TypeError('WritableStreamDefaultController.prototype.signal is not supported');\n            }\n            return this._abortController.signal;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Closes the controlled writable stream, making all future interactions with it fail with the given error `e`.\n     *\n     * This method is rarely used, since usually it suffices to return a rejected promise from one of the underlying\n     * sink's methods. However, it can be useful for suddenly shutting down a stream in response to an event outside the\n     * normal lifecycle of interactions with the underlying sink.\n     */\n    WritableStreamDefaultController.prototype.error = function (e) {\n        if (e === void 0) { e = undefined; }\n        if (!IsWritableStreamDefaultController(this)) {\n            throw defaultControllerBrandCheckException$2('error');\n        }\n        var state = this._controlledWritableStream._state;\n        if (state !== 'writable') {\n            // The stream is closed, errored or will be soon. The sink can't do anything useful if it gets an error here, so\n            // just treat it as a no-op.\n            return;\n        }\n        WritableStreamDefaultControllerError(this, e);\n    };\n    /** @internal */\n    WritableStreamDefaultController.prototype[AbortSteps] = function (reason) {\n        var result = this._abortAlgorithm(reason);\n        WritableStreamDefaultControllerClearAlgorithms(this);\n        return result;\n    };\n    /** @internal */\n    WritableStreamDefaultController.prototype[ErrorSteps] = function () {\n        ResetQueue(this);\n    };\n    return WritableStreamDefaultController;\n}());\nObject.defineProperties(WritableStreamDefaultController.prototype, {\n    abortReason: { enumerable: true },\n    signal: { enumerable: true },\n    error: { enumerable: true }\n});\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(WritableStreamDefaultController.prototype, SymbolPolyfill.toStringTag, {\n        value: 'WritableStreamDefaultController',\n        configurable: true\n    });\n}\n// Abstract operations implementing interface required by the WritableStream.\nfunction IsWritableStreamDefaultController(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_controlledWritableStream')) {\n        return false;\n    }\n    return x instanceof WritableStreamDefaultController;\n}\nfunction SetUpWritableStreamDefaultController(stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm) {\n    controller._controlledWritableStream = stream;\n    stream._writableStreamController = controller;\n    // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n    controller._queue = undefined;\n    controller._queueTotalSize = undefined;\n    ResetQueue(controller);\n    controller._abortReason = undefined;\n    controller._abortController = createAbortController();\n    controller._started = false;\n    controller._strategySizeAlgorithm = sizeAlgorithm;\n    controller._strategyHWM = highWaterMark;\n    controller._writeAlgorithm = writeAlgorithm;\n    controller._closeAlgorithm = closeAlgorithm;\n    controller._abortAlgorithm = abortAlgorithm;\n    var backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n    WritableStreamUpdateBackpressure(stream, backpressure);\n    var startResult = startAlgorithm();\n    var startPromise = promiseResolvedWith(startResult);\n    uponPromise(startPromise, function () {\n        controller._started = true;\n        WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n        return null;\n    }, function (r) {\n        controller._started = true;\n        WritableStreamDealWithRejection(stream, r);\n        return null;\n    });\n}\nfunction SetUpWritableStreamDefaultControllerFromUnderlyingSink(stream, underlyingSink, highWaterMark, sizeAlgorithm) {\n    var controller = Object.create(WritableStreamDefaultController.prototype);\n    var startAlgorithm;\n    var writeAlgorithm;\n    var closeAlgorithm;\n    var abortAlgorithm;\n    if (underlyingSink.start !== undefined) {\n        startAlgorithm = function () { return underlyingSink.start(controller); };\n    }\n    else {\n        startAlgorithm = function () { return undefined; };\n    }\n    if (underlyingSink.write !== undefined) {\n        writeAlgorithm = function (chunk) { return underlyingSink.write(chunk, controller); };\n    }\n    else {\n        writeAlgorithm = function () { return promiseResolvedWith(undefined); };\n    }\n    if (underlyingSink.close !== undefined) {\n        closeAlgorithm = function () { return underlyingSink.close(); };\n    }\n    else {\n        closeAlgorithm = function () { return promiseResolvedWith(undefined); };\n    }\n    if (underlyingSink.abort !== undefined) {\n        abortAlgorithm = function (reason) { return underlyingSink.abort(reason); };\n    }\n    else {\n        abortAlgorithm = function () { return promiseResolvedWith(undefined); };\n    }\n    SetUpWritableStreamDefaultController(stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm);\n}\n// ClearAlgorithms may be called twice. Erroring the same stream in multiple ways will often result in redundant calls.\nfunction WritableStreamDefaultControllerClearAlgorithms(controller) {\n    controller._writeAlgorithm = undefined;\n    controller._closeAlgorithm = undefined;\n    controller._abortAlgorithm = undefined;\n    controller._strategySizeAlgorithm = undefined;\n}\nfunction WritableStreamDefaultControllerClose(controller) {\n    EnqueueValueWithSize(controller, closeSentinel, 0);\n    WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\nfunction WritableStreamDefaultControllerGetChunkSize(controller, chunk) {\n    try {\n        return controller._strategySizeAlgorithm(chunk);\n    }\n    catch (chunkSizeE) {\n        WritableStreamDefaultControllerErrorIfNeeded(controller, chunkSizeE);\n        return 1;\n    }\n}\nfunction WritableStreamDefaultControllerGetDesiredSize(controller) {\n    return controller._strategyHWM - controller._queueTotalSize;\n}\nfunction WritableStreamDefaultControllerWrite(controller, chunk, chunkSize) {\n    try {\n        EnqueueValueWithSize(controller, chunk, chunkSize);\n    }\n    catch (enqueueE) {\n        WritableStreamDefaultControllerErrorIfNeeded(controller, enqueueE);\n        return;\n    }\n    var stream = controller._controlledWritableStream;\n    if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._state === 'writable') {\n        var backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n        WritableStreamUpdateBackpressure(stream, backpressure);\n    }\n    WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n// Abstract operations for the WritableStreamDefaultController.\nfunction WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller) {\n    var stream = controller._controlledWritableStream;\n    if (!controller._started) {\n        return;\n    }\n    if (stream._inFlightWriteRequest !== undefined) {\n        return;\n    }\n    var state = stream._state;\n    if (state === 'erroring') {\n        WritableStreamFinishErroring(stream);\n        return;\n    }\n    if (controller._queue.length === 0) {\n        return;\n    }\n    var value = PeekQueueValue(controller);\n    if (value === closeSentinel) {\n        WritableStreamDefaultControllerProcessClose(controller);\n    }\n    else {\n        WritableStreamDefaultControllerProcessWrite(controller, value);\n    }\n}\nfunction WritableStreamDefaultControllerErrorIfNeeded(controller, error) {\n    if (controller._controlledWritableStream._state === 'writable') {\n        WritableStreamDefaultControllerError(controller, error);\n    }\n}\nfunction WritableStreamDefaultControllerProcessClose(controller) {\n    var stream = controller._controlledWritableStream;\n    WritableStreamMarkCloseRequestInFlight(stream);\n    DequeueValue(controller);\n    var sinkClosePromise = controller._closeAlgorithm();\n    WritableStreamDefaultControllerClearAlgorithms(controller);\n    uponPromise(sinkClosePromise, function () {\n        WritableStreamFinishInFlightClose(stream);\n        return null;\n    }, function (reason) {\n        WritableStreamFinishInFlightCloseWithError(stream, reason);\n        return null;\n    });\n}\nfunction WritableStreamDefaultControllerProcessWrite(controller, chunk) {\n    var stream = controller._controlledWritableStream;\n    WritableStreamMarkFirstWriteRequestInFlight(stream);\n    var sinkWritePromise = controller._writeAlgorithm(chunk);\n    uponPromise(sinkWritePromise, function () {\n        WritableStreamFinishInFlightWrite(stream);\n        var state = stream._state;\n        DequeueValue(controller);\n        if (!WritableStreamCloseQueuedOrInFlight(stream) && state === 'writable') {\n            var backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n            WritableStreamUpdateBackpressure(stream, backpressure);\n        }\n        WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n        return null;\n    }, function (reason) {\n        if (stream._state === 'writable') {\n            WritableStreamDefaultControllerClearAlgorithms(controller);\n        }\n        WritableStreamFinishInFlightWriteWithError(stream, reason);\n        return null;\n    });\n}\nfunction WritableStreamDefaultControllerGetBackpressure(controller) {\n    var desiredSize = WritableStreamDefaultControllerGetDesiredSize(controller);\n    return desiredSize <= 0;\n}\n// A client of WritableStreamDefaultController may use these functions directly to bypass state check.\nfunction WritableStreamDefaultControllerError(controller, error) {\n    var stream = controller._controlledWritableStream;\n    WritableStreamDefaultControllerClearAlgorithms(controller);\n    WritableStreamStartErroring(stream, error);\n}\n// Helper functions for the WritableStream.\nfunction streamBrandCheckException$2(name) {\n    return new TypeError(\"WritableStream.prototype.\".concat(name, \" can only be used on a WritableStream\"));\n}\n// Helper functions for the WritableStreamDefaultController.\nfunction defaultControllerBrandCheckException$2(name) {\n    return new TypeError(\"WritableStreamDefaultController.prototype.\".concat(name, \" can only be used on a WritableStreamDefaultController\"));\n}\n// Helper functions for the WritableStreamDefaultWriter.\nfunction defaultWriterBrandCheckException(name) {\n    return new TypeError(\"WritableStreamDefaultWriter.prototype.\".concat(name, \" can only be used on a WritableStreamDefaultWriter\"));\n}\nfunction defaultWriterLockException(name) {\n    return new TypeError('Cannot ' + name + ' a stream using a released writer');\n}\nfunction defaultWriterClosedPromiseInitialize(writer) {\n    writer._closedPromise = newPromise(function (resolve, reject) {\n        writer._closedPromise_resolve = resolve;\n        writer._closedPromise_reject = reject;\n        writer._closedPromiseState = 'pending';\n    });\n}\nfunction defaultWriterClosedPromiseInitializeAsRejected(writer, reason) {\n    defaultWriterClosedPromiseInitialize(writer);\n    defaultWriterClosedPromiseReject(writer, reason);\n}\nfunction defaultWriterClosedPromiseInitializeAsResolved(writer) {\n    defaultWriterClosedPromiseInitialize(writer);\n    defaultWriterClosedPromiseResolve(writer);\n}\nfunction defaultWriterClosedPromiseReject(writer, reason) {\n    if (writer._closedPromise_reject === undefined) {\n        return;\n    }\n    setPromiseIsHandledToTrue(writer._closedPromise);\n    writer._closedPromise_reject(reason);\n    writer._closedPromise_resolve = undefined;\n    writer._closedPromise_reject = undefined;\n    writer._closedPromiseState = 'rejected';\n}\nfunction defaultWriterClosedPromiseResetToRejected(writer, reason) {\n    defaultWriterClosedPromiseInitializeAsRejected(writer, reason);\n}\nfunction defaultWriterClosedPromiseResolve(writer) {\n    if (writer._closedPromise_resolve === undefined) {\n        return;\n    }\n    writer._closedPromise_resolve(undefined);\n    writer._closedPromise_resolve = undefined;\n    writer._closedPromise_reject = undefined;\n    writer._closedPromiseState = 'resolved';\n}\nfunction defaultWriterReadyPromiseInitialize(writer) {\n    writer._readyPromise = newPromise(function (resolve, reject) {\n        writer._readyPromise_resolve = resolve;\n        writer._readyPromise_reject = reject;\n    });\n    writer._readyPromiseState = 'pending';\n}\nfunction defaultWriterReadyPromiseInitializeAsRejected(writer, reason) {\n    defaultWriterReadyPromiseInitialize(writer);\n    defaultWriterReadyPromiseReject(writer, reason);\n}\nfunction defaultWriterReadyPromiseInitializeAsResolved(writer) {\n    defaultWriterReadyPromiseInitialize(writer);\n    defaultWriterReadyPromiseResolve(writer);\n}\nfunction defaultWriterReadyPromiseReject(writer, reason) {\n    if (writer._readyPromise_reject === undefined) {\n        return;\n    }\n    setPromiseIsHandledToTrue(writer._readyPromise);\n    writer._readyPromise_reject(reason);\n    writer._readyPromise_resolve = undefined;\n    writer._readyPromise_reject = undefined;\n    writer._readyPromiseState = 'rejected';\n}\nfunction defaultWriterReadyPromiseReset(writer) {\n    defaultWriterReadyPromiseInitialize(writer);\n}\nfunction defaultWriterReadyPromiseResetToRejected(writer, reason) {\n    defaultWriterReadyPromiseInitializeAsRejected(writer, reason);\n}\nfunction defaultWriterReadyPromiseResolve(writer) {\n    if (writer._readyPromise_resolve === undefined) {\n        return;\n    }\n    writer._readyPromise_resolve(undefined);\n    writer._readyPromise_resolve = undefined;\n    writer._readyPromise_reject = undefined;\n    writer._readyPromiseState = 'fulfilled';\n}\n\n/// <reference lib=\"dom\" />\nfunction getGlobals() {\n    if (typeof globalThis !== 'undefined') {\n        return globalThis;\n    }\n    else if (typeof self !== 'undefined') {\n        return self;\n    }\n    else if (typeof global !== 'undefined') {\n        return global;\n    }\n    return undefined;\n}\nvar globals = getGlobals();\n\n/// <reference types=\"node\" />\nfunction isDOMExceptionConstructor(ctor) {\n    if (!(typeof ctor === 'function' || typeof ctor === 'object')) {\n        return false;\n    }\n    if (ctor.name !== 'DOMException') {\n        return false;\n    }\n    try {\n        new ctor();\n        return true;\n    }\n    catch (_a) {\n        return false;\n    }\n}\n/**\n * Support:\n * - Web browsers\n * - Node 18 and higher (https://github.com/nodejs/node/commit/e4b1fb5e6422c1ff151234bb9de792d45dd88d87)\n */\nfunction getFromGlobal() {\n    var ctor = globals === null || globals === void 0 ? void 0 : globals.DOMException;\n    return isDOMExceptionConstructor(ctor) ? ctor : undefined;\n}\n/**\n * Support:\n * - All platforms\n */\nfunction createPolyfill() {\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    var ctor = function DOMException(message, name) {\n        this.message = message || '';\n        this.name = name || 'Error';\n        if (Error.captureStackTrace) {\n            Error.captureStackTrace(this, this.constructor);\n        }\n    };\n    setFunctionName(ctor, 'DOMException');\n    ctor.prototype = Object.create(Error.prototype);\n    Object.defineProperty(ctor.prototype, 'constructor', { value: ctor, writable: true, configurable: true });\n    return ctor;\n}\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nvar DOMException = getFromGlobal() || createPolyfill();\n\nfunction ReadableStreamPipeTo(source, dest, preventClose, preventAbort, preventCancel, signal) {\n    var reader = AcquireReadableStreamDefaultReader(source);\n    var writer = AcquireWritableStreamDefaultWriter(dest);\n    source._disturbed = true;\n    var shuttingDown = false;\n    // This is used to keep track of the spec's requirement that we wait for ongoing writes during shutdown.\n    var currentWrite = promiseResolvedWith(undefined);\n    return newPromise(function (resolve, reject) {\n        var abortAlgorithm;\n        if (signal !== undefined) {\n            abortAlgorithm = function () {\n                var error = signal.reason !== undefined ? signal.reason : new DOMException('Aborted', 'AbortError');\n                var actions = [];\n                if (!preventAbort) {\n                    actions.push(function () {\n                        if (dest._state === 'writable') {\n                            return WritableStreamAbort(dest, error);\n                        }\n                        return promiseResolvedWith(undefined);\n                    });\n                }\n                if (!preventCancel) {\n                    actions.push(function () {\n                        if (source._state === 'readable') {\n                            return ReadableStreamCancel(source, error);\n                        }\n                        return promiseResolvedWith(undefined);\n                    });\n                }\n                shutdownWithAction(function () { return Promise.all(actions.map(function (action) { return action(); })); }, true, error);\n            };\n            if (signal.aborted) {\n                abortAlgorithm();\n                return;\n            }\n            signal.addEventListener('abort', abortAlgorithm);\n        }\n        // Using reader and writer, read all chunks from this and write them to dest\n        // - Backpressure must be enforced\n        // - Shutdown must stop all activity\n        function pipeLoop() {\n            return newPromise(function (resolveLoop, rejectLoop) {\n                function next(done) {\n                    if (done) {\n                        resolveLoop();\n                    }\n                    else {\n                        // Use `PerformPromiseThen` instead of `uponPromise` to avoid\n                        // adding unnecessary `.catch(rethrowAssertionErrorRejection)` handlers\n                        PerformPromiseThen(pipeStep(), next, rejectLoop);\n                    }\n                }\n                next(false);\n            });\n        }\n        function pipeStep() {\n            if (shuttingDown) {\n                return promiseResolvedWith(true);\n            }\n            return PerformPromiseThen(writer._readyPromise, function () {\n                return newPromise(function (resolveRead, rejectRead) {\n                    ReadableStreamDefaultReaderRead(reader, {\n                        _chunkSteps: function (chunk) {\n                            currentWrite = PerformPromiseThen(WritableStreamDefaultWriterWrite(writer, chunk), undefined, noop);\n                            resolveRead(false);\n                        },\n                        _closeSteps: function () { return resolveRead(true); },\n                        _errorSteps: rejectRead\n                    });\n                });\n            });\n        }\n        // Errors must be propagated forward\n        isOrBecomesErrored(source, reader._closedPromise, function (storedError) {\n            if (!preventAbort) {\n                shutdownWithAction(function () { return WritableStreamAbort(dest, storedError); }, true, storedError);\n            }\n            else {\n                shutdown(true, storedError);\n            }\n            return null;\n        });\n        // Errors must be propagated backward\n        isOrBecomesErrored(dest, writer._closedPromise, function (storedError) {\n            if (!preventCancel) {\n                shutdownWithAction(function () { return ReadableStreamCancel(source, storedError); }, true, storedError);\n            }\n            else {\n                shutdown(true, storedError);\n            }\n            return null;\n        });\n        // Closing must be propagated forward\n        isOrBecomesClosed(source, reader._closedPromise, function () {\n            if (!preventClose) {\n                shutdownWithAction(function () { return WritableStreamDefaultWriterCloseWithErrorPropagation(writer); });\n            }\n            else {\n                shutdown();\n            }\n            return null;\n        });\n        // Closing must be propagated backward\n        if (WritableStreamCloseQueuedOrInFlight(dest) || dest._state === 'closed') {\n            var destClosed_1 = new TypeError('the destination writable stream closed before all data could be piped to it');\n            if (!preventCancel) {\n                shutdownWithAction(function () { return ReadableStreamCancel(source, destClosed_1); }, true, destClosed_1);\n            }\n            else {\n                shutdown(true, destClosed_1);\n            }\n        }\n        setPromiseIsHandledToTrue(pipeLoop());\n        function waitForWritesToFinish() {\n            // Another write may have started while we were waiting on this currentWrite, so we have to be sure to wait\n            // for that too.\n            var oldCurrentWrite = currentWrite;\n            return PerformPromiseThen(currentWrite, function () { return oldCurrentWrite !== currentWrite ? waitForWritesToFinish() : undefined; });\n        }\n        function isOrBecomesErrored(stream, promise, action) {\n            if (stream._state === 'errored') {\n                action(stream._storedError);\n            }\n            else {\n                uponRejection(promise, action);\n            }\n        }\n        function isOrBecomesClosed(stream, promise, action) {\n            if (stream._state === 'closed') {\n                action();\n            }\n            else {\n                uponFulfillment(promise, action);\n            }\n        }\n        function shutdownWithAction(action, originalIsError, originalError) {\n            if (shuttingDown) {\n                return;\n            }\n            shuttingDown = true;\n            if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n                uponFulfillment(waitForWritesToFinish(), doTheRest);\n            }\n            else {\n                doTheRest();\n            }\n            function doTheRest() {\n                uponPromise(action(), function () { return finalize(originalIsError, originalError); }, function (newError) { return finalize(true, newError); });\n                return null;\n            }\n        }\n        function shutdown(isError, error) {\n            if (shuttingDown) {\n                return;\n            }\n            shuttingDown = true;\n            if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n                uponFulfillment(waitForWritesToFinish(), function () { return finalize(isError, error); });\n            }\n            else {\n                finalize(isError, error);\n            }\n        }\n        function finalize(isError, error) {\n            WritableStreamDefaultWriterRelease(writer);\n            ReadableStreamReaderGenericRelease(reader);\n            if (signal !== undefined) {\n                signal.removeEventListener('abort', abortAlgorithm);\n            }\n            if (isError) {\n                reject(error);\n            }\n            else {\n                resolve(undefined);\n            }\n            return null;\n        }\n    });\n}\n\n/**\n * Allows control of a {@link ReadableStream | readable stream}'s state and internal queue.\n *\n * @public\n */\nvar ReadableStreamDefaultController = /** @class */ (function () {\n    function ReadableStreamDefaultController() {\n        throw new TypeError('Illegal constructor');\n    }\n    Object.defineProperty(ReadableStreamDefaultController.prototype, \"desiredSize\", {\n        /**\n         * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n         * over-full. An underlying source ought to use this information to determine when and how to apply backpressure.\n         */\n        get: function () {\n            if (!IsReadableStreamDefaultController(this)) {\n                throw defaultControllerBrandCheckException$1('desiredSize');\n            }\n            return ReadableStreamDefaultControllerGetDesiredSize(this);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n     * the stream, but once those are read, the stream will become closed.\n     */\n    ReadableStreamDefaultController.prototype.close = function () {\n        if (!IsReadableStreamDefaultController(this)) {\n            throw defaultControllerBrandCheckException$1('close');\n        }\n        if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n            throw new TypeError('The stream is not in a state that permits close');\n        }\n        ReadableStreamDefaultControllerClose(this);\n    };\n    ReadableStreamDefaultController.prototype.enqueue = function (chunk) {\n        if (chunk === void 0) { chunk = undefined; }\n        if (!IsReadableStreamDefaultController(this)) {\n            throw defaultControllerBrandCheckException$1('enqueue');\n        }\n        if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n            throw new TypeError('The stream is not in a state that permits enqueue');\n        }\n        return ReadableStreamDefaultControllerEnqueue(this, chunk);\n    };\n    /**\n     * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n     */\n    ReadableStreamDefaultController.prototype.error = function (e) {\n        if (e === void 0) { e = undefined; }\n        if (!IsReadableStreamDefaultController(this)) {\n            throw defaultControllerBrandCheckException$1('error');\n        }\n        ReadableStreamDefaultControllerError(this, e);\n    };\n    /** @internal */\n    ReadableStreamDefaultController.prototype[CancelSteps] = function (reason) {\n        ResetQueue(this);\n        var result = this._cancelAlgorithm(reason);\n        ReadableStreamDefaultControllerClearAlgorithms(this);\n        return result;\n    };\n    /** @internal */\n    ReadableStreamDefaultController.prototype[PullSteps] = function (readRequest) {\n        var stream = this._controlledReadableStream;\n        if (this._queue.length > 0) {\n            var chunk = DequeueValue(this);\n            if (this._closeRequested && this._queue.length === 0) {\n                ReadableStreamDefaultControllerClearAlgorithms(this);\n                ReadableStreamClose(stream);\n            }\n            else {\n                ReadableStreamDefaultControllerCallPullIfNeeded(this);\n            }\n            readRequest._chunkSteps(chunk);\n        }\n        else {\n            ReadableStreamAddReadRequest(stream, readRequest);\n            ReadableStreamDefaultControllerCallPullIfNeeded(this);\n        }\n    };\n    /** @internal */\n    ReadableStreamDefaultController.prototype[ReleaseSteps] = function () {\n        // Do nothing.\n    };\n    return ReadableStreamDefaultController;\n}());\nObject.defineProperties(ReadableStreamDefaultController.prototype, {\n    close: { enumerable: true },\n    enqueue: { enumerable: true },\n    error: { enumerable: true },\n    desiredSize: { enumerable: true }\n});\nsetFunctionName(ReadableStreamDefaultController.prototype.close, 'close');\nsetFunctionName(ReadableStreamDefaultController.prototype.enqueue, 'enqueue');\nsetFunctionName(ReadableStreamDefaultController.prototype.error, 'error');\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(ReadableStreamDefaultController.prototype, SymbolPolyfill.toStringTag, {\n        value: 'ReadableStreamDefaultController',\n        configurable: true\n    });\n}\n// Abstract operations for the ReadableStreamDefaultController.\nfunction IsReadableStreamDefaultController(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableStream')) {\n        return false;\n    }\n    return x instanceof ReadableStreamDefaultController;\n}\nfunction ReadableStreamDefaultControllerCallPullIfNeeded(controller) {\n    var shouldPull = ReadableStreamDefaultControllerShouldCallPull(controller);\n    if (!shouldPull) {\n        return;\n    }\n    if (controller._pulling) {\n        controller._pullAgain = true;\n        return;\n    }\n    controller._pulling = true;\n    var pullPromise = controller._pullAlgorithm();\n    uponPromise(pullPromise, function () {\n        controller._pulling = false;\n        if (controller._pullAgain) {\n            controller._pullAgain = false;\n            ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n        }\n        return null;\n    }, function (e) {\n        ReadableStreamDefaultControllerError(controller, e);\n        return null;\n    });\n}\nfunction ReadableStreamDefaultControllerShouldCallPull(controller) {\n    var stream = controller._controlledReadableStream;\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n        return false;\n    }\n    if (!controller._started) {\n        return false;\n    }\n    if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n        return true;\n    }\n    var desiredSize = ReadableStreamDefaultControllerGetDesiredSize(controller);\n    if (desiredSize > 0) {\n        return true;\n    }\n    return false;\n}\nfunction ReadableStreamDefaultControllerClearAlgorithms(controller) {\n    controller._pullAlgorithm = undefined;\n    controller._cancelAlgorithm = undefined;\n    controller._strategySizeAlgorithm = undefined;\n}\n// A client of ReadableStreamDefaultController may use these functions directly to bypass state check.\nfunction ReadableStreamDefaultControllerClose(controller) {\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n        return;\n    }\n    var stream = controller._controlledReadableStream;\n    controller._closeRequested = true;\n    if (controller._queue.length === 0) {\n        ReadableStreamDefaultControllerClearAlgorithms(controller);\n        ReadableStreamClose(stream);\n    }\n}\nfunction ReadableStreamDefaultControllerEnqueue(controller, chunk) {\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n        return;\n    }\n    var stream = controller._controlledReadableStream;\n    if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n        ReadableStreamFulfillReadRequest(stream, chunk, false);\n    }\n    else {\n        var chunkSize = void 0;\n        try {\n            chunkSize = controller._strategySizeAlgorithm(chunk);\n        }\n        catch (chunkSizeE) {\n            ReadableStreamDefaultControllerError(controller, chunkSizeE);\n            throw chunkSizeE;\n        }\n        try {\n            EnqueueValueWithSize(controller, chunk, chunkSize);\n        }\n        catch (enqueueE) {\n            ReadableStreamDefaultControllerError(controller, enqueueE);\n            throw enqueueE;\n        }\n    }\n    ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n}\nfunction ReadableStreamDefaultControllerError(controller, e) {\n    var stream = controller._controlledReadableStream;\n    if (stream._state !== 'readable') {\n        return;\n    }\n    ResetQueue(controller);\n    ReadableStreamDefaultControllerClearAlgorithms(controller);\n    ReadableStreamError(stream, e);\n}\nfunction ReadableStreamDefaultControllerGetDesiredSize(controller) {\n    var state = controller._controlledReadableStream._state;\n    if (state === 'errored') {\n        return null;\n    }\n    if (state === 'closed') {\n        return 0;\n    }\n    return controller._strategyHWM - controller._queueTotalSize;\n}\n// This is used in the implementation of TransformStream.\nfunction ReadableStreamDefaultControllerHasBackpressure(controller) {\n    if (ReadableStreamDefaultControllerShouldCallPull(controller)) {\n        return false;\n    }\n    return true;\n}\nfunction ReadableStreamDefaultControllerCanCloseOrEnqueue(controller) {\n    var state = controller._controlledReadableStream._state;\n    if (!controller._closeRequested && state === 'readable') {\n        return true;\n    }\n    return false;\n}\nfunction SetUpReadableStreamDefaultController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm) {\n    controller._controlledReadableStream = stream;\n    controller._queue = undefined;\n    controller._queueTotalSize = undefined;\n    ResetQueue(controller);\n    controller._started = false;\n    controller._closeRequested = false;\n    controller._pullAgain = false;\n    controller._pulling = false;\n    controller._strategySizeAlgorithm = sizeAlgorithm;\n    controller._strategyHWM = highWaterMark;\n    controller._pullAlgorithm = pullAlgorithm;\n    controller._cancelAlgorithm = cancelAlgorithm;\n    stream._readableStreamController = controller;\n    var startResult = startAlgorithm();\n    uponPromise(promiseResolvedWith(startResult), function () {\n        controller._started = true;\n        ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n        return null;\n    }, function (r) {\n        ReadableStreamDefaultControllerError(controller, r);\n        return null;\n    });\n}\nfunction SetUpReadableStreamDefaultControllerFromUnderlyingSource(stream, underlyingSource, highWaterMark, sizeAlgorithm) {\n    var controller = Object.create(ReadableStreamDefaultController.prototype);\n    var startAlgorithm;\n    var pullAlgorithm;\n    var cancelAlgorithm;\n    if (underlyingSource.start !== undefined) {\n        startAlgorithm = function () { return underlyingSource.start(controller); };\n    }\n    else {\n        startAlgorithm = function () { return undefined; };\n    }\n    if (underlyingSource.pull !== undefined) {\n        pullAlgorithm = function () { return underlyingSource.pull(controller); };\n    }\n    else {\n        pullAlgorithm = function () { return promiseResolvedWith(undefined); };\n    }\n    if (underlyingSource.cancel !== undefined) {\n        cancelAlgorithm = function (reason) { return underlyingSource.cancel(reason); };\n    }\n    else {\n        cancelAlgorithm = function () { return promiseResolvedWith(undefined); };\n    }\n    SetUpReadableStreamDefaultController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm);\n}\n// Helper functions for the ReadableStreamDefaultController.\nfunction defaultControllerBrandCheckException$1(name) {\n    return new TypeError(\"ReadableStreamDefaultController.prototype.\".concat(name, \" can only be used on a ReadableStreamDefaultController\"));\n}\n\nfunction ReadableStreamTee(stream, cloneForBranch2) {\n    if (IsReadableByteStreamController(stream._readableStreamController)) {\n        return ReadableByteStreamTee(stream);\n    }\n    return ReadableStreamDefaultTee(stream);\n}\nfunction ReadableStreamDefaultTee(stream, cloneForBranch2) {\n    var reader = AcquireReadableStreamDefaultReader(stream);\n    var reading = false;\n    var readAgain = false;\n    var canceled1 = false;\n    var canceled2 = false;\n    var reason1;\n    var reason2;\n    var branch1;\n    var branch2;\n    var resolveCancelPromise;\n    var cancelPromise = newPromise(function (resolve) {\n        resolveCancelPromise = resolve;\n    });\n    function pullAlgorithm() {\n        if (reading) {\n            readAgain = true;\n            return promiseResolvedWith(undefined);\n        }\n        reading = true;\n        var readRequest = {\n            _chunkSteps: function (chunk) {\n                // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n                // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n                // successful synchronously-available reads get ahead of asynchronously-available errors.\n                _queueMicrotask(function () {\n                    readAgain = false;\n                    var chunk1 = chunk;\n                    var chunk2 = chunk;\n                    // There is no way to access the cloning code right now in the reference implementation.\n                    // If we add one then we'll need an implementation for serializable objects.\n                    // if (!canceled2 && cloneForBranch2) {\n                    //   chunk2 = StructuredDeserialize(StructuredSerialize(chunk2));\n                    // }\n                    if (!canceled1) {\n                        ReadableStreamDefaultControllerEnqueue(branch1._readableStreamController, chunk1);\n                    }\n                    if (!canceled2) {\n                        ReadableStreamDefaultControllerEnqueue(branch2._readableStreamController, chunk2);\n                    }\n                    reading = false;\n                    if (readAgain) {\n                        pullAlgorithm();\n                    }\n                });\n            },\n            _closeSteps: function () {\n                reading = false;\n                if (!canceled1) {\n                    ReadableStreamDefaultControllerClose(branch1._readableStreamController);\n                }\n                if (!canceled2) {\n                    ReadableStreamDefaultControllerClose(branch2._readableStreamController);\n                }\n                if (!canceled1 || !canceled2) {\n                    resolveCancelPromise(undefined);\n                }\n            },\n            _errorSteps: function () {\n                reading = false;\n            }\n        };\n        ReadableStreamDefaultReaderRead(reader, readRequest);\n        return promiseResolvedWith(undefined);\n    }\n    function cancel1Algorithm(reason) {\n        canceled1 = true;\n        reason1 = reason;\n        if (canceled2) {\n            var compositeReason = CreateArrayFromList([reason1, reason2]);\n            var cancelResult = ReadableStreamCancel(stream, compositeReason);\n            resolveCancelPromise(cancelResult);\n        }\n        return cancelPromise;\n    }\n    function cancel2Algorithm(reason) {\n        canceled2 = true;\n        reason2 = reason;\n        if (canceled1) {\n            var compositeReason = CreateArrayFromList([reason1, reason2]);\n            var cancelResult = ReadableStreamCancel(stream, compositeReason);\n            resolveCancelPromise(cancelResult);\n        }\n        return cancelPromise;\n    }\n    function startAlgorithm() {\n        // do nothing\n    }\n    branch1 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel1Algorithm);\n    branch2 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel2Algorithm);\n    uponRejection(reader._closedPromise, function (r) {\n        ReadableStreamDefaultControllerError(branch1._readableStreamController, r);\n        ReadableStreamDefaultControllerError(branch2._readableStreamController, r);\n        if (!canceled1 || !canceled2) {\n            resolveCancelPromise(undefined);\n        }\n        return null;\n    });\n    return [branch1, branch2];\n}\nfunction ReadableByteStreamTee(stream) {\n    var reader = AcquireReadableStreamDefaultReader(stream);\n    var reading = false;\n    var readAgainForBranch1 = false;\n    var readAgainForBranch2 = false;\n    var canceled1 = false;\n    var canceled2 = false;\n    var reason1;\n    var reason2;\n    var branch1;\n    var branch2;\n    var resolveCancelPromise;\n    var cancelPromise = newPromise(function (resolve) {\n        resolveCancelPromise = resolve;\n    });\n    function forwardReaderError(thisReader) {\n        uponRejection(thisReader._closedPromise, function (r) {\n            if (thisReader !== reader) {\n                return null;\n            }\n            ReadableByteStreamControllerError(branch1._readableStreamController, r);\n            ReadableByteStreamControllerError(branch2._readableStreamController, r);\n            if (!canceled1 || !canceled2) {\n                resolveCancelPromise(undefined);\n            }\n            return null;\n        });\n    }\n    function pullWithDefaultReader() {\n        if (IsReadableStreamBYOBReader(reader)) {\n            ReadableStreamReaderGenericRelease(reader);\n            reader = AcquireReadableStreamDefaultReader(stream);\n            forwardReaderError(reader);\n        }\n        var readRequest = {\n            _chunkSteps: function (chunk) {\n                // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n                // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n                // successful synchronously-available reads get ahead of asynchronously-available errors.\n                _queueMicrotask(function () {\n                    readAgainForBranch1 = false;\n                    readAgainForBranch2 = false;\n                    var chunk1 = chunk;\n                    var chunk2 = chunk;\n                    if (!canceled1 && !canceled2) {\n                        try {\n                            chunk2 = CloneAsUint8Array(chunk);\n                        }\n                        catch (cloneE) {\n                            ReadableByteStreamControllerError(branch1._readableStreamController, cloneE);\n                            ReadableByteStreamControllerError(branch2._readableStreamController, cloneE);\n                            resolveCancelPromise(ReadableStreamCancel(stream, cloneE));\n                            return;\n                        }\n                    }\n                    if (!canceled1) {\n                        ReadableByteStreamControllerEnqueue(branch1._readableStreamController, chunk1);\n                    }\n                    if (!canceled2) {\n                        ReadableByteStreamControllerEnqueue(branch2._readableStreamController, chunk2);\n                    }\n                    reading = false;\n                    if (readAgainForBranch1) {\n                        pull1Algorithm();\n                    }\n                    else if (readAgainForBranch2) {\n                        pull2Algorithm();\n                    }\n                });\n            },\n            _closeSteps: function () {\n                reading = false;\n                if (!canceled1) {\n                    ReadableByteStreamControllerClose(branch1._readableStreamController);\n                }\n                if (!canceled2) {\n                    ReadableByteStreamControllerClose(branch2._readableStreamController);\n                }\n                if (branch1._readableStreamController._pendingPullIntos.length > 0) {\n                    ReadableByteStreamControllerRespond(branch1._readableStreamController, 0);\n                }\n                if (branch2._readableStreamController._pendingPullIntos.length > 0) {\n                    ReadableByteStreamControllerRespond(branch2._readableStreamController, 0);\n                }\n                if (!canceled1 || !canceled2) {\n                    resolveCancelPromise(undefined);\n                }\n            },\n            _errorSteps: function () {\n                reading = false;\n            }\n        };\n        ReadableStreamDefaultReaderRead(reader, readRequest);\n    }\n    function pullWithBYOBReader(view, forBranch2) {\n        if (IsReadableStreamDefaultReader(reader)) {\n            ReadableStreamReaderGenericRelease(reader);\n            reader = AcquireReadableStreamBYOBReader(stream);\n            forwardReaderError(reader);\n        }\n        var byobBranch = forBranch2 ? branch2 : branch1;\n        var otherBranch = forBranch2 ? branch1 : branch2;\n        var readIntoRequest = {\n            _chunkSteps: function (chunk) {\n                // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n                // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n                // successful synchronously-available reads get ahead of asynchronously-available errors.\n                _queueMicrotask(function () {\n                    readAgainForBranch1 = false;\n                    readAgainForBranch2 = false;\n                    var byobCanceled = forBranch2 ? canceled2 : canceled1;\n                    var otherCanceled = forBranch2 ? canceled1 : canceled2;\n                    if (!otherCanceled) {\n                        var clonedChunk = void 0;\n                        try {\n                            clonedChunk = CloneAsUint8Array(chunk);\n                        }\n                        catch (cloneE) {\n                            ReadableByteStreamControllerError(byobBranch._readableStreamController, cloneE);\n                            ReadableByteStreamControllerError(otherBranch._readableStreamController, cloneE);\n                            resolveCancelPromise(ReadableStreamCancel(stream, cloneE));\n                            return;\n                        }\n                        if (!byobCanceled) {\n                            ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n                        }\n                        ReadableByteStreamControllerEnqueue(otherBranch._readableStreamController, clonedChunk);\n                    }\n                    else if (!byobCanceled) {\n                        ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n                    }\n                    reading = false;\n                    if (readAgainForBranch1) {\n                        pull1Algorithm();\n                    }\n                    else if (readAgainForBranch2) {\n                        pull2Algorithm();\n                    }\n                });\n            },\n            _closeSteps: function (chunk) {\n                reading = false;\n                var byobCanceled = forBranch2 ? canceled2 : canceled1;\n                var otherCanceled = forBranch2 ? canceled1 : canceled2;\n                if (!byobCanceled) {\n                    ReadableByteStreamControllerClose(byobBranch._readableStreamController);\n                }\n                if (!otherCanceled) {\n                    ReadableByteStreamControllerClose(otherBranch._readableStreamController);\n                }\n                if (chunk !== undefined) {\n                    if (!byobCanceled) {\n                        ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n                    }\n                    if (!otherCanceled && otherBranch._readableStreamController._pendingPullIntos.length > 0) {\n                        ReadableByteStreamControllerRespond(otherBranch._readableStreamController, 0);\n                    }\n                }\n                if (!byobCanceled || !otherCanceled) {\n                    resolveCancelPromise(undefined);\n                }\n            },\n            _errorSteps: function () {\n                reading = false;\n            }\n        };\n        ReadableStreamBYOBReaderRead(reader, view, 1, readIntoRequest);\n    }\n    function pull1Algorithm() {\n        if (reading) {\n            readAgainForBranch1 = true;\n            return promiseResolvedWith(undefined);\n        }\n        reading = true;\n        var byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch1._readableStreamController);\n        if (byobRequest === null) {\n            pullWithDefaultReader();\n        }\n        else {\n            pullWithBYOBReader(byobRequest._view, false);\n        }\n        return promiseResolvedWith(undefined);\n    }\n    function pull2Algorithm() {\n        if (reading) {\n            readAgainForBranch2 = true;\n            return promiseResolvedWith(undefined);\n        }\n        reading = true;\n        var byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch2._readableStreamController);\n        if (byobRequest === null) {\n            pullWithDefaultReader();\n        }\n        else {\n            pullWithBYOBReader(byobRequest._view, true);\n        }\n        return promiseResolvedWith(undefined);\n    }\n    function cancel1Algorithm(reason) {\n        canceled1 = true;\n        reason1 = reason;\n        if (canceled2) {\n            var compositeReason = CreateArrayFromList([reason1, reason2]);\n            var cancelResult = ReadableStreamCancel(stream, compositeReason);\n            resolveCancelPromise(cancelResult);\n        }\n        return cancelPromise;\n    }\n    function cancel2Algorithm(reason) {\n        canceled2 = true;\n        reason2 = reason;\n        if (canceled1) {\n            var compositeReason = CreateArrayFromList([reason1, reason2]);\n            var cancelResult = ReadableStreamCancel(stream, compositeReason);\n            resolveCancelPromise(cancelResult);\n        }\n        return cancelPromise;\n    }\n    function startAlgorithm() {\n        return;\n    }\n    branch1 = CreateReadableByteStream(startAlgorithm, pull1Algorithm, cancel1Algorithm);\n    branch2 = CreateReadableByteStream(startAlgorithm, pull2Algorithm, cancel2Algorithm);\n    forwardReaderError(reader);\n    return [branch1, branch2];\n}\n\nfunction isReadableStreamLike(stream) {\n    return typeIsObject(stream) && typeof stream.getReader !== 'undefined';\n}\n\nfunction ReadableStreamFrom(source) {\n    if (isReadableStreamLike(source)) {\n        return ReadableStreamFromDefaultReader(source.getReader());\n    }\n    return ReadableStreamFromIterable(source);\n}\nfunction ReadableStreamFromIterable(asyncIterable) {\n    var stream;\n    var iteratorRecord = GetIterator(asyncIterable, 'async');\n    var startAlgorithm = noop;\n    function pullAlgorithm() {\n        var nextResult;\n        try {\n            nextResult = IteratorNext(iteratorRecord);\n        }\n        catch (e) {\n            return promiseRejectedWith(e);\n        }\n        var nextPromise = promiseResolvedWith(nextResult);\n        return transformPromiseWith(nextPromise, function (iterResult) {\n            if (!typeIsObject(iterResult)) {\n                throw new TypeError('The promise returned by the iterator.next() method must fulfill with an object');\n            }\n            var done = IteratorComplete(iterResult);\n            if (done) {\n                ReadableStreamDefaultControllerClose(stream._readableStreamController);\n            }\n            else {\n                var value = IteratorValue(iterResult);\n                ReadableStreamDefaultControllerEnqueue(stream._readableStreamController, value);\n            }\n        });\n    }\n    function cancelAlgorithm(reason) {\n        var iterator = iteratorRecord.iterator;\n        var returnMethod;\n        try {\n            returnMethod = GetMethod(iterator, 'return');\n        }\n        catch (e) {\n            return promiseRejectedWith(e);\n        }\n        if (returnMethod === undefined) {\n            return promiseResolvedWith(undefined);\n        }\n        var returnResult;\n        try {\n            returnResult = reflectCall(returnMethod, iterator, [reason]);\n        }\n        catch (e) {\n            return promiseRejectedWith(e);\n        }\n        var returnPromise = promiseResolvedWith(returnResult);\n        return transformPromiseWith(returnPromise, function (iterResult) {\n            if (!typeIsObject(iterResult)) {\n                throw new TypeError('The promise returned by the iterator.return() method must fulfill with an object');\n            }\n            return undefined;\n        });\n    }\n    stream = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, 0);\n    return stream;\n}\nfunction ReadableStreamFromDefaultReader(reader) {\n    var stream;\n    var startAlgorithm = noop;\n    function pullAlgorithm() {\n        var readPromise;\n        try {\n            readPromise = reader.read();\n        }\n        catch (e) {\n            return promiseRejectedWith(e);\n        }\n        return transformPromiseWith(readPromise, function (readResult) {\n            if (!typeIsObject(readResult)) {\n                throw new TypeError('The promise returned by the reader.read() method must fulfill with an object');\n            }\n            if (readResult.done) {\n                ReadableStreamDefaultControllerClose(stream._readableStreamController);\n            }\n            else {\n                var value = readResult.value;\n                ReadableStreamDefaultControllerEnqueue(stream._readableStreamController, value);\n            }\n        });\n    }\n    function cancelAlgorithm(reason) {\n        try {\n            return promiseResolvedWith(reader.cancel(reason));\n        }\n        catch (e) {\n            return promiseRejectedWith(e);\n        }\n    }\n    stream = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, 0);\n    return stream;\n}\n\nfunction convertUnderlyingDefaultOrByteSource(source, context) {\n    assertDictionary(source, context);\n    var original = source;\n    var autoAllocateChunkSize = original === null || original === void 0 ? void 0 : original.autoAllocateChunkSize;\n    var cancel = original === null || original === void 0 ? void 0 : original.cancel;\n    var pull = original === null || original === void 0 ? void 0 : original.pull;\n    var start = original === null || original === void 0 ? void 0 : original.start;\n    var type = original === null || original === void 0 ? void 0 : original.type;\n    return {\n        autoAllocateChunkSize: autoAllocateChunkSize === undefined ?\n            undefined :\n            convertUnsignedLongLongWithEnforceRange(autoAllocateChunkSize, \"\".concat(context, \" has member 'autoAllocateChunkSize' that\")),\n        cancel: cancel === undefined ?\n            undefined :\n            convertUnderlyingSourceCancelCallback(cancel, original, \"\".concat(context, \" has member 'cancel' that\")),\n        pull: pull === undefined ?\n            undefined :\n            convertUnderlyingSourcePullCallback(pull, original, \"\".concat(context, \" has member 'pull' that\")),\n        start: start === undefined ?\n            undefined :\n            convertUnderlyingSourceStartCallback(start, original, \"\".concat(context, \" has member 'start' that\")),\n        type: type === undefined ? undefined : convertReadableStreamType(type, \"\".concat(context, \" has member 'type' that\"))\n    };\n}\nfunction convertUnderlyingSourceCancelCallback(fn, original, context) {\n    assertFunction(fn, context);\n    return function (reason) { return promiseCall(fn, original, [reason]); };\n}\nfunction convertUnderlyingSourcePullCallback(fn, original, context) {\n    assertFunction(fn, context);\n    return function (controller) { return promiseCall(fn, original, [controller]); };\n}\nfunction convertUnderlyingSourceStartCallback(fn, original, context) {\n    assertFunction(fn, context);\n    return function (controller) { return reflectCall(fn, original, [controller]); };\n}\nfunction convertReadableStreamType(type, context) {\n    type = \"\".concat(type);\n    if (type !== 'bytes') {\n        throw new TypeError(\"\".concat(context, \" '\").concat(type, \"' is not a valid enumeration value for ReadableStreamType\"));\n    }\n    return type;\n}\n\nfunction convertIteratorOptions(options, context) {\n    assertDictionary(options, context);\n    var preventCancel = options === null || options === void 0 ? void 0 : options.preventCancel;\n    return { preventCancel: Boolean(preventCancel) };\n}\n\nfunction convertPipeOptions(options, context) {\n    assertDictionary(options, context);\n    var preventAbort = options === null || options === void 0 ? void 0 : options.preventAbort;\n    var preventCancel = options === null || options === void 0 ? void 0 : options.preventCancel;\n    var preventClose = options === null || options === void 0 ? void 0 : options.preventClose;\n    var signal = options === null || options === void 0 ? void 0 : options.signal;\n    if (signal !== undefined) {\n        assertAbortSignal(signal, \"\".concat(context, \" has member 'signal' that\"));\n    }\n    return {\n        preventAbort: Boolean(preventAbort),\n        preventCancel: Boolean(preventCancel),\n        preventClose: Boolean(preventClose),\n        signal: signal\n    };\n}\nfunction assertAbortSignal(signal, context) {\n    if (!isAbortSignal(signal)) {\n        throw new TypeError(\"\".concat(context, \" is not an AbortSignal.\"));\n    }\n}\n\nfunction convertReadableWritablePair(pair, context) {\n    assertDictionary(pair, context);\n    var readable = pair === null || pair === void 0 ? void 0 : pair.readable;\n    assertRequiredField(readable, 'readable', 'ReadableWritablePair');\n    assertReadableStream(readable, \"\".concat(context, \" has member 'readable' that\"));\n    var writable = pair === null || pair === void 0 ? void 0 : pair.writable;\n    assertRequiredField(writable, 'writable', 'ReadableWritablePair');\n    assertWritableStream(writable, \"\".concat(context, \" has member 'writable' that\"));\n    return { readable: readable, writable: writable };\n}\n\n/**\n * A readable stream represents a source of data, from which you can read.\n *\n * @public\n */\nvar ReadableStream = /** @class */ (function () {\n    function ReadableStream(rawUnderlyingSource, rawStrategy) {\n        if (rawUnderlyingSource === void 0) { rawUnderlyingSource = {}; }\n        if (rawStrategy === void 0) { rawStrategy = {}; }\n        if (rawUnderlyingSource === undefined) {\n            rawUnderlyingSource = null;\n        }\n        else {\n            assertObject(rawUnderlyingSource, 'First parameter');\n        }\n        var strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n        var underlyingSource = convertUnderlyingDefaultOrByteSource(rawUnderlyingSource, 'First parameter');\n        InitializeReadableStream(this);\n        if (underlyingSource.type === 'bytes') {\n            if (strategy.size !== undefined) {\n                throw new RangeError('The strategy for a byte stream cannot have a size function');\n            }\n            var highWaterMark = ExtractHighWaterMark(strategy, 0);\n            SetUpReadableByteStreamControllerFromUnderlyingSource(this, underlyingSource, highWaterMark);\n        }\n        else {\n            var sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n            var highWaterMark = ExtractHighWaterMark(strategy, 1);\n            SetUpReadableStreamDefaultControllerFromUnderlyingSource(this, underlyingSource, highWaterMark, sizeAlgorithm);\n        }\n    }\n    Object.defineProperty(ReadableStream.prototype, \"locked\", {\n        /**\n         * Whether or not the readable stream is locked to a {@link ReadableStreamDefaultReader | reader}.\n         */\n        get: function () {\n            if (!IsReadableStream(this)) {\n                throw streamBrandCheckException$1('locked');\n            }\n            return IsReadableStreamLocked(this);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Cancels the stream, signaling a loss of interest in the stream by a consumer.\n     *\n     * The supplied `reason` argument will be given to the underlying source's {@link UnderlyingSource.cancel | cancel()}\n     * method, which might or might not use it.\n     */\n    ReadableStream.prototype.cancel = function (reason) {\n        if (reason === void 0) { reason = undefined; }\n        if (!IsReadableStream(this)) {\n            return promiseRejectedWith(streamBrandCheckException$1('cancel'));\n        }\n        if (IsReadableStreamLocked(this)) {\n            return promiseRejectedWith(new TypeError('Cannot cancel a stream that already has a reader'));\n        }\n        return ReadableStreamCancel(this, reason);\n    };\n    ReadableStream.prototype.getReader = function (rawOptions) {\n        if (rawOptions === void 0) { rawOptions = undefined; }\n        if (!IsReadableStream(this)) {\n            throw streamBrandCheckException$1('getReader');\n        }\n        var options = convertReaderOptions(rawOptions, 'First parameter');\n        if (options.mode === undefined) {\n            return AcquireReadableStreamDefaultReader(this);\n        }\n        return AcquireReadableStreamBYOBReader(this);\n    };\n    ReadableStream.prototype.pipeThrough = function (rawTransform, rawOptions) {\n        if (rawOptions === void 0) { rawOptions = {}; }\n        if (!IsReadableStream(this)) {\n            throw streamBrandCheckException$1('pipeThrough');\n        }\n        assertRequiredArgument(rawTransform, 1, 'pipeThrough');\n        var transform = convertReadableWritablePair(rawTransform, 'First parameter');\n        var options = convertPipeOptions(rawOptions, 'Second parameter');\n        if (IsReadableStreamLocked(this)) {\n            throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream');\n        }\n        if (IsWritableStreamLocked(transform.writable)) {\n            throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream');\n        }\n        var promise = ReadableStreamPipeTo(this, transform.writable, options.preventClose, options.preventAbort, options.preventCancel, options.signal);\n        setPromiseIsHandledToTrue(promise);\n        return transform.readable;\n    };\n    ReadableStream.prototype.pipeTo = function (destination, rawOptions) {\n        if (rawOptions === void 0) { rawOptions = {}; }\n        if (!IsReadableStream(this)) {\n            return promiseRejectedWith(streamBrandCheckException$1('pipeTo'));\n        }\n        if (destination === undefined) {\n            return promiseRejectedWith(\"Parameter 1 is required in 'pipeTo'.\");\n        }\n        if (!IsWritableStream(destination)) {\n            return promiseRejectedWith(new TypeError(\"ReadableStream.prototype.pipeTo's first argument must be a WritableStream\"));\n        }\n        var options;\n        try {\n            options = convertPipeOptions(rawOptions, 'Second parameter');\n        }\n        catch (e) {\n            return promiseRejectedWith(e);\n        }\n        if (IsReadableStreamLocked(this)) {\n            return promiseRejectedWith(new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream'));\n        }\n        if (IsWritableStreamLocked(destination)) {\n            return promiseRejectedWith(new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream'));\n        }\n        return ReadableStreamPipeTo(this, destination, options.preventClose, options.preventAbort, options.preventCancel, options.signal);\n    };\n    /**\n     * Tees this readable stream, returning a two-element array containing the two resulting branches as\n     * new {@link ReadableStream} instances.\n     *\n     * Teeing a stream will lock it, preventing any other consumer from acquiring a reader.\n     * To cancel the stream, cancel both of the resulting branches; a composite cancellation reason will then be\n     * propagated to the stream's underlying source.\n     *\n     * Note that the chunks seen in each branch will be the same object. If the chunks are not immutable,\n     * this could allow interference between the two branches.\n     */\n    ReadableStream.prototype.tee = function () {\n        if (!IsReadableStream(this)) {\n            throw streamBrandCheckException$1('tee');\n        }\n        var branches = ReadableStreamTee(this);\n        return CreateArrayFromList(branches);\n    };\n    ReadableStream.prototype.values = function (rawOptions) {\n        if (rawOptions === void 0) { rawOptions = undefined; }\n        if (!IsReadableStream(this)) {\n            throw streamBrandCheckException$1('values');\n        }\n        var options = convertIteratorOptions(rawOptions, 'First parameter');\n        return AcquireReadableStreamAsyncIterator(this, options.preventCancel);\n    };\n    /**\n     * Creates a new ReadableStream wrapping the provided iterable or async iterable.\n     *\n     * This can be used to adapt various kinds of objects into a readable stream,\n     * such as an array, an async generator, or a Node.js readable stream.\n     */\n    ReadableStream.from = function (asyncIterable) {\n        return ReadableStreamFrom(asyncIterable);\n    };\n    return ReadableStream;\n}());\nObject.defineProperties(ReadableStream, {\n    from: { enumerable: true }\n});\nObject.defineProperties(ReadableStream.prototype, {\n    cancel: { enumerable: true },\n    getReader: { enumerable: true },\n    pipeThrough: { enumerable: true },\n    pipeTo: { enumerable: true },\n    tee: { enumerable: true },\n    values: { enumerable: true },\n    locked: { enumerable: true }\n});\nsetFunctionName(ReadableStream.from, 'from');\nsetFunctionName(ReadableStream.prototype.cancel, 'cancel');\nsetFunctionName(ReadableStream.prototype.getReader, 'getReader');\nsetFunctionName(ReadableStream.prototype.pipeThrough, 'pipeThrough');\nsetFunctionName(ReadableStream.prototype.pipeTo, 'pipeTo');\nsetFunctionName(ReadableStream.prototype.tee, 'tee');\nsetFunctionName(ReadableStream.prototype.values, 'values');\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(ReadableStream.prototype, SymbolPolyfill.toStringTag, {\n        value: 'ReadableStream',\n        configurable: true\n    });\n}\nif (typeof SymbolPolyfill.asyncIterator === 'symbol') {\n    Object.defineProperty(ReadableStream.prototype, SymbolPolyfill.asyncIterator, {\n        value: ReadableStream.prototype.values,\n        writable: true,\n        configurable: true\n    });\n}\n// Abstract operations for the ReadableStream.\n// Throws if and only if startAlgorithm throws.\nfunction CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm) {\n    if (highWaterMark === void 0) { highWaterMark = 1; }\n    if (sizeAlgorithm === void 0) { sizeAlgorithm = function () { return 1; }; }\n    var stream = Object.create(ReadableStream.prototype);\n    InitializeReadableStream(stream);\n    var controller = Object.create(ReadableStreamDefaultController.prototype);\n    SetUpReadableStreamDefaultController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm);\n    return stream;\n}\n// Throws if and only if startAlgorithm throws.\nfunction CreateReadableByteStream(startAlgorithm, pullAlgorithm, cancelAlgorithm) {\n    var stream = Object.create(ReadableStream.prototype);\n    InitializeReadableStream(stream);\n    var controller = Object.create(ReadableByteStreamController.prototype);\n    SetUpReadableByteStreamController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, 0, undefined);\n    return stream;\n}\nfunction InitializeReadableStream(stream) {\n    stream._state = 'readable';\n    stream._reader = undefined;\n    stream._storedError = undefined;\n    stream._disturbed = false;\n}\nfunction IsReadableStream(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_readableStreamController')) {\n        return false;\n    }\n    return x instanceof ReadableStream;\n}\nfunction IsReadableStreamLocked(stream) {\n    if (stream._reader === undefined) {\n        return false;\n    }\n    return true;\n}\n// ReadableStream API exposed for controllers.\nfunction ReadableStreamCancel(stream, reason) {\n    stream._disturbed = true;\n    if (stream._state === 'closed') {\n        return promiseResolvedWith(undefined);\n    }\n    if (stream._state === 'errored') {\n        return promiseRejectedWith(stream._storedError);\n    }\n    ReadableStreamClose(stream);\n    var reader = stream._reader;\n    if (reader !== undefined && IsReadableStreamBYOBReader(reader)) {\n        var readIntoRequests = reader._readIntoRequests;\n        reader._readIntoRequests = new SimpleQueue();\n        readIntoRequests.forEach(function (readIntoRequest) {\n            readIntoRequest._closeSteps(undefined);\n        });\n    }\n    var sourceCancelPromise = stream._readableStreamController[CancelSteps](reason);\n    return transformPromiseWith(sourceCancelPromise, noop);\n}\nfunction ReadableStreamClose(stream) {\n    stream._state = 'closed';\n    var reader = stream._reader;\n    if (reader === undefined) {\n        return;\n    }\n    defaultReaderClosedPromiseResolve(reader);\n    if (IsReadableStreamDefaultReader(reader)) {\n        var readRequests = reader._readRequests;\n        reader._readRequests = new SimpleQueue();\n        readRequests.forEach(function (readRequest) {\n            readRequest._closeSteps();\n        });\n    }\n}\nfunction ReadableStreamError(stream, e) {\n    stream._state = 'errored';\n    stream._storedError = e;\n    var reader = stream._reader;\n    if (reader === undefined) {\n        return;\n    }\n    defaultReaderClosedPromiseReject(reader, e);\n    if (IsReadableStreamDefaultReader(reader)) {\n        ReadableStreamDefaultReaderErrorReadRequests(reader, e);\n    }\n    else {\n        ReadableStreamBYOBReaderErrorReadIntoRequests(reader, e);\n    }\n}\n// Helper functions for the ReadableStream.\nfunction streamBrandCheckException$1(name) {\n    return new TypeError(\"ReadableStream.prototype.\".concat(name, \" can only be used on a ReadableStream\"));\n}\n\nfunction convertQueuingStrategyInit(init, context) {\n    assertDictionary(init, context);\n    var highWaterMark = init === null || init === void 0 ? void 0 : init.highWaterMark;\n    assertRequiredField(highWaterMark, 'highWaterMark', 'QueuingStrategyInit');\n    return {\n        highWaterMark: convertUnrestrictedDouble(highWaterMark)\n    };\n}\n\n// The size function must not have a prototype property nor be a constructor\nvar byteLengthSizeFunction = function (chunk) {\n    return chunk.byteLength;\n};\nsetFunctionName(byteLengthSizeFunction, 'size');\n/**\n * A queuing strategy that counts the number of bytes in each chunk.\n *\n * @public\n */\nvar ByteLengthQueuingStrategy = /** @class */ (function () {\n    function ByteLengthQueuingStrategy(options) {\n        assertRequiredArgument(options, 1, 'ByteLengthQueuingStrategy');\n        options = convertQueuingStrategyInit(options, 'First parameter');\n        this._byteLengthQueuingStrategyHighWaterMark = options.highWaterMark;\n    }\n    Object.defineProperty(ByteLengthQueuingStrategy.prototype, \"highWaterMark\", {\n        /**\n         * Returns the high water mark provided to the constructor.\n         */\n        get: function () {\n            if (!IsByteLengthQueuingStrategy(this)) {\n                throw byteLengthBrandCheckException('highWaterMark');\n            }\n            return this._byteLengthQueuingStrategyHighWaterMark;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(ByteLengthQueuingStrategy.prototype, \"size\", {\n        /**\n         * Measures the size of `chunk` by returning the value of its `byteLength` property.\n         */\n        get: function () {\n            if (!IsByteLengthQueuingStrategy(this)) {\n                throw byteLengthBrandCheckException('size');\n            }\n            return byteLengthSizeFunction;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return ByteLengthQueuingStrategy;\n}());\nObject.defineProperties(ByteLengthQueuingStrategy.prototype, {\n    highWaterMark: { enumerable: true },\n    size: { enumerable: true }\n});\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(ByteLengthQueuingStrategy.prototype, SymbolPolyfill.toStringTag, {\n        value: 'ByteLengthQueuingStrategy',\n        configurable: true\n    });\n}\n// Helper functions for the ByteLengthQueuingStrategy.\nfunction byteLengthBrandCheckException(name) {\n    return new TypeError(\"ByteLengthQueuingStrategy.prototype.\".concat(name, \" can only be used on a ByteLengthQueuingStrategy\"));\n}\nfunction IsByteLengthQueuingStrategy(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_byteLengthQueuingStrategyHighWaterMark')) {\n        return false;\n    }\n    return x instanceof ByteLengthQueuingStrategy;\n}\n\n// The size function must not have a prototype property nor be a constructor\nvar countSizeFunction = function () {\n    return 1;\n};\nsetFunctionName(countSizeFunction, 'size');\n/**\n * A queuing strategy that counts the number of chunks.\n *\n * @public\n */\nvar CountQueuingStrategy = /** @class */ (function () {\n    function CountQueuingStrategy(options) {\n        assertRequiredArgument(options, 1, 'CountQueuingStrategy');\n        options = convertQueuingStrategyInit(options, 'First parameter');\n        this._countQueuingStrategyHighWaterMark = options.highWaterMark;\n    }\n    Object.defineProperty(CountQueuingStrategy.prototype, \"highWaterMark\", {\n        /**\n         * Returns the high water mark provided to the constructor.\n         */\n        get: function () {\n            if (!IsCountQueuingStrategy(this)) {\n                throw countBrandCheckException('highWaterMark');\n            }\n            return this._countQueuingStrategyHighWaterMark;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(CountQueuingStrategy.prototype, \"size\", {\n        /**\n         * Measures the size of `chunk` by always returning 1.\n         * This ensures that the total queue size is a count of the number of chunks in the queue.\n         */\n        get: function () {\n            if (!IsCountQueuingStrategy(this)) {\n                throw countBrandCheckException('size');\n            }\n            return countSizeFunction;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return CountQueuingStrategy;\n}());\nObject.defineProperties(CountQueuingStrategy.prototype, {\n    highWaterMark: { enumerable: true },\n    size: { enumerable: true }\n});\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(CountQueuingStrategy.prototype, SymbolPolyfill.toStringTag, {\n        value: 'CountQueuingStrategy',\n        configurable: true\n    });\n}\n// Helper functions for the CountQueuingStrategy.\nfunction countBrandCheckException(name) {\n    return new TypeError(\"CountQueuingStrategy.prototype.\".concat(name, \" can only be used on a CountQueuingStrategy\"));\n}\nfunction IsCountQueuingStrategy(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_countQueuingStrategyHighWaterMark')) {\n        return false;\n    }\n    return x instanceof CountQueuingStrategy;\n}\n\nfunction convertTransformer(original, context) {\n    assertDictionary(original, context);\n    var cancel = original === null || original === void 0 ? void 0 : original.cancel;\n    var flush = original === null || original === void 0 ? void 0 : original.flush;\n    var readableType = original === null || original === void 0 ? void 0 : original.readableType;\n    var start = original === null || original === void 0 ? void 0 : original.start;\n    var transform = original === null || original === void 0 ? void 0 : original.transform;\n    var writableType = original === null || original === void 0 ? void 0 : original.writableType;\n    return {\n        cancel: cancel === undefined ?\n            undefined :\n            convertTransformerCancelCallback(cancel, original, \"\".concat(context, \" has member 'cancel' that\")),\n        flush: flush === undefined ?\n            undefined :\n            convertTransformerFlushCallback(flush, original, \"\".concat(context, \" has member 'flush' that\")),\n        readableType: readableType,\n        start: start === undefined ?\n            undefined :\n            convertTransformerStartCallback(start, original, \"\".concat(context, \" has member 'start' that\")),\n        transform: transform === undefined ?\n            undefined :\n            convertTransformerTransformCallback(transform, original, \"\".concat(context, \" has member 'transform' that\")),\n        writableType: writableType\n    };\n}\nfunction convertTransformerFlushCallback(fn, original, context) {\n    assertFunction(fn, context);\n    return function (controller) { return promiseCall(fn, original, [controller]); };\n}\nfunction convertTransformerStartCallback(fn, original, context) {\n    assertFunction(fn, context);\n    return function (controller) { return reflectCall(fn, original, [controller]); };\n}\nfunction convertTransformerTransformCallback(fn, original, context) {\n    assertFunction(fn, context);\n    return function (chunk, controller) { return promiseCall(fn, original, [chunk, controller]); };\n}\nfunction convertTransformerCancelCallback(fn, original, context) {\n    assertFunction(fn, context);\n    return function (reason) { return promiseCall(fn, original, [reason]); };\n}\n\n// Class TransformStream\n/**\n * A transform stream consists of a pair of streams: a {@link WritableStream | writable stream},\n * known as its writable side, and a {@link ReadableStream | readable stream}, known as its readable side.\n * In a manner specific to the transform stream in question, writes to the writable side result in new data being\n * made available for reading from the readable side.\n *\n * @public\n */\nvar TransformStream = /** @class */ (function () {\n    function TransformStream(rawTransformer, rawWritableStrategy, rawReadableStrategy) {\n        if (rawTransformer === void 0) { rawTransformer = {}; }\n        if (rawWritableStrategy === void 0) { rawWritableStrategy = {}; }\n        if (rawReadableStrategy === void 0) { rawReadableStrategy = {}; }\n        if (rawTransformer === undefined) {\n            rawTransformer = null;\n        }\n        var writableStrategy = convertQueuingStrategy(rawWritableStrategy, 'Second parameter');\n        var readableStrategy = convertQueuingStrategy(rawReadableStrategy, 'Third parameter');\n        var transformer = convertTransformer(rawTransformer, 'First parameter');\n        if (transformer.readableType !== undefined) {\n            throw new RangeError('Invalid readableType specified');\n        }\n        if (transformer.writableType !== undefined) {\n            throw new RangeError('Invalid writableType specified');\n        }\n        var readableHighWaterMark = ExtractHighWaterMark(readableStrategy, 0);\n        var readableSizeAlgorithm = ExtractSizeAlgorithm(readableStrategy);\n        var writableHighWaterMark = ExtractHighWaterMark(writableStrategy, 1);\n        var writableSizeAlgorithm = ExtractSizeAlgorithm(writableStrategy);\n        var startPromise_resolve;\n        var startPromise = newPromise(function (resolve) {\n            startPromise_resolve = resolve;\n        });\n        InitializeTransformStream(this, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark, readableSizeAlgorithm);\n        SetUpTransformStreamDefaultControllerFromTransformer(this, transformer);\n        if (transformer.start !== undefined) {\n            startPromise_resolve(transformer.start(this._transformStreamController));\n        }\n        else {\n            startPromise_resolve(undefined);\n        }\n    }\n    Object.defineProperty(TransformStream.prototype, \"readable\", {\n        /**\n         * The readable side of the transform stream.\n         */\n        get: function () {\n            if (!IsTransformStream(this)) {\n                throw streamBrandCheckException('readable');\n            }\n            return this._readable;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(TransformStream.prototype, \"writable\", {\n        /**\n         * The writable side of the transform stream.\n         */\n        get: function () {\n            if (!IsTransformStream(this)) {\n                throw streamBrandCheckException('writable');\n            }\n            return this._writable;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return TransformStream;\n}());\nObject.defineProperties(TransformStream.prototype, {\n    readable: { enumerable: true },\n    writable: { enumerable: true }\n});\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(TransformStream.prototype, SymbolPolyfill.toStringTag, {\n        value: 'TransformStream',\n        configurable: true\n    });\n}\nfunction InitializeTransformStream(stream, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark, readableSizeAlgorithm) {\n    function startAlgorithm() {\n        return startPromise;\n    }\n    function writeAlgorithm(chunk) {\n        return TransformStreamDefaultSinkWriteAlgorithm(stream, chunk);\n    }\n    function abortAlgorithm(reason) {\n        return TransformStreamDefaultSinkAbortAlgorithm(stream, reason);\n    }\n    function closeAlgorithm() {\n        return TransformStreamDefaultSinkCloseAlgorithm(stream);\n    }\n    stream._writable = CreateWritableStream(startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, writableHighWaterMark, writableSizeAlgorithm);\n    function pullAlgorithm() {\n        return TransformStreamDefaultSourcePullAlgorithm(stream);\n    }\n    function cancelAlgorithm(reason) {\n        return TransformStreamDefaultSourceCancelAlgorithm(stream, reason);\n    }\n    stream._readable = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, readableHighWaterMark, readableSizeAlgorithm);\n    // The [[backpressure]] slot is set to undefined so that it can be initialised by TransformStreamSetBackpressure.\n    stream._backpressure = undefined;\n    stream._backpressureChangePromise = undefined;\n    stream._backpressureChangePromise_resolve = undefined;\n    TransformStreamSetBackpressure(stream, true);\n    stream._transformStreamController = undefined;\n}\nfunction IsTransformStream(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_transformStreamController')) {\n        return false;\n    }\n    return x instanceof TransformStream;\n}\n// This is a no-op if both sides are already errored.\nfunction TransformStreamError(stream, e) {\n    ReadableStreamDefaultControllerError(stream._readable._readableStreamController, e);\n    TransformStreamErrorWritableAndUnblockWrite(stream, e);\n}\nfunction TransformStreamErrorWritableAndUnblockWrite(stream, e) {\n    TransformStreamDefaultControllerClearAlgorithms(stream._transformStreamController);\n    WritableStreamDefaultControllerErrorIfNeeded(stream._writable._writableStreamController, e);\n    TransformStreamUnblockWrite(stream);\n}\nfunction TransformStreamUnblockWrite(stream) {\n    if (stream._backpressure) {\n        // Pretend that pull() was called to permit any pending write() calls to complete. TransformStreamSetBackpressure()\n        // cannot be called from enqueue() or pull() once the ReadableStream is errored, so this will will be the final time\n        // _backpressure is set.\n        TransformStreamSetBackpressure(stream, false);\n    }\n}\nfunction TransformStreamSetBackpressure(stream, backpressure) {\n    // Passes also when called during construction.\n    if (stream._backpressureChangePromise !== undefined) {\n        stream._backpressureChangePromise_resolve();\n    }\n    stream._backpressureChangePromise = newPromise(function (resolve) {\n        stream._backpressureChangePromise_resolve = resolve;\n    });\n    stream._backpressure = backpressure;\n}\n// Class TransformStreamDefaultController\n/**\n * Allows control of the {@link ReadableStream} and {@link WritableStream} of the associated {@link TransformStream}.\n *\n * @public\n */\nvar TransformStreamDefaultController = /** @class */ (function () {\n    function TransformStreamDefaultController() {\n        throw new TypeError('Illegal constructor');\n    }\n    Object.defineProperty(TransformStreamDefaultController.prototype, \"desiredSize\", {\n        /**\n         * Returns the desired size to fill the readable side’s internal queue. It can be negative, if the queue is over-full.\n         */\n        get: function () {\n            if (!IsTransformStreamDefaultController(this)) {\n                throw defaultControllerBrandCheckException('desiredSize');\n            }\n            var readableController = this._controlledTransformStream._readable._readableStreamController;\n            return ReadableStreamDefaultControllerGetDesiredSize(readableController);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    TransformStreamDefaultController.prototype.enqueue = function (chunk) {\n        if (chunk === void 0) { chunk = undefined; }\n        if (!IsTransformStreamDefaultController(this)) {\n            throw defaultControllerBrandCheckException('enqueue');\n        }\n        TransformStreamDefaultControllerEnqueue(this, chunk);\n    };\n    /**\n     * Errors both the readable side and the writable side of the controlled transform stream, making all future\n     * interactions with it fail with the given error `e`. Any chunks queued for transformation will be discarded.\n     */\n    TransformStreamDefaultController.prototype.error = function (reason) {\n        if (reason === void 0) { reason = undefined; }\n        if (!IsTransformStreamDefaultController(this)) {\n            throw defaultControllerBrandCheckException('error');\n        }\n        TransformStreamDefaultControllerError(this, reason);\n    };\n    /**\n     * Closes the readable side and errors the writable side of the controlled transform stream. This is useful when the\n     * transformer only needs to consume a portion of the chunks written to the writable side.\n     */\n    TransformStreamDefaultController.prototype.terminate = function () {\n        if (!IsTransformStreamDefaultController(this)) {\n            throw defaultControllerBrandCheckException('terminate');\n        }\n        TransformStreamDefaultControllerTerminate(this);\n    };\n    return TransformStreamDefaultController;\n}());\nObject.defineProperties(TransformStreamDefaultController.prototype, {\n    enqueue: { enumerable: true },\n    error: { enumerable: true },\n    terminate: { enumerable: true },\n    desiredSize: { enumerable: true }\n});\nsetFunctionName(TransformStreamDefaultController.prototype.enqueue, 'enqueue');\nsetFunctionName(TransformStreamDefaultController.prototype.error, 'error');\nsetFunctionName(TransformStreamDefaultController.prototype.terminate, 'terminate');\nif (typeof SymbolPolyfill.toStringTag === 'symbol') {\n    Object.defineProperty(TransformStreamDefaultController.prototype, SymbolPolyfill.toStringTag, {\n        value: 'TransformStreamDefaultController',\n        configurable: true\n    });\n}\n// Transform Stream Default Controller Abstract Operations\nfunction IsTransformStreamDefaultController(x) {\n    if (!typeIsObject(x)) {\n        return false;\n    }\n    if (!Object.prototype.hasOwnProperty.call(x, '_controlledTransformStream')) {\n        return false;\n    }\n    return x instanceof TransformStreamDefaultController;\n}\nfunction SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm, cancelAlgorithm) {\n    controller._controlledTransformStream = stream;\n    stream._transformStreamController = controller;\n    controller._transformAlgorithm = transformAlgorithm;\n    controller._flushAlgorithm = flushAlgorithm;\n    controller._cancelAlgorithm = cancelAlgorithm;\n    controller._finishPromise = undefined;\n    controller._finishPromise_resolve = undefined;\n    controller._finishPromise_reject = undefined;\n}\nfunction SetUpTransformStreamDefaultControllerFromTransformer(stream, transformer) {\n    var controller = Object.create(TransformStreamDefaultController.prototype);\n    var transformAlgorithm;\n    var flushAlgorithm;\n    var cancelAlgorithm;\n    if (transformer.transform !== undefined) {\n        transformAlgorithm = function (chunk) { return transformer.transform(chunk, controller); };\n    }\n    else {\n        transformAlgorithm = function (chunk) {\n            try {\n                TransformStreamDefaultControllerEnqueue(controller, chunk);\n                return promiseResolvedWith(undefined);\n            }\n            catch (transformResultE) {\n                return promiseRejectedWith(transformResultE);\n            }\n        };\n    }\n    if (transformer.flush !== undefined) {\n        flushAlgorithm = function () { return transformer.flush(controller); };\n    }\n    else {\n        flushAlgorithm = function () { return promiseResolvedWith(undefined); };\n    }\n    if (transformer.cancel !== undefined) {\n        cancelAlgorithm = function (reason) { return transformer.cancel(reason); };\n    }\n    else {\n        cancelAlgorithm = function () { return promiseResolvedWith(undefined); };\n    }\n    SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm, cancelAlgorithm);\n}\nfunction TransformStreamDefaultControllerClearAlgorithms(controller) {\n    controller._transformAlgorithm = undefined;\n    controller._flushAlgorithm = undefined;\n    controller._cancelAlgorithm = undefined;\n}\nfunction TransformStreamDefaultControllerEnqueue(controller, chunk) {\n    var stream = controller._controlledTransformStream;\n    var readableController = stream._readable._readableStreamController;\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(readableController)) {\n        throw new TypeError('Readable side is not in a state that permits enqueue');\n    }\n    // We throttle transform invocations based on the backpressure of the ReadableStream, but we still\n    // accept TransformStreamDefaultControllerEnqueue() calls.\n    try {\n        ReadableStreamDefaultControllerEnqueue(readableController, chunk);\n    }\n    catch (e) {\n        // This happens when readableStrategy.size() throws.\n        TransformStreamErrorWritableAndUnblockWrite(stream, e);\n        throw stream._readable._storedError;\n    }\n    var backpressure = ReadableStreamDefaultControllerHasBackpressure(readableController);\n    if (backpressure !== stream._backpressure) {\n        TransformStreamSetBackpressure(stream, true);\n    }\n}\nfunction TransformStreamDefaultControllerError(controller, e) {\n    TransformStreamError(controller._controlledTransformStream, e);\n}\nfunction TransformStreamDefaultControllerPerformTransform(controller, chunk) {\n    var transformPromise = controller._transformAlgorithm(chunk);\n    return transformPromiseWith(transformPromise, undefined, function (r) {\n        TransformStreamError(controller._controlledTransformStream, r);\n        throw r;\n    });\n}\nfunction TransformStreamDefaultControllerTerminate(controller) {\n    var stream = controller._controlledTransformStream;\n    var readableController = stream._readable._readableStreamController;\n    ReadableStreamDefaultControllerClose(readableController);\n    var error = new TypeError('TransformStream terminated');\n    TransformStreamErrorWritableAndUnblockWrite(stream, error);\n}\n// TransformStreamDefaultSink Algorithms\nfunction TransformStreamDefaultSinkWriteAlgorithm(stream, chunk) {\n    var controller = stream._transformStreamController;\n    if (stream._backpressure) {\n        var backpressureChangePromise = stream._backpressureChangePromise;\n        return transformPromiseWith(backpressureChangePromise, function () {\n            var writable = stream._writable;\n            var state = writable._state;\n            if (state === 'erroring') {\n                throw writable._storedError;\n            }\n            return TransformStreamDefaultControllerPerformTransform(controller, chunk);\n        });\n    }\n    return TransformStreamDefaultControllerPerformTransform(controller, chunk);\n}\nfunction TransformStreamDefaultSinkAbortAlgorithm(stream, reason) {\n    var controller = stream._transformStreamController;\n    if (controller._finishPromise !== undefined) {\n        return controller._finishPromise;\n    }\n    // stream._readable cannot change after construction, so caching it across a call to user code is safe.\n    var readable = stream._readable;\n    // Assign the _finishPromise now so that if _cancelAlgorithm calls readable.cancel() internally,\n    // we don't run the _cancelAlgorithm again.\n    controller._finishPromise = newPromise(function (resolve, reject) {\n        controller._finishPromise_resolve = resolve;\n        controller._finishPromise_reject = reject;\n    });\n    var cancelPromise = controller._cancelAlgorithm(reason);\n    TransformStreamDefaultControllerClearAlgorithms(controller);\n    uponPromise(cancelPromise, function () {\n        if (readable._state === 'errored') {\n            defaultControllerFinishPromiseReject(controller, readable._storedError);\n        }\n        else {\n            ReadableStreamDefaultControllerError(readable._readableStreamController, reason);\n            defaultControllerFinishPromiseResolve(controller);\n        }\n        return null;\n    }, function (r) {\n        ReadableStreamDefaultControllerError(readable._readableStreamController, r);\n        defaultControllerFinishPromiseReject(controller, r);\n        return null;\n    });\n    return controller._finishPromise;\n}\nfunction TransformStreamDefaultSinkCloseAlgorithm(stream) {\n    var controller = stream._transformStreamController;\n    if (controller._finishPromise !== undefined) {\n        return controller._finishPromise;\n    }\n    // stream._readable cannot change after construction, so caching it across a call to user code is safe.\n    var readable = stream._readable;\n    // Assign the _finishPromise now so that if _flushAlgorithm calls readable.cancel() internally,\n    // we don't also run the _cancelAlgorithm.\n    controller._finishPromise = newPromise(function (resolve, reject) {\n        controller._finishPromise_resolve = resolve;\n        controller._finishPromise_reject = reject;\n    });\n    var flushPromise = controller._flushAlgorithm();\n    TransformStreamDefaultControllerClearAlgorithms(controller);\n    uponPromise(flushPromise, function () {\n        if (readable._state === 'errored') {\n            defaultControllerFinishPromiseReject(controller, readable._storedError);\n        }\n        else {\n            ReadableStreamDefaultControllerClose(readable._readableStreamController);\n            defaultControllerFinishPromiseResolve(controller);\n        }\n        return null;\n    }, function (r) {\n        ReadableStreamDefaultControllerError(readable._readableStreamController, r);\n        defaultControllerFinishPromiseReject(controller, r);\n        return null;\n    });\n    return controller._finishPromise;\n}\n// TransformStreamDefaultSource Algorithms\nfunction TransformStreamDefaultSourcePullAlgorithm(stream) {\n    // Invariant. Enforced by the promises returned by start() and pull().\n    TransformStreamSetBackpressure(stream, false);\n    // Prevent the next pull() call until there is backpressure.\n    return stream._backpressureChangePromise;\n}\nfunction TransformStreamDefaultSourceCancelAlgorithm(stream, reason) {\n    var controller = stream._transformStreamController;\n    if (controller._finishPromise !== undefined) {\n        return controller._finishPromise;\n    }\n    // stream._writable cannot change after construction, so caching it across a call to user code is safe.\n    var writable = stream._writable;\n    // Assign the _finishPromise now so that if _flushAlgorithm calls writable.abort() or\n    // writable.cancel() internally, we don't run the _cancelAlgorithm again, or also run the\n    // _flushAlgorithm.\n    controller._finishPromise = newPromise(function (resolve, reject) {\n        controller._finishPromise_resolve = resolve;\n        controller._finishPromise_reject = reject;\n    });\n    var cancelPromise = controller._cancelAlgorithm(reason);\n    TransformStreamDefaultControllerClearAlgorithms(controller);\n    uponPromise(cancelPromise, function () {\n        if (writable._state === 'errored') {\n            defaultControllerFinishPromiseReject(controller, writable._storedError);\n        }\n        else {\n            WritableStreamDefaultControllerErrorIfNeeded(writable._writableStreamController, reason);\n            TransformStreamUnblockWrite(stream);\n            defaultControllerFinishPromiseResolve(controller);\n        }\n        return null;\n    }, function (r) {\n        WritableStreamDefaultControllerErrorIfNeeded(writable._writableStreamController, r);\n        TransformStreamUnblockWrite(stream);\n        defaultControllerFinishPromiseReject(controller, r);\n        return null;\n    });\n    return controller._finishPromise;\n}\n// Helper functions for the TransformStreamDefaultController.\nfunction defaultControllerBrandCheckException(name) {\n    return new TypeError(\"TransformStreamDefaultController.prototype.\".concat(name, \" can only be used on a TransformStreamDefaultController\"));\n}\nfunction defaultControllerFinishPromiseResolve(controller) {\n    if (controller._finishPromise_resolve === undefined) {\n        return;\n    }\n    controller._finishPromise_resolve();\n    controller._finishPromise_resolve = undefined;\n    controller._finishPromise_reject = undefined;\n}\nfunction defaultControllerFinishPromiseReject(controller, reason) {\n    if (controller._finishPromise_reject === undefined) {\n        return;\n    }\n    setPromiseIsHandledToTrue(controller._finishPromise);\n    controller._finishPromise_reject(reason);\n    controller._finishPromise_resolve = undefined;\n    controller._finishPromise_reject = undefined;\n}\n// Helper functions for the TransformStream.\nfunction streamBrandCheckException(name) {\n    return new TypeError(\"TransformStream.prototype.\".concat(name, \" can only be used on a TransformStream\"));\n}\n\nexport { ByteLengthQueuingStrategy, CountQueuingStrategy, ReadableByteStreamController, ReadableStream, ReadableStreamBYOBReader, ReadableStreamBYOBRequest, ReadableStreamDefaultController, ReadableStreamDefaultReader, TransformStream, TransformStreamDefaultController, WritableStream, WritableStreamDefaultController, WritableStreamDefaultWriter };\n//# sourceMappingURL=ponyfill.mjs.map\n"], "names": ["extendStatics", "d", "b", "Object", "Array", "p", "__extends", "TypeError", "String", "__", "assert", "test", "noop", "typeIsObject", "x", "isStreamConstructor", "ctor", "startCalled", "e", "isReadableStream", "readable", "isWritableStream", "writable", "isTransformStream", "transform", "supportsByobReader", "reader", "_a", "createReadableStreamWrapper", "byteSourceSupported", "supportsByteSource", "type", "_b", "parseReadableType", "undefined", "source", "createWrappingReadableSource", "WrappingReadableByteStreamSource", "WrappingReadableStreamDefaultSource", "typeString", "RangeError", "AbstractWrappingReadableStreamSource", "underlyingStream", "controller", "reason", "_this", "closed", "read", "result", "readPromise", "pendingRead", "finishRead", "afterRead", "_super", "arguments", "toUint8Array", "view", "Uint8Array", "supportsByob", "byobRequest", "buffer", "copyArrayBufferView", "from", "to", "fromArray", "toArray", "createWritableStreamWrapper", "createWrappingWritableSink", "WrappingWritableStreamSink", "underlyingWriter", "Promise", "resolve", "reject", "chunk", "writer", "writeRequest", "write", "writePromise", "pendingWrite", "finishWrite", "afterWrite", "createTransformStreamWrapper", "createWrappingTransformer", "WrappingTransformStreamTransformer", "error", "readerClosed", "AsyncIteratorPrototype", "SymbolPolyfill", "Symbol", "description", "__values", "o", "s", "m", "i", "__await", "v", "SuppressedError", "setFunctionName", "fn", "name", "originalPromise", "originalPromiseThen", "originalPromiseReject", "newPromise", "executor", "promiseResolvedWith", "value", "promiseRejectedWith", "PerformPromiseThen", "promise", "onFulfilled", "onRejected", "uponPromise", "uponFulfillment", "uponRejection", "transformPromiseWith", "fulfillmentH<PERSON>ler", "<PERSON><PERSON><PERSON><PERSON>", "setPromiseIsHandledToTrue", "_queueMicrotask", "callback", "queueMicrotask", "resolvedPromise_1", "cb", "reflectCall", "F", "V", "args", "Function", "promiseCall", "SimpleQueue", "element", "oldBack", "newBack", "QUEUE_MAX_ARRAY_SIZE", "oldFront", "newFront", "old<PERSON>ursor", "newCursor", "elements", "node", "front", "cursor", "AbortSteps", "ErrorSteps", "CancelSteps", "PullSteps", "ReleaseSteps", "ReadableStreamReaderGenericInitialize", "stream", "defaultReaderClosedPromiseInitialize", "defaultReaderClosedPromiseInitializeAsResolved", "defaultReaderClosedPromiseResolve", "defaultReaderClosedPromiseInitializeAsRejected", "defaultReaderClosedPromiseReject", "ReadableStreamReaderGenericCancel", "ReadableStreamCancel", "ReadableStreamReaderGenericRelease", "defaultReaderClosedPromiseResetToRejected", "readerLockException", "NumberIsFinite", "Number", "isFinite", "MathTrunc", "Math", "assertDictionary", "obj", "context", "assertFunction", "assertObject", "assertRequiredArgument", "position", "assertRequiredField", "field", "convertUnrestrictedDouble", "censorNegativeZero", "convertUnsignedLongLongWithEnforceRange", "upperBound", "assertReadableStream", "IsReadableStream", "AcquireReadableStreamDefaultReader", "ReadableStreamDefaultReader", "ReadableStreamAddReadRequest", "readRequest", "ReadableStreamFulfillReadRequest", "done", "ReadableStreamGetNumReadRequests", "ReadableStreamHasDefaultReader", "IsReadableStreamDefaultReader", "IsReadableStreamLocked", "defaultReaderBrandCheckException", "resolvePromise", "rejectPromise", "ReadableStreamDefaultReaderRead", "ReadableStreamDefaultReaderRelease", "ReadableStreamDefaultReaderErrorReadRequests", "readRequests", "ReadableStreamAsyncIteratorImpl", "preventCancel", "nextSteps", "returnSteps", "ReadableStreamAsyncIteratorPrototype", "IsReadableStreamAsyncIterator", "streamAsyncIteratorBrandCheckException", "NumberIsNaN", "CreateArrayFromList", "CopyDataBlockBytes", "dest", "destOffset", "src", "srcOffset", "n", "TransferArrayBuffer", "O", "structuredClone", "IsDetachedBuffer", "ArrayBufferSlice", "begin", "end", "length", "slice", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GetMethod", "receiver", "prop", "func", "CloneAsUint8Array", "DequeueValue", "container", "pair", "EnqueueValueWithSize", "size", "Infinity", "ResetQueue", "isDataViewConstructor", "DataView", "ReadableStreamBYOBRequest", "IsReadableStreamBYOBRequest", "byobRequestBrandCheckException", "bytes<PERSON>ritten", "ReadableByteStreamControllerRespond", "ReadableByteStreamControllerRespondWithNewView", "ReadableByteStreamController", "IsReadableByteStreamController", "byteStreamControllerBrandCheckException", "ReadableByteStreamControllerGetBYOBRequest", "ReadableByteStreamControllerGetDesiredSize", "state", "ReadableByteStreamControllerClose", "ReadableByteStreamControllerEnqueue", "ReadableByteStreamControllerError", "ReadableByteStreamControllerClearPendingPullIntos", "ReadableByteStreamControllerClearAlgorithms", "ReadableByteStreamControllerFillReadRequestFromQueue", "autoAllocateChunkSize", "bufferE", "pullIntoDescriptor", "ReadableByteStreamControllerCallPullIfNeeded", "firstPullInto", "ReadableByteStreamControllerShouldCallPull", "ReadableStreamHasBYOBReader", "ReadableStreamGetNumReadIntoRequests", "desiredSize", "ReadableByteStreamControllerInvalidateBYOBRequest", "ReadableByteStreamControllerCommitPullIntoDescriptor", "<PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerConvertPullIntoDescriptor", "ReadableStreamFulfillReadIntoRequest", "readIntoRequest", "bytesFilled", "elementSize", "ReadableByteStreamControllerEnqueueChunkToQueue", "byteOffset", "byteLength", "ReadableByteStreamControllerEnqueueClonedChunkToQueue", "clonedChunk", "cloneE", "ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue", "firstDescriptor", "ReadableByteStreamControllerShiftPendingPullInto", "ReadableByteStreamControllerFillPullIntoDescriptorFromQueue", "maxBytesToCopy", "maxBytesFilled", "totalBytesToCopyRemaining", "ready", "remainderBytes", "maxAlignedBytes", "queue", "headOfQueue", "bytesToCopy", "destStart", "ReadableByteStreamControllerFillHeadPullIntoDescriptor", "ReadableByteStreamControllerHandleQueueDrain", "ReadableStreamClose", "ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue", "ReadableByteStreamControllerRespondInternal", "ReadableByteStreamControllerRespondInClosedState", "ReadableByteStreamControllerRespondInReadableState", "remainderSize", "firstPendingPullInto", "<PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerProcessReadRequestsUsingQueue", "ReadableStreamError", "entry", "SetUpReadableStreamBYOBRequest", "request", "viewByteLength", "SetUpReadableByteStreamController", "startAlgorithm", "pullAlgorithm", "cancelAlgorithm", "highWaterMark", "r", "AcquireReadableStreamBYOBReader", "ReadableStreamBYOBReader", "ReadableStreamAddReadIntoRequest", "IsReadableStreamBYOBReader", "byobReaderBrandCheckException", "rawOptions", "options", "min", "ReadableStreamBYOBReaderRead", "ReadableStreamBYOBReaderRelease", "ReadableStreamBYOBReaderErrorReadIntoRequests", "ReadableByteStreamControllerPullInto", "emptyView", "readIntoRequests", "ExtractHighWaterMark", "strategy", "defaultHWM", "ExtractSizeAlgorithm", "convertQueuingStrategy", "init", "convertQueuingStrategySize", "assertWritableStream", "IsWritableStream", "supportsAbortController", "AbortController", "WritableStream", "rawUnderlyingSink", "rawStrategy", "original", "abort", "close", "start", "underlyingSink", "convertUnderlyingSinkAbortCallback", "convertUnderlyingSinkCloseCallback", "convertUnderlyingSinkStartCallback", "convertUnderlyingSinkWriteCallback", "InitializeWritableStream", "sizeAlgorithm", "SetUpWritableStreamDefaultControllerFromUnderlyingSink", "writeAlgorithm", "closeAlgorithm", "abortAlgorithm", "WritableStreamDefaultController", "SetUpWritableStreamDefaultController", "streamBrandCheckException$2", "IsWritableStreamLocked", "WritableStreamAbort", "WritableStreamCloseQueuedOrInFlight", "WritableStreamClose", "AcquireWritableStreamDefaultWriter", "WritableStreamDefaultWriter", "wasAlreadyErroring", "WritableStreamStartErroring", "defaultWriterReadyPromiseResolve", "WritableStreamDefaultControllerClose", "closeSentinel", "WritableStreamDefaultControllerAdvanceQueueIfNeeded", "WritableStreamDealWithRejection", "WritableStreamFinishErroring", "WritableStreamDefaultWriterEnsureReadyPromiseRejected", "WritableStreamHasOperationMarkedInFlight", "storedError", "WritableStreamRejectCloseAndClosedPromiseIfNeeded", "abortRequest", "defaultWriterClosedPromiseReject", "WritableStreamUpdateBackpressure", "backpressure", "defaultWriterReadyPromiseReset", "defaultWriterReadyPromiseInitialize", "defaultWriterReadyPromiseInitializeAsResolved", "defaultWriterClosedPromiseInitialize", "defaultWriterReadyPromiseInitializeAsRejected", "defaultWriterClosedPromiseInitializeAsResolved", "defaultWriterClosedPromiseResolve", "defaultWriterClosedPromiseInitializeAsRejected", "IsWritableStreamDefaultWriter", "defaultWriterBrandCheckException", "defaultWriterLockException", "WritableStreamDefaultWriterGetDesiredSize", "WritableStreamDefaultControllerGetDesiredSize", "WritableStreamDefaultWriterAbort", "WritableStreamDefaultWriterClose", "WritableStreamDefaultWriterRelease", "WritableStreamDefaultWriterWrite", "defaultWriterReadyPromiseReject", "defaultWriterReadyPromiseResetToRejected", "releasedError", "defaultWriterClosedPromiseResetToRejected", "chunkSize", "WritableStreamDefaultControllerGetChunkSize", "chunkSizeE", "WritableStreamDefaultControllerErrorIfNeeded", "WritableStreamDefaultControllerWrite", "enqueueE", "WritableStreamDefaultControllerGetBackpressure", "IsWritableStreamDefaultController", "defaultControllerBrandCheckException$2", "WritableStreamDefaultControllerError", "WritableStreamDefaultControllerClearAlgorithms", "createAbortController", "WritableStreamDefaultControllerProcessClose", "sinkClosePromise", "WritableStreamDefaultControllerProcessWrite", "globals", "getGlobals", "globalThis", "self", "global", "DOMException", "getFromGlobal", "isDOMExceptionConstructor", "message", "Error", "ReadableStreamPipeTo", "preventClose", "preventAbort", "signal", "shuttingDown", "currentWrite", "actions", "shutdownWithAction", "action", "isOrBecomesErrored", "shutdown", "isOrBecomesClosed", "WritableStreamDefaultWriterCloseWithErrorPropagation", "destClosed_1", "waitForWritesToFinish", "oldCurrentWrite", "resolveLoop", "rejectLoop", "next", "pipeStep", "resolveRead", "rejectRead", "originalIsError", "originalError", "doTheRest", "finalize", "newError", "isError", "ReadableStreamDefaultController", "IsReadableStreamDefaultController", "defaultControllerBrandCheckException$1", "ReadableStreamDefaultControllerGetDesiredSize", "ReadableStreamDefaultControllerCanCloseOrEnqueue", "ReadableStreamDefaultControllerClose", "ReadableStreamDefaultControllerEnqueue", "ReadableStreamDefaultControllerError", "ReadableStreamDefaultControllerClearAlgorithms", "ReadableStreamDefaultControllerCallPullIfNeeded", "ReadableStreamDefaultControllerShouldCallPull", "SetUpReadableStreamDefaultController", "convertPipeOptions", "assertAbortSignal", "isAbortSignal", "Boolean", "ReadableStream", "rawUnderlyingSource", "underlyingSource", "cancel", "pull", "convertUnderlyingSourceCancelCallback", "convertUnderlyingSourcePullCallback", "convertUnderlyingSourceStartCallback", "convertReadableStreamType", "InitializeReadableStream", "SetUpReadableByteStreamControllerFromUnderlyingSource", "underlyingByteSource", "streamBrandCheckException$1", "mode", "convertReadableStreamReaderMode", "rawTransform", "destination", "cloneForBranch2", "branches", "ReadableByteStreamTee", "reason1", "reason2", "branch1", "branch2", "resolveCancelPromise", "reading", "readAgainForBranch1", "readAgainForBranch2", "canceled1", "canceled2", "cancelPromise", "forwardReaderError", "thisReader", "pullWithDefaultReader", "chunk2", "pull1Algorithm", "pull2Algorithm", "pullWithBYOBReader", "forBranch2", "byobBranch", "otherBranch", "byobCanceled", "otherCanceled", "CreateReadableByteStream", "cancelResult", "ReadableStreamDefaultTee", "readAgain", "CreateReadableStream", "impl", "iterator", "asyncIterable", "ReadableStreamFromDefaultReader", "readResult", "ReadableStreamFromIterable", "iteratorRecord", "GetIterator", "hint", "method", "syncIteratorRecord", "syncIterable", "asyncIterator", "next<PERSON><PERSON><PERSON>", "syncMethod", "__asyncGenerator", "thisArg", "_arguments", "generator", "g", "q", "verb", "a", "resume", "step", "fulfill", "settle", "f", "__generator", "body", "y", "t", "_", "op", "__asyncDelegator", "__asyncValues", "nextResult", "IteratorNext", "iterResult", "return<PERSON><PERSON><PERSON>", "returnResult", "convertQueuingStrategyInit", "byteLengthSizeFunction", "ByteLengthQueuingStrategy", "IsByteLengthQueuingStrategy", "byteLengthBrandCheckException", "countSizeFunction", "CountQueuingStrategy", "IsCountQueuingStrategy", "countBrandCheckException", "TransformStream", "rawTransformer", "rawWritableStrategy", "rawReadableStrategy", "flush", "readableType", "writableType", "startPromise_resolve", "writableStrategy", "readableStrategy", "transformer", "convertTransformerCancelCallback", "convertTransformerFlushCallback", "convertTransformerStartCallback", "convertTransformerTransformCallback", "readableHighWaterMark", "readableSizeAlgorithm", "writableHighWaterMark", "writableSizeAlgorithm", "InitializeTransformStream", "startPromise", "TransformStreamDefaultSinkWriteAlgorithm", "TransformStreamDefaultControllerPerformTransform", "TransformStreamDefaultSinkCloseAlgorithm", "flushPromise", "TransformStreamDefaultControllerClearAlgorithms", "defaultControllerFinishPromiseReject", "defaultControllerFinishPromiseResolve", "TransformStreamDefaultSinkAbortAlgorithm", "TransformStreamDefaultSourcePullAlgorithm", "TransformStreamSetBackpressure", "TransformStreamDefaultSourceCancelAlgorithm", "TransformStreamUnblockWrite", "SetUpTransformStreamDefaultControllerFromTransformer", "transformAlgorithm", "flushAlgorithm", "TransformStreamDefaultController", "TransformStreamDefaultControllerEnqueue", "transformResultE", "IsTransformStream", "streamBrandCheckException", "TransformStreamError", "TransformStreamErrorWritableAndUnblockWrite", "IsTransformStreamDefaultController", "defaultControllerBrandCheckException", "TransformStreamDefaultControllerError", "TransformStreamDefaultControllerTerminate", "readableController"], "mappings": ";mbAgBA,IAAIA,EAAgB,SAASC,CAAC,CAAEC,CAAC,EAI7B,MAAOF,AAHPA,CAAAA,EAAgBG,OAAO,cAAc,EAChC,EAAE,UAAW,EAAE,AAAC,aAAaC,OAAS,SAAUH,CAAC,CAAEC,CAAC,EAAID,EAAE,SAAS,CAAGC,CAAG,GAC1E,SAAUD,CAAC,CAAEC,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACD,EAAGG,IAAIJ,CAAAA,CAAC,CAACI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,GAC/EJ,EAAGC,EAC5B,EAEA,SAASI,EAAUL,CAAC,CAAEC,CAAC,EACnB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIK,UAAU,uBAAyBC,OAAON,GAAK,iCAE7D,SAASO,IAAO,IAAI,CAAC,WAAW,CAAGR,CAAG,CADtCD,EAAcC,EAAGC,GAEjBD,EAAE,SAAS,CAAGC,AAAM,OAANA,EAAaC,OAAO,MAAM,CAACD,GAAMO,CAAAA,EAAG,SAAS,CAAGP,EAAE,SAAS,CAAE,IAAIO,CAAG,CACtF,CAEA,SAASC,EAAOC,CAAI,EAChB,GAAI,CAACA,EACD,MAAM,AAAIJ,UAAU,mBAE5B,CAEA,SAASK,IAET,CACA,SAASC,EAAaC,CAAC,EACnB,MAAO,AAAc,UAAb,OAAOA,GAAkBA,AAAM,OAANA,GAAe,AAAa,YAAb,OAAOA,CAC3D,CAEA,SAASC,EAAoBC,CAAI,EAC7B,GAAI,AAAgB,YAAhB,OAAOA,EACP,MAAO,GAEX,IAAIC,EAAc,GAClB,GAAI,CACA,IAAID,EAAK,CACL,MAAO,WACHC,EAAc,EAClB,CACJ,EACJ,CACA,MAAOC,EAAG,CAEV,CACA,OAAOD,CACX,CACA,SAASE,EAAiBC,CAAQ,QAC9B,EAAKP,EAAaO,IAGd,AAA8B,YAA9B,OAAOA,EAAS,SAAS,EAFlB,EAMf,CAUA,SAASC,EAAiBC,CAAQ,QAC9B,EAAKT,EAAaS,IAGd,AAA8B,YAA9B,OAAOA,EAAS,SAAS,EAFlB,EAMf,CAUA,SAASC,EAAkBC,CAAS,QAChC,GAAKX,EAAaW,IAGbL,EAAiBK,EAAU,QAAQ,GAGnCH,EAAiBG,EAAU,QAAQ,IAL7B,EASf,CAUA,SAASC,EAAmBL,CAAQ,EAChC,GAAI,CAGA,OADAM,AADaN,EAAS,SAAS,CAAC,CAAE,KAAM,MAAO,GACxC,WAAW,GACX,EACX,CACA,MAAOO,EAAI,CACP,MAAO,EACX,CACJ,CAWA,SAASC,EAA4BZ,CAAI,EACrCN,EApEA,GAAKK,EAD4BC,EAqEEA,IAjE9BG,EAAiB,IAAIH,KAFf,IAoEX,IAtEiCA,EAsE7Ba,EAAsBC,AAZ9B,SAA4Bd,CAAI,EAC5B,GAAI,CAEA,OADA,IAAIA,EAAK,CAAE,KAAM,OAAQ,GAClB,EACX,CACA,MAAOW,EAAI,CACP,MAAO,EACX,CACJ,EAIiDX,GAC7C,OAAO,SAAUI,CAAQ,CAAEO,CAAE,EACzB,IAAkCI,EAAOC,AAAhCL,CAAAA,AAAO,KAAK,IAAZA,EAAgB,CAAC,EAAIA,CAAC,EAAa,IAAI,CAKhD,GAHa,UADbI,CAAAA,EAAOE,EAAkBF,EAAI,GACL,CAACF,GACrBE,CAAAA,EAAOG,KAAAA,CAAQ,EAEfd,EAAS,WAAW,GAAKJ,GACrBe,CAAAA,AAAS,UAATA,GAAoBN,EAAmBL,EAAQ,EAC/C,OAAOA,EAGf,GAAIW,AAAS,UAATA,EAAkB,CAClB,IAAII,EAASC,EAA6BhB,EAAU,CAAE,KAAMW,CAAK,GACjE,OAAO,IAAIf,EAAKmB,EACpB,CAEI,IAAIA,EAASC,EAA6BhB,GAC1C,OAAO,IAAIJ,EAAKmB,EAExB,CACJ,CACA,SAASC,EAA6BhB,CAAQ,CAAEO,CAAE,EAC9C,IAIIQ,EAJ8BJ,EAAOC,AAAhCL,CAAAA,AAAO,KAAK,IAAZA,EAAgB,CAAC,EAAIA,CAAC,EAAa,IAAI,CAWhD,OAVAjB,EAAOS,EAAiBC,IACxBV,EAAOU,AAAoB,KAApBA,EAAS,MAAM,EAIlBe,EADAJ,AAAS,UAFbA,CAAAA,EAAOE,EAAkBF,EAAI,EAGhB,IAAIM,EAAiCjB,GAGrC,IAAIkB,EAAoClB,EAGzD,CACA,SAASa,EAAkBF,CAAI,EAC3B,IAAIQ,EAAa/B,OAAOuB,GACxB,GAAIQ,AAAe,UAAfA,EACA,OAAOA,EAEN,GAAIR,AAASG,KAAAA,IAATH,EACL,OAAOA,CAGP,OAAM,AAAIS,WAAW,4BAE7B,CACA,IAAIC,EAAsD,WACtD,SAASA,EAAqCC,CAAgB,EAC1D,IAAI,CAAC,iBAAiB,CAAGR,KAAAA,EACzB,IAAI,CAAC,WAAW,CAAGA,KAAAA,EACnB,IAAI,CAAC,yBAAyB,CAAGA,KAAAA,EACjC,IAAI,CAAC,YAAY,CAAGA,KAAAA,EACpB,IAAI,CAAC,iBAAiB,CAAGQ,EAEzB,IAAI,CAAC,oBAAoB,EAC7B,CAyFA,OAxFAD,EAAqC,SAAS,CAAC,KAAK,CAAG,SAAUE,CAAU,EACvE,IAAI,CAAC,yBAAyB,CAAGA,CACrC,EACAF,EAAqC,SAAS,CAAC,MAAM,CAAG,SAAUG,CAAM,EAEpE,OADAlC,EAAO,AAA2BwB,KAAAA,IAA3B,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAACU,EACzC,EACAH,EAAqC,SAAS,CAAC,oBAAoB,CAAG,WAClE,GAAI,AAAqB,YAArB,IAAI,CAAC,WAAW,EAGpB,IAAI,CAAC,aAAa,GAClB,IAAIf,EAAS,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAC7C,KAAI,CAAC,WAAW,CAAG,UACnB,IAAI,CAAC,aAAa,CAACA,GACvB,EACAe,EAAqC,SAAS,CAAC,aAAa,CAAG,SAAUf,CAAM,EAC3E,IAAImB,EAAQ,IAAI,CAChBnC,EAAO,AAA2BwB,KAAAA,IAA3B,IAAI,CAAC,iBAAiB,EAC7B,IAAI,CAAC,iBAAiB,CAAGR,EACzB,IAAIoB,EAAS,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAC1C,IAAI,CAACA,EAGLA,EACK,IAAI,CAAC,WAAc,OAAOD,EAAM,kBAAkB,EAAI,GACtD,IAAI,CAAC,WACFnB,IAAWmB,EAAM,iBAAiB,EAClCA,EAAM,yBAAyB,CAAC,KAAK,EAE7C,EAAG,SAAUD,CAAM,EACXlB,IAAWmB,EAAM,iBAAiB,EAClCA,EAAM,yBAAyB,CAAC,KAAK,CAACD,EAE9C,GACK,KAAK,CAAChC,EACf,EACA6B,EAAqC,SAAS,CAAC,aAAa,CAAG,WAC3D,GAAI,AAA2BP,KAAAA,IAA3B,IAAI,CAAC,iBAAiB,CAG1B,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAClC,IAAI,CAAC,iBAAiB,CAAGA,KAAAA,EACzB,IAAI,CAAC,WAAW,CAAGA,KAAAA,CACvB,EACAO,EAAqC,SAAS,CAAC,sBAAsB,CAAG,WACpE,IAAII,EAAQ,IAAI,CAChB,IAAI,CAAC,oBAAoB,GAEzB,IAAIE,EAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,GACjC,IAAI,CAAC,SAAUC,CAAM,EACtB,IAAIL,EAAaE,EAAM,yBAAyB,AAC5CG,CAAAA,EAAO,IAAI,CACXH,EAAM,SAAS,GAGfF,EAAW,OAAO,CAACK,EAAO,KAAK,CAEvC,GAEA,OADA,IAAI,CAAC,eAAe,CAACD,GACdA,CACX,EACAN,EAAqC,SAAS,CAAC,SAAS,CAAG,WACvD,GAAI,CACA,IAAI,CAAC,yBAAyB,CAAC,KAAK,EACxC,CACA,MAAOd,EAAI,CAEX,CACJ,EACAc,EAAqC,SAAS,CAAC,eAAe,CAAG,SAAUQ,CAAW,EAClF,IACIC,EADAL,EAAQ,IAAI,CAEZM,EAAa,WACTN,EAAM,YAAY,GAAKK,GACvBL,CAAAA,EAAM,YAAY,CAAGX,KAAAA,CAAQ,CAErC,CACA,KAAI,CAAC,YAAY,CAAGgB,EAAcD,EAAY,IAAI,CAACE,EAAYA,EACnE,EACAV,EAAqC,SAAS,CAAC,kBAAkB,CAAG,WAChE,IAAII,EAAQ,IAAI,CAChB,IAAI,CAAC,IAAI,CAAC,YAAY,EAGtB,IAAIO,EAAY,WAAc,OAAOP,EAAM,kBAAkB,EAAI,EACjE,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAACO,EAAWA,GAC7C,EACOX,CACX,IACIH,EAAqD,SAAUe,CAAM,EAErE,SAASf,IACL,OAAOe,AAAW,OAAXA,GAAmBA,EAAO,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAIA,OAPAhD,EAAUgC,EAAqCe,GAI/Cf,EAAoC,SAAS,CAAC,IAAI,CAAG,WACjD,OAAO,IAAI,CAAC,sBAAsB,EACtC,EACOA,CACX,EAAEG,GACF,SAASc,EAAaC,CAAI,EACtB,OAAO,IAAIC,WAAWD,EAAK,MAAM,CAAEA,EAAK,UAAU,CAAEA,EAAK,UAAU,CACvE,CAMA,IAAInB,EAAkD,SAAUgB,CAAM,EAElE,SAAShB,EAAiCK,CAAgB,EACtD,IAAIG,EAAQ,IAAI,CACZa,EAAejC,EAAmBiB,GAGtC,MADAG,AADAA,CAAAA,EAAQQ,EAAO,IAAI,CAAC,IAAI,CAAEX,IAAqB,IAAI,AAAD,EAC5C,aAAa,CAAGgB,EACfb,CACX,CAiDA,OAxDAvC,EAAU+B,EAAkCgB,GAQ5ClD,OAAO,cAAc,CAACkC,EAAiC,SAAS,CAAE,OAAQ,CACtE,IAAK,WACD,MAAO,OACX,EACA,WAAY,GACZ,aAAc,EAClB,GACAA,EAAiC,SAAS,CAAC,iBAAiB,CAAG,WAC3D,GAAI,AAAqB,SAArB,IAAI,CAAC,WAAW,EAGpB3B,EAAO,IAAI,CAAC,aAAa,EACzB,IAAI,CAAC,aAAa,GAClB,IAAIgB,EAAS,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAE,KAAM,MAAO,EAC7D,KAAI,CAAC,WAAW,CAAG,OACnB,IAAI,CAAC,aAAa,CAACA,GACvB,EACAW,EAAiC,SAAS,CAAC,IAAI,CAAG,WAC9C,GAAI,IAAI,CAAC,aAAa,CAAE,CACpB,IAAIsB,EAAc,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAC5D,GAAIA,EACA,OAAO,IAAI,CAAC,oBAAoB,CAACA,EAEzC,CACA,OAAO,IAAI,CAAC,sBAAsB,EACtC,EACAtB,EAAiC,SAAS,CAAC,oBAAoB,CAAG,SAAUsB,CAAW,EACnF,IAAId,EAAQ,IAAI,CAChB,IAAI,CAAC,iBAAiB,GAGtB,IAAIe,EAAS,IAAIH,WAAWE,EAAY,IAAI,CAAC,UAAU,EAEnDZ,EAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAACa,GAClC,IAAI,CAAC,SAAUZ,CAAM,EACtBH,EAAM,yBAAyB,CAC3BG,EAAO,IAAI,EACXH,EAAM,SAAS,GACfc,EAAY,OAAO,CAAC,MAGpBE,AAvDhB,SAA6BC,CAAI,CAAEC,CAAE,EACjC,IAAIC,EAAYT,EAAaO,GAE7BG,AADcV,EAAaQ,GACnB,GAAG,CAACC,EAAW,EAC3B,EAmDoChB,EAAO,KAAK,CAAEW,EAAY,IAAI,EAClDA,EAAY,OAAO,CAACX,EAAO,KAAK,CAAC,UAAU,EAEnD,GAEA,OADA,IAAI,CAAC,eAAe,CAACD,GACdA,CACX,EACOV,CACX,EAAEI,GAEF,SAASyB,EAA4BlD,CAAI,MAtRJA,EAwRjC,OADAN,EAtRA,GAAKK,EAD4BC,EAuREA,IAnR9BK,EAAiB,IAAIL,KAFf,IAsRJ,SAAUM,CAAQ,SACrB,AAAIA,EAAS,WAAW,GAAKN,EAClBM,EAGJ,IAAIN,EADAmD,EAA2B7C,GAE1C,CACJ,CACA,SAAS6C,EAA2B7C,CAAQ,EAIxC,OAHAZ,EAAOW,EAAiBC,IACxBZ,EAAOY,AAAoB,KAApBA,EAAS,MAAM,EAEf,IAAI8C,EADE9C,EAAS,SAAS,GAEnC,CACA,IAAI8C,EAA4C,WAC5C,SAASA,EAA2BC,CAAgB,EAChD,IAAIxB,EAAQ,IAAI,AAChB,KAAI,CAAC,yBAAyB,CAAGX,KAAAA,EACjC,IAAI,CAAC,aAAa,CAAGA,KAAAA,EACrB,IAAI,CAAC,MAAM,CAAG,WACd,IAAI,CAAC,YAAY,CAAGA,KAAAA,EACpB,IAAI,CAAC,iBAAiB,CAAGmC,EACzB,IAAI,CAAC,aAAa,CAAG,IAAIC,QAAQ,SAAUC,CAAO,CAAEC,CAAM,EACtD3B,EAAM,mBAAmB,CAAG2B,CAChC,GACA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC5D,EAC7B,CAkFA,OAjFAwD,EAA2B,SAAS,CAAC,KAAK,CAAG,SAAUzB,CAAU,EAC7D,IAAIE,EAAQ,IAAI,AAChB,KAAI,CAAC,yBAAyB,CAAGF,EACjC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CACxB,IAAI,CAAC,WACNE,EAAM,MAAM,CAAG,QACnB,GACK,KAAK,CAAC,SAAUD,CAAM,EAAI,OAAOC,EAAM,eAAe,CAACD,EAAS,EACzE,EACAwB,EAA2B,SAAS,CAAC,KAAK,CAAG,SAAUK,CAAK,EACxD,IAAI5B,EAAQ,IAAI,CACZ6B,EAAS,IAAI,CAAC,iBAAiB,CAEnC,GAAIA,AAAuB,OAAvBA,EAAO,WAAW,CAClB,OAAOA,EAAO,KAAK,CAEvB,IAAIC,EAAeD,EAAO,KAAK,CAACD,GAEhCE,EAAa,KAAK,CAAC,SAAU/B,CAAM,EAAI,OAAOC,EAAM,eAAe,CAACD,EAAS,GAC7E8B,EAAO,KAAK,CAAC,KAAK,CAAC,SAAU9B,CAAM,EAAI,OAAOC,EAAM,cAAc,CAACD,EAAS,GAE5E,IAAIgC,EAAQN,QAAQ,IAAI,CAAC,CAACK,EAAc,IAAI,CAAC,aAAa,CAAC,EAE3D,OADA,IAAI,CAAC,gBAAgB,CAACC,GACfA,CACX,EACAR,EAA2B,SAAS,CAAC,KAAK,CAAG,WACzC,IAAIvB,EAAQ,IAAI,QAChB,AAAI,AAAuBX,KAAAA,IAAvB,IAAI,CAAC,aAAa,CACX,IAAI,CAAC,iBAAiB,CAAC,KAAK,GAEhC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,WAAc,OAAOW,EAAM,KAAK,EAAI,EAC/E,EACAuB,EAA2B,SAAS,CAAC,KAAK,CAAG,SAAUxB,CAAM,EACzD,GAAI,AAAgB,YAAhB,IAAI,CAAC,MAAM,CAIf,OAAO8B,AADM,IAAI,CAAC,iBAAiB,CACrB,KAAK,CAAC9B,EACxB,EACAwB,EAA2B,SAAS,CAAC,gBAAgB,CAAG,SAAUS,CAAY,EAC1E,IACIC,EADAjC,EAAQ,IAAI,CAEZkC,EAAc,WACVlC,EAAM,aAAa,GAAKiC,GACxBjC,CAAAA,EAAM,aAAa,CAAGX,KAAAA,CAAQ,CAEtC,CACA,KAAI,CAAC,aAAa,CAAG4C,EAAeD,EAAa,IAAI,CAACE,EAAaA,EACvE,EACAX,EAA2B,SAAS,CAAC,mBAAmB,CAAG,WACvD,IAAIvB,EAAQ,IAAI,CAChB,GAAI,AAAuBX,KAAAA,IAAvB,IAAI,CAAC,aAAa,CAClB,OAAOoC,QAAQ,OAAO,GAE1B,IAAIU,EAAa,WAAc,OAAOnC,EAAM,mBAAmB,EAAI,EACnE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAACmC,EAAYA,EAC/C,EACAZ,EAA2B,SAAS,CAAC,cAAc,CAAG,SAAUxB,CAAM,EAClE,IAAIC,EAAQ,IAAI,CAChB,GAAI,AAAgB,aAAhB,IAAI,CAAC,MAAM,CAAgC,CAC3C,IAAI,CAAC,MAAM,CAAG,WACd,IAAI,CAAC,YAAY,CAAGD,EACpB,IAAIoC,EAAa,WAAc,OAAOnC,EAAM,eAAe,CAACD,EAAS,CACjE,AAAuBV,MAAAA,IAAvB,IAAI,CAAC,aAAa,CAClB8C,IAGA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAACA,EAAYA,GAEhD,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAACpC,EACzC,CACJ,EACAwB,EAA2B,SAAS,CAAC,eAAe,CAAG,SAAUxB,CAAM,EAC/C,aAAhB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,cAAc,CAACA,GAEJ,aAAhB,IAAI,CAAC,MAAM,GACX,IAAI,CAAC,MAAM,CAAG,UACd,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,EAElD,EACOwB,CACX,IAEA,SAASa,EAA6BjE,CAAI,MAlXJA,EAoXlC,OADAN,EAlXA,GAAKK,EAD6BC,EAmXEA,IA/W/BO,EAAkB,IAAIP,KAFhB,IAkXJ,SAAUQ,CAAS,SACtB,AAAIA,EAAU,WAAW,GAAKR,EACnBQ,EAGJ,IAAIR,EADOkE,EAA0B1D,GAEhD,CACJ,CACA,SAAS0D,EAA0B1D,CAAS,EACxCd,EAAOa,EAAkBC,IACzB,IAIIkD,EAJAtD,EAAWI,EAAU,QAAQ,CAAEF,EAAWE,EAAU,QAAQ,CAChEd,EAAOU,AAAoB,KAApBA,EAAS,MAAM,EACtBV,EAAOY,AAAoB,KAApBA,EAAS,MAAM,EACtB,IAAII,EAASN,EAAS,SAAS,GAE/B,GAAI,CACAsD,EAASpD,EAAS,SAAS,EAC/B,CACA,MAAOJ,EAAG,CAEN,MADAQ,EAAO,WAAW,GACZR,CACV,CACA,OAAO,IAAIiE,EAAmCzD,EAAQgD,EAC1D,CACA,IAAIS,EAAoD,WACpD,SAASA,EAAmCzD,CAAM,CAAEgD,CAAM,EACtD,IAAI7B,EAAQ,IAAI,AAChB,KAAI,CAAC,0BAA0B,CAAGX,KAAAA,EAClC,IAAI,CAAC,OAAO,CAAG,SAAUc,CAAM,EAC3B,IAAIA,EAAO,IAAI,CAIf,OADAH,EAAM,0BAA0B,CAAC,OAAO,CAACG,EAAO,KAAK,EAC9CH,EAAM,OAAO,CAAC,IAAI,GAAG,IAAI,CAACA,EAAM,OAAO,CAClD,EACA,IAAI,CAAC,QAAQ,CAAG,SAAUD,CAAM,EAC5BC,EAAM,YAAY,CAACD,GACnBC,EAAM,0BAA0B,CAAC,KAAK,CAACD,GACvCC,EAAM,OAAO,CAAC,MAAM,CAACD,GAAQ,KAAK,CAAChC,GACnCiC,EAAM,OAAO,CAAC,KAAK,CAACD,GAAQ,KAAK,CAAChC,EACtC,EACA,IAAI,CAAC,YAAY,CAAG,WAChBiC,EAAM,aAAa,GACnBA,EAAM,0BAA0B,CAAC,SAAS,GAC1C,IAAIuC,EAAQ,AAAI7E,UAAU,8BAC1BsC,EAAM,OAAO,CAAC,KAAK,CAACuC,GAAO,KAAK,CAACxE,EACrC,EACA,IAAI,CAAC,OAAO,CAAGc,EACf,IAAI,CAAC,OAAO,CAAGgD,EACf,IAAI,CAAC,aAAa,CAAG,IAAIJ,QAAQ,SAAUC,CAAO,CAAEC,CAAM,EACtD3B,EAAM,aAAa,CAAG0B,EACtB1B,EAAM,YAAY,CAAG2B,CACzB,EACJ,CAoBA,OAnBAW,EAAmC,SAAS,CAAC,KAAK,CAAG,SAAUxC,CAAU,EACrE,IAAI,CAAC,0BAA0B,CAAGA,EAClC,IAAI,CAAC,OAAO,CAAC,IAAI,GACZ,IAAI,CAAC,IAAI,CAAC,OAAO,EACjB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAE,IAAI,CAAC,QAAQ,EAC1C,IAAI0C,EAAe,IAAI,CAAC,OAAO,CAAC,MAAM,CAClCA,GACAA,EACK,IAAI,CAAC,IAAI,CAAC,YAAY,CAAE,IAAI,CAAC,QAAQ,CAElD,EACAF,EAAmC,SAAS,CAAC,SAAS,CAAG,SAAUV,CAAK,EACpE,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAACA,EAC9B,EACAU,EAAmC,SAAS,CAAC,KAAK,CAAG,WACjD,IAAItC,EAAQ,IAAI,CAChB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,GACpB,IAAI,CAAC,WAAc,OAAOA,EAAM,aAAa,AAAE,EACxD,EACOsC,CACX,gpBCniBA,IA+8FQnE,EASAA,EAl2EJW,EACA2D,EAvnBAC,EAAiB,AAAkB,YAAlB,OAAOC,QAAyB,AAA2B,UAA3B,OAAOA,OAAO,QAAQ,CACvEA,OACA,SAAUC,CAAW,EAAI,MAAO,UAAU,MAAM,CAACA,EAAa,IAAM,EA+CxE,SAASC,EAASC,CAAC,EACf,IAAIC,EAAI,AAAkB,YAAlB,OAAOJ,QAAyBA,OAAO,QAAQ,CAAEK,EAAID,GAAKD,CAAC,CAACC,EAAE,CAAEE,EAAI,EAC5E,GAAID,EAAG,OAAOA,EAAE,IAAI,CAACF,GACrB,GAAIA,GAAK,AAAoB,UAApB,OAAOA,EAAE,MAAM,CAAe,MAAO,CAC1C,KAAM,WAEF,OADIA,GAAKG,GAAKH,EAAE,MAAM,EAAEA,CAAAA,EAAI,KAAK,GAC1B,CAAE,MAAOA,GAAKA,CAAC,CAACG,IAAI,CAAE,KAAM,CAACH,CAAE,CAC1C,CACJ,CACA,OAAM,AAAIpF,UAAUqF,EAAI,0BAA4B,kCACxD,CAEA,SAASG,EAAQC,CAAC,EACd,OAAO,IAAI,YAAYD,EAAW,KAAI,CAAC,CAAC,CAAGC,EAAG,IAAI,AAAD,EAAK,IAAID,EAAQC,EACtE,CAiCA,SAASpF,IAET,CAEA,SAASC,EAAaC,CAAC,EACnB,MAAO,AAAc,UAAb,OAAOA,GAAkBA,AAAM,OAANA,GAAe,AAAa,YAAb,OAAOA,CAC3D,CAXA,AAA2B,YAA3B,OAAOmF,iBAAiCA,gBAaxC,SAASC,EAAgBC,CAAE,CAAEC,CAAI,EAC7B,GAAI,CACAjG,OAAO,cAAc,CAACgG,EAAI,OAAQ,CAC9B,MAAOC,EACP,aAAc,EAClB,EACJ,CACA,MAAOzE,EAAI,CAGX,CACJ,CAEA,IAAI0E,EAAkB/B,QAClBgC,EAAsBhC,QAAQ,SAAS,CAAC,IAAI,CAC5CiC,EAAwBjC,QAAQ,MAAM,CAAC,IAAI,CAAC+B,GAEhD,SAASG,EAAWC,CAAQ,EACxB,OAAO,IAAIJ,EAAgBI,EAC/B,CAEA,SAASC,EAAoBC,CAAK,EAC9B,OAAOH,EAAW,SAAUjC,CAAO,EAAI,OAAOA,EAAQoC,EAAQ,EAClE,CAEA,SAASC,EAAoBhE,CAAM,EAC/B,OAAO2D,EAAsB3D,EACjC,CACA,SAASiE,EAAmBC,CAAO,CAAEC,CAAW,CAAEC,CAAU,EAGxD,OAAOV,EAAoB,IAAI,CAACQ,EAASC,EAAaC,EAC1D,CAIA,SAASC,EAAYH,CAAO,CAAEC,CAAW,CAAEC,CAAU,EACjDH,EAAmBA,EAAmBC,EAASC,EAAaC,GAAa9E,KAAAA,EAtCxCtB,EAuCrC,CACA,SAASsG,EAAgBJ,CAAO,CAAEC,CAAW,EACzCE,EAAYH,EAASC,EACzB,CACA,SAASI,EAAcL,CAAO,CAAEE,CAAU,EACtCC,EAAYH,EAAS5E,KAAAA,EAAW8E,EACpC,CACA,SAASI,EAAqBN,CAAO,CAAEO,CAAkB,CAAEC,CAAgB,EACvE,OAAOT,EAAmBC,EAASO,EAAoBC,EAC3D,CACA,SAASC,EAA0BT,CAAO,EACtCD,EAAmBC,EAAS5E,KAAAA,EAlDKtB,EAmDrC,CACA,IAAI4G,EAAkB,SAAUC,CAAQ,EACpC,GAAI,AAA0B,YAA1B,OAAOC,eACPF,EAAkBE,mBAEjB,CACD,IAAIC,EAAoBjB,EAAoBxE,KAAAA,GAC5CsF,EAAkB,SAAUI,CAAE,EAAI,OAAOf,EAAmBc,EAAmBC,EAAK,CACxF,CACA,OAAOJ,EAAgBC,EAC3B,EACA,SAASI,EAAYC,CAAC,CAAEC,CAAC,CAAEC,CAAI,EAC3B,GAAI,AAAa,YAAb,OAAOF,EACP,MAAM,AAAIvH,UAAU,8BAExB,OAAO0H,SAAS,SAAS,CAAC,KAAK,CAAC,IAAI,CAACH,EAAGC,EAAGC,EAC/C,CACA,SAASE,EAAYJ,CAAC,CAAEC,CAAC,CAAEC,CAAI,EAC3B,GAAI,CACA,OAAOtB,EAAoBmB,EAAYC,EAAGC,EAAGC,GACjD,CACA,MAAOrB,EAAO,CACV,OA9CGJ,EA8CwBI,EAC/B,CACJ,CAWA,IAAIwB,EAA6B,WAC7B,SAASA,IACL,IAAI,CAAC,OAAO,CAAG,EACf,IAAI,CAAC,KAAK,CAAG,EAEb,IAAI,CAAC,MAAM,CAAG,CACV,UAAW,EAAE,CACb,MAAOjG,KAAAA,CACX,EACA,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,MAAM,CAIxB,IAAI,CAAC,OAAO,CAAG,EAEf,IAAI,CAAC,KAAK,CAAG,CACjB,CAqFA,OApFA/B,OAAO,cAAc,CAACgI,EAAY,SAAS,CAAE,SAAU,CACnD,IAAK,WACD,OAAO,IAAI,CAAC,KAAK,AACrB,EACA,WAAY,GACZ,aAAc,EAClB,GAKAA,EAAY,SAAS,CAAC,IAAI,CAAG,SAAUC,CAAO,EAC1C,IAAIC,EAAU,IAAI,CAAC,KAAK,CACpBC,EAAUD,CACmBE,CAAAA,QAA7BF,EAAQ,SAAS,CAAC,MAAM,EACxBC,CAAAA,EAAU,CACN,UAAW,EAAE,CACb,MAAOpG,KAAAA,CACX,GAIJmG,EAAQ,SAAS,CAAC,IAAI,CAACD,GACnBE,IAAYD,IACZ,IAAI,CAAC,KAAK,CAAGC,EACbD,EAAQ,KAAK,CAAGC,GAEpB,EAAE,IAAI,CAAC,KAAK,AAChB,EAGAH,EAAY,SAAS,CAAC,KAAK,CAAG,WAC1B,IAAIK,EAAW,IAAI,CAAC,MAAM,CACtBC,EAAWD,EACXE,EAAY,IAAI,CAAC,OAAO,CACxBC,EAAYD,EAAY,EACxBE,EAAWJ,EAAS,SAAS,CAC7BJ,EAAUQ,CAAQ,CAACF,EAAU,CAajC,OA1EmB,QA8DfC,IACAF,EAAWD,EAAS,KAAK,CACzBG,EAAY,GAGhB,EAAE,IAAI,CAAC,KAAK,CACZ,IAAI,CAAC,OAAO,CAAGA,EACXH,IAAaC,GACb,KAAI,CAAC,MAAM,CAAGA,CAAO,EAGzBG,CAAQ,CAACF,EAAU,CAAGxG,KAAAA,EACfkG,CACX,EASAD,EAAY,SAAS,CAAC,OAAO,CAAG,SAAUV,CAAQ,EAI9C,IAHA,IAAI3B,EAAI,IAAI,CAAC,OAAO,CAChB+C,EAAO,IAAI,CAAC,MAAM,CAClBD,EAAWC,EAAK,SAAS,CAEzB,AADG/C,CAAAA,IAAM8C,EAAS,MAAM,EAAIC,AAAe3G,KAAAA,IAAf2G,EAAK,KAAK,AAAa,GAC/C/C,CAAAA,IAAM8C,EAAS,MAAM,GAErBA,EAAWC,AADXA,CAAAA,EAAOA,EAAK,KAAK,AAAD,EACA,SAAS,CACzB/C,EAAI,EACA8C,AAAoB,IAApBA,EAAS,MAAM,CAJC,GAD8B,EAStDnB,EAASmB,CAAQ,CAAC9C,EAAE,EACpB,EAAEA,CACN,CACJ,EAGAqC,EAAY,SAAS,CAAC,IAAI,CAAG,WACzB,IAAIW,EAAQ,IAAI,CAAC,MAAM,CACnBC,EAAS,IAAI,CAAC,OAAO,CACzB,OAAOD,EAAM,SAAS,CAACC,EAAO,AAClC,EACOZ,CACX,IAEIa,EAAazD,EAAe,kBAC5B0D,EAAa1D,EAAe,kBAC5B2D,EAAc3D,EAAe,mBAC7B4D,EAAY5D,EAAe,iBAC3B6D,EAAe7D,EAAe,oBAElC,SAAS8D,EAAsC3H,CAAM,CAAE4H,CAAM,EACzD5H,EAAO,oBAAoB,CAAG4H,EAC9BA,EAAO,OAAO,CAAG5H,EACb4H,AAAkB,aAAlBA,EAAO,MAAM,CACbC,EAAqC7H,GAEhC4H,AAAkB,WAAlBA,EAAO,MAAM,CAClBE,AAuCR,SAAwD9H,CAAM,EAC1D6H,EAAqC7H,GACrC+H,EAAkC/H,EACtC,EA1CuDA,GAG/CgI,AAgCR,SAAwDhI,CAAM,CAAEkB,CAAM,EAClE2G,EAAqC7H,GACrCiI,EAAiCjI,EAAQkB,EAC7C,EAnCuDlB,EAAQ4H,EAAO,YAAY,CAElF,CAGA,SAASM,EAAkClI,CAAM,CAAEkB,CAAM,EAErD,OAAOiH,GADMnI,EAAO,oBAAoB,CACJkB,EACxC,CACA,SAASkH,EAAmCpI,CAAM,EAC9C,IAAI4H,EAAS5H,EAAO,oBAAoB,AACpC4H,AAAkB,cAAlBA,EAAO,MAAM,CACbK,EAAiCjI,EAAQ,AAAInB,UAAU,qFAGvDwJ,AAkCR,SAAmDrI,CAAM,CAAEkB,CAAM,MAjBTlB,EAAQkB,EAARlB,EAkBLA,EAlBakB,EAkBLA,EAjBvD2G,EAAqC7H,GACrCiI,EAAiCjI,EAAQkB,EAiB7C,EApCkDlB,EAAQ,AAAInB,UAAU,qFAEpE+I,EAAO,yBAAyB,CAACF,EAAa,GAC9CE,EAAO,OAAO,CAAGpH,KAAAA,EACjBR,EAAO,oBAAoB,CAAGQ,KAAAA,CAClC,CAEA,SAAS8H,EAAoB5D,CAAI,EAC7B,OAAO,AAAI7F,UAAU,UAAY6F,EAAO,oCAC5C,CAEA,SAASmD,EAAqC7H,CAAM,EAChDA,EAAO,cAAc,CAAG8E,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EACxD9C,EAAO,sBAAsB,CAAG6C,EAChC7C,EAAO,qBAAqB,CAAG8C,CACnC,EACJ,CACA,SAASkF,EAA+ChI,CAAM,CAAEkB,CAAM,EAClE2G,EAAqC7H,GACrCiI,EAAiCjI,EAAQkB,EAC7C,CAKA,SAAS+G,EAAiCjI,CAAM,CAAEkB,CAAM,EACpD,GAAIlB,AAAiCQ,KAAAA,IAAjCR,EAAO,qBAAqB,CAGhC6F,EAA0B7F,EAAO,cAAc,EAC/CA,EAAO,qBAAqB,CAACkB,GAC7BlB,EAAO,sBAAsB,CAAGQ,KAAAA,EAChCR,EAAO,qBAAqB,CAAGQ,KAAAA,CACnC,CAIA,SAASuH,EAAkC/H,CAAM,EAC7C,GAAIA,AAAkCQ,KAAAA,IAAlCR,EAAO,sBAAsB,CAGjCA,EAAO,sBAAsB,CAACQ,KAAAA,GAC9BR,EAAO,sBAAsB,CAAGQ,KAAAA,EAChCR,EAAO,qBAAqB,CAAGQ,KAAAA,CACnC,CAIA,IAAI+H,EAAiBC,OAAO,QAAQ,EAAI,SAAUpJ,CAAC,EAC/C,MAAO,AAAa,UAAb,OAAOA,GAAkBqJ,SAASrJ,EAC7C,EAIIsJ,EAAYC,KAAK,KAAK,EAAI,SAAUrE,CAAC,EACrC,OAAOA,EAAI,EAAIqE,KAAK,IAAI,CAACrE,GAAKqE,KAAK,KAAK,CAACrE,EAC7C,EAMA,SAASsE,EAAiBC,CAAG,CAAEC,CAAO,MAHhB1J,EAIlB,GAAIyJ,AAAQrI,KAAAA,IAARqI,GAAqB,CAHlB,CAAa,UAAb,OADWzJ,EAIqByJ,IAHP,AAAa,YAAb,OAAOzJ,CAAe,EAIlD,MAAM,AAAIP,UAAU,GAAG,MAAM,CAACiK,EAAS,sBAE/C,CAEA,SAASC,EAAe3J,CAAC,CAAE0J,CAAO,EAC9B,GAAI,AAAa,YAAb,OAAO1J,EACP,MAAM,AAAIP,UAAU,GAAG,MAAM,CAACiK,EAAS,uBAE/C,CAKA,SAASE,EAAa5J,CAAC,CAAE0J,CAAO,MAHd1J,EAId,GAAI,CAHG,CAAc,UAAb,OADMA,EAIAA,IAHmBA,AAAM,OAANA,GAAe,AAAa,YAAb,OAAOA,CAAe,EAIlE,MAAM,AAAIP,UAAU,GAAG,MAAM,CAACiK,EAAS,sBAE/C,CACA,SAASG,EAAuB7J,CAAC,CAAE8J,CAAQ,CAAEJ,CAAO,EAChD,GAAI1J,AAAMoB,KAAAA,IAANpB,EACA,MAAM,AAAIP,UAAU,aAAa,MAAM,CAACqK,EAAU,qBAAqB,MAAM,CAACJ,EAAS,MAE/F,CACA,SAASK,EAAoB/J,CAAC,CAAEgK,CAAK,CAAEN,CAAO,EAC1C,GAAI1J,AAAMoB,KAAAA,IAANpB,EACA,MAAM,AAAIP,UAAU,GAAG,MAAM,CAACuK,EAAO,qBAAqB,MAAM,CAACN,EAAS,MAElF,CAEA,SAASO,EAA0BpE,CAAK,EACpC,OAAOuD,OAAOvD,EAClB,CACA,SAASqE,EAAmBlK,CAAC,EACzB,OAAOA,AAAM,IAANA,EAAU,EAAIA,CACzB,CAKA,SAASmK,EAAwCtE,CAAK,CAAE6D,CAAO,EAE3D,IATwB1J,EAAAA,EASpBoK,EAAahB,OAAO,gBAAgB,CACpCpJ,EAAIoJ,OAAOvD,GAEf,GAAI,CAACsD,EADLnJ,EAVOA,AAAM,KADWA,EAWDA,GAVN,EAAIA,GAYjB,MAAM,AAAIP,UAAU,GAAG,MAAM,CAACiK,EAAS,4BAG3C,GAAI1J,AADJA,CAAAA,EAdOA,AAAM,KADWA,EAIEsJ,EAWVtJ,IAdC,EAAIA,CAcJ,EAPA,GAQKA,EAAIoK,EACtB,MAAM,AAAI3K,UAAU,GAAG,MAAM,CAACiK,EAAS,sCAAsC,MAAM,CATtE,EASmF,QAAQ,MAAM,CAACU,EAAY,uBAE/H,AAAI,AAACjB,EAAenJ,IAAMA,AAAM,IAANA,EAOnBA,EANI,CAOf,CAEA,SAASqK,EAAqBrK,CAAC,CAAE0J,CAAO,EACpC,GAAI,CAACY,GAAiBtK,GAClB,MAAM,AAAIP,UAAU,GAAG,MAAM,CAACiK,EAAS,6BAE/C,CAGA,SAASa,EAAmC/B,CAAM,EAC9C,OAAO,IAAIgC,GAA4BhC,EAC3C,CAEA,SAASiC,GAA6BjC,CAAM,CAAEkC,CAAW,EACrDlC,EAAO,OAAO,CAAC,aAAa,CAAC,IAAI,CAACkC,EACtC,CACA,SAASC,GAAiCnC,CAAM,CAAE7E,CAAK,CAAEiH,CAAI,EAEzD,IAAIF,EAAc9J,AADL4H,EAAO,OAAO,CACF,aAAa,CAAC,KAAK,GACxCoC,EACAF,EAAY,WAAW,GAGvBA,EAAY,WAAW,CAAC/G,EAEhC,CACA,SAASkH,GAAiCrC,CAAM,EAC5C,OAAOA,EAAO,OAAO,CAAC,aAAa,CAAC,MAAM,AAC9C,CACA,SAASsC,GAA+BtC,CAAM,EAC1C,IAAI5H,EAAS4H,EAAO,OAAO,OAC3B,GAAepH,KAAAA,IAAXR,GAGCmK,GAA8BnK,KAFxB,EAMf,CAMA,IAAI4J,GAA6C,WAC7C,SAASA,EAA4BhC,CAAM,EAGvC,GAFAqB,EAAuBrB,EAAQ,EAAG,+BAClC6B,EAAqB7B,EAAQ,mBACzBwC,GAAuBxC,GACvB,MAAM,AAAI/I,UAAU,+EAExB8I,EAAsC,IAAI,CAAEC,GAC5C,IAAI,CAAC,aAAa,CAAG,IAAInB,CAC7B,CAwEA,OAvEAhI,OAAO,cAAc,CAACmL,EAA4B,SAAS,CAAE,SAAU,CAKnE,IAAK,WACD,GAAI,CAACO,GAA8B,IAAI,EACnC,OA1XLtF,EA0XgCwF,GAAiC,WAEhE,OAAO,IAAI,CAAC,cAAc,AAC9B,EACA,WAAY,GACZ,aAAc,EAClB,GAIAT,EAA4B,SAAS,CAAC,MAAM,CAAG,SAAU1I,CAAM,EAE3D,GADe,KAAK,IAAhBA,GAAqBA,CAAAA,EAASV,KAAAA,CAAQ,EACtC,CAAC2J,GAA8B,IAAI,EACnC,OAvYDtF,EAuY4BwF,GAAiC,WAEhE,GAAI,AAA8B7J,KAAAA,IAA9B,IAAI,CAAC,oBAAoB,CACzB,OA1YDqE,EA0Y4ByD,EAAoB,WAEnD,OAAOJ,EAAkC,IAAI,CAAEhH,EACnD,EAMA0I,EAA4B,SAAS,CAAC,IAAI,CAAG,WACzC,GAAI,CAACO,GAA8B,IAAI,EACnC,OArZDtF,EAqZ4BwF,GAAiC,SAEhE,GAAI,AAA8B7J,KAAAA,IAA9B,IAAI,CAAC,oBAAoB,CACzB,OAxZDqE,EAwZ4ByD,EAAoB,cAInD,IAFIgC,EACAC,EACAnF,EAAUN,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EAC9CwH,EAAiBzH,EACjB0H,EAAgBzH,CACpB,GAOA,OADA0H,GAAgC,IAAI,CALlB,CACd,YAAa,SAAUzH,CAAK,EAAI,OAAOuH,EAAe,CAAE,MAAOvH,EAAO,KAAM,EAAM,EAAI,EACtF,YAAa,WAAc,OAAOuH,EAAe,CAAE,MAAO9J,KAAAA,EAAW,KAAM,EAAK,EAAI,EACpF,YAAa,SAAUhB,CAAC,EAAI,OAAO+K,EAAc/K,EAAI,CACzD,GAEO4F,CACX,EAUAwE,EAA4B,SAAS,CAAC,WAAW,CAAG,WAChD,GAAI,CAACO,GAA8B,IAAI,EACnC,MAAME,GAAiC,eAE3C,GAAI,AAA8B7J,KAAAA,IAA9B,IAAI,CAAC,oBAAoB,CAG7BiK,AA0CR,UAA4CzK,CAAM,EAC9CoI,EAAmCpI,GAEnC0K,GAA6C1K,EADrC,AAAInB,UAAU,uBAE1B,GA9C2C,IAAI,CAC3C,EACO+K,CACX,IAiBA,SAASO,GAA8B/K,CAAC,QACpC,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,mBAGtCA,aAAawK,EACxB,CACA,SAASY,GAAgCxK,CAAM,CAAE8J,CAAW,EACxD,IAAIlC,EAAS5H,EAAO,oBAAoB,AACxC4H,CAAAA,EAAO,UAAU,CAAG,GAChBA,AAAkB,WAAlBA,EAAO,MAAM,CACbkC,EAAY,WAAW,GAElBlC,AAAkB,YAAlBA,EAAO,MAAM,CAClBkC,EAAY,WAAW,CAAClC,EAAO,YAAY,EAG3CA,EAAO,yBAAyB,CAACH,EAAU,CAACqC,EAEpD,CArCArL,OAAO,gBAAgB,CAACmL,GAA4B,SAAS,CAAE,CAC3D,OAAQ,CAAE,WAAY,EAAK,EAC3B,KAAM,CAAE,WAAY,EAAK,EACzB,YAAa,CAAE,WAAY,EAAK,EAChC,OAAQ,CAAE,WAAY,EAAK,CAC/B,GACApF,EAAgBoF,GAA4B,SAAS,CAAC,MAAM,CAAE,UAC9DpF,EAAgBoF,GAA4B,SAAS,CAAC,IAAI,CAAE,QAC5DpF,EAAgBoF,GAA4B,SAAS,CAAC,WAAW,CAAE,eACzB,UAAtC,OAAO/F,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAACmL,GAA4B,SAAS,CAAE/F,EAAe,WAAW,CAAE,CACrF,MAAO,8BACP,aAAc,EAClB,GA8BJ,SAAS6G,GAA6C1K,CAAM,CAAER,CAAC,EAC3D,IAAImL,EAAe3K,EAAO,aAAa,AACvCA,CAAAA,EAAO,aAAa,CAAG,IAAIyG,EAC3BkE,EAAa,OAAO,CAAC,SAAUb,CAAW,EACtCA,EAAY,WAAW,CAACtK,EAC5B,EACJ,CAEA,SAAS6K,GAAiC3F,CAAI,EAC1C,OAAO,AAAI7F,UAAU,yCAAyC,MAAM,CAAC6F,EAAM,sDAC/E,CAK4C,UAAxC,OAAOb,EAAe,aAAa,GAM/B5D,AAHsBA,CAAAA,EAAK,CAAC,EAG1B,CAAC4D,EAAe,aAAa,CAAC,CAAG,WAC/B,OAAO,IAAI,AACf,EAEJpF,OAAO,cAAc,CAPrBmF,EAMI3D,EAC0C4D,EAAe,aAAa,CAAE,CAAE,WAAY,EAAM,IAIpG,IAAI+G,GAAiD,WACjD,SAASA,EAAgC5K,CAAM,CAAE6K,CAAa,EAC1D,IAAI,CAAC,eAAe,CAAGrK,KAAAA,EACvB,IAAI,CAAC,WAAW,CAAG,GACnB,IAAI,CAAC,OAAO,CAAGR,EACf,IAAI,CAAC,cAAc,CAAG6K,CAC1B,CAiEA,OAhEAD,EAAgC,SAAS,CAAC,IAAI,CAAG,WAC7C,IAzfsBxF,EAyflBjE,EAAQ,IAAI,CACZ2J,EAAY,WAAc,OAAO3J,EAAM,UAAU,EAAI,EAIzD,OAHA,IAAI,CAAC,eAAe,CAAG,IAAI,CAAC,eAAe,EA3frBiE,EA4fG,IAAI,CAAC,eAAe,CA3f1CD,EAAmBC,EA2fyB0F,EAAWA,IACtDA,IACG,IAAI,CAAC,eAAe,AAC/B,EACAF,EAAgC,SAAS,CAAC,MAAM,CAAG,SAAU3F,CAAK,EAC9D,IAjgBsBG,EAigBlBjE,EAAQ,IAAI,CACZ4J,EAAc,WAAc,OAAO5J,EAAM,YAAY,CAAC8D,EAAQ,EAClE,OAAO,IAAI,CAAC,eAAe,EAngBLG,EAogBG,IAAI,CAAC,eAAe,CAngB1CD,EAAmBC,EAmgByB2F,EAAaA,IACxDA,GACR,EACAH,EAAgC,SAAS,CAAC,UAAU,CAAG,WACnD,IAKIN,EACAC,EANApJ,EAAQ,IAAI,CAChB,GAAI,IAAI,CAAC,WAAW,CAChB,OAAOyB,QAAQ,OAAO,CAAC,CAAE,MAAOpC,KAAAA,EAAW,KAAM,EAAK,GAE1D,IAAIR,EAAS,IAAI,CAAC,OAAO,CAGrBoF,EAAUN,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EAC9CwH,EAAiBzH,EACjB0H,EAAgBzH,CACpB,GAsBA,OADA0H,GAAgCxK,EApBd,CACd,YAAa,SAAU+C,CAAK,EACxB5B,EAAM,eAAe,CAAGX,KAAAA,EAGxBsF,EAAgB,WAAc,OAAOwE,EAAe,CAAE,MAAOvH,EAAO,KAAM,EAAM,EAAI,EACxF,EACA,YAAa,WACT5B,EAAM,eAAe,CAAGX,KAAAA,EACxBW,EAAM,WAAW,CAAG,GACpBiH,EAAmCpI,GACnCsK,EAAe,CAAE,MAAO9J,KAAAA,EAAW,KAAM,EAAK,EAClD,EACA,YAAa,SAAUU,CAAM,EACzBC,EAAM,eAAe,CAAGX,KAAAA,EACxBW,EAAM,WAAW,CAAG,GACpBiH,EAAmCpI,GACnCuK,EAAcrJ,EAClB,CACJ,GAEOkE,CACX,EACAwF,EAAgC,SAAS,CAAC,YAAY,CAAG,SAAU3F,CAAK,EACpE,GAAI,IAAI,CAAC,WAAW,CAChB,OAAOrC,QAAQ,OAAO,CAAC,CAAE,MAAOqC,EAAO,KAAM,EAAK,EAEtD,KAAI,CAAC,WAAW,CAAG,GACnB,IAAIjF,EAAS,IAAI,CAAC,OAAO,CACzB,GAAI,CAAC,IAAI,CAAC,cAAc,CAAE,CACtB,IAjjB+C4F,EAijB3CtE,EAAS4G,EAAkClI,EAAQiF,GAEvD,OADAmD,EAAmCpI,GAjjBpCmF,EAkjB6B7D,EAAQ,WAAc,MAAQ,CAAE,MAAO2D,EAAO,KAAM,EAAK,CAAI,EAnjB1CW,KAAAA,EAojBnD,CAEA,OADAwC,EAAmCpI,GAC5BgF,EAAoB,CAAE,MAAOC,EAAO,KAAM,EAAK,EAC1D,EACO2F,CACX,IACII,GAAuC,CACvC,KAAM,WACF,GAAI,CAACC,GAA8B,IAAI,EACnC,OAhlBDpG,EAglB4BqG,GAAuC,SAEtE,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,EACvC,EACA,OAAQ,SAAUjG,CAAK,EACnB,GAAI,CAACgG,GAA8B,IAAI,EACnC,OAtlBDpG,EAslB4BqG,GAAuC,WAEtE,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAACjG,EAC1C,CACJ,CAC+BzE,MAAAA,IAA3BoD,GACAnF,OAAO,cAAc,CAACuM,GAAsCpH,GAUhE,SAASqH,GAA8B7L,CAAC,EACpC,GAAI,CAACD,EAAaC,IAGd,CAACX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,sBAFzC,MAAO,GAKX,GAAI,CAEA,OAAOA,EAAE,kBAAkB,YACvBwL,EACR,CACA,MAAO3K,EAAI,CACP,MAAO,EACX,CACJ,CAEA,SAASiL,GAAuCxG,CAAI,EAChD,OAAO,AAAI7F,UAAU,+BAA+B,MAAM,CAAC6F,EAAM,qDACrE,CAIA,IAAIyG,GAAc3C,OAAO,KAAK,EAAI,SAAUpJ,CAAC,EAEzC,OAAOA,GAAMA,CACjB,EAEA,SAASgM,GAAoBlE,CAAQ,EAGjC,OAAOA,EAAS,KAAK,EACzB,CACA,SAASmE,GAAmBC,CAAI,CAAEC,CAAU,CAAEC,CAAG,CAAEC,CAAS,CAAEC,CAAC,EAC3D,IAAI3J,WAAWuJ,GAAM,GAAG,CAAC,IAAIvJ,WAAWyJ,EAAKC,EAAWC,GAAIH,EAChE,CACA,IAAII,GAAsB,SAAUC,CAAC,EAWjC,MAVI,AAAsB,YAAtB,OAAOA,EAAE,QAAQ,CACjBD,GAAsB,SAAUzJ,CAAM,EAAI,OAAOA,EAAO,QAAQ,EAAI,EAE/D,AAA2B,YAA3B,OAAO2J,gBACZF,GAAsB,SAAUzJ,CAAM,EAAI,OAAO2J,gBAAgB3J,EAAQ,CAAE,SAAU,CAACA,EAAO,AAAC,EAAI,EAIlGyJ,GAAsB,SAAUzJ,CAAM,EAAI,OAAOA,CAAQ,EAEtDyJ,GAAoBC,EAC/B,EACIE,GAAmB,SAAUF,CAAC,EAQ9B,MAAOE,CANHA,GADA,AAAsB,WAAtB,OAAOF,EAAE,QAAQ,CACE,SAAU1J,CAAM,EAAI,OAAOA,EAAO,QAAQ,AAAE,EAI5C,SAAUA,CAAM,EAAI,OAAOA,AAAsB,IAAtBA,EAAO,UAAU,AAAQ,GAEnD0J,EAC5B,EACA,SAASG,GAAiB7J,CAAM,CAAE8J,CAAK,CAAEC,CAAG,EAGxC,GAAI/J,EAAO,KAAK,CACZ,OAAOA,EAAO,KAAK,CAAC8J,EAAOC,GAE/B,IAAIC,EAASD,EAAMD,EACfG,EAAQ,IAAIC,YAAYF,GAE5B,OADAb,GAAmBc,EAAO,EAAGjK,EAAQ8J,EAAOE,GACrCC,CACX,CACA,SAASE,GAAUC,CAAQ,CAAEC,CAAI,EAC7B,IAAIC,EAAOF,CAAQ,CAACC,EAAK,CACzB,GAAIC,MAAAA,GAGJ,GAAI,AAAgB,YAAhB,OAAOA,EACP,MAAM,AAAI3N,UAAU,GAAG,MAAM,CAACC,OAAOyN,GAAO,uBAEhD,OAAOC,EACX,CA6EA,SAASC,GAAkBb,CAAC,EAExB,OAAO,IAAI7J,WADEgK,GAAiBH,EAAE,MAAM,CAAEA,EAAE,UAAU,CAAEA,EAAE,UAAU,CAAGA,EAAE,UAAU,EAErF,CAEA,SAASc,GAAaC,CAAS,EAC3B,IAAIC,EAAOD,EAAU,MAAM,CAAC,KAAK,GAKjC,OAJAA,EAAU,eAAe,EAAIC,EAAK,IAAI,CAClCD,EAAU,eAAe,CAAG,GAC5BA,CAAAA,EAAU,eAAe,CAAG,GAEzBC,EAAK,KAAK,AACrB,CACA,SAASC,GAAqBF,CAAS,CAAE1H,CAAK,CAAE6H,CAAI,MAzBvBxI,EA0BzB,GAAI,IAzBa,UAAb,OADqBA,EA0BAwI,IAtBrB3B,GAAY7G,MAGZA,CAAAA,EAAI,KAmB0BwI,IAASC,IACvC,MAAM,AAAIjM,WAAW,wDAEzB6L,EAAU,MAAM,CAAC,IAAI,CAAC,CAAE,MAAO1H,EAAO,KAAM6H,CAAK,GACjDH,EAAU,eAAe,EAAIG,CACjC,CAKA,SAASE,GAAWL,CAAS,EACzBA,EAAU,MAAM,CAAG,IAAIlG,EACvBkG,EAAU,eAAe,CAAG,CAChC,CAEA,SAASM,GAAsB3N,CAAI,EAC/B,OAAOA,IAAS4N,QACpB,CAgBA,IAAIC,GAA2C,WAC3C,SAASA,IACL,MAAM,AAAItO,UAAU,sBACxB,CA4CA,OA3CAJ,OAAO,cAAc,CAAC0O,EAA0B,SAAS,CAAE,OAAQ,CAI/D,IAAK,WACD,GAAI,CAACC,GAA4B,IAAI,EACjC,MAAMC,GAA+B,QAEzC,OAAO,IAAI,CAAC,KAAK,AACrB,EACA,WAAY,GACZ,aAAc,EAClB,GACAF,EAA0B,SAAS,CAAC,OAAO,CAAG,SAAUG,CAAY,EAChE,GAAI,CAACF,GAA4B,IAAI,EACjC,MAAMC,GAA+B,WAIzC,GAFApE,EAAuBqE,EAAc,EAAG,WACxCA,EAAe/D,EAAwC+D,EAAc,mBACjE,AAAiD9M,KAAAA,IAAjD,IAAI,CAAC,uCAAuC,CAC5C,MAAM,AAAI3B,UAAU,0CAExB,GAAIiN,GAAiB,IAAI,CAAC,KAAK,CAAC,MAAM,EAClC,MAAM,AAAIjN,UAAU,mFAExB0O,GAAoC,IAAI,CAAC,uCAAuC,CAAED,EACtF,EACAH,EAA0B,SAAS,CAAC,kBAAkB,CAAG,SAAUrL,CAAI,EACnE,GAAI,CAACsL,GAA4B,IAAI,EACjC,MAAMC,GAA+B,sBAGzC,GADApE,EAAuBnH,EAAM,EAAG,sBAC5B,CAACsK,YAAY,MAAM,CAACtK,GACpB,MAAM,AAAIjD,UAAU,gDAExB,GAAI,AAAiD2B,KAAAA,IAAjD,IAAI,CAAC,uCAAuC,CAC5C,MAAM,AAAI3B,UAAU,0CAExB,GAAIiN,GAAiBhK,EAAK,MAAM,EAC5B,MAAM,AAAIjD,UAAU,iFAExB2O,GAA+C,IAAI,CAAC,uCAAuC,CAAE1L,EACjG,EACOqL,CACX,IACA1O,OAAO,gBAAgB,CAAC0O,GAA0B,SAAS,CAAE,CACzD,QAAS,CAAE,WAAY,EAAK,EAC5B,mBAAoB,CAAE,WAAY,EAAK,EACvC,KAAM,CAAE,WAAY,EAAK,CAC7B,GACA3I,EAAgB2I,GAA0B,SAAS,CAAC,OAAO,CAAE,WAC7D3I,EAAgB2I,GAA0B,SAAS,CAAC,kBAAkB,CAAE,sBAC9B,UAAtC,OAAOtJ,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAAC0O,GAA0B,SAAS,CAAEtJ,EAAe,WAAW,CAAE,CACnF,MAAO,4BACP,aAAc,EAClB,GAOJ,IAAI4J,GAA8C,WAC9C,SAASA,IACL,MAAM,AAAI5O,UAAU,sBACxB,CAgIA,OA/HAJ,OAAO,cAAc,CAACgP,EAA6B,SAAS,CAAE,cAAe,CAIzE,IAAK,WACD,GAAI,CAACC,GAA+B,IAAI,EACpC,MAAMC,GAAwC,eAElD,OAAOC,GAA2C,IAAI,CAC1D,EACA,WAAY,GACZ,aAAc,EAClB,GACAnP,OAAO,cAAc,CAACgP,EAA6B,SAAS,CAAE,cAAe,CAKzE,IAAK,WACD,GAAI,CAACC,GAA+B,IAAI,EACpC,MAAMC,GAAwC,eAElD,OAAOE,GAA2C,IAAI,CAC1D,EACA,WAAY,GACZ,aAAc,EAClB,GAKAJ,EAA6B,SAAS,CAAC,KAAK,CAAG,WAC3C,GAAI,CAACC,GAA+B,IAAI,EACpC,MAAMC,GAAwC,SAElD,GAAI,IAAI,CAAC,eAAe,CACpB,MAAM,AAAI9O,UAAU,8DAExB,IAAIiP,EAAQ,IAAI,CAAC,6BAA6B,CAAC,MAAM,CACrD,GAAIA,AAAU,aAAVA,EACA,MAAM,AAAIjP,UAAU,kBAAkB,MAAM,CAACiP,EAAO,8DAExDC,GAAkC,IAAI,CAC1C,EACAN,EAA6B,SAAS,CAAC,OAAO,CAAG,SAAU1K,CAAK,EAC5D,GAAI,CAAC2K,GAA+B,IAAI,EACpC,MAAMC,GAAwC,WAGlD,GADA1E,EAAuBlG,EAAO,EAAG,WAC7B,CAACqJ,YAAY,MAAM,CAACrJ,GACpB,MAAM,AAAIlE,UAAU,sCAExB,GAAIkE,AAAqB,IAArBA,EAAM,UAAU,CAChB,MAAM,AAAIlE,UAAU,uCAExB,GAAIkE,AAA4B,IAA5BA,EAAM,MAAM,CAAC,UAAU,CACvB,MAAM,AAAIlE,UAAU,gDAExB,GAAI,IAAI,CAAC,eAAe,CACpB,MAAM,AAAIA,UAAU,gCAExB,IAAIiP,EAAQ,IAAI,CAAC,6BAA6B,CAAC,MAAM,CACrD,GAAIA,AAAU,aAAVA,EACA,MAAM,AAAIjP,UAAU,kBAAkB,MAAM,CAACiP,EAAO,mEAExDE,GAAoC,IAAI,CAAEjL,EAC9C,EAIA0K,EAA6B,SAAS,CAAC,KAAK,CAAG,SAAUjO,CAAC,EAEtD,GADU,KAAK,IAAXA,GAAgBA,CAAAA,EAAIgB,KAAAA,CAAQ,EAC5B,CAACkN,GAA+B,IAAI,EACpC,MAAMC,GAAwC,SAElDM,GAAkC,IAAI,CAAEzO,EAC5C,EAEAiO,EAA6B,SAAS,CAACjG,EAAY,CAAG,SAAUtG,CAAM,EAClEgN,GAAkD,IAAI,EACtDlB,GAAW,IAAI,EACf,IAAI1L,EAAS,IAAI,CAAC,gBAAgB,CAACJ,GAEnC,OADAiN,GAA4C,IAAI,EACzC7M,CACX,EAEAmM,EAA6B,SAAS,CAAChG,EAAU,CAAG,SAAUqC,CAAW,EACrE,IAAIlC,EAAS,IAAI,CAAC,6BAA6B,CAC/C,GAAI,IAAI,CAAC,eAAe,CAAG,EAAG,CAC1BwG,GAAqD,IAAI,CAAEtE,GAC3D,MACJ,CACA,IAAIuE,EAAwB,IAAI,CAAC,sBAAsB,CACvD,GAAIA,AAA0B7N,KAAAA,IAA1B6N,EAAqC,CACrC,IAAInM,EAAS,KAAK,EAClB,GAAI,CACAA,EAAS,IAAIkK,YAAYiC,EAC7B,CACA,MAAOC,EAAS,CACZxE,EAAY,WAAW,CAACwE,GACxB,MACJ,CACA,IAAIC,EAAqB,CACrB,OAAQrM,EACR,iBAAkBmM,EAClB,WAAY,EACZ,WAAYA,EACZ,YAAa,EACb,YAAa,EACb,YAAa,EACb,gBAAiBtM,WACjB,WAAY,SAChB,EACA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAACwM,EAChC,CACA1E,GAA6BjC,EAAQkC,GACrC0E,GAA6C,IAAI,CACrD,EAEAf,EAA6B,SAAS,CAAC/F,EAAa,CAAG,WACnD,GAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAG,EAAG,CACnC,IAAI+G,EAAgB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAC/CA,CAAAA,EAAc,UAAU,CAAG,OAC3B,IAAI,CAAC,iBAAiB,CAAG,IAAIhI,EAC7B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAACgI,EAChC,CACJ,EACOhB,CACX,IAkBA,SAASC,GAA+BtO,CAAC,QACrC,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,mCAGtCA,aAAaqO,EACxB,CACA,SAASL,GAA4BhO,CAAC,QAClC,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,6CAGtCA,aAAa+N,EACxB,CACA,SAASqB,GAA6CvN,CAAU,EAE5D,IAAI,CADayN,AAsPrB,SAAoDzN,CAAU,EAC1D,IAAI2G,EAAS3G,EAAW,6BAA6B,OACrD,AAAsB,aAAlB2G,EAAO,MAAM,GAGb3G,EAAW,eAAe,GAG1B,CAACA,EAAW,QAAQ,MAGpBiJ,GAA+BtC,IAAWqC,GAAiCrC,GAAU,GAGrF+G,GAA4B/G,IAAWgH,GAAqChH,GAAU,GAItFiH,AADchB,GAA2C5M,GAC3C,IAGX,GACX,EA5QgEA,IAI5D,GAAIA,EAAW,QAAQ,CAAE,CACrBA,EAAW,UAAU,CAAG,GACxB,MACJ,CACAA,EAAW,QAAQ,CAAG,GAGtBsE,EADkBtE,EAAW,cAAc,GAClB,WAMrB,OALAA,EAAW,QAAQ,CAAG,GAClBA,EAAW,UAAU,GACrBA,EAAW,UAAU,CAAG,GACxBuN,GAA6CvN,IAE1C,IACX,EAAG,SAAUzB,CAAC,EAEV,OADAyO,GAAkChN,EAAYzB,GACvC,IACX,GACJ,CACA,SAAS0O,GAAkDjN,CAAU,EACjE6N,GAAkD7N,GAClDA,EAAW,iBAAiB,CAAG,IAAIwF,CACvC,CACA,SAASsI,GAAqDnH,CAAM,CAAE2G,CAAkB,EACpF,IAAIvE,EAAO,EACW,YAAlBpC,EAAO,MAAM,EACboC,CAAAA,EAAO,EAAG,EAEd,IAAIgF,EAAaC,GAAsDV,EACnEA,AAAkC,aAAlCA,EAAmB,UAAU,CAC7BxE,GAAiCnC,EAAQoH,EAAYhF,GAGrDkF,AAkeR,SAA8CtH,CAAM,CAAE7E,CAAK,CAAEiH,CAAI,EAE7D,IAAImF,EAAkBnP,AADT4H,EAAO,OAAO,CACE,iBAAiB,CAAC,KAAK,GAChDoC,EACAmF,EAAgB,WAAW,CAACpM,GAG5BoM,EAAgB,WAAW,CAACpM,EAEpC,EA3e6C6E,EAAQoH,EAAYhF,EAEjE,CACA,SAASiF,GAAsDV,CAAkB,EAC7E,IAAIa,EAAcb,EAAmB,WAAW,CAC5Cc,EAAcd,EAAmB,WAAW,CAChD,OAAO,IAAIA,EAAmB,eAAe,CAACA,EAAmB,MAAM,CAAEA,EAAmB,UAAU,CAAEa,EAAcC,EAC1H,CACA,SAASC,GAAgDrO,CAAU,CAAEiB,CAAM,CAAEqN,CAAU,CAAEC,CAAU,EAC/FvO,EAAW,MAAM,CAAC,IAAI,CAAC,CAAE,OAAQiB,EAAQ,WAAYqN,EAAY,WAAYC,CAAW,GACxFvO,EAAW,eAAe,EAAIuO,CAClC,CACA,SAASC,GAAsDxO,CAAU,CAAEiB,CAAM,CAAEqN,CAAU,CAAEC,CAAU,EACrG,IAAIE,EACJ,GAAI,CACAA,EAAc3D,GAAiB7J,EAAQqN,EAAYA,EAAaC,EACpE,CACA,MAAOG,EAAQ,CAEX,MADA1B,GAAkChN,EAAY0O,GACxCA,CACV,CACAL,GAAgDrO,EAAYyO,EAAa,EAAGF,EAChF,CACA,SAASI,GAA2D3O,CAAU,CAAE4O,CAAe,EACvFA,EAAgB,WAAW,CAAG,GAC9BJ,GAAsDxO,EAAY4O,EAAgB,MAAM,CAAEA,EAAgB,UAAU,CAAEA,EAAgB,WAAW,EAErJC,GAAiD7O,EACrD,CACA,SAAS8O,GAA4D9O,CAAU,CAAEsN,CAAkB,EAC/F,IAAIyB,EAAiBrH,KAAK,GAAG,CAAC1H,EAAW,eAAe,CAAEsN,EAAmB,UAAU,CAAGA,EAAmB,WAAW,EACpH0B,EAAiB1B,EAAmB,WAAW,CAAGyB,EAClDE,EAA4BF,EAC5BG,EAAQ,GACRC,EAAiBH,EAAiB1B,EAAmB,WAAW,CAChE8B,EAAkBJ,EAAiBG,EAGnCC,GAAmB9B,EAAmB,WAAW,GACjD2B,EAA4BG,EAAkB9B,EAAmB,WAAW,CAC5E4B,EAAQ,IAGZ,IADA,IAAIG,EAAQrP,EAAW,MAAM,CACtBiP,EAA4B,GAAG,CAClC,IAAIK,EAAcD,EAAM,IAAI,GACxBE,EAAc7H,KAAK,GAAG,CAACuH,EAA2BK,EAAY,UAAU,EACxEE,EAAYlC,EAAmB,UAAU,CAAGA,EAAmB,WAAW,CAC9ElD,GAAmBkD,EAAmB,MAAM,CAAEkC,EAAWF,EAAY,MAAM,CAAEA,EAAY,UAAU,CAAEC,GACjGD,EAAY,UAAU,GAAKC,EAC3BF,EAAM,KAAK,IAGXC,EAAY,UAAU,EAAIC,EAC1BD,EAAY,UAAU,EAAIC,GAE9BvP,EAAW,eAAe,EAAIuP,EAC9BE,GAAuDzP,EAAYuP,EAAajC,GAChF2B,GAA6BM,CACjC,CACA,OAAOL,CACX,CACA,SAASO,GAAuDzP,CAAU,CAAE6L,CAAI,CAAEyB,CAAkB,EAChGA,EAAmB,WAAW,EAAIzB,CACtC,CACA,SAAS6D,GAA6C1P,CAAU,EACxDA,AAA+B,IAA/BA,EAAW,eAAe,EAAUA,EAAW,eAAe,EAC9DkN,GAA4ClN,GAC5C2P,GAAoB3P,EAAW,6BAA6B,GAG5DuN,GAA6CvN,EAErD,CACA,SAAS6N,GAAkD7N,CAAU,EACjE,GAAIA,AAA4B,OAA5BA,EAAW,YAAY,CAG3BA,EAAW,YAAY,CAAC,uCAAuC,CAAGT,KAAAA,EAClES,EAAW,YAAY,CAAC,KAAK,CAAG,KAChCA,EAAW,YAAY,CAAG,IAC9B,CACA,SAAS4P,GAAiE5P,CAAU,EAChF,KAAOA,EAAW,iBAAiB,CAAC,MAAM,CAAG,GAAG,CAC5C,GAAIA,AAA+B,IAA/BA,EAAW,eAAe,CAC1B,OAEJ,IAAIsN,EAAqBtN,EAAW,iBAAiB,CAAC,IAAI,GACtD8O,GAA4D9O,EAAYsN,KACxEuB,GAAiD7O,GACjD8N,GAAqD9N,EAAW,6BAA6B,CAAEsN,GAEvG,CACJ,CArKA9P,OAAO,gBAAgB,CAACgP,GAA6B,SAAS,CAAE,CAC5D,MAAO,CAAE,WAAY,EAAK,EAC1B,QAAS,CAAE,WAAY,EAAK,EAC5B,MAAO,CAAE,WAAY,EAAK,EAC1B,YAAa,CAAE,WAAY,EAAK,EAChC,YAAa,CAAE,WAAY,EAAK,CACpC,GACAjJ,EAAgBiJ,GAA6B,SAAS,CAAC,KAAK,CAAE,SAC9DjJ,EAAgBiJ,GAA6B,SAAS,CAAC,OAAO,CAAE,WAChEjJ,EAAgBiJ,GAA6B,SAAS,CAAC,KAAK,CAAE,SACpB,UAAtC,OAAO5J,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAACgP,GAA6B,SAAS,CAAE5J,EAAe,WAAW,CAAE,CACtF,MAAO,+BACP,aAAc,EAClB,GA4PJ,SAASiN,GAA4C7P,CAAU,CAAEqM,CAAY,EACzE,IAAIuC,EAAkB5O,EAAW,iBAAiB,CAAC,IAAI,GACvD6N,GAAkD7N,GAE9C6M,AAAU,WADF7M,EAAW,6BAA6B,CAAC,MAAM,EAEvD8P,AAvCR,SAA0D9P,CAAU,CAAE4O,CAAe,EAC9C,SAA/BA,EAAgB,UAAU,EAC1BC,GAAiD7O,GAErD,IAAI2G,EAAS3G,EAAW,6BAA6B,CACrD,GAAI0N,GAA4B/G,GAC5B,KAAOgH,GAAqChH,GAAU,GAElDmH,GAAqDnH,EAD5BkI,GAAiD7O,GAItF,EA4ByDA,EAAY4O,IAG7DmB,AA9BR,SAA4D/P,CAAU,CAAEqM,CAAY,CAAEiB,CAAkB,EAEpG,GADAmC,GAAuDzP,EAAYqM,EAAciB,GAC7EA,AAAkC,SAAlCA,EAAmB,UAAU,CAAa,CAC1CqB,GAA2D3O,EAAYsN,GACvEsC,GAAiE5P,GACjE,MACJ,CACA,IAAIsN,CAAAA,EAAmB,WAAW,CAAGA,EAAmB,WAAW,AAAD,GAKlEuB,GAAiD7O,GACjD,IAAIgQ,EAAgB1C,EAAmB,WAAW,CAAGA,EAAmB,WAAW,CACnF,GAAI0C,EAAgB,EAAG,CACnB,IAAIhF,EAAMsC,EAAmB,UAAU,CAAGA,EAAmB,WAAW,CACxEkB,GAAsDxO,EAAYsN,EAAmB,MAAM,CAAEtC,EAAMgF,EAAeA,EACtH,CACA1C,EAAmB,WAAW,EAAI0C,EAClClC,GAAqD9N,EAAW,6BAA6B,CAAEsN,GAC/FsC,GAAiE5P,GACrE,EAS2DA,EAAYqM,EAAcuC,GAEjFrB,GAA6CvN,EACjD,CACA,SAAS6O,GAAiD7O,CAAU,EAEhE,OADiBA,EAAW,iBAAiB,CAAC,KAAK,EAEvD,CAwBA,SAASkN,GAA4ClN,CAAU,EAC3DA,EAAW,cAAc,CAAGT,KAAAA,EAC5BS,EAAW,gBAAgB,CAAGT,KAAAA,CAClC,CAEA,SAASuN,GAAkC9M,CAAU,EACjD,IAAI2G,EAAS3G,EAAW,6BAA6B,CACrD,GAAIA,CAAAA,EAAW,eAAe,EAAI2G,AAAkB,aAAlBA,EAAO,MAAM,EAG/C,GAAI3G,EAAW,eAAe,CAAG,EAAG,CAChCA,EAAW,eAAe,CAAG,GAC7B,MACJ,CACA,GAAIA,EAAW,iBAAiB,CAAC,MAAM,CAAG,EAAG,CACzC,IAAIiQ,EAAuBjQ,EAAW,iBAAiB,CAAC,IAAI,GAC5D,GAAIiQ,EAAqB,WAAW,CAAGA,EAAqB,WAAW,EAAK,EAAG,CAC3E,IAAI1R,EAAI,AAAIX,UAAU,0DAEtB,OADAoP,GAAkChN,EAAYzB,GACxCA,CACV,CACJ,CACA2O,GAA4ClN,GAC5C2P,GAAoBhJ,GACxB,CACA,SAASoG,GAAoC/M,CAAU,CAAE8B,CAAK,EAC1D,IAAI6E,EAAS3G,EAAW,6BAA6B,CACrD,GAAIA,CAAAA,EAAW,eAAe,EAAI2G,AAAkB,aAAlBA,EAAO,MAAM,EAG/C,IAAI1F,EAASa,EAAM,MAAM,CAAEwM,EAAaxM,EAAM,UAAU,CAAEyM,EAAazM,EAAM,UAAU,CACvF,GAAI+I,GAAiB5J,GACjB,MAAM,AAAIrD,UAAU,wDAExB,IAAIsS,EAAoBxF,GAAoBzJ,GAC5C,GAAIjB,EAAW,iBAAiB,CAAC,MAAM,CAAG,EAAG,CACzC,IAAIiQ,EAAuBjQ,EAAW,iBAAiB,CAAC,IAAI,GAC5D,GAAI6K,GAAiBoF,EAAqB,MAAM,EAC5C,MAAM,AAAIrS,UAAU,8FAExBiQ,GAAkD7N,GAClDiQ,EAAqB,MAAM,CAAGvF,GAAoBuF,EAAqB,MAAM,EACrC,SAApCA,EAAqB,UAAU,EAC/BtB,GAA2D3O,EAAYiQ,EAE/E,CACIhH,GAA+BtC,KAC/BwJ,AA1LR,SAAmEnQ,CAAU,EAEzE,IADA,IAAIjB,EAASiB,EAAW,6BAA6B,CAAC,OAAO,CACtDjB,EAAO,aAAa,CAAC,MAAM,CAAG,GAAG,CACpC,GAAIiB,AAA+B,IAA/BA,EAAW,eAAe,CAC1B,OAGJmN,GAAqDnN,EADnCjB,EAAO,aAAa,CAAC,KAAK,GAEhD,CACJ,EAiLkEiB,GACtDgJ,AAA6C,IAA7CA,GAAiCrC,GACjC0H,GAAgDrO,EAAYkQ,EAAmB5B,EAAYC,IAGvFvO,EAAW,iBAAiB,CAAC,MAAM,CAAG,GACtC6O,GAAiD7O,GAGrD8I,GAAiCnC,EADX,IAAI7F,WAAWoP,EAAmB5B,EAAYC,GACV,MAGzDb,GAA4B/G,IAEjC0H,GAAgDrO,EAAYkQ,EAAmB5B,EAAYC,GAC3FqB,GAAiE5P,IAGjEqO,GAAgDrO,EAAYkQ,EAAmB5B,EAAYC,GAE/FhB,GAA6CvN,GACjD,CACA,SAASgN,GAAkChN,CAAU,CAAEzB,CAAC,EACpD,IAAIoI,EAAS3G,EAAW,6BAA6B,CACrD,GAAI2G,AAAkB,aAAlBA,EAAO,MAAM,CAGjBsG,GAAkDjN,GAClD+L,GAAW/L,GACXkN,GAA4ClN,GAC5CoQ,GAAoBzJ,EAAQpI,EAChC,CACA,SAAS4O,GAAqDnN,CAAU,CAAE6I,CAAW,EACjF,IAAIwH,EAAQrQ,EAAW,MAAM,CAAC,KAAK,EACnCA,CAAAA,EAAW,eAAe,EAAIqQ,EAAM,UAAU,CAC9CX,GAA6C1P,GAC7C,IAAIa,EAAO,IAAIC,WAAWuP,EAAM,MAAM,CAAEA,EAAM,UAAU,CAAEA,EAAM,UAAU,EAC1ExH,EAAY,WAAW,CAAChI,EAC5B,CACA,SAAS8L,GAA2C3M,CAAU,EAC1D,GAAIA,AAA4B,OAA5BA,EAAW,YAAY,EAAaA,EAAW,iBAAiB,CAAC,MAAM,CAAG,EAAG,CAC7E,IAAI4O,EAAkB5O,EAAW,iBAAiB,CAAC,IAAI,GACnDa,EAAO,IAAIC,WAAW8N,EAAgB,MAAM,CAAEA,EAAgB,UAAU,CAAGA,EAAgB,WAAW,CAAEA,EAAgB,UAAU,CAAGA,EAAgB,WAAW,EAChK5N,EAAcxD,OAAO,MAAM,CAAC0O,GAA0B,SAAS,EACnEoE,AAmHR,UAAwCC,CAAO,CAAEvQ,CAAU,CAAEa,CAAI,EAC7D0P,EAAQ,uCAAuC,CAAGvQ,EAClDuQ,EAAQ,KAAK,CAAG1P,CACpB,GAtHuCG,EAAahB,EAAYa,GACxDb,EAAW,YAAY,CAAGgB,CAC9B,CACA,OAAOhB,EAAW,YAAY,AAClC,CACA,SAAS4M,GAA2C5M,CAAU,EAC1D,IAAI6M,EAAQ7M,EAAW,6BAA6B,CAAC,MAAM,OAC3D,AAAI6M,AAAU,YAAVA,EACO,KAEPA,AAAU,WAAVA,EACO,EAEJ7M,EAAW,YAAY,CAAGA,EAAW,eAAe,AAC/D,CACA,SAASsM,GAAoCtM,CAAU,CAAEqM,CAAY,EACjE,IAAIuC,EAAkB5O,EAAW,iBAAiB,CAAC,IAAI,GAEvD,GAAI6M,AAAU,WADF7M,EAAW,6BAA6B,CAAC,MAAM,CAEvD,IAAIqM,AAAiB,IAAjBA,EACA,MAAM,AAAIzO,UAAU,mEACxB,KAEC,CACD,GAAIyO,AAAiB,IAAjBA,EACA,MAAM,AAAIzO,UAAU,mFAExB,GAAIgR,EAAgB,WAAW,CAAGvC,EAAeuC,EAAgB,UAAU,CACvE,MAAM,AAAI/O,WAAW,4BAE7B,CACA+O,EAAgB,MAAM,CAAGlE,GAAoBkE,EAAgB,MAAM,EACnEiB,GAA4C7P,EAAYqM,EAC5D,CACA,SAASE,GAA+CvM,CAAU,CAAEa,CAAI,EACpE,IAAI+N,EAAkB5O,EAAW,iBAAiB,CAAC,IAAI,GAEvD,GAAI6M,AAAU,WADF7M,EAAW,6BAA6B,CAAC,MAAM,CAEvD,IAAIa,AAAoB,IAApBA,EAAK,UAAU,CACf,MAAM,AAAIjD,UAAU,mFACxB,MAGA,GAAIiD,AAAoB,IAApBA,EAAK,UAAU,CACf,MAAM,AAAIjD,UAAU,mGAG5B,GAAIgR,EAAgB,UAAU,CAAGA,EAAgB,WAAW,GAAK/N,EAAK,UAAU,CAC5E,MAAM,AAAIhB,WAAW,2DAEzB,GAAI+O,EAAgB,gBAAgB,GAAK/N,EAAK,MAAM,CAAC,UAAU,CAC3D,MAAM,AAAIhB,WAAW,8DAEzB,GAAI+O,EAAgB,WAAW,CAAG/N,EAAK,UAAU,CAAG+N,EAAgB,UAAU,CAC1E,MAAM,AAAI/O,WAAW,2DAEzB,IAAI2Q,EAAiB3P,EAAK,UAAU,AACpC+N,CAAAA,EAAgB,MAAM,CAAGlE,GAAoB7J,EAAK,MAAM,EACxDgP,GAA4C7P,EAAYwQ,EAC5D,CACA,SAASC,GAAkC9J,CAAM,CAAE3G,CAAU,CAAE0Q,CAAc,CAAEC,CAAa,CAAEC,CAAe,CAAEC,CAAa,CAAEzD,CAAqB,EAC/IpN,EAAW,6BAA6B,CAAG2G,EAC3C3G,EAAW,UAAU,CAAG,GACxBA,EAAW,QAAQ,CAAG,GACtBA,EAAW,YAAY,CAAG,KAE1BA,EAAW,MAAM,CAAGA,EAAW,eAAe,CAAGT,KAAAA,EACjDwM,GAAW/L,GACXA,EAAW,eAAe,CAAG,GAC7BA,EAAW,QAAQ,CAAG,GACtBA,EAAW,YAAY,CAAG6Q,EAC1B7Q,EAAW,cAAc,CAAG2Q,EAC5B3Q,EAAW,gBAAgB,CAAG4Q,EAC9B5Q,EAAW,sBAAsB,CAAGoN,EACpCpN,EAAW,iBAAiB,CAAG,IAAIwF,EACnCmB,EAAO,yBAAyB,CAAG3G,EAEnCsE,EAAYP,EADM2M,KAC4B,WAG1C,OAFA1Q,EAAW,QAAQ,CAAG,GACtBuN,GAA6CvN,GACtC,IACX,EAAG,SAAU8Q,CAAC,EAEV,OADA9D,GAAkChN,EAAY8Q,GACvC,IACX,EACJ,CAmCA,SAAS1E,GAA+B3I,CAAI,EACxC,OAAO,AAAI7F,UAAU,uCAAuC,MAAM,CAAC6F,EAAM,oDAC7E,CAEA,SAASiJ,GAAwCjJ,CAAI,EACjD,OAAO,AAAI7F,UAAU,0CAA0C,MAAM,CAAC6F,EAAM,uDAChF,CA0BA,SAASsN,GAAgCpK,CAAM,EAC3C,OAAO,IAAIqK,GAAyBrK,EACxC,CAEA,SAASsK,GAAiCtK,CAAM,CAAEuH,CAAe,EAC7DvH,EAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAACuH,EAC1C,CAWA,SAASP,GAAqChH,CAAM,EAChD,OAAOA,EAAO,OAAO,CAAC,iBAAiB,CAAC,MAAM,AAClD,CACA,SAAS+G,GAA4B/G,CAAM,EACvC,IAAI5H,EAAS4H,EAAO,OAAO,OAC3B,GAAepH,KAAAA,IAAXR,GAGCmS,GAA2BnS,KAFrB,EAMf,CAMA,IAAIiS,GAA0C,WAC1C,SAASA,EAAyBrK,CAAM,EAGpC,GAFAqB,EAAuBrB,EAAQ,EAAG,4BAClC6B,EAAqB7B,EAAQ,mBACzBwC,GAAuBxC,GACvB,MAAM,AAAI/I,UAAU,+EAExB,GAAI,CAAC6O,GAA+B9F,EAAO,yBAAyB,EAChE,MAAM,AAAI/I,UAAU,+FAGxB8I,EAAsC,IAAI,CAAEC,GAC5C,IAAI,CAAC,iBAAiB,CAAG,IAAInB,CACjC,CAmGA,OAlGAhI,OAAO,cAAc,CAACwT,EAAyB,SAAS,CAAE,SAAU,CAKhE,IAAK,WACD,GAAI,CAACE,GAA2B,IAAI,EAChC,OArlDLtN,EAqlDgCuN,GAA8B,WAE7D,OAAO,IAAI,CAAC,cAAc,AAC9B,EACA,WAAY,GACZ,aAAc,EAClB,GAIAH,EAAyB,SAAS,CAAC,MAAM,CAAG,SAAU/Q,CAAM,EAExD,GADe,KAAK,IAAhBA,GAAqBA,CAAAA,EAASV,KAAAA,CAAQ,EACtC,CAAC2R,GAA2B,IAAI,EAChC,OAlmDDtN,EAkmD4BuN,GAA8B,WAE7D,GAAI,AAA8B5R,KAAAA,IAA9B,IAAI,CAAC,oBAAoB,CACzB,OArmDDqE,EAqmD4ByD,EAAoB,WAEnD,OAAOJ,EAAkC,IAAI,CAAEhH,EACnD,EACA+Q,EAAyB,SAAS,CAAC,IAAI,CAAG,SAAUnQ,CAAI,CAAEuQ,CAAU,EAEhE,GADmB,KAAK,IAApBA,GAAyBA,CAAAA,EAAa,CAAC,GACvC,CAACF,GAA2B,IAAI,EAChC,OA5mDDtN,EA4mD4BuN,GAA8B,SAE7D,GAAI,CAAChG,YAAY,MAAM,CAACtK,GACpB,OA/mDD+C,EA+mD4B,AAAIhG,UAAU,sCAE7C,GAAIiD,AAAoB,IAApBA,EAAK,UAAU,CACf,OAlnDD+C,EAknD4B,AAAIhG,UAAU,uCAE7C,GAAIiD,AAA2B,IAA3BA,EAAK,MAAM,CAAC,UAAU,CACtB,OArnDD+C,EAqnD4B,AAAIhG,UAAU,gDAE7C,GAAIiN,GAAiBhK,EAAK,MAAM,EAC5B,OAxnDD+C,EAwnD4B,AAAIhG,UAAU,oCAG7C,GAAI,KAxGoByT,EAASxJ,EACjC7I,EADwBqS,EAyGaD,EAzGJvJ,EAyGgB,UAvGrDF,EAAiB0J,EAASxJ,GAuGlBwJ,EArGD,CACH,IAAK/I,EAFC,AAAyE,OAAxEtJ,CAAAA,EAAKqS,MAAAA,EAAyC,KAAK,EAAIA,EAAQ,GAAG,AAAD,GAAerS,AAAO,KAAK,IAAZA,EAAgBA,EAAK,EAE1D,GAAG,MAAM,CAAC6I,EAAS,0BACzE,CAoGI,CACA,MAAOtJ,EAAG,CACN,OA/nDDqF,EA+nD4BrF,EAC/B,CACA,IAPI8S,EAsBAhI,EACAC,EAhBAgI,EAAMD,EAAQ,GAAG,CACrB,GAAIC,AAAQ,IAARA,EACA,OAnoDD1N,EAmoD4B,AAAIhG,UAAU,uCAE7C,GAl2BGoO,GAAsBnL,AAk2BTA,EAl2Bc,WAAW,EAu2BpC,IAAIyQ,EAAMzQ,EAAK,UAAU,CAC1B,OA3oDD+C,EA2oD4B,AAAI/D,WAAW,+DAC9C,MANI,GAAIyR,EAAMzQ,EAAK,MAAM,CACjB,OAvoDL+C,EAuoDgC,AAAI/D,WAAW,4DAMlD,GAAI,AAA8BN,KAAAA,IAA9B,IAAI,CAAC,oBAAoB,CACzB,OA9oDDqE,EA8oD4ByD,EAAoB,cAInD,IAAIlD,EAAUN,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EAC9CwH,EAAiBzH,EACjB0H,EAAgBzH,CACpB,GAOA,OADA0P,GAA6B,IAAI,CAAE1Q,EAAMyQ,EALnB,CAClB,YAAa,SAAUxP,CAAK,EAAI,OAAOuH,EAAe,CAAE,MAAOvH,EAAO,KAAM,EAAM,EAAI,EACtF,YAAa,SAAUA,CAAK,EAAI,OAAOuH,EAAe,CAAE,MAAOvH,EAAO,KAAM,EAAK,EAAI,EACrF,YAAa,SAAUvD,CAAC,EAAI,OAAO+K,EAAc/K,EAAI,CACzD,GAEO4F,CACX,EAUA6M,EAAyB,SAAS,CAAC,WAAW,CAAG,WAC7C,GAAI,CAACE,GAA2B,IAAI,EAChC,MAAMC,GAA8B,eAExC,GAAI,AAA8B5R,KAAAA,IAA9B,IAAI,CAAC,oBAAoB,CAG7BiS,AAuCR,UAAyCzS,CAAM,EAC3CoI,EAAmCpI,GAEnC0S,GAA8C1S,EADtC,AAAInB,UAAU,uBAE1B,GA3CwC,IAAI,CACxC,EACOoT,CACX,IAiBA,SAASE,GAA2B/S,CAAC,QACjC,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,uBAGtCA,aAAa6S,EACxB,CACA,SAASO,GAA6BxS,CAAM,CAAE8B,CAAI,CAAEyQ,CAAG,CAAEpD,CAAe,EACpE,IAAIvH,EAAS5H,EAAO,oBAAoB,AACxC4H,CAAAA,EAAO,UAAU,CAAG,GAChBA,AAAkB,YAAlBA,EAAO,MAAM,CACbuH,EAAgB,WAAW,CAACvH,EAAO,YAAY,GAG/C+K,AAziBR,SAA8C1R,CAAU,CAAEa,CAAI,CAAEyQ,CAAG,CAAEpD,CAAe,EAChF,IArYgC7P,EA0Y5B4C,EALA0F,EAAS3G,EAAW,6BAA6B,CACjD3B,EAAOwC,EAAK,WAAW,CAC3B,IAAIuN,EAtYJ,AAAIpC,GAD4B3N,EAuYaA,GArYlC,EAEJA,EAAK,iBAAiB,CAoYzBiQ,EAAazN,EAAK,UAAU,CAAE0N,EAAa1N,EAAK,UAAU,CAG9D,GAAI,CACAI,EAASyJ,GAAoB7J,EAAK,MAAM,CAC5C,CACA,MAAOtC,EAAG,CACN2P,EAAgB,WAAW,CAAC3P,GAC5B,MACJ,CACA,IAAI+O,EAAqB,CACrB,OAAQrM,EACR,iBAAkBA,EAAO,UAAU,CACnC,WAAYqN,EACZ,WAAYC,EACZ,YAAa,EACb,YAfc+C,EAAMlD,EAgBpB,YAAaA,EACb,gBAAiB/P,EACjB,WAAY,MAChB,EACA,GAAI2B,EAAW,iBAAiB,CAAC,MAAM,CAAG,EAAG,CACzCA,EAAW,iBAAiB,CAAC,IAAI,CAACsN,GAIlC2D,GAAiCtK,EAAQuH,GACzC,MACJ,CACA,GAAIvH,AAAkB,WAAlBA,EAAO,MAAM,CAAe,CAC5B,IAAIgL,EAAY,IAAItT,EAAKiP,EAAmB,MAAM,CAAEA,EAAmB,UAAU,CAAE,GACnFY,EAAgB,WAAW,CAACyD,GAC5B,MACJ,CACA,GAAI3R,EAAW,eAAe,CAAG,EAAG,CAChC,GAAI8O,GAA4D9O,EAAYsN,GAAqB,CAC7F,IAAIS,EAAaC,GAAsDV,GACvEoC,GAA6C1P,GAC7CkO,EAAgB,WAAW,CAACH,GAC5B,MACJ,CACA,GAAI/N,EAAW,eAAe,CAAE,CAC5B,IAAIzB,EAAI,AAAIX,UAAU,2DACtBoP,GAAkChN,EAAYzB,GAC9C2P,EAAgB,WAAW,CAAC3P,GAC5B,MACJ,CACJ,CACAyB,EAAW,iBAAiB,CAAC,IAAI,CAACsN,GAClC2D,GAAiCtK,EAAQuH,GACzCX,GAA6CvN,EACjD,EAkf6C2G,EAAO,yBAAyB,CAAE9F,EAAMyQ,EAAKpD,EAE1F,CAlCA1Q,OAAO,gBAAgB,CAACwT,GAAyB,SAAS,CAAE,CACxD,OAAQ,CAAE,WAAY,EAAK,EAC3B,KAAM,CAAE,WAAY,EAAK,EACzB,YAAa,CAAE,WAAY,EAAK,EAChC,OAAQ,CAAE,WAAY,EAAK,CAC/B,GACAzN,EAAgByN,GAAyB,SAAS,CAAC,MAAM,CAAE,UAC3DzN,EAAgByN,GAAyB,SAAS,CAAC,IAAI,CAAE,QACzDzN,EAAgByN,GAAyB,SAAS,CAAC,WAAW,CAAE,eACtB,UAAtC,OAAOpO,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAACwT,GAAyB,SAAS,CAAEpO,EAAe,WAAW,CAAE,CAClF,MAAO,2BACP,aAAc,EAClB,GA2BJ,SAAS6O,GAA8C1S,CAAM,CAAER,CAAC,EAC5D,IAAIqT,EAAmB7S,EAAO,iBAAiB,AAC/CA,CAAAA,EAAO,iBAAiB,CAAG,IAAIyG,EAC/BoM,EAAiB,OAAO,CAAC,SAAU1D,CAAe,EAC9CA,EAAgB,WAAW,CAAC3P,EAChC,EACJ,CAEA,SAAS4S,GAA8B1N,CAAI,EACvC,OAAO,AAAI7F,UAAU,sCAAsC,MAAM,CAAC6F,EAAM,mDAC5E,CAEA,SAASoO,GAAqBC,CAAQ,CAAEC,CAAU,EAC9C,IAAIlB,EAAgBiB,EAAS,aAAa,CAC1C,GAAIjB,AAAkBtR,KAAAA,IAAlBsR,EACA,OAAOkB,EAEX,GAAI7H,GAAY2G,IAAkBA,EAAgB,EAC9C,MAAM,AAAIhR,WAAW,yBAEzB,OAAOgR,CACX,CACA,SAASmB,GAAqBF,CAAQ,EAClC,IAAIjG,EAAOiG,EAAS,IAAI,QACxB,AAAKjG,EAGEA,EAFI,WAAc,OAAO,CAAG,CAGvC,CAEA,SAASoG,GAAuBC,CAAI,CAAErK,CAAO,EACzCF,EAAiBuK,EAAMrK,GACvB,IAAIgJ,EAAgBqB,MAAAA,EAAmC,KAAK,EAAIA,EAAK,aAAa,CAC9ErG,EAAOqG,MAAAA,EAAmC,KAAK,EAAIA,EAAK,IAAI,CAChE,MAAO,CACH,cAAerB,AAAkBtR,KAAAA,IAAlBsR,EAA8BtR,KAAAA,EAAY6I,EAA0ByI,GACnF,KAAMhF,AAAStM,KAAAA,IAATsM,EAAqBtM,KAAAA,EAAY4S,AAG/C,SAAoC3O,CAAE,CAAEqE,CAAO,EAE3C,OADAC,EAAetE,EAAIqE,GACZ,SAAU/F,CAAK,EAAI,OAAOsG,EAA0B5E,EAAG1B,GAAS,CAC3E,EAN0E+J,EAAM,GAAG,MAAM,CAAChE,EAAS,2BAC/F,CACJ,CA8CA,SAASuK,GAAqBjU,CAAC,CAAE0J,CAAO,EACpC,GAAI,CAACwK,GAAiBlU,GAClB,MAAM,AAAIP,UAAU,GAAG,MAAM,CAACiK,EAAS,6BAE/C,CAcA,IAAIyK,GAA0B,AAA2B,YAA3B,OAAOC,gBAkBjCC,GAAgC,WAChC,SAASA,EAAeC,CAAiB,CAAEC,CAAW,EACxB,KAAK,IAA3BD,GAAgCA,CAAAA,EAAoB,CAAC,GACrC,KAAK,IAArBC,GAA0BA,CAAAA,EAAc,CAAC,GACzCD,AAAsBlT,KAAAA,IAAtBkT,EACAA,EAAoB,KAGpB1K,EAAa0K,EAAmB,mBAEpC,IAtFuBE,EAAU9K,EAEjC+K,EACAC,EACAC,EACA1T,EACA6C,EAgFI6P,EAAWG,GAAuBS,EAAa,oBACnD,IAAIK,GAvF6BlL,EAuF6B,kBAtFlEF,EAD2BgL,EAuFoBF,EAtFpB5K,GACvB+K,EAAQD,MAAAA,EAA2C,KAAK,EAAIA,EAAS,KAAK,CAC1EE,EAAQF,MAAAA,EAA2C,KAAK,EAAIA,EAAS,KAAK,CAC1EG,EAAQH,MAAAA,EAA2C,KAAK,EAAIA,EAAS,KAAK,CAC1EvT,EAAOuT,MAAAA,EAA2C,KAAK,EAAIA,EAAS,IAAI,CACxE1Q,EAAQ0Q,MAAAA,EAA2C,KAAK,EAAIA,EAAS,KAAK,CACvE,CACH,MAAOC,AAAUrT,KAAAA,IAAVqT,EACHrT,KAAAA,EACAyT,AAaZ,SAA4CxP,CAAE,CAAEmP,CAAQ,CAAE9K,CAAO,EAE7D,OADAC,EAAetE,EAAIqE,GACZ,SAAU5H,CAAM,EAAI,OAAOsF,EAAY/B,EAAImP,EAAU,CAAC1S,EAAO,CAAG,CAC3E,EAhB+C2S,EAAOD,EAAU,GAAG,MAAM,CAAC9K,EAAS,6BAC3E,MAAOgL,AAAUtT,KAAAA,IAAVsT,EACHtT,KAAAA,EACA0T,AAcZ,SAA4CzP,CAAE,CAAEmP,CAAQ,CAAE9K,CAAO,EAE7D,OADAC,EAAetE,EAAIqE,GACZ,WAAc,OAAOtC,EAAY/B,EAAImP,EAAU,EAAE,CAAG,CAC/D,EAjB+CE,EAAOF,EAAU,GAAG,MAAM,CAAC9K,EAAS,6BAC3E,MAAOiL,AAAUvT,KAAAA,IAAVuT,EACHvT,KAAAA,EACA2T,AAeZ,SAA4C1P,CAAE,CAAEmP,CAAQ,CAAE9K,CAAO,EAE7D,OADAC,EAAetE,EAAIqE,GACZ,SAAU7H,CAAU,EAAI,OAAOkF,EAAY1B,EAAImP,EAAU,CAAC3S,EAAW,CAAG,CACnF,EAlB+C8S,EAAOH,EAAU,GAAG,MAAM,CAAC9K,EAAS,6BAC3E,MAAO5F,AAAU1C,KAAAA,IAAV0C,EACH1C,KAAAA,EACA4T,AAgBZ,SAA4C3P,CAAE,CAAEmP,CAAQ,CAAE9K,CAAO,EAE7D,OADAC,EAAetE,EAAIqE,GACZ,SAAU/F,CAAK,CAAE9B,CAAU,EAAI,OAAOuF,EAAY/B,EAAImP,EAAU,CAAC7Q,EAAO9B,EAAW,CAAG,CACjG,EAnB+CiC,EAAO0Q,EAAU,GAAG,MAAM,CAAC9K,EAAS,6BAC3E,KAAMzI,CACV,GAqEI,GAFAgU,GAAyB,IAAI,EAEzBhU,AAASG,KAAAA,IADFwT,EAAe,IAAI,CAE1B,MAAM,AAAIlT,WAAW,6BAEzB,IAAIwT,EAAgBrB,GAAqBF,GAEzCwB,AAotBR,UAAgE3M,CAAM,CAAEoM,CAAc,CAAElC,CAAa,CAAEwC,CAAa,EAChH,IACI3C,EACA6C,EACAC,EACAC,EAJAzT,EAAaxC,OAAO,MAAM,CAACkW,GAAgC,SAAS,EAMpEhD,EADAqC,AAAyBxT,KAAAA,IAAzBwT,EAAe,KAAK,CACH,WAAc,OAAOA,EAAe,KAAK,CAAC/S,EAAa,EAGvD,WAAgC,EAGjDuT,EADAR,AAAyBxT,KAAAA,IAAzBwT,EAAe,KAAK,CACH,SAAUjR,CAAK,EAAI,OAAOiR,EAAe,KAAK,CAACjR,EAAO9B,EAAa,EAGnE,WAAc,OAAO+D,EAAoBxE,KAAAA,EAAY,EAGtEiU,EADAT,AAAyBxT,KAAAA,IAAzBwT,EAAe,KAAK,CACH,WAAc,OAAOA,EAAe,KAAK,EAAI,EAG7C,WAAc,OAAOhP,EAAoBxE,KAAAA,EAAY,EAQ1EoU,GAAqChN,EAAQ3G,EAAY0Q,EAAgB6C,EAAgBC,EALrFC,EADAV,AAAyBxT,KAAAA,IAAzBwT,EAAe,KAAK,CACH,SAAU9S,CAAM,EAAI,OAAO8S,EAAe,KAAK,CAAC9S,EAAS,EAGzD,WAAc,OAAO8D,EAAoBxE,KAAAA,EAAY,EAE+CsR,EAAewC,EAC5I,GAnvB+D,IAAI,CAAEN,EADzClB,GAAqBC,EAAU,GACyCuB,EAChG,CAmEA,OAlEA7V,OAAO,cAAc,CAACgV,EAAe,SAAS,CAAE,SAAU,CAItD,IAAK,WACD,GAAI,CAACH,GAAiB,IAAI,EACtB,MAAMuB,GAA4B,UAEtC,OAAOC,GAAuB,IAAI,CACtC,EACA,WAAY,GACZ,aAAc,EAClB,GAUArB,EAAe,SAAS,CAAC,KAAK,CAAG,SAAUvS,CAAM,EAE7C,GADe,KAAK,IAAhBA,GAAqBA,CAAAA,EAASV,KAAAA,CAAQ,EACtC,CAAC8S,GAAiB,IAAI,EACtB,OAh4DDzO,EAg4D4BgQ,GAA4B,UAE3D,GAAIC,GAAuB,IAAI,EAC3B,OAn4DDjQ,EAm4D4B,AAAIhG,UAAU,oDAE7C,OAAOkW,GAAoB,IAAI,CAAE7T,EACrC,EASAuS,EAAe,SAAS,CAAC,KAAK,CAAG,WAC7B,GAAI,CAACH,GAAiB,IAAI,EACtB,OAj5DDzO,EAi5D4BgQ,GAA4B,UAE3D,GAAIC,GAAuB,IAAI,EAC3B,OAp5DDjQ,EAo5D4B,AAAIhG,UAAU,oDAE7C,GAAImW,GAAoC,IAAI,EACxC,OAv5DDnQ,EAu5D4B,AAAIhG,UAAU,2CAE7C,OAAOoW,GAAoB,IAAI,CACnC,EASAxB,EAAe,SAAS,CAAC,SAAS,CAAG,WACjC,GAAI,CAACH,GAAiB,IAAI,EACtB,MAAMuB,GAA4B,aAEtC,OAAOK,AAoBf,SAA4CtN,CAAM,EAC9C,OAAO,IAAIuN,GAA4BvN,EAC3C,EAtBkD,IAAI,CAClD,EACO6L,CACX,IAiBA,SAASyB,GAAmCtN,CAAM,EAC9C,OAAO,IAAIuN,GAA4BvN,EAC3C,CAlBAnJ,OAAO,gBAAgB,CAACgV,GAAe,SAAS,CAAE,CAC9C,MAAO,CAAE,WAAY,EAAK,EAC1B,MAAO,CAAE,WAAY,EAAK,EAC1B,UAAW,CAAE,WAAY,EAAK,EAC9B,OAAQ,CAAE,WAAY,EAAK,CAC/B,GACAjP,EAAgBiP,GAAe,SAAS,CAAC,KAAK,CAAE,SAChDjP,EAAgBiP,GAAe,SAAS,CAAC,KAAK,CAAE,SAChDjP,EAAgBiP,GAAe,SAAS,CAAC,SAAS,CAAE,aACV,UAAtC,OAAO5P,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAACgV,GAAe,SAAS,CAAE5P,EAAe,WAAW,CAAE,CACxE,MAAO,iBACP,aAAc,EAClB,GAgBJ,SAASwQ,GAAyBzM,CAAM,EACpCA,EAAO,MAAM,CAAG,WAGhBA,EAAO,YAAY,CAAGpH,KAAAA,EACtBoH,EAAO,OAAO,CAAGpH,KAAAA,EAGjBoH,EAAO,yBAAyB,CAAGpH,KAAAA,EAGnCoH,EAAO,cAAc,CAAG,IAAInB,EAG5BmB,EAAO,qBAAqB,CAAGpH,KAAAA,EAG/BoH,EAAO,aAAa,CAAGpH,KAAAA,EAGvBoH,EAAO,qBAAqB,CAAGpH,KAAAA,EAE/BoH,EAAO,oBAAoB,CAAGpH,KAAAA,EAE9BoH,EAAO,aAAa,CAAG,EAC3B,CACA,SAAS0L,GAAiBlU,CAAC,QACvB,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,+BAGtCA,aAAaqU,EACxB,CACA,SAASqB,GAAuBlN,CAAM,SAClC,AAAuBpH,KAAAA,IAAnBoH,EAAO,OAAO,EAGX,EACX,CACA,SAASmN,GAAoBnN,CAAM,CAAE1G,CAAM,EAEvC,GAAI0G,AAAkB,WAAlBA,EAAO,MAAM,EAAiBA,AAAkB,YAAlBA,EAAO,MAAM,CAC3C,OAAO5C,EAAoBxE,KAAAA,EAE/BoH,CAAAA,EAAO,yBAAyB,CAAC,YAAY,CAAG1G,EAChD,AAA6D,OAA5DjB,CAAAA,EAAK2H,EAAO,yBAAyB,CAAC,gBAAgB,AAAD,GAAe3H,AAAO,KAAK,IAAZA,GAAyBA,EAAG,KAAK,CAACiB,GAIvG,IATIjB,EASA6N,EAAQlG,EAAO,MAAM,CACzB,GAAIkG,AAAU,WAAVA,GAAsBA,AAAU,YAAVA,EACtB,OAAO9I,EAAoBxE,KAAAA,GAE/B,GAAIoH,AAAgCpH,KAAAA,IAAhCoH,EAAO,oBAAoB,CAC3B,OAAOA,EAAO,oBAAoB,CAAC,QAAQ,CAE/C,IAAIwN,EAAqB,EACX,cAAVtH,IACAsH,EAAqB,GAErBlU,EAASV,KAAAA,GAEb,IAAI4E,EAAUN,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EAC9C8E,EAAO,oBAAoB,CAAG,CAC1B,SAAUpH,KAAAA,EACV,SAAUqC,EACV,QAASC,EACT,QAAS5B,EACT,oBAAqBkU,CACzB,CACJ,GAKA,OAJAxN,EAAO,oBAAoB,CAAC,QAAQ,CAAGxC,EACnC,CAACgQ,GACDC,GAA4BzN,EAAQ1G,GAEjCkE,CACX,CACA,SAAS6P,GAAoBrN,CAAM,EAC/B,IAAIkG,EAAQlG,EAAO,MAAM,CACzB,GAAIkG,AAAU,WAAVA,GAAsBA,AAAU,YAAVA,EACtB,OA1hEGjJ,EA0hEwB,AAAIhG,UAAU,kBAAkB,MAAM,CAACiP,EAAO,+DAE7E,IAAI1I,EAAUN,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EAK9C8E,EAAO,aAAa,CAJD,CACf,SAAU/E,EACV,QAASC,CACb,CAEJ,GACIE,EAAS4E,EAAO,OAAO,CAK3B,OAJepH,KAAAA,IAAXwC,GAAwB4E,EAAO,aAAa,EAAIkG,AAAU,aAAVA,GAChDwH,GAAiCtS,GAErCuS,AAyjBJ,SAA8CtU,CAAU,EACpD4L,GAAqB5L,EAAYuU,GAAe,GAChDC,GAAoDxU,EACxD,EA5jByC2G,EAAO,yBAAyB,EAC9DxC,CACX,CAYA,SAASsQ,GAAgC9N,CAAM,CAAElE,CAAK,EAElD,GAAIoK,AAAU,aADFlG,EAAO,MAAM,CACC,CACtByN,GAA4BzN,EAAQlE,GACpC,MACJ,CACAiS,GAA6B/N,EACjC,CACA,SAASyN,GAA4BzN,CAAM,CAAE1G,CAAM,EAC/C,IAAID,EAAa2G,EAAO,yBAAyB,AACjDA,CAAAA,EAAO,MAAM,CAAG,WAChBA,EAAO,YAAY,CAAG1G,EACtB,IAAI8B,EAAS4E,EAAO,OAAO,AACZpH,MAAAA,IAAXwC,GACA4S,GAAsD5S,EAAQ9B,GAE9D,CAAC2U,AA8ET,SAAkDjO,CAAM,QACpD,AAAIA,CAAAA,AAAiCpH,KAAAA,IAAjCoH,EAAO,qBAAqB,EAAkBA,AAAiCpH,KAAAA,IAAjCoH,EAAO,qBAAqB,AAAa,GAGpF,EACX,EAnFkDA,IAAW3G,EAAW,QAAQ,EACxE0U,GAA6B/N,EAErC,CACA,SAAS+N,GAA6B/N,CAAM,EACxCA,EAAO,MAAM,CAAG,UAChBA,EAAO,yBAAyB,CAACL,EAAW,GAC5C,IAAIuO,EAAclO,EAAO,YAAY,CAKrC,GAJAA,EAAO,cAAc,CAAC,OAAO,CAAC,SAAU3E,CAAY,EAChDA,EAAa,OAAO,CAAC6S,EACzB,GACAlO,EAAO,cAAc,CAAG,IAAInB,EACxBmB,AAAgCpH,KAAAA,IAAhCoH,EAAO,oBAAoB,CAAgB,CAC3CmO,GAAkDnO,GAClD,MACJ,CACA,IAAIoO,EAAepO,EAAO,oBAAoB,CAE9C,GADAA,EAAO,oBAAoB,CAAGpH,KAAAA,EAC1BwV,EAAa,mBAAmB,CAAE,CAClCA,EAAa,OAAO,CAACF,GACrBC,GAAkDnO,GAClD,MACJ,CAEArC,EADcqC,EAAO,yBAAyB,CAACN,EAAW,CAAC0O,EAAa,OAAO,EAC1D,WAGjB,OAFAA,EAAa,QAAQ,GACrBD,GAAkDnO,GAC3C,IACX,EAAG,SAAU1G,CAAM,EAGf,OAFA8U,EAAa,OAAO,CAAC9U,GACrB6U,GAAkDnO,GAC3C,IACX,EACJ,CAuCA,SAASoN,GAAoCpN,CAAM,QAC/C,AAAIA,CAAAA,AAAyBpH,KAAAA,IAAzBoH,EAAO,aAAa,EAAkBA,AAAiCpH,KAAAA,IAAjCoH,EAAO,qBAAqB,AAAa,GAG5E,EACX,CAcA,SAASmO,GAAkDnO,CAAM,EAChCpH,KAAAA,IAAzBoH,EAAO,aAAa,GACpBA,EAAO,aAAa,CAAC,OAAO,CAACA,EAAO,YAAY,EAChDA,EAAO,aAAa,CAAGpH,KAAAA,GAE3B,IAAIwC,EAAS4E,EAAO,OAAO,AACZpH,MAAAA,IAAXwC,GACAiT,GAAiCjT,EAAQ4E,EAAO,YAAY,CAEpE,CACA,SAASsO,GAAiCtO,CAAM,CAAEuO,CAAY,EAC1D,IAAInT,EAAS4E,EAAO,OAAO,AACZpH,MAAAA,IAAXwC,GAAwBmT,IAAiBvO,EAAO,aAAa,GACzDuO,EACAC,AA0mBZ,SAAwCpT,CAAM,EAC1CqT,GAAoCrT,EACxC,EA5mB2CA,GAG/BsS,GAAiCtS,IAGzC4E,EAAO,aAAa,CAAGuO,CAC3B,CAMA,IAAIhB,GAA6C,WAC7C,SAASA,EAA4BvN,CAAM,EAGvC,GAFAqB,EAAuBrB,EAAQ,EAAG,+BAClCyL,GAAqBzL,EAAQ,mBACzBkN,GAAuBlN,GACvB,MAAM,AAAI/I,UAAU,8EAExB,KAAI,CAAC,oBAAoB,CAAG+I,EAC5BA,EAAO,OAAO,CAAG,IAAI,CACrB,IAAIkG,EAAQlG,EAAO,MAAM,CACzB,GAAIkG,AAAU,aAAVA,EACI,CAACkH,GAAoCpN,IAAWA,EAAO,aAAa,CACpEyO,GAAoC,IAAI,EAGxCC,AAgkBhB,SAAuDtT,CAAM,EACzDqT,GAAoCrT,GACpCsS,GAAiCtS,EACrC,EAnkB8D,IAAI,EAEtDuT,GAAqC,IAAI,OAExC,GAAIzI,AAAU,aAAVA,EACL0I,GAA8C,IAAI,CAAE5O,EAAO,YAAY,EACvE2O,GAAqC,IAAI,OAExC,GAAIzI,AAAU,WAAVA,EACLwI,AAujBZ,UAAuDtT,CAAM,EACzDqT,GAAoCrT,GACpCsS,GAAiCtS,EACrC,GA1jB0D,IAAI,EAClDyT,AAihBZ,SAAwDzT,CAAM,EAC1DuT,GAAqCvT,GACrC0T,GAAkC1T,EACtC,EAphB2D,IAAI,MAElD,CACD,IAAI8S,EAAclO,EAAO,YAAY,CACrC4O,GAA8C,IAAI,CAAEV,GACpDa,AAwgBZ,SAAwD3T,CAAM,CAAE9B,CAAM,EAClEqV,GAAqCvT,GACrCiT,GAAiCjT,EAAQ9B,EAC7C,EA3gB2D,IAAI,CAAE4U,EACzD,CACJ,CAiHA,OAhHArX,OAAO,cAAc,CAAC0W,EAA4B,SAAS,CAAE,SAAU,CAKnE,IAAK,WACD,GAAI,CAACyB,GAA8B,IAAI,EACnC,OAnuEL/R,EAmuEgCgS,GAAiC,WAEhE,OAAO,IAAI,CAAC,cAAc,AAC9B,EACA,WAAY,GACZ,aAAc,EAClB,GACApY,OAAO,cAAc,CAAC0W,EAA4B,SAAS,CAAE,cAAe,CASxE,IAAK,WACD,GAAI,CAACyB,GAA8B,IAAI,EACnC,MAAMC,GAAiC,eAE3C,GAAI,AAA8BrW,KAAAA,IAA9B,IAAI,CAAC,oBAAoB,CACzB,MAAMsW,GAA2B,eAErC,OAAOC,AAqJnB,SAAmD/T,CAAM,EACrD,IAAI4E,EAAS5E,EAAO,oBAAoB,CACpC8K,EAAQlG,EAAO,MAAM,OACzB,AAAIkG,AAAU,YAAVA,GAAuBA,AAAU,aAAVA,EAChB,KAEPA,AAAU,WAAVA,EACO,EAEJkJ,GAA8CpP,EAAO,yBAAyB,CACzF,EA/J6D,IAAI,CACzD,EACA,WAAY,GACZ,aAAc,EAClB,GACAnJ,OAAO,cAAc,CAAC0W,EAA4B,SAAS,CAAE,QAAS,CASlE,IAAK,WACD,GAAI,CAACyB,GAA8B,IAAI,EACnC,OA1wEL/R,EA0wEgCgS,GAAiC,UAEhE,OAAO,IAAI,CAAC,aAAa,AAC7B,EACA,WAAY,GACZ,aAAc,EAClB,GAIA1B,EAA4B,SAAS,CAAC,KAAK,CAAG,SAAUjU,CAAM,EAE1D,GADe,KAAK,IAAhBA,GAAqBA,CAAAA,EAASV,KAAAA,CAAQ,EACtC,CAACoW,GAA8B,IAAI,EACnC,OAvxED/R,EAuxE4BgS,GAAiC,UAEhE,GAAI,AAA8BrW,KAAAA,IAA9B,IAAI,CAAC,oBAAoB,CACzB,OA1xEDqE,EA0xE4BiS,GAA2B,UAE1D,OAAOG,AAgFf,SAA0CjU,CAAM,CAAE9B,CAAM,EAEpD,OAAO6T,GADM/R,EAAO,oBAAoB,CACL9B,EACvC,EAnFgD,IAAI,CAAEA,EAClD,EAIAiU,EAA4B,SAAS,CAAC,KAAK,CAAG,WAC1C,GAAI,CAACyB,GAA8B,IAAI,EACnC,OAnyED/R,EAmyE4BgS,GAAiC,UAEhE,IAAIjP,EAAS,IAAI,CAAC,oBAAoB,CACtC,GAAIA,AAAWpH,KAAAA,IAAXoH,EACA,OAvyED/C,EAuyE4BiS,GAA2B,UAE1D,GAAI9B,GAAoCpN,GACpC,OA1yED/C,EA0yE4B,AAAIhG,UAAU,2CAE7C,OAAOqY,GAAiC,IAAI,CAChD,EAWA/B,EAA4B,SAAS,CAAC,WAAW,CAAG,WAChD,GAAI,CAACyB,GAA8B,IAAI,EACnC,MAAMC,GAAiC,eAG3C,GAAIjP,AAAWpH,KAAAA,IADF,IAAI,CAAC,oBAAoB,CAItC2W,GAAmC,IAAI,CAC3C,EACAhC,EAA4B,SAAS,CAAC,KAAK,CAAG,SAAUpS,CAAK,EAEzD,GADc,KAAK,IAAfA,GAAoBA,CAAAA,EAAQvC,KAAAA,CAAQ,EACpC,CAACoW,GAA8B,IAAI,EACnC,OAr0ED/R,EAq0E4BgS,GAAiC,UAEhE,GAAI,AAA8BrW,KAAAA,IAA9B,IAAI,CAAC,oBAAoB,CACzB,OAx0EDqE,EAw0E4BiS,GAA2B,aAE1D,OAAOM,GAAiC,IAAI,CAAErU,EAClD,EACOoS,CACX,IAqBA,SAASyB,GAA8BxX,CAAC,QACpC,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,0BAGtCA,aAAa+V,EACxB,CA5BA1W,OAAO,gBAAgB,CAAC0W,GAA4B,SAAS,CAAE,CAC3D,MAAO,CAAE,WAAY,EAAK,EAC1B,MAAO,CAAE,WAAY,EAAK,EAC1B,YAAa,CAAE,WAAY,EAAK,EAChC,MAAO,CAAE,WAAY,EAAK,EAC1B,OAAQ,CAAE,WAAY,EAAK,EAC3B,YAAa,CAAE,WAAY,EAAK,EAChC,MAAO,CAAE,WAAY,EAAK,CAC9B,GACA3Q,EAAgB2Q,GAA4B,SAAS,CAAC,KAAK,CAAE,SAC7D3Q,EAAgB2Q,GAA4B,SAAS,CAAC,KAAK,CAAE,SAC7D3Q,EAAgB2Q,GAA4B,SAAS,CAAC,WAAW,CAAE,eACnE3Q,EAAgB2Q,GAA4B,SAAS,CAAC,KAAK,CAAE,SACnB,UAAtC,OAAOtR,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAAC0W,GAA4B,SAAS,CAAEtR,EAAe,WAAW,CAAE,CACrF,MAAO,8BACP,aAAc,EAClB,GAiBJ,SAASqT,GAAiClU,CAAM,EAE5C,OAAOiS,GADMjS,EAAO,oBAAoB,CAE5C,CAoBA,SAAS4S,GAAsD5S,CAAM,CAAEU,CAAK,EACpEV,AAA8B,YAA9BA,EAAO,kBAAkB,CACzBqU,GAAgCrU,EAAQU,GAGxC4T,AA+YR,SAAkDtU,CAAM,CAAE9B,CAAM,EAC5DsV,GAA8CxT,EAAQ9B,EAC1D,EAjZiD8B,EAAQU,EAEzD,CAYA,SAASyT,GAAmCnU,CAAM,EAC9C,IA5B4DA,EAAQU,EA4BhEkE,EAAS5E,EAAO,oBAAoB,CACpCuU,EAAgB,AAAI1Y,UAAU,oFAClC+W,GAAsD5S,EAAQuU,GA9BFvU,EAiCLA,EAjCaU,EAiCL6T,EAhC3DvU,AAA+B,YAA/BA,EAAO,mBAAmB,CAC1BiT,GAAiCjT,EAAQU,GAGzC8T,AA+WR,SAAmDxU,CAAM,CAAE9B,CAAM,MAlBT8B,EAAQ9B,EAAR8B,EAmBLA,EAnBa9B,EAmBLA,EAlBvDqV,GAAqCvT,GACrCiT,GAAiCjT,EAAQ9B,EAkB7C,EAjXkD8B,EAAQU,GA6BtDkE,EAAO,OAAO,CAAGpH,KAAAA,EACjBwC,EAAO,oBAAoB,CAAGxC,KAAAA,CAClC,CACA,SAAS4W,GAAiCpU,CAAM,CAAED,CAAK,EACnD,IA1XmC6E,EA0X/BA,EAAS5E,EAAO,oBAAoB,CACpC/B,EAAa2G,EAAO,yBAAyB,CAC7C6P,EAAYC,AA6LpB,SAAqDzW,CAAU,CAAE8B,CAAK,EAClE,GAAI,CACA,OAAO9B,EAAW,sBAAsB,CAAC8B,EAC7C,CACA,MAAO4U,EAAY,CAEf,OADAC,GAA6C3W,EAAY0W,GAClD,CACX,CACJ,EArMgE1W,EAAY8B,GACxE,GAAI6E,IAAW5E,EAAO,oBAAoB,CACtC,OAz6EG6B,EAy6EwBiS,GAA2B,aAE1D,IAAIhJ,EAAQlG,EAAO,MAAM,CACzB,GAAIkG,AAAU,YAAVA,EACA,OA76EGjJ,EA66EwB+C,EAAO,YAAY,EAElD,GAAIoN,GAAoCpN,IAAWkG,AAAU,WAAVA,EAC/C,OAh7EGjJ,EAg7EwB,AAAIhG,UAAU,6DAE7C,GAAIiP,AAAU,aAAVA,EACA,OAn7EGjJ,EAm7EwB+C,EAAO,YAAY,EAElD,IAAIxC,GA1Y+BwC,EA0YSA,EAzY9B9C,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EAK9C8E,EAAO,cAAc,CAAC,IAAI,CAJP,CACf,SAAU/E,EACV,QAASC,CACb,EAEJ,IAqYA,OADA+U,AA0LJ,SAA8C5W,CAAU,CAAE8B,CAAK,CAAE0U,CAAS,EACtE,GAAI,CACA5K,GAAqB5L,EAAY8B,EAAO0U,EAC5C,CACA,MAAOK,EAAU,CACbF,GAA6C3W,EAAY6W,GACzD,MACJ,CACA,IAAIlQ,EAAS3G,EAAW,yBAAyB,AAC7C,EAAC+T,GAAoCpN,IAAWA,AAAkB,aAAlBA,EAAO,MAAM,EAE7DsO,GAAiCtO,EADdmQ,GAA+C9W,IAGtEwU,GAAoDxU,EACxD,EAxMyCA,EAAY8B,EAAO0U,GACjDrS,CACX,CACA,IAAIoQ,GAAgB,CAAC,EAMjBb,GAAiD,WACjD,SAASA,IACL,MAAM,AAAI9V,UAAU,sBACxB,CAmEA,OAlEAJ,OAAO,cAAc,CAACkW,EAAgC,SAAS,CAAE,cAAe,CAQ5E,IAAK,WACD,GAAI,CAACqD,GAAkC,IAAI,EACvC,MAAMC,GAAuC,eAEjD,OAAO,IAAI,CAAC,YAAY,AAC5B,EACA,WAAY,GACZ,aAAc,EAClB,GACAxZ,OAAO,cAAc,CAACkW,EAAgC,SAAS,CAAE,SAAU,CAIvE,IAAK,WACD,GAAI,CAACqD,GAAkC,IAAI,EACvC,MAAMC,GAAuC,UAEjD,GAAI,AAA0BzX,KAAAA,IAA1B,IAAI,CAAC,gBAAgB,CAIrB,MAAM,AAAI3B,UAAU,qEAExB,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,AACvC,EACA,WAAY,GACZ,aAAc,EAClB,GAQA8V,EAAgC,SAAS,CAAC,KAAK,CAAG,SAAUnV,CAAC,EAEzD,GADU,KAAK,IAAXA,GAAgBA,CAAAA,EAAIgB,KAAAA,CAAQ,EAC5B,CAACwX,GAAkC,IAAI,EACvC,MAAMC,GAAuC,SAGjD,GAAInK,AAAU,aADF,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAMjDoK,GAAqC,IAAI,CAAE1Y,EAC/C,EAEAmV,EAAgC,SAAS,CAACrN,EAAW,CAAG,SAAUpG,CAAM,EACpE,IAAII,EAAS,IAAI,CAAC,eAAe,CAACJ,GAElC,OADAiX,GAA+C,IAAI,EAC5C7W,CACX,EAEAqT,EAAgC,SAAS,CAACpN,EAAW,CAAG,WACpDyF,GAAW,IAAI,CACnB,EACO2H,CACX,IAaA,SAASqD,GAAkC5Y,CAAC,QACxC,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,+BAGtCA,aAAauV,EACxB,CACA,SAASC,GAAqChN,CAAM,CAAE3G,CAAU,CAAE0Q,CAAc,CAAE6C,CAAc,CAAEC,CAAc,CAAEC,CAAc,CAAE5C,CAAa,CAAEwC,CAAa,EAC1JrT,EAAW,yBAAyB,CAAG2G,EACvCA,EAAO,yBAAyB,CAAG3G,EAEnCA,EAAW,MAAM,CAAGT,KAAAA,EACpBS,EAAW,eAAe,CAAGT,KAAAA,EAC7BwM,GAAW/L,GACXA,EAAW,YAAY,CAAGT,KAAAA,EAC1BS,EAAW,gBAAgB,CAAGmX,AA9tBlC,WACI,GAAI7E,GACA,OAAO,IAAIC,eAGnB,IA0tBIvS,EAAW,QAAQ,CAAG,GACtBA,EAAW,sBAAsB,CAAGqT,EACpCrT,EAAW,YAAY,CAAG6Q,EAC1B7Q,EAAW,eAAe,CAAGuT,EAC7BvT,EAAW,eAAe,CAAGwT,EAC7BxT,EAAW,eAAe,CAAGyT,EAE7BwB,GAAiCtO,EADdmQ,AAsJvB,SAAwD9W,CAAU,EAE9D,OAAO4N,AAAe,GADJmI,GAA8C/V,EAEpE,EAzJsEA,IAIlEsE,EADmBP,EADD2M,KAEQ,WAGtB,OAFA1Q,EAAW,QAAQ,CAAG,GACtBwU,GAAoDxU,GAC7C,IACX,EAAG,SAAU8Q,CAAC,EAGV,OAFA9Q,EAAW,QAAQ,CAAG,GACtByU,GAAgC9N,EAAQmK,GACjC,IACX,EACJ,CAjDAtT,OAAO,gBAAgB,CAACkW,GAAgC,SAAS,CAAE,CAC/D,YAAa,CAAE,WAAY,EAAK,EAChC,OAAQ,CAAE,WAAY,EAAK,EAC3B,MAAO,CAAE,WAAY,EAAK,CAC9B,GAC0C,UAAtC,OAAO9Q,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAACkW,GAAgC,SAAS,CAAE9Q,EAAe,WAAW,CAAE,CACzF,MAAO,kCACP,aAAc,EAClB,GA0EJ,SAASsU,GAA+ClX,CAAU,EAC9DA,EAAW,eAAe,CAAGT,KAAAA,EAC7BS,EAAW,eAAe,CAAGT,KAAAA,EAC7BS,EAAW,eAAe,CAAGT,KAAAA,EAC7BS,EAAW,sBAAsB,CAAGT,KAAAA,CACxC,CAcA,SAASwW,GAA8C/V,CAAU,EAC7D,OAAOA,EAAW,YAAY,CAAGA,EAAW,eAAe,AAC/D,CAiBA,SAASwU,GAAoDxU,CAAU,EACnE,IAAI2G,EAAS3G,EAAW,yBAAyB,CACjD,GAAI,CAACA,EAAW,QAAQ,EAGpB2G,AAAiCpH,KAAAA,IAAjCoH,EAAO,qBAAqB,CAF5B,OAMJ,GAAIkG,AAAU,aADFlG,EAAO,MAAM,CACC,CACtB+N,GAA6B/N,GAC7B,MACJ,CACA,GAAI3G,AAA6B,IAA7BA,EAAW,MAAM,CAAC,MAAM,EAG5B,IAAIgE,EAx3DG2H,AADID,AAy3DgB1L,EAz3DN,MAAM,CAAC,IAAI,GACpB,KAAK,AAy3DbgE,CAAAA,IAAUuQ,GACV6C,AAWR,SAAqDpX,CAAU,EAC3D,IArgB4C2G,EAqgBxCA,EAAS3G,EAAW,yBAAyB,AApgBjD2G,EAD4CA,EAsgBLA,GArgBhC,qBAAqB,CAAGA,EAAO,aAAa,CACnDA,EAAO,aAAa,CAAGpH,KAAAA,EAqgBvBkM,GAAazL,GACb,IAAIqX,EAAmBrX,EAAW,eAAe,GACjDkX,GAA+ClX,GAC/CsE,EAAY+S,EAAkB,eAnjBS1Q,EAanC5E,EAwiBA,MApjBJ4E,CADuCA,EAojBDA,GAnjB/B,qBAAqB,CAAC,QAAQ,CAACpH,KAAAA,GACtCoH,EAAO,qBAAqB,CAAGpH,KAAAA,EAEjB,aADFoH,EAAO,MAAM,GAGrBA,EAAO,YAAY,CAAGpH,KAAAA,EACcA,KAAAA,IAAhCoH,EAAO,oBAAoB,GAC3BA,EAAO,oBAAoB,CAAC,QAAQ,GACpCA,EAAO,oBAAoB,CAAGpH,KAAAA,IAGtCoH,EAAO,MAAM,CAAG,SAEDpH,KAAAA,KADXwC,EAAS4E,EAAO,OAAO,GAEvB8O,GAAkC1T,GAsiB3B,IACX,EAAG,SAAU9B,CAAM,MApiB6B0G,EAAQlE,EAsiBpD,OAtiB4CkE,EAqiBDA,EAriBSlE,EAqiBDxC,EApiBvD0G,EAAO,qBAAqB,CAAC,OAAO,CAAClE,GACrCkE,EAAO,qBAAqB,CAAGpH,KAAAA,EAEKA,KAAAA,IAAhCoH,EAAO,oBAAoB,GAC3BA,EAAO,oBAAoB,CAAC,OAAO,CAAClE,GACpCkE,EAAO,oBAAoB,CAAGpH,KAAAA,GAElCkV,GAAgC9N,EAAQlE,GA8hB7B,IACX,EACJ,EAxBoDzC,GAG5CsX,AAsBR,SAAqDtX,CAAU,CAAE8B,CAAK,EAClE,IA/gBiD6E,EA+gB7CA,EAAS3G,EAAW,yBAAyB,AA9gBjD2G,EADiDA,EAghBLA,GA/gBrC,qBAAqB,CAAGA,EAAO,cAAc,CAAC,KAAK,GAihB1DrC,EADuBtE,EAAW,eAAe,CAAC8B,GACpB,WAvkB9B6E,CADuCA,EAykBDA,GAxkB/B,qBAAqB,CAAC,QAAQ,CAACpH,KAAAA,GACtCoH,EAAO,qBAAqB,CAAGpH,KAAAA,EAwkB3B,IA1kBmCoH,EA0kB/BkG,EAAQlG,EAAO,MAAM,CAOzB,OANA8E,GAAazL,GACT,CAAC+T,GAAoCpN,IAAWkG,AAAU,aAAVA,GAEhDoI,GAAiCtO,EADdmQ,GAA+C9W,IAGtEwU,GAAoDxU,GAC7C,IACX,EAAG,SAAUC,CAAM,MA9kB6B0G,EAAQlE,EAmlBpD,MAJsB,aAAlBkE,EAAO,MAAM,EACbuQ,GAA+ClX,GAhlBP2G,EAklBDA,EAllBSlE,EAklBDxC,EAjlBvD0G,EAAO,qBAAqB,CAAC,OAAO,CAAClE,GACrCkE,EAAO,qBAAqB,CAAGpH,KAAAA,EAC/BkV,GAAgC9N,EAAQlE,GAglB7B,IACX,EACJ,EA3CoDzC,EAAYgE,GAEhE,CACA,SAAS2S,GAA6C3W,CAAU,CAAEyC,CAAK,EACf,aAAhDzC,EAAW,yBAAyB,CAAC,MAAM,EAC3CiX,GAAqCjX,EAAYyC,EAEzD,CAqCA,SAASqU,GAA+C9W,CAAU,EAE9D,OAAO4N,AAAe,GADJmI,GAA8C/V,EAEpE,CAEA,SAASiX,GAAqCjX,CAAU,CAAEyC,CAAK,EAC3D,IAAIkE,EAAS3G,EAAW,yBAAyB,CACjDkX,GAA+ClX,GAC/CoU,GAA4BzN,EAAQlE,EACxC,CAEA,SAASmR,GAA4BnQ,CAAI,EACrC,OAAO,AAAI7F,UAAU,4BAA4B,MAAM,CAAC6F,EAAM,yCAClE,CAEA,SAASuT,GAAuCvT,CAAI,EAChD,OAAO,AAAI7F,UAAU,6CAA6C,MAAM,CAAC6F,EAAM,0DACnF,CAEA,SAASmS,GAAiCnS,CAAI,EAC1C,OAAO,AAAI7F,UAAU,yCAAyC,MAAM,CAAC6F,EAAM,sDAC/E,CACA,SAASoS,GAA2BpS,CAAI,EACpC,OAAO,AAAI7F,UAAU,UAAY6F,EAAO,oCAC5C,CACA,SAAS6R,GAAqCvT,CAAM,EAChDA,EAAO,cAAc,CAAG8B,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EACxDE,EAAO,sBAAsB,CAAGH,EAChCG,EAAO,qBAAqB,CAAGF,EAC/BE,EAAO,mBAAmB,CAAG,SACjC,EACJ,CACA,SAAS2T,GAA+C3T,CAAM,CAAE9B,CAAM,EAClEqV,GAAqCvT,GACrCiT,GAAiCjT,EAAQ9B,EAC7C,CAKA,SAAS+U,GAAiCjT,CAAM,CAAE9B,CAAM,EACpD,GAAI8B,AAAiCxC,KAAAA,IAAjCwC,EAAO,qBAAqB,CAGhC6C,EAA0B7C,EAAO,cAAc,EAC/CA,EAAO,qBAAqB,CAAC9B,GAC7B8B,EAAO,sBAAsB,CAAGxC,KAAAA,EAChCwC,EAAO,qBAAqB,CAAGxC,KAAAA,EAC/BwC,EAAO,mBAAmB,CAAG,UACjC,CAIA,SAAS0T,GAAkC1T,CAAM,EAC7C,GAAIA,AAAkCxC,KAAAA,IAAlCwC,EAAO,sBAAsB,CAGjCA,EAAO,sBAAsB,CAACxC,KAAAA,GAC9BwC,EAAO,sBAAsB,CAAGxC,KAAAA,EAChCwC,EAAO,qBAAqB,CAAGxC,KAAAA,EAC/BwC,EAAO,mBAAmB,CAAG,UACjC,CACA,SAASqT,GAAoCrT,CAAM,EAC/CA,EAAO,aAAa,CAAG8B,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EACvDE,EAAO,qBAAqB,CAAGH,EAC/BG,EAAO,oBAAoB,CAAGF,CAClC,GACAE,EAAO,kBAAkB,CAAG,SAChC,CACA,SAASwT,GAA8CxT,CAAM,CAAE9B,CAAM,EACjEmV,GAAoCrT,GACpCqU,GAAgCrU,EAAQ9B,EAC5C,CACA,SAASoV,GAA8CtT,CAAM,EACzDqT,GAAoCrT,GACpCsS,GAAiCtS,EACrC,CACA,SAASqU,GAAgCrU,CAAM,CAAE9B,CAAM,EACnD,GAAI8B,AAAgCxC,KAAAA,IAAhCwC,EAAO,oBAAoB,CAG/B6C,EAA0B7C,EAAO,aAAa,EAC9CA,EAAO,oBAAoB,CAAC9B,GAC5B8B,EAAO,qBAAqB,CAAGxC,KAAAA,EAC/BwC,EAAO,oBAAoB,CAAGxC,KAAAA,EAC9BwC,EAAO,kBAAkB,CAAG,UAChC,CAOA,SAASsS,GAAiCtS,CAAM,EAC5C,GAAIA,AAAiCxC,KAAAA,IAAjCwC,EAAO,qBAAqB,CAGhCA,EAAO,qBAAqB,CAACxC,KAAAA,GAC7BwC,EAAO,qBAAqB,CAAGxC,KAAAA,EAC/BwC,EAAO,oBAAoB,CAAGxC,KAAAA,EAC9BwC,EAAO,kBAAkB,CAAG,WAChC,CAeA,IAAIwV,GAAUC,AAZd,WACI,GAAI,AAAsB,aAAtB,OAAOC,WACP,OAAOA,WAEN,GAAI,AAAgB,aAAhB,OAAOC,KACZ,OAAOA,KAEN,GAAI,AAAkB,aAAlB,OAAOC,OACZ,OAAOA,MAGf,IA+CA,IAAIC,GAAeC,AArBRC,CAAAA,CAAAA,AAtBX,SAAmCzZ,CAAI,EACnC,GAAI,CAAE,CAAgB,YAAhB,OAAOA,GAAuB,AAAgB,UAAhB,OAAOA,CAAgB,GAGvDA,AAAc,iBAAdA,EAAK,IAAI,CAFT,MAAO,GAKX,GAAI,CAEA,OADA,IAAIA,EACG,EACX,CACA,MAAOW,EAAI,CACP,MAAO,EACX,CACJ,EAOQX,EAAOkZ,MAAAA,GAAyC,KAAK,EAAIA,GAAQ,YAAY,EACjChY,KAAAA,EAAPlB,CAAe,IAexDkF,EAPIlF,EAAO,SAAsB0Z,CAAO,CAAEtU,CAAI,EAC1C,IAAI,CAAC,OAAO,CAAGsU,GAAW,GAC1B,IAAI,CAAC,IAAI,CAAGtU,GAAQ,QAChBuU,MAAM,iBAAiB,EACvBA,MAAM,iBAAiB,CAAC,IAAI,CAAE,IAAI,CAAC,WAAW,CAEtD,EACsB,gBACtB3Z,EAAK,SAAS,CAAGb,OAAO,MAAM,CAACwa,MAAM,SAAS,EAC9Cxa,OAAO,cAAc,CAACa,EAAK,SAAS,CAAE,cAAe,CAAE,MAAOA,EAAM,SAAU,GAAM,aAAc,EAAK,GAChGA,GAKX,SAAS4Z,GAAqBzY,CAAM,CAAE6K,CAAI,CAAE6N,CAAY,CAAEC,CAAY,CAAEvO,CAAa,CAAEwO,CAAM,EACzF,IAAIrZ,EAAS2J,EAAmClJ,GAC5CuC,EA36BG,IAAImS,GA26BqC7J,EAChD7K,CAAAA,EAAO,UAAU,CAAG,GACpB,IAAI6Y,EAAe,GAEfC,EAAevU,EAAoBxE,KAAAA,GACvC,OAAOsE,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EACvC,IAAI4R,EACJ,GAAI2E,AAAW7Y,KAAAA,IAAX6Y,EAAsB,CAsBtB,GArBA3E,EAAiB,WACb,IAAIhR,EAAQ2V,AAAkB7Y,KAAAA,IAAlB6Y,EAAO,MAAM,CAAiBA,EAAO,MAAM,CAAG,IAAIR,GAAa,UAAW,cAClFW,EAAU,EAAE,AACZ,EAACJ,GACDI,EAAQ,IAAI,CAAC,iBACT,AAAIlO,AAAgB,aAAhBA,EAAK,MAAM,CACJyJ,GAAoBzJ,EAAM5H,GAE9BsB,EAAoBxE,KAAAA,EAC/B,GAEA,CAACqK,GACD2O,EAAQ,IAAI,CAAC,iBACT,AAAI/Y,AAAkB,aAAlBA,EAAO,MAAM,CACN0H,GAAqB1H,EAAQiD,GAEjCsB,EAAoBxE,KAAAA,EAC/B,GAEJiZ,EAAmB,WAAc,OAAO7W,QAAQ,GAAG,CAAC4W,EAAQ,GAAG,CAAC,SAAUE,CAAM,EAAI,OAAOA,GAAU,GAAK,EAAG,GAAMhW,EACvH,EACI2V,EAAO,OAAO,CAAE,CAChB3E,IACA,MACJ,CACA2E,EAAO,gBAAgB,CAAC,QAAS3E,EACrC,CAmEA,GA9BAiF,EAAmBlZ,EAAQT,EAAO,cAAc,CAAE,SAAU8V,CAAW,EAOnE,OANKsD,EAIDQ,EAAS,GAAM9D,GAHf2D,EAAmB,WAAc,OAAO1E,GAAoBzJ,EAAMwK,EAAc,EAAG,GAAMA,GAKtF,IACX,GAEA6D,EAAmBrO,EAAMtI,EAAO,cAAc,CAAE,SAAU8S,CAAW,EAOjE,OANKjL,EAID+O,EAAS,GAAM9D,GAHf2D,EAAmB,WAAc,OAAOtR,GAAqB1H,EAAQqV,EAAc,EAAG,GAAMA,GAKzF,IACX,GAEA+D,AAkCA,SAA2BjS,CAAM,CAAExC,CAAO,CAAEsU,CAAM,EAC9C,GAAI9R,AAAkB,WAAlBA,EAAO,MAAM,CACb8R,SAx9FZnU,EA29F4BH,EAASsU,EAEjC,EAzCkBjZ,EAAQT,EAAO,cAAc,CAAE,WAO7C,OANKmZ,EAIDS,IAHAH,EAAmB,WAAc,OAAOK,AAhlBxD,SAA8D9W,CAAM,EAChE,IAAI4E,EAAS5E,EAAO,oBAAoB,CACpC8K,EAAQlG,EAAO,MAAM,CACzB,GAAIoN,GAAoCpN,IAAWkG,AAAU,WAAVA,EAC/C,OAAO9I,EAAoBxE,KAAAA,GAE/B,GAAIsN,AAAU,YAAVA,EACA,OA33EGjJ,EA23EwB+C,EAAO,YAAY,EAElD,OAAOsP,GAAiClU,EAC5C,EAskB6GA,EAAS,GAKnG,IACX,GAEIgS,GAAoC1J,IAASA,AAAgB,WAAhBA,EAAK,MAAM,CAAe,CACvE,IAAIyO,EAAe,AAAIlb,UAAU,+EAC5BgM,EAID+O,EAAS,GAAMG,GAHfN,EAAmB,WAAc,OAAOtR,GAAqB1H,EAAQsZ,EAAe,EAAG,GAAMA,EAKrG,CAEA,SAASC,IAGL,IAAIC,EAAkBV,EACtB,OAAOpU,EAAmBoU,EAAc,WAAc,OAAOU,IAAoBV,EAAeS,IAA0BxZ,KAAAA,CAAW,EACzI,CACA,SAASmZ,EAAmB/R,CAAM,CAAExC,CAAO,CAAEsU,CAAM,EAC3C9R,AAAkB,YAAlBA,EAAO,MAAM,CACb8R,EAAO9R,EAAO,YAAY,EAG1BnC,EAAcL,EAASsU,EAE/B,CAdA7T,EAvEWf,EAAW,SAAUoV,CAAW,CAAEC,CAAU,GAW/CC,AAVA,SAASA,EAAKpQ,CAAI,EACVA,EACAkQ,IAKA/U,EAAmBkV,AAMnC,kBACI,AAAIf,EACOtU,EAAoB,IAExBG,EAAmBnC,EAAO,aAAa,CAAE,WAC5C,OAAO8B,EAAW,SAAUwV,CAAW,CAAEC,CAAU,EAC/C/P,GAAgCxK,EAAQ,CACpC,YAAa,SAAU+C,CAAK,EACxBwW,EAAepU,EAAmBiS,GAAiCpU,EAAQD,GAAQvC,KAAAA,EAAWtB,GAC9Fob,EAAY,GAChB,EACA,YAAa,WAAc,OAAOA,EAAY,GAAO,EACrD,YAAaC,CACjB,EACJ,EACJ,EACJ,IAtB+CH,EAAMD,EAE7C,EACK,GACT,IAkFJ,SAASV,EAAmBC,CAAM,CAAEc,CAAe,CAAEC,CAAa,EAC9D,IAAInB,EAIJ,GADAA,EAAe,GACXhO,AAAgB,aAAhBA,EAAK,MAAM,EAAoB0J,GAAoC1J,GAInEoP,QAJ0E,KAp+FjEtV,EAAAA,EAq+FO4U,IAp+F5BzU,EAAYH,EAo+FyCsV,EAC7C,CAIA,SAASA,IAEL,OADAnV,EAAYmU,IAAU,WAAc,OAAOiB,EAASH,EAAiBC,EAAgB,EAAG,SAAUG,CAAQ,EAAI,OAAOD,EAAS,GAAMC,EAAW,GACxI,IACX,CACJ,CACA,SAAShB,EAASiB,CAAO,CAAEnX,CAAK,EAC5B,IAAI4V,EAIJ,GADAA,EAAe,GACXhO,AAAgB,aAAhBA,EAAK,MAAM,EAAoB0J,GAAoC1J,GAInEqP,EAASE,EAASnX,OAJwD,KAp/FjE0B,EAAAA,EAq/FO4U,IAp/F5BzU,EAAYH,EAo/FyC,WAAc,OAAOuV,EAASE,EAASnX,EAAQ,EAC5F,CAIJ,CACA,SAASiX,EAASE,CAAO,CAAEnX,CAAK,EAY5B,OAXAyT,GAAmCnU,GACnCoF,EAAmCpI,GACpBQ,KAAAA,IAAX6Y,GACAA,EAAO,mBAAmB,CAAC,QAAS3E,GAEpCmG,EACA/X,EAAOY,GAGPb,EAAQrC,KAAAA,GAEL,IACX,CACJ,EACJ,CAOA,IAAIsa,GAAiD,WACjD,SAASA,IACL,MAAM,AAAIjc,UAAU,sBACxB,CA8EA,OA7EAJ,OAAO,cAAc,CAACqc,EAAgC,SAAS,CAAE,cAAe,CAK5E,IAAK,WACD,GAAI,CAACC,GAAkC,IAAI,EACvC,MAAMC,GAAuC,eAEjD,OAAOC,GAA8C,IAAI,CAC7D,EACA,WAAY,GACZ,aAAc,EAClB,GAKAH,EAAgC,SAAS,CAAC,KAAK,CAAG,WAC9C,GAAI,CAACC,GAAkC,IAAI,EACvC,MAAMC,GAAuC,SAEjD,GAAI,CAACE,GAAiD,IAAI,EACtD,MAAM,AAAIrc,UAAU,mDAExBsc,GAAqC,IAAI,CAC7C,EACAL,EAAgC,SAAS,CAAC,OAAO,CAAG,SAAU/X,CAAK,EAE/D,GADc,KAAK,IAAfA,GAAoBA,CAAAA,EAAQvC,KAAAA,CAAQ,EACpC,CAACua,GAAkC,IAAI,EACvC,MAAMC,GAAuC,WAEjD,GAAI,CAACE,GAAiD,IAAI,EACtD,MAAM,AAAIrc,UAAU,qDAExB,OAAOuc,GAAuC,IAAI,CAAErY,EACxD,EAIA+X,EAAgC,SAAS,CAAC,KAAK,CAAG,SAAUtb,CAAC,EAEzD,GADU,KAAK,IAAXA,GAAgBA,CAAAA,EAAIgB,KAAAA,CAAQ,EAC5B,CAACua,GAAkC,IAAI,EACvC,MAAMC,GAAuC,SAEjDK,GAAqC,IAAI,CAAE7b,EAC/C,EAEAsb,EAAgC,SAAS,CAACtT,EAAY,CAAG,SAAUtG,CAAM,EACrE8L,GAAW,IAAI,EACf,IAAI1L,EAAS,IAAI,CAAC,gBAAgB,CAACJ,GAEnC,OADAoa,GAA+C,IAAI,EAC5Cha,CACX,EAEAwZ,EAAgC,SAAS,CAACrT,EAAU,CAAG,SAAUqC,CAAW,EACxE,IAAIlC,EAAS,IAAI,CAAC,yBAAyB,CAC3C,GAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAG,EAAG,CACxB,IAAI7E,EAAQ2J,GAAa,IAAI,CACzB,KAAI,CAAC,eAAe,EAAI,AAAuB,IAAvB,IAAI,CAAC,MAAM,CAAC,MAAM,EAC1C4O,GAA+C,IAAI,EACnD1K,GAAoBhJ,IAGpB2T,GAAgD,IAAI,EAExDzR,EAAY,WAAW,CAAC/G,EAC5B,MAEI8G,GAA6BjC,EAAQkC,GACrCyR,GAAgD,IAAI,CAE5D,EAEAT,EAAgC,SAAS,CAACpT,EAAa,CAAG,WAE1D,EACOoT,CACX,IAiBA,SAASC,GAAkC3b,CAAC,QACxC,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,+BAGtCA,aAAa0b,EACxB,CACA,SAASS,GAAgDta,CAAU,EAE/D,IAAI,CADaua,GAA8Cva,IAI/D,GAAIA,EAAW,QAAQ,CAAE,CACrBA,EAAW,UAAU,CAAG,GACxB,MACJ,CACAA,EAAW,QAAQ,CAAG,GAEtBsE,EADkBtE,EAAW,cAAc,GAClB,WAMrB,OALAA,EAAW,QAAQ,CAAG,GAClBA,EAAW,UAAU,GACrBA,EAAW,UAAU,CAAG,GACxBsa,GAAgDta,IAE7C,IACX,EAAG,SAAUzB,CAAC,EAEV,OADA6b,GAAqCpa,EAAYzB,GAC1C,IACX,GACJ,CACA,SAASgc,GAA8Cva,CAAU,EAC7D,IAAI2G,EAAS3G,EAAW,yBAAyB,OACjD,EAAKia,GAAiDja,MAGjDA,EAAW,QAAQ,MAGpBmJ,GAAuBxC,IAAWqC,GAAiCrC,GAAU,GAI7EiH,AADcoM,GAA8Cha,GAC9C,IAGX,GACX,CACA,SAASqa,GAA+Cra,CAAU,EAC9DA,EAAW,cAAc,CAAGT,KAAAA,EAC5BS,EAAW,gBAAgB,CAAGT,KAAAA,EAC9BS,EAAW,sBAAsB,CAAGT,KAAAA,CACxC,CAEA,SAAS2a,GAAqCla,CAAU,EACpD,IAAI,CAACia,GAAiDja,IAGtD,IAAI2G,EAAS3G,EAAW,yBAAyB,AACjDA,CAAAA,EAAW,eAAe,CAAG,GACI,IAA7BA,EAAW,MAAM,CAAC,MAAM,GACxBqa,GAA+Cra,GAC/C2P,GAAoBhJ,IAE5B,CACA,SAASwT,GAAuCna,CAAU,CAAE8B,CAAK,EAC7D,IAAI,CAACmY,GAAiDja,IAGtD,IAAI2G,EAAS3G,EAAW,yBAAyB,CACjD,GAAImJ,GAAuBxC,IAAWqC,GAAiCrC,GAAU,EAC7EmC,GAAiCnC,EAAQ7E,EAAO,QAE/C,CACD,IAAI0U,EAAY,KAAK,EACrB,GAAI,CACAA,EAAYxW,EAAW,sBAAsB,CAAC8B,EAClD,CACA,MAAO4U,EAAY,CAEf,MADA0D,GAAqCpa,EAAY0W,GAC3CA,CACV,CACA,GAAI,CACA9K,GAAqB5L,EAAY8B,EAAO0U,EAC5C,CACA,MAAOK,EAAU,CAEb,MADAuD,GAAqCpa,EAAY6W,GAC3CA,CACV,CACJ,CACAyD,GAAgDta,GACpD,CACA,SAASoa,GAAqCpa,CAAU,CAAEzB,CAAC,EACvD,IAAIoI,EAAS3G,EAAW,yBAAyB,CACjD,GAAI2G,AAAkB,aAAlBA,EAAO,MAAM,CAGjBoF,GAAW/L,GACXqa,GAA+Cra,GAC/CoQ,GAAoBzJ,EAAQpI,EAChC,CACA,SAASyb,GAA8Cha,CAAU,EAC7D,IAAI6M,EAAQ7M,EAAW,yBAAyB,CAAC,MAAM,OACvD,AAAI6M,AAAU,YAAVA,EACO,KAEPA,AAAU,WAAVA,EACO,EAEJ7M,EAAW,YAAY,CAAGA,EAAW,eAAe,AAC/D,CA/HAxC,OAAO,gBAAgB,CAACqc,GAAgC,SAAS,CAAE,CAC/D,MAAO,CAAE,WAAY,EAAK,EAC1B,QAAS,CAAE,WAAY,EAAK,EAC5B,MAAO,CAAE,WAAY,EAAK,EAC1B,YAAa,CAAE,WAAY,EAAK,CACpC,GACAtW,EAAgBsW,GAAgC,SAAS,CAAC,KAAK,CAAE,SACjEtW,EAAgBsW,GAAgC,SAAS,CAAC,OAAO,CAAE,WACnEtW,EAAgBsW,GAAgC,SAAS,CAAC,KAAK,CAAE,SACvB,UAAtC,OAAOjX,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAACqc,GAAgC,SAAS,CAAEjX,EAAe,WAAW,CAAE,CACzF,MAAO,kCACP,aAAc,EAClB,GA0HJ,SAASqX,GAAiDja,CAAU,EAChE,IAAI6M,EAAQ7M,EAAW,yBAAyB,CAAC,MAAM,OACvD,CAAKA,EAAW,eAAe,EAAI6M,AAAU,aAAVA,GACxB,EAGf,CACA,SAAS2N,GAAqC7T,CAAM,CAAE3G,CAAU,CAAE0Q,CAAc,CAAEC,CAAa,CAAEC,CAAe,CAAEC,CAAa,CAAEwC,CAAa,EAC1IrT,EAAW,yBAAyB,CAAG2G,EACvC3G,EAAW,MAAM,CAAGT,KAAAA,EACpBS,EAAW,eAAe,CAAGT,KAAAA,EAC7BwM,GAAW/L,GACXA,EAAW,QAAQ,CAAG,GACtBA,EAAW,eAAe,CAAG,GAC7BA,EAAW,UAAU,CAAG,GACxBA,EAAW,QAAQ,CAAG,GACtBA,EAAW,sBAAsB,CAAGqT,EACpCrT,EAAW,YAAY,CAAG6Q,EAC1B7Q,EAAW,cAAc,CAAG2Q,EAC5B3Q,EAAW,gBAAgB,CAAG4Q,EAC9BjK,EAAO,yBAAyB,CAAG3G,EAEnCsE,EAAYP,EADM2M,KAC4B,WAG1C,OAFA1Q,EAAW,QAAQ,CAAG,GACtBsa,GAAgDta,GACzC,IACX,EAAG,SAAU8Q,CAAC,EAEV,OADAsJ,GAAqCpa,EAAY8Q,GAC1C,IACX,EACJ,CA2BA,SAASiJ,GAAuCtW,CAAI,EAChD,OAAO,AAAI7F,UAAU,6CAA6C,MAAM,CAAC6F,EAAM,0DACnF,CAweA,SAASgX,GAAmBpJ,CAAO,CAAExJ,CAAO,EACxCF,EAAiB0J,EAASxJ,GAC1B,IAAIsQ,EAAe9G,MAAAA,EAAyC,KAAK,EAAIA,EAAQ,YAAY,CACrFzH,EAAgByH,MAAAA,EAAyC,KAAK,EAAIA,EAAQ,aAAa,CACvF6G,EAAe7G,MAAAA,EAAyC,KAAK,EAAIA,EAAQ,YAAY,CACrF+G,EAAS/G,MAAAA,EAAyC,KAAK,EAAIA,EAAQ,MAAM,CAI7E,OAHe9R,KAAAA,IAAX6Y,GACAsC,AASR,SAA2BtC,CAAM,CAAEvQ,CAAO,EACtC,GAAI,CAAC8S,AAx/DT,SAAuB3W,CAAK,EACxB,GAAI,AAAiB,UAAjB,OAAOA,GAAsBA,AAAU,OAAVA,EAC7B,MAAO,GAEX,GAAI,CACA,MAAO,AAAyB,WAAzB,OAAOA,EAAM,OAAO,AAC/B,CACA,MAAOhF,EAAI,CAEP,MAAO,EACX,CACJ,EA6+DuBoZ,GACf,MAAM,AAAIxa,UAAU,GAAG,MAAM,CAACiK,EAAS,2BAE/C,EAb0BuQ,EAAQ,GAAG,MAAM,CAACvQ,EAAS,8BAE1C,CACH,aAAc+S,CAAAA,CAAQzC,EACtB,cAAeyC,CAAAA,CAAQhR,EACvB,aAAcgR,CAAAA,CAAQ1C,EACtB,OAAQE,CACZ,CACJ,CAuBA,IAAIyC,GAAgC,WAChC,SAASA,EAAeC,CAAmB,CAAEpI,CAAW,EACxB,KAAK,IAA7BoI,GAAkCA,CAAAA,EAAsB,CAAC,GACzC,KAAK,IAArBpI,GAA0BA,CAAAA,EAAc,CAAC,GACzCoI,AAAwBvb,KAAAA,IAAxBub,EACAA,EAAsB,KAGtB/S,EAAa+S,EAAqB,mBAEtC,IAAIhJ,EAAWG,GAAuBS,EAAa,oBACnD,IAAIqI,GAnG0ClT,EAmGmC,kBAlGrFF,EAD0CnI,EAmGsBsb,EAlGvCjT,GAErBuF,EAAwBuF,MADbnT,EACwD,KAAK,EAAImT,AADjEnT,EAC0E,qBAAqB,CAC1Gwb,EAASrI,MAFEnT,EAEyC,KAAK,EAAImT,AAFlDnT,EAE2D,MAAM,CAC5Eyb,EAAOtI,MAHInT,EAGuC,KAAK,EAAImT,AAHhDnT,EAGyD,IAAI,CACxEsT,EAAQH,MAJGnT,EAIwC,KAAK,EAAImT,AAJjDnT,EAI0D,KAAK,CAC1EJ,EAAOuT,MALInT,EAKuC,KAAK,EAAImT,AALhDnT,EAKyD,IAAI,CACrE,CACH,sBAAuB4N,AAA0B7N,KAAAA,IAA1B6N,EACnB7N,KAAAA,EACA+I,EAAwC8E,EAAuB,GAAG,MAAM,CAACvF,EAAS,6CACtF,OAAQmT,AAAWzb,KAAAA,IAAXyb,EACJzb,KAAAA,EACA2b,AAUZ,SAA+C1X,CAAE,CAAEmP,CAAQ,CAAE9K,CAAO,EAEhE,OADAC,EAAetE,EAAIqE,GACZ,SAAU5H,CAAM,EAAI,OAAOsF,EAAY/B,EAAImP,EAAU,CAAC1S,EAAO,CAAG,CAC3E,EAbkD+a,EAZ/Bxb,EAYiD,GAAG,MAAM,CAACqI,EAAS,8BAC/E,KAAMoT,AAAS1b,KAAAA,IAAT0b,EACF1b,KAAAA,EACA4b,AAWZ,SAA6C3X,CAAE,CAAEmP,CAAQ,CAAE9K,CAAO,EAE9D,OADAC,EAAetE,EAAIqE,GACZ,SAAU7H,CAAU,EAAI,OAAOuF,EAAY/B,EAAImP,EAAU,CAAC3S,EAAW,CAAG,CACnF,EAdgDib,EAf7Bzb,EAe6C,GAAG,MAAM,CAACqI,EAAS,4BAC3E,MAAOiL,AAAUvT,KAAAA,IAAVuT,EACHvT,KAAAA,EACA6b,AAYZ,SAA8C5X,CAAE,CAAEmP,CAAQ,CAAE9K,CAAO,EAE/D,OADAC,EAAetE,EAAIqE,GACZ,SAAU7H,CAAU,EAAI,OAAOkF,EAAY1B,EAAImP,EAAU,CAAC3S,EAAW,CAAG,CACnF,EAfiD8S,EAlB9BtT,EAkB+C,GAAG,MAAM,CAACqI,EAAS,6BAC7E,KAAMzI,AAASG,KAAAA,IAATH,EAAqBG,KAAAA,EAAY8b,AAe/C,SAAmCjc,CAAI,CAAEyI,CAAO,EAE5C,GAAIzI,AAAS,UADbA,CAAAA,EAAO,GAAG,MAAM,CAACA,EAAI,EAEjB,MAAM,AAAIxB,UAAU,GAAG,MAAM,CAACiK,EAAS,MAAM,MAAM,CAACzI,EAAM,8DAE9D,OAAOA,CACX,EArByEA,EAAM,GAAG,MAAM,CAACyI,EAAS,2BAC9F,GA+EI,GADAyT,GAAyB,IAAI,EACzBP,AAA0B,UAA1BA,EAAiB,IAAI,CAAc,CACnC,GAAIjJ,AAAkBvS,KAAAA,IAAlBuS,EAAS,IAAI,CACb,MAAM,AAAIjS,WAAW,8DAEzB,IAAIgR,EAAgBgB,GAAqBC,EAAU,IACnDyJ,AAx3EZ,SAA+D5U,CAAM,CAAE6U,CAAoB,CAAE3K,CAAa,EACtG,IACIH,EACAC,EACAC,EAHA5Q,EAAaxC,OAAO,MAAM,CAACgP,GAA6B,SAAS,EAKjEkE,EADA8K,AAA+Bjc,KAAAA,IAA/Bic,EAAqB,KAAK,CACT,WAAc,OAAOA,EAAqB,KAAK,CAACxb,EAAa,EAG7D,WAAgC,EAGjD2Q,EADA6K,AAA8Bjc,KAAAA,IAA9Bic,EAAqB,IAAI,CACT,WAAc,OAAOA,EAAqB,IAAI,CAACxb,EAAa,EAG5D,WAAc,OAAO+D,EAAoBxE,KAAAA,EAAY,EAGrEqR,EADA4K,AAAgCjc,KAAAA,IAAhCic,EAAqB,MAAM,CACT,SAAUvb,CAAM,EAAI,OAAOub,EAAqB,MAAM,CAACvb,EAAS,EAGhE,WAAc,OAAO8D,EAAoBxE,KAAAA,EAAY,EAE3E,IAAI6N,EAAwBoO,EAAqB,qBAAqB,CACtE,GAAIpO,AAA0B,IAA1BA,EACA,MAAM,AAAIxP,UAAU,gDAExB6S,GAAkC9J,EAAQ3G,EAAY0Q,EAAgBC,EAAeC,EAAiBC,EAAezD,EACzH,EA41EkE,IAAI,CAAE2N,EAAkBlK,EAClF,KACK,CACD,IA7GkCrR,EAAQqI,EAG9CuF,EACA4N,EACAC,EACAnI,EACA1T,EAzd0DuH,EAAQoU,EAAkBlK,EAAewC,EAEnG3C,EACAC,EACAC,EAHA5Q,EA8jBQqT,EAAgBrB,GAAqBF,GACrCjB,EAAgBgB,GAAqBC,EAAU,GAhkBGnL,EAikBG,IAAI,CAjkBCoU,EAikBCA,EAjkBiBlK,EAikBCA,EAjkBcwC,EAikBCA,EAhkBpGrT,EAAaxC,OAAO,MAAM,CAACqc,GAAgC,SAAS,EAKpEnJ,EADAqK,AAA2Bxb,KAAAA,IAA3Bwb,EAAiB,KAAK,CACL,WAAc,OAAOA,EAAiB,KAAK,CAAC/a,EAAa,EAGzD,WAAgC,EAGjD2Q,EADAoK,AAA0Bxb,KAAAA,IAA1Bwb,EAAiB,IAAI,CACL,WAAc,OAAOA,EAAiB,IAAI,CAAC/a,EAAa,EAGxD,WAAc,OAAO+D,EAAoBxE,KAAAA,EAAY,EAQzEib,GAAqC7T,EAAQ3G,EAAY0Q,EAAgBC,EALrEC,EADAmK,AAA4Bxb,KAAAA,IAA5Bwb,EAAiB,MAAM,CACL,SAAU9a,CAAM,EAAI,OAAO8a,EAAiB,MAAM,CAAC9a,EAAS,EAG5D,WAAc,OAAO8D,EAAoBxE,KAAAA,EAAY,EAE8BsR,EAAewC,EA2iBpH,CACJ,CAwHA,OAvHA7V,OAAO,cAAc,CAACqd,EAAe,SAAS,CAAE,SAAU,CAItD,IAAK,WACD,GAAI,CAACpS,GAAiB,IAAI,EACtB,MAAMgT,GAA4B,UAEtC,OAAOtS,GAAuB,IAAI,CACtC,EACA,WAAY,GACZ,aAAc,EAClB,GAOA0R,EAAe,SAAS,CAAC,MAAM,CAAG,SAAU5a,CAAM,EAE9C,GADe,KAAK,IAAhBA,GAAqBA,CAAAA,EAASV,KAAAA,CAAQ,EACtC,CAACkJ,GAAiB,IAAI,EACtB,OAj3HD7E,EAi3H4B6X,GAA4B,WAE3D,GAAItS,GAAuB,IAAI,EAC3B,OAp3HDvF,EAo3H4B,AAAIhG,UAAU,qDAE7C,OAAOsJ,GAAqB,IAAI,CAAEjH,EACtC,EACA4a,EAAe,SAAS,CAAC,SAAS,CAAG,SAAUzJ,CAAU,MAn3E/BC,EAASxJ,EAE/B6T,EAsBiC/U,EA61EjC,GADmB,KAAK,IAApByK,GAAyBA,CAAAA,EAAa7R,KAAAA,CAAQ,EAC9C,CAACkJ,GAAiB,IAAI,EACtB,MAAMgT,GAA4B,aAGtC,GAAIpK,AAAiB9R,KAAAA,IAAjB8R,CAz3E2BxJ,EAw3EgB,kBAv3EnDF,EAD0B0J,EAw3EaD,EAv3EbvJ,GAEnB,CACH,KAAM6T,AAASnc,KAAAA,KAFfmc,EAAOrK,MAAAA,EAAyC,KAAK,EAAIA,EAAQ,IAAI,EAE1C9R,KAAAA,EAAYoc,AAG/C,SAAyCD,CAAI,CAAE7T,CAAO,EAElD,GAAI6T,AAAS,SADbA,CAAAA,EAAO,GAAG,MAAM,CAACA,EAAI,EAEjB,MAAM,AAAI9d,UAAU,GAAG,MAAM,CAACiK,EAAS,MAAM,MAAM,CAAC6T,EAAM,oEAE9D,OAAOA,CACX,EAT+EA,EAAM,GAAG,MAAM,CAAC7T,EAAS,2BACpG,GAo3EgB,IAAI,CACZ,OAAOa,EAAmC,IAAI,EAElD,OAp2EiC/B,EAo2EM,IAAI,CAn2ExC,IAAIqK,GAAyBrK,EAo2EpC,EACAkU,EAAe,SAAS,CAAC,WAAW,CAAG,SAAUe,CAAY,CAAExK,CAAU,EAErE,GADmB,KAAK,IAApBA,GAAyBA,CAAAA,EAAa,CAAC,GACvC,CAAC3I,GAAiB,IAAI,EACtB,MAAMgT,GAA4B,eAEtCzT,EAAuB4T,EAAc,EAAG,eACxC,IAxF6BjQ,EAAM9D,EAEnCpJ,EAGAE,EAmFIE,GAxF+BgJ,EAwFuB,kBAvF9DF,EADiCgE,EAwFeiQ,EAvFzB/T,GAEvBK,EADIzJ,EAAWkN,MAAAA,EAAmC,KAAK,EAAIA,EAAK,QAAQ,CAC1C,WAAY,wBAC1CnD,EAAqB/J,EAAU,GAAG,MAAM,CAACoJ,EAAS,gCAElDK,EADIvJ,EAAWgN,MAAAA,EAAmC,KAAK,EAAIA,EAAK,QAAQ,CAC1C,WAAY,wBAC1CyG,GAAqBzT,EAAU,GAAG,MAAM,CAACkJ,EAAS,gCAC3C,CAAE,SAAUpJ,EAAU,SAAUE,CAAS,GAiFxC0S,EAAUoJ,GAAmBrJ,EAAY,oBAC7C,GAAIjI,GAAuB,IAAI,EAC3B,MAAM,AAAIvL,UAAU,kFAExB,GAAIiW,GAAuBhV,EAAU,QAAQ,EACzC,MAAM,AAAIjB,UAAU,kFAIxB,OADAgH,EADcqT,GAAqB,IAAI,CAAEpZ,EAAU,QAAQ,CAAEwS,EAAQ,YAAY,CAAEA,EAAQ,YAAY,CAAEA,EAAQ,aAAa,CAAEA,EAAQ,MAAM,GAEvIxS,EAAU,QAAQ,AAC7B,EACAgc,EAAe,SAAS,CAAC,MAAM,CAAG,SAAUgB,CAAW,CAAEzK,CAAU,MAW3DC,EATJ,GADmB,KAAK,IAApBD,GAAyBA,CAAAA,EAAa,CAAC,GACvC,CAAC3I,GAAiB,IAAI,EACtB,OAx5HD7E,EAw5H4B6X,GAA4B,WAE3D,GAAII,AAAgBtc,KAAAA,IAAhBsc,EACA,OA35HDjY,EA25H4B,wCAE/B,GAAI,CAACyO,GAAiBwJ,GAClB,OA95HDjY,EA85H4B,AAAIhG,UAAU,8EAG7C,GAAI,CACAyT,EAAUoJ,GAAmBrJ,EAAY,mBAC7C,CACA,MAAO7S,EAAG,CACN,OAr6HDqF,EAq6H4BrF,EAC/B,CACA,GAAI4K,GAAuB,IAAI,EAC3B,OAx6HDvF,EAw6H4B,AAAIhG,UAAU,8EAE7C,GAAIiW,GAAuBgI,GACvB,OA36HDjY,EA26H4B,AAAIhG,UAAU,8EAE7C,OAAOqa,GAAqB,IAAI,CAAE4D,EAAaxK,EAAQ,YAAY,CAAEA,EAAQ,YAAY,CAAEA,EAAQ,aAAa,CAAEA,EAAQ,MAAM,CACpI,EAYAwJ,EAAe,SAAS,CAAC,GAAG,CAAG,WAC3B,GAAI,CAACpS,GAAiB,IAAI,EACtB,MAAMgT,GAA4B,OAEtC,IAzoBmB9U,EAAQmV,EAyoBvBC,GAzoBepV,EAyoBc,IAAI,CAxoBzC,AAAI8F,GAA+B9F,EAAO,yBAAyB,EACxDqV,AAwGf,SAA+BrV,CAAM,EACjC,IAMIsV,EACAC,EACAC,EACAC,EACAC,EAVAtd,EAAS2J,EAAmC/B,GAC5C2V,EAAU,GACVC,EAAsB,GACtBC,EAAsB,GACtBC,EAAY,GACZC,EAAY,GAMZC,EAAgB9Y,EAAW,SAAUjC,CAAO,EAC5Cya,EAAuBza,CAC3B,GACA,SAASgb,EAAmBC,CAAU,EAClCrY,EAAcqY,EAAW,cAAc,CAAE,SAAU/L,CAAC,SAChD,AAAI+L,IAAe9d,EACR,MAEXiO,GAAkCmP,EAAQ,yBAAyB,CAAErL,GACrE9D,GAAkCoP,EAAQ,yBAAyB,CAAEtL,GACjE,EAAC2L,GAAa,CAACC,CAAQ,GACvBL,EAAqB9c,KAAAA,GAElB,KACX,EACJ,CACA,SAASud,IACD5L,GAA2BnS,KAC3BoI,EAAmCpI,GAEnC6d,EADA7d,EAAS2J,EAAmC/B,KA6DhD4C,GAAgCxK,EA1Dd,CACd,YAAa,SAAU+C,CAAK,EAIxB+C,EAAgB,WACZ0X,EAAsB,GACtBC,EAAsB,GAEtB,IAAIO,EAASjb,EACb,GAAI,CAAC2a,GAAa,CAACC,EACf,GAAI,CACAK,EAASvR,GAAkB1J,EAC/B,CACA,MAAO4M,EAAQ,CACX1B,GAAkCmP,EAAQ,yBAAyB,CAAEzN,GACrE1B,GAAkCoP,EAAQ,yBAAyB,CAAE1N,GACrE2N,EAAqBnV,GAAqBP,EAAQ+H,IAClD,MACJ,CAEA,CAAC+N,GACD1P,GAAoCoP,EAAQ,yBAAyB,CAd5Dra,GAgBT,CAAC4a,GACD3P,GAAoCqP,EAAQ,yBAAyB,CAAEW,GAE3ET,EAAU,GACNC,EACAS,IAEKR,GACLS,GAER,EACJ,EACA,YAAa,WACTX,EAAU,GACN,CAACG,GACD3P,GAAkCqP,EAAQ,yBAAyB,EAEnE,CAACO,GACD5P,GAAkCsP,EAAQ,yBAAyB,EAEnED,EAAQ,yBAAyB,CAAC,iBAAiB,CAAC,MAAM,CAAG,GAC7D7P,GAAoC6P,EAAQ,yBAAyB,CAAE,GAEvEC,EAAQ,yBAAyB,CAAC,iBAAiB,CAAC,MAAM,CAAG,GAC7D9P,GAAoC8P,EAAQ,yBAAyB,CAAE,GAEvE,EAACK,GAAa,CAACC,CAAQ,GACvBL,EAAqB9c,KAAAA,EAE7B,EACA,YAAa,WACT+c,EAAU,EACd,CACJ,EAEJ,CACA,SAASY,EAAmBrc,CAAI,CAAEsc,CAAU,EACxC,GAAIjU,GAA8BnK,GAC9BoI,EAAmCpI,GAEnC6d,EADA7d,EAl+DD,IAAIiS,GAk+DsCrK,IAG7C,IAAIyW,EAAaD,EAAaf,EAAUD,EACpCkB,EAAcF,EAAahB,EAAUC,EAiEzC7K,GAA6BxS,EAAQ8B,EAAM,EAhErB,CAClB,YAAa,SAAUiB,CAAK,EAIxB+C,EAAgB,WACZ0X,EAAsB,GACtBC,EAAsB,GACtB,IAAIc,EAAeH,EAAaT,EAAYD,EAE5C,GADoBU,EAAaV,EAAYC,EAiBpC,CAACY,GACN/Q,GAA+C6Q,EAAW,yBAAyB,CAAEtb,OAjBrE,CAChB,IAAI2M,EAAc,KAAK,EACvB,GAAI,CACAA,EAAcjD,GAAkB1J,EACpC,CACA,MAAO4M,EAAQ,CACX1B,GAAkCoQ,EAAW,yBAAyB,CAAE1O,GACxE1B,GAAkCqQ,EAAY,yBAAyB,CAAE3O,GACzE2N,EAAqBnV,GAAqBP,EAAQ+H,IAClD,MACJ,CACI,CAAC4O,GACD/Q,GAA+C6Q,EAAW,yBAAyB,CAAEtb,GAEzFiL,GAAoCsQ,EAAY,yBAAyB,CAAE5O,EAC/E,CAIA6N,EAAU,GACNC,EACAS,IAEKR,GACLS,GAER,EACJ,EACA,YAAa,SAAUnb,CAAK,EACxBwa,EAAU,GACV,IAAIgB,EAAeH,EAAaT,EAAYD,EACxCc,EAAgBJ,EAAaV,EAAYC,CACzC,EAACY,GACDxQ,GAAkCsQ,EAAW,yBAAyB,EAEtE,CAACG,GACDzQ,GAAkCuQ,EAAY,yBAAyB,EAE7D9d,KAAAA,IAAVuC,IACI,CAACwb,GACD/Q,GAA+C6Q,EAAW,yBAAyB,CAAEtb,GAErF,CAACyb,GAAiBF,EAAY,yBAAyB,CAAC,iBAAiB,CAAC,MAAM,CAAG,GACnF/Q,GAAoC+Q,EAAY,yBAAyB,CAAE,IAG/E,EAACC,GAAgB,CAACC,CAAY,GAC9BlB,EAAqB9c,KAAAA,EAE7B,EACA,YAAa,WACT+c,EAAU,EACd,CACJ,EAEJ,CACA,SAASU,IACL,GAAIV,EAEA,OADAC,EAAsB,GACfxY,EAAoBxE,KAAAA,GAE/B+c,EAAU,GACV,IAAItb,EAAc2L,GAA2CwP,EAAQ,yBAAyB,EAO9F,OANInb,AAAgB,OAAhBA,EACA8b,IAGAI,EAAmBlc,EAAY,KAAK,CAAE,IAEnC+C,EAAoBxE,KAAAA,EAC/B,CACA,SAAS0d,IACL,GAAIX,EAEA,OADAE,EAAsB,GACfzY,EAAoBxE,KAAAA,GAE/B+c,EAAU,GACV,IAAItb,EAAc2L,GAA2CyP,EAAQ,yBAAyB,EAO9F,OANIpb,AAAgB,OAAhBA,EACA8b,IAGAI,EAAmBlc,EAAY,KAAK,CAAE,IAEnC+C,EAAoBxE,KAAAA,EAC/B,CAqBA,SAASmR,IAET,CAIA,OAHAyL,EAAUqB,GAAyB9M,EAAgBsM,EAvBnD,SAA0B/c,CAAM,EAG5B,GAFAwc,EAAY,GACZR,EAAUhc,EACNyc,EAAW,CAEX,IAAIe,EAAevW,GAAqBP,EADlBwD,GAAoB,CAAC8R,EAASC,EAAQ,GAE5DG,EAAqBoB,EACzB,CACA,OAAOd,CACX,GAeAP,EAAUoB,GAAyB9M,EAAgBuM,EAdnD,SAA0Bhd,CAAM,EAG5B,GAFAyc,EAAY,GACZR,EAAUjc,EACNwc,EAAW,CAEX,IAAIgB,EAAevW,GAAqBP,EADlBwD,GAAoB,CAAC8R,EAASC,EAAQ,GAE5DG,EAAqBoB,EACzB,CACA,OAAOd,CACX,GAMAC,EAAmB7d,GACZ,CAACod,EAASC,EAAQ,AAC7B,EAzUqCzV,GAE1B+W,AAEX,SAAkC/W,CAAM,CAAEmV,CAAe,EACrD,IAKIG,EACAC,EACAC,EACAC,EACAC,EATAtd,EAAS2J,EAAmC/B,GAC5C2V,EAAU,GACVqB,EAAY,GACZlB,EAAY,GACZC,EAAY,GAMZC,EAAgB9Y,EAAW,SAAUjC,CAAO,EAC5Cya,EAAuBza,CAC3B,GACA,SAAS+O,WACL,AAAI2L,GACAqB,EAAY,GACL5Z,EAAoBxE,KAAAA,KAE/B+c,EAAU,GA2CV/S,GAAgCxK,EA1Cd,CACd,YAAa,SAAU+C,CAAK,EAIxB+C,EAAgB,WACZ8Y,EAAY,EAQR,EAAClB,GACDtC,GAAuCgC,EAAQ,yBAAyB,CAR/Dra,GAUT,CAAC4a,GACDvC,GAAuCiC,EAAQ,yBAAyB,CAV/Dta,GAYbwa,EAAU,GACNqB,GACAhN,GAER,EACJ,EACA,YAAa,WACT2L,EAAU,GACN,CAACG,GACDvC,GAAqCiC,EAAQ,yBAAyB,EAEtE,CAACO,GACDxC,GAAqCkC,EAAQ,yBAAyB,EAEtE,EAACK,GAAa,CAACC,CAAQ,GACvBL,EAAqB9c,KAAAA,EAE7B,EACA,YAAa,WACT+c,EAAU,EACd,CACJ,GAEOvY,EAAoBxE,KAAAA,GAC/B,CAqBA,SAASmR,IAET,CAWA,OAVAyL,EAAUyB,GAAqBlN,EAAgBC,EAvB/C,SAA0B1Q,CAAM,EAG5B,GAFAwc,EAAY,GACZR,EAAUhc,EACNyc,EAAW,CAEX,IAAIe,EAAevW,GAAqBP,EADlBwD,GAAoB,CAAC8R,EAASC,EAAQ,GAE5DG,EAAqBoB,EACzB,CACA,OAAOd,CACX,GAeAP,EAAUwB,GAAqBlN,EAAgBC,EAd/C,SAA0B1Q,CAAM,EAG5B,GAFAyc,EAAY,GACZR,EAAUjc,EACNwc,EAAW,CAEX,IAAIgB,EAAevW,GAAqBP,EADlBwD,GAAoB,CAAC8R,EAASC,EAAQ,GAE5DG,EAAqBoB,EACzB,CACA,OAAOd,CACX,GAMAnY,EAAczF,EAAO,cAAc,CAAE,SAAU+R,CAAC,EAM5C,OALAsJ,GAAqC+B,EAAQ,yBAAyB,CAAErL,GACxEsJ,GAAqCgC,EAAQ,yBAAyB,CAAEtL,GACpE,EAAC2L,GAAa,CAACC,CAAQ,GACvBL,EAAqB9c,KAAAA,GAElB,IACX,GACO,CAAC4c,EAASC,EAAQ,AAC7B,EArGoCzV,IAsoB5B,OAAOwD,GAAoB4R,EAC/B,EACAlB,EAAe,SAAS,CAAC,MAAM,CAAG,SAAUzJ,CAAU,EAElD,GADmB,KAAK,IAApBA,GAAyBA,CAAAA,EAAa7R,KAAAA,CAAQ,EAC9C,CAACkJ,GAAiB,IAAI,EACtB,MAAMgT,GAA4B,UAEtC,IAjLwBpK,EAASxJ,EAtrGGlB,EAAQiD,EAE5CiU,EACAC,EAo2GIzM,GAjL6BxJ,EAiLgB,kBAhLrDF,EAD4B0J,EAiLaD,EAhLfvJ,GAEnB,CAAE,cAAe+S,CAAAA,CADJvJ,CAAAA,MAAAA,EAAyC,KAAK,EAAIA,EAAQ,aAAa,AAAD,CAC3C,GA+K3C,OAx2GoC1K,EAw2GM,IAAI,CAx2GFiD,EAw2GIyH,EAAQ,aAAa,CAt2GrEwM,EAAO,IAAIlU,GADFjB,EAAmC/B,GACOiD,GAEvDkU,CADIA,EAAWtgB,OAAO,MAAM,CAACuM,KACpB,kBAAkB,CAAG8T,EACvBC,CAo2GP,EAOAjD,EAAe,IAAI,CAAG,SAAUkD,CAAa,MAzUrBve,EAJEmH,EA8UtB,OAzUJ,AAJOzI,EADmByI,EAIFnH,EA0UMue,IA7UC,AAA4B,SAArBpX,EAAO,SAAS,CAK3CqX,AA6Df,SAAyCjf,CAAM,EAE3C,IADI4H,EAgCJ,OADAA,EAASiX,GA9BY3f,EACrB,eACQqC,EAtrH+CqE,EAurHnD,GAAI,CACArE,EAAcvB,EAAO,IAAI,EAC7B,CACA,MAAOR,EAAG,CACN,OA9sHDqF,EA8sH4BrF,EAC/B,CACA,OA5rHG2F,EA4rHyB5D,EAAa,SAAU2d,CAAU,EACzD,GAAI,CAAC/f,EAAa+f,GACd,MAAM,AAAIrgB,UAAU,gFAExB,GAAIqgB,EAAW,IAAI,CACf/D,GAAqCvT,EAAO,yBAAyB,MAEpE,CACD,IAAI3C,EAAQia,EAAW,KAAK,CAC5B9D,GAAuCxT,EAAO,yBAAyB,CAAE3C,EAC7E,CACJ,EAxsHmDW,KAAAA,EAysHvD,EACA,SAAyB1E,CAAM,EAC3B,GAAI,CACA,OAAO8D,EAAoBhF,EAAO,MAAM,CAACkB,GAC7C,CACA,MAAO1B,EAAG,CACN,OAluHDqF,EAkuH4BrF,EAC/B,CACJ,EAC8E,EAElF,EA/F+CiB,EAAO,SAAS,IAEpD0e,AAEX,SAAoCH,CAAa,EAE7C,IADIpX,EACAwX,EAAiBC,AA/7FzB,SAASA,EAAYxW,CAAG,CAAEyW,CAAI,CAAEC,CAAM,EAElC,GADa,KAAK,IAAdD,GAAmBA,CAAAA,EAAO,MAAK,EAC/BC,AAAW/e,KAAAA,IAAX+e,GACA,GAAID,AAAS,UAATA,EAEA,IAAIC,AAAW/e,KAAAA,IADf+e,CAAAA,EAASlT,GAAUxD,EAAKhF,EAAe,aAAa,GAC1B,CACtB,IA/BqB2b,EAG7Bvf,EAEAwf,EAIAC,EAaAC,EASYC,EAAavT,GAAUxD,EAAKhF,EAAe,QAAQ,EAEvD,OAjCqB2b,EAgCIH,EAAYxW,EAAK,OAAQ+W,GA3B1DH,EAAgBxf,CAAAA,AAChBA,CADgBA,EAAK,CAAC,EACpB,CAAC4D,EAAe,QAAQ,CAAC,CAAG,WAAc,OAAO2b,EAAmB,QAAQ,AAAE,EAChFvf,CAAC,EAeD0f,EAAaD,CAbbA,EAAiB,WACjB,OAAOG,AAjwBf,SAA0BC,CAAO,CAAEC,CAAU,CAAEC,CAAS,EACpD,GAAI,CAAClc,OAAO,aAAa,CAAE,MAAM,AAAIjF,UAAU,wCAC/C,IAAoDuF,EAAhD6b,EAAID,EAAU,KAAK,CAACF,EAASC,GAAc,EAAE,EAAMG,EAAI,EAAE,CAC7D,OAAO9b,EAAI,CAAC,EAAG+b,EAAK,QAASA,EAAK,SAAUA,EAAK,UAAW/b,CAAC,CAACN,OAAO,aAAa,CAAC,CAAG,WAAc,OAAO,IAAI,AAAE,EAAGM,EACpH,SAAS+b,EAAKzU,CAAC,EAAQuU,CAAC,CAACvU,EAAE,EAAEtH,CAAAA,CAAC,CAACsH,EAAE,CAAG,SAAUpH,CAAC,EAAI,OAAO,IAAI1B,QAAQ,SAAUwd,CAAC,CAAE5hB,CAAC,EAAI0hB,EAAE,IAAI,CAAC,CAACxU,EAAGpH,EAAG8b,EAAG5hB,EAAE,EAAI,GAAK6hB,EAAO3U,EAAGpH,EAAI,EAAI,EAAG,CACzI,SAAS+b,EAAO3U,CAAC,CAAEpH,CAAC,EAAI,GAAI,CAAEgc,AAC9B,UAAcvO,CAAC,EAAIA,EAAE,KAAK,YAAY1N,EAAUzB,QAAQ,OAAO,CAACmP,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAACwO,EAASzd,GAAU0d,EAAON,CAAC,CAAC,EAAE,CAAC,EAAE,CAAEnO,EAAI,GADpFkO,CAAC,CAACvU,EAAE,CAACpH,GAAK,CAAE,MAAO9E,EAAG,CAAEghB,EAAON,CAAC,CAAC,EAAE,CAAC,EAAE,CAAE1gB,EAAI,CAAE,CAEjF,SAAS+gB,EAAQtb,CAAK,EAAIob,EAAO,OAAQpb,EAAQ,CACjD,SAASnC,EAAOmC,CAAK,EAAIob,EAAO,QAASpb,EAAQ,CACjD,SAASub,EAAOC,CAAC,CAAEnc,CAAC,EAAQmc,EAAEnc,GAAI4b,EAAE,KAAK,GAAbO,AAAiBP,EAAE,MAAM,EAAEG,EAAOH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAEA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAG,CACrF,EAuvBgC,IAAI,CAAEte,UAAW,WACrC,OAAO8e,AA9yBnB,SAAqBZ,CAAO,CAAEa,CAAI,EAC9B,IAAsGF,EAAGG,EAAGC,EAAGZ,EAA3Ga,EAAI,CAAE,MAAO,EAAG,KAAM,WAAa,GAAID,AAAO,EAAPA,CAAC,CAAC,EAAE,CAAM,MAAMA,CAAC,CAAC,EAAE,CAAE,OAAOA,CAAC,CAAC,EAAE,AAAE,EAAG,KAAM,EAAE,CAAE,IAAK,EAAE,AAAC,EACnG,OAAOZ,EAAI,CAAE,KAAME,EAAK,GAAI,MAASA,EAAK,GAAI,OAAUA,EAAK,EAAG,EAAG,AAAkB,YAAlB,OAAOrc,QAA0Bmc,CAAAA,CAAC,CAACnc,OAAO,QAAQ,CAAC,CAAG,WAAa,OAAO,IAAI,AAAE,GAAImc,EACvJ,SAASE,EAAKzU,CAAC,EAAI,OAAO,SAAUpH,CAAC,EAAI,OAAOgc,AAChD,SAAcS,CAAE,EACZ,GAAIN,EAAG,MAAM,AAAI5hB,UAAU,mCAC3B,KAAOohB,GAAMA,CAAAA,EAAI,EAAGc,CAAE,CAAC,EAAE,EAAKD,CAAAA,EAAI,EAAC,EAAIA,GAAG,GAAI,CAC1C,GAAIL,EAAI,EAAGG,GAAMC,CAAAA,EAAIE,AAAQ,EAARA,CAAE,CAAC,EAAE,CAAOH,EAAE,MAAS,CAAGG,CAAE,CAAC,EAAE,CAAGH,EAAE,KAAQ,EAAK,CAACC,CAAAA,EAAID,EAAE,MAAS,AAAD,GAAMC,EAAE,IAAI,CAACD,GAAI,GAAKA,EAAE,IAAI,AAAD,GAAM,CAAC,AAACC,CAAAA,EAAIA,EAAE,IAAI,CAACD,EAAGG,CAAE,CAAC,EAAE,GAAG,IAAI,CAAE,OAAOF,EAE3J,OADID,EAAI,EAAJA,AAAOC,GAAGE,CAAAA,EAAK,CAACA,AAAQ,EAARA,CAAE,CAAC,EAAE,CAAMF,EAAE,KAAK,CAAC,AAAD,EAC9BE,CAAE,CAAC,EAAE,EACT,KAAK,EAAG,KAAK,EAAGF,EAAIE,EAAI,KACxB,MAAK,EAAc,OAAXD,EAAE,KAAK,GAAW,CAAE,MAAOC,CAAE,CAAC,EAAE,CAAE,KAAM,EAAM,CACtD,MAAK,EAAGD,EAAE,KAAK,GAAIF,EAAIG,CAAE,CAAC,EAAE,CAAEA,EAAK,CAAC,EAAE,CAAE,QACxC,MAAK,EAAGA,EAAKD,EAAE,GAAG,CAAC,GAAG,GAAIA,EAAE,IAAI,CAAC,GAAG,GAAI,QACxC,SACI,GAAI,CAAcD,CAAAA,EAAIA,AAAhBA,CAAAA,EAAIC,EAAE,IAAI,AAAD,EAAS,MAAM,CAAG,GAAKD,CAAC,CAACA,EAAE,MAAM,CAAG,EAAE,AAAD,GAAOE,CAAAA,AAAU,IAAVA,CAAE,CAAC,EAAE,EAAUA,AAAU,IAAVA,CAAE,CAAC,EAAE,AAAK,EAAI,CAAED,EAAI,EAAG,QAAU,CAC3G,GAAIC,AAAU,IAAVA,CAAE,CAAC,EAAE,EAAW,EAACF,GAAME,CAAE,CAAC,EAAE,CAAGF,CAAC,CAAC,EAAE,EAAIE,CAAE,CAAC,EAAE,CAAGF,CAAC,CAAC,EAAE,EAAI,CAAEC,EAAE,KAAK,CAAGC,CAAE,CAAC,EAAE,CAAE,KAAO,CACrF,GAAIA,AAAU,IAAVA,CAAE,CAAC,EAAE,EAAUD,EAAE,KAAK,CAAGD,CAAC,CAAC,EAAE,CAAE,CAAEC,EAAE,KAAK,CAAGD,CAAC,CAAC,EAAE,CAAEA,EAAIE,EAAI,KAAO,CACpE,GAAIF,GAAKC,EAAE,KAAK,CAAGD,CAAC,CAAC,EAAE,CAAE,CAAEC,EAAE,KAAK,CAAGD,CAAC,CAAC,EAAE,CAAEC,EAAE,GAAG,CAAC,IAAI,CAACC,GAAK,KAAO,CAC9DF,CAAC,CAAC,EAAE,EAAEC,EAAE,GAAG,CAAC,GAAG,GACnBA,EAAE,IAAI,CAAC,GAAG,GAAI,QACtB,CACAC,EAAKJ,EAAK,IAAI,CAACb,EAASgB,EAC5B,CAAE,MAAOthB,EAAG,CAAEuhB,EAAK,CAAC,EAAGvhB,EAAE,CAAEohB,EAAI,CAAG,QAAU,CAAEH,EAAII,EAAI,CAAG,CACzD,GAAIE,AAAQ,EAARA,CAAE,CAAC,EAAE,CAAM,MAAMA,CAAE,CAAC,EAAE,CAAE,MAAO,CAAE,MAAOA,CAAE,CAAC,EAAE,CAAGA,CAAE,CAAC,EAAE,CAAG,KAAK,EAAG,KAAM,EAAK,CACnF,EAtBqD,CAACrV,EAAGpH,EAAE,CAAG,CAAG,CAuBrE,EAoxB+B,IAAI,CAAE,SAAUrE,CAAE,EACjC,OAAQA,EAAG,KAAK,EACZ,KAAK,EAAG,MAAO,CAAC,EAAc+D,EAASgd,AAxvB3D,SAA0B/c,CAAC,EACvB,IAAIG,EAAGzF,EACP,OAAOyF,EAAI,CAAC,EAAG+b,EAAK,QAASA,EAAK,QAAS,SAAU3gB,CAAC,EAAI,MAAMA,CAAG,GAAI2gB,EAAK,UAAW/b,CAAC,CAACN,OAAO,QAAQ,CAAC,CAAG,WAAc,OAAO,IAAI,AAAE,EAAGM,EAC1I,SAAS+b,EAAKzU,CAAC,CAAE+U,CAAC,EAAIrc,CAAC,CAACsH,EAAE,CAAGzH,CAAC,CAACyH,EAAE,CAAG,SAAUpH,CAAC,EAAI,MAAO,AAAC3F,CAAAA,EAAI,CAACA,CAAAA,EAAK,CAAE,MAAO0F,EAAQJ,CAAC,CAACyH,EAAE,CAACpH,IAAK,KAAM,EAAM,EAAImc,EAAIA,EAAEnc,GAAKA,CAAG,EAAImc,CAAG,CACzI,EAovB4EQ,AAlvB5E,SAAuBhd,CAAC,EACpB,GAAI,CAACH,OAAO,aAAa,CAAE,MAAM,AAAIjF,UAAU,wCAC/C,IAAiCuF,EAA7BD,EAAIF,CAAC,CAACH,OAAO,aAAa,CAAC,CAC/B,OAAOK,EAAIA,EAAE,IAAI,CAACF,GAAMA,CAAAA,EAAqCD,EAASC,GAA2BG,EAAI,CAAC,EAAG+b,EAAK,QAASA,EAAK,SAAUA,EAAK,UAAW/b,CAAC,CAACN,OAAO,aAAa,CAAC,CAAG,WAAc,OAAO,IAAI,AAAE,EAAGM,CAAAA,EAC9M,SAAS+b,EAAKzU,CAAC,EAAItH,CAAC,CAACsH,EAAE,CAAGzH,CAAC,CAACyH,EAAE,EAAI,SAAUpH,CAAC,EAAI,OAAO,IAAI1B,QAAQ,SAAUC,CAAO,CAAEC,CAAM,EAAIwB,AACjG,UAAgBzB,CAAO,CAAEC,CAAM,CAAEvE,CAAC,CAAE+F,CAAC,EAAI1B,QAAQ,OAAO,CAAC0B,GAAG,IAAI,CAAC,SAASA,CAAC,EAAIzB,EAAQ,CAAE,MAAOyB,EAAG,KAAM/F,CAAE,EAAI,EAAGuE,EAAS,GADND,EAASC,EAAQwB,AAArCA,CAAAA,EAAIL,CAAC,CAACyH,EAAE,CAACpH,EAAC,EAA6B,IAAI,CAAEA,EAAE,KAAK,CAAG,EAAI,CAAG,CAEnK,EA4uB0Fmb,KAAgB,AACtF,MAAK,EACL,KAAK,EADG,MAAO,CAAC,EAAapb,EAAQ,KAAK,CAAC,KAAK,EAAG,CAACpE,EAAG,IAAI,GAAG,EAAE,AAEhE,MAAK,EAAG,MAAO,CAAC,EAAcA,EAAG,IAAI,GAAG,AAC5C,CACJ,EACJ,EACJ,KAE+B,IAAI,CAC5B,CAAE,SAAUyf,EAAe,WAAYC,EAAY,KAAM,EAAM,CAW9D,OAGAJ,EAASlT,GAAUxD,EAAKhF,EAAe,QAAQ,EAGvD,GAAI0b,AAAW/e,KAAAA,IAAX+e,EACA,MAAM,AAAI1gB,UAAU,8BAExB,IAAIkgB,EAAW5Y,EAAYoZ,EAAQ1W,EAAK,EAAE,EAC1C,GAAI,CAAC1J,EAAa4f,GACd,MAAM,AAAIlgB,UAAU,6CAExB,IAAI8gB,EAAaZ,EAAS,IAAI,CAC9B,MAAO,CAAE,SAAUA,EAAU,WAAYY,EAAY,KAAM,EAAM,CACrE,EAu6FqCX,EAAe,SAqDhD,OADApX,EAASiX,GAnDY3f,EACrB,eACQgiB,EA9nH+Ctb,EA+nHnD,GAAI,CACAsb,EAAaC,AA36FzB,SAAsB/B,CAAc,EAChC,IAAI9d,EAAS6E,EAAYiZ,EAAe,UAAU,CAAEA,EAAe,QAAQ,CAAE,EAAE,EAC/E,GAAI,CAACjgB,EAAamC,GACd,MAAM,AAAIzC,UAAU,oDAExB,OAAOyC,CACX,EAq6FsC8d,EAC9B,CACA,MAAO5f,EAAG,CACN,OAtpHDqF,EAspH4BrF,EAC/B,CAEA,OAroHG2F,EAooHeH,EAAoBkc,GACG,SAAUE,CAAU,EACzD,GAAI,CAACjiB,EAAaiiB,GACd,MAAM,AAAIviB,UAAU,kFAGxB,GA96FOuiB,AA66FqBA,EA76FV,IAAI,CA+6FlBjG,GAAqCvT,EAAO,yBAAyB,MAEpE,CACD,IAAI3C,EA/6FTmc,AA+6F+BA,EA/6FpB,KAAK,CAg7FXhG,GAAuCxT,EAAO,yBAAyB,CAAE3C,EAC7E,CACJ,EAlpHmDW,KAAAA,EAmpHvD,EACA,SAAyB1E,CAAM,EAC3B,IArpHmD0E,EAspH/Cyb,EAUAC,EAXAvC,EAAWK,EAAe,QAAQ,CAEtC,GAAI,CACAiC,EAAehV,GAAU0S,EAAU,SACvC,CACA,MAAOvf,EAAG,CACN,OA9qHDqF,EA8qH4BrF,EAC/B,CACA,GAAI6hB,AAAiB7gB,KAAAA,IAAjB6gB,EACA,OAAOrc,EAAoBxE,KAAAA,GAG/B,GAAI,CACA8gB,EAAenb,EAAYkb,EAActC,EAAU,CAAC7d,EAAO,CAC/D,CACA,MAAO1B,EAAG,CACN,OAxrHDqF,EAwrH4BrF,EAC/B,CAEA,OAvqHG2F,EAsqHiBH,EAAoBsc,GACG,SAAUF,CAAU,EAC3D,GAAI,CAACjiB,EAAaiiB,GACd,MAAM,AAAIviB,UAAU,mFAG5B,EA7qHmD+G,KAAAA,EA8qHvD,EAC8E,EAElF,EA1DsCnF,EAuUlC,EACOqb,CACX,IAmCA,SAAS+C,GAAqBlN,CAAc,CAAEC,CAAa,CAAEC,CAAe,CAAEC,CAAa,CAAEwC,CAAa,EAChF,KAAK,IAAvBxC,GAA4BA,CAAAA,EAAgB,GAC1B,KAAK,IAAvBwC,GAA4BA,CAAAA,EAAgB,WAAc,OAAO,CAAG,GACxE,IAAI1M,EAASnJ,OAAO,MAAM,CAACqd,GAAe,SAAS,EAInD,OAHAS,GAAyB3U,GAEzB6T,GAAqC7T,EADpBnJ,OAAO,MAAM,CAACqc,GAAgC,SAAS,EACfnJ,EAAgBC,EAAeC,EAAiBC,EAAewC,GACjH1M,CACX,CAEA,SAAS6W,GAAyB9M,CAAc,CAAEC,CAAa,CAAEC,CAAe,EAC5E,IAAIjK,EAASnJ,OAAO,MAAM,CAACqd,GAAe,SAAS,EAInD,OAHAS,GAAyB3U,GAEzB8J,GAAkC9J,EADjBnJ,OAAO,MAAM,CAACgP,GAA6B,SAAS,EACfkE,EAAgBC,EAAeC,EAAiB,EAAGrR,KAAAA,GAClGoH,CACX,CACA,SAAS2U,GAAyB3U,CAAM,EACpCA,EAAO,MAAM,CAAG,WAChBA,EAAO,OAAO,CAAGpH,KAAAA,EACjBoH,EAAO,YAAY,CAAGpH,KAAAA,EACtBoH,EAAO,UAAU,CAAG,EACxB,CACA,SAAS8B,GAAiBtK,CAAC,QACvB,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,+BAGtCA,aAAa0c,EACxB,CACA,SAAS1R,GAAuBxC,CAAM,SAClC,AAAuBpH,KAAAA,IAAnBoH,EAAO,OAAO,EAGX,EACX,CAEA,SAASO,GAAqBP,CAAM,CAAE1G,CAAM,EAExC,GADA0G,EAAO,UAAU,CAAG,GAChBA,AAAkB,WAAlBA,EAAO,MAAM,CACb,OAAO5C,EAAoBxE,KAAAA,GAE/B,GAAIoH,AAAkB,YAAlBA,EAAO,MAAM,CACb,OAniIG/C,EAmiIwB+C,EAAO,YAAY,EAElDgJ,GAAoBhJ,GACpB,IAnhIuDhC,EAmhInD5F,EAAS4H,EAAO,OAAO,CAC3B,GAAI5H,AAAWQ,KAAAA,IAAXR,GAAwBmS,GAA2BnS,GAAS,CAC5D,IAAI6S,EAAmB7S,EAAO,iBAAiB,AAC/CA,CAAAA,EAAO,iBAAiB,CAAG,IAAIyG,EAC/BoM,EAAiB,OAAO,CAAC,SAAU1D,CAAe,EAC9CA,EAAgB,WAAW,CAAC3O,KAAAA,EAChC,EACJ,CAEA,OA3hIO2E,EA0hImByC,EAAO,yBAAyB,CAACJ,EAAY,CAACtG,GACvBhC,EA5hIM0G,KAAAA,EA6hI3D,CACA,SAASgL,GAAoBhJ,CAAM,EAC/BA,EAAO,MAAM,CAAG,SAChB,IAAI5H,EAAS4H,EAAO,OAAO,CAC3B,GAAI5H,AAAWQ,KAAAA,IAAXR,EAIJ,IADA+H,EAAkC/H,GAC9BmK,GAA8BnK,GAAS,CACvC,IAAI2K,EAAe3K,EAAO,aAAa,AACvCA,CAAAA,EAAO,aAAa,CAAG,IAAIyG,EAC3BkE,EAAa,OAAO,CAAC,SAAUb,CAAW,EACtCA,EAAY,WAAW,EAC3B,EACJ,EACJ,CACA,SAASuH,GAAoBzJ,CAAM,CAAEpI,CAAC,EAClCoI,EAAO,MAAM,CAAG,UAChBA,EAAO,YAAY,CAAGpI,EACtB,IAAIQ,EAAS4H,EAAO,OAAO,CAC3B,GAAI5H,AAAWQ,KAAAA,IAAXR,EAGJiI,EAAiCjI,EAAQR,GACrC2K,GAA8BnK,GAC9B0K,GAA6C1K,EAAQR,GAGrDkT,GAA8C1S,EAAQR,EAE9D,CAEA,SAASkd,GAA4BhY,CAAI,EACrC,OAAO,AAAI7F,UAAU,4BAA4B,MAAM,CAAC6F,EAAM,yCAClE,CAEA,SAAS6c,GAA2BpO,CAAI,CAAErK,CAAO,EAC7CF,EAAiBuK,EAAMrK,GACvB,IAAIgJ,EAAgBqB,MAAAA,EAAmC,KAAK,EAAIA,EAAK,aAAa,CAElF,OADAhK,EAAoB2I,EAAe,gBAAiB,uBAC7C,CACH,cAAezI,EAA0ByI,EAC7C,CACJ,CAvIArT,OAAO,gBAAgB,CAACqd,GAAgB,CACpC,KAAM,CAAE,WAAY,EAAK,CAC7B,GACArd,OAAO,gBAAgB,CAACqd,GAAe,SAAS,CAAE,CAC9C,OAAQ,CAAE,WAAY,EAAK,EAC3B,UAAW,CAAE,WAAY,EAAK,EAC9B,YAAa,CAAE,WAAY,EAAK,EAChC,OAAQ,CAAE,WAAY,EAAK,EAC3B,IAAK,CAAE,WAAY,EAAK,EACxB,OAAQ,CAAE,WAAY,EAAK,EAC3B,OAAQ,CAAE,WAAY,EAAK,CAC/B,GACAtX,EAAgBsX,GAAe,IAAI,CAAE,QACrCtX,EAAgBsX,GAAe,SAAS,CAAC,MAAM,CAAE,UACjDtX,EAAgBsX,GAAe,SAAS,CAAC,SAAS,CAAE,aACpDtX,EAAgBsX,GAAe,SAAS,CAAC,WAAW,CAAE,eACtDtX,EAAgBsX,GAAe,SAAS,CAAC,MAAM,CAAE,UACjDtX,EAAgBsX,GAAe,SAAS,CAAC,GAAG,CAAE,OAC9CtX,EAAgBsX,GAAe,SAAS,CAAC,MAAM,CAAE,UACP,UAAtC,OAAOjY,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAACqd,GAAe,SAAS,CAAEjY,EAAe,WAAW,CAAE,CACxE,MAAO,iBACP,aAAc,EAClB,GAEwC,UAAxC,OAAOA,EAAe,aAAa,EACnCpF,OAAO,cAAc,CAACqd,GAAe,SAAS,CAAEjY,EAAe,aAAa,CAAE,CAC1E,MAAOiY,GAAe,SAAS,CAAC,MAAM,CACtC,SAAU,GACV,aAAc,EAClB,GA4GJ,IAAI0F,GAAyB,SAAUze,CAAK,EACxC,OAAOA,EAAM,UAAU,AAC3B,EACAyB,EAAgBgd,GAAwB,QAMxC,IAAIC,GAA2C,WAC3C,SAASA,EAA0BnP,CAAO,EACtCrJ,EAAuBqJ,EAAS,EAAG,6BACnCA,EAAUiP,GAA2BjP,EAAS,mBAC9C,IAAI,CAAC,uCAAuC,CAAGA,EAAQ,aAAa,AACxE,CA2BA,OA1BA7T,OAAO,cAAc,CAACgjB,EAA0B,SAAS,CAAE,gBAAiB,CAIxE,IAAK,WACD,GAAI,CAACC,GAA4B,IAAI,EACjC,MAAMC,GAA8B,iBAExC,OAAO,IAAI,CAAC,uCAAuC,AACvD,EACA,WAAY,GACZ,aAAc,EAClB,GACAljB,OAAO,cAAc,CAACgjB,EAA0B,SAAS,CAAE,OAAQ,CAI/D,IAAK,WACD,GAAI,CAACC,GAA4B,IAAI,EACjC,MAAMC,GAA8B,QAExC,OAAOH,EACX,EACA,WAAY,GACZ,aAAc,EAClB,GACOC,CACX,IAYA,SAASE,GAA8Bjd,CAAI,EACvC,OAAO,AAAI7F,UAAU,uCAAuC,MAAM,CAAC6F,EAAM,oDAC7E,CACA,SAASgd,GAA4BtiB,CAAC,QAClC,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,6CAGtCA,aAAaqiB,EACxB,CAtBAhjB,OAAO,gBAAgB,CAACgjB,GAA0B,SAAS,CAAE,CACzD,cAAe,CAAE,WAAY,EAAK,EAClC,KAAM,CAAE,WAAY,EAAK,CAC7B,GAC0C,UAAtC,OAAO5d,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAACgjB,GAA0B,SAAS,CAAE5d,EAAe,WAAW,CAAE,CACnF,MAAO,4BACP,aAAc,EAClB,GAiBJ,IAAI+d,GAAoB,WACpB,OAAO,CACX,EACApd,EAAgBod,GAAmB,QAMnC,IAAIC,GAAsC,WACtC,SAASA,EAAqBvP,CAAO,EACjCrJ,EAAuBqJ,EAAS,EAAG,wBACnCA,EAAUiP,GAA2BjP,EAAS,mBAC9C,IAAI,CAAC,kCAAkC,CAAGA,EAAQ,aAAa,AACnE,CA4BA,OA3BA7T,OAAO,cAAc,CAACojB,EAAqB,SAAS,CAAE,gBAAiB,CAInE,IAAK,WACD,GAAI,CAACC,GAAuB,IAAI,EAC5B,MAAMC,GAAyB,iBAEnC,OAAO,IAAI,CAAC,kCAAkC,AAClD,EACA,WAAY,GACZ,aAAc,EAClB,GACAtjB,OAAO,cAAc,CAACojB,EAAqB,SAAS,CAAE,OAAQ,CAK1D,IAAK,WACD,GAAI,CAACC,GAAuB,IAAI,EAC5B,MAAMC,GAAyB,QAEnC,OAAOH,EACX,EACA,WAAY,GACZ,aAAc,EAClB,GACOC,CACX,IAYA,SAASE,GAAyBrd,CAAI,EAClC,OAAO,AAAI7F,UAAU,kCAAkC,MAAM,CAAC6F,EAAM,+CACxE,CACA,SAASod,GAAuB1iB,CAAC,QAC7B,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,wCAGtCA,aAAayiB,EACxB,CAtBApjB,OAAO,gBAAgB,CAACojB,GAAqB,SAAS,CAAE,CACpD,cAAe,CAAE,WAAY,EAAK,EAClC,KAAM,CAAE,WAAY,EAAK,CAC7B,GAC0C,UAAtC,OAAOhe,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAACojB,GAAqB,SAAS,CAAEhe,EAAe,WAAW,CAAE,CAC9E,MAAO,uBACP,aAAc,EAClB,GAmEJ,IAAIme,GAAiC,WACjC,SAASA,EAAgBC,CAAc,CAAEC,CAAmB,CAAEC,CAAmB,EACtD,KAAK,IAAxBF,GAA6BA,CAAAA,EAAiB,CAAC,GACvB,KAAK,IAA7BC,GAAkCA,CAAAA,EAAsB,CAAC,GACjC,KAAK,IAA7BC,GAAkCA,CAAAA,EAAsB,CAAC,GACtC3hB,KAAAA,IAAnByhB,GACAA,CAAAA,EAAiB,IAAG,EAExB,IA3DoBrO,EAAU9K,EAE9BmT,EACAmG,EACAC,EACAtO,EACAjU,EACAwiB,EAiEIC,EAbAC,EAAmBtP,GAAuBgP,EAAqB,oBAC/DO,EAAmBvP,GAAuBiP,EAAqB,mBACnE,IAAIO,GA7D0B5Z,EA6DuB,kBA5DzDF,EADwBgL,EA6DiBqO,EA5DdnZ,GACvBmT,EAASrI,MAAAA,EAA2C,KAAK,EAAIA,EAAS,MAAM,CAC5EwO,EAAQxO,MAAAA,EAA2C,KAAK,EAAIA,EAAS,KAAK,CAC1EyO,EAAezO,MAAAA,EAA2C,KAAK,EAAIA,EAAS,YAAY,CACxFG,EAAQH,MAAAA,EAA2C,KAAK,EAAIA,EAAS,KAAK,CAC1E9T,EAAY8T,MAAAA,EAA2C,KAAK,EAAIA,EAAS,SAAS,CAClF0O,EAAe1O,MAAAA,EAA2C,KAAK,EAAIA,EAAS,YAAY,CACrF,CACH,OAAQqI,AAAWzb,KAAAA,IAAXyb,EACJzb,KAAAA,EACAmiB,AA0BZ,SAA0Cle,CAAE,CAAEmP,CAAQ,CAAE9K,CAAO,EAE3D,OADAC,EAAetE,EAAIqE,GACZ,SAAU5H,CAAM,EAAI,OAAOsF,EAAY/B,EAAImP,EAAU,CAAC1S,EAAO,CAAG,CAC3E,EA7B6C+a,EAAQrI,EAAU,GAAG,MAAM,CAAC9K,EAAS,8BAC1E,MAAOsZ,AAAU5hB,KAAAA,IAAV4hB,EACH5hB,KAAAA,EACAoiB,AAWZ,SAAyCne,CAAE,CAAEmP,CAAQ,CAAE9K,CAAO,EAE1D,OADAC,EAAetE,EAAIqE,GACZ,SAAU7H,CAAU,EAAI,OAAOuF,EAAY/B,EAAImP,EAAU,CAAC3S,EAAW,CAAG,CACnF,EAd4CmhB,EAAOxO,EAAU,GAAG,MAAM,CAAC9K,EAAS,6BACxE,aAAcuZ,EACd,MAAOtO,AAAUvT,KAAAA,IAAVuT,EACHvT,KAAAA,EACAqiB,AAWZ,SAAyCpe,CAAE,CAAEmP,CAAQ,CAAE9K,CAAO,EAE1D,OADAC,EAAetE,EAAIqE,GACZ,SAAU7H,CAAU,EAAI,OAAOkF,EAAY1B,EAAImP,EAAU,CAAC3S,EAAW,CAAG,CACnF,EAd4C8S,EAAOH,EAAU,GAAG,MAAM,CAAC9K,EAAS,6BACxE,UAAWhJ,AAAcU,KAAAA,IAAdV,EACPU,KAAAA,EACAsiB,AAYZ,SAA6Cre,CAAE,CAAEmP,CAAQ,CAAE9K,CAAO,EAE9D,OADAC,EAAetE,EAAIqE,GACZ,SAAU/F,CAAK,CAAE9B,CAAU,EAAI,OAAOuF,EAAY/B,EAAImP,EAAU,CAAC7Q,EAAO9B,EAAW,CAAG,CACjG,EAfgDnB,EAAW8T,EAAU,GAAG,MAAM,CAAC9K,EAAS,iCAChF,aAAcwZ,CAClB,GAuCI,GAAII,AAA6BliB,KAAAA,IAA7BkiB,EAAY,YAAY,CACxB,MAAM,AAAI5hB,WAAW,kCAEzB,GAAI4hB,AAA6BliB,KAAAA,IAA7BkiB,EAAY,YAAY,CACxB,MAAM,AAAI5hB,WAAW,kCAEzB,IAAIiiB,EAAwBjQ,GAAqB2P,EAAkB,GAC/DO,EAAwB/P,GAAqBwP,GAC7CQ,EAAwBnQ,GAAqB0P,EAAkB,GAC/DU,EAAwBjQ,GAAqBuP,GAKjDW,AA+CR,UAAmCvb,CAAM,CAAEwb,CAAY,CAAEH,CAAqB,CAAEC,CAAqB,CAAEH,CAAqB,CAAEC,CAAqB,MAl6ErHrR,EAAgB6C,EAAgBC,EAAgBC,EAAgB5C,EAAewC,EAGrG1M,EAg6EJ,SAAS+J,IACL,OAAOyR,CACX,CAUAxb,EAAO,SAAS,EA/6EU+J,EA+6EcA,EA/6EE6C,EAs6E1C,SAAwBzR,CAAK,EACzB,OAAOsgB,AAkOf,SAAkDzb,CAAM,CAAE7E,CAAK,EAC3D,IAtjJuD6C,EAsjJnD3E,EAAa2G,EAAO,0BAA0B,CAClD,GAAIA,EAAO,aAAa,CAAE,EAEtB,OAxjJGzC,EAujJ6ByC,EAAO,0BAA0B,CACV,WACnD,IAAIhI,EAAWgI,EAAO,SAAS,CAE/B,GAAIkG,AAAU,aADFlO,EAAS,MAAM,CAEvB,MAAMA,EAAS,YAAY,CAE/B,OAAO0jB,GAAiDriB,EAAY8B,EACxE,EAhkJmD6C,KAAAA,EAikJvD,CACA,OAAO0d,GAAiDriB,EAAY8B,EACxE,EAhPwD6E,EAAQ7E,EAC5D,EAx6E0D0R,EA46E1D,WACI,OAAO8O,AA0Qf,SAAkD3b,CAAM,EACpD,IAAI3G,EAAa2G,EAAO,0BAA0B,CAClD,GAAI3G,AAA8BT,KAAAA,IAA9BS,EAAW,cAAc,CACzB,OAAOA,EAAW,cAAc,CAGpC,IAAIvB,EAAWkI,EAAO,SAAS,AAG/B3G,CAAAA,EAAW,cAAc,CAAG6D,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EAC5D7B,EAAW,sBAAsB,CAAG4B,EACpC5B,EAAW,qBAAqB,CAAG6B,CACvC,GACA,IAAI0gB,EAAeviB,EAAW,eAAe,GAgB7C,OAfAwiB,GAAgDxiB,GAChDsE,EAAYie,EAAc,WAQtB,MAPI9jB,AAAoB,YAApBA,EAAS,MAAM,CACfgkB,GAAqCziB,EAAYvB,EAAS,YAAY,GAGtEyb,GAAqCzb,EAAS,yBAAyB,EACvEikB,GAAsC1iB,IAEnC,IACX,EAAG,SAAU8Q,CAAC,EAGV,OAFAsJ,GAAqC3b,EAAS,yBAAyB,CAAEqS,GACzE2R,GAAqCziB,EAAY8Q,GAC1C,IACX,GACO9Q,EAAW,cAAc,AACpC,EAxSwD2G,EACpD,EA96E0E8M,EAy6E1E,SAAwBxT,CAAM,EAC1B,OAAO0iB,AA8Of,SAAkDhc,CAAM,CAAE1G,CAAM,EAC5D,IAAID,EAAa2G,EAAO,0BAA0B,CAClD,GAAI3G,AAA8BT,KAAAA,IAA9BS,EAAW,cAAc,CACzB,OAAOA,EAAW,cAAc,CAGpC,IAAIvB,EAAWkI,EAAO,SAAS,AAG/B3G,CAAAA,EAAW,cAAc,CAAG6D,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EAC5D7B,EAAW,sBAAsB,CAAG4B,EACpC5B,EAAW,qBAAqB,CAAG6B,CACvC,GACA,IAAI8a,EAAgB3c,EAAW,gBAAgB,CAACC,GAgBhD,OAfAuiB,GAAgDxiB,GAChDsE,EAAYqY,EAAe,WAQvB,MAPIle,AAAoB,YAApBA,EAAS,MAAM,CACfgkB,GAAqCziB,EAAYvB,EAAS,YAAY,GAGtE2b,GAAqC3b,EAAS,yBAAyB,CAAEwB,GACzEyiB,GAAsC1iB,IAEnC,IACX,EAAG,SAAU8Q,CAAC,EAGV,OAFAsJ,GAAqC3b,EAAS,yBAAyB,CAAEqS,GACzE2R,GAAqCziB,EAAY8Q,GAC1C,IACX,GACO9Q,EAAW,cAAc,AACpC,EA5QwD2G,EAAQ1G,EAC5D,EA16EsB,KAAK,KAD+D4Q,EA+6EcmR,IA96ExEnR,CAAAA,EAAgB,GAC1B,KAAK,KAF8EwC,EA+6EsB4O,IA76E/F5O,CAAAA,EAAgB,WAAc,OAAO,CAAG,GAExED,GADIzM,EAASnJ,OAAO,MAAM,CAACgV,GAAe,SAAS,GAGnDmB,GAAqChN,EADpBnJ,OAAO,MAAM,CAACkW,GAAgC,SAAS,EACfhD,EAAgB6C,EAAgBC,EAAgBC,EAAgB5C,EAAewC,GACjI1M,EA+6EPA,CAAAA,EAAO,SAAS,CAAGiX,GAAqBlN,EANxC,WACI,OAAOkS,AAsSf,SAAmDjc,CAAM,EAIrD,OAFAkc,GAA+Blc,EAAQ,IAEhCA,EAAO,0BAA0B,AAC5C,EA3SyDA,EACrD,EACA,SAAyB1G,CAAM,EAC3B,OAAO6iB,AAySf,SAAqDnc,CAAM,CAAE1G,CAAM,EAC/D,IAAID,EAAa2G,EAAO,0BAA0B,CAClD,GAAI3G,AAA8BT,KAAAA,IAA9BS,EAAW,cAAc,CACzB,OAAOA,EAAW,cAAc,CAGpC,IAAIrB,EAAWgI,EAAO,SAAS,AAI/B3G,CAAAA,EAAW,cAAc,CAAG6D,EAAW,SAAUjC,CAAO,CAAEC,CAAM,EAC5D7B,EAAW,sBAAsB,CAAG4B,EACpC5B,EAAW,qBAAqB,CAAG6B,CACvC,GACA,IAAI8a,EAAgB3c,EAAW,gBAAgB,CAACC,GAkBhD,OAjBAuiB,GAAgDxiB,GAChDsE,EAAYqY,EAAe,WASvB,MARIhe,AAAoB,YAApBA,EAAS,MAAM,CACf8jB,GAAqCziB,EAAYrB,EAAS,YAAY,GAGtEgY,GAA6ChY,EAAS,yBAAyB,CAAEsB,GACjF8iB,GAA4Bpc,GAC5B+b,GAAsC1iB,IAEnC,IACX,EAAG,SAAU8Q,CAAC,EAIV,OAHA6F,GAA6ChY,EAAS,yBAAyB,CAAEmS,GACjFiS,GAA4Bpc,GAC5B8b,GAAqCziB,EAAY8Q,GAC1C,IACX,GACO9Q,EAAW,cAAc,AACpC,EA1U2D2G,EAAQ1G,EAC/D,EACwF6hB,EAAuBC,GAE/Gpb,EAAO,aAAa,CAAGpH,KAAAA,EACvBoH,EAAO,0BAA0B,CAAGpH,KAAAA,EACpCoH,EAAO,kCAAkC,CAAGpH,KAAAA,EAC5CsjB,GAA+Blc,EAAQ,IACvCA,EAAO,0BAA0B,CAAGpH,KAAAA,CACxC,GA1EkC,IAAI,CAHXsE,EAAW,SAAUjC,CAAO,EAC3C0f,EAAuB1f,CAC3B,GAC8CogB,EAAuBC,EAAuBH,EAAuBC,GACnHiB,AAwMR,SAA8Drc,CAAM,CAAE8a,CAAW,EAC7E,IAX2C9a,EAAQ3G,EAAYijB,EAAoBC,EAAgBtS,EAY/FqS,EACAC,EACAtS,EAHA5Q,EAAaxC,OAAO,MAAM,CAAC2lB,GAAiC,SAAS,EAKrEF,EADAxB,AAA0BliB,KAAAA,IAA1BkiB,EAAY,SAAS,CACA,SAAU3f,CAAK,EAAI,OAAO2f,EAAY,SAAS,CAAC3f,EAAO9B,EAAa,EAGpE,SAAU8B,CAAK,EAChC,GAAI,CAEA,OADAshB,GAAwCpjB,EAAY8B,GAC7CiC,EAAoBxE,KAAAA,EAC/B,CACA,MAAO8jB,EAAkB,CACrB,OA1gJLzf,EA0gJgCyf,EAC/B,CACJ,EAGAH,EADAzB,AAAsBliB,KAAAA,IAAtBkiB,EAAY,KAAK,CACA,WAAc,OAAOA,EAAY,KAAK,CAACzhB,EAAa,EAGpD,WAAc,OAAO+D,EAAoBxE,KAAAA,EAAY,EAGtEqR,EADA6Q,AAAuBliB,KAAAA,IAAvBkiB,EAAY,MAAM,CACA,SAAUxhB,CAAM,EAAI,OAAOwhB,EAAY,MAAM,CAACxhB,EAAS,EAGvD,WAAc,OAAO8D,EAAoBxE,KAAAA,EAAY,EAvChCoH,EAyCLA,EAzCa3G,EAyCLA,EAzCiBijB,EAyCLA,EAzCyBC,EAyCLA,EAzCqBtS,EAyCLA,EAxC9F5Q,EAAW,0BAA0B,CAAG2G,EACxCA,EAAO,0BAA0B,CAAG3G,EACpCA,EAAW,mBAAmB,CAAGijB,EACjCjjB,EAAW,eAAe,CAAGkjB,EAC7BljB,EAAW,gBAAgB,CAAG4Q,EAC9B5Q,EAAW,cAAc,CAAGT,KAAAA,EAC5BS,EAAW,sBAAsB,CAAGT,KAAAA,EACpCS,EAAW,qBAAqB,CAAGT,KAAAA,CAkCvC,EAxO6D,IAAI,CAAEkiB,GACvDA,AAAsBliB,KAAAA,IAAtBkiB,EAAY,KAAK,CACjBH,EAAqBG,EAAY,KAAK,CAAC,IAAI,CAAC,0BAA0B,GAGtEH,EAAqB/hB,KAAAA,EAE7B,CA2BA,OA1BA/B,OAAO,cAAc,CAACujB,EAAgB,SAAS,CAAE,WAAY,CAIzD,IAAK,WACD,GAAI,CAACuC,GAAkB,IAAI,EACvB,MAAMC,GAA0B,YAEpC,OAAO,IAAI,CAAC,SAAS,AACzB,EACA,WAAY,GACZ,aAAc,EAClB,GACA/lB,OAAO,cAAc,CAACujB,EAAgB,SAAS,CAAE,WAAY,CAIzD,IAAK,WACD,GAAI,CAACuC,GAAkB,IAAI,EACvB,MAAMC,GAA0B,YAEpC,OAAO,IAAI,CAAC,SAAS,AACzB,EACA,WAAY,GACZ,aAAc,EAClB,GACOxC,CACX,IACAvjB,OAAO,gBAAgB,CAACujB,GAAgB,SAAS,CAAE,CAC/C,SAAU,CAAE,WAAY,EAAK,EAC7B,SAAU,CAAE,WAAY,EAAK,CACjC,GAC0C,UAAtC,OAAOne,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAACujB,GAAgB,SAAS,CAAEne,EAAe,WAAW,CAAE,CACzE,MAAO,kBACP,aAAc,EAClB,GA8BJ,SAAS0gB,GAAkBnlB,CAAC,QACxB,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,gCAGtCA,aAAa4iB,EACxB,CAEA,SAASyC,GAAqB7c,CAAM,CAAEpI,CAAC,EACnC6b,GAAqCzT,EAAO,SAAS,CAAC,yBAAyB,CAAEpI,GACjFklB,GAA4C9c,EAAQpI,EACxD,CACA,SAASklB,GAA4C9c,CAAM,CAAEpI,CAAC,EAC1DikB,GAAgD7b,EAAO,0BAA0B,EACjFgQ,GAA6ChQ,EAAO,SAAS,CAAC,yBAAyB,CAAEpI,GACzFwkB,GAA4Bpc,EAChC,CACA,SAASoc,GAA4Bpc,CAAM,EACnCA,EAAO,aAAa,EAIpBkc,GAA+Blc,EAAQ,GAE/C,CACA,SAASkc,GAA+Blc,CAAM,CAAEuO,CAAY,EAEd3V,KAAAA,IAAtCoH,EAAO,0BAA0B,EACjCA,EAAO,kCAAkC,GAE7CA,EAAO,0BAA0B,CAAG9C,EAAW,SAAUjC,CAAO,EAC5D+E,EAAO,kCAAkC,CAAG/E,CAChD,GACA+E,EAAO,aAAa,CAAGuO,CAC3B,CAOA,IAAIiO,GAAkD,WAClD,SAASA,IACL,MAAM,AAAIvlB,UAAU,sBACxB,CA2CA,OA1CAJ,OAAO,cAAc,CAAC2lB,EAAiC,SAAS,CAAE,cAAe,CAI7E,IAAK,WACD,GAAI,CAACO,GAAmC,IAAI,EACxC,MAAMC,GAAqC,eAG/C,OAAO3J,GADkB,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,yBAAyB,CAEhG,EACA,WAAY,GACZ,aAAc,EAClB,GACAmJ,EAAiC,SAAS,CAAC,OAAO,CAAG,SAAUrhB,CAAK,EAEhE,GADc,KAAK,IAAfA,GAAoBA,CAAAA,EAAQvC,KAAAA,CAAQ,EACpC,CAACmkB,GAAmC,IAAI,EACxC,MAAMC,GAAqC,WAE/CP,GAAwC,IAAI,CAAEthB,EAClD,EAKAqhB,EAAiC,SAAS,CAAC,KAAK,CAAG,SAAUljB,CAAM,EAE/D,GADe,KAAK,IAAhBA,GAAqBA,CAAAA,EAASV,KAAAA,CAAQ,EACtC,CAACmkB,GAAmC,IAAI,EACxC,MAAMC,GAAqC,SAE/CC,AA4GR,UAA+C5jB,CAAU,CAAEzB,CAAC,EACxDilB,GAAqBxjB,EAAW,0BAA0B,CAAEzB,EAChE,GA9G8C,IAAI,CAAE0B,EAChD,EAKAkjB,EAAiC,SAAS,CAAC,SAAS,CAAG,WACnD,GAAI,CAACO,GAAmC,IAAI,EACxC,MAAMC,GAAqC,aAE/CE,AA4GR,UAAmD7jB,CAAU,EACzD,IAAI2G,EAAS3G,EAAW,0BAA0B,CAElDka,GADyBvT,EAAO,SAAS,CAAC,yBAAyB,EAGnE8c,GAA4C9c,EADhC,AAAI/I,UAAU,8BAE9B,GAlHkD,IAAI,CAClD,EACOulB,CACX,IAiBA,SAASO,GAAmCvlB,CAAC,QACzC,GAAKD,EAAaC,IAGbX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACW,EAAG,gCAGtCA,aAAaglB,EACxB,CAxBA3lB,OAAO,gBAAgB,CAAC2lB,GAAiC,SAAS,CAAE,CAChE,QAAS,CAAE,WAAY,EAAK,EAC5B,MAAO,CAAE,WAAY,EAAK,EAC1B,UAAW,CAAE,WAAY,EAAK,EAC9B,YAAa,CAAE,WAAY,EAAK,CACpC,GACA5f,EAAgB4f,GAAiC,SAAS,CAAC,OAAO,CAAE,WACpE5f,EAAgB4f,GAAiC,SAAS,CAAC,KAAK,CAAE,SAClE5f,EAAgB4f,GAAiC,SAAS,CAAC,SAAS,CAAE,aAC5B,UAAtC,OAAOvgB,EAAe,WAAW,EACjCpF,OAAO,cAAc,CAAC2lB,GAAiC,SAAS,CAAEvgB,EAAe,WAAW,CAAE,CAC1F,MAAO,mCACP,aAAc,EAClB,GAuDJ,SAAS4f,GAAgDxiB,CAAU,EAC/DA,EAAW,mBAAmB,CAAGT,KAAAA,EACjCS,EAAW,eAAe,CAAGT,KAAAA,EAC7BS,EAAW,gBAAgB,CAAGT,KAAAA,CAClC,CACA,SAAS6jB,GAAwCpjB,CAAU,CAAE8B,CAAK,EAC9D,IAAI6E,EAAS3G,EAAW,0BAA0B,CAC9C8jB,EAAqBnd,EAAO,SAAS,CAAC,yBAAyB,CACnE,GAAI,CAACsT,GAAiD6J,GAClD,MAAM,AAAIlmB,UAAU,wDAIxB,GAAI,CACAuc,GAAuC2J,EAAoBhiB,EAC/D,CACA,MAAOvD,EAAG,CAGN,MADAklB,GAA4C9c,EAAQpI,GAC9CoI,EAAO,SAAS,CAAC,YAAY,AACvC,CA7zCA,EAAI4T,GA8zC8DuJ,IA3zC3D,EADP,IA6zCqBnd,EAAO,aAAa,EACrCkc,GAA+Blc,EAAQ,GAE/C,CAIA,SAAS0b,GAAiDriB,CAAU,CAAE8B,CAAK,EAEvE,OAviJOoC,EAsiJgBlE,EAAW,mBAAmB,CAAC8B,GACRvC,KAAAA,EAAW,SAAUuR,CAAC,EAEhE,MADA0S,GAAqBxjB,EAAW,0BAA0B,CAAE8Q,GACtDA,CACV,EACJ,CAgIA,SAAS6S,GAAqClgB,CAAI,EAC9C,OAAO,AAAI7F,UAAU,8CAA8C,MAAM,CAAC6F,EAAM,2DACpF,CACA,SAASif,GAAsC1iB,CAAU,EACrD,GAAIA,AAAsCT,KAAAA,IAAtCS,EAAW,sBAAsB,CAGrCA,EAAW,sBAAsB,GACjCA,EAAW,sBAAsB,CAAGT,KAAAA,EACpCS,EAAW,qBAAqB,CAAGT,KAAAA,CACvC,CACA,SAASkjB,GAAqCziB,CAAU,CAAEC,CAAM,EAC5D,GAAID,AAAqCT,KAAAA,IAArCS,EAAW,qBAAqB,CAGpC4E,EAA0B5E,EAAW,cAAc,EACnDA,EAAW,qBAAqB,CAACC,GACjCD,EAAW,sBAAsB,CAAGT,KAAAA,EACpCS,EAAW,qBAAqB,CAAGT,KAAAA,CACvC,CAEA,SAASgkB,GAA0B9f,CAAI,EACnC,OAAO,AAAI7F,UAAU,6BAA6B,MAAM,CAAC6F,EAAM,0CACnE"}