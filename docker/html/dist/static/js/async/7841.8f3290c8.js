/*! For license information please see 7841.8f3290c8.js.LICENSE.txt */
"use strict";(self.webpackChunk_coze_studio_app=self.webpackChunk_coze_studio_app||[]).push([["7841"],{187394:function(t,e,i){i.r(e),i.d(e,{getDocument:()=>s.Me,initPdfJsWorker:()=>n,generatePdfAssetsUrl:()=>r});var s=i("936010"),a=JSON.parse('{"name":"@coze-arch/pdfjs-shadow","version":"0.0.1","description":"shadow copy of pdfjs-dist","license":"Apache-2.0","author":"<EMAIL>","maintainers":[],"main":"src/index.ts","unpkg":"./lib","types":"./src/index.ts","files":["lib","README.md"],"scripts":{"build":"tsc -b tsconfig.build.json && node -r sucrase/register scripts/build.ts","lint":"eslint ./ --cache","test":"vitest --run --passWithNoTests","test:cov":"npm run test -- --coverage"},"devDependencies":{"@coze-arch/eslint-config":"workspace:*","@coze-arch/stylelint-config":"workspace:*","@coze-arch/ts-config":"workspace:*","@coze-arch/vitest-config":"workspace:*","@types/node":"^18","@vitest/coverage-v8":"~3.0.5","core-js":"^3.37.1","esbuild":"^0.15.18","pdfjs-dist":"4.3.136","sucrase":"^3.32.0","vitest":"~3.0.5"},"// deps":"@types/react-dom@^18.2.7 为脚本自动补齐，请勿改动","botPublishConfig":{"main":"lib/worker.js"}}'),r=t=>{var e,{name:i}=a;switch(t){case"cmaps":e="lib/cmaps/";break;case"pdf.worker":e="lib/worker.js";break;default:throw Error("目前只支持引用 cmaps 与 pdf.worker 文件，如需引用其他文件请联系 @fanwenjie.fe")}var s=i.replace(/^@/,"");return"//".concat("lf-cdn.coze.cn/obj/unpkg","/").concat(s,"/").concat("0.1.0-alpha.x6e892414ec","/").concat(e)},n=()=>{!s.Tu.workerSrc&&(s.Tu.workerSrc=r("pdf.worker"))}},936010:function(t,e,i){let s;i.d(e,{Me:function(){return iq},Tu:function(){return iV}});var a={};a.d=(t,e)=>{for(var i in e)a.o(e,i)&&!a.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var r=globalThis.pdfjsLib={};a.d(r,{AbortException:()=>O,AnnotationEditorLayer:()=>i$,AnnotationEditorParamsType:()=>u,AnnotationEditorType:()=>c,AnnotationEditorUIManager:()=>tk,AnnotationLayer:()=>iP,AnnotationMode:()=>d,CMapCompressionType:()=>A,ColorPicker:()=>iH,DOMSVGFactory:()=>ta,DrawLayer:()=>iG,FeatureTest:()=>z,GlobalWorkerOptions:()=>ee,ImageKind:()=>f,InvalidPDFException:()=>R,MissingPDFException:()=>I,OPS:()=>y,Outliner:()=>iD,PDFDataRangeTransport:()=>eW,PDFDateString:()=>tf,PDFWorker:()=>eJ,PasswordResponses:()=>_,PermissionFlag:()=>p,PixelsPerInch:()=>tt,RenderingCancelledException:()=>tn,TextLayer:()=>eO,UnexpectedResponseException:()=>D,Util:()=>j,VerbosityLevel:()=>v,XfaLayer:()=>e9,build:()=>e6,createValidAbsoluteUrl:()=>T,fetchData:()=>te,getDocument:()=>eG,getFilenameFromUrl:()=>th,getPdfFilenameFromUrl:()=>td,getXfaPageViewport:()=>tm,isDataScheme:()=>to,isPdfFile:()=>tl,noContextMenu:()=>tp,normalizeUnicode:()=>V,renderTextLayer:()=>eN,setLayerDimensions:()=>ty,shadow:()=>M,updateTextLayer:()=>eB,version:()=>e5});let n="object"==typeof process&&process+""=="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&"browser"!==process.type),o=[1,0,0,1,0,0],l=[.001,0,0,.001,0,0],h={ANY:1,DISPLAY:2,PRINT:4,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256},d={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},c={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15},u={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35},p={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},g={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},f={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},m={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,WIDGET:20},b={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},v={ERRORS:0,WARNINGS:1,INFOS:5},A={NONE:0,BINARY:1},y={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91},_={NEED_PASSWORD:1,INCORRECT_PASSWORD:2},w=v.WARNINGS;function x(t){w>=v.INFOS&&console.log(`Info: ${t}`)}function E(t){w>=v.WARNINGS&&console.log(`Warning: ${t}`)}function C(t){throw Error(t)}function S(t,e){!t&&C(e)}function T(t,e=null,i=null){if(!t)return null;try{if(i&&"string"==typeof t){if(i.addDefaultProtocol&&t.startsWith("www.")){let e=t.match(/\./g);e?.length>=2&&(t=`http://${t}`)}if(i.tryConvertEncoding)try{t=function(t){return decodeURIComponent(escape(t))}(t)}catch{}}let s=e?new URL(t,e):new URL(t);if(function(t){switch(t?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(s))return s}catch{}return null}function M(t,e,i,s=!1){return Object.defineProperty(t,e,{value:i,enumerable:!s,configurable:!0,writable:!1}),i}let k=function(){function t(e,i){this.constructor===t&&C("Cannot initialize BaseException."),this.message=e,this.name=i}return t.prototype=Error(),t.constructor=t,t}();class L extends k{constructor(t,e){super(t,"PasswordException"),this.code=e}}class P extends k{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class R extends k{constructor(t){super(t,"InvalidPDFException")}}class I extends k{constructor(t){super(t,"MissingPDFException")}}class D extends k{constructor(t,e){super(t,"UnexpectedResponseException"),this.status=e}}class F extends k{constructor(t){super(t,"FormatError")}}class O extends k{constructor(t){super(t,"AbortException")}}function N(t){("object"!=typeof t||t?.length===void 0)&&C("Invalid argument for bytesToString");let e=t.length;if(e<8192)return String.fromCharCode.apply(null,t);let i=[];for(let s=0;s<e;s+=8192){let a=Math.min(s+8192,e),r=t.subarray(s,a);i.push(String.fromCharCode.apply(null,r))}return i.join("")}function B(t){"string"!=typeof t&&C("Invalid argument for stringToBytes");let e=t.length,i=new Uint8Array(e);for(let s=0;s<e;++s)i[s]=255&t.charCodeAt(s);return i}function H(t){let e=Object.create(null);for(let[i,s]of t)e[i]=s;return e}class z{static get isLittleEndian(){return M(this,"isLittleEndian",function(){let t=new Uint8Array(4);return t[0]=1,1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return M(this,"isEvalSupported",function(){try{return Function(""),!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return M(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get platform(){return"undefined"!=typeof navigator&&"string"==typeof navigator?.platform?M(this,"platform",{isMac:navigator.platform.includes("Mac")}):M(this,"platform",{isMac:!1})}static get isCSSRoundSupported(){return M(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}let U=Array.from(Array(256).keys(),t=>t.toString(16).padStart(2,"0"));class j{static makeHexColor(t,e,i){return`#${U[t]}${U[e]}${U[i]}`}static scaleMinMax(t,e){let i;t[0]?(t[0]<0&&(i=e[0],e[0]=e[2],e[2]=i),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(i=e[1],e[1]=e[3],e[3]=i),e[1]*=t[3],e[3]*=t[3]):(i=e[0],e[0]=e[1],e[1]=i,i=e[2],e[2]=e[3],e[3]=i,t[1]<0&&(i=e[1],e[1]=e[3],e[3]=i),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(i=e[0],e[0]=e[2],e[2]=i),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){let i=t[0]*e[0]+t[1]*e[2]+e[4];return[i,t[0]*e[1]+t[1]*e[3]+e[5]]}static applyInverseTransform(t,e){let i=e[0]*e[3]-e[1]*e[2],s=(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/i;return[s,(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/i]}static getAxialAlignedBoundingBox(t,e){let i=this.applyTransform(t,e),s=this.applyTransform(t.slice(2,4),e),a=this.applyTransform([t[0],t[3]],e),r=this.applyTransform([t[2],t[1]],e);return[Math.min(i[0],s[0],a[0],r[0]),Math.min(i[1],s[1],a[1],r[1]),Math.max(i[0],s[0],a[0],r[0]),Math.max(i[1],s[1],a[1],r[1])]}static inverseTransform(t){let e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t){let e=[t[0],t[2],t[1],t[3]],i=t[0]*e[0]+t[1]*e[2],s=t[0]*e[1]+t[1]*e[3],a=t[2]*e[0]+t[3]*e[2],r=t[2]*e[1]+t[3]*e[3],n=(i+r)/2,o=Math.sqrt((i+r)**2-4*(i*r-a*s))/2;return[Math.sqrt(n+o||1),Math.sqrt(n-o||1)]}static normalizeRect(t){let e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){let i=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),s=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(i>s)return null;let a=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),r=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return a>r?null:[i,a,s,r]}static #t(t,e,i,s,a,r,n,o,l,h){if(l<=0||l>=1)return;let d=1-l,c=l*l,u=c*l,p=d*(d*(d*t+3*l*e)+3*c*i)+u*s,g=d*(d*(d*a+3*l*r)+3*c*n)+u*o;h[0]=Math.min(h[0],p),h[1]=Math.min(h[1],g),h[2]=Math.max(h[2],p),h[3]=Math.max(h[3],g)}static #e(t,e,i,s,a,r,n,o,l,h,d,c){if(1e-12>Math.abs(l)){Math.abs(h)>=1e-12&&this.#t(t,e,i,s,a,r,n,o,-d/h,c);return}let u=h**2-4*d*l;if(u<0)return;let p=Math.sqrt(u),g=2*l;this.#t(t,e,i,s,a,r,n,o,(-h+p)/g,c),this.#t(t,e,i,s,a,r,n,o,(-h-p)/g,c)}static bezierBoundingBox(t,e,i,s,a,r,n,o,l){return l?(l[0]=Math.min(l[0],t,n),l[1]=Math.min(l[1],e,o),l[2]=Math.max(l[2],t,n),l[3]=Math.max(l[3],e,o)):l=[Math.min(t,n),Math.min(e,o),Math.max(t,n),Math.max(e,o)],this.#e(t,i,a,n,e,s,r,o,3*(-t+3*(i-a)+n),6*(t-2*i+a),3*(i-t),l),this.#e(t,i,a,n,e,s,r,o,3*(-e+3*(s-r)+o),6*(e-2*s+r),3*(s-e),l),l}}let $=null,G=null;function V(t){return!$&&($=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,G=new Map([["ﬅ","ſt"]])),t.replaceAll($,(t,e,i)=>e?e.normalize("NFKC"):G.get(i))}let q="pdfjs_internal_id_",W={BEZIER_CURVE_TO:0,MOVE_TO:1,LINE_TO:2,QUADRATIC_CURVE_TO:3,RESTORE:4,SAVE:5,SCALE:6,TRANSFORM:7,TRANSLATE:8};class K{constructor(){this.constructor===K&&C("Cannot initialize BaseFilterFactory.")}addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,i,s,a){return"none"}destroy(t=!1){}}class X{constructor(){this.constructor===X&&C("Cannot initialize BaseCanvasFactory.")}create(t,e){if(t<=0||e<=0)throw Error("Invalid canvas size");let i=this._createCanvas(t,e);return{canvas:i,context:i.getContext("2d")}}reset(t,e,i){if(!t.canvas)throw Error("Canvas is not specified");if(e<=0||i<=0)throw Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=i}destroy(t){if(!t.canvas)throw Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){C("Abstract method `_createCanvas` called.")}}class Y{constructor({baseUrl:t=null,isCompressed:e=!0}){this.constructor===Y&&C("Cannot initialize BaseCMapReaderFactory."),this.baseUrl=t,this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!t)throw Error("CMap name must be specified.");let e=this.baseUrl+t+(this.isCompressed?".bcmap":""),i=this.isCompressed?A.BINARY:A.NONE;return this._fetchData(e,i).catch(t=>{throw Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)})}_fetchData(t,e){C("Abstract method `_fetchData` called.")}}class Q{constructor({baseUrl:t=null}){this.constructor===Q&&C("Cannot initialize BaseStandardFontDataFactory."),this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!t)throw Error("Font filename must be specified.");let e=`${this.baseUrl}${t}`;return this._fetchData(e).catch(t=>{throw Error(`Unable to load font data at: ${e}`)})}_fetchData(t){C("Abstract method `_fetchData` called.")}}class J{constructor(){this.constructor===J&&C("Cannot initialize BaseSVGFactory.")}create(t,e,i=!1){if(t<=0||e<=0)throw Error("Invalid SVG dimensions");let s=this._createSVG("svg:svg");return s.setAttribute("version","1.1"),!i&&(s.setAttribute("width",`${t}px`),s.setAttribute("height",`${e}px`)),s.setAttribute("preserveAspectRatio","none"),s.setAttribute("viewBox",`0 0 ${t} ${e}`),s}createElement(t){if("string"!=typeof t)throw Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){C("Abstract method `_createSVG` called.")}}let Z="http://www.w3.org/2000/svg";class tt{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}async function te(t,e="text"){if(tu(t,document.baseURI)){let i=await fetch(t);if(!i.ok)throw Error(i.statusText);switch(e){case"arraybuffer":return i.arrayBuffer();case"blob":return i.blob();case"json":return i.json()}return i.text()}return new Promise((i,s)=>{let a=new XMLHttpRequest;a.open("GET",t,!0),a.responseType=e,a.onreadystatechange=()=>{if(a.readyState===XMLHttpRequest.DONE){if(200===a.status||0===a.status){switch(e){case"arraybuffer":case"blob":case"json":i(a.response);return}i(a.responseText);return}s(Error(a.statusText))}},a.send(null)})}class ti extends Y{_fetchData(t,e){return te(t,this.isCompressed?"arraybuffer":"text").then(t=>({cMapData:t instanceof ArrayBuffer?new Uint8Array(t):B(t),compressionType:e}))}}class ts extends Q{_fetchData(t){return te(t,"arraybuffer").then(t=>new Uint8Array(t))}}class ta extends J{_createSVG(t){return document.createElementNS(Z,t)}}class tr{constructor({viewBox:t,scale:e,rotation:i,offsetX:s=0,offsetY:a=0,dontFlip:r=!1}){let n,o,l,h,d,c,u,p;this.viewBox=t,this.scale=e,this.rotation=i,this.offsetX=s,this.offsetY=a;let g=(t[2]+t[0])/2,f=(t[3]+t[1])/2;switch((i%=360)<0&&(i+=360),i){case 180:n=-1,o=0,l=0,h=1;break;case 90:n=0,o=1,l=1,h=0;break;case 270:n=0,o=-1,l=-1,h=0;break;case 0:n=1,o=0,l=0,h=-1;break;default:throw Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}r&&(l=-l,h=-h),0===n?(d=Math.abs(f-t[1])*e+s,c=Math.abs(g-t[0])*e+a,u=(t[3]-t[1])*e,p=(t[2]-t[0])*e):(d=Math.abs(g-t[0])*e+s,c=Math.abs(f-t[1])*e+a,u=(t[2]-t[0])*e,p=(t[3]-t[1])*e),this.transform=[n*e,o*e,l*e,h*e,d-n*e*g-l*e*f,c-o*e*g-h*e*f],this.width=u,this.height=p}get rawDims(){let{viewBox:t}=this;return M(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:i=this.offsetX,offsetY:s=this.offsetY,dontFlip:a=!1}={}){return new tr({viewBox:this.viewBox.slice(),scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:a})}convertToViewportPoint(t,e){return j.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){let e=j.applyTransform([t[0],t[1]],this.transform),i=j.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],i[0],i[1]]}convertToPdfPoint(t,e){return j.applyInverseTransform([t,e],this.transform)}}class tn extends k{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function to(t){let e=t.length,i=0;for(;i<e&&""===t[i].trim();)i++;return"data:"===t.substring(i,i+5).toLowerCase()}function tl(t){return"string"==typeof t&&/\.pdf$/i.test(t)}function th(t){return[t]=t.split(/[#?]/,1),t.substring(t.lastIndexOf("/")+1)}function td(t,e="document.pdf"){if("string"!=typeof t)return e;if(to(t))return E('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),e;let i=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,s=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t),a=i.exec(s[1])||i.exec(s[2])||i.exec(s[3]);if(a&&(a=a[0]).includes("%"))try{a=i.exec(decodeURIComponent(a))[0]}catch{}return a||e}class tc{started=Object.create(null);times=[];time(t){t in this.started&&E(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){!(t in this.started)&&E(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){let t=[],e=0;for(let{name:t}of this.times)e=Math.max(t.length,e);for(let{name:i,start:s,end:a}of this.times)t.push(`${i.padEnd(e)} ${a-s}ms
`);return t.join("")}}function tu(t,e){try{let{protocol:i}=e?new URL(t,e):new URL(t);return"http:"===i||"https:"===i}catch{return!1}}function tp(t){t.preventDefault()}function tg(t){console.log("Deprecated API usage: "+t)}class tf{static toDateObject(t){if(!t||"string"!=typeof t)return null;let e=(s||=RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?")).exec(t);if(!e)return null;let i=parseInt(e[1],10),a=parseInt(e[2],10);a=a>=1&&a<=12?a-1:0;let r=parseInt(e[3],10);r=r>=1&&r<=31?r:1;let n=parseInt(e[4],10);n=n>=0&&n<=23?n:0;let o=parseInt(e[5],10);o=o>=0&&o<=59?o:0;let l=parseInt(e[6],10);l=l>=0&&l<=59?l:0;let h=e[7]||"Z",d=parseInt(e[8],10);d=d>=0&&d<=23?d:0;let c=parseInt(e[9],10)||0;return c=c>=0&&c<=59?c:0,"-"===h?(n+=d,o+=c):"+"===h&&(n-=d,o-=c),new Date(Date.UTC(i,a,r,n,o,l))}}function tm(t,{scale:e=1,rotation:i=0}){let{width:s,height:a}=t.attributes.style;return new tr({viewBox:[0,0,parseInt(s),parseInt(a)],scale:e,rotation:i})}function tb(t){if(t.startsWith("#")){let e=parseInt(t.slice(1),16);return[(0xff0000&e)>>16,(65280&e)>>8,255&e]}return t.startsWith("rgb(")?t.slice(4,-1).split(",").map(t=>parseInt(t)):t.startsWith("rgba(")?t.slice(5,-1).split(",").map(t=>parseInt(t)).slice(0,3):(E(`Not a valid color format: "${t}"`),[0,0,0])}function tv(t){let{a:e,b:i,c:s,d:a,e:r,f:n}=t.getTransform();return[e,i,s,a,r,n]}function tA(t){let{a:e,b:i,c:s,d:a,e:r,f:n}=t.getTransform().invertSelf();return[e,i,s,a,r,n]}function ty(t,e,i=!1,s=!0){if(e instanceof tr){let{pageWidth:s,pageHeight:a}=e.rawDims,{style:r}=t,n=z.isCSSRoundSupported,o=`var(--scale-factor) * ${s}px`,l=`var(--scale-factor) * ${a}px`,h=n?`round(${o}, 1px)`:`calc(${o})`,d=n?`round(${l}, 1px)`:`calc(${l})`;i&&e.rotation%180!=0?(r.width=d,r.height=h):(r.width=h,r.height=d)}s&&t.setAttribute("data-main-rotation",e.rotation)}class t_{#i=null;#s=null;#a;#r=null;constructor(t){this.#a=t}render(){let t=this.#i=document.createElement("div");t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",tp),t.addEventListener("pointerdown",t_.#n);let e=this.#r=document.createElement("div");e.className="buttons",t.append(e);let i=this.#a.toolbarPosition;if(i){let{style:e}=t,s="ltr"===this.#a._uiManager.direction?1-i[0]:i[0];e.insetInlineEnd=`${100*s}%`,e.top=`calc(${100*i[1]}% + var(--editor-toolbar-vert-offset))`}return this.#o(),t}static #n(t){t.stopPropagation()}#l(t){this.#a._focusEventsAllowed=!1,t.preventDefault(),t.stopPropagation()}#h(t){this.#a._focusEventsAllowed=!0,t.preventDefault(),t.stopPropagation()}#d(t){t.addEventListener("focusin",this.#l.bind(this),{capture:!0}),t.addEventListener("focusout",this.#h.bind(this),{capture:!0}),t.addEventListener("contextmenu",tp)}hide(){this.#i.classList.add("hidden"),this.#s?.hideDropdown()}show(){this.#i.classList.remove("hidden")}#o(){let t=document.createElement("button");t.className="delete",t.tabIndex=0,t.setAttribute("data-l10n-id",`pdfjs-editor-remove-${this.#a.editorType}-button`),this.#d(t),t.addEventListener("click",t=>{this.#a._uiManager.delete()}),this.#r.append(t)}get #c(){let t=document.createElement("div");return t.className="divider",t}addAltTextButton(t){this.#d(t),this.#r.prepend(t,this.#c)}addColorPicker(t){this.#s=t;let e=t.renderButton();this.#d(e),this.#r.prepend(e,this.#c)}remove(){this.#i.remove(),this.#s?.destroy(),this.#s=null}}class tw{#r=null;#i=null;#u;constructor(t){this.#u=t}#p(){let t=this.#i=document.createElement("div");t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",tp);let e=this.#r=document.createElement("div");return e.className="buttons",t.append(e),this.#g(),t}#f(t,e){let i=0,s=0;for(let a of t){let t=a.y+a.height;if(t<i)continue;let r=a.x+(e?a.width:0);if(t>i){s=r,i=t;continue}e?r>s&&(s=r):r<s&&(s=r)}return[e?1-s:s,i]}show(t,e,i){let[s,a]=this.#f(e,i),{style:r}=this.#i||=this.#p();t.append(this.#i),r.insetInlineEnd=`${100*s}%`,r.top=`calc(${100*a}% + var(--editor-toolbar-vert-offset))`}hide(){this.#i.remove()}#g(){let t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");let e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label"),t.addEventListener("contextmenu",tp),t.addEventListener("click",()=>{this.#u.highlightSelection("floating_button")}),this.#r.append(t)}}function tx(t,e,i){for(let s of i)e.addEventListener(s,t[s].bind(t))}class tE{#m=0;get id(){return`pdfjs_internal_editor_${this.#m++}`}}class tC{#b=(function(){if("undefined"!=typeof crypto&&"function"==typeof crypto?.randomUUID)return crypto.randomUUID();let t=new Uint8Array(32);if("undefined"!=typeof crypto&&"function"==typeof crypto?.getRandomValues)crypto.getRandomValues(t);else for(let e=0;e<32;e++)t[e]=Math.floor(255*Math.random());return N(t)})();#m=0;#v=null;static get _isSVGFittingCanvas(){let t=new OffscreenCanvas(1,3).getContext("2d"),e=new Image;return e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',M(this,"_isSVGFittingCanvas",e.decode().then(()=>(t.drawImage(e,0,0,1,1,0,0,1,3),0===new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0])))}async #A(t,e){this.#v||=new Map;let i=this.#v.get(t);if(null===i)return null;if(i?.bitmap)return i.refCounter+=1,i;try{let t;if(i||={bitmap:null,id:`image_${this.#b}_${this.#m++}`,refCounter:0,isSvg:!1},"string"==typeof e?(i.url=e,t=await te(e,"blob")):t=i.file=e,"image/svg+xml"===t.type){let e=tC._isSVGFittingCanvas,s=new FileReader,a=new Image,r=new Promise((t,r)=>{a.onload=()=>{i.bitmap=a,i.isSvg=!0,t()},s.onload=async()=>{let t=i.svgUrl=s.result;a.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t},a.onerror=s.onerror=r});s.readAsDataURL(t),await r}else i.bitmap=await createImageBitmap(t);i.refCounter=1}catch(t){console.error(t),i=null}return this.#v.set(t,i),i&&this.#v.set(i.id,i),i}async getFromFile(t){let{lastModified:e,name:i,size:s,type:a}=t;return this.#A(`${e}_${i}_${s}_${a}`,t)}async getFromUrl(t){return this.#A(t,t)}async getFromId(t){this.#v||=new Map;let e=this.#v.get(t);return e?e.bitmap?(e.refCounter+=1,e):e.file?this.getFromFile(e.file):this.getFromUrl(e.url):null}getSvgUrl(t){let e=this.#v.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#v||=new Map;let e=this.#v.get(t);if(!e)return;if(e.refCounter-=1,0===e.refCounter)e.bitmap=null}isValidId(t){return t.startsWith(`image_${this.#b}_`)}}class tS{#y=[];#_=!1;#w;#x=-1;constructor(t=128){this.#w=t}add({cmd:t,undo:e,post:i,mustExec:s,type:a=NaN,overwriteIfSameType:r=!1,keepUndo:n=!1}){if(s&&t(),this.#_)return;let o={cmd:t,undo:e,post:i,type:a};if(-1===this.#x){this.#y.length>0&&(this.#y.length=0),this.#x=0,this.#y.push(o);return}if(r&&this.#y[this.#x].type===a){n&&(o.undo=this.#y[this.#x].undo),this.#y[this.#x]=o;return}let l=this.#x+1;l===this.#w?this.#y.splice(0,1):(this.#x=l,l<this.#y.length&&this.#y.splice(l)),this.#y.push(o)}undo(){if(-1===this.#x)return;this.#_=!0;let{undo:t,post:e}=this.#y[this.#x];t(),e?.(),this.#_=!1,this.#x-=1}redo(){if(this.#x<this.#y.length-1){this.#x+=1,this.#_=!0;let{cmd:t,post:e}=this.#y[this.#x];t(),e?.(),this.#_=!1}}hasSomethingToUndo(){return -1!==this.#x}hasSomethingToRedo(){return this.#x<this.#y.length-1}destroy(){this.#y=null}}class tT{constructor(t){this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;let{isMac:e}=z.platform;for(let[i,s,a={}]of t)for(let t of i){let i=t.startsWith("mac+");e&&i?(this.callbacks.set(t.slice(4),{callback:s,options:a}),this.allKeys.add(t.split("+").at(-1))):!e&&!i&&(this.callbacks.set(t,{callback:s,options:a}),this.allKeys.add(t.split("+").at(-1)))}}#E(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);let e=this.buffer.join("+");return this.buffer.length=0,e}exec(t,e){if(!this.allKeys.has(e.key))return;let i=this.callbacks.get(this.#E(e));if(!i)return;let{callback:s,options:{bubbles:a=!1,args:r=[],checker:n=null}}=i;if(!n||!!n(t,e))s.bind(t,...r,e)(),!a&&(e.stopPropagation(),e.preventDefault())}}class tM{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){let t=new Map([["CanvasText",null],["Canvas",null]]);return!function(t){let e=document.createElement("span");for(let i of(e.style.visibility="hidden",document.body.append(e),t.keys())){e.style.color=i;let s=window.getComputedStyle(e).color;t.set(i,tb(s))}e.remove()}(t),M(this,"_colors",t)}convert(t){let e=tb(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(let[t,i]of this._colors)if(i.every((t,i)=>t===e[i]))return tM._colorsMapping.get(t);return e}getHexCode(t){let e=this._colors.get(t);return e?j.makeHexColor(...e):t}}class tk{#C=null;#S=new Map;#T=new Map;#M=null;#k=null;#L=null;#P=new tS;#R=0;#I=new Set;#D=null;#F=null;#O=new Set;#N=!1;#B=null;#H=null;#z=null;#U=!1;#j=null;#$=new tE;#G=!1;#V=!1;#q=null;#W=null;#K=null;#X=c.NONE;#Y=new Set;#Q=null;#J=null;#Z=null;#tt=this.blur.bind(this);#te=this.focus.bind(this);#ti=this.copy.bind(this);#ts=this.cut.bind(this);#ta=this.paste.bind(this);#tr=this.keydown.bind(this);#tn=this.keyup.bind(this);#to=this.onEditingAction.bind(this);#tl=this.onPageChanging.bind(this);#th=this.onScaleChanging.bind(this);#td=this.#tc.bind(this);#tu=this.onRotationChanging.bind(this);#tp={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1};#tg=[0,0];#tf=null;#tm=null;#tb=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){let t=tk.prototype,e=t=>t.#tm.contains(document.activeElement)&&"BUTTON"!==document.activeElement.tagName&&t.hasSomethingToControl(),i=(t,{target:e})=>{if(e instanceof HTMLInputElement){let{type:t}=e;return"text"!==t&&"number"!==t}return!0},s=this.TRANSLATE_SMALL,a=this.TRANSLATE_BIG;return M(this,"_keyboardManager",new tT([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:i}],[["ctrl+z","mac+meta+z"],t.undo,{checker:i}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:i}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:i}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#tm.contains(e)&&!t.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#tm.contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-s,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-a,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[s,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[a,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-s],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-a],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,s],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,a],checker:e}]]))}constructor(t,e,i,s,a,r,n,o,l){this.#tm=t,this.#tb=e,this.#M=i,this._eventBus=s,this._eventBus._on("editingaction",this.#to),this._eventBus._on("pagechanging",this.#tl),this._eventBus._on("scalechanging",this.#th),this._eventBus._on("rotationchanging",this.#tu),this.#tv(),this.#tA(),this.#k=a.annotationStorage,this.#B=a.filterFactory,this.#J=r,this.#z=n||null,this.#N=o,this.#K=l||null,this.viewParameters={realScale:tt.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1}destroy(){for(let t of(this.#ty(),this.#t_(),this._eventBus._off("editingaction",this.#to),this._eventBus._off("pagechanging",this.#tl),this._eventBus._off("scalechanging",this.#th),this._eventBus._off("rotationchanging",this.#tu),this.#T.values()))t.destroy();this.#T.clear(),this.#S.clear(),this.#O.clear(),this.#C=null,this.#Y.clear(),this.#P.destroy(),this.#M?.destroy(),this.#j?.hide(),this.#j=null,this.#H&&(clearTimeout(this.#H),this.#H=null),this.#tf&&(clearTimeout(this.#tf),this.#tf=null),this.#tw()}async mlGuess(t){return this.#K?.guess(t)||null}get hasMLManager(){return!!this.#K}get hcmFilter(){return M(this,"hcmFilter",this.#J?this.#B.addHCMFilter(this.#J.foreground,this.#J.background):"none")}get direction(){return M(this,"direction",getComputedStyle(this.#tm).direction)}get highlightColors(){return M(this,"highlightColors",this.#z?new Map(this.#z.split(",").map(t=>t.split("=").map(t=>t.trim()))):null)}get highlightColorNames(){return M(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,t=>t.reverse())):null)}setMainHighlightColorPicker(t){this.#W=t}editAltText(t){this.#M?.editAltText(this,t)}onPageChanging({pageNumber:t}){this.#R=t-1}focusMainContainer(){this.#tm.focus()}findParent(t,e){for(let i of this.#T.values()){let{x:s,y:a,width:r,height:n}=i.div.getBoundingClientRect();if(t>=s&&t<=s+r&&e>=a&&e<=a+n)return i}return null}disableUserSelect(t=!1){this.#tb.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#O.add(t)}removeShouldRescale(t){this.#O.delete(t)}onScaleChanging({scale:t}){for(let e of(this.commitOrRemove(),this.viewParameters.realScale=t*tt.PDF_TO_CSS_UNITS,this.#O))e.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}#tx({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t}highlightSelection(t=""){let e=document.getSelection();if(!e||e.isCollapsed)return;let{anchorNode:i,anchorOffset:s,focusNode:a,focusOffset:r}=e,n=e.toString(),o=this.#tx(e).closest(".textLayer"),l=this.getSelectionBoxes(o);if(!!l){for(let h of(e.empty(),this.#X===c.NONE&&(this._eventBus.dispatch("showannotationeditorui",{source:this,mode:c.HIGHLIGHT}),this.showAllEditors("highlight",!0,!0)),this.#T.values()))if(h.hasTextLayer(o)){h.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:l,anchorNode:i,anchorOffset:s,focusNode:a,focusOffset:r,text:n});break}}}#tE(){let t=document.getSelection();if(!t||t.isCollapsed)return;let e=this.#tx(t).closest(".textLayer"),i=this.getSelectionBoxes(e);if(!!i)this.#j||=new tw(this),this.#j.show(e,i,"ltr"===this.direction)}addToAnnotationStorage(t){!t.isEmpty()&&this.#k&&!this.#k.has(t.id)&&this.#k.setValue(t.id,t)}#tc(){let t=document.getSelection();if(!t||t.isCollapsed){this.#Q&&(this.#j?.hide(),this.#Q=null,this.#tC({hasSelectedText:!1}));return}let{anchorNode:e}=t;if(e===this.#Q)return;if(!this.#tx(t).closest(".textLayer")){this.#Q&&(this.#j?.hide(),this.#Q=null,this.#tC({hasSelectedText:!1}));return}if(this.#j?.hide(),this.#Q=e,this.#tC({hasSelectedText:!0}),this.#X===c.HIGHLIGHT||this.#X===c.NONE){if(this.#X===c.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),this.#U=this.isShiftKeyDown,!this.isShiftKeyDown){let t=e=>{if("pointerup"!==e.type||0===e.button)window.removeEventListener("pointerup",t),window.removeEventListener("blur",t),"pointerup"===e.type&&this.#tS("main_toolbar")};window.addEventListener("pointerup",t),window.addEventListener("blur",t)}}}#tS(t=""){this.#X===c.HIGHLIGHT?this.highlightSelection(t):this.#N&&this.#tE()}#tv(){document.addEventListener("selectionchange",this.#td)}#tw(){document.removeEventListener("selectionchange",this.#td)}#tT(){window.addEventListener("focus",this.#te),window.addEventListener("blur",this.#tt)}#t_(){window.removeEventListener("focus",this.#te),window.removeEventListener("blur",this.#tt)}blur(){if(this.isShiftKeyDown=!1,this.#U&&(this.#U=!1,this.#tS("main_toolbar")),!this.hasSelection)return;let{activeElement:t}=document;for(let e of this.#Y)if(e.div.contains(t)){this.#q=[e,t],e._focusEventsAllowed=!1;break}}focus(){if(!this.#q)return;let[t,e]=this.#q;this.#q=null,e.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0}),e.focus()}#tA(){window.addEventListener("keydown",this.#tr),window.addEventListener("keyup",this.#tn)}#ty(){window.removeEventListener("keydown",this.#tr),window.removeEventListener("keyup",this.#tn)}#tM(){document.addEventListener("copy",this.#ti),document.addEventListener("cut",this.#ts),document.addEventListener("paste",this.#ta)}#tk(){document.removeEventListener("copy",this.#ti),document.removeEventListener("cut",this.#ts),document.removeEventListener("paste",this.#ta)}addEditListeners(){this.#tA(),this.#tM()}removeEditListeners(){this.#ty(),this.#tk()}copy(t){if(t.preventDefault(),this.#C?.commitOrRemove(),!this.hasSelection)return;let e=[];for(let t of this.#Y){let i=t.serialize(!0);i&&e.push(i)}if(0!==e.length)t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}paste(t){t.preventDefault();let{clipboardData:e}=t;for(let t of e.items)for(let e of this.#F)if(e.isHandlingMimeForPasting(t.type)){e.paste(t,this.currentLayer);return}let i=e.getData("application/pdfjs");if(!i)return;try{i=JSON.parse(i)}catch(t){E(`paste: "${t.message}".`);return}if(!Array.isArray(i))return;this.unselectAll();let s=this.currentLayer;try{let t=[];for(let e of i){let i=s.deserialize(e);if(!i)return;t.push(i)}this.addCommands({cmd:()=>{for(let e of t)this.#tL(e);this.#tP(t)},undo:()=>{for(let e of t)e.remove()},mustExec:!0})}catch(t){E(`paste: "${t.message}".`)}}keydown(t){!this.isShiftKeyDown&&"Shift"===t.key&&(this.isShiftKeyDown=!0),this.#X!==c.NONE&&!this.isEditorHandlingKeyboard&&tk._keyboardManager.exec(this,t)}keyup(t){this.isShiftKeyDown&&"Shift"===t.key&&(this.isShiftKeyDown=!1,this.#U&&(this.#U=!1,this.#tS("main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu")}}#tC(t){Object.entries(t).some(([t,e])=>this.#tp[t]!==e)&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#tp,t)}),this.#X===c.HIGHLIGHT&&!1===t.hasSelectedEditor&&this.#tR([[u.HIGHLIGHT_FREE,!0]]))}#tR(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){t?(this.#tT(),this.#tM(),this.#tC({isEditing:this.#X!==c.NONE,isEmpty:this.#tI(),hasSomethingToUndo:this.#P.hasSomethingToUndo(),hasSomethingToRedo:this.#P.hasSomethingToRedo(),hasSelectedEditor:!1})):(this.#t_(),this.#tk(),this.#tC({isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!this.#F)for(let e of(this.#F=t,this.#F))this.#tR(e.defaultPropertiesToUpdate)}getId(){return this.#$.id}get currentLayer(){return this.#T.get(this.#R)}getLayer(t){return this.#T.get(t)}get currentPageIndex(){return this.#R}addLayer(t){this.#T.set(t.pageIndex,t),this.#G?t.enable():t.disable()}removeLayer(t){this.#T.delete(t.pageIndex)}updateMode(t,e=null,i=!1){if(this.#X===t)return;if(this.#X=t,t===c.NONE){this.setEditingState(!1),this.#tD();return}for(let e of(this.setEditingState(!0),this.#tF(),this.unselectAll(),this.#T.values()))e.updateMode(t);if(!e&&i){this.addNewEditorFromKeyboard();return}if(!!e){for(let t of this.#S.values())if(t.annotationElementId===e){this.setSelected(t),t.enterInEditMode();break}}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){if(t!==this.#X)this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){if(!!this.#F){switch(t){case u.CREATE:this.currentLayer.addNewEditor();return;case u.HIGHLIGHT_DEFAULT_COLOR:this.#W?.updateColor(e);break;case u.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(this.#Z||=new Map).set(t,e),this.showAllEditors("highlight",e)}for(let i of this.#Y)i.updateParams(t,e);for(let i of this.#F)i.updateDefaultParams(t,e)}}showAllEditors(t,e,i=!1){for(let i of this.#S.values())i.editorType===t&&i.show(e);(this.#Z?.get(u.HIGHLIGHT_SHOW_ALL)??!0)!==e&&this.#tR([[u.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(this.#V!==t)for(let e of(this.#V=t,this.#T.values()))t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}#tF(){if(!this.#G){for(let t of(this.#G=!0,this.#T.values()))t.enable();for(let t of this.#S.values())t.enable()}}#tD(){if(this.unselectAll(),this.#G){for(let t of(this.#G=!1,this.#T.values()))t.disable();for(let t of this.#S.values())t.disable()}}getEditors(t){let e=[];for(let i of this.#S.values())i.pageIndex===t&&e.push(i);return e}getEditor(t){return this.#S.get(t)}addEditor(t){this.#S.set(t.id,t)}removeEditor(t){t.div.contains(document.activeElement)&&(this.#H&&clearTimeout(this.#H),this.#H=setTimeout(()=>{this.focusMainContainer(),this.#H=null},0)),this.#S.delete(t.id),this.unselect(t),(!t.annotationElementId||!this.#I.has(t.annotationElementId))&&this.#k?.remove(t.id)}addDeletedAnnotationElement(t){this.#I.add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return this.#I.has(t)}removeDeletedAnnotationElement(t){this.#I.delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}#tL(t){let e=this.#T.get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))}setActiveEditor(t){if(this.#C!==t)this.#C=t,t&&this.#tR(t.propertiesToUpdate)}get #tO(){let t=null;for(t of this.#Y);return t}updateUI(t){this.#tO===t&&this.#tR(t.propertiesToUpdate)}toggleSelected(t){if(this.#Y.has(t)){this.#Y.delete(t),t.unselect(),this.#tC({hasSelectedEditor:this.hasSelection});return}this.#Y.add(t),t.select(),this.#tR(t.propertiesToUpdate),this.#tC({hasSelectedEditor:!0})}setSelected(t){for(let e of this.#Y)e!==t&&e.unselect();this.#Y.clear(),this.#Y.add(t),t.select(),this.#tR(t.propertiesToUpdate),this.#tC({hasSelectedEditor:!0})}isSelected(t){return this.#Y.has(t)}get firstSelectedEditor(){return this.#Y.values().next().value}unselect(t){t.unselect(),this.#Y.delete(t),this.#tC({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#Y.size}get isEnterHandled(){return 1===this.#Y.size&&this.firstSelectedEditor.isEnterHandled}undo(){this.#P.undo(),this.#tC({hasSomethingToUndo:this.#P.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#tI()})}redo(){this.#P.redo(),this.#tC({hasSomethingToUndo:!0,hasSomethingToRedo:this.#P.hasSomethingToRedo(),isEmpty:this.#tI()})}addCommands(t){this.#P.add(t),this.#tC({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#tI()})}#tI(){if(0===this.#S.size)return!0;if(1===this.#S.size)for(let t of this.#S.values())return t.isEmpty();return!1}delete(){if(this.commitOrRemove(),!this.hasSelection)return;let t=[...this.#Y];this.addCommands({cmd:()=>{for(let e of t)e.remove()},undo:()=>{for(let e of t)this.#tL(e)},mustExec:!0})}commitOrRemove(){this.#C?.commitOrRemove()}hasSomethingToControl(){return this.#C||this.hasSelection}#tP(t){for(let t of this.#Y)t.unselect();for(let e of(this.#Y.clear(),t)){if(!e.isEmpty())this.#Y.add(e),e.select()}this.#tC({hasSelectedEditor:this.hasSelection})}selectAll(){for(let t of this.#Y)t.commit();this.#tP(this.#S.values())}unselectAll(){if((!this.#C||(this.#C.commitOrRemove(),this.#X===c.NONE))&&!!this.hasSelection){for(let t of this.#Y)t.unselect();this.#Y.clear(),this.#tC({hasSelectedEditor:!1})}}translateSelectedEditors(t,e,i=!1){if(!i&&this.commitOrRemove(),!this.hasSelection)return;this.#tg[0]+=t,this.#tg[1]+=e;let[s,a]=this.#tg,r=[...this.#Y];for(let i of(this.#tf&&clearTimeout(this.#tf),this.#tf=setTimeout(()=>{this.#tf=null,this.#tg[0]=this.#tg[1]=0,this.addCommands({cmd:()=>{for(let t of r)this.#S.has(t.id)&&t.translateInPage(s,a)},undo:()=>{for(let t of r)this.#S.has(t.id)&&t.translateInPage(-s,-a)},mustExec:!1})},1e3),r))i.translateInPage(t,e)}setUpDragSession(){if(!!this.hasSelection)for(let t of(this.disableUserSelect(!0),this.#D=new Map,this.#Y))this.#D.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}endDragSession(){if(!this.#D)return!1;this.disableUserSelect(!1);let t=this.#D;this.#D=null;let e=!1;for(let[{x:i,y:s,pageIndex:a},r]of t)r.newX=i,r.newY=s,r.newPageIndex=a,e||=i!==r.savedX||s!==r.savedY||a!==r.savedPageIndex;if(!e)return!1;let i=(t,e,i,s)=>{if(this.#S.has(t.id)){let a=this.#T.get(s);a?t._setParentAndPosition(a,e,i):(t.pageIndex=s,t.x=e,t.y=i)}};return this.addCommands({cmd:()=>{for(let[e,{newX:s,newY:a,newPageIndex:r}]of t)i(e,s,a,r)},undo:()=>{for(let[e,{savedX:s,savedY:a,savedPageIndex:r}]of t)i(e,s,a,r)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(!!this.#D)for(let i of this.#D.keys())i.drag(t,e)}rebuild(t){if(null===t.parent){let e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||1===this.#Y.size&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return this.#C===t}getActive(){return this.#C}getMode(){return this.#X}get imageManager(){return M(this,"imageManager",new tC)}getSelectionBoxes(t){let e;if(!t)return null;let i=document.getSelection();for(let e=0,s=i.rangeCount;e<s;e++)if(!t.contains(i.getRangeAt(e).commonAncestorContainer))return null;let{x:s,y:a,width:r,height:n}=t.getBoundingClientRect();switch(t.getAttribute("data-main-rotation")){case"90":e=(t,e,i,o)=>({x:(e-a)/n,y:1-(t+i-s)/r,width:o/n,height:i/r});break;case"180":e=(t,e,i,o)=>({x:1-(t+i-s)/r,y:1-(e+o-a)/n,width:i/r,height:o/n});break;case"270":e=(t,e,i,o)=>({x:1-(e+o-a)/n,y:(t-s)/r,width:o/n,height:i/r});break;default:e=(t,e,i,o)=>({x:(t-s)/r,y:(e-a)/n,width:i/r,height:o/n})}let o=[];for(let t=0,s=i.rangeCount;t<s;t++){let s=i.getRangeAt(t);if(!s.collapsed)for(let{x:t,y:i,width:a,height:r}of s.getClientRects()){if(0!==a&&0!==r)o.push(e(t,i,a,r))}}return 0===o.length?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(this.#L||=new Map).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){this.#L?.delete(t)}renderAnnotationElement(t){let e=this.#L?.get(t.data.id);if(!e)return;let i=this.#k.getRawValue(e);if(!!i&&(this.#X!==c.NONE||!!i.hasBeenModified))i.renderAnnotationElement(t)}}class tL{#tN="";#tB=!1;#tH=null;#tz=null;#tU=null;#tj=!1;#a=null;static _l10nPromise=null;constructor(t){this.#a=t}static initialize(t){tL._l10nPromise||=t}async render(){let t=this.#tH=document.createElement("button");t.className="altText";let e=await tL._l10nPromise.get("pdfjs-editor-alt-text-button-label");t.textContent=e,t.setAttribute("aria-label",e),t.tabIndex="0",t.addEventListener("contextmenu",tp),t.addEventListener("pointerdown",t=>t.stopPropagation());let i=t=>{t.preventDefault(),this.#a._uiManager.editAltText(this.#a)};return t.addEventListener("click",i,{capture:!0}),t.addEventListener("keydown",e=>{e.target===t&&"Enter"===e.key&&(this.#tj=!0,i(e))}),await this.#t$(),t}finish(){if(!!this.#tH)this.#tH.focus({focusVisible:this.#tj}),this.#tj=!1}isEmpty(){return!this.#tN&&!this.#tB}get data(){return{altText:this.#tN,decorative:this.#tB}}set data({altText:t,decorative:e}){if(this.#tN!==t||this.#tB!==e)this.#tN=t,this.#tB=e,this.#t$()}toggle(t=!1){if(!!this.#tH)!t&&this.#tU&&(clearTimeout(this.#tU),this.#tU=null),this.#tH.disabled=!t}destroy(){this.#tH?.remove(),this.#tH=null,this.#tz=null}async #t$(){let t=this.#tH;if(!t)return;if(!this.#tN&&!this.#tB){t.classList.remove("done"),this.#tz?.remove();return}t.classList.add("done"),tL._l10nPromise.get("pdfjs-editor-alt-text-edit-button-label").then(e=>{t.setAttribute("aria-label",e)});let e=this.#tz;if(!e){this.#tz=e=document.createElement("span"),e.className="tooltip",e.setAttribute("role","tooltip");let i=e.id=`alt-text-tooltip-${this.#a.id}`;t.setAttribute("aria-describedby",i);t.addEventListener("mouseenter",()=>{this.#tU=setTimeout(()=>{this.#tU=null,this.#tz.classList.add("show"),this.#a._reportTelemetry({action:"alt_text_tooltip"})},100)}),t.addEventListener("mouseleave",()=>{this.#tU&&(clearTimeout(this.#tU),this.#tU=null),this.#tz?.classList.remove("show")})}e.innerText=this.#tB?await tL._l10nPromise.get("pdfjs-editor-alt-text-decorative-tooltip"):this.#tN,!e.parentNode&&t.append(e);let i=this.#a.getImageForAltText();i?.setAttribute("aria-describedby",e.id)}}class tP{#tG=null;#tN=null;#tV=!1;#tq=!1;#tW=null;#tK=null;#tX=this.focusin.bind(this);#tY=this.focusout.bind(this);#tQ=null;#tJ="";#tZ=!1;#t0=null;#t1=!1;#t2=!1;#t3=!1;#t5=null;#t6=0;#t4=0;#t8=null;_initialOptions=Object.create(null);_isVisible=!0;_uiManager=null;_focusEventsAllowed=!0;_l10nPromise=null;#t7=!1;#t9=tP._zIndex++;static _borderLineWidth=-1;static _colorManager=new tM;static _zIndex=1;static _telemetryTimeout=1e3;static get _resizerKeyboardManager(){let t=tP.prototype._resizeWithKeyboard,e=tk.TRANSLATE_SMALL,i=tk.TRANSLATE_BIG;return M(this,"_resizerKeyboardManager",new tT([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-i,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[i,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-i]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,i]}],[["Escape","mac+Escape"],tP.prototype._stopResizingWithKeyboard]]))}constructor(t){this.constructor===tP&&C("Cannot initialize AnnotationEditor."),this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null;let{rotation:e,rawDims:{pageWidth:i,pageHeight:s,pageX:a,pageY:r}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[i,s],this.pageTranslation=[a,r];let[n,o]=this.parentDimensions;this.x=t.x/n,this.y=t.y/o,this.isAttachedToDOM=!1,this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get _defaultLineColor(){return M(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){let e=new tR({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t,e,i){if(tP._l10nPromise||=new Map(["pdfjs-editor-alt-text-button-label","pdfjs-editor-alt-text-edit-button-label","pdfjs-editor-alt-text-decorative-tooltip","pdfjs-editor-resizer-label-topLeft","pdfjs-editor-resizer-label-topMiddle","pdfjs-editor-resizer-label-topRight","pdfjs-editor-resizer-label-middleRight","pdfjs-editor-resizer-label-bottomRight","pdfjs-editor-resizer-label-bottomMiddle","pdfjs-editor-resizer-label-bottomLeft","pdfjs-editor-resizer-label-middleLeft"].map(e=>[e,t.get(e.replaceAll(/([A-Z])/g,t=>`-${t.toLowerCase()}`))])),i?.strings)for(let e of i.strings)tP._l10nPromise.set(e,t.get(e));if(-1!==tP._borderLineWidth)return;let s=getComputedStyle(document.documentElement);tP._borderLineWidth=parseFloat(s.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){C("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#t7}set _isDraggable(t){this.#t7=t,this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){let[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t),this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t),this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2,this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#t9}setParent(t){null!==t?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):this.#et(),this.parent=t}focusin(t){if(!!this._focusEventsAllowed)this.#tZ?this.#tZ=!1:this.parent.setSelected(this)}focusout(t){if(!this._focusEventsAllowed||!this.isAttachedToDOM)return;let e=t.relatedTarget;if(!e?.closest(`#${this.id}`))t.preventDefault(),!this.parent?.isMultipleSelection&&this.commitOrRemove()}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,i,s){let[a,r]=this.parentDimensions;[i,s]=this.screenToPageTranslation(i,s),this.x=(t+i)/a,this.y=(e+s)/r,this.fixAndSetPosition()}#ee([t,e],i,s){[i,s]=this.screenToPageTranslation(i,s),this.x+=i/t,this.y+=s/e,this.fixAndSetPosition()}translate(t,e){this.#ee(this.parentDimensions,t,e)}translateInPage(t,e){this.#t0||=[this.x,this.y],this.#ee(this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}drag(t,e){this.#t0||=[this.x,this.y];let[i,s]=this.parentDimensions;if(this.x+=t/i,this.y+=e/s,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){let{x:t,y:e}=this.div.getBoundingClientRect();this.parent.findNewParent(this,t,e)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:a,y:r}=this,[n,o]=this.getBaseTranslation();a+=n,r+=o,this.div.style.left=`${(100*a).toFixed(2)}%`,this.div.style.top=`${(100*r).toFixed(2)}%`,this.div.scrollIntoView({block:"nearest"})}get _hasBeenMoved(){return!!this.#t0&&(this.#t0[0]!==this.x||this.#t0[1]!==this.y)}getBaseTranslation(){let[t,e]=this.parentDimensions,{_borderLineWidth:i}=tP,s=i/t,a=i/e;switch(this.rotation){case 90:return[-s,a];case 180:return[s,a];case 270:return[s,-a];default:return[-s,-a]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){let[e,i]=this.pageDimensions,{x:s,y:a,width:r,height:n}=this;if(r*=e,n*=i,s*=e,a*=i,this._mustFixPosition)switch(t){case 0:s=Math.max(0,Math.min(e-r,s)),a=Math.max(0,Math.min(i-n,a));break;case 90:s=Math.max(0,Math.min(e-n,s)),a=Math.min(i,Math.max(r,a));break;case 180:s=Math.min(e,Math.max(r,s)),a=Math.min(i,Math.max(n,a));break;case 270:s=Math.min(e,Math.max(n,s)),a=Math.max(0,Math.min(i-r,a))}this.x=s/=e,this.y=a/=i;let[o,l]=this.getBaseTranslation();s+=o,a+=l;let{style:h}=this.div;h.left=`${(100*s).toFixed(2)}%`,h.top=`${(100*a).toFixed(2)}%`,this.moveInDOM()}static #ei(t,e,i){switch(i){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return tP.#ei(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return tP.#ei(t,e,360-this.parentRotation)}#es(t){switch(t){case 90:{let[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{let[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){let{parentScale:t,pageDimensions:[e,i]}=this,s=e*t,a=i*t;return z.isCSSRoundSupported?[Math.round(s),Math.round(a)]:[s,a]}setDims(t,e){let[i,s]=this.parentDimensions;this.div.style.width=`${(100*t/i).toFixed(2)}%`,!this.#tq&&(this.div.style.height=`${(100*e/s).toFixed(2)}%`)}fixDims(){let{style:t}=this.div,{height:e,width:i}=t,s=i.endsWith("%"),a=!this.#tq&&e.endsWith("%");if(s&&a)return;let[r,n]=this.parentDimensions;!s&&(t.width=`${(100*parseFloat(i)/r).toFixed(2)}%`),!this.#tq&&!a&&(t.height=`${(100*parseFloat(e)/n).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#ea(){if(!this.#tW){for(let t of(this.#tW=document.createElement("div"),this.#tW.classList.add("resizers"),this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"])){let e=document.createElement("div");this.#tW.append(e),e.classList.add("resizer",t),e.setAttribute("data-resizer-name",t),e.addEventListener("pointerdown",this.#er.bind(this,t)),e.addEventListener("contextmenu",tp),e.tabIndex=-1}this.div.prepend(this.#tW)}}#er(t,e){e.preventDefault();let{isMac:i}=z.platform;if(0!==e.button||e.ctrlKey&&i)return;this.#tN?.toggle(!1);let s=this.#en.bind(this,t),a=this._isDraggable;this._isDraggable=!1;let r={passive:!0,capture:!0};this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",s,r),window.addEventListener("contextmenu",tp);let n=this.x,o=this.y,l=this.width,h=this.height,d=this.parent.div.style.cursor,c=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;let u=()=>{this.parent.togglePointerEvents(!0),this.#tN?.toggle(!0),this._isDraggable=a,window.removeEventListener("pointerup",u),window.removeEventListener("blur",u),window.removeEventListener("pointermove",s,r),window.removeEventListener("contextmenu",tp),this.parent.div.style.cursor=d,this.div.style.cursor=c,this.#eo(n,o,l,h)};window.addEventListener("pointerup",u),window.addEventListener("blur",u)}#eo(t,e,i,s){let a=this.x,r=this.y,n=this.width,o=this.height;if(a!==t||r!==e||n!==i||o!==s)this.addCommands({cmd:()=>{this.width=n,this.height=o,this.x=a,this.y=r;let[t,e]=this.parentDimensions;this.setDims(t*n,e*o),this.fixAndSetPosition()},undo:()=>{this.width=i,this.height=s,this.x=t,this.y=e;let[a,r]=this.parentDimensions;this.setDims(a*i,r*s),this.fixAndSetPosition()},mustExec:!0})}#en(t,e){let i,s,a,r;let[n,o]=this.parentDimensions,l=this.x,h=this.y,d=this.width,c=this.height,u=tP.MIN_SIZE/n,p=tP.MIN_SIZE/o,g=t=>Math.round(1e4*t)/1e4,f=this.#es(this.rotation),m=(t,e)=>[f[0]*t+f[2]*e,f[1]*t+f[3]*e],b=this.#es(360-this.rotation),v=!1,A=!1;switch(t){case"topLeft":v=!0,i=(t,e)=>[0,0],s=(t,e)=>[t,e];break;case"topMiddle":i=(t,e)=>[t/2,0],s=(t,e)=>[t/2,e];break;case"topRight":v=!0,i=(t,e)=>[t,0],s=(t,e)=>[0,e];break;case"middleRight":A=!0,i=(t,e)=>[t,e/2],s=(t,e)=>[0,e/2];break;case"bottomRight":v=!0,i=(t,e)=>[t,e],s=(t,e)=>[0,0];break;case"bottomMiddle":i=(t,e)=>[t/2,e],s=(t,e)=>[t/2,0];break;case"bottomLeft":v=!0,i=(t,e)=>[0,e],s=(t,e)=>[t,0];break;case"middleLeft":A=!0,i=(t,e)=>[0,e/2],s=(t,e)=>[t,e/2]}let y=i(d,c),_=s(d,c),w=m(..._),x=g(l+w[0]),E=g(h+w[1]),C=1,S=1,[T,M]=this.screenToPageTranslation(e.movementX,e.movementY);if([T,M]=(a=T/n,r=M/o,[b[0]*a+b[2]*r,b[1]*a+b[3]*r]),v){let t=Math.hypot(d,c);C=S=Math.max(Math.min(Math.hypot(_[0]-y[0]-T,_[1]-y[1]-M)/t,1/d,1/c),u/d,p/c)}else A?C=Math.max(u,Math.min(1,Math.abs(_[0]-y[0]-T)))/d:S=Math.max(p,Math.min(1,Math.abs(_[1]-y[1]-M)))/c;let k=g(d*C),L=g(c*S),P=x-(w=m(...s(k,L)))[0],R=E-w[1];this.width=k,this.height=L,this.x=P,this.y=R,this.setDims(n*k,o*L),this.fixAndSetPosition()}altTextFinish(){this.#tN?.finish()}async addEditToolbar(){return this.#tQ||this.#t2?this.#tQ:(this.#tQ=new t_(this),this.div.append(this.#tQ.render()),this.#tN&&this.#tQ.addAltTextButton(await this.#tN.render()),this.#tQ)}removeEditToolbar(){if(!!this.#tQ)this.#tQ.remove(),this.#tQ=null,this.#tN?.destroy()}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){if(!this.#tN)tL.initialize(tP._l10nPromise),this.#tN=new tL(this),await this.addEditToolbar()}get altTextData(){return this.#tN?.data}set altTextData(t){if(!!this.#tN)this.#tN.data=t}hasAltText(){return!this.#tN?.isEmpty()}render(){this.div=document.createElement("div"),this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360),this.div.className=this.name,this.div.setAttribute("id",this.id),this.div.tabIndex=this.#tV?-1:0,!this._isVisible&&this.div.classList.add("hidden"),this.setInForeground(),this.div.addEventListener("focusin",this.#tX),this.div.addEventListener("focusout",this.#tY);let[t,e]=this.parentDimensions;this.parentRotation%180!=0&&(this.div.style.maxWidth=`${(100*e/t).toFixed(2)}%`,this.div.style.maxHeight=`${(100*t/e).toFixed(2)}%`);let[i,s]=this.getInitialTranslation();return this.translate(i,s),tx(this,this.div,["pointerdown"]),this.div}pointerdown(t){let{isMac:e}=z.platform;if(0!==t.button||t.ctrlKey&&e){t.preventDefault();return}if(this.#tZ=!0,this._isDraggable){this.#el(t);return}this.#eh(t)}#eh(t){let{isMac:e}=z.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}#el(t){let e,i;let s=this._uiManager.isSelected(this);this._uiManager.setUpDragSession(),s&&(this.div.classList.add("moving"),e={passive:!0,capture:!0},this.#t6=t.clientX,this.#t4=t.clientY,i=t=>{let{clientX:e,clientY:i}=t,[s,a]=this.screenToPageTranslation(e-this.#t6,i-this.#t4);this.#t6=e,this.#t4=i,this._uiManager.dragSelectedEditors(s,a)},window.addEventListener("pointermove",i,e));let a=()=>{window.removeEventListener("pointerup",a),window.removeEventListener("blur",a),s&&(this.div.classList.remove("moving"),window.removeEventListener("pointermove",i,e)),this.#tZ=!1,!this._uiManager.endDragSession()&&this.#eh(t)};window.addEventListener("pointerup",a),window.addEventListener("blur",a)}moveInDOM(){this.#t5&&clearTimeout(this.#t5),this.#t5=setTimeout(()=>{this.#t5=null,this.parent?.moveEditorInDOM(this)},0)}_setParentAndPosition(t,e,i){t.changeParent(this),this.x=e,this.y=i,this.fixAndSetPosition()}getRect(t,e,i=this.rotation){let s=this.parentScale,[a,r]=this.pageDimensions,[n,o]=this.pageTranslation,l=t/s,h=e/s,d=this.x*a,c=this.y*r,u=this.width*a,p=this.height*r;switch(i){case 0:return[d+l+n,r-c-h-p+o,d+l+u+n,r-c-h+o];case 90:return[d+h+n,r-c+l+o,d+h+p+n,r-c+l+u+o];case 180:return[d-l-u+n,r-c+h+o,d-l+n,r-c+h+p+o];case 270:return[d-h-p+n,r-c-l-u+o,d-h+n,r-c-l+o];default:throw Error("Invalid rotation")}}getRectInCurrentCoords(t,e){let[i,s,a,r]=t,n=a-i,o=r-s;switch(this.rotation){case 0:return[i,e-r,n,o];case 90:return[i,e-s,o,n];case 180:return[a,e-s,n,o];case 270:return[a,e-r,o,n];default:throw Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){this.#t2=!0}disableEditMode(){this.#t2=!1}isInEditMode(){return this.#t2}shouldGetKeyboardEvents(){return this.#t3}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){this.div?.addEventListener("focusin",this.#tX),this.div?.addEventListener("focusout",this.#tY)}rotate(t){}serialize(t=!1,e=null){C("An editor must be serializable")}static deserialize(t,e,i){let s=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:i});s.rotation=t.rotation;let[a,r]=s.pageDimensions,[n,o,l,h]=s.getRectInCurrentCoords(t.rect,r);return s.x=n/a,s.y=o/r,s.width=l/a,s.height=h/r,s}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||null!==this.serialize())}remove(){if(this.div.removeEventListener("focusin",this.#tX),this.div.removeEventListener("focusout",this.#tY),!this.isEmpty()&&this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),this.#t5&&(clearTimeout(this.#t5),this.#t5=null),this.#et(),this.removeEditToolbar(),this.#t8){for(let t of this.#t8.values())clearTimeout(t);this.#t8=null}this.parent=null}get isResizable(){return!1}makeResizable(){this.isResizable&&(this.#ea(),this.#tW.classList.remove("hidden"),tx(this,this.div,["keydown"]))}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||"Enter"!==t.key)return;this._uiManager.setSelected(this),this.#tK={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};let e=this.#tW.children;if(!this.#tG){this.#tG=Array.from(e);let t=this.#ed.bind(this),i=this.#ec.bind(this);for(let e of this.#tG){let s=e.getAttribute("data-resizer-name");e.setAttribute("role","spinbutton"),e.addEventListener("keydown",t),e.addEventListener("blur",i),e.addEventListener("focus",this.#eu.bind(this,s)),tP._l10nPromise.get(`pdfjs-editor-resizer-label-${s}`).then(t=>e.setAttribute("aria-label",t))}}let i=this.#tG[0],s=0;for(let t of e){if(t===i)break;s++}let a=(360-this.rotation+this.parentRotation)%360/90*(this.#tG.length/4);if(a!==s){if(a<s)for(let t=0;t<s-a;t++)this.#tW.append(this.#tW.firstChild);else if(a>s)for(let t=0;t<a-s;t++)this.#tW.firstChild.before(this.#tW.lastChild);let t=0;for(let i of e){let e=this.#tG[t++].getAttribute("data-resizer-name");tP._l10nPromise.get(`pdfjs-editor-resizer-label-${e}`).then(t=>i.setAttribute("aria-label",t))}}this.#ep(0),this.#t3=!0,this.#tW.firstChild.focus({focusVisible:!0}),t.preventDefault(),t.stopImmediatePropagation()}#ed(t){tP._resizerKeyboardManager.exec(this,t)}#ec(t){this.#t3&&t.relatedTarget?.parentNode!==this.#tW&&this.#et()}#eu(t){this.#tJ=this.#t3?t:""}#ep(t){if(!!this.#tG)for(let e of this.#tG)e.tabIndex=t}_resizeWithKeyboard(t,e){if(!!this.#t3)this.#en(this.#tJ,{movementX:t,movementY:e})}#et(){if(this.#t3=!1,this.#ep(-1),this.#tK){let{savedX:t,savedY:e,savedWidth:i,savedHeight:s}=this.#tK;this.#eo(t,e,i,s),this.#tK=null}}_stopResizingWithKeyboard(){this.#et(),this.div.focus()}select(){if(this.makeResizable(),this.div?.classList.add("selectedEditor"),!this.#tQ){this.addEditToolbar().then(()=>{this.div?.classList.contains("selectedEditor")&&this.#tQ?.show()});return}this.#tQ?.show()}unselect(){this.#tW?.classList.add("hidden"),this.div?.classList.remove("selectedEditor"),this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),this.#tQ?.hide()}updateParams(t,e){}disableEditing(){}enableEditing(){}enterInEditMode(){}getImageForAltText(){return null}get contentDiv(){return this.div}get isEditing(){return this.#t1}set isEditing(t){if(this.#t1=t,!!this.parent)t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null)}setAspectRatio(t,e){this.#tq=!0;let{style:i}=this.div;i.aspectRatio=t/e,i.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){this.#t8||=new Map;let{action:e}=t,i=this.#t8.get(e);i&&clearTimeout(i),i=setTimeout(()=>{this._reportTelemetry(t),this.#t8.delete(e),0===this.#t8.size&&(this.#t8=null)},tP._telemetryTimeout),this.#t8.set(e,i);return}t.type||=this.editorType,this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),this.#tV=!1}disable(){this.div&&(this.div.tabIndex=-1),this.#tV=!0}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(e){if("CANVAS"===e.nodeName){let t=e;(e=document.createElement("div")).classList.add("annotationContent",this.editorType),t.before(e)}}else(e=document.createElement("div")).classList.add("annotationContent",this.editorType),t.container.prepend(e);return e}resetAnnotationElement(t){let{firstChild:e}=t.container;"DIV"===e.nodeName&&e.classList.contains("annotationContent")&&e.remove()}}class tR extends tP{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex}}}class tI{constructor(t){this.h1=t?0xffffffff&t:0xc3d2e1f0,this.h2=t?0xffffffff&t:0xc3d2e1f0}update(t){let e,i;if("string"==typeof t){e=new Uint8Array(2*t.length),i=0;for(let s=0,a=t.length;s<a;s++){let a=t.charCodeAt(s);a<=255?e[i++]=a:(e[i++]=a>>>8,e[i++]=255&a)}}else if(ArrayBuffer.isView(t))i=(e=t.slice()).byteLength;else throw Error("Invalid data format, must be a string or TypedArray.");let s=i>>2,a=i-4*s,r=new Uint32Array(e.buffer,0,s),n=0,o=0,l=this.h1,h=this.h2,d=11601,c=13715;for(let t=0;t<s;t++)1&t?(l^=n=0x1b873593*(n=(n=0xcc9e2d51*(n=r[t])&0xffff0000|n*d&65535)<<15|n>>>17)&0xffff0000|n*c&65535,l=5*(l=l<<13|l>>>19)+0xe6546b64):(h^=o=0x1b873593*(o=(o=0xcc9e2d51*(o=r[t])&0xffff0000|o*d&65535)<<15|o>>>17)&0xffff0000|o*c&65535,h=5*(h=h<<13|h>>>19)+0xe6546b64);switch(n=0,a){case 3:n^=e[4*s+2]<<16;case 2:n^=e[4*s+1]<<8;case 1:n^=e[4*s],n=0x1b873593*(n=(n=0xcc9e2d51*n&0xffff0000|n*d&65535)<<15|n>>>17)&0xffff0000|n*c&65535,1&s?l^=n:h^=n}this.h1=l,this.h2=h}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,e=0xff51afd7*e&0xffff0000|((e<<16|(t=0xed558ccd*t&0xffff0000|36045*t&65535)>>>16)*0xafd7ed55&0xffff0000)>>>16,t^=e>>>1,e=0xc4ceb9fe*e&0xffff0000|((e<<16|(t=0x1a85ec53*t&0xffff0000|60499*t&65535)>>>16)*0xb9fe1a85&0xffff0000)>>>16,((t^=e>>>1)>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}let tD=Object.freeze({map:null,hash:"",transfer:void 0});class tF{#eg=!1;#ef=new Map;constructor(){this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){let i=this.#ef.get(t);return void 0===i?e:Object.assign(e,i)}getRawValue(t){return this.#ef.get(t)}remove(t){if(this.#ef.delete(t),0===this.#ef.size&&this.resetModified(),"function"==typeof this.onAnnotationEditor){for(let t of this.#ef.values())if(t instanceof tP)return;this.onAnnotationEditor(null)}}setValue(t,e){let i=this.#ef.get(t),s=!1;if(void 0!==i)for(let[t,a]of Object.entries(e))i[t]!==a&&(s=!0,i[t]=a);else s=!0,this.#ef.set(t,e);s&&this.#em(),e instanceof tP&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#ef.has(t)}getAll(){return this.#ef.size>0?H(this.#ef):null}setAll(t){for(let[e,i]of Object.entries(t))this.setValue(e,i)}get size(){return this.#ef.size}#em(){!this.#eg&&(this.#eg=!0,"function"==typeof this.onSetModified&&this.onSetModified())}resetModified(){this.#eg&&(this.#eg=!1,"function"==typeof this.onResetModified&&this.onResetModified())}get print(){return new tO(this)}get serializable(){if(0===this.#ef.size)return tD;let t=new Map,e=new tI,i=[],s=Object.create(null),a=!1;for(let[i,r]of this.#ef){let n=r instanceof tP?r.serialize(!1,s):r;n&&(t.set(i,n),e.update(`${i}:${JSON.stringify(n)}`),a||=!!n.bitmap)}if(a)for(let e of t.values())e.bitmap&&i.push(e.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:i}:tD}get editorStats(){let t=null,e=new Map;for(let i of this.#ef.values()){if(!(i instanceof tP))continue;let s=i.telemetryFinalData;if(!s)continue;let{type:a}=s;!e.has(a)&&e.set(a,Object.getPrototypeOf(i).constructor),t||=Object.create(null);let r=t[a]||=new Map;for(let[t,e]of Object.entries(s)){if("type"===t)continue;let i=r.get(t);!i&&(i=new Map,r.set(t,i));let s=i.get(e)??0;i.set(e,s+1)}}for(let[i,s]of e)t[i]=s.computeTelemetryFinalData(t[i]);return t}}class tO extends tF{#eb;constructor(t){super();let{map:e,hash:i,transfer:s}=t.serializable,a=structuredClone(e,s?{transfer:s}:null);this.#eb={map:a,hash:i,transfer:s}}get print(){C("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#eb}}class tN{#ev=new Set;constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){!this.styleElement&&(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));let e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(let t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),this.#ev.clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont({systemFontInfo:t,_inspectFont:e}){if(!(!t||this.#ev.has(t.loadedName))){if(S(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){let{loadedName:i,src:s,style:a}=t,r=new FontFace(i,s,a);this.addNativeFontFace(r);try{await r.load(),this.#ev.add(i),e?.(t)}catch{E(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(r)}return}C("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo){await this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){let e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(i){throw E(`Failed to load font '${e.family}': '${i}'.`),t.disableFontFace=!0,i}}return}let e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise(e=>{let i=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,i)})}}get isFontLoadingAPISupported(){return M(this,"isFontLoadingAPISupported",!!this._document?.fonts)}get isSyncFontLoadingSupported(){let t=!1;return n?t=!0:"undefined"!=typeof navigator&&"string"==typeof navigator?.userAgent&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent)&&(t=!0),M(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){let{loadingRequests:e}=this,i={done:!1,complete:function(){for(S(!i.done,"completeRequest() cannot be called twice."),i.done=!0;e.length>0&&e[0].done;)setTimeout(e.shift().callback,0)},callback:t};return e.push(i),i}get _loadTestFont(){return M(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e){var i;let s,a;function r(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function n(t,e,i,s){let a=t.substring(0,e);return a+s+t.substring(e+i)}let o=this._document.createElement("canvas");o.width=1,o.height=1;let l=o.getContext("2d"),h=0,d=`lt${Date.now()}${this.loadTestFontId++}`,c=this._loadTestFont;c=n(c,976,d.length,d);let u=r(c,16);for(s=0,a=d.length-3;s<a;s+=4)u=u-0x58585858+r(d,s)|0;s<d.length&&(u=u-0x58585858+r(d+"XXX",s)|0),c=n(c,16,4,String.fromCharCode((i=u)>>24&255,i>>16&255,i>>8&255,255&i));let p=`url(data:font/opentype;base64,${btoa(c)});`,g=`@font-face {font-family:"${d}";src:${p}}`;this.insertRule(g);let f=this._document.createElement("div");for(let e of(f.style.visibility="hidden",f.style.width=f.style.height="10px",f.style.position="absolute",f.style.top=f.style.left="0px",[t.loadedName,d])){let t=this._document.createElement("span");t.textContent="Hi",t.style.fontFamily=e,f.append(t)}this._document.body.append(f),!function t(e,i){if(++h>30){E("Load test font never loaded."),i();return}if(l.font="30px "+e,l.fillText(".",0,20),l.getImageData(0,0,1,1).data[3]>0){i();return}setTimeout(t.bind(null,e,i))}(d,()=>{f.remove(),e.complete()})}}class tB{constructor(t,{disableFontFace:e=!1,inspectFont:i=null}){for(let e in this.compiledGlyphs=Object.create(null),t)this[e]=t[e];this.disableFontFace=!0===e,this._inspectFont=i}createNativeFontFace(){let t;if(!this.data||this.disableFontFace)return null;if(this.cssFontInfo){let e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});return this._inspectFont?.(this),t}createFontFaceRule(){let t;if(!this.data||this.disableFontFace)return null;let e=N(this.data),i=`url(data:${this.mimetype};base64,${btoa(e)});`;if(this.cssFontInfo){let e=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(e+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),t=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${e}src:${i}}`}else t=`@font-face {font-family:"${this.loadedName}";src:${i}}`;return this._inspectFont?.(this,i),t}getPathGenerator(t,e){let i;if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];try{i=t.get(this.loadedName+"_path_"+e)}catch(t){E(`getPathGenerator - ignoring character: "${t}".`)}if(!Array.isArray(i)||0===i.length)return this.compiledGlyphs[e]=function(t,e){};let s=[];for(let t=0,e=i.length;t<e;)switch(i[t++]){case W.BEZIER_CURVE_TO:{let[e,a,r,n,o,l]=i.slice(t,t+6);s.push(t=>t.bezierCurveTo(e,a,r,n,o,l)),t+=6}break;case W.MOVE_TO:{let[e,a]=i.slice(t,t+2);s.push(t=>t.moveTo(e,a)),t+=2}break;case W.LINE_TO:{let[e,a]=i.slice(t,t+2);s.push(t=>t.lineTo(e,a)),t+=2}break;case W.QUADRATIC_CURVE_TO:{let[e,a,r,n]=i.slice(t,t+4);s.push(t=>t.quadraticCurveTo(e,a,r,n)),t+=4}break;case W.RESTORE:s.push(t=>t.restore());break;case W.SAVE:s.push(t=>t.save());break;case W.SCALE:S(2===s.length,"Scale command is only valid at the third position.");break;case W.TRANSFORM:{let[e,a,r,n,o,l]=i.slice(t,t+6);s.push(t=>t.transform(e,a,r,n,o,l)),t+=6}break;case W.TRANSLATE:{let[e,a]=i.slice(t,t+2);s.push(t=>t.translate(e,a)),t+=2}}return this.compiledGlyphs[e]=function(t,e){s[0](t),s[1](t),t.scale(e,-e);for(let e=2,i=s.length;e<i;e++)s[e](t)}}}if(n){var tH=Promise.withResolvers(),tz=null;(async()=>{let t,e;let i=await import("fs"),s=await import("http"),a=await import("https");return new Map(Object.entries({fs:i,http:s,https:a,url:await import("url"),canvas:t,path2d:e}))})().then(t=>{tz=t,tH.resolve()},t=>{E(`loadPackages: ${t}`),tz=new Map,tH.resolve()})}class tU{static get promise(){return tH.promise}static get(t){return tz?.get(t)}}let tj=function(t){return tU.get("fs").promises.readFile(t).then(t=>new Uint8Array(t))},t$={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};function tG(t,e){if(!e)return;let i=e[2]-e[0],s=e[3]-e[1],a=new Path2D;a.rect(e[0],e[1],i,s),t.clip(a)}class tV{constructor(){this.constructor===tV&&C("Cannot initialize BaseShadingPattern.")}getPattern(){C("Abstract method `getPattern` called.")}}class tq extends tV{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;for(let i of("axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1)),this._colorStops))e.addColorStop(i[0],i[1]);return e}getPattern(t,e,i,s){let a;if(s===t$.STROKE||s===t$.FILL){let r=e.current.getClippedPathBoundingBox(s,tv(t))||[0,0,0,0],n=Math.ceil(r[2]-r[0])||1,o=Math.ceil(r[3]-r[1])||1,l=e.cachedCanvases.getCanvas("pattern",n,o,!0),h=l.context;h.clearRect(0,0,h.canvas.width,h.canvas.height),h.beginPath(),h.rect(0,0,h.canvas.width,h.canvas.height),h.translate(-r[0],-r[1]),i=j.transform(i,[1,0,0,1,r[0],r[1]]),h.transform(...e.baseTransform),this.matrix&&h.transform(...this.matrix),tG(h,this._bbox),h.fillStyle=this._createGradient(h),h.fill(),a=t.createPattern(l.canvas,"no-repeat");let d=new DOMMatrix(i);a.setTransform(d)}else tG(t,this._bbox),a=this._createGradient(t);return a}}function tW(t,e,i,s,a,r,n,o){let l,h,d,c,u,p,g,f,m;let b=e.coords,v=e.colors,A=t.data,y=4*t.width;b[i+1]>b[s+1]&&(l=i,i=s,s=l,l=r,r=n,n=l),b[s+1]>b[a+1]&&(l=s,s=a,a=l,l=n,n=o,o=l),b[i+1]>b[s+1]&&(l=i,i=s,s=l,l=r,r=n,n=l);let _=(b[i]+e.offsetX)*e.scaleX,w=(b[i+1]+e.offsetY)*e.scaleY,x=(b[s]+e.offsetX)*e.scaleX,E=(b[s+1]+e.offsetY)*e.scaleY,C=(b[a]+e.offsetX)*e.scaleX,S=(b[a+1]+e.offsetY)*e.scaleY;if(w>=S)return;let T=v[r],M=v[r+1],k=v[r+2],L=v[n],P=v[n+1],R=v[n+2],I=v[o],D=v[o+1],F=v[o+2],O=Math.round(w),N=Math.round(S);for(let t=O;t<=N;t++){let e;if(t<E){let e=t<w?0:(w-t)/(w-E);h=_-(_-x)*e,d=T-(T-L)*e,c=M-(M-P)*e,u=k-(k-R)*e}else{let e;h=x-(x-C)*(e=t>S?1:E===S?0:(E-t)/(E-S)),d=L-(L-I)*e,c=P-(P-D)*e,u=R-(R-F)*e}p=_-(_-C)*(e=t<w?0:t>S?1:(w-t)/(w-S)),g=T-(T-I)*e,f=M-(M-D)*e,m=k-(k-F)*e;let i=Math.round(Math.min(h,p)),s=Math.round(Math.max(h,p)),a=y*t+4*i;for(let t=i;t<=s;t++)(e=(h-t)/(h-p))<0?e=0:e>1&&(e=1),A[a++]=d-(d-g)*e|0,A[a++]=c-(c-f)*e|0,A[a++]=u-(u-m)*e|0,A[a++]=255}}class tK extends tV{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[7],this._background=t[8],this.matrix=null}_createMeshCanvas(t,e,i){let s=Math.floor(this._bounds[0]),a=Math.floor(this._bounds[1]),r=Math.ceil(this._bounds[2])-s,n=Math.ceil(this._bounds[3])-a,o=Math.min(Math.ceil(Math.abs(r*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(n*t[1]*1.1)),3e3),h=r/o,d=n/l,c={coords:this._coords,colors:this._colors,offsetX:-s,offsetY:-a,scaleX:1/h,scaleY:1/d},u=o+4,p=l+4,g=i.getCanvas("mesh",u,p,!1),f=g.context,m=f.createImageData(o,l);if(e){let t=m.data;for(let i=0,s=t.length;i<s;i+=4)t[i]=e[0],t[i+1]=e[1],t[i+2]=e[2],t[i+3]=255}for(let t of this._figures)!function(t,e,i){let s,a;let r=e.coords,n=e.colors;switch(e.type){case"lattice":let o=e.verticesPerRow,l=Math.floor(r.length/o)-1,h=o-1;for(s=0;s<l;s++){let e=s*o;for(let s=0;s<h;s++,e++)tW(t,i,r[e],r[e+1],r[e+o],n[e],n[e+1],n[e+o]),tW(t,i,r[e+o+1],r[e+1],r[e+o],n[e+o+1],n[e+1],n[e+o])}break;case"triangles":for(s=0,a=r.length;s<a;s+=3)tW(t,i,r[s],r[s+1],r[s+2],n[s],n[s+1],n[s+2]);break;default:throw Error("illegal figure")}}(m,t,c);return f.putImageData(m,2,2),{canvas:g.canvas,offsetX:s-2*h,offsetY:a-2*d,scaleX:h,scaleY:d}}getPattern(t,e,i,s){let a;if(tG(t,this._bbox),s===t$.SHADING)a=j.singularValueDecompose2dScale(tv(t));else if(a=j.singularValueDecompose2dScale(e.baseTransform),this.matrix){let t=j.singularValueDecompose2dScale(this.matrix);a=[a[0]*t[0],a[1]*t[1]]}let r=this._createMeshCanvas(a,s===t$.SHADING?null:this._background,e.cachedCanvases);return s!==t$.SHADING&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(r.offsetX,r.offsetY),t.scale(r.scaleX,r.scaleY),t.createPattern(r.canvas,"no-repeat")}}class tX extends tV{getPattern(){return"hotpink"}}let tY={COLORED:1,UNCOLORED:2};class tQ{static MAX_PATTERN_SIZE=3e3;constructor(t,e,i,s,a){this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.color=e,this.ctx=i,this.canvasGraphicsFactory=s,this.baseTransform=a}createPatternCanvas(t){let e=this.operatorList,i=this.bbox,s=this.xstep,a=this.ystep,r=this.paintType,n=this.tilingType,o=this.color,l=this.canvasGraphicsFactory;x("TilingType: "+n);let h=i[0],d=i[1],c=i[2],u=i[3],p=j.singularValueDecompose2dScale(this.matrix),g=j.singularValueDecompose2dScale(this.baseTransform),f=[p[0]*g[0],p[1]*g[1]],m=this.getSizeAndScale(s,this.ctx.canvas.width,f[0]),b=this.getSizeAndScale(a,this.ctx.canvas.height,f[1]),v=t.cachedCanvases.getCanvas("pattern",m.size,b.size,!0),A=v.context,y=l.createCanvasGraphics(A);y.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(y,r,o);let _=h,w=d,E=c,C=u;return h<0&&(_=0,E+=Math.abs(h)),d<0&&(w=0,C+=Math.abs(d)),A.translate(-(m.scale*_),-(b.scale*w)),y.transform(m.scale,0,0,b.scale,0,0),A.save(),this.clipBbox(y,_,w,E,C),y.baseTransform=tv(y.ctx),y.executeOperatorList(e),y.endDrawing(),{canvas:v.canvas,scaleX:m.scale,scaleY:b.scale,offsetX:_,offsetY:w}}getSizeAndScale(t,e,i){t=Math.abs(t);let s=Math.max(tQ.MAX_PATTERN_SIZE,e),a=Math.ceil(t*i);return a>=s?a=s:i=a/t,{scale:i,size:a}}clipBbox(t,e,i,s,a){let r=s-e,n=a-i;t.ctx.rect(e,i,r,n),t.current.updateRectMinMax(tv(t.ctx),[e,i,s,a]),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,i){let s=t.ctx,a=t.current;switch(e){case tY.COLORED:let r=this.ctx;s.fillStyle=r.fillStyle,s.strokeStyle=r.strokeStyle,a.fillColor=r.fillStyle,a.strokeColor=r.strokeStyle;break;case tY.UNCOLORED:let n=j.makeHexColor(i[0],i[1],i[2]);s.fillStyle=n,s.strokeStyle=n,a.fillColor=n,a.strokeColor=n;break;default:throw new F(`Unsupported paint type: ${e}`)}}getPattern(t,e,i,s){let a=i;s!==t$.SHADING&&(a=j.transform(a,e.baseTransform),this.matrix&&(a=j.transform(a,this.matrix)));let r=this.createPatternCanvas(e),n=new DOMMatrix(a);n=(n=n.translate(r.offsetX,r.offsetY)).scale(1/r.scaleX,1/r.scaleY);let o=t.createPattern(r.canvas,"repeat");return o.setTransform(n),o}}class tJ{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,i){let s;return void 0!==this.cache[t]?(s=this.cache[t],this.canvasFactory.reset(s,e,i)):(s=this.canvasFactory.create(e,i),this.cache[t]=s),s}delete(t){delete this.cache[t]}clear(){for(let t in this.cache){let e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function tZ(t,e,i,s,a,r,n,o,l,h){let[d,c,u,p,g,f]=tv(t);if(0===c&&0===u){let m=Math.round(n*d+g),b=Math.round(o*p+f),v=Math.abs(Math.round((n+l)*d+g)-m)||1,A=Math.abs(Math.round((o+h)*p+f)-b)||1;return t.setTransform(Math.sign(d),0,0,Math.sign(p),m,b),t.drawImage(e,i,s,a,r,0,0,v,A),t.setTransform(d,c,u,p,g,f),[v,A]}if(0===d&&0===p){let m=Math.round(o*u+g),b=Math.round(n*c+f),v=Math.abs(Math.round((o+h)*u+g)-m)||1,A=Math.abs(Math.round((n+l)*c+f)-b)||1;return t.setTransform(0,Math.sign(c),Math.sign(u),0,m,b),t.drawImage(e,i,s,a,r,0,0,A,v),t.setTransform(d,c,u,p,g,f),[A,v]}return t.drawImage(e,i,s,a,r,n,o,l,h),[Math.hypot(d,c)*l,Math.hypot(u,p)*h]}class t0{constructor(t,e){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=o,this.textMatrixScale=1,this.fontMatrix=l,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=g.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.transferMaps="none",this.startNewPathAndClipBox([0,0,t,e])}clone(){let t=Object.create(this);return t.clipBox=this.clipBox.slice(),t}setCurrentPoint(t,e){this.x=t,this.y=e}updatePathMinMax(t,e,i){[e,i]=j.applyTransform([e,i],t),this.minX=Math.min(this.minX,e),this.minY=Math.min(this.minY,i),this.maxX=Math.max(this.maxX,e),this.maxY=Math.max(this.maxY,i)}updateRectMinMax(t,e){let i=j.applyTransform(e,t),s=j.applyTransform(e.slice(2),t),a=j.applyTransform([e[0],e[3]],t),r=j.applyTransform([e[2],e[1]],t);this.minX=Math.min(this.minX,i[0],s[0],a[0],r[0]),this.minY=Math.min(this.minY,i[1],s[1],a[1],r[1]),this.maxX=Math.max(this.maxX,i[0],s[0],a[0],r[0]),this.maxY=Math.max(this.maxY,i[1],s[1],a[1],r[1])}updateScalingPathMinMax(t,e){j.scaleMinMax(t,e),this.minX=Math.min(this.minX,e[0]),this.minY=Math.min(this.minY,e[1]),this.maxX=Math.max(this.maxX,e[2]),this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,i,s,a,r,n,o,l,h){let d=j.bezierBoundingBox(e,i,s,a,r,n,o,l,h);if(!h)this.updateRectMinMax(t,d)}getPathBoundingBox(t=t$.FILL,e=null){let i=[this.minX,this.minY,this.maxX,this.maxY];if(t===t$.STROKE){!e&&C("Stroke bounding box must include transform.");let t=j.singularValueDecompose2dScale(e),s=t[0]*this.lineWidth/2,a=t[1]*this.lineWidth/2;i[0]-=s,i[1]-=a,i[2]+=s,i[3]+=a}return i}updateClipFromPath(){let t=j.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t,this.minX=1/0,this.minY=1/0,this.maxX=0,this.maxY=0}getClippedPathBoundingBox(t=t$.FILL,e=null){return j.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function t1(t,e){let i,s,a,r;if("undefined"!=typeof ImageData&&e instanceof ImageData){t.putImageData(e,0,0);return}let n=e.height,o=e.width,l=n%16,h=(n-l)/16,d=0===l?h:h+1,c=t.createImageData(o,16),u=0,p,g=e.data,m=c.data;if(e.kind===f.GRAYSCALE_1BPP){let e=g.byteLength,r=new Uint32Array(m.buffer,0,m.byteLength>>2),n=r.length,f=o+7>>3,b=z.isLittleEndian?0xff000000:255;for(i=0;i<d;i++){for(s=0,a=i<h?16:l,p=0;s<a;s++){let t=e-u,i=0,s=t>f?o:8*t-7,a=-8&s,n=0,l=0;for(;i<a;i+=8)l=g[u++],r[p++]=128&l?0xffffffff:b,r[p++]=64&l?0xffffffff:b,r[p++]=32&l?0xffffffff:b,r[p++]=16&l?0xffffffff:b,r[p++]=8&l?0xffffffff:b,r[p++]=4&l?0xffffffff:b,r[p++]=2&l?0xffffffff:b,r[p++]=1&l?0xffffffff:b;for(;i<s;i++)0===n&&(l=g[u++],n=128),r[p++]=l&n?0xffffffff:b,n>>=1}for(;p<n;)r[p++]=0;t.putImageData(c,0,16*i)}}else if(e.kind===f.RGBA_32BPP){for(i=0,s=0,r=64*o;i<h;i++)m.set(g.subarray(u,u+r)),u+=r,t.putImageData(c,0,s),s+=16;i<d&&(r=o*l*4,m.set(g.subarray(u,u+r)),t.putImageData(c,0,s))}else if(e.kind===f.RGB_24BPP)for(i=0,r=o*(a=16);i<d;i++){for(i>=h&&(r=o*(a=l)),p=0,s=r;s--;)m[p++]=g[u++],m[p++]=g[u++],m[p++]=g[u++],m[p++]=255;t.putImageData(c,0,16*i)}else throw Error(`bad image kind: ${e.kind}`)}function t2(t,e){if(e.bitmap){t.drawImage(e.bitmap,0,0);return}let i=e.height,s=e.width,a=i%16,r=(i-a)/16,n=0===a?r:r+1,o=t.createImageData(s,16),l=0,h=e.data,d=o.data;for(let e=0;e<n;e++){let i=e<r?16:a;({srcPos:l}=function({src:t,srcPos:e=0,dest:i,width:s,height:a,nonBlackColor:r=0xffffffff,inverseDecode:n=!1}){let o=z.isLittleEndian?0xff000000:255,[l,h]=n?[r,o]:[o,r],d=s>>3,c=7&s,u=t.length;i=new Uint32Array(i.buffer);let p=0;for(let s=0;s<a;s++){for(let s=e+d;e<s;e++){let s=e<u?t[e]:255;i[p++]=128&s?h:l,i[p++]=64&s?h:l,i[p++]=32&s?h:l,i[p++]=16&s?h:l,i[p++]=8&s?h:l,i[p++]=4&s?h:l,i[p++]=2&s?h:l,i[p++]=1&s?h:l}if(0===c)continue;let s=e<u?t[e++]:255;for(let t=0;t<c;t++)i[p++]=s&1<<7-t?h:l}return{srcPos:e,destPos:p}}({src:h,srcPos:l,dest:d,width:s,height:i,nonBlackColor:0})),t.putImageData(o,0,16*e)}}function t3(t,e){for(let i of["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"])void 0!==t[i]&&(e[i]=t[i]);void 0!==t.setLineDash&&(e.setLineDash(t.getLineDash()),e.lineDashOffset=t.lineDashOffset)}function t5(t){if(t.strokeStyle=t.fillStyle="#000000",t.fillRule="nonzero",t.globalAlpha=1,t.lineWidth=1,t.lineCap="butt",t.lineJoin="miter",t.miterLimit=10,t.globalCompositeOperation="source-over",t.font="10px sans-serif",void 0!==t.setLineDash&&(t.setLineDash([]),t.lineDashOffset=0),!n){let{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}}function t6(t,e){if(e)return!0;let i=j.singularValueDecompose2dScale(t);i[0]=Math.fround(i[0]),i[1]=Math.fround(i[1]);let s=Math.fround((globalThis.devicePixelRatio||1)*tt.PDF_TO_CSS_UNITS);return i[0]<=s&&i[1]<=s}let t4=["butt","round","square"],t8=["miter","round","bevel"],t7={},t9={};class et{constructor(t,e,i,s,a,{optionalContentConfig:r,markedContentStack:n=null},o,l){this.ctx=t,this.current=new t0(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=i,this.canvasFactory=s,this.filterFactory=a,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=n||[],this.optionalContentConfig=r,this.cachedCanvases=new tJ(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=o,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=l,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:i=!1,background:s=null}){let a=this.ctx.canvas.width,r=this.ctx.canvas.height,n=this.ctx.fillStyle;if(this.ctx.fillStyle=s||"#ffffff",this.ctx.fillRect(0,0,a,r),this.ctx.fillStyle=n,i){let t=this.cachedCanvases.getCanvas("transparent",a,r);this.compositeCtx=this.ctx,this.transparentCanvas=t.canvas,this.ctx=t.context,this.ctx.save(),this.ctx.transform(...tv(this.compositeCtx))}this.ctx.save(),t5(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=tv(this.ctx)}executeOperatorList(t,e,i,s){let a;let r=t.argsArray,n=t.fnArray,o=e||0,l=r.length;if(l===o)return o;let h=l-o>10&&"function"==typeof i,d=h?Date.now()+15:0,c=0,u=this.commonObjs,p=this.objs;for(;;){if(void 0!==s&&o===s.nextBreakPoint)return s.breakIt(o,i),o;if((a=n[o])!==y.dependency)this[a].apply(this,r[o]);else for(let t of r[o]){let e=t.startsWith("g_")?u:p;if(!e.has(t))return e.get(t,i),o}if(++o===l)return o;if(h&&++c>10){if(Date.now()>d)return i(),o;c=0}}}#eA(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)}endDrawing(){for(let t of(this.#eA(),this.cachedCanvases.clear(),this.cachedPatterns.clear(),this._cachedBitmapsMap.values())){for(let e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),this.#ey()}#ey(){if(this.pageColors){let t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){let e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}}_scaleImage(t,e){let i,s;let a=t.width,r=t.height,n=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=a,h=r,d="prescale1";for(;n>2&&l>1||o>2&&h>1;){let e=l,a=h;n>2&&l>1&&(e=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2),n/=l/e),o>2&&h>1&&(a=h>=16384?Math.floor(h/2)-1||1:Math.ceil(h)/2,o/=h/a),(s=(i=this.cachedCanvases.getCanvas(d,e,a)).context).clearRect(0,0,e,a),s.drawImage(t,0,0,l,h,0,0,e,a),t=i.canvas,l=e,h=a,d="prescale1"===d?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:h}}_createMaskCanvas(t){let e,i,s,a;let r=this.ctx,{width:n,height:o}=t,l=this.current.fillColor,h=this.current.patternFill,d=tv(r);if((t.bitmap||t.data)&&t.count>1){let a=t.bitmap||t.data.buffer;i=JSON.stringify(h?d:[d.slice(0,4),l]),!(e=this._cachedBitmapsMap.get(a))&&(e=new Map,this._cachedBitmapsMap.set(a,e));let r=e.get(i);if(r&&!h){let t=Math.round(Math.min(d[0],d[2])+d[4]);return{canvas:r,offsetX:t,offsetY:Math.round(Math.min(d[1],d[3])+d[5])}}s=r}!s&&t2((a=this.cachedCanvases.getCanvas("maskCanvas",n,o)).context,t);let c=j.transform(d,[1/n,0,0,-1/o,0,0]);c=j.transform(c,[1,0,0,1,0,-o]);let[u,p,g,f]=j.getAxialAlignedBoundingBox([0,0,n,o],c),m=Math.round(g-u)||1,b=Math.round(f-p)||1,v=this.cachedCanvases.getCanvas("fillCanvas",m,b),A=v.context;A.translate(-u,-p),A.transform(...c),!s&&(s=(s=this._scaleImage(a.canvas,tA(A))).img,e&&h&&e.set(i,s)),A.imageSmoothingEnabled=t6(tv(A),t.interpolate),tZ(A,s,0,0,s.width,s.height,0,0,n,o),A.globalCompositeOperation="source-in";let y=j.transform(tA(A),[1,0,0,1,-u,-p]);return A.fillStyle=h?l.getPattern(r,this,y,t$.FILL):l,A.fillRect(0,0,n,o),e&&!h&&(this.cachedCanvases.delete("fillCanvas"),e.set(i,v.canvas)),{canvas:v.canvas,offsetX:Math.round(u),offsetY:Math.round(p)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=t4[t]}setLineJoin(t){this.ctx.lineJoin=t8[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){let i=this.ctx;void 0!==i.setLineDash&&(i.setLineDash(t),i.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(let[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=i;break;case"ca":this.current.fillAlpha=i,this.ctx.globalAlpha=i;break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(i)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){let t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw Error("beginSMaskMode called while already in smask mode");let t=this.ctx.canvas.width,e=this.ctx.canvas.height,i="smaskGroupAt"+this.groupLevel,s=this.cachedCanvases.getCanvas(i,t,e);this.suspendedCtx=this.ctx,this.ctx=s.context;let a=this.ctx;a.setTransform(...tv(this.suspendedCtx)),t3(this.suspendedCtx,a),!function(t,e){if(t._removeMirroring)throw Error("Context is already forwarding operations.");t.__originalSave=t.save,t.__originalRestore=t.restore,t.__originalRotate=t.rotate,t.__originalScale=t.scale,t.__originalTranslate=t.translate,t.__originalTransform=t.transform,t.__originalSetTransform=t.setTransform,t.__originalResetTransform=t.resetTransform,t.__originalClip=t.clip,t.__originalMoveTo=t.moveTo,t.__originalLineTo=t.lineTo,t.__originalBezierCurveTo=t.bezierCurveTo,t.__originalRect=t.rect,t.__originalClosePath=t.closePath,t.__originalBeginPath=t.beginPath,t._removeMirroring=()=>{t.save=t.__originalSave,t.restore=t.__originalRestore,t.rotate=t.__originalRotate,t.scale=t.__originalScale,t.translate=t.__originalTranslate,t.transform=t.__originalTransform,t.setTransform=t.__originalSetTransform,t.resetTransform=t.__originalResetTransform,t.clip=t.__originalClip,t.moveTo=t.__originalMoveTo,t.lineTo=t.__originalLineTo,t.bezierCurveTo=t.__originalBezierCurveTo,t.rect=t.__originalRect,t.closePath=t.__originalClosePath,t.beginPath=t.__originalBeginPath,delete t._removeMirroring},t.save=function(){e.save(),this.__originalSave()},t.restore=function(){e.restore(),this.__originalRestore()},t.translate=function(t,i){e.translate(t,i),this.__originalTranslate(t,i)},t.scale=function(t,i){e.scale(t,i),this.__originalScale(t,i)},t.transform=function(t,i,s,a,r,n){e.transform(t,i,s,a,r,n),this.__originalTransform(t,i,s,a,r,n)},t.setTransform=function(t,i,s,a,r,n){e.setTransform(t,i,s,a,r,n),this.__originalSetTransform(t,i,s,a,r,n)},t.resetTransform=function(){e.resetTransform(),this.__originalResetTransform()},t.rotate=function(t){e.rotate(t),this.__originalRotate(t)},t.clip=function(t){e.clip(t),this.__originalClip(t)},t.moveTo=function(t,i){e.moveTo(t,i),this.__originalMoveTo(t,i)},t.lineTo=function(t,i){e.lineTo(t,i),this.__originalLineTo(t,i)},t.bezierCurveTo=function(t,i,s,a,r,n){e.bezierCurveTo(t,i,s,a,r,n),this.__originalBezierCurveTo(t,i,s,a,r,n)},t.rect=function(t,i,s,a){e.rect(t,i,s,a),this.__originalRect(t,i,s,a)},t.closePath=function(){e.closePath(),this.__originalClosePath()},t.beginPath=function(){e.beginPath(),this.__originalBeginPath()}}(a,this.suspendedCtx),this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),t3(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];let e=this.current.activeSMask,i=this.suspendedCtx;this.composeSMask(i,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,i,s){let a=s[0],r=s[1],n=s[2]-a,o=s[3]-r;if(0!==n&&0!==o)this.genericComposeSMask(e.context,i,n,o,e.subtype,e.backdrop,e.transferMap,a,r,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(i.canvas,0,0),t.restore()}genericComposeSMask(t,e,i,s,a,r,n,o,l,h,d){let c=t.canvas,u=o-h,p=l-d;if(r){if(u<0||p<0||u+i>c.width||p+s>c.height){let t=this.cachedCanvases.getCanvas("maskExtension",i,s),e=t.context;e.drawImage(c,-u,-p),r.some(t=>0!==t)&&(e.globalCompositeOperation="destination-atop",e.fillStyle=j.makeHexColor(...r),e.fillRect(0,0,i,s),e.globalCompositeOperation="source-over"),c=t.canvas,u=p=0}else if(r.some(t=>0!==t)){t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);let e=new Path2D;e.rect(u,p,i,s),t.clip(e),t.globalCompositeOperation="destination-atop",t.fillStyle=j.makeHexColor(...r),t.fillRect(u,p,i,s),t.restore()}}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),"Alpha"===a&&n?e.filter=this.filterFactory.addAlphaFilter(n):"Luminosity"===a&&(e.filter=this.filterFactory.addLuminosityFilter(n));let g=new Path2D;g.rect(o,l,i,s),e.clip(g),e.globalCompositeOperation="destination-in",e.drawImage(c,u,p,i,s,o,l,i,s),e.restore()}save(){this.inSMaskMode?(t3(this.ctx,this.suspendedCtx),this.suspendedCtx.save()):this.ctx.save();let t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){0===this.stateStack.length&&this.inSMaskMode&&this.endSMaskMode(),0!==this.stateStack.length&&(this.current=this.stateStack.pop(),this.inSMaskMode?(this.suspendedCtx.restore(),t3(this.suspendedCtx,this.ctx)):this.ctx.restore(),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null)}transform(t,e,i,s,a,r){this.ctx.transform(t,e,i,s,a,r),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,i){let s,a;let r=this.ctx,n=this.current,o=n.x,l=n.y,h=tv(r),d=0===h[0]&&0===h[3]||0===h[1]&&0===h[2],c=d?i.slice(0):null;for(let i=0,u=0,p=t.length;i<p;i++)switch(0|t[i]){case y.rectangle:o=e[u++],l=e[u++];let p=e[u++],g=e[u++],f=o+p,m=l+g;r.moveTo(o,l),0===p||0===g?r.lineTo(f,m):(r.lineTo(f,l),r.lineTo(f,m),r.lineTo(o,m)),!d&&n.updateRectMinMax(h,[o,l,f,m]),r.closePath();break;case y.moveTo:o=e[u++],l=e[u++],r.moveTo(o,l),!d&&n.updatePathMinMax(h,o,l);break;case y.lineTo:o=e[u++],l=e[u++],r.lineTo(o,l),!d&&n.updatePathMinMax(h,o,l);break;case y.curveTo:s=o,a=l,o=e[u+4],l=e[u+5],r.bezierCurveTo(e[u],e[u+1],e[u+2],e[u+3],o,l),n.updateCurvePathMinMax(h,s,a,e[u],e[u+1],e[u+2],e[u+3],o,l,c),u+=6;break;case y.curveTo2:s=o,a=l,r.bezierCurveTo(o,l,e[u],e[u+1],e[u+2],e[u+3]),n.updateCurvePathMinMax(h,s,a,o,l,e[u],e[u+1],e[u+2],e[u+3],c),o=e[u+2],l=e[u+3],u+=4;break;case y.curveTo3:s=o,a=l,o=e[u+2],l=e[u+3],r.bezierCurveTo(e[u],e[u+1],o,l,o,l),n.updateCurvePathMinMax(h,s,a,e[u],e[u+1],o,l,o,l,c),u+=4;break;case y.closePath:r.closePath()}d&&n.updateScalingPathMinMax(h,c),n.setCurrentPoint(o,l)}closePath(){this.ctx.closePath()}stroke(t=!0){let e=this.ctx,i=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha,this.contentVisible&&("object"==typeof i&&i?.getPattern?(e.save(),e.strokeStyle=i.getPattern(e,this,tA(e),t$.STROKE),this.rescaleAndStroke(!1),e.restore()):this.rescaleAndStroke(!0)),t&&this.consumePath(this.current.getClippedPathBoundingBox()),e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath(),this.stroke()}fill(t=!0){let e=this.ctx,i=this.current.fillColor,s=this.current.patternFill,a=!1;s&&(e.save(),e.fillStyle=i.getPattern(e,this,tA(e),t$.FILL),a=!0);let r=this.current.getClippedPathBoundingBox();this.contentVisible&&null!==r&&(this.pendingEOFill?(e.fill("evenodd"),this.pendingEOFill=!1):e.fill()),a&&e.restore(),t&&this.consumePath(r)}eoFill(){this.pendingEOFill=!0,this.fill()}fillStroke(){this.fill(!1),this.stroke(!1),this.consumePath()}eoFillStroke(){this.pendingEOFill=!0,this.fillStroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=t7}eoClip(){this.pendingClip=t9}beginText(){this.current.textMatrix=o,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){let t=this.pendingTextPaths,e=this.ctx;if(void 0===t){e.beginPath();return}for(let i of(e.save(),e.beginPath(),t))e.setTransform(...i.transform),e.translate(i.x,i.y),i.addToPath(e,i.fontSize);e.restore(),e.clip(),e.beginPath(),delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){let i=this.commonObjs.get(t),s=this.current;if(!i)throw Error(`Can't find font for ${t}`);if(s.fontMatrix=i.fontMatrix||l,(0===s.fontMatrix[0]||0===s.fontMatrix[3])&&E("Invalid font matrix for font "+t),e<0?(e=-e,s.fontDirection=-1):s.fontDirection=1,this.current.font=i,this.current.fontSize=e,i.isType3Font)return;let a=i.loadedName||"sans-serif",r=i.systemFontInfo?.css||`"${a}", ${i.fallbackName}`,n="normal";i.black?n="900":i.bold&&(n="bold");let o=i.italic?"italic":"normal",h=e;e<16?h=16:e>100&&(h=100),this.current.fontSizeScale=e/h,this.ctx.font=`${o} ${n} ${h}px ${r}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t,e,i,s,a,r){this.current.textMatrix=[t,e,i,s,a,r],this.current.textMatrixScale=Math.hypot(t,e),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,i,s){let a;let r=this.ctx,n=this.current,o=n.font,l=n.textRenderingMode,h=n.fontSize/n.fontSizeScale,d=l&g.FILL_STROKE_MASK,c=!!(l&g.ADD_TO_PATH_FLAG),u=n.patternFill&&!o.missingFile;(o.disableFontFace||c||u)&&(a=o.getPathGenerator(this.commonObjs,t)),o.disableFontFace||u?(r.save(),r.translate(e,i),r.beginPath(),a(r,h),s&&r.setTransform(...s),(d===g.FILL||d===g.FILL_STROKE)&&r.fill(),(d===g.STROKE||d===g.FILL_STROKE)&&r.stroke(),r.restore()):((d===g.FILL||d===g.FILL_STROKE)&&r.fillText(t,e,i),(d===g.STROKE||d===g.FILL_STROKE)&&r.strokeText(t,e,i)),c&&(this.pendingTextPaths||=[]).push({transform:tv(r),x:e,y:i,fontSize:h,addToPath:a})}get isFontSubpixelAAEnabled(){let{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);let e=t.getImageData(0,0,10,10).data,i=!1;for(let t=3;t<e.length;t+=4)if(e[t]>0&&e[t]<255){i=!0;break}return M(this,"isFontSubpixelAAEnabled",i)}showText(t){let e;let i=this.current,s=i.font;if(s.isType3Font)return this.showType3Text(t);let a=i.fontSize;if(0===a)return;let r=this.ctx,n=i.fontSizeScale,o=i.charSpacing,l=i.wordSpacing,h=i.fontDirection,d=i.textHScale*h,c=t.length,u=s.vertical,p=u?1:-1,f=s.defaultVMetrics,m=a*i.fontMatrix[0],b=i.textRenderingMode===g.FILL&&!s.disableFontFace&&!i.patternFill;if(r.save(),r.transform(...i.textMatrix),r.translate(i.x,i.y+i.textRise),h>0?r.scale(d,-1):r.scale(d,1),i.patternFill){r.save();let t=i.fillColor.getPattern(r,this,tA(r),t$.FILL);e=tv(r),r.restore(),r.fillStyle=t}let v=i.lineWidth,A=i.textMatrixScale;if(0===A||0===v){let t=i.textRenderingMode&g.FILL_STROKE_MASK;(t===g.STROKE||t===g.FILL_STROKE)&&(v=this.getSinglePixelWidth())}else v/=A;if(1!==n&&(r.scale(n,n),v/=n),r.lineWidth=v,s.isInvalidPDFjsFont){let e=[],s=0;for(let i of t)e.push(i.unicode),s+=i.width;r.fillText(e.join(""),0,0),i.x+=s*m*d,r.restore(),this.compose();return}let y=0,_;for(_=0;_<c;++_){let i,d;let c=t[_];if("number"==typeof c){y+=p*c*a/1e3;continue}let g=!1,v=(c.isSpace?l:0)+o,A=c.fontChar,w=c.accent,x=c.width;if(u){let t=c.vmetric||f,e=-(c.vmetric?t[1]:.5*x)*m,s=t[2]*m;x=t?-t[0]:x,i=e/n,d=(y+s)/n}else i=y/n,d=0;if(s.remeasure&&x>0){let t=1e3*r.measureText(A).width/a*n;if(x<t&&this.isFontSubpixelAAEnabled){let e=x/t;g=!0,r.save(),r.scale(e,1),i/=e}else x!==t&&(i+=(x-t)/2e3*a/n)}if(this.contentVisible&&(c.isInFont||s.missingFile)){if(b&&!w)r.fillText(A,i,d);else if(this.paintChar(A,i,d,e),w){let t=i+a*w.offset.x/n,s=d-a*w.offset.y/n;this.paintChar(w.fontChar,t,s,e)}}y+=u?x*m-v*h:x*m+v*h,g&&r.restore()}u?i.y-=y:i.x+=y*d,r.restore(),this.compose()}showType3Text(t){let e,i,s,a;let r=this.ctx,n=this.current,o=n.font,h=n.fontSize,d=n.fontDirection,c=o.vertical?1:-1,u=n.charSpacing,p=n.wordSpacing,f=n.textHScale*d,m=n.fontMatrix||l,b=t.length;if(n.textRenderingMode!==g.INVISIBLE&&0!==h){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,r.save(),r.transform(...n.textMatrix),r.translate(n.x,n.y),r.scale(f,d),e=0;e<b;++e){if("number"==typeof(i=t[e])){a=c*i*h/1e3,this.ctx.translate(a,0),n.x+=a*f;continue}let l=(i.isSpace?p:0)+u,d=o.charProcOperatorList[i.operatorListId];if(!d){E(`Type3 character "${i.operatorListId}" is not available.`);continue}this.contentVisible&&(this.processingType3=i,this.save(),r.scale(h,h),r.transform(...m),this.executeOperatorList(d),this.restore()),s=j.applyTransform([i.width,0],m)[0]*h+l,r.translate(s,0),n.x+=s*f}r.restore(),this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,i,s,a,r){this.ctx.rect(i,s,a-i,r-s),this.ctx.clip(),this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){let i=t[1],s=this.baseTransform||tv(this.ctx);e=new tQ(t,i,this.ctx,{createCanvasGraphics:t=>new et(t,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})},s)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t,e,i){let s=j.makeHexColor(t,e,i);this.ctx.strokeStyle=s,this.current.strokeColor=s}setFillRGBColor(t,e,i){let s=j.makeHexColor(t,e,i);this.ctx.fillStyle=s,this.current.fillColor=s,this.current.patternFill=!1}_getPattern(t,e=null){let i;return this.cachedPatterns.has(t)?i=this.cachedPatterns.get(t):(i=function(t){switch(t[0]){case"RadialAxial":return new tq(t);case"Mesh":return new tK(t);case"Dummy":return new tX}throw Error(`Unknown IR type: ${t[0]}`)}(this.getObject(t)),this.cachedPatterns.set(t,i)),e&&(i.matrix=e),i}shadingFill(t){if(!this.contentVisible)return;let e=this.ctx;this.save();let i=this._getPattern(t);e.fillStyle=i.getPattern(e,this,tA(e),t$.SHADING);let s=tA(e);if(s){let{width:t,height:i}=e.canvas,[a,r,n,o]=j.getAxialAlignedBoundingBox([0,0,t,i],s);this.ctx.fillRect(a,r,n-a,o-r)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){C("Should not call beginInlineImage")}beginImageData(){C("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(!!this.contentVisible){if(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=tv(this.ctx),e){let t=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],t,i),this.current.updateRectMinMax(tv(this.ctx),e),this.clip(),this.endPath()}}}paintFormXObjectEnd(){if(!!this.contentVisible)this.restore(),this.baseTransform=this.baseTransformStack.pop()}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);let e=this.ctx;!t.isolated&&x("TODO: Support non-isolated groups."),t.knockout&&E("Knockout groups not supported.");let i=tv(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw Error("Bounding box is required.");let s=j.getAxialAlignedBoundingBox(t.bbox,tv(e)),a=[0,0,e.canvas.width,e.canvas.height],r=Math.floor((s=j.intersect(s,a)||[0,0,0,0])[0]),n=Math.floor(s[1]),o=Math.max(Math.ceil(s[2])-r,1),l=Math.max(Math.ceil(s[3])-n,1);this.current.startNewPathAndClipBox([0,0,o,l]);let h="groupAt"+this.groupLevel;t.smask&&(h+="_smask_"+this.smaskCounter++%2);let d=this.cachedCanvases.getCanvas(h,o,l),c=d.context;c.translate(-r,-n),c.transform(...i),t.smask?this.smaskStack.push({canvas:d.canvas,context:c,offsetX:r,offsetY:n,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(r,n),e.save()),t3(e,c),this.ctx=c,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;let e=this.ctx,i=this.groupStack.pop();if(this.ctx=i,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();let t=tv(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...t);let i=j.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t);this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(i)}}beginAnnotation(t,e,i,s,a){if(this.#eA(),t5(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){let s=e[2]-e[0],r=e[3]-e[1];if(a&&this.annotationCanvasMap){i=i.slice(),i[4]-=e[0],i[5]-=e[1],(e=e.slice())[0]=e[1]=0,e[2]=s,e[3]=r;let[a,n]=j.singularValueDecompose2dScale(tv(this.ctx)),{viewportScale:o}=this,l=Math.ceil(s*this.outputScaleX*o),h=Math.ceil(r*this.outputScaleY*o);this.annotationCanvas=this.canvasFactory.create(l,h);let{canvas:d,context:c}=this.annotationCanvas;this.annotationCanvasMap.set(t,d),this.annotationCanvas.savedCtx=this.ctx,this.ctx=c,this.ctx.save(),this.ctx.setTransform(a,0,0,-n,0,r*n),t5(this.ctx)}else t5(this.ctx),this.ctx.rect(e[0],e[1],s,r),this.ctx.clip(),this.endPath()}this.current=new t0(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...i),this.transform(...s)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),this.#ey(),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;let e=t.count;(t=this.getObject(t.data,t)).count=e;let i=this.ctx,s=this.processingType3;if(s&&(void 0===s.compiled&&(s.compiled=function(t){let e,i,s;let{width:a,height:r}=t;if(a>1e3||r>1e3)return null;let n=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),o=a+1,l=new Uint8Array(o*(r+1)),h=a+7&-8,d=new Uint8Array(h*r),c=0;for(let e of t.data){let t=128;for(;t>0;)d[c++]=e&t?0:255,t>>=1}let u=0;for(0!==d[c=0]&&(l[0]=1,++u),i=1;i<a;i++)d[c]!==d[c+1]&&(l[i]=d[c]?2:1,++u),c++;for(0!==d[c]&&(l[i]=2,++u),e=1;e<r;e++){c=e*h,s=e*o,d[c-h]!==d[c]&&(l[s]=d[c]?1:8,++u);let t=(d[c]?4:0)+(d[c-h]?8:0);for(i=1;i<a;i++)n[t=(t>>2)+(d[c+1]?4:0)+(d[c-h+1]?8:0)]&&(l[s+i]=n[t],++u),c++;if(d[c-h]!==d[c]&&(l[s+i]=d[c]?2:4,++u),u>1e3)return null}for(c=h*(r-1),s=e*o,0!==d[c]&&(l[s]=8,++u),i=1;i<a;i++)d[c]!==d[c+1]&&(l[s+i]=d[c]?4:8,++u),c++;if(0!==d[c]&&(l[s+i]=4,++u),u>1e3)return null;let p=new Int32Array([0,o,-1,0,-o,0,0,0,1]),g=new Path2D;for(e=0;u&&e<=r;e++){let t=e*o,i=t+a;for(;t<i&&!l[t];)t++;if(t===i)continue;g.moveTo(t%o,e);let s=t,r=l[t];do{let e=p[r];do t+=e;while(!l[t]);let i=l[t];5!==i&&10!==i?(r=i,l[t]=0):(r=i&51*r>>4,l[t]&=r>>2|r<<2),g.lineTo(t%o,t/o|0),!l[t]&&--u}while(s!==t);--e}return d=null,l=null,function(t){t.save(),t.scale(1/a,-1/r),t.translate(0,-r),t.fill(g),t.beginPath(),t.restore()}}(t)),s.compiled)){s.compiled(i);return}let a=this._createMaskCanvas(t),r=a.canvas;i.save(),i.setTransform(1,0,0,1,0,0),i.drawImage(r,a.offsetX,a.offsetY),i.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,i=0,s=0,a,r){if(!this.contentVisible)return;t=this.getObject(t.data,t);let n=this.ctx;n.save();let o=tv(n);n.transform(e,i,s,a,0,0);let l=this._createMaskCanvas(t);n.setTransform(1,0,0,1,l.offsetX-o[4],l.offsetY-o[5]);for(let t=0,h=r.length;t<h;t+=2){let h=j.transform(o,[e,i,s,a,r[t],r[t+1]]),[d,c]=j.applyTransform([0,0],h);n.drawImage(l.canvas,d,c)}n.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;let e=this.ctx,i=this.current.fillColor,s=this.current.patternFill;for(let a of t){let{data:t,width:r,height:n,transform:o}=a,l=this.cachedCanvases.getCanvas("maskCanvas",r,n),h=l.context;h.save(),t2(h,this.getObject(t,a)),h.globalCompositeOperation="source-in",h.fillStyle=s?i.getPattern(h,this,tA(e),t$.FILL):i,h.fillRect(0,0,r,n),h.restore(),e.save(),e.transform(...o),e.scale(1,-1),tZ(e,l.canvas,0,0,r,n,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;let e=this.getObject(t);if(!e){E("Dependent image isn't ready yet");return}this.paintInlineImageXObject(e)}paintImageXObjectRepeat(t,e,i,s){if(!this.contentVisible)return;let a=this.getObject(t);if(!a){E("Dependent image isn't ready yet");return}let r=a.width,n=a.height,o=[];for(let t=0,a=s.length;t<a;t+=2)o.push({transform:[e,0,0,i,s[t],s[t+1]],x:0,y:0,w:r,h:n});this.paintInlineImageXObjectGroup(a,o)}applyTransferMapsToCanvas(t){return"none"!==this.current.transferMaps&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;let{bitmap:e,width:i,height:s}=t,a=this.cachedCanvases.getCanvas("inlineImage",i,s),r=a.context;return r.filter=this.current.transferMaps,r.drawImage(e,0,0),r.filter="none",a.canvas}paintInlineImageXObject(t){let e;if(!this.contentVisible)return;let i=t.width,s=t.height,a=this.ctx;if(this.save(),!n){let{filter:t}=a;"none"!==t&&""!==t&&(a.filter="none")}if(a.scale(1/i,-1/s),t.bitmap)e=this.applyTransferMapsToBitmap(t);else if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)e=t;else{let a=this.cachedCanvases.getCanvas("inlineImage",i,s).context;t1(a,t),e=this.applyTransferMapsToCanvas(a)}let r=this._scaleImage(e,tA(a));a.imageSmoothingEnabled=t6(tv(a),t.interpolate),tZ(a,r.img,0,0,r.paintWidth,r.paintHeight,0,-s,i,s),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){let i;if(!this.contentVisible)return;let s=this.ctx;if(t.bitmap)i=t.bitmap;else{let e=t.width,s=t.height,a=this.cachedCanvases.getCanvas("inlineImage",e,s).context;t1(a,t),i=this.applyTransferMapsToCanvas(a)}for(let t of e)s.save(),s.transform(...t.transform),s.scale(1,-1),tZ(s,i,t.x,t.y,t.w,t.h,0,-1,1,1),s.restore();this.compose()}paintSolidColorImageMask(){if(!!this.contentVisible)this.ctx.fillRect(0,0,1,1),this.compose()}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){let e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),!this.pendingClip&&this.compose(t);let i=this.ctx;this.pendingClip&&(!e&&(this.pendingClip===t9?i.clip("evenodd"):i.clip()),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox),i.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){let t=tv(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{let e=Math.abs(t[0]*t[3]-t[2]*t[1]),i=Math.hypot(t[0],t[2]),s=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(i,s)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){let t,e;let{lineWidth:i}=this.current,{a:s,b:a,c:r,d:n}=this.ctx.getTransform();if(0===a&&0===r){let a=Math.abs(s),r=Math.abs(n);if(a===r){if(0===i)t=e=1/a;else{let s=a*i;t=e=s<1?1/s:1}}else if(0===i)t=1/a,e=1/r;else{let s=a*i,n=r*i;t=s<1?1/s:1,e=n<1?1/n:1}}else{let o=Math.abs(s*n-a*r),l=Math.hypot(s,a),h=Math.hypot(r,n);if(0===i)t=h/o,e=l/o;else{let s=i*o;t=h>s?h/s:1,e=l>s?l/s:1}}this._cachedScaleForStroking[0]=t,this._cachedScaleForStroking[1]=e}return this._cachedScaleForStroking}rescaleAndStroke(t){let{ctx:e}=this,{lineWidth:i}=this.current,[s,a]=this.getScaleForStroking();if(e.lineWidth=i||1,1===s&&1===a){e.stroke();return}let r=e.getLineDash();if(t&&e.save(),e.scale(s,a),r.length>0){let t=Math.max(s,a);e.setLineDash(r.map(e=>e/t)),e.lineDashOffset/=t}e.stroke(),t&&e.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}for(let t in y)void 0!==et.prototype[t]&&(et.prototype[y[t]]=et.prototype[t]);class ee{static #e_=null;static #ew="";static get workerPort(){return this.#e_}static set workerPort(t){if(!("undefined"!=typeof Worker&&t instanceof Worker)&&null!==t)throw Error("Invalid `workerPort` type.");this.#e_=t}static get workerSrc(){return this.#ew}static set workerSrc(t){if("string"!=typeof t)throw Error("Invalid `workerSrc` type.");this.#ew=t}}let ei={DATA:1,ERROR:2},es={CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function ea(t){switch(!(t instanceof Error||"object"==typeof t&&null!==t)&&C('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),t.name){case"AbortException":return new O(t.message);case"MissingPDFException":return new I(t.message);case"PasswordException":return new L(t.message,t.code);case"UnexpectedResponseException":return new D(t.message,t.status);case"UnknownErrorException":return new P(t.message,t.details);default:return new P(t.message,t.toString())}}class er{constructor(t,e,i){this.sourceName=t,this.targetName=e,this.comObj=i,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),this._onComObjOnMessage=t=>{let e=t.data;if(e.targetName!==this.sourceName)return;if(e.stream){this.#ex(e);return}if(e.callback){let t=e.callbackId,i=this.callbackCapabilities[t];if(!i)throw Error(`Cannot resolve callback ${t}`);if(delete this.callbackCapabilities[t],e.callback===ei.DATA)i.resolve(e.data);else if(e.callback===ei.ERROR)i.reject(ea(e.reason));else throw Error("Unexpected callback case");return}let s=this.actionHandler[e.action];if(!s)throw Error(`Unknown action from worker: ${e.action}`);if(e.callbackId){let t=this.sourceName,a=e.sourceName;new Promise(function(t){t(s(e.data))}).then(function(s){i.postMessage({sourceName:t,targetName:a,callback:ei.DATA,callbackId:e.callbackId,data:s})},function(s){i.postMessage({sourceName:t,targetName:a,callback:ei.ERROR,callbackId:e.callbackId,reason:ea(s)})});return}if(e.streamId){this.#eE(e);return}s(e.data)},i.addEventListener("message",this._onComObjOnMessage)}on(t,e){let i=this.actionHandler;if(i[t])throw Error(`There is already an actionName called "${t}"`);i[t]=e}send(t,e,i){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},i)}sendWithPromise(t,e,i){let s=this.callbackId++,a=Promise.withResolvers();this.callbackCapabilities[s]=a;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:s,data:e},i)}catch(t){a.reject(t)}return a.promise}sendWithStream(t,e,i,s){let a=this.streamId++,r=this.sourceName,n=this.targetName,o=this.comObj;return new ReadableStream({start:i=>{let l=Promise.withResolvers();return this.streamControllers[a]={controller:i,startCall:l,pullCall:null,cancelCall:null,isClosed:!1},o.postMessage({sourceName:r,targetName:n,action:t,streamId:a,data:e,desiredSize:i.desiredSize},s),l.promise},pull:t=>{let e=Promise.withResolvers();return this.streamControllers[a].pullCall=e,o.postMessage({sourceName:r,targetName:n,stream:es.PULL,streamId:a,desiredSize:t.desiredSize}),e.promise},cancel:t=>{S(t instanceof Error,"cancel must have a valid reason");let e=Promise.withResolvers();return this.streamControllers[a].cancelCall=e,this.streamControllers[a].isClosed=!0,o.postMessage({sourceName:r,targetName:n,stream:es.CANCEL,streamId:a,reason:ea(t)}),e.promise}},i)}#eE(t){let e=t.streamId,i=this.sourceName,s=t.sourceName,a=this.comObj,r=this,n=this.actionHandler[t.action],o={enqueue(t,r=1,n){if(this.isCancelled)return;let o=this.desiredSize;this.desiredSize-=r,o>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),a.postMessage({sourceName:i,targetName:s,stream:es.ENQUEUE,streamId:e,chunk:t},n)},close(){if(!this.isCancelled)this.isCancelled=!0,a.postMessage({sourceName:i,targetName:s,stream:es.CLOSE,streamId:e}),delete r.streamSinks[e]},error(t){if(S(t instanceof Error,"error must have a valid reason"),!this.isCancelled)this.isCancelled=!0,a.postMessage({sourceName:i,targetName:s,stream:es.ERROR,streamId:e,reason:ea(t)})},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};o.sinkCapability.resolve(),o.ready=o.sinkCapability.promise,this.streamSinks[e]=o,new Promise(function(e){e(n(t.data,o))}).then(function(){a.postMessage({sourceName:i,targetName:s,stream:es.START_COMPLETE,streamId:e,success:!0})},function(t){a.postMessage({sourceName:i,targetName:s,stream:es.START_COMPLETE,streamId:e,reason:ea(t)})})}#ex(t){let e=t.streamId,i=this.sourceName,s=t.sourceName,a=this.comObj,r=this.streamControllers[e],n=this.streamSinks[e];switch(t.stream){case es.START_COMPLETE:t.success?r.startCall.resolve():r.startCall.reject(ea(t.reason));break;case es.PULL_COMPLETE:t.success?r.pullCall.resolve():r.pullCall.reject(ea(t.reason));break;case es.PULL:if(!n){a.postMessage({sourceName:i,targetName:s,stream:es.PULL_COMPLETE,streamId:e,success:!0});break}n.desiredSize<=0&&t.desiredSize>0&&n.sinkCapability.resolve(),n.desiredSize=t.desiredSize,new Promise(function(t){t(n.onPull?.())}).then(function(){a.postMessage({sourceName:i,targetName:s,stream:es.PULL_COMPLETE,streamId:e,success:!0})},function(t){a.postMessage({sourceName:i,targetName:s,stream:es.PULL_COMPLETE,streamId:e,reason:ea(t)})});break;case es.ENQUEUE:if(S(r,"enqueue should have stream controller"),r.isClosed)break;r.controller.enqueue(t.chunk);break;case es.CLOSE:if(S(r,"close should have stream controller"),r.isClosed)break;r.isClosed=!0,r.controller.close(),this.#eC(r,e);break;case es.ERROR:S(r,"error should have stream controller"),r.controller.error(ea(t.reason)),this.#eC(r,e);break;case es.CANCEL_COMPLETE:t.success?r.cancelCall.resolve():r.cancelCall.reject(ea(t.reason)),this.#eC(r,e);break;case es.CANCEL:if(!n)break;new Promise(function(e){e(n.onCancel?.(ea(t.reason)))}).then(function(){a.postMessage({sourceName:i,targetName:s,stream:es.CANCEL_COMPLETE,streamId:e,success:!0})},function(t){a.postMessage({sourceName:i,targetName:s,stream:es.CANCEL_COMPLETE,streamId:e,reason:ea(t)})}),n.sinkCapability.reject(ea(t.reason)),n.isCancelled=!0,delete this.streamSinks[e];break;default:throw Error("Unexpected stream case")}}async #eC(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]),delete this.streamControllers[e]}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}class en{#eS;#eT;constructor({parsedData:t,rawData:e}){this.#eS=t,this.#eT=e}getRaw(){return this.#eT}get(t){return this.#eS.get(t)??null}getAll(){return H(this.#eS)}has(t){return this.#eS.has(t)}}let eo=Symbol("INTERNAL");class el{#eM=!1;#ek=!1;#eL=!1;#eP=!0;constructor(t,{name:e,intent:i,usage:s}){this.#eM=!!(t&h.DISPLAY),this.#ek=!!(t&h.PRINT),this.name=e,this.intent=i,this.usage=s}get visible(){if(this.#eL)return this.#eP;if(!this.#eP)return!1;let{print:t,view:e}=this.usage;return this.#eM?e?.viewState!=="OFF":!this.#ek||t?.printState!=="OFF"}_setVisible(t,e,i=!1){t!==eo&&C("Internal method `_setVisible` called."),this.#eL=i,this.#eP=e}}class eh{#eR=null;#eI=new Map;#eD=null;#eF=null;constructor(t,e=h.DISPLAY){if(this.renderingIntent=e,this.name=null,this.creator=null,null===t)return;for(let i of(this.name=t.name,this.creator=t.creator,this.#eF=t.order,t.groups))this.#eI.set(i.id,new el(e,i));if("OFF"===t.baseState)for(let t of this.#eI.values())t._setVisible(eo,!1);for(let e of t.on)this.#eI.get(e)._setVisible(eo,!0);for(let e of t.off)this.#eI.get(e)._setVisible(eo,!1);this.#eD=this.getHash()}#eO(t){let e=t.length;if(e<2)return!0;let i=t[0];for(let s=1;s<e;s++){let e;let a=t[s];if(Array.isArray(a))e=this.#eO(a);else{if(!this.#eI.has(a))return E(`Optional content group not found: ${a}`),!0;e=this.#eI.get(a).visible}switch(i){case"And":if(!e)return!1;break;case"Or":if(e)return!0;break;case"Not":return!e;default:return!0}}return"And"===i}isVisible(t){if(0===this.#eI.size)return!0;if(!t)return x("Optional content group not defined."),!0;if("OCG"===t.type)return this.#eI.has(t.id)?this.#eI.get(t.id).visible:(E(`Optional content group not found: ${t.id}`),!0);if("OCMD"===t.type){if(t.expression)return this.#eO(t.expression);if(t.policy&&"AnyOn"!==t.policy){if("AllOn"===t.policy){for(let e of t.ids){if(!this.#eI.has(e)){E(`Optional content group not found: ${e}`);break}if(!this.#eI.get(e).visible)return!1}return!0}else if("AnyOff"===t.policy){for(let e of t.ids){if(!this.#eI.has(e))return E(`Optional content group not found: ${e}`),!0;if(!this.#eI.get(e).visible)return!0}return!1}else if("AllOff"===t.policy){for(let e of t.ids){if(!this.#eI.has(e)){E(`Optional content group not found: ${e}`);break}if(this.#eI.get(e).visible)return!1}return!0}}else{for(let e of t.ids){if(!this.#eI.has(e))return E(`Optional content group not found: ${e}`),!0;if(this.#eI.get(e).visible)return!0}return!1}return E(`Unknown optional content policy ${t.policy}.`),!0}return E(`Unknown group type ${t.type}.`),!0}setVisibility(t,e=!0){let i=this.#eI.get(t);if(!i){E(`Optional content group not found: ${t}`);return}i._setVisible(eo,!!e,!0),this.#eR=null}setOCGState({state:t,preserveRB:e}){let i;for(let e of t){switch(e){case"ON":case"OFF":case"Toggle":i=e;continue}let t=this.#eI.get(e);if(!!t)switch(i){case"ON":t._setVisible(eo,!0);break;case"OFF":t._setVisible(eo,!1);break;case"Toggle":t._setVisible(eo,!t.visible)}}this.#eR=null}get hasInitialVisibility(){return null===this.#eD||this.getHash()===this.#eD}getOrder(){return this.#eI.size?this.#eF?this.#eF.slice():[...this.#eI.keys()]:null}getGroups(){return this.#eI.size>0?H(this.#eI):null}getGroup(t){return this.#eI.get(t)||null}getHash(){if(null!==this.#eR)return this.#eR;let t=new tI;for(let[e,i]of this.#eI)t.update(`${e}:${i.visible}`);return this.#eR=t.hexdigest()}}class ed{constructor(t,{disableRange:e=!1,disableStream:i=!1}){S(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');let{length:s,initialData:a,progressiveDone:r,contentDispositionFilename:n}=t;if(this._queuedChunks=[],this._progressiveDone=r,this._contentDispositionFilename=n,a?.length>0){let t=a instanceof Uint8Array&&a.byteLength===a.buffer.byteLength?a.buffer:new Uint8Array(a).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!i,this._isRangeSupported=!e,this._contentLength=s,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener((t,e)=>{this._onReceiveData({begin:t,chunk:e})}),t.addProgressListener((t,e)=>{this._onProgress({loaded:t,total:e})}),t.addProgressiveReadListener(t=>{this._onReceiveData({chunk:t})}),t.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),t.transportReady()}_onReceiveData({begin:t,chunk:e}){let i=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;void 0===t?this._fullRequestReader?this._fullRequestReader._enqueue(i):this._queuedChunks.push(i):S(this._rangeReaders.some(function(e){return e._begin===t&&(e._enqueue(i),!0)}),"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){void 0===t.total?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){let e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){S(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");let t=this._queuedChunks;return this._queuedChunks=null,new ec(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;let i=new eu(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(i),i}cancelAllRequests(t){for(let e of(this._fullRequestReader?.cancel(t),this._rangeReaders.slice(0)))e.cancel(t);this._pdfDataRangeTransport.abort()}}class ec{constructor(t,e,i=!1,s=null){for(let a of(this._stream=t,this._done=i||!1,this._filename=tl(s)?s:null,this._queuedChunks=e||[],this._loaded=0,this._queuedChunks))this._loaded+=a.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){if(!this._done)this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunks.push(t),this._loaded+=t.byteLength}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};let t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){for(let t of(this._done=!0,this._requests))t.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){if(!this._done)this._done=!0}}class eu{constructor(t,e,i){this._stream=t,this._begin=e,this._end=i,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{for(let e of(this._requests.shift().resolve({value:t,done:!1}),this._requests))e.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){let t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};let t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){for(let t of(this._done=!0,this._requests))t.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function ep({getResponseHeader:t,isHttp:e,rangeChunkSize:i,disableRange:s}){let a={allowRangeRequests:!1,suggestedLength:void 0},r=parseInt(t("Content-Length"),10);return Number.isInteger(r)?(a.suggestedLength=r,r<=2*i||s||!e||"bytes"!==t("Accept-Ranges")||"identity"!==(t("Content-Encoding")||"identity"))?a:(a.allowRangeRequests=!0,a):a}function eg(t){let e=t("Content-Disposition");if(e){let t=function(t){let e=!0,i=s("filename\\*","i").exec(t);if(i){let t=n(i=i[1]);return r(t=l(t=o(t=unescape(t))))}if(i=function(t){let e;let i=[],a=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(e=a.exec(t));){let[,t,s,a]=e;if((t=parseInt(t,10))in i){if(0===t)break;continue}i[t]=[s,a]}let r=[];for(let t=0;t<i.length&&t in i;++t){;let[e,s]=i[t];s=n(s),e&&(s=unescape(s),0===t&&(s=o(s))),r.push(s)}return r.join("")}(t))return r(l(i));if(i=s("filename","i").exec(t)){let t=n(i=i[1]);return r(t=l(t))}function s(t,e){return RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function a(t,i){if(t){if(!/^[\x00-\xFF]+$/.test(i))return i;try{let s=new TextDecoder(t,{fatal:!0}),a=B(i);i=s.decode(a),e=!1}catch{}}return i}function r(t){return e&&/[\x80-\xff]/.test(t)&&(t=a("utf-8",t),e&&(t=a("iso-8859-1",t))),t}function n(t){if(t.startsWith('"')){let e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){let i=e[t].indexOf('"');-1!==i&&(e[t]=e[t].slice(0,i),e.length=t+1),e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function o(t){let e=t.indexOf("'");if(-1===e)return t;let i=t.slice(0,e);return a(i,t.slice(e+1).replace(/^[^']*'/,""))}function l(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(t,e,i,s){if("q"===i||"Q"===i)return a(e,s=(s=s.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,function(t,e){return String.fromCharCode(parseInt(e,16))}));try{s=atob(s)}catch{}return a(e,s)})}return""}(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if(tl(t))return t}return null}function ef(t,e){return 404===t||0===t&&e.startsWith("file:")?new I('Missing PDF "'+e+'".'):new D(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t)}function em(t){return 200===t||206===t}function eb(t,e,i){return{method:"GET",headers:t,signal:i.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function ev(t){let e=new Headers;for(let i in t){let s=t[i];if(void 0!==s)e.append(i,s)}return e}function eA(t){return t instanceof Uint8Array?t.buffer:t instanceof ArrayBuffer?t:(E(`getArrayBuffer - unexpected data format: ${t}`),new Uint8Array(t).buffer)}class ey{constructor(t){this.source=t,this.isHttp=/^https?:/i.test(t.url),this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return S(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new e_(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;let i=new ew(this,t,e);return this._rangeRequestReaders.push(i),i}cancelAllRequests(t){for(let e of(this._fullRequestReader?.cancel(t),this._rangeRequestReaders.slice(0)))e.cancel(t)}}class e_{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;let e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._headers=ev(this._stream.httpHeaders);let i=e.url;fetch(i,eb(this._headers,this._withCredentials,this._abortController)).then(t=>{if(!em(t.status))throw ef(t.status,i);this._reader=t.body.getReader(),this._headersCapability.resolve();let e=e=>t.headers.get(e),{allowRangeRequests:s,suggestedLength:a}=ep({getResponseHeader:e,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=s,this._contentLength=a||this._contentLength,this._filename=eg(e),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new O("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;let{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:eA(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class ew{constructor(t,e,i){this._stream=t,this._reader=null,this._loaded=0;let s=t.source;this._withCredentials=s.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!s.disableStream,this._abortController=new AbortController,this._headers=ev(this._stream.httpHeaders),this._headers.append("Range",`bytes=${e}-${i-1}`);let a=s.url;fetch(a,eb(this._headers,this._withCredentials,this._abortController)).then(t=>{if(!em(t.status))throw ef(t.status,a);this._readCapability.resolve(),this._reader=t.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;let{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded}),{value:eA(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class ex{constructor(t,e={}){this.url=t,this.isHttp=/^https?:/i.test(t),this.httpHeaders=this.isHttp&&e.httpHeaders||Object.create(null),this.withCredentials=e.withCredentials||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}requestRange(t,e,i){let s={begin:t,end:e};for(let t in i)s[t]=i[t];return this.request(s)}requestFull(t){return this.request(t)}request(t){let e=new XMLHttpRequest,i=this.currXhrId++,s=this.pendingRequests[i]={xhr:e};for(let t in e.open("GET",this.url),e.withCredentials=this.withCredentials,this.httpHeaders){let i=this.httpHeaders[t];if(void 0!==i)e.setRequestHeader(t,i)}return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),s.expectedStatus=206):s.expectedStatus=200,e.responseType="arraybuffer",t.onError&&(e.onerror=function(i){t.onError(e.status)}),e.onreadystatechange=this.onStateChange.bind(this,i),e.onprogress=this.onProgress.bind(this,i),s.onHeadersReceived=t.onHeadersReceived,s.onDone=t.onDone,s.onError=t.onError,s.onProgress=t.onProgress,e.send(null),i}onProgress(t,e){let i=this.pendingRequests[t];if(!!i)i.onProgress?.(e)}onStateChange(t,e){let i=this.pendingRequests[t];if(!i)return;let s=i.xhr;if(s.readyState>=2&&i.onHeadersReceived&&(i.onHeadersReceived(),delete i.onHeadersReceived),4!==s.readyState||!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],0===s.status&&this.isHttp){i.onError?.(s.status);return}let a=s.status||200;if(!(200===a&&206===i.expectedStatus)&&a!==i.expectedStatus){i.onError?.(s.status);return}let r=function(t){let e=t.response;return"string"!=typeof e?e:B(e).buffer}(s);if(206===a){let t=s.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);i.onDone({begin:parseInt(e[1],10),chunk:r})}else r?i.onDone({begin:0,chunk:r}):i.onError?.(s.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){let e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class eE{constructor(t){this._source=t,this._manager=new ex(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials}),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){let e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return S(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new eC(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){let i=new eS(this._manager,t,e);return i.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(i),i}cancelAllRequests(t){for(let e of(this._fullRequestReader?.cancel(t),this._rangeRequestReaders.slice(0)))e.cancel(t)}}class eC{constructor(t,e){this._manager=t;let i={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=e.url,this._fullRequestId=t.requestFull(i),this._headersReceivedCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){let t=this._fullRequestId,e=this._manager.getRequestXhr(t),i=t=>e.getResponseHeader(t),{allowRangeRequests:s,suggestedLength:a}=ep({getResponseHeader:i,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});s&&(this._isRangeSupported=!0),this._contentLength=a||this._contentLength,this._filename=eg(i),this._isRangeSupported&&this._manager.abortRequest(t),this._headersReceivedCapability.resolve()}_onDone(t){if(t&&(this._requests.length>0?this._requests.shift().resolve({value:t.chunk,done:!1}):this._cachedChunks.push(t.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(let t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){for(let e of(this._storedError=ef(t,this._url),this._headersReceivedCapability.reject(this._storedError),this._requests))e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};let t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){for(let e of(this._done=!0,this._headersReceivedCapability.reject(t),this._requests))e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class eS{constructor(t,e,i){this._manager=t;let s={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url,this._requestId=t.requestRange(e,i,s),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_close(){this.onClosed?.(this)}_onDone(t){let e=t.chunk;for(let t of(this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._queuedChunk=e,this._done=!0,this._requests))t.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){for(let e of(this._storedError=ef(t,this._url),this._requests))e.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){!this.isStreamingSupported&&this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){let t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};let t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){for(let t of(this._done=!0,this._requests))t.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}let eT=/^file:\/\/\/[a-zA-Z]:\//;class eM{constructor(t){this.source=t,this.url=function(t){let e=tU.get("url"),i=e.parse(t);return"file:"===i.protocol||i.host?i:/^[a-z]:[/\\]/i.test(t)?e.parse(`file:///${t}`):(!i.host&&(i.protocol="file:"),i)}(t.url),this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol,this.isFsUrl="file:"===this.url.protocol,this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return S(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=this.isFsUrl?new eD(this):new eR(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;let i=this.isFsUrl?new eF(this,t,e):new eI(this,t,e);return this._rangeRequestReaders.push(i),i}cancelAllRequests(t){for(let e of(this._fullRequestReader?.cancel(t),this._rangeRequestReaders.slice(0)))e.cancel(t)}}class ek{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;let e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers()}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;let t=this._readableStream.read();return null===t?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",t=>{this._error(t)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new O("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class eL{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();let e=t.source;this._isStreamingSupported=!e.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;let t=this._readableStream.read();return null===t?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,this.onProgress?.({loaded:this._loaded}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",t=>{this._error(t)}),this._storedError&&this._readableStream.destroy(this._storedError)}}function eP(t,e){return{protocol:t.protocol,auth:t.auth,host:t.hostname,port:t.port,path:t.path,method:"GET",headers:e}}class eR extends ek{constructor(t){super(t);let e=e=>{if(404===e.statusCode){let t=new I(`Missing PDF "${this._url}".`);this._storedError=t,this._headersCapability.reject(t);return}this._headersCapability.resolve(),this._setReadableStream(e);let i=t=>this._readableStream.headers[t.toLowerCase()],{allowRangeRequests:s,suggestedLength:a}=ep({getResponseHeader:i,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=s,this._contentLength=a||this._contentLength,this._filename=eg(i)};if(this._request=null,"http:"===this._url.protocol){let i=tU.get("http");this._request=i.request(eP(this._url,t.httpHeaders),e)}else{let i=tU.get("https");this._request=i.request(eP(this._url,t.httpHeaders),e)}this._request.on("error",t=>{this._storedError=t,this._headersCapability.reject(t)}),this._request.end()}}class eI extends eL{constructor(t,e,i){for(let e in super(t),this._httpHeaders={},t.httpHeaders){let i=t.httpHeaders[e];if(void 0!==i)this._httpHeaders[e]=i}this._httpHeaders.Range=`bytes=${e}-${i-1}`;let s=t=>{if(404===t.statusCode){let t=new I(`Missing PDF "${this._url}".`);this._storedError=t;return}this._setReadableStream(t)};if(this._request=null,"http:"===this._url.protocol){let t=tU.get("http");this._request=t.request(eP(this._url,this._httpHeaders),s)}else{let t=tU.get("https");this._request=t.request(eP(this._url,this._httpHeaders),s)}this._request.on("error",t=>{this._storedError=t}),this._request.end()}}class eD extends ek{constructor(t){super(t);let e=decodeURIComponent(this._url.path);eT.test(this._url.href)&&(e=e.replace(/^\//,""));let i=tU.get("fs");i.promises.lstat(e).then(t=>{this._contentLength=t.size,this._setReadableStream(i.createReadStream(e)),this._headersCapability.resolve()},t=>{"ENOENT"===t.code&&(t=new I(`Missing PDF "${e}".`)),this._storedError=t,this._headersCapability.reject(t)})}}class eF extends eL{constructor(t,e,i){super(t);let s=decodeURIComponent(this._url.path);eT.test(this._url.href)&&(s=s.replace(/^\//,""));let a=tU.get("fs");this._setReadableStream(a.createReadStream(s,{start:e,end:i-1}))}}class eO{#eN=Promise.withResolvers();#tm=null;#eB=!1;#eH=!!globalThis.FontInspector?.enabled;#ez=null;#eU=null;#ej=0;#e$=0;#eG=null;#eV=null;#eq=0;#eW=0;#eK=Object.create(null);#eX=[];#eY=null;#eQ=[];#eJ=new WeakMap;#eZ=null;static #e0=new Map;static #e1=new Map;static #e2=new Set;constructor({textContentSource:t,container:e,viewport:i}){if(t instanceof ReadableStream)this.#eY=t;else if("object"==typeof t)this.#eY=new ReadableStream({start(e){e.enqueue(t),e.close()}});else throw Error('No "textContentSource" parameter specified.');this.#tm=this.#eV=e,this.#eW=i.scale*(globalThis.devicePixelRatio||1),this.#eq=i.rotation,this.#eU={prevFontSize:null,prevFontFamily:null,div:null,properties:null,ctx:null};let{pageWidth:s,pageHeight:a,pageX:r,pageY:n}=i.rawDims;this.#eZ=[1,0,0,-1,-r,n+a],this.#e$=s,this.#ej=a,ty(e,i),this.#eN.promise.catch(()=>{}).then(()=>{eO.#e2.delete(this),this.#eU=null,this.#eK=null})}render(){let t=()=>{this.#eG.read().then(({value:e,done:i})=>{if(i){this.#eN.resolve();return}this.#ez??=e.lang,Object.assign(this.#eK,e.styles),this.#e3(e.items),t()},this.#eN.reject)};return this.#eG=this.#eY.getReader(),eO.#e2.add(this),t(),this.#eN.promise}update({viewport:t,onBefore:e=null}){let i=t.scale*(globalThis.devicePixelRatio||1),s=t.rotation;if(s!==this.#eq&&(e?.(),this.#eq=s,ty(this.#eV,{rotation:s})),i!==this.#eW){e?.(),this.#eW=i;let t={prevFontSize:null,prevFontFamily:null,div:null,properties:null,ctx:eO.#e5(this.#ez)};for(let e of this.#eQ)t.properties=this.#eJ.get(e),t.div=e,this.#e6(t)}}cancel(){let t=new O("TextLayer task cancelled.");this.#eG?.cancel(t).catch(()=>{}),this.#eG=null,this.#eN.reject(t)}get textDivs(){return this.#eQ}get textContentItemsStr(){return this.#eX}#e3(t){if(this.#eB)return;this.#eU.ctx||=eO.#e5(this.#ez);let e=this.#eQ,i=this.#eX;for(let s of t){if(e.length>1e5){E("Ignoring additional textDivs for performance reasons."),this.#eB=!0;return}if(void 0===s.str){if("beginMarkedContentProps"===s.type||"beginMarkedContent"===s.type){let t=this.#tm;this.#tm=document.createElement("span"),this.#tm.classList.add("markedContent"),null!==s.id&&this.#tm.setAttribute("id",`${s.id}`),t.append(this.#tm)}else"endMarkedContent"===s.type&&(this.#tm=this.#tm.parentNode);continue}i.push(s.str),this.#e4(s)}}#e4(t){let e,i;let s=document.createElement("span"),a={angle:0,canvasWidth:0,hasText:""!==t.str,hasEOL:t.hasEOL,fontSize:0};this.#eQ.push(s);let r=j.transform(this.#eZ,t.transform),n=Math.atan2(r[1],r[0]),o=this.#eK[t.fontName];o.vertical&&(n+=Math.PI/2);let l=this.#eH&&o.fontSubstitution||o.fontFamily,h=Math.hypot(r[2],r[3]),d=h*eO.#e8(l,this.#ez);0===n?(e=r[4],i=r[5]-d):(e=r[4]+d*Math.sin(n),i=r[5]-d*Math.cos(n));let c="calc(var(--scale-factor)*",u=s.style;this.#tm===this.#eV?(u.left=`${(100*e/this.#e$).toFixed(2)}%`,u.top=`${(100*i/this.#ej).toFixed(2)}%`):(u.left=`${c}${e.toFixed(2)}px)`,u.top=`${c}${i.toFixed(2)}px)`),u.fontSize=`${c}${h.toFixed(2)}px)`,u.fontFamily=l,a.fontSize=h,s.setAttribute("role","presentation"),s.textContent=t.str,s.dir=t.dir,this.#eH&&(s.dataset.fontName=o.fontSubstitutionLoadedName||t.fontName),0!==n&&(a.angle=180/Math.PI*n);let p=!1;if(t.str.length>1)p=!0;else if(" "!==t.str&&t.transform[0]!==t.transform[3]){let e=Math.abs(t.transform[0]),i=Math.abs(t.transform[3]);e!==i&&Math.max(e,i)/Math.min(e,i)>1.5&&(p=!0)}if(p&&(a.canvasWidth=o.vertical?t.height:t.width),this.#eJ.set(s,a),this.#eU.div=s,this.#eU.properties=a,this.#e6(this.#eU),a.hasText&&this.#tm.append(s),a.hasEOL){let t=document.createElement("br");t.setAttribute("role","presentation"),this.#tm.append(t)}}#e6(t){let{div:e,properties:i,ctx:s,prevFontSize:a,prevFontFamily:r}=t,{style:n}=e,o="";if(0!==i.canvasWidth&&i.hasText){let{fontFamily:l}=n,{canvasWidth:h,fontSize:d}=i;(a!==d||r!==l)&&(s.font=`${d*this.#eW}px ${l}`,t.prevFontSize=d,t.prevFontFamily=l);let{width:c}=s.measureText(e.textContent);c>0&&(o=`scaleX(${h*this.#eW/c})`)}0!==i.angle&&(o=`rotate(${i.angle}deg) ${o}`),o.length>0&&(n.transform=o)}static cleanup(){if(!(this.#e2.size>0)){for(let{canvas:t}of(this.#e0.clear(),this.#e1.values()))t.remove();this.#e1.clear()}}static #e5(t=null){let e=this.#e1.get(t||="");if(!e){let i=document.createElement("canvas");i.className="hiddenCanvasElement",i.lang=t,document.body.append(i),e=i.getContext("2d",{alpha:!1}),this.#e1.set(t,e)}return e}static #e8(t,e){let i=this.#e0.get(t);if(i)return i;let s=this.#e5(e),a=s.font;s.canvas.width=s.canvas.height=30,s.font=`30px ${t}`;let r=s.measureText(""),n=r.fontBoundingBoxAscent,o=Math.abs(r.fontBoundingBoxDescent);if(n){let e=n/(n+o);return this.#e0.set(t,e),s.canvas.width=s.canvas.height=0,s.font=a,e}s.strokeStyle="red",s.clearRect(0,0,30,30),s.strokeText("g",0,0);let l=s.getImageData(0,0,30,30).data;o=0;for(let t=l.length-1-3;t>=0;t-=4)if(l[t]>0){o=Math.ceil(t/4/30);break}s.clearRect(0,0,30,30),s.strokeText("A",0,30),l=s.getImageData(0,0,30,30).data,n=0;for(let t=0,e=l.length;t<e;t+=4)if(l[t]>0){n=30-Math.floor(t/4/30);break}s.canvas.width=s.canvas.height=0,s.font=a;let h=n?n/(n+o):.8;return this.#e0.set(t,h),h}}function eN(){tg("`renderTextLayer`, please use `TextLayer` instead.");let{textContentSource:t,container:e,viewport:i,...s}=arguments[0],a=Object.keys(s);a.length>0&&E("Ignoring `renderTextLayer` parameters: "+a.join(", "));let r=new eO({textContentSource:t,container:e,viewport:i}),{textDivs:n,textContentItemsStr:o}=r;return{promise:r.render(),textDivs:n,textContentItemsStr:o}}function eB(){tg("`updateTextLayer`, please use `TextLayer` instead.")}class eH{static textContent(t){let e=[],i={items:e,styles:Object.create(null)};return!function t(i){if(!i)return;let s=null,a=i.name;if("#text"===a)s=i.value;else{if(!eH.shouldBuildText(a))return;i?.attributes?.textContent?s=i.attributes.textContent:i.value&&(s=i.value)}if(null!==s&&e.push({str:s}),!!i.children)for(let e of i.children)t(e)}(t),i}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}let ez=n?class t extends X{_createCanvas(t,e){return tU.get("canvas").createCanvas(t,e)}}:class t extends X{constructor({ownerDocument:t=globalThis.document}={}){super(),this._document=t}_createCanvas(t,e){let i=this._document.createElement("canvas");return i.width=t,i.height=e,i}},eU=n?class t extends Y{_fetchData(t,e){return tj(t).then(t=>({cMapData:t,compressionType:e}))}}:ti,ej=n?class t extends K{}:class t extends K{#e7;#e9;#it;#ie;#ii;#m=0;constructor({docId:t,ownerDocument:e=globalThis.document}={}){super(),this.#it=t,this.#ie=e}get #v(){return this.#e7||=new Map}get #is(){return this.#ii||=new Map}get #ia(){if(!this.#e9){let t=this.#ie.createElement("div"),{style:e}=t;e.visibility="hidden",e.contain="strict",e.width=e.height=0,e.position="absolute",e.top=e.left=0,e.zIndex=-1;let i=this.#ie.createElementNS(Z,"svg");i.setAttribute("width",0),i.setAttribute("height",0),this.#e9=this.#ie.createElementNS(Z,"defs"),t.append(i),i.append(this.#e9),this.#ie.body.append(t)}return this.#e9}#ir(t){if(1===t.length){let e=t[0],i=Array(256);for(let t=0;t<256;t++)i[t]=e[t]/255;let s=i.join(",");return[s,s,s]}let[e,i,s]=t,a=Array(256),r=Array(256),n=Array(256);for(let t=0;t<256;t++)a[t]=e[t]/255,r[t]=i[t]/255,n[t]=s[t]/255;return[a.join(","),r.join(","),n.join(",")]}addFilter(t){if(!t)return"none";let e=this.#v.get(t);if(e)return e;let[i,s,a]=this.#ir(t),r=1===t.length?i:`${i}${s}${a}`;if(e=this.#v.get(r))return this.#v.set(t,e),e;let n=`g_${this.#it}_transfer_map_${this.#m++}`,o=`url(#${n})`;this.#v.set(t,o),this.#v.set(r,o);let l=this.#io(n);return this.#il(i,s,a,l),o}addHCMFilter(t,e){let i=`${t}-${e}`,s="base",a=this.#is.get(s);if(a?.key===i)return a.url;if(a?(a.filter?.remove(),a.key=i,a.url="none",a.filter=null):(a={key:i,url:"none",filter:null},this.#is.set(s,a)),!t||!e)return a.url;let r=this.#ih(t);t=j.makeHexColor(...r);let n=this.#ih(e);if(e=j.makeHexColor(...n),this.#ia.style.color="","#000000"===t&&"#ffffff"===e||t===e)return a.url;let o=Array(256);for(let t=0;t<=255;t++){let e=t/255;o[t]=e<=.03928?e/12.92:((e+.055)/1.055)**2.4}let l=o.join(","),h=`g_${this.#it}_hcm_filter`,d=a.filter=this.#io(h);this.#il(l,l,l,d),this.#id(d);let c=(t,e)=>{let i=r[t]/255,s=n[t]/255,a=Array(e+1);for(let t=0;t<=e;t++)a[t]=i+t/e*(s-i);return a.join(",")};return this.#il(c(0,5),c(1,5),c(2,5),d),a.url=`url(#${h})`,a.url}addAlphaFilter(t){let e=this.#v.get(t);if(e)return e;let[i]=this.#ir([t]),s=`alpha_${i}`;if(e=this.#v.get(s))return this.#v.set(t,e),e;let a=`g_${this.#it}_alpha_map_${this.#m++}`,r=`url(#${a})`;this.#v.set(t,r),this.#v.set(s,r);let n=this.#io(a);return this.#ic(i,n),r}addLuminosityFilter(t){let e,i,s=this.#v.get(t||"luminosity");if(s)return s;if(t?([e]=this.#ir([t]),i=`luminosity_${e}`):i="luminosity",s=this.#v.get(i))return this.#v.set(t,s),s;let a=`g_${this.#it}_luminosity_map_${this.#m++}`,r=`url(#${a})`;this.#v.set(t,r),this.#v.set(i,r);let n=this.#io(a);return this.#iu(n),t&&this.#ic(e,n),r}addHighlightHCMFilter(t,e,i,s,a){let r=`${e}-${i}-${s}-${a}`,n=this.#is.get(t);if(n?.key===r)return n.url;if(n?(n.filter?.remove(),n.key=r,n.url="none",n.filter=null):(n={key:r,url:"none",filter:null},this.#is.set(t,n)),!e||!i)return n.url;let[o,l]=[e,i].map(this.#ih.bind(this)),h=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),d=Math.round(.2126*l[0]+.7152*l[1]+.0722*l[2]),[c,u]=[s,a].map(this.#ih.bind(this));d<h&&([h,d,c,u]=[d,h,u,c]),this.#ia.style.color="";let p=(t,e,i)=>{let s=Array(256),a=(d-h)/i,r=t/255,n=(e-t)/(255*i),o=0;for(let t=0;t<=i;t++){let e=Math.round(h+t*a),i=r+t*n;for(let t=o;t<=e;t++)s[t]=i;o=e+1}for(let t=o;t<256;t++)s[t]=s[o-1];return s.join(",")},g=`g_${this.#it}_hcm_${t}_filter`,f=n.filter=this.#io(g);return this.#id(f),this.#il(p(c[0],u[0],5),p(c[1],u[1],5),p(c[2],u[2],5),f),n.url=`url(#${g})`,n.url}destroy(t=!1){if(!t||0===this.#is.size)this.#e9&&(this.#e9.parentNode.parentNode.remove(),this.#e9=null),this.#e7&&(this.#e7.clear(),this.#e7=null),this.#m=0}#iu(t){let e=this.#ie.createElementNS(Z,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),t.append(e)}#id(t){let e=this.#ie.createElementNS(Z,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),t.append(e)}#io(t){let e=this.#ie.createElementNS(Z,"filter");return e.setAttribute("color-interpolation-filters","sRGB"),e.setAttribute("id",t),this.#ia.append(e),e}#ip(t,e,i){let s=this.#ie.createElementNS(Z,e);s.setAttribute("type","discrete"),s.setAttribute("tableValues",i),t.append(s)}#il(t,e,i,s){let a=this.#ie.createElementNS(Z,"feComponentTransfer");s.append(a),this.#ip(a,"feFuncR",t),this.#ip(a,"feFuncG",e),this.#ip(a,"feFuncB",i)}#ic(t,e){let i=this.#ie.createElementNS(Z,"feComponentTransfer");e.append(i),this.#ip(i,"feFuncA",t)}#ih(t){return this.#ia.style.color=t,tb(getComputedStyle(this.#ia).getPropertyValue("color"))}},e$=n?class t extends Q{_fetchData(t){return tj(t)}}:ts;function eG(t){var e;if("string"==typeof t||t instanceof URL?t={url:t}:(t instanceof ArrayBuffer||ArrayBuffer.isView(t))&&(t={data:t}),"object"!=typeof t)throw Error("Invalid parameter in getDocument, need parameter object.");if(!t.url&&!t.data&&!t.range)throw Error("Invalid parameter object: need either .data, .range or .url");let i=new eq,{docId:s}=i,a=t.url?function(t){if(t instanceof URL)return t.href;try{return new URL(t,window.location).href}catch{if(n&&"string"==typeof t)return t}throw Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}(t.url):null,r=t.data?function(t){if(n&&"undefined"!=typeof Buffer&&t instanceof Buffer)throw Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return B(t);if(t instanceof ArrayBuffer||ArrayBuffer.isView(t)||"object"==typeof t&&!isNaN(t?.length))return new Uint8Array(t);throw Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}(t.data):null,o=t.httpHeaders||null,l=!0===t.withCredentials,h=t.password??null,d=t.range instanceof eW?t.range:null,c=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:65536,u=t.worker instanceof eJ?t.worker:null,p=t.verbosity,g="string"!=typeof t.docBaseUrl||to(t.docBaseUrl)?null:t.docBaseUrl,f="string"==typeof t.cMapUrl?t.cMapUrl:null,m=!1!==t.cMapPacked,b=t.CMapReaderFactory||eU,v="string"==typeof t.standardFontDataUrl?t.standardFontDataUrl:null,A=t.StandardFontDataFactory||e$,y=!0!==t.stopAtErrors,_=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,x=!1!==t.isEvalSupported,E="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!n,C=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,S="boolean"==typeof t.disableFontFace?t.disableFontFace:n,T=!0===t.fontExtraProperties,M=!0===t.enableXfa,k=t.ownerDocument||globalThis.document,L=!0===t.disableRange,P=!0===t.disableStream,R=!0===t.disableAutoFetch,I=!0===t.pdfBug,D=d?d.length:t.length??NaN,F="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!n&&!S,O="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:b===ti&&A===ts&&f&&v&&tu(f,document.baseURI)&&tu(v,document.baseURI),N=t.canvasFactory||new ez({ownerDocument:k}),H=t.filterFactory||new ej({docId:s,ownerDocument:k});Number.isInteger(e=p)&&(w=e);let z={canvasFactory:N,filterFactory:H};if(!O&&(z.cMapReaderFactory=new b({baseUrl:f,isCompressed:m}),z.standardFontDataFactory=new A({baseUrl:v})),!u){let t={verbosity:p,port:ee.workerPort};u=t.port?eJ.fromPort(t):new eJ(t),i._worker=u}let U={docId:s,apiVersion:"4.3.136",data:r,password:h,disableAutoFetch:R,rangeChunkSize:c,length:D,docBaseUrl:g,enableXfa:M,evaluatorOptions:{maxImageSize:_,disableFontFace:S,ignoreErrors:y,isEvalSupported:x,isOffscreenCanvasSupported:E,canvasMaxAreaInBytes:C,fontExtraProperties:T,useSystemFonts:F,cMapUrl:O?f:null,standardFontDataUrl:O?v:null}},j={disableFontFace:S,fontExtraProperties:T,ownerDocument:k,pdfBug:I,styleElement:null,loadingParams:{disableAutoFetch:R,enableXfa:M}};return u.promise.then(function(){let t;if(i.destroyed)throw Error("Loading aborted");if(u.destroyed)throw Error("Worker was destroyed");let e=u.messageHandler.sendWithPromise("GetDocRequest",U,r?[r.buffer]:null);if(d)t=new ed(d,{disableRange:L,disableStream:P});else if(!r){var h;h={url:a,length:D,httpHeaders:o,withCredentials:l,rangeChunkSize:c,disableRange:L,disableStream:P},t=n?"undefined"!=typeof fetch&&"undefined"!=typeof Response&&"body"in Response.prototype&&tu(h.url)?new ey(h):new eM(h):tu(h.url)?new ey(h):new eE(h)}return e.then(e=>{if(i.destroyed)throw Error("Loading aborted");if(u.destroyed)throw Error("Worker was destroyed");let a=new er(s,e,u.port),r=new eZ(a,i,t,j,z);i._transport=r,a.send("Ready",null)})}).catch(i._capability.reject),i}function eV(t){return"object"==typeof t&&Number.isInteger(t?.num)&&t.num>=0&&Number.isInteger(t?.gen)&&t.gen>=0}class eq{static #it=0;constructor(){this._capability=Promise.withResolvers(),this._transport=null,this._worker=null,this.docId=`d${eq.#it++}`,this.destroyed=!1,this.onPassword=null,this.onProgress=null}get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0),await this._transport?.destroy()}catch(t){throw this._worker?.port&&delete this._worker._pendingDestroy,t}this._transport=null,this._worker&&(this._worker.destroy(),this._worker=null)}}class eW{constructor(t,e,i=!1,s=null){this.length=t,this.initialData=e,this.progressiveDone=i,this.contentDispositionFilename=s,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=Promise.withResolvers()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(let i of this._rangeListeners)i(t,e)}onDataProgress(t,e){this._readyCapability.promise.then(()=>{for(let i of this._progressListeners)i(t,e)})}onDataProgressiveRead(t){this._readyCapability.promise.then(()=>{for(let e of this._progressiveReadListeners)e(t)})}onDataProgressiveDone(){this._readyCapability.promise.then(()=>{for(let t of this._progressiveDoneListeners)t()})}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){C("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class eK{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return M(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){let{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class eX{#ig=null;#im=!1;constructor(t,e,i,s=!1){this._pageIndex=t,this._pageInfo=e,this._transport=i,this._stats=s?new tc:null,this._pdfBug=s,this.commonObjs=i.commonObjs,this.objs=new e1,this._maybeCleanupAfterRender=!1,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:i=0,offsetY:s=0,dontFlip:a=!1}={}){return new tr({viewBox:this.view,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:a})}getAnnotations({intent:t="display"}={}){let{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return M(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:i="display",annotationMode:s=d.ENABLE,transform:a=null,background:r=null,optionalContentConfigPromise:n=null,annotationCanvasMap:o=null,pageColors:l=null,printAnnotationStorage:c=null}){this._stats?.time("Overall");let u=this._transport.getRenderingIntent(i,s,c),{renderingIntent:p,cacheKey:g}=u;this.#im=!1,this.#ib(),n||=this._transport.getOptionalContentConfig(p);let f=this._intentStates.get(g);!f&&(f=Object.create(null),this._intentStates.set(g,f)),f.streamReaderCancelTimeout&&(clearTimeout(f.streamReaderCancelTimeout),f.streamReaderCancelTimeout=null);let m=!!(p&h.PRINT);!f.displayReadyCapability&&(f.displayReadyCapability=Promise.withResolvers(),f.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(u));let b=t=>{f.renderTasks.delete(v),(this._maybeCleanupAfterRender||m)&&(this.#im=!0),this.#iv(!m),t?(v.capability.reject(t),this._abortOperatorList({intentState:f,reason:t instanceof Error?t:Error(t)})):v.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats))},v=new e3({callback:b,params:{canvasContext:t,viewport:e,transform:a,background:r},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:o,operatorList:f.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!m,pdfBug:this._pdfBug,pageColors:l});(f.renderTasks||=new Set).add(v);let A=v.task;return Promise.all([f.displayReadyCapability.promise,n]).then(([t,e])=>{if(this.destroyed){b();return}if(this._stats?.time("Rendering"),!(e.renderingIntent&p))throw Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");v.initializeGraphics({transparency:t,optionalContentConfig:e}),v.operatorListChanged()}).catch(b),A}getOperatorList({intent:t="display",annotationMode:e=d.ENABLE,printAnnotationStorage:i=null}={}){let s;let a=this._transport.getRenderingIntent(t,e,i,!0),r=this._intentStates.get(a.cacheKey);return!r&&(r=Object.create(null),this._intentStates.set(a.cacheKey,r)),!r.opListReadCapability&&((s=Object.create(null)).operatorListChanged=function(){r.operatorList.lastChunk&&(r.opListReadCapability.resolve(r.operatorList),r.renderTasks.delete(s))},r.opListReadCapability=Promise.withResolvers(),(r.renderTasks||=new Set).add(s),r.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(a)),r.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then(t=>eH.textContent(t));let e=this.streamTextContent(t);return new Promise(function(t,i){let s=e.getReader(),a={items:[],styles:Object.create(null),lang:null};!function e(){s.read().then(function({value:i,done:s}){if(s){t(a);return}a.lang??=i.lang,Object.assign(a.styles,i.styles),a.items.push(...i.items),e()},i)}()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;let t=[];for(let e of this._intentStates.values()){if(this._abortOperatorList({intentState:e,reason:Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(let i of e.renderTasks)t.push(i.completed),i.cancel()}return this.objs.clear(),this.#im=!1,this.#ib(),Promise.all(t)}cleanup(t=!1){this.#im=!0;let e=this.#iv(!1);return t&&e&&(this._stats&&=new tc),e}#iv(t=!1){if(this.#ib(),!this.#im||this.destroyed)return!1;if(t)return this.#ig=setTimeout(()=>{this.#ig=null,this.#iv(!1)},5e3),!1;for(let{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),this.#im=!1,!0}#ib(){this.#ig&&(clearTimeout(this.#ig),this.#ig=null)}_startRenderPage(t,e){let i=this._intentStates.get(e);if(!!i)this._stats?.timeEnd("Page Request"),i.displayReadyCapability?.resolve(t)}_renderPageChunk(t,e){for(let i=0,s=t.length;i<s;i++)e.operatorList.fnArray.push(t.fnArray[i]),e.operatorList.argsArray.push(t.argsArray[i]);for(let i of(e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots,e.renderTasks))i.operatorListChanged();t.lastChunk&&this.#iv(!0)}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:i}){let{map:s,transfer:a}=i,r=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:s},a).getReader(),n=this._intentStates.get(e);n.streamReader=r;let o=()=>{r.read().then(({value:t,done:e})=>{if(e){n.streamReader=null;return}if(!this._transport.destroyed)this._renderPageChunk(t,n),o()},t=>{if(n.streamReader=null,!this._transport.destroyed){if(n.operatorList){for(let t of(n.operatorList.lastChunk=!0,n.renderTasks))t.operatorListChanged();this.#iv(!0)}if(n.displayReadyCapability)n.displayReadyCapability.reject(t);else if(n.opListReadCapability)n.opListReadCapability.reject(t);else throw t}})};o()}_abortOperatorList({intentState:t,reason:e,force:i=!1}){if(!t.streamReader)return;if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!i){if(t.renderTasks.size>0)return;if(e instanceof tn){let i=100;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay),t.streamReaderCancelTimeout=setTimeout(()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})},i);return}}if(t.streamReader.cancel(new O(e.message)).catch(()=>{}),t.streamReader=null,!this._transport.destroyed){for(let[e,i]of this._intentStates)if(i===t){this._intentStates.delete(e);break}this.cleanup()}}get stats(){return this._stats}}class eY{#iA=new Set;#iy=Promise.resolve();postMessage(t,e){let i={data:structuredClone(t,e?{transfer:e}:null)};this.#iy.then(()=>{for(let t of this.#iA)t.call(this,i)})}addEventListener(t,e){this.#iA.add(e)}removeEventListener(t,e){this.#iA.delete(e)}terminate(){this.#iA.clear()}}let eQ={isWorkerDisabled:!1,fakeWorkerId:0};n&&(eQ.isWorkerDisabled=!0,ee.workerSrc||="./pdf.worker.mjs"),eQ.isSameOrigin=function(t,e){let i;try{if(!(i=new URL(t)).origin||"null"===i.origin)return!1}catch{return!1}let s=new URL(e,i);return i.origin===s.origin},eQ.createCDNWrapper=function(t){let e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))};class eJ{static #i_;constructor({name:t=null,port:e=null,verbosity:i=w}={}){if(this.name=t,this.destroyed=!1,this.verbosity=i,this._readyCapability=Promise.withResolvers(),this._port=null,this._webWorker=null,this._messageHandler=null,e){if(eJ.#i_?.has(e))throw Error("Cannot use more than one PDFWorker per port.");(eJ.#i_||=new WeakMap).set(e,this),this._initializeFromPort(e);return}this._initialize()}get promise(){return n?Promise.all([tU.promise,this._readyCapability.promise]):this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t,this._messageHandler=new er("main","worker",t),this._messageHandler.on("ready",function(){}),this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})}_initialize(){if(!eQ.isWorkerDisabled&&!eJ.#iw){let{workerSrc:t}=eJ;try{!eQ.isSameOrigin(window.location.href,t)&&(t=eQ.createCDNWrapper(new URL(t,window.location).href));let e=new Worker(t,{type:"module"}),i=new er("main","worker",e),s=()=>{e.removeEventListener("error",a),i.destroy(),e.terminate(),this.destroyed?this._readyCapability.reject(Error("Worker was destroyed")):this._setupFakeWorker()},a=()=>{!this._webWorker&&s()};e.addEventListener("error",a),i.on("test",t=>{if(e.removeEventListener("error",a),this.destroyed){s();return}t?(this._messageHandler=i,this._port=e,this._webWorker=e,this._readyCapability.resolve(),i.send("configure",{verbosity:this.verbosity})):(this._setupFakeWorker(),i.destroy(),e.terminate())}),i.on("ready",t=>{if(e.removeEventListener("error",a),this.destroyed){s();return}try{r()}catch{this._setupFakeWorker()}});let r=()=>{let t=new Uint8Array;i.send("test",t,[t.buffer])};r();return}catch{x("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){!eQ.isWorkerDisabled&&(E("Setting up fake worker."),eQ.isWorkerDisabled=!0),eJ._setupFakeWorkerGlobal.then(t=>{if(this.destroyed){this._readyCapability.reject(Error("Worker was destroyed"));return}let e=new eY;this._port=e;let i=`fake${eQ.fakeWorkerId++}`,s=new er(i+"_worker",i,e);t.setup(s,e);let a=new er(i,i+"_worker",e);this._messageHandler=a,this._readyCapability.resolve(),a.send("configure",{verbosity:this.verbosity})}).catch(t=>{this._readyCapability.reject(Error(`Setting up fake worker failed: "${t.message}".`))})}destroy(){this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),eJ.#i_?.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}static fromPort(t){if(!t?.port)throw Error("PDFWorker.fromPort - invalid method signature.");let e=this.#i_?.get(t.port);if(e){if(e._pendingDestroy)throw Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new eJ(t)}static get workerSrc(){if(ee.workerSrc)return ee.workerSrc;throw Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get #iw(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}}static get _setupFakeWorkerGlobal(){return M(this,"_setupFakeWorkerGlobal",(async()=>this.#iw?this.#iw:(await import(this.workerSrc)).WorkerMessageHandler)())}}class eZ{#ix=new Map;#iE=new Map;#iC=new Map;#iS=new Map;#iT=null;constructor(t,e,i,s,a){this.messageHandler=t,this.loadingTask=e,this.commonObjs=new e1,this.fontLoader=new tN({ownerDocument:s.ownerDocument,styleElement:s.styleElement}),this.loadingParams=s.loadingParams,this._params=s,this.canvasFactory=a.canvasFactory,this.filterFactory=a.filterFactory,this.cMapReaderFactory=a.cMapReaderFactory,this.standardFontDataFactory=a.standardFontDataFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=i,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.setupMessageHandler()}#iM(t,e=null){let i=this.#ix.get(t);if(i)return i;let s=this.messageHandler.sendWithPromise(t,e);return this.#ix.set(t,s),s}get annotationStorage(){return M(this,"annotationStorage",new tF)}getRenderingIntent(t,e=d.ENABLE,i=null,s=!1){let a=h.DISPLAY,r=tD;switch(t){case"any":a=h.ANY;break;case"display":break;case"print":a=h.PRINT;break;default:E(`getRenderingIntent - invalid intent: ${t}`)}switch(e){case d.DISABLE:a+=h.ANNOTATIONS_DISABLE;break;case d.ENABLE:break;case d.ENABLE_FORMS:a+=h.ANNOTATIONS_FORMS;break;case d.ENABLE_STORAGE:r=((a+=h.ANNOTATIONS_STORAGE)&h.PRINT&&i instanceof tO?i:this.annotationStorage).serializable;break;default:E(`getRenderingIntent - invalid annotationMode: ${e}`)}return s&&(a+=h.OPLIST),{renderingIntent:a,cacheKey:`${a}_${r.hash}`,annotationStorageSerializable:r}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),this.#iT?.reject(Error("Worker was destroyed during onPassword callback"));let t=[];for(let e of this.#iE.values())t.push(e._destroy());this.#iE.clear(),this.#iC.clear(),this.#iS.clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();let e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then(()=>{this.commonObjs.clear(),this.fontLoader.clear(),this.#ix.clear(),this.filterFactory.destroy(),eO.cleanup(),this._networkStream?.cancelAllRequests(new O("Worker was terminated.")),this.messageHandler&&(this.messageHandler.destroy(),this.messageHandler=null),this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){let{messageHandler:t,loadingTask:e}=this;t.on("GetReader",(t,e)=>{S(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}},e.onPull=()=>{this._fullReader.read().then(function({value:t,done:i}){if(i){e.close();return}S(t instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(t),1,[t])}).catch(t=>{e.error(t)})},e.onCancel=t=>{this._fullReader.cancel(t),e.ready.catch(t=>{if(!this.destroyed)throw t})}}),t.on("ReaderHeadersReady",t=>{let i=Promise.withResolvers(),s=this._fullReader;return s.headersReady.then(()=>{(!s.isStreamingSupported||!s.isRangeSupported)&&(this._lastProgress&&e.onProgress?.(this._lastProgress),s.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}),i.resolve({isStreamingSupported:s.isStreamingSupported,isRangeSupported:s.isRangeSupported,contentLength:s.contentLength})},i.reject),i.promise}),t.on("GetRangeReader",(t,e)=>{S(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");let i=this._networkStream.getRangeReader(t.begin,t.end);if(!i){e.close();return}e.onPull=()=>{i.read().then(function({value:t,done:i}){if(i){e.close();return}S(t instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(t),1,[t])}).catch(t=>{e.error(t)})},e.onCancel=t=>{i.cancel(t),e.ready.catch(t=>{if(!this.destroyed)throw t})}}),t.on("GetDoc",({pdfInfo:t})=>{this._numPages=t.numPages,this._htmlForXfa=t.htmlForXfa,delete t.htmlForXfa,e._capability.resolve(new eK(t,this))}),t.on("DocException",function(t){let i;switch(t.name){case"PasswordException":i=new L(t.message,t.code);break;case"InvalidPDFException":i=new R(t.message);break;case"MissingPDFException":i=new I(t.message);break;case"UnexpectedResponseException":i=new D(t.message,t.status);break;case"UnknownErrorException":i=new P(t.message,t.details);break;default:C("DocException - expected a valid Error.")}e._capability.reject(i)}),t.on("PasswordRequest",t=>{if(this.#iT=Promise.withResolvers(),e.onPassword)try{e.onPassword(t=>{t instanceof Error?this.#iT.reject(t):this.#iT.resolve({password:t})},t.code)}catch(t){this.#iT.reject(t)}else this.#iT.reject(new L(t.message,t.code));return this.#iT.promise}),t.on("DataLoaded",t=>{e.onProgress?.({loaded:t.length,total:t.length}),this.downloadInfoCapability.resolve(t)}),t.on("StartRenderPage",t=>{if(!this.destroyed)this.#iE.get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)}),t.on("commonobj",([e,i,s])=>{if(this.destroyed||this.commonObjs.has(e))return null;switch(i){case"Font":let{disableFontFace:a,fontExtraProperties:r,pdfBug:n}=this._params;if("error"in s){let t=s.error;E(`Error during font loading: ${t}`),this.commonObjs.resolve(e,t);break}let o=new tB(s,{disableFontFace:a,inspectFont:n&&globalThis.FontInspector?.enabled?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null});this.fontLoader.bind(o).catch(()=>t.sendWithPromise("FontFallback",{id:e})).finally(()=>{!r&&o.data&&(o.data=null),this.commonObjs.resolve(e,o)});break;case"CopyLocalImage":let{imageRef:l}=s;for(let t of(S(l,"The imageRef must be defined."),this.#iE.values()))for(let[,i]of t.objs){if(i?.ref===l){if(!i.dataLen)return null;return this.commonObjs.resolve(e,structuredClone(i)),i.dataLen}}break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(e,s);break;default:throw Error(`Got unknown common object type ${i}`)}return null}),t.on("obj",([t,e,i,s])=>{if(this.destroyed)return;let a=this.#iE.get(e);if(!a.objs.has(t)){if(0===a._intentStates.size){s?.bitmap?.close();return}switch(i){case"Image":a.objs.resolve(t,s),s?.dataLen>1e7&&(a._maybeCleanupAfterRender=!0);break;case"Pattern":a.objs.resolve(t,s);break;default:throw Error(`Got unknown object type ${i}`)}}}),t.on("DocProgress",t=>{if(!this.destroyed)e.onProgress?.({loaded:t.loaded,total:t.total})}),t.on("FetchBuiltInCMap",t=>this.destroyed?Promise.reject(Error("Worker was destroyed.")):this.cMapReaderFactory?this.cMapReaderFactory.fetch(t):Promise.reject(Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))),t.on("FetchStandardFontData",t=>this.destroyed?Promise.reject(Error("Worker was destroyed.")):this.standardFontDataFactory?this.standardFontDataFactory.fetch(t):Promise.reject(Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter.")))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&E("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");let{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally(()=>{this.annotationStorage.resetModified()})}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(Error("Invalid page request."));let e=t-1,i=this.#iC.get(e);if(i)return i;let s=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then(i=>{if(this.destroyed)throw Error("Transport destroyed");i.refStr&&this.#iS.set(i.refStr,t);let s=new eX(e,i,this,this._params.pdfBug);return this.#iE.set(e,s),s});return this.#iC.set(e,s),s}getPageIndex(t){return eV(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#iM("GetFieldObjects")}hasJSActions(){return this.#iM("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#iM("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return this.#iM("GetOptionalContentConfig").then(e=>new eh(e,t))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){let t="GetMetadata",e=this.#ix.get(t);if(e)return e;let i=this.messageHandler.sendWithPromise(t,null).then(t=>({info:t[0],metadata:t[1]?new en(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null}));return this.#ix.set(t,i),i}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){for(let t of(await this.messageHandler.sendWithPromise("Cleanup",null),this.#iE.values()))if(!t.cleanup())throw Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`);this.commonObjs.clear(),!t&&this.fontLoader.clear(),this.#ix.clear(),this.filterFactory.destroy(!0),eO.cleanup()}}cachedPageNumber(t){if(!eV(t))return null;let e=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`;return this.#iS.get(e)??null}}let e0=Symbol("INITIAL_DATA");class e1{#ik=Object.create(null);#iL(t){return this.#ik[t]||={...Promise.withResolvers(),data:e0}}get(t,e=null){if(e){let i=this.#iL(t);return i.promise.then(()=>e(i.data)),null}let i=this.#ik[t];if(!i||i.data===e0)throw Error(`Requesting object that isn't resolved yet ${t}.`);return i.data}has(t){let e=this.#ik[t];return!!e&&e.data!==e0}resolve(t,e=null){let i=this.#iL(t);i.data=e,i.resolve()}clear(){for(let t in this.#ik){let{data:e}=this.#ik[t];e?.bitmap?.close()}this.#ik=Object.create(null)}*[Symbol.iterator](){for(let t in this.#ik){let{data:e}=this.#ik[t];if(e!==e0)yield[t,e]}}}class e2{#iP=null;constructor(t){this.#iP=t,this.onContinue=null}get promise(){return this.#iP.capability.promise}cancel(t=0){this.#iP.cancel(null,t)}get separateAnnots(){let{separateAnnots:t}=this.#iP.operatorList;if(!t)return!1;let{annotationCanvasMap:e}=this.#iP;return t.form||t.canvas&&e?.size>0}}class e3{static #iR=new WeakSet;constructor({callback:t,params:e,objs:i,commonObjs:s,annotationCanvasMap:a,operatorList:r,pageIndex:n,canvasFactory:o,filterFactory:l,useRequestAnimationFrame:h=!1,pdfBug:d=!1,pageColors:c=null}){this.callback=t,this.params=e,this.objs=i,this.commonObjs=s,this.annotationCanvasMap=a,this.operatorListIdx=null,this.operatorList=r,this._pageIndex=n,this.canvasFactory=o,this.filterFactory=l,this._pdfBug=d,this.pageColors=c,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window,this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new e2(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(e3.#iR.has(this._canvas))throw Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");e3.#iR.add(this._canvas)}this._pdfBug&&globalThis.StepperManager?.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());let{canvasContext:i,viewport:s,transform:a,background:r}=this.params;this.gfx=new et(i,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:a,viewport:s,transparency:t,background:r}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1,this.cancelled=!0,this.gfx?.endDrawing(),e3.#iR.delete(this._canvas),this.callback(t||new tn(`Rendering cancelled, page ${this._pageIndex+1}`,e))}operatorListChanged(){if(!this.graphicsReady){this.graphicsReadyCallback||=this._continueBound;return}if(this.stepper?.updateOperatorList(this.operatorList),!this.running)this._continue()}_continue(){if(this.running=!0,!this.cancelled)this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext()}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame(()=>{this._nextBound().catch(this._cancelBound)}):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){if(!this.cancelled)this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),e3.#iR.delete(this._canvas),this.callback()))}}let e5="4.3.136",e6="0cec64437";function e4(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function e8(t){return Math.max(0,Math.min(255,255*t))}class e7{static CMYK_G([t,e,i,s]){return["G",1-Math.min(1,.3*t+.59*i+.11*e+s)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return[t=e8(t),t,t]}static G_HTML([t]){let e=e4(t);return`#${e}${e}${e}`}static RGB_G([t,e,i]){return["G",.3*t+.59*e+.11*i]}static RGB_rgb(t){return t.map(e8)}static RGB_HTML(t){return`#${t.map(e4).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,i,s]){return["RGB",1-Math.min(1,t+s),1-Math.min(1,i+s),1-Math.min(1,e+s)]}static CMYK_rgb([t,e,i,s]){return[e8(1-Math.min(1,t+s)),e8(1-Math.min(1,i+s)),e8(1-Math.min(1,e+s))]}static CMYK_HTML(t){let e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,i]){let s=1-t,a=1-e,r=1-i,n=Math.min(s,a,r);return["CMYK",s,a,r,n]}}class e9{static setupStorage(t,e,i,s,a){let r=s.getValue(e,{value:null});switch(i.name){case"textarea":if(null!==r.value&&(t.textContent=r.value),"print"===a)break;t.addEventListener("input",t=>{s.setValue(e,{value:t.target.value})});break;case"input":if("radio"===i.attributes.type||"checkbox"===i.attributes.type){if(r.value===i.attributes.xfaOn?t.setAttribute("checked",!0):r.value===i.attributes.xfaOff&&t.removeAttribute("checked"),"print"===a)break;t.addEventListener("change",t=>{s.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})})}else{if(null!==r.value&&t.setAttribute("value",r.value),"print"===a)break;t.addEventListener("input",t=>{s.setValue(e,{value:t.target.value})})}break;case"select":if(null!==r.value)for(let e of(t.setAttribute("value",r.value),i.children))e.attributes.value===r.value?e.attributes.selected=!0:e.attributes.hasOwnProperty("selected")&&delete e.attributes.selected;t.addEventListener("input",t=>{let i=t.target.options,a=-1===i.selectedIndex?"":i[i.selectedIndex].value;s.setValue(e,{value:a})})}}static setAttributes({html:t,element:e,storage:i=null,intent:s,linkService:a}){let{attributes:r}=e,n=t instanceof HTMLAnchorElement;for(let[e,i]of("radio"===r.type&&(r.name=`${r.name}-${s}`),Object.entries(r))){if(null!=i)switch(e){case"class":i.length&&t.setAttribute(e,i.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",i);break;case"style":Object.assign(t.style,i);break;case"textContent":t.textContent=i;break;default:(!n||"href"!==e&&"newWindow"!==e)&&t.setAttribute(e,i)}}n&&a.addLinkAttributes(t,r.href,r.newWindow),i&&r.dataId&&this.setupStorage(t,r.dataId,e,i)}static render(t){let e=t.annotationStorage,i=t.linkService,s=t.xfaHtml,a=t.intent||"display",r=document.createElement(s.name);s.attributes&&this.setAttributes({html:r,element:s,intent:a,linkService:i});let n="richText"!==a,o=t.div;if(o.append(r),t.viewport){let e=`matrix(${t.viewport.transform.join(",")})`;o.style.transform=e}n&&o.setAttribute("class","xfaLayer xfaFont");let l=[];if(0===s.children.length){if(s.value){let t=document.createTextNode(s.value);r.append(t),n&&eH.shouldBuildText(s.name)&&l.push(t)}return{textDivs:l}}let h=[[s,-1,r]];for(;h.length>0;){let[t,s,r]=h.at(-1);if(s+1===t.children.length){h.pop();continue}let o=t.children[++h.at(-1)[1]];if(null===o)continue;let{name:d}=o;if("#text"===d){let t=document.createTextNode(o.value);l.push(t),r.append(t);continue}let c=o?.attributes?.xmlns?document.createElementNS(o.attributes.xmlns,d):document.createElement(d);if(r.append(c),o.attributes&&this.setAttributes({html:c,element:o,storage:e,intent:a,linkService:i}),o.children?.length>0)h.push([o,-1,c]);else if(o.value){let t=document.createTextNode(o.value);n&&eH.shouldBuildText(d)&&l.push(t),c.append(t)}}for(let t of o.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))t.setAttribute("readOnly",!0);return{textDivs:l}}static update(t){let e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}let it=new WeakSet;function ie(t){return{width:t[2]-t[0],height:t[3]-t[1]}}class ii{static create(t){switch(t.data.annotationType){case m.LINK:return new ia(t);case m.TEXT:return new ir(t);case m.WIDGET:switch(t.data.fieldType){case"Tx":return new il(t);case"Btn":if(t.data.radioButton)return new ic(t);if(t.data.checkBox)return new id(t);return new iu(t);case"Ch":return new ip(t);case"Sig":return new ih(t)}return new io(t);case m.POPUP:return new ig(t);case m.FREETEXT:return new ib(t);case m.LINE:return new iv(t);case m.SQUARE:return new iA(t);case m.CIRCLE:return new iy(t);case m.POLYLINE:return new i_(t);case m.CARET:return new ix(t);case m.INK:return new iE(t);case m.POLYGON:return new iw(t);case m.HIGHLIGHT:return new iC(t);case m.UNDERLINE:return new iS(t);case m.SQUIGGLY:return new iT(t);case m.STRIKEOUT:return new iM(t);case m.STAMP:return new ik(t);case m.FILEATTACHMENT:return new iL(t);default:return new is(t)}}}class is{#iI=null;#iD=!1;#iF=null;constructor(t,{isRenderable:e=!1,ignoreBorder:i=!1,createQuadrilaterals:s=!1}={}){this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(i)),s&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:i}){return!!(t?.str||e?.str||i?.str)}get hasPopupData(){return is._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;this.#iI||={rect:this.data.rect.slice(0)};let{rect:e}=t;e&&this.#iO(e),this.#iF?.popup.updateEdited(t)}resetEdited(){if(!!this.#iI)this.#iO(this.#iI.rect),this.#iF?.popup.resetEdited(),this.#iI=null}#iO(t){let{container:{style:e},data:{rect:i,rotation:s},parent:{viewport:{rawDims:{pageWidth:a,pageHeight:r,pageX:n,pageY:o}}}}=this;i?.splice(0,4,...t);let{width:l,height:h}=ie(t);e.left=`${100*(t[0]-n)/a}%`,e.top=`${100*(r-t[3]+o)/r}%`,0===s?(e.width=`${100*l/a}%`,e.height=`${100*h/r}%`):this.setRotation(s)}_createContainer(t){let{data:e,parent:{page:i,viewport:s}}=this,a=document.createElement("section");a.setAttribute("data-annotation-id",e.id),!(this instanceof io)&&(a.tabIndex=1e3);let{style:r}=a;if(r.zIndex=this.parent.zIndex++,e.popupRef&&a.setAttribute("aria-haspopup","dialog"),e.alternativeText&&(a.title=e.alternativeText),e.noRotate&&a.classList.add("norotate"),!e.rect||this instanceof ig){let{rotation:t}=e;return!e.hasOwnCanvas&&0!==t&&this.setRotation(t,a),a}let{width:n,height:o}=ie(e.rect);if(!t&&e.borderStyle.width>0){r.borderWidth=`${e.borderStyle.width}px`;let t=e.borderStyle.horizontalCornerRadius,i=e.borderStyle.verticalCornerRadius;if(t>0||i>0){let e=`calc(${t}px * var(--scale-factor)) / calc(${i}px * var(--scale-factor))`;r.borderRadius=e}else if(this instanceof ic){let t=`calc(${n}px * var(--scale-factor)) / calc(${o}px * var(--scale-factor))`;r.borderRadius=t}switch(e.borderStyle.style){case b.SOLID:r.borderStyle="solid";break;case b.DASHED:r.borderStyle="dashed";break;case b.BEVELED:E("Unimplemented border style: beveled");break;case b.INSET:E("Unimplemented border style: inset");break;case b.UNDERLINE:r.borderBottomStyle="solid"}let s=e.borderColor||null;s?(this.#iD=!0,r.borderColor=j.makeHexColor(0|s[0],0|s[1],0|s[2])):r.borderWidth=0}let l=j.normalizeRect([e.rect[0],i.view[3]-e.rect[1]+i.view[1],e.rect[2],i.view[3]-e.rect[3]+i.view[1]]),{pageWidth:h,pageHeight:d,pageX:c,pageY:u}=s.rawDims;r.left=`${100*(l[0]-c)/h}%`,r.top=`${100*(l[1]-u)/d}%`;let{rotation:p}=e;return e.hasOwnCanvas||0===p?(r.width=`${100*n/h}%`,r.height=`${100*o/d}%`):this.setRotation(p,a),a}setRotation(t,e=this.container){let i,s;if(!this.data.rect)return;let{pageWidth:a,pageHeight:r}=this.parent.viewport.rawDims,{width:n,height:o}=ie(this.data.rect);t%180==0?(i=100*n/a,s=100*o/r):(i=100*o/a,s=100*n/r),e.style.width=`${i}%`,e.style.height=`${s}%`,e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){let t=(t,e,i)=>{let s=i.detail[t],a=s[0],r=s.slice(1);i.target.style[e]=e7[`${a}_HTML`](r),this.annotationStorage.setValue(this.data.id,{[e]:e7[`${a}_rgb`](r)})};return M(this,"_commonActions",{display:t=>{let{display:e}=t.detail,i=e%2==1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{let{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout(()=>t.target.focus({preventScroll:!1}),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:t=>{let e=t.detail.rotation;this.setRotation(e),this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){let i=this._commonActions;for(let s of Object.keys(e.detail)){let a=t[s]||i[s];a?.(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;let e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;let i=this._commonActions;for(let[s,a]of Object.entries(e)){let r=i[s];r&&(r({detail:{[s]:a},target:t}),delete e[s])}}_createQuadrilaterals(){let t;if(!this.container)return;let{quadPoints:e}=this.data;if(!e)return;let[i,s,a,r]=this.data.rect;if(1===e.length){let[,{x:t,y:n},{x:o,y:l}]=e[0];if(a===t&&r===n&&i===o&&s===l)return}let{style:n}=this.container;if(this.#iD){let{borderColor:e,borderWidth:i}=n;n.borderWidth=0,t=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${e}" stroke-width="${i}">`],this.container.classList.add("hasBorder")}let o=a-i,l=r-s,{svgFactory:h}=this,d=h.createElement("svg");d.classList.add("quadrilateralsContainer"),d.setAttribute("width",0),d.setAttribute("height",0);let c=h.createElement("defs");d.append(c);let u=h.createElement("clipPath"),p=`clippath_${this.data.id}`;for(let[,{x:s,y:a},{x:n,y:d}]of(u.setAttribute("id",p),u.setAttribute("clipPathUnits","objectBoundingBox"),c.append(u),e)){let e=h.createElement("rect"),c=(n-i)/o,p=(r-a)/l,g=(s-n)/o,f=(a-d)/l;e.setAttribute("x",c),e.setAttribute("y",p),e.setAttribute("width",g),e.setAttribute("height",f),u.append(e),t?.push(`<rect vector-effect="non-scaling-stroke" x="${c}" y="${p}" width="${g}" height="${f}"/>`)}this.#iD&&(t.push("</g></svg>')"),n.backgroundImage=t.join("")),this.container.append(d),this.container.style.clipPath=`url(#${p})`}_createPopup(){let{container:t,data:e}=this;t.setAttribute("aria-haspopup","dialog");let i=this.#iF=new ig({data:{color:e.color,titleObj:e.titleObj,modificationDate:e.modificationDate,contentsObj:e.contentsObj,richText:e.richText,parentRect:e.rect,borderStyle:0,id:`popup_${e.id}`,rotation:e.rotation},parent:this.parent,elements:[this]});this.parent.div.append(i.render())}render(){C("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){let i=[];if(this._fieldObjects){let s=this._fieldObjects[t];if(s)for(let{page:t,id:a,exportValues:r}of s){if(-1===t||a===e)continue;let s="string"==typeof r?r:null,n=document.querySelector(`[data-element-id="${a}"]`);if(n&&!it.has(n)){E(`_getElementsByName - element not allowed: ${a}`);continue}i.push({id:a,exportValue:s,domElement:n})}return i}for(let s of document.getElementsByName(t)){let{exportValue:t}=s,a=s.getAttribute("data-element-id");if(a!==e&&!!it.has(s))i.push({id:a,exportValue:t,domElement:s})}return i}show(){this.container&&(this.container.hidden=!1),this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0),this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){let t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(let e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}get _isEditable(){return!1}_editOnDoubleClick(){if(!this._isEditable)return;let{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})})}}class ia extends is{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0}),this.isTooltipOnly=t.data.isTooltipOnly}render(){let{data:t,linkService:e}=this,i=document.createElement("a");i.setAttribute("data-element-id",t.id);let s=!1;return t.url?(e.addLinkAttributes(i,t.url,t.newWindow),s=!0):t.action?(this._bindNamedAction(i,t.action),s=!0):t.attachment?(this.#iN(i,t.attachment,t.attachmentDest),s=!0):t.setOCGState?(this.#iB(i,t.setOCGState),s=!0):t.dest?(this._bindLink(i,t.dest),s=!0):(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(i,t),s=!0),t.resetForm?(this._bindResetFormAction(i,t.resetForm),s=!0):this.isTooltipOnly&&!s&&(this._bindLink(i,""),s=!0)),this.container.classList.add("linkAnnotation"),s&&this.container.append(i),this.container}#iH(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e),t.onclick=()=>(e&&this.linkService.goToDestination(e),!1),(e||""===e)&&this.#iH()}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeNamedAction(e),!1),this.#iH()}#iN(t,e,i=null){t.href=this.linkService.getAnchorUrl(""),e.description&&(t.title=e.description),t.onclick=()=>(this.downloadManager?.openOrDownloadData(e.content,e.filename,i),!1),this.#iH()}#iB(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeSetOCGState(e),!1),this.#iH()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");let i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(let s of Object.keys(e.actions)){let a=i.get(s);if(!!a)t[a]=()=>(this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:s}}),!1)}!t.onclick&&(t.onclick=()=>!1),this.#iH()}_bindResetFormAction(t,e){let i=t.onclick;if(!i&&(t.href=this.linkService.getAnchorUrl("")),this.#iH(),!this._fieldObjects){E('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),!i&&(t.onclick=()=>!1);return}t.onclick=()=>{i?.();let{fields:t,refs:s,include:a}=e,r=[];if(0!==t.length||0!==s.length){let e=new Set(s);for(let i of t)for(let{id:t}of this._fieldObjects[i]||[])e.add(t);for(let t of Object.values(this._fieldObjects))for(let i of t)e.has(i.id)===a&&r.push(i)}else for(let t of Object.values(this._fieldObjects))r.push(...t);let n=this.annotationStorage,o=[];for(let t of r){let{id:e}=t;switch(o.push(e),t.type){case"text":{let i=t.defaultValue||"";n.setValue(e,{value:i});break}case"checkbox":case"radiobutton":{let i=t.defaultValue===t.exportValues;n.setValue(e,{value:i});break}case"combobox":case"listbox":{let i=t.defaultValue||"";n.setValue(e,{value:i});break}default:continue}let i=document.querySelector(`[data-element-id="${e}"]`);if(!!i){if(!it.has(i)){E(`_bindResetFormAction - element not allowed: ${e}`);continue}i.dispatchEvent(new Event("resetform"))}}return this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:o,name:"ResetForm"}}),!1}}}class ir extends is{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");let t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class io extends is{render(){return this.container}showElementAndHideCanvas(t){this.data.hasOwnCanvas&&(t.previousSibling?.nodeName==="CANVAS"&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return z.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,i,s,a){i.includes("mouse")?t.addEventListener(i,t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:a(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})}):t.addEventListener(i,t=>{if("blur"===i){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===i){if(e.focused)return;e.focused=!0}if(!!a)this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:a(t)}})})}_setEventListeners(t,e,i,s){for(let[a,r]of i)("Action"===r||this.data.actions?.[r])&&(("Focus"===r||"Blur"===r)&&(e||={focused:!1}),this._setEventListener(t,e,a,r,s),"Focus"!==r||this.data.actions?.Blur?"Blur"===r&&!this.data.actions?.Focus&&this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null))}_setBackgroundColor(t){let e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":j.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){let e;let{fontColor:i}=this.data.defaultAppearanceData,s=this.data.defaultAppearanceData.fontSize||9,a=t.style,r=t=>Math.round(10*t)/10;if(this.data.multiLine){let t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),i=Math.round(t/(1.35*s))||1;e=Math.min(s,r(t/i/1.35))}else e=Math.min(s,r(Math.abs(this.data.rect[3]-this.data.rect[1]-2)/1.35));a.fontSize=`calc(${e}px * var(--scale-factor))`,a.color=j.makeHexColor(i[0],i[1],i[2]),null!==this.data.textAlignment&&(a.textAlign=["left","center","right"][this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class il extends io{constructor(t){super(t,{isRenderable:t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,i,s){let a=this.annotationStorage;for(let r of this._getElementsByName(t.name,t.id))r.domElement&&(r.domElement[e]=i),a.setValue(r.id,{[s]:i})}render(){let t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let i=null;if(this.renderForms){let s=t.getValue(e,{value:this.data.fieldValue}),a=s.value||"",r=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;r&&a.length>r&&(a=a.slice(0,r));let n=s.formattedValue||this.data.textContent?.join("\n")||null;n&&this.data.comb&&(n=n.replaceAll(/\s+/g,""));let o={userValue:a,formattedValue:n,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?((i=document.createElement("textarea")).textContent=n??a,this.data.doNotScroll&&(i.style.overflowY="hidden")):((i=document.createElement("input")).type="text",i.setAttribute("value",n??a),this.data.doNotScroll&&(i.style.overflowX="hidden")),this.data.hasOwnCanvas&&(i.hidden=!0),it.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,i.name=this.data.fieldName,i.tabIndex=1e3,this._setRequired(i,this.data.required),r&&(i.maxLength=r),i.addEventListener("input",s=>{t.setValue(e,{value:s.target.value}),this.setPropertyOnSiblings(i,"value",s.target.value,"value"),o.formattedValue=null}),i.addEventListener("resetform",t=>{let e=this.data.defaultFieldValue??"";i.value=o.userValue=e,o.formattedValue=null});let l=t=>{let{formattedValue:e}=o;null!=e&&(t.target.value=e),t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){i.addEventListener("focus",t=>{if(o.focused)return;let{target:e}=t;o.userValue&&(e.value=o.userValue),o.lastCommittedValue=e.value,o.commitKey=1,!this.data.actions?.Focus&&(o.focused=!0)}),i.addEventListener("updatefromsandbox",i=>{this.showElementAndHideCanvas(i.target);this._dispatchEventFromSandbox({value(i){o.userValue=i.detail.value??"",t.setValue(e,{value:o.userValue.toString()}),i.target.value=o.userValue},formattedValue(i){let{formattedValue:s}=i.detail;o.formattedValue=s,null!=s&&i.target!==document.activeElement&&(i.target.value=s),t.setValue(e,{formattedValue:s})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:i=>{let{charLimit:s}=i.detail,{target:a}=i;if(0===s){a.removeAttribute("maxLength");return}a.setAttribute("maxLength",s);let r=o.userValue;if(!!r&&!(r.length<=s))r=r.slice(0,s),a.value=o.userValue=r,t.setValue(e,{value:r}),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:r,willCommit:!0,commitKey:1,selStart:a.selectionStart,selEnd:a.selectionEnd}})}},i)}),i.addEventListener("keydown",t=>{o.commitKey=1;let i=-1;if("Escape"===t.key?i=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(o.commitKey=3):i=2,-1===i)return;let{value:s}=t.target;if(o.lastCommittedValue!==s)o.lastCommittedValue=s,o.userValue=s,this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}})});let s=l;l=null,i.addEventListener("blur",t=>{if(!o.focused||!t.relatedTarget)return;!this.data.actions?.Blur&&(o.focused=!1);let{value:i}=t.target;o.userValue=i,o.lastCommittedValue!==i&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,willCommit:!0,commitKey:o.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}),s(t)}),this.data.actions?.Keystroke&&i.addEventListener("beforeinput",t=>{o.lastCommittedValue=null;let{data:i,target:s}=t,{value:a,selectionStart:r,selectionEnd:n}=s,l=r,h=n;switch(t.inputType){case"deleteWordBackward":{let t=a.substring(0,r).match(/\w*[^\w]*$/);t&&(l-=t[0].length);break}case"deleteWordForward":{let t=a.substring(r).match(/^[^\w]*\w*/);t&&(h+=t[0].length);break}case"deleteContentBackward":r===n&&(l-=1);break;case"deleteContentForward":r===n&&(h+=1)}t.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:a,change:i||"",willCommit:!1,selStart:l,selEnd:h}})}),this._setEventListeners(i,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],t=>t.target.value)}if(l&&i.addEventListener("blur",l),this.data.comb){let t=(this.data.rect[2]-this.data.rect[0])/r;i.classList.add("comb"),i.style.letterSpacing=`calc(${t}px * var(--scale-factor) - 1ch)`}}else(i=document.createElement("div")).textContent=this.data.fieldValue,i.style.verticalAlign="middle",i.style.display="table-cell",this.data.hasOwnCanvas&&(i.hidden=!0);return this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class ih extends io{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class id extends io{constructor(t){super(t,{isRenderable:t.renderForms})}render(){let t=this.annotationStorage,e=this.data,i=e.id,s=t.getValue(i,{value:e.exportValue===e.fieldValue}).value;"string"==typeof s&&(s="Off"!==s,t.setValue(i,{value:s})),this.container.classList.add("buttonWidgetAnnotation","checkBox");let a=document.createElement("input");return it.add(a),a.setAttribute("data-element-id",i),a.disabled=e.readOnly,this._setRequired(a,this.data.required),a.type="checkbox",a.name=e.fieldName,s&&a.setAttribute("checked",!0),a.setAttribute("exportValue",e.exportValue),a.tabIndex=1e3,a.addEventListener("change",s=>{let{name:a,checked:r}=s.target;for(let s of this._getElementsByName(a,i)){let i=r&&s.exportValue===e.exportValue;s.domElement&&(s.domElement.checked=i),t.setValue(s.id,{value:i})}t.setValue(i,{value:r})}),a.addEventListener("resetform",t=>{let i=e.defaultFieldValue||"Off";t.target.checked=i===e.exportValue}),this.enableScripting&&this.hasJSActions&&(a.addEventListener("updatefromsandbox",e=>{this._dispatchEventFromSandbox({value(e){e.target.checked="Off"!==e.detail.value,t.setValue(i,{value:e.target.checked})}},e)}),this._setEventListeners(a,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],t=>t.target.checked)),this._setBackgroundColor(a),this._setDefaultPropertiesFromJS(a),this.container.append(a),this.container}}class ic extends io{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");let t=this.annotationStorage,e=this.data,i=e.id,s=t.getValue(i,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof s&&(s=s!==e.buttonValue,t.setValue(i,{value:s})),s)for(let s of this._getElementsByName(e.fieldName,i))t.setValue(s.id,{value:!1});let a=document.createElement("input");if(it.add(a),a.setAttribute("data-element-id",i),a.disabled=e.readOnly,this._setRequired(a,this.data.required),a.type="radio",a.name=e.fieldName,s&&a.setAttribute("checked",!0),a.tabIndex=1e3,a.addEventListener("change",e=>{let{name:s,checked:a}=e.target;for(let e of this._getElementsByName(s,i))t.setValue(e.id,{value:!1});t.setValue(i,{value:a})}),a.addEventListener("resetform",t=>{let i=e.defaultFieldValue;t.target.checked=null!=i&&i===e.buttonValue}),this.enableScripting&&this.hasJSActions){let s=e.buttonValue;a.addEventListener("updatefromsandbox",e=>{this._dispatchEventFromSandbox({value:e=>{let a=s===e.detail.value;for(let s of this._getElementsByName(e.target.name)){let e=a&&s.id===i;s.domElement&&(s.domElement.checked=e),t.setValue(s.id,{value:e})}}},e)}),this._setEventListeners(a,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],t=>t.target.checked)}return this._setBackgroundColor(a),this._setDefaultPropertiesFromJS(a),this.container.append(a),this.container}}class iu extends ia{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){let t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");let e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",t=>{this._dispatchEventFromSandbox({},t)})),t}}class ip extends io{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");let t=this.annotationStorage,e=this.data.id,i=t.getValue(e,{value:this.data.fieldValue}),s=document.createElement("select");it.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,this._setRequired(s,this.data.required),s.name=this.data.fieldName,s.tabIndex=1e3;let a=this.data.combo&&this.data.options.length>0;for(let t of(!this.data.combo&&(s.size=this.data.options.length,this.data.multiSelect&&(s.multiple=!0)),s.addEventListener("resetform",t=>{let e=this.data.defaultFieldValue;for(let t of s.options)t.selected=t.value===e}),this.data.options)){let e=document.createElement("option");e.textContent=t.displayValue,e.value=t.exportValue,i.value.includes(t.exportValue)&&(e.setAttribute("selected",!0),a=!1),s.append(e)}let r=null;if(a){let t=document.createElement("option");t.value=" ",t.setAttribute("hidden",!0),t.setAttribute("selected",!0),s.prepend(t),r=()=>{t.remove(),s.removeEventListener("input",r),r=null},s.addEventListener("input",r)}let n=t=>{let e=t?"value":"textContent",{options:i,multiple:a}=s;return a?Array.prototype.filter.call(i,t=>t.selected).map(t=>t[e]):-1===i.selectedIndex?null:i[i.selectedIndex][e]},o=n(!1),l=t=>{let e=t.target.options;return Array.prototype.map.call(e,t=>({displayValue:t.textContent,exportValue:t.value}))};return this.enableScripting&&this.hasJSActions?(s.addEventListener("updatefromsandbox",i=>{this._dispatchEventFromSandbox({value(i){r?.();let a=i.detail.value,l=new Set(Array.isArray(a)?a:[a]);for(let t of s.options)t.selected=l.has(t.value);t.setValue(e,{value:n(!0)}),o=n(!1)},multipleSelection(t){s.multiple=!0},remove(i){let a=s.options,r=i.detail.remove;a[r].selected=!1,s.remove(r),a.length>0&&-1===Array.prototype.findIndex.call(a,t=>t.selected)&&(a[0].selected=!0),t.setValue(e,{value:n(!0),items:l(i)}),o=n(!1)},clear(i){for(;0!==s.length;)s.remove(0);t.setValue(e,{value:null,items:[]}),o=n(!1)},insert(i){let{index:a,displayValue:r,exportValue:h}=i.detail.insert,d=s.children[a],c=document.createElement("option");c.textContent=r,c.value=h,d?d.before(c):s.append(c),t.setValue(e,{value:n(!0),items:l(i)}),o=n(!1)},items(i){let{items:a}=i.detail;for(;0!==s.length;)s.remove(0);for(let t of a){let{displayValue:e,exportValue:i}=t,a=document.createElement("option");a.textContent=e,a.value=i,s.append(a)}s.options.length>0&&(s.options[0].selected=!0),t.setValue(e,{value:n(!0),items:l(i)}),o=n(!1)},indices(i){let s=new Set(i.detail.indices);for(let t of i.target.options)t.selected=s.has(t.index);t.setValue(e,{value:n(!0)}),o=n(!1)},editable(t){t.target.disabled=!t.detail.editable}},i)}),s.addEventListener("input",i=>{let s=n(!0),a=n(!1);t.setValue(e,{value:s}),i.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:o,change:a,changeEx:s,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(s,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],t=>t.target.value)):s.addEventListener("input",function(i){t.setValue(e,{value:n(!0)})}),this.data.combo&&this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class ig extends is{constructor(t){let{data:e,elements:i}=t;super(t,{isRenderable:is._hasPopupData(e)}),this.elements=i,this.popup=null}render(){this.container.classList.add("popupAnnotation");let t=this.popup=new im({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(let i of this.elements)i.popup=t,e.push(i.data.id),i.addHighlightArea();return this.container.setAttribute("aria-controls",e.map(t=>`${q}${t}`).join(",")),this.container}}class im{#iz=this.#iU.bind(this);#ij=this.#i$.bind(this);#iG=this.#iV.bind(this);#iq=this.#iW.bind(this);#iK=null;#tm=null;#iX=null;#iY=null;#iQ=null;#iJ=null;#iZ=null;#i0=!1;#i1=null;#x=null;#i2=null;#i3=null;#i5=null;#iI=null;#i6=!1;constructor({container:t,color:e,elements:i,titleObj:s,modificationDate:a,contentsObj:r,richText:n,parent:o,rect:l,parentRect:h,open:d}){for(let d of(this.#tm=t,this.#i5=s,this.#iX=r,this.#i3=n,this.#iJ=o,this.#iK=e,this.#i2=l,this.#iZ=h,this.#iQ=i,this.#iY=tf.toDateObject(a),this.trigger=i.flatMap(t=>t.getElementsToTriggerPopup()),this.trigger))d.addEventListener("click",this.#iq),d.addEventListener("mouseenter",this.#iG),d.addEventListener("mouseleave",this.#ij),d.classList.add("popupTriggerArea");for(let t of i)t.container?.addEventListener("keydown",this.#iz);this.#tm.hidden=!0,d&&this.#iW()}render(){if(this.#i1)return;let t=this.#i1=document.createElement("div");if(t.className="popup",this.#iK){let e=t.style.outlineColor=j.makeHexColor(...this.#iK);CSS.supports("background-color","color-mix(in srgb, red 30%, white)")?t.style.backgroundColor=`color-mix(in srgb, ${e} 30%, white)`:t.style.backgroundColor=j.makeHexColor(...this.#iK.map(t=>Math.floor(.7*(255-t)+t)))}let e=document.createElement("span");e.className="header";let i=document.createElement("h1");if(e.append(i),{dir:i.dir,str:i.textContent}=this.#i5,t.append(e),this.#iY){let t=document.createElement("span");t.classList.add("popupDate"),t.setAttribute("data-l10n-id","pdfjs-annotation-date-string"),t.setAttribute("data-l10n-args",JSON.stringify({date:this.#iY.toLocaleDateString(),time:this.#iY.toLocaleTimeString()})),e.append(t)}let s=this.#i4;if(s)e9.render({xfaHtml:s,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{let e=this._formatContents(this.#iX);t.append(e)}this.#tm.append(t)}get #i4(){let t=this.#i3,e=this.#iX;return t?.str&&(!e?.str||e.str===t.str)&&this.#i3.html||null}get #i8(){return this.#i4?.attributes?.style?.fontSize||0}get #i7(){return this.#i4?.attributes?.style?.color||null}#i9(t){let e=[],i={style:{color:this.#i7,fontSize:this.#i8?`calc(${this.#i8}px * var(--scale-factor))`:""}};for(let s of t.split("\n"))e.push({name:"span",value:s,attributes:i});return{str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}}}_formatContents({str:t,dir:e}){let i=document.createElement("p");i.classList.add("popupContent"),i.dir=e;let s=t.split(/(?:\r\n?|\n)/);for(let t=0,e=s.length;t<e;++t){let a=s[t];i.append(document.createTextNode(a)),t<e-1&&i.append(document.createElement("br"))}return i}#iU(t){if(!t.altKey&&!t.shiftKey&&!t.ctrlKey&&!t.metaKey)("Enter"===t.key||"Escape"===t.key&&this.#i0)&&this.#iW()}updateEdited({rect:t,popupContent:e}){this.#iI||={contentsObj:this.#iX,richText:this.#i3},t&&(this.#x=null),e&&(this.#i3=this.#i9(e),this.#iX=null),this.#i1?.remove(),this.#i1=null}resetEdited(){if(!!this.#iI)({contentsObj:this.#iX,richText:this.#i3}=this.#iI),this.#iI=null,this.#i1?.remove(),this.#i1=null,this.#x=null}#st(){if(null!==this.#x)return;let{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:i,pageX:s,pageY:a}}}=this.#iJ,r=!!this.#iZ,n=r?this.#iZ:this.#i2;for(let t of this.#iQ)if(!n||null!==j.intersect(t.data.rect,n)){n=t.data.rect,r=!0;break}let o=j.normalizeRect([n[0],t[3]-n[1]+t[1],n[2],t[3]-n[3]+t[1]]),l=r?n[2]-n[0]+5:0,h=o[0]+l,d=o[1];this.#x=[100*(h-s)/e,100*(d-a)/i];let{style:c}=this.#tm;c.left=`${this.#x[0]}%`,c.top=`${this.#x[1]}%`}#iW(){this.#i0=!this.#i0,this.#i0?(this.#iV(),this.#tm.addEventListener("click",this.#iq),this.#tm.addEventListener("keydown",this.#iz)):(this.#i$(),this.#tm.removeEventListener("click",this.#iq),this.#tm.removeEventListener("keydown",this.#iz))}#iV(){!this.#i1&&this.render(),this.isVisible?this.#i0&&this.#tm.classList.add("focused"):(this.#st(),this.#tm.hidden=!1,this.#tm.style.zIndex=parseInt(this.#tm.style.zIndex)+1e3)}#i$(){if(this.#tm.classList.remove("focused"),!this.#i0&&!!this.isVisible)this.#tm.hidden=!0,this.#tm.style.zIndex=parseInt(this.#tm.style.zIndex)-1e3}forceHide(){if(this.#i6=this.isVisible,!!this.#i6)this.#tm.hidden=!0}maybeShow(){if(!!this.#i6)!this.#i1&&this.#iV(),this.#i6=!1,this.#tm.hidden=!1}get isVisible(){return!1===this.#tm.hidden}}class ib extends is{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=c.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){let t=document.createElement("div");for(let e of(t.classList.add("annotationTextContent"),t.setAttribute("role","comment"),this.textContent)){let i=document.createElement("span");i.textContent=e,t.append(i)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}get _isEditable(){return this.data.hasOwnCanvas}}class iv extends is{#se=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");let t=this.data,{width:e,height:i}=ie(t.rect),s=this.svgFactory.create(e,i,!0),a=this.#se=this.svgFactory.createElement("svg:line");return a.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]),a.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]),a.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]),a.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]),a.setAttribute("stroke-width",t.borderStyle.width||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),s.append(a),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#se}addHighlightArea(){this.container.classList.add("highlightArea")}}class iA extends is{#si=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");let t=this.data,{width:e,height:i}=ie(t.rect),s=this.svgFactory.create(e,i,!0),a=t.borderStyle.width,r=this.#si=this.svgFactory.createElement("svg:rect");return r.setAttribute("x",a/2),r.setAttribute("y",a/2),r.setAttribute("width",e-a),r.setAttribute("height",i-a),r.setAttribute("stroke-width",a||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),s.append(r),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#si}addHighlightArea(){this.container.classList.add("highlightArea")}}class iy extends is{#ss=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");let t=this.data,{width:e,height:i}=ie(t.rect),s=this.svgFactory.create(e,i,!0),a=t.borderStyle.width,r=this.#ss=this.svgFactory.createElement("svg:ellipse");return r.setAttribute("cx",e/2),r.setAttribute("cy",i/2),r.setAttribute("rx",e/2-a/2),r.setAttribute("ry",i/2-a/2),r.setAttribute("stroke-width",a||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),s.append(r),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#ss}addHighlightArea(){this.container.classList.add("highlightArea")}}class i_ extends is{#sa=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);let t=this.data,{width:e,height:i}=ie(t.rect),s=this.svgFactory.create(e,i,!0),a=[];for(let e of t.vertices){let i=e.x-t.rect[0],s=t.rect[3]-e.y;a.push(i+","+s)}a=a.join(" ");let r=this.#sa=this.svgFactory.createElement(this.svgElementName);return r.setAttribute("points",a),r.setAttribute("stroke-width",t.borderStyle.width||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),s.append(r),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#sa}addHighlightArea(){this.container.classList.add("highlightArea")}}class iw extends i_{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class ix extends is{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class iE extends is{#sr=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=c.INK}render(){this.container.classList.add(this.containerClassName);let t=this.data,{width:e,height:i}=ie(t.rect),s=this.svgFactory.create(e,i,!0);for(let e of t.inkLists){let i=[];for(let s of e){let e=s.x-t.rect[0],a=t.rect[3]-s.y;i.push(`${e},${a}`)}i=i.join(" ");let a=this.svgFactory.createElement(this.svgElementName);this.#sr.push(a),a.setAttribute("points",i),a.setAttribute("stroke-width",t.borderStyle.width||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),!t.popupRef&&this.hasPopupData&&this._createPopup(),s.append(a)}return this.container.append(s),this.container}getElementsToTriggerPopup(){return this.#sr}addHighlightArea(){this.container.classList.add("highlightArea")}}class iC extends is{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this.container}}class iS extends is{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class iT extends is{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class iM extends is{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class ik extends is{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("stampAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class iL extends is{#sn=null;constructor(t){super(t,{isRenderable:!0});let{file:e}=this.data;this.filename=e.filename,this.content=e.content,this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...e})}render(){let t;this.container.classList.add("fileAttachmentAnnotation");let{container:e,data:i}=this;i.hasAppearance||0===i.fillAlpha?t=document.createElement("div"):((t=document.createElement("img")).src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(i.name)?"paperclip":"pushpin"}.svg`,i.fillAlpha&&i.fillAlpha<1&&(t.style=`filter: opacity(${Math.round(100*i.fillAlpha)}%);`)),t.addEventListener("dblclick",this.#so.bind(this)),this.#sn=t;let{isMac:s}=z.platform;return e.addEventListener("keydown",t=>{"Enter"===t.key&&(s?t.metaKey:t.ctrlKey)&&this.#so()}),!i.popupRef&&this.hasPopupData?this._createPopup():t.classList.add("popupTriggerArea"),e.append(t),e}getElementsToTriggerPopup(){return this.#sn}addHighlightArea(){this.container.classList.add("highlightArea")}#so(){this.downloadManager?.openOrDownloadData(this.content,this.filename)}}class iP{#sl=null;#sh=null;#sd=new Map;constructor({div:t,accessibilityManager:e,annotationCanvasMap:i,annotationEditorUIManager:s,page:a,viewport:r}){this.div=t,this.#sl=e,this.#sh=i,this.page=a,this.viewport=r,this.zIndex=0,this._annotationEditorUIManager=s}#sc(t,e){let i=t.firstChild||t;i.id=`${q}${e}`,this.div.append(t),this.#sl?.moveElementInDOM(this.div,t,i,!1)}async render(t){let{annotations:e}=t,i=this.div;ty(i,this.viewport);let s=new Map,a={data:null,layer:i,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new ta,annotationStorage:t.annotationStorage||new tF,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(let t of e){if(t.noHTML)continue;let e=t.annotationType===m.POPUP;if(e){let e=s.get(t.id);if(!e)continue;a.elements=e}else{let{width:e,height:i}=ie(t.rect);if(e<=0||i<=0)continue}a.data=t;let i=ii.create(a);if(!i.isRenderable)continue;if(!e&&t.popupRef){let e=s.get(t.popupRef);e?e.push(i):s.set(t.popupRef,[i])}let r=i.render();t.hidden&&(r.style.visibility="hidden"),this.#sc(r,t.id),i.annotationEditorType>0&&(this.#sd.set(i.data.id,i),this._annotationEditorUIManager?.renderAnnotationElement(i))}this.#su()}update({viewport:t}){let e=this.div;this.viewport=t,ty(e,{rotation:t.rotation}),this.#su(),e.hidden=!1}#su(){if(!this.#sh)return;let t=this.div;for(let[e,i]of this.#sh){let s=t.querySelector(`[data-annotation-id="${e}"]`);if(!s)continue;i.className="annotationContent";let{firstChild:a}=s;a?"CANVAS"===a.nodeName?a.replaceWith(i):a.classList.contains("annotationContent")?a.after(i):a.before(i):s.append(i)}this.#sh.clear()}getEditableAnnotations(){return Array.from(this.#sd.values())}getEditableAnnotation(t){return this.#sd.get(t)}}let iR=/\r\n?|\n/g;class iI extends tP{#sp=this.editorDivBlur.bind(this);#sg=this.editorDivFocus.bind(this);#sf=this.editorDivInput.bind(this);#sm=this.editorDivKeydown.bind(this);#sb=this.editorDivPaste.bind(this);#iK;#sv="";#sA=`${this.id}-editor`;#i8;#sy=null;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){let t=iI.prototype,e=t=>t.isEmpty(),i=tk.TRANSLATE_SMALL,s=tk.TRANSLATE_BIG;return M(this,"_keyboardManager",new tT([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-s,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[s,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-s],checker:e}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,s],checker:e}]]))}static _type="freetext";static _editorType=c.FREETEXT;constructor(t){super({...t,name:"freeTextEditor"}),this.#iK=t.color||iI._defaultColor||tP._defaultLineColor,this.#i8=t.fontSize||iI._defaultFontSize}static initialize(t,e){tP.initialize(t,e,{strings:["pdfjs-free-text-default-content"]});let i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case u.FREETEXT_SIZE:iI._defaultFontSize=e;break;case u.FREETEXT_COLOR:iI._defaultColor=e}}updateParams(t,e){switch(t){case u.FREETEXT_SIZE:this.#s_(e);break;case u.FREETEXT_COLOR:this.#sw(e)}}static get defaultPropertiesToUpdate(){return[[u.FREETEXT_SIZE,iI._defaultFontSize],[u.FREETEXT_COLOR,iI._defaultColor||tP._defaultLineColor]]}get propertiesToUpdate(){return[[u.FREETEXT_SIZE,this.#i8],[u.FREETEXT_COLOR,this.#iK]]}#s_(t){let e=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--scale-factor))`,this.translate(0,-(t-this.#i8)*this.parentScale),this.#i8=t,this.#sx()},i=this.#i8;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:u.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#sw(t){let e=t=>{this.#iK=this.editorDiv.style.color=t},i=this.#iK;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:u.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){let t=this.parentScale;return[-iI._internalPadding*t,-(iI._internalPadding+this.#i8)*t]}rebuild(){if(!this.parent)return;if(super.rebuild(),null!==this.div)!this.isAttachedToDOM&&this.parent.add(this)}enableEditMode(){if(!this.isInEditMode())this.parent.setEditingState(!1),this.parent.updateToolbar(c.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.editorDiv.addEventListener("keydown",this.#sm),this.editorDiv.addEventListener("focus",this.#sg),this.editorDiv.addEventListener("blur",this.#sp),this.editorDiv.addEventListener("input",this.#sf),this.editorDiv.addEventListener("paste",this.#sb)}disableEditMode(){if(!!this.isInEditMode())this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",this.#sA),this._isDraggable=!0,this.editorDiv.removeEventListener("keydown",this.#sm),this.editorDiv.removeEventListener("focus",this.#sg),this.editorDiv.removeEventListener("blur",this.#sp),this.editorDiv.removeEventListener("input",this.#sf),this.editorDiv.removeEventListener("paste",this.#sb),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing")}focusin(t){if(!!this._focusEventsAllowed)super.focusin(t),t.target!==this.editorDiv&&this.editorDiv.focus()}onceAdded(){if(!this.width)this.enableEditMode(),this.editorDiv.focus(),this._initialOptions?.isCentered&&this.center(),this._initialOptions=null}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}#sE(){let t=[];for(let e of(this.editorDiv.normalize(),this.editorDiv.childNodes))t.push(iI.#sC(e));return t.join("\n")}#sx(){let t;let[e,i]=this.parentDimensions;if(this.isAttachedToDOM)t=this.div.getBoundingClientRect();else{let{currentLayer:e,div:i}=this,s=i.style.display,a=i.classList.contains("hidden");i.classList.remove("hidden"),i.style.display="hidden",e.div.append(this.div),t=i.getBoundingClientRect(),i.remove(),i.style.display=s,i.classList.toggle("hidden",a)}this.rotation%180==this.parentRotation%180?(this.width=t.width/e,this.height=t.height/i):(this.width=t.height/e,this.height=t.width/i),this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();let t=this.#sv,e=this.#sv=this.#sE().trimEnd();if(t===e)return;let i=t=>{if(this.#sv=t,!t){this.remove();return}this.#sS(),this._uiManager.rebuild(this),this.#sx()};this.addCommands({cmd:()=>{i(e)},undo:()=>{i(t)},mustExec:!1}),this.#sx()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(t){this.enterInEditMode()}keydown(t){t.target===this.div&&"Enter"===t.key&&(this.enterInEditMode(),t.preventDefault())}editorDivKeydown(t){iI._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){let t,e;if(this.div)return this.div;this.width&&(t=this.x,e=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",this.#sA),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text"),this.enableEditing(),tP._l10nPromise.get("pdfjs-free-text-default-content").then(t=>this.editorDiv?.setAttribute("default-content",t)),this.editorDiv.contentEditable=!0;let{style:i}=this.editorDiv;if(i.fontSize=`calc(${this.#i8}px * var(--scale-factor))`,i.color=this.#iK,this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),tx(this,this.div,["dblclick","keydown"]),this.width){let[i,s]=this.parentDimensions;if(this.annotationElementId){let a,r;let{position:n}=this.#sy,[o,l]=this.getInitialTranslation();[o,l]=this.pageTranslationToScreen(o,l);let[h,d]=this.pageDimensions,[c,u]=this.pageTranslation;switch(this.rotation){case 0:a=t+(n[0]-c)/h,r=e+this.height-(n[1]-u)/d;break;case 90:a=t+(n[0]-c)/h,r=e-(n[1]-u)/d,[o,l]=[l,-o];break;case 180:a=t-this.width+(n[0]-c)/h,r=e-(n[1]-u)/d,[o,l]=[-o,-l];break;case 270:a=t+(n[0]-c-this.height*d)/h,r=e+(n[1]-u-this.width*h)/d,[o,l]=[-l,o]}this.setAt(a*i,r*s,o,l)}else this.setAt(t*i,e*s,this.width*i,this.height*s);this.#sS(),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}static #sC(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(iR,"")}editorDivPaste(t){let e=t.clipboardData||window.clipboardData,{types:i}=e;if(1===i.length&&"text/plain"===i[0])return;t.preventDefault();let s=iI.#sT(e.getData("text")||"").replaceAll(iR,"\n");if(!s)return;let a=window.getSelection();if(!a.rangeCount)return;this.editorDiv.normalize(),a.deleteFromDocument();let r=a.getRangeAt(0);if(!s.includes("\n")){r.insertNode(document.createTextNode(s)),this.editorDiv.normalize(),a.collapseToStart();return}let{startContainer:n,startOffset:o}=r,l=[],h=[];if(n.nodeType===Node.TEXT_NODE){let t=n.parentElement;if(h.push(n.nodeValue.slice(o).replaceAll(iR,"")),t!==this.editorDiv){let e=l;for(let i of this.editorDiv.childNodes){if(i===t){e=h;continue}e.push(iI.#sC(i))}}l.push(n.nodeValue.slice(0,o).replaceAll(iR,""))}else if(n===this.editorDiv){let t=l,e=0;for(let i of this.editorDiv.childNodes)e++===o&&(t=h),t.push(iI.#sC(i))}this.#sv=`${l.join("\n")}${s}${h.join("\n")}`,this.#sS();let d=new Range,c=l.reduce((t,e)=>t+e.length,0);for(let{firstChild:t}of this.editorDiv.childNodes)if(t.nodeType===Node.TEXT_NODE){let e=t.nodeValue.length;if(c<=e){d.setStart(t,c),d.setEnd(t,c);break}c-=e}a.removeAllRanges(),a.addRange(d)}#sS(){if(this.editorDiv.replaceChildren(),!!this.#sv)for(let t of this.#sv.split("\n")){let e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br")),this.editorDiv.append(e)}}#sM(){return this.#sv.replaceAll("\xa0"," ")}static #sT(t){return t.replaceAll(" ","\xa0")}get contentDiv(){return this.editorDiv}static deserialize(t,e,i){let s=null;if(t instanceof ib){let{data:{defaultAppearanceData:{fontSize:e,fontColor:i},rect:a,rotation:r,id:n},textContent:o,textPosition:l,parent:{page:{pageNumber:h}}}=t;if(!o||0===o.length)return null;s=t={annotationType:c.FREETEXT,color:Array.from(i),fontSize:e,value:o.join("\n"),position:l,pageIndex:h-1,rect:a.slice(0),rotation:r,id:n,deleted:!1}}let a=super.deserialize(t,e,i);return a.#i8=t.fontSize,a.#iK=j.makeHexColor(...t.color),a.#sv=iI.#sT(t.value),a.annotationElementId=t.id||null,a.#sy=s,a}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return{pageIndex:this.pageIndex,id:this.annotationElementId,deleted:!0};let e=iI._internalPadding*this.parentScale,i=this.getRect(e,e),s=tP._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#iK),a={annotationType:c.FREETEXT,color:s,fontSize:this.#i8,value:this.#sM(),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?a:this.annotationElementId&&!this.#sk(a)?null:(a.id=this.annotationElementId,a)}#sk(t){let{value:e,fontSize:i,color:s,pageIndex:a}=this.#sy;return this._hasBeenMoved||t.value!==e||t.fontSize!==i||t.color.some((t,e)=>t!==s[e])||t.pageIndex!==a}renderAnnotationElement(t){let e=super.renderAnnotationElement(t);if(this.deleted)return e;let{style:i}=e;for(let t of(i.fontSize=`calc(${this.#i8}px * var(--scale-factor))`,i.color=this.#iK,e.replaceChildren(),this.#sv.split("\n"))){let i=document.createElement("div");i.append(t?document.createTextNode(t):document.createElement("br")),e.append(i)}let s=iI._internalPadding*this.parentScale;return t.updateEdited({rect:this.getRect(s,s),popupContent:this.#sv}),e}resetAnnotationElement(t){super.resetAnnotationElement(t),t.resetEdited()}}class iD{#sL;#sP=[];#sR=[];constructor(t,e=0,i=0,s=!0){let a=1/0,r=-1/0,n=1/0,o=-1/0;for(let{x:i,y:s,width:l,height:h}of t){let t=1e-4*Math.floor((i-e)/1e-4),d=1e-4*Math.ceil((i+l+e)/1e-4),c=1e-4*Math.floor((s-e)/1e-4),u=1e-4*Math.ceil((s+h+e)/1e-4),p=[t,c,u,!0],g=[d,c,u,!1];this.#sP.push(p,g),a=Math.min(a,t),r=Math.max(r,d),n=Math.min(n,c),o=Math.max(o,u)}let l=r-a+2*i,h=o-n+2*i,d=a-i,c=n-i,u=this.#sP.at(s?-1:-2),p=[u[0],u[2]];for(let t of this.#sP){let[e,i,s]=t;t[0]=(e-d)/l,t[1]=(i-c)/h,t[2]=(s-c)/h}this.#sL={x:d,y:c,width:l,height:h,lastPoint:p}}getOutlines(){this.#sP.sort((t,e)=>t[0]-e[0]||t[1]-e[1]||t[2]-e[2]);let t=[];for(let e of this.#sP)e[3]?(t.push(...this.#sI(e)),this.#sD(e)):(this.#sF(e),t.push(...this.#sI(e)));return this.#sO(t)}#sO(t){let e;let i=[],s=new Set;for(let e of t){let[t,s,a]=e;i.push([t,s,e],[t,a,e])}i.sort((t,e)=>t[1]-e[1]||t[0]-e[0]);for(let t=0,e=i.length;t<e;t+=2){let e=i[t][2],a=i[t+1][2];e.push(a),a.push(e),s.add(e),s.add(a)}let a=[];for(;s.size>0;){let t=s.values().next().value,[i,r,n,o,l]=t;s.delete(t);let h=i,d=r;for(e=[i,n],a.push(e);;){let t;if(s.has(o))t=o;else if(s.has(l))t=l;else break;s.delete(t),[i,r,n,o,l]=t,h!==i&&(e.push(h,d,i,d===r?r:n),h=i),d=d===r?n:r}e.push(h,d)}return new iO(a,this.#sL)}#sN(t){let e=this.#sR,i=0,s=e.length-1;for(;i<=s;){let a=i+s>>1,r=e[a][0];if(r===t)return a;r<t?i=a+1:s=a-1}return s+1}#sD([,t,e]){let i=this.#sN(t);this.#sR.splice(i,0,[t,e])}#sF([,t,e]){let i=this.#sN(t);for(let s=i;s<this.#sR.length;s++){let[i,a]=this.#sR[s];if(i!==t)break;if(i===t&&a===e){this.#sR.splice(s,1);return}}for(let s=i-1;s>=0;s--){let[i,a]=this.#sR[s];if(i!==t)break;if(i===t&&a===e){this.#sR.splice(s,1);return}}}#sI(t){let[e,i,s]=t,a=[[e,i,s]],r=this.#sN(s);for(let t=0;t<r;t++){let[i,s]=this.#sR[t];for(let t=0,r=a.length;t<r;t++){let[,n,o]=a[t];if(!(s<=n)&&!(o<=i)){if(n>=i){if(o>s)a[t][1]=s;else{if(1===r)return[];a.splice(t,1),t--,r--}continue}a[t][2]=i,o>s&&a.push([e,s,o])}}}return a}}class iF{toSVGPath(){throw Error("Abstract method `toSVGPath` must be implemented.")}get box(){throw Error("Abstract getter `box` must be implemented.")}serialize(t,e){throw Error("Abstract method `serialize` must be implemented.")}get free(){return this instanceof iB}}class iO extends iF{#sL;#sB;constructor(t,e){super(),this.#sB=t,this.#sL=e}toSVGPath(){let t=[];for(let e of this.#sB){let[i,s]=e;t.push(`M${i} ${s}`);for(let a=2;a<e.length;a+=2){let r=e[a],n=e[a+1];r===i?(t.push(`V${n}`),s=n):n===s&&(t.push(`H${r}`),i=r)}t.push("Z")}return t.join(" ")}serialize([t,e,i,s],a){let r=[],n=i-t,o=s-e;for(let e of this.#sB){let i=Array(e.length);for(let a=0;a<e.length;a+=2)i[a]=t+e[a]*n,i[a+1]=s-e[a+1]*o;r.push(i)}return r}get box(){return this.#sL}}class iN{#sL;#sH=[];#sz;#sU;#sj=[];#s$=new Float64Array(18);#sG;#sV;#sq;#sW;#sK;#sX;#sY=[];static #sQ=8;static #sJ=2;static #sZ=iN.#sQ+iN.#sJ;constructor({x:t,y:e},i,s,a,r,n=0){this.#sL=i,this.#sX=a*s,this.#sU=r,this.#s$.set([NaN,NaN,NaN,NaN,t,e],6),this.#sz=n,this.#sW=iN.#sQ*s,this.#sq=iN.#sZ*s,this.#sK=s,this.#sY.push(t,e)}get free(){return!0}isEmpty(){return isNaN(this.#s$[8])}#s0(){let t=this.#s$.subarray(4,6),e=this.#s$.subarray(16,18),[i,s,a,r]=this.#sL;return[(this.#sG+(t[0]-e[0])/2-i)/a,(this.#sV+(t[1]-e[1])/2-s)/r,(this.#sG+(e[0]-t[0])/2-i)/a,(this.#sV+(e[1]-t[1])/2-s)/r]}add({x:t,y:e}){this.#sG=t,this.#sV=e;let[i,s,a,r]=this.#sL,[n,o,l,h]=this.#s$.subarray(8,12),d=t-l,c=e-h,u=Math.hypot(d,c);if(u<this.#sq)return!1;let p=u-this.#sW,g=p/u,f=g*d,m=g*c,b=n,v=o;n=l,o=h,l+=f,h+=m,this.#sY?.push(t,e);let A=-m/p*this.#sX,y=f/p*this.#sX;return(this.#s$.set(this.#s$.subarray(2,8),0),this.#s$.set([l+A,h+y],4),this.#s$.set(this.#s$.subarray(14,18),12),this.#s$.set([l-A,h-y],16),isNaN(this.#s$[6]))?(0===this.#sj.length&&(this.#s$.set([n+A,o+y],2),this.#sj.push(NaN,NaN,NaN,NaN,(n+A-i)/a,(o+y-s)/r),this.#s$.set([n-A,o-y],14),this.#sH.push(NaN,NaN,NaN,NaN,(n-A-i)/a,(o-y-s)/r)),this.#s$.set([b,v,n,o,l,h],6),!this.isEmpty()):(this.#s$.set([b,v,n,o,l,h],6),Math.abs(Math.atan2(v-o,b-n)-Math.atan2(m,f))<Math.PI/2)?([n,o,l,h]=this.#s$.subarray(2,6),this.#sj.push(NaN,NaN,NaN,NaN,((n+l)/2-i)/a,((o+h)/2-s)/r),[n,o,b,v]=this.#s$.subarray(14,18),this.#sH.push(NaN,NaN,NaN,NaN,((b+n)/2-i)/a,((v+o)/2-s)/r),!0):([b,v,n,o,l,h]=this.#s$.subarray(0,6),this.#sj.push(((b+5*n)/6-i)/a,((v+5*o)/6-s)/r,((5*n+l)/6-i)/a,((5*o+h)/6-s)/r,((n+l)/2-i)/a,((o+h)/2-s)/r),[l,h,n,o,b,v]=this.#s$.subarray(12,18),this.#sH.push(((b+5*n)/6-i)/a,((v+5*o)/6-s)/r,((5*n+l)/6-i)/a,((5*o+h)/6-s)/r,((n+l)/2-i)/a,((o+h)/2-s)/r),!0)}toSVGPath(){if(this.isEmpty())return"";let t=this.#sj,e=this.#sH,i=this.#s$.subarray(4,6),s=this.#s$.subarray(16,18),[a,r,n,o]=this.#sL,[l,h,d,c]=this.#s0();if(isNaN(this.#s$[6])&&!this.isEmpty())return`M${(this.#s$[2]-a)/n} ${(this.#s$[3]-r)/o} L${(this.#s$[4]-a)/n} ${(this.#s$[5]-r)/o} L${l} ${h} L${d} ${c} L${(this.#s$[16]-a)/n} ${(this.#s$[17]-r)/o} L${(this.#s$[14]-a)/n} ${(this.#s$[15]-r)/o} Z`;let u=[];u.push(`M${t[4]} ${t[5]}`);for(let e=6;e<t.length;e+=6)isNaN(t[e])?u.push(`L${t[e+4]} ${t[e+5]}`):u.push(`C${t[e]} ${t[e+1]} ${t[e+2]} ${t[e+3]} ${t[e+4]} ${t[e+5]}`);u.push(`L${(i[0]-a)/n} ${(i[1]-r)/o} L${l} ${h} L${d} ${c} L${(s[0]-a)/n} ${(s[1]-r)/o}`);for(let t=e.length-6;t>=6;t-=6)isNaN(e[t])?u.push(`L${e[t+4]} ${e[t+5]}`):u.push(`C${e[t]} ${e[t+1]} ${e[t+2]} ${e[t+3]} ${e[t+4]} ${e[t+5]}`);return u.push(`L${e[4]} ${e[5]} Z`),u.join(" ")}getOutlines(){let t=this.#sj,e=this.#sH,i=this.#s$,s=i.subarray(4,6),a=i.subarray(16,18),[r,n,o,l]=this.#sL,h=new Float64Array((this.#sY?.length??0)+2);for(let t=0,e=h.length-2;t<e;t+=2)h[t]=(this.#sY[t]-r)/o,h[t+1]=(this.#sY[t+1]-n)/l;h[h.length-2]=(this.#sG-r)/o,h[h.length-1]=(this.#sV-n)/l;let[d,c,u,p]=this.#s0();if(isNaN(i[6])&&!this.isEmpty()){let t=new Float64Array(36);return t.set([NaN,NaN,NaN,NaN,(i[2]-r)/o,(i[3]-n)/l,NaN,NaN,NaN,NaN,(i[4]-r)/o,(i[5]-n)/l,NaN,NaN,NaN,NaN,d,c,NaN,NaN,NaN,NaN,u,p,NaN,NaN,NaN,NaN,(i[16]-r)/o,(i[17]-n)/l,NaN,NaN,NaN,NaN,(i[14]-r)/o,(i[15]-n)/l],0),new iB(t,h,this.#sL,this.#sK,this.#sz,this.#sU)}let g=new Float64Array(this.#sj.length+24+this.#sH.length),f=t.length;for(let e=0;e<f;e+=2){if(isNaN(t[e])){g[e]=g[e+1]=NaN;continue}g[e]=t[e],g[e+1]=t[e+1]}g.set([NaN,NaN,NaN,NaN,(s[0]-r)/o,(s[1]-n)/l,NaN,NaN,NaN,NaN,d,c,NaN,NaN,NaN,NaN,u,p,NaN,NaN,NaN,NaN,(a[0]-r)/o,(a[1]-n)/l],f),f+=24;for(let t=e.length-6;t>=6;t-=6)for(let i=0;i<6;i+=2){if(isNaN(e[t+i])){g[f]=g[f+1]=NaN,f+=2;continue}g[f]=e[t+i],g[f+1]=e[t+i+1],f+=2}return g.set([NaN,NaN,NaN,NaN,e[4],e[5]],f),new iB(g,h,this.#sL,this.#sK,this.#sz,this.#sU)}}class iB extends iF{#sL;#s1=null;#sz;#sU;#sY;#sK;#s2;constructor(t,e,i,s,a,r){super(),this.#s2=t,this.#sY=e,this.#sL=i,this.#sK=s,this.#sz=a,this.#sU=r,this.#s3(r);let{x:n,y:o,width:l,height:h}=this.#s1;for(let e=0,i=t.length;e<i;e+=2)t[e]=(t[e]-n)/l,t[e+1]=(t[e+1]-o)/h;for(let t=0,i=e.length;t<i;t+=2)e[t]=(e[t]-n)/l,e[t+1]=(e[t+1]-o)/h}toSVGPath(){let t=[`M${this.#s2[4]} ${this.#s2[5]}`];for(let e=6,i=this.#s2.length;e<i;e+=6){if(isNaN(this.#s2[e])){t.push(`L${this.#s2[e+4]} ${this.#s2[e+5]}`);continue}t.push(`C${this.#s2[e]} ${this.#s2[e+1]} ${this.#s2[e+2]} ${this.#s2[e+3]} ${this.#s2[e+4]} ${this.#s2[e+5]}`)}return t.push("Z"),t.join(" ")}serialize([t,e,i,s],a){let r,n;let o=i-t,l=s-e;switch(a){case 0:r=this.#s5(this.#s2,t,s,o,-l),n=this.#s5(this.#sY,t,s,o,-l);break;case 90:r=this.#s6(this.#s2,t,e,o,l),n=this.#s6(this.#sY,t,e,o,l);break;case 180:r=this.#s5(this.#s2,i,e,-o,l),n=this.#s5(this.#sY,i,e,-o,l);break;case 270:r=this.#s6(this.#s2,i,s,-o,-l),n=this.#s6(this.#sY,i,s,-o,-l)}return{outline:Array.from(r),points:[Array.from(n)]}}#s5(t,e,i,s,a){let r=new Float64Array(t.length);for(let n=0,o=t.length;n<o;n+=2)r[n]=e+t[n]*s,r[n+1]=i+t[n+1]*a;return r}#s6(t,e,i,s,a){let r=new Float64Array(t.length);for(let n=0,o=t.length;n<o;n+=2)r[n]=e+t[n+1]*s,r[n+1]=i+t[n]*a;return r}#s3(t){let e=this.#s2,i=e[4],s=e[5],a=i,r=s,n=i,o=s,l=i,h=s,d=t?Math.max:Math.min;for(let t=6,c=e.length;t<c;t+=6){if(isNaN(e[t]))a=Math.min(a,e[t+4]),r=Math.min(r,e[t+5]),n=Math.max(n,e[t+4]),o=Math.max(o,e[t+5]),h<e[t+5]?(l=e[t+4],h=e[t+5]):h===e[t+5]&&(l=d(l,e[t+4]));else{let c=j.bezierBoundingBox(i,s,...e.slice(t,t+6));a=Math.min(a,c[0]),r=Math.min(r,c[1]),n=Math.max(n,c[2]),o=Math.max(o,c[3]),h<c[3]?(l=c[2],h=c[3]):h===c[3]&&(l=d(l,c[2]))}i=e[t+4],s=e[t+5]}let c=a-this.#sz,u=r-this.#sz,p=n-a+2*this.#sz,g=o-r+2*this.#sz;this.#s1={x:c,y:u,width:p,height:g,lastPoint:[l,h]}}get box(){return this.#s1}getNewOutline(t,e){let{x:i,y:s,width:a,height:r}=this.#s1,[n,o,l,h]=this.#sL,d=a*l,c=r*h,u=i*l+n,p=s*h+o,g=new iN({x:this.#sY[0]*d+u,y:this.#sY[1]*c+p},this.#sL,this.#sK,t,this.#sU,e??this.#sz);for(let t=2;t<this.#sY.length;t+=2)g.add({x:this.#sY[t]*d+u,y:this.#sY[t+1]*c+p});return g.getOutlines()}}class iH{#iz=this.#iU.bind(this);#s4=this.#n.bind(this);#s8=null;#s7=null;#s9;#at=null;#ae=!1;#ai=!1;#a=null;#as;#u=null;#aa;static get _keyboardManager(){return M(this,"_keyboardManager",new tT([[["Escape","mac+Escape"],iH.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],iH.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],iH.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],iH.prototype._moveToPrevious],[["Home","mac+Home"],iH.prototype._moveToBeginning],[["End","mac+End"],iH.prototype._moveToEnd]]))}constructor({editor:t=null,uiManager:e=null}){t?(this.#ai=!1,this.#aa=u.HIGHLIGHT_COLOR,this.#a=t):(this.#ai=!0,this.#aa=u.HIGHLIGHT_DEFAULT_COLOR),this.#u=t?._uiManager||e,this.#as=this.#u._eventBus,this.#s9=t?.color||this.#u?.highlightColors.values().next().value||"#FFFF98"}renderButton(){let t=this.#s8=document.createElement("button");t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.setAttribute("aria-haspopup",!0),t.addEventListener("click",this.#ar.bind(this)),t.addEventListener("keydown",this.#iz);let e=this.#s7=document.createElement("span");return e.className="swatch",e.setAttribute("aria-hidden",!0),e.style.backgroundColor=this.#s9,t.append(e),t}renderMainDropdown(){let t=this.#at=this.#an();return t.setAttribute("aria-orientation","horizontal"),t.setAttribute("aria-labelledby","highlightColorPickerLabel"),t}#an(){let t=document.createElement("div");for(let[e,i]of(t.addEventListener("contextmenu",tp),t.className="dropdown",t.role="listbox",t.setAttribute("aria-multiselectable",!1),t.setAttribute("aria-orientation","vertical"),t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown"),this.#u.highlightColors)){let s=document.createElement("button");s.tabIndex="0",s.role="option",s.setAttribute("data-color",i),s.title=e,s.setAttribute("data-l10n-id",`pdfjs-editor-colorpicker-${e}`);let a=document.createElement("span");s.append(a),a.className="swatch",a.style.backgroundColor=i,s.setAttribute("aria-selected",i===this.#s9),s.addEventListener("click",this.#ao.bind(this,i)),t.append(s)}return t.addEventListener("keydown",this.#iz),t}#ao(t,e){e.stopPropagation(),this.#as.dispatch("switchannotationeditorparams",{source:this,type:this.#aa,value:t})}_colorSelectFromKeyboard(t){if(t.target===this.#s8){this.#ar(t);return}let e=t.target.getAttribute("data-color");if(!!e)this.#ao(e,t)}_moveToNext(t){if(!this.#al){this.#ar(t);return}if(t.target===this.#s8){this.#at.firstChild?.focus();return}t.target.nextSibling?.focus()}_moveToPrevious(t){if(t.target===this.#at?.firstChild||t.target===this.#s8){this.#al&&this._hideDropdownFromKeyboard();return}!this.#al&&this.#ar(t),t.target.previousSibling?.focus()}_moveToBeginning(t){if(!this.#al){this.#ar(t);return}this.#at.firstChild?.focus()}_moveToEnd(t){if(!this.#al){this.#ar(t);return}this.#at.lastChild?.focus()}#iU(t){iH._keyboardManager.exec(this,t)}#ar(t){if(this.#al){this.hideDropdown();return}if(this.#ae=0===t.detail,window.addEventListener("pointerdown",this.#s4),this.#at){this.#at.classList.remove("hidden");return}let e=this.#at=this.#an();this.#s8.append(e)}#n(t){if(!this.#at?.contains(t.target))this.hideDropdown()}hideDropdown(){this.#at?.classList.add("hidden"),window.removeEventListener("pointerdown",this.#s4)}get #al(){return this.#at&&!this.#at.classList.contains("hidden")}_hideDropdownFromKeyboard(){if(!this.#ai){if(!this.#al){this.#a?.unselect();return}this.hideDropdown(),this.#s8.focus({preventScroll:!0,focusVisible:this.#ae})}}updateColor(t){if(this.#s7&&(this.#s7.style.backgroundColor=t),!this.#at)return;let e=this.#u.highlightColors.values();for(let i of this.#at.children)i.setAttribute("aria-selected",e.next().value===t)}destroy(){this.#s8?.remove(),this.#s8=null,this.#s7=null,this.#at?.remove(),this.#at=null}}class iz extends tP{#ah=null;#ad=0;#ac;#au=null;#s=null;#ap=null;#ag=null;#af=0;#am=null;#ab=null;#m=null;#av=!1;#tr=this.#aA.bind(this);#ay=null;#a_;#aw=null;#ax="";#sX;#aE="";static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=12;static _l10nPromise;static _type="highlight";static _editorType=c.HIGHLIGHT;static _freeHighlightId=-1;static _freeHighlight=null;static _freeHighlightClipId="";static get _keyboardManager(){let t=iz.prototype;return M(this,"_keyboardManager",new tT([[["ArrowLeft","mac+ArrowLeft"],t._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],t._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],t._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],t._moveCaret,{args:[3]}]]))}constructor(t){super({...t,name:"highlightEditor"}),this.color=t.color||iz._defaultColor,this.#sX=t.thickness||iz._defaultThickness,this.#a_=t.opacity||iz._defaultOpacity,this.#ac=t.boxes||null,this.#aE=t.methodOfCreation||"",this.#ax=t.text||"",this._isDraggable=!1,t.highlightId>-1?(this.#av=!0,this.#aC(t),this.#aS()):(this.#ah=t.anchorNode,this.#ad=t.anchorOffset,this.#ag=t.focusNode,this.#af=t.focusOffset,this.#aT(),this.#aS(),this.rotate(this.rotation))}get telemetryInitialData(){return{action:"added",type:this.#av?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:this.#sX,methodOfCreation:this.#aE}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}#aT(){let t=new iD(this.#ac,.001);this.#ab=t.getOutlines(),{x:this.x,y:this.y,width:this.width,height:this.height}=this.#ab.box;let e=new iD(this.#ac,.0025,.001,"ltr"===this._uiManager.direction);this.#ap=e.getOutlines();let{lastPoint:i}=this.#ap.box;this.#ay=[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height]}#aC({highlightOutlines:t,highlightId:e,clipPathId:i}){this.#ab=t;if(this.#ap=t.getNewOutline(this.#sX/2****,.0025),e>=0)this.#m=e,this.#au=i,this.parent.drawLayer.finalizeLine(e,t),this.#aw=this.parent.drawLayer.highlightOutline(this.#ap);else if(this.parent){let e=this.parent.viewport.rotation;this.parent.drawLayer.updateLine(this.#m,t),this.parent.drawLayer.updateBox(this.#m,iz.#aM(this.#ab.box,(e-this.rotation+360)%360)),this.parent.drawLayer.updateLine(this.#aw,this.#ap),this.parent.drawLayer.updateBox(this.#aw,iz.#aM(this.#ap.box,e))}let{x:s,y:a,width:r,height:n}=t.box;switch(this.rotation){case 0:this.x=s,this.y=a,this.width=r,this.height=n;break;case 90:{let[t,e]=this.parentDimensions;this.x=a,this.y=1-s,this.width=r*e/t,this.height=n*t/e;break}case 180:this.x=1-s,this.y=1-a,this.width=r,this.height=n;break;case 270:{let[t,e]=this.parentDimensions;this.x=1-a,this.y=s,this.width=r*e/t,this.height=n*t/e}}let{lastPoint:o}=this.#ap.box;this.#ay=[(o[0]-s)/r,(o[1]-a)/n]}static initialize(t,e){tP.initialize(t,e),iz._defaultColor||=e.highlightColors?.values().next().value||"#fff066"}static updateDefaultParams(t,e){switch(t){case u.HIGHLIGHT_DEFAULT_COLOR:iz._defaultColor=e;break;case u.HIGHLIGHT_THICKNESS:iz._defaultThickness=e}}translateInPage(t,e){}get toolbarPosition(){return this.#ay}updateParams(t,e){switch(t){case u.HIGHLIGHT_COLOR:this.#sw(e);break;case u.HIGHLIGHT_THICKNESS:this.#ak(e)}}static get defaultPropertiesToUpdate(){return[[u.HIGHLIGHT_DEFAULT_COLOR,iz._defaultColor],[u.HIGHLIGHT_THICKNESS,iz._defaultThickness]]}get propertiesToUpdate(){return[[u.HIGHLIGHT_COLOR,this.color||iz._defaultColor],[u.HIGHLIGHT_THICKNESS,this.#sX||iz._defaultThickness],[u.HIGHLIGHT_FREE,this.#av]]}#sw(t){let e=t=>{this.color=t,this.parent?.drawLayer.changeColor(this.#m,t),this.#s?.updateColor(t)},i=this.color;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:u.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(t)},!0)}#ak(t){let e=this.#sX,i=t=>{this.#sX=t,this.#aL(t)};this.addCommands({cmd:i.bind(this,t),undo:i.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:u.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:t},!0)}async addEditToolbar(){let t=await super.addEditToolbar();return t?(this._uiManager.highlightColors&&(this.#s=new iH({editor:this}),t.addColorPicker(this.#s)),t):null}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(this.#aP())}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,this.#aP())}onceAdded(){this.parent.addUndoableEditor(this),this.div.focus()}remove(){this.#aR(),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){if(!this.parent)return;if(super.rebuild(),null!==this.div)this.#aS(),!this.isAttachedToDOM&&this.parent.add(this)}setParent(t){let e=!1;this.parent&&!t?this.#aR():t&&(this.#aS(t),e=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(t),this.show(this._isVisible),e&&this.select()}#aL(t){if(!this.#av)return;this.#aC({highlightOutlines:this.#ab.getNewOutline(t/2)}),this.fixAndSetPosition();let[e,i]=this.parentDimensions;this.setDims(this.width*e,this.height*i)}#aR(){if(null!==this.#m&&!!this.parent)this.parent.drawLayer.remove(this.#m),this.#m=null,this.parent.drawLayer.remove(this.#aw),this.#aw=null}#aS(t=this.parent){if(null===this.#m)({id:this.#m,clipPathId:this.#au}=t.drawLayer.highlight(this.#ab,this.color,this.#a_)),this.#aw=t.drawLayer.highlightOutline(this.#ap),this.#am&&(this.#am.style.clipPath=this.#au)}static #aM({x:t,y:e,width:i,height:s},a){switch(a){case 90:return{x:1-e-s,y:t,width:s,height:i};case 180:return{x:1-t-i,y:1-e-s,width:i,height:s};case 270:return{x:e,y:1-t-i,width:s,height:i}}return{x:t,y:e,width:i,height:s}}rotate(t){let e;let{drawLayer:i}=this.parent;this.#av?(t=(t-this.rotation+360)%360,e=iz.#aM(this.#ab.box,t)):e=iz.#aM(this,t),i.rotate(this.#m,t),i.rotate(this.#aw,t),i.updateBox(this.#m,e),i.updateBox(this.#aw,iz.#aM(this.#ap.box,t))}render(){if(this.div)return this.div;let t=super.render();this.#ax&&(t.setAttribute("aria-label",this.#ax),t.setAttribute("role","mark")),this.#av?t.classList.add("free"):this.div.addEventListener("keydown",this.#tr);let e=this.#am=document.createElement("div");t.append(e),e.setAttribute("aria-hidden","true"),e.className="internal",e.style.clipPath=this.#au;let[i,s]=this.parentDimensions;return this.setDims(this.width*i,this.height*s),tx(this,this.#am,["pointerover","pointerleave"]),this.enableEditing(),t}pointerover(){this.parent.drawLayer.addClass(this.#aw,"hovered")}pointerleave(){this.parent.drawLayer.removeClass(this.#aw,"hovered")}#aA(t){iz._keyboardManager.exec(this,t)}_moveCaret(t){switch(this.parent.unselect(this),t){case 0:case 2:this.#aI(!0);break;case 1:case 3:this.#aI(!1)}}#aI(t){if(!this.#ah)return;let e=window.getSelection();t?e.setPosition(this.#ah,this.#ad):e.setPosition(this.#ag,this.#af)}select(){if(super.select(),!!this.#aw)this.parent?.drawLayer.removeClass(this.#aw,"hovered"),this.parent?.drawLayer.addClass(this.#aw,"selected")}unselect(){if(super.unselect(),!!this.#aw)this.parent?.drawLayer.removeClass(this.#aw,"selected"),!this.#av&&this.#aI(!1)}get _mustFixPosition(){return!this.#av}show(t=this._isVisible){super.show(t),this.parent&&(this.parent.drawLayer.show(this.#m,t),this.parent.drawLayer.show(this.#aw,t))}#aP(){return this.#av?this.rotation:0}#aD(){if(this.#av)return null;let[t,e]=this.pageDimensions,i=this.#ac,s=Array(8*i.length),a=0;for(let{x:r,y:n,width:o,height:l}of i){let i=r*t,h=(1-n-l)*e;s[a]=s[a+4]=i,s[a+1]=s[a+3]=h,s[a+2]=s[a+6]=i+o*t,s[a+5]=s[a+7]=h+l*e,a+=8}return s}#aF(t){return this.#ab.serialize(t,this.#aP())}static startHighlighting(t,e,{target:i,x:s,y:a}){let{x:r,y:n,width:o,height:l}=i.getBoundingClientRect(),h=e=>{this.#aO(t,e)},d={capture:!0,passive:!1},c=t=>{t.preventDefault(),t.stopPropagation()},u=e=>{i.removeEventListener("pointermove",h),window.removeEventListener("blur",u),window.removeEventListener("pointerup",u),window.removeEventListener("pointerdown",c,d),window.removeEventListener("contextmenu",tp),this.#aN(t,e)};window.addEventListener("blur",u),window.addEventListener("pointerup",u),window.addEventListener("pointerdown",c,d),window.addEventListener("contextmenu",tp),i.addEventListener("pointermove",h),this._freeHighlight=new iN({x:s,y:a},[r,n,o,l],t.scale,this._defaultThickness/2,e,.001),{id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.highlight(this._freeHighlight,this._defaultColor,this._defaultOpacity,!0)}static #aO(t,e){this._freeHighlight.add(e)&&t.drawLayer.updatePath(this._freeHighlightId,this._freeHighlight)}static #aN(t,e){this._freeHighlight.isEmpty()?t.drawLayer.removeFreeHighlight(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""}static deserialize(t,e,i){let s=super.deserialize(t,e,i),{rect:[a,r,n,o],color:l,quadPoints:h}=t;s.color=j.makeHexColor(...l),s.#a_=t.opacity;let[d,c]=s.pageDimensions;s.width=(n-a)/d,s.height=(o-r)/c;let u=s.#ac=[];for(let t=0;t<h.length;t+=8)u.push({x:(h[4]-n)/d,y:(o-(1-h[t+5]))/c,width:(h[t+2]-h[t])/d,height:(h[t+5]-h[t+1])/c});return s.#aT(),s}serialize(t=!1){if(this.isEmpty()||t)return null;let e=this.getRect(0,0),i=tP._colorManager.convert(this.color);return{annotationType:c.HIGHLIGHT,color:i,opacity:this.#a_,thickness:this.#sX,quadPoints:this.#aD(),outlines:this.#aF(e),pageIndex:this.pageIndex,rect:e,rotation:this.#aP(),structTreeParentId:this._structTreeParentId}}static canCreateNewEmptyEditor(){return!1}}class iU extends tP{#aB=0;#aH=0;#az=this.canvasPointermove.bind(this);#aU=this.canvasPointerleave.bind(this);#aj=this.canvasPointerup.bind(this);#a$=this.canvasPointerdown.bind(this);#aG=null;#aV=new Path2D;#aq=!1;#aW=!1;#aK=!1;#aX=null;#aY=0;#aQ=0;#aJ=null;static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=1;static _type="ink";static _editorType=c.INK;constructor(t){super({...t,name:"inkEditor"}),this.color=t.color||null,this.thickness=t.thickness||null,this.opacity=t.opacity||null,this.paths=[],this.bezierPath2D=[],this.allRawPaths=[],this.currentPath=[],this.scaleFactor=1,this.translationX=this.translationY=0,this.x=0,this.y=0,this._willKeepAspectRatio=!0}static initialize(t,e){tP.initialize(t,e)}static updateDefaultParams(t,e){switch(t){case u.INK_THICKNESS:iU._defaultThickness=e;break;case u.INK_COLOR:iU._defaultColor=e;break;case u.INK_OPACITY:iU._defaultOpacity=e/100}}updateParams(t,e){switch(t){case u.INK_THICKNESS:this.#ak(e);break;case u.INK_COLOR:this.#sw(e);break;case u.INK_OPACITY:this.#aZ(e)}}static get defaultPropertiesToUpdate(){return[[u.INK_THICKNESS,iU._defaultThickness],[u.INK_COLOR,iU._defaultColor||tP._defaultLineColor],[u.INK_OPACITY,Math.round(100*iU._defaultOpacity)]]}get propertiesToUpdate(){return[[u.INK_THICKNESS,this.thickness||iU._defaultThickness],[u.INK_COLOR,this.color||iU._defaultColor||tP._defaultLineColor],[u.INK_OPACITY,Math.round(100*(this.opacity??iU._defaultOpacity))]]}#ak(t){let e=t=>{this.thickness=t,this.#a0()},i=this.thickness;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:u.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})}#sw(t){let e=t=>{this.color=t,this.#a1()},i=this.color;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:u.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})}#aZ(t){let e=t=>{this.opacity=t,this.#a1()};t/=100;let i=this.opacity;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:u.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})}rebuild(){if(!this.parent)return;if(super.rebuild(),null!==this.div)!this.canvas&&(this.#a2(),this.#a3()),!this.isAttachedToDOM&&(this.parent.add(this),this.#a5()),this.#a0()}remove(){if(null!==this.canvas)!this.isEmpty()&&this.commit(),this.canvas.width=this.canvas.height=0,this.canvas.remove(),this.canvas=null,this.#aG&&(clearTimeout(this.#aG),this.#aG=null),this.#aX.disconnect(),this.#aX=null,super.remove()}setParent(t){!this.parent&&t?this._uiManager.removeShouldRescale(this):this.parent&&null===t&&this._uiManager.addShouldRescale(this),super.setParent(t)}onScaleChanging(){let[t,e]=this.parentDimensions,i=this.width*t,s=this.height*e;this.setDimensions(i,s)}enableEditMode(){if(!this.#aq&&null!==this.canvas)super.enableEditMode(),this._isDraggable=!1,this.canvas.addEventListener("pointerdown",this.#a$)}disableEditMode(){if(!!this.isInEditMode()&&null!==this.canvas)super.disableEditMode(),this._isDraggable=!this.isEmpty(),this.div.classList.remove("editing"),this.canvas.removeEventListener("pointerdown",this.#a$)}onceAdded(){this._isDraggable=!this.isEmpty()}isEmpty(){return 0===this.paths.length||1===this.paths.length&&0===this.paths[0].length}#a6(){let{parentRotation:t,parentDimensions:[e,i]}=this;switch(t){case 90:return[0,i,i,e];case 180:return[e,i,e,i];case 270:return[e,0,i,e];default:return[0,0,e,i]}}#a4(){let{ctx:t,color:e,opacity:i,thickness:s,parentScale:a,scaleFactor:r}=this;t.lineWidth=s*a/r,t.lineCap="round",t.lineJoin="round",t.miterLimit=10,t.strokeStyle=`${e}${Math.round(Math.min(255,Math.max(1,255*i))).toString(16).padStart(2,"0")}`}#a8(t,e){this.canvas.addEventListener("contextmenu",tp),this.canvas.addEventListener("pointerleave",this.#aU),this.canvas.addEventListener("pointermove",this.#az),this.canvas.addEventListener("pointerup",this.#aj),this.canvas.removeEventListener("pointerdown",this.#a$),this.isEditing=!0,!this.#aK&&(this.#aK=!0,this.#a5(),this.thickness||=iU._defaultThickness,this.color||=iU._defaultColor||tP._defaultLineColor,this.opacity??=iU._defaultOpacity),this.currentPath.push([t,e]),this.#aW=!1,this.#a4(),this.#aJ=()=>{this.#a7(),this.#aJ&&window.requestAnimationFrame(this.#aJ)},window.requestAnimationFrame(this.#aJ)}#a9(t,e){let[i,s]=this.currentPath.at(-1);if(this.currentPath.length>1&&t===i&&e===s)return;let a=this.currentPath,r=this.#aV;if(a.push([t,e]),this.#aW=!0,a.length<=2){r.moveTo(...a[0]),r.lineTo(t,e);return}3===a.length&&(this.#aV=r=new Path2D,r.moveTo(...a[0])),this.#rt(r,...a.at(-3),...a.at(-2),t,e)}#re(){if(0===this.currentPath.length)return;let t=this.currentPath.at(-1);this.#aV.lineTo(...t)}#ri(t,e){let i;if(this.#aJ=null,t=Math.min(Math.max(t,0),this.canvas.width),e=Math.min(Math.max(e,0),this.canvas.height),this.#a9(t,e),this.#re(),1!==this.currentPath.length)i=this.#rs();else{let s=[t,e];i=[[s,s.slice(),s.slice(),s]]}let s=this.#aV,a=this.currentPath;this.currentPath=[],this.#aV=new Path2D;this.addCommands({cmd:()=>{this.allRawPaths.push(a),this.paths.push(i),this.bezierPath2D.push(s),this._uiManager.rebuild(this)},undo:()=>{this.allRawPaths.pop(),this.paths.pop(),this.bezierPath2D.pop(),0===this.paths.length?this.remove():(!this.canvas&&(this.#a2(),this.#a3()),this.#a0())},mustExec:!0})}#a7(){if(!this.#aW)return;this.#aW=!1,this.thickness,this.parentScale;let t=this.currentPath.slice(-3);t.map(t=>t[0]),t.map(t=>t[1]);let{ctx:e}=this;for(let t of(e.save(),e.clearRect(0,0,this.canvas.width,this.canvas.height),this.bezierPath2D))e.stroke(t);e.stroke(this.#aV),e.restore()}#rt(t,e,i,s,a,r,n){let o=(e+s)/2,l=(i+a)/2,h=(s+r)/2,d=(a+n)/2;t.bezierCurveTo(o+2*(s-o)/3,l+2*(a-l)/3,h+2*(s-h)/3,d+2*(a-d)/3,h,d)}#rs(){let t;let e=this.currentPath;if(e.length<=2)return[[e[0],e[0],e.at(-1),e.at(-1)]];let i=[],[s,a]=e[0];for(t=1;t<e.length-2;t++){let[r,n]=e[t],[o,l]=e[t+1],h=(r+o)/2,d=(n+l)/2,c=[s+2*(r-s)/3,a+2*(n-a)/3],u=[h+2*(r-h)/3,d+2*(n-d)/3];i.push([[s,a],c,u,[h,d]]),[s,a]=[h,d]}let[r,n]=e[t],[o,l]=e[t+1],h=[s+2*(r-s)/3,a+2*(n-a)/3];return i.push([[s,a],h,[o+2*(r-o)/3,l+2*(n-l)/3],[o,l]]),i}#a1(){if(this.isEmpty()){this.#ra();return}this.#a4();let{canvas:t,ctx:e}=this;for(let i of(e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t.width,t.height),this.#ra(),this.bezierPath2D))e.stroke(i)}commit(){if(!this.#aq)super.commit(),this.isEditing=!1,this.disableEditMode(),this.setInForeground(),this.#aq=!0,this.div.classList.add("disabled"),this.#a0(!0),this.select(),this.parent.addInkEditorIfNeeded(!0),this.moveInDOM(),this.div.focus({preventScroll:!0})}focusin(t){if(!!this._focusEventsAllowed)super.focusin(t),this.enableEditMode()}canvasPointerdown(t){if(0===t.button&&!!this.isInEditMode()&&!this.#aq)this.setInForeground(),t.preventDefault(),!this.div.contains(document.activeElement)&&this.div.focus({preventScroll:!0}),this.#a8(t.offsetX,t.offsetY)}canvasPointermove(t){t.preventDefault(),this.#a9(t.offsetX,t.offsetY)}canvasPointerup(t){t.preventDefault(),this.#rr(t)}canvasPointerleave(t){this.#rr(t)}#rr(t){this.canvas.removeEventListener("pointerleave",this.#aU),this.canvas.removeEventListener("pointermove",this.#az),this.canvas.removeEventListener("pointerup",this.#aj),this.canvas.addEventListener("pointerdown",this.#a$),this.#aG&&clearTimeout(this.#aG),this.#aG=setTimeout(()=>{this.#aG=null,this.canvas.removeEventListener("contextmenu",tp)},10),this.#ri(t.offsetX,t.offsetY),this.addToAnnotationStorage(),this.setInBackground()}#a2(){this.canvas=document.createElement("canvas"),this.canvas.width=this.canvas.height=0,this.canvas.className="inkEditorCanvas",this.canvas.setAttribute("data-l10n-id","pdfjs-ink-canvas"),this.div.append(this.canvas),this.ctx=this.canvas.getContext("2d")}#a3(){this.#aX=new ResizeObserver(t=>{let e=t[0].contentRect;e.width&&e.height&&this.setDimensions(e.width,e.height)}),this.#aX.observe(this.div)}get isResizable(){return!this.isEmpty()&&this.#aq}render(){let t,e;if(this.div)return this.div;this.width&&(t=this.x,e=this.y),super.render(),this.div.setAttribute("data-l10n-id","pdfjs-ink");let[i,s,a,r]=this.#a6();if(this.setAt(i,s,0,0),this.setDims(a,r),this.#a2(),this.width){let[i,s]=this.parentDimensions;this.setAspectRatio(this.width*i,this.height*s),this.setAt(t*i,e*s,this.width*i,this.height*s),this.#aK=!0,this.#a5(),this.setDims(this.width*i,this.height*s),this.#a1(),this.div.classList.add("disabled")}else this.div.classList.add("editing"),this.enableEditMode();return this.#a3(),this.div}#a5(){if(!this.#aK)return;let[t,e]=this.parentDimensions;this.canvas.width=Math.ceil(this.width*t),this.canvas.height=Math.ceil(this.height*e),this.#ra()}setDimensions(t,e){let i=Math.round(t),s=Math.round(e);if(this.#aY===i&&this.#aQ===s)return;this.#aY=i,this.#aQ=s,this.canvas.style.visibility="hidden";let[a,r]=this.parentDimensions;this.width=t/a,this.height=e/r,this.fixAndSetPosition(),this.#aq&&this.#rn(t,e),this.#a5(),this.#a1(),this.canvas.style.visibility="visible",this.fixDims()}#rn(t,e){let i=this.#ro(),s=(t-i)/this.#aH,a=(e-i)/this.#aB;this.scaleFactor=Math.min(s,a)}#ra(){let t=this.#ro()/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+t,this.translationY*this.scaleFactor+t)}static #rl(t){let e=new Path2D;for(let i=0,s=t.length;i<s;i++){let[s,a,r,n]=t[i];0===i&&e.moveTo(...s),e.bezierCurveTo(a[0],a[1],r[0],r[1],n[0],n[1])}return e}static #rh(t,e,i){let[s,a,r,n]=e;switch(i){case 0:for(let e=0,i=t.length;e<i;e+=2)t[e]+=s,t[e+1]=n-t[e+1];break;case 90:for(let e=0,i=t.length;e<i;e+=2){let i=t[e];t[e]=t[e+1]+s,t[e+1]=i+a}break;case 180:for(let e=0,i=t.length;e<i;e+=2)t[e]=r-t[e],t[e+1]+=a;break;case 270:for(let e=0,i=t.length;e<i;e+=2){let i=t[e];t[e]=r-t[e+1],t[e+1]=n-i}break;default:throw Error("Invalid rotation")}return t}static #rd(t,e,i){let[s,a,r,n]=e;switch(i){case 0:for(let e=0,i=t.length;e<i;e+=2)t[e]-=s,t[e+1]=n-t[e+1];break;case 90:for(let e=0,i=t.length;e<i;e+=2){let i=t[e];t[e]=t[e+1]-a,t[e+1]=i-s}break;case 180:for(let e=0,i=t.length;e<i;e+=2)t[e]=r-t[e],t[e+1]-=a;break;case 270:for(let e=0,i=t.length;e<i;e+=2){let i=t[e];t[e]=n-t[e+1],t[e+1]=r-i}break;default:throw Error("Invalid rotation")}return t}#rc(t,e,i,s){let a=[],r=this.thickness/2,n=t*e+r,o=t*i+r;for(let e of this.paths){let i=[],r=[];for(let s=0,a=e.length;s<a;s++){let[l,h,d,c]=e[s];if(l[0]===c[0]&&l[1]===c[1]&&1===a){let e=t*l[0]+n,s=t*l[1]+o;i.push(e,s),r.push(e,s);break}let u=t*l[0]+n,p=t*l[1]+o,g=t*h[0]+n,f=t*h[1]+o,m=t*d[0]+n,b=t*d[1]+o,v=t*c[0]+n,A=t*c[1]+o;0===s&&(i.push(u,p),r.push(u,p)),i.push(g,f,m,b,v,A),r.push(g,f),s===a-1&&r.push(v,A)}a.push({bezier:iU.#rh(i,s,this.rotation),points:iU.#rh(r,s,this.rotation)})}return a}#ru(){let t=1/0,e=-1/0,i=1/0,s=-1/0;for(let a of this.paths)for(let[r,n,o,l]of a){let a=j.bezierBoundingBox(...r,...n,...o,...l);t=Math.min(t,a[0]),i=Math.min(i,a[1]),e=Math.max(e,a[2]),s=Math.max(s,a[3])}return[t,i,e,s]}#ro(){return this.#aq?Math.ceil(this.thickness*this.parentScale):0}#a0(t=!1){if(this.isEmpty())return;if(!this.#aq){this.#a1();return}let e=this.#ru(),i=this.#ro();this.#aH=Math.max(tP.MIN_SIZE,e[2]-e[0]),this.#aB=Math.max(tP.MIN_SIZE,e[3]-e[1]);let s=Math.ceil(i+this.#aH*this.scaleFactor),a=Math.ceil(i+this.#aB*this.scaleFactor),[r,n]=this.parentDimensions;this.width=s/r,this.height=a/n,this.setAspectRatio(s,a);let o=this.translationX,l=this.translationY;this.translationX=-e[0],this.translationY=-e[1],this.#a5(),this.#a1(),this.#aY=s,this.#aQ=a,this.setDims(s,a);let h=t?i/this.scaleFactor/2:0;this.translate(o-this.translationX-h,l-this.translationY-h)}static deserialize(t,e,i){if(t instanceof iE)return null;let s=super.deserialize(t,e,i);s.thickness=t.thickness,s.color=j.makeHexColor(...t.color),s.opacity=t.opacity;let[a,r]=s.pageDimensions,n=s.width*a,o=s.height*r,l=s.parentScale,h=t.thickness/2;s.#aq=!0,s.#aY=Math.round(n),s.#aQ=Math.round(o);let{paths:d,rect:c,rotation:u}=t;for(let{bezier:t}of d){t=iU.#rd(t,c,u);let e=[];s.paths.push(e);let i=l*(t[0]-h),a=l*(t[1]-h);for(let s=2,r=t.length;s<r;s+=6){let r=l*(t[s]-h),n=l*(t[s+1]-h),o=l*(t[s+2]-h),d=l*(t[s+3]-h),c=l*(t[s+4]-h),u=l*(t[s+5]-h);e.push([[i,a],[r,n],[o,d],[c,u]]),i=c,a=u}let r=this.#rl(e);s.bezierPath2D.push(r)}let p=s.#ru();return s.#aH=Math.max(tP.MIN_SIZE,p[2]-p[0]),s.#aB=Math.max(tP.MIN_SIZE,p[3]-p[1]),s.#rn(n,o),s}serialize(){if(this.isEmpty())return null;let t=this.getRect(0,0),e=tP._colorManager.convert(this.ctx.strokeStyle);return{annotationType:c.INK,color:e,thickness:this.thickness,opacity:this.opacity,paths:this.#rc(this.scaleFactor/this.parentScale,this.translationX,this.translationY,t),pageIndex:this.pageIndex,rect:t,rotation:this.rotation,structTreeParentId:this._structTreeParentId}}}class ij extends tP{#rp=null;#rg=null;#rf=null;#rm=null;#rb=null;#rv="";#rA=null;#aX=null;#ry=null;#r_=!1;#rw=!1;static _type="stamp";static _editorType=c.STAMP;constructor(t){super({...t,name:"stampEditor"}),this.#rm=t.bitmapUrl,this.#rb=t.bitmapFile}static initialize(t,e){tP.initialize(t,e)}static get supportedTypes(){return M(this,"supportedTypes",["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"].map(t=>`image/${t}`))}static get supportedTypesStr(){return M(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(t){return this.supportedTypes.includes(t)}static paste(t,e){e.pasteEditor(c.STAMP,{bitmapFile:t.getAsFile()})}#rx(t,e=!1){if(!t){this.remove();return}this.#rp=t.bitmap,!e&&(this.#rg=t.id,this.#r_=t.isSvg),t.file&&(this.#rv=t.file.name),this.#a2()}#rE(){this.#rf=null,this._uiManager.enableWaiting(!1),this.#rA&&this.div.focus()}#rC(){if(this.#rg){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(this.#rg).then(t=>this.#rx(t,!0)).finally(()=>this.#rE());return}if(this.#rm){let t=this.#rm;this.#rm=null,this._uiManager.enableWaiting(!0),this.#rf=this._uiManager.imageManager.getFromUrl(t).then(t=>this.#rx(t)).finally(()=>this.#rE());return}if(this.#rb){let t=this.#rb;this.#rb=null,this._uiManager.enableWaiting(!0),this.#rf=this._uiManager.imageManager.getFromFile(t).then(t=>this.#rx(t)).finally(()=>this.#rE());return}let t=document.createElement("input");t.type="file",t.accept=ij.supportedTypesStr,this.#rf=new Promise(e=>{t.addEventListener("change",async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);let e=await this._uiManager.imageManager.getFromFile(t.files[0]);this.#rx(e)}else this.remove();e()}),t.addEventListener("cancel",()=>{this.remove(),e()})}).finally(()=>this.#rE()),t.click()}remove(){this.#rg&&(this.#rp=null,this._uiManager.imageManager.deleteId(this.#rg),this.#rA?.remove(),this.#rA=null,this.#aX?.disconnect(),this.#aX=null,this.#ry&&(clearTimeout(this.#ry),this.#ry=null)),super.remove()}rebuild(){if(!this.parent){this.#rg&&this.#rC();return}if(super.rebuild(),null!==this.div)this.#rg&&null===this.#rA&&this.#rC(),!this.isAttachedToDOM&&this.parent.add(this)}onceAdded(){this._isDraggable=!0,this.div.focus()}isEmpty(){return!(this.#rf||this.#rp||this.#rm||this.#rb||this.#rg)}get isResizable(){return!0}render(){let t,e;if(this.div)return this.div;if(this.width&&(t=this.x,e=this.y),super.render(),this.div.hidden=!0,this.addAltTextButton(),this.#rp?this.#a2():this.#rC(),this.width){let[i,s]=this.parentDimensions;this.setAt(t*i,e*s,this.width*i,this.height*s)}return this.div}#a2(){let{div:t}=this,{width:e,height:i}=this.#rp,[s,a]=this.pageDimensions;if(this.width)e=this.width*s,i=this.height*a;else if(e>.75*s||i>.75*a){let t=Math.min(.75*s/e,.75*a/i);e*=t,i*=t}let[r,n]=this.parentDimensions;this.setDims(e*r/s,i*n/a),this._uiManager.enableWaiting(!1);let o=this.#rA=document.createElement("canvas");t.append(o),t.hidden=!1,this.#rS(e,i),this.#a3(),!this.#rw&&(this.parent.addUndoableEditor(this),this.#rw=!0),this._reportTelemetry({action:"inserted_image"}),this.#rv&&o.setAttribute("aria-label",this.#rv)}#rT(t,e){let[i,s]=this.parentDimensions;this.width=t/i,this.height=e/s,this.setDims(t,e),this._initialOptions?.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,null!==this.#ry&&clearTimeout(this.#ry);this.#ry=setTimeout(()=>{this.#ry=null,this.#rS(t,e)},200)}#rM(t,e){let{width:i,height:s}=this.#rp,a=i,r=s,n=this.#rp;for(;a>2*t||r>2*e;){let i=a,s=r;a>2*t&&(a=a>=16384?Math.floor(a/2)-1:Math.ceil(a/2)),r>2*e&&(r=r>=16384?Math.floor(r/2)-1:Math.ceil(r/2));let o=new OffscreenCanvas(a,r);o.getContext("2d").drawImage(n,0,0,i,s,0,0,a,r),n=o.transferToImageBitmap()}return n}#rS(t,e){t=Math.ceil(t),e=Math.ceil(e);let i=this.#rA;if(!i||i.width===t&&i.height===e)return;i.width=t,i.height=e;let s=this.#r_?this.#rp:this.#rM(t,e);if(this._uiManager.hasMLManager&&!this.hasAltText()){let i=new OffscreenCanvas(t,e).getContext("2d");i.drawImage(s,0,0,s.width,s.height,0,0,t,e),this._uiManager.mlGuess({service:"image-to-text",request:{data:i.getImageData(0,0,t,e).data,width:t,height:e,channels:4}}).then(t=>{let e=t?.output||"";this.parent&&e&&!this.hasAltText()&&(this.altTextData={altText:e,decorative:!1})})}let a=i.getContext("2d");a.filter=this._uiManager.hcmFilter,a.drawImage(s,0,0,s.width,s.height,0,0,t,e)}getImageForAltText(){return this.#rA}#rk(t){if(t){if(this.#r_){let t=this._uiManager.imageManager.getSvgUrl(this.#rg);if(t)return t}let t=document.createElement("canvas");return{width:t.width,height:t.height}=this.#rp,t.getContext("2d").drawImage(this.#rp,0,0),t.toDataURL()}if(this.#r_){let[t,e]=this.pageDimensions,i=Math.round(this.width*t*tt.PDF_TO_CSS_UNITS),s=Math.round(this.height*e*tt.PDF_TO_CSS_UNITS),a=new OffscreenCanvas(i,s);return a.getContext("2d").drawImage(this.#rp,0,0,this.#rp.width,this.#rp.height,0,0,i,s),a.transferToImageBitmap()}return structuredClone(this.#rp)}#a3(){this.#aX=new ResizeObserver(t=>{let e=t[0].contentRect;e.width&&e.height&&this.#rT(e.width,e.height)}),this.#aX.observe(this.div)}static deserialize(t,e,i){if(t instanceof ik)return null;let s=super.deserialize(t,e,i),{rect:a,bitmapUrl:r,bitmapId:n,isSvg:o,accessibilityData:l}=t;n&&i.imageManager.isValidId(n)?s.#rg=n:s.#rm=r,s.#r_=o;let[h,d]=s.pageDimensions;return s.width=(a[2]-a[0])/h,s.height=(a[3]-a[1])/d,l&&(s.altTextData=l),s}serialize(t=!1,e=null){if(this.isEmpty())return null;let i={annotationType:c.STAMP,bitmapId:this.#rg,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#r_,structTreeParentId:this._structTreeParentId};if(t)return i.bitmapUrl=this.#rk(!0),i.accessibilityData=this.altTextData,i;let{decorative:s,altText:a}=this.altTextData;if(!s&&a&&(i.accessibilityData={type:"Figure",alt:a}),null===e)return i;e.stamps||=new Map;let r=this.#r_?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(e.stamps.has(this.#rg)){if(this.#r_){let t=e.stamps.get(this.#rg);r>t.area&&(t.area=r,t.serialized.bitmap.close(),t.serialized.bitmap=this.#rk(!1))}}else e.stamps.set(this.#rg,{area:r,serialized:i}),i.bitmap=this.#rk(!1);return i}}class i${#sl;#rL=!1;#rP=null;#rR=null;#rI=null;#rD=null;#rF=null;#rO=new Map;#rN=!1;#rB=!1;#rH=!1;#rz=null;#u;static _initialized=!1;static #F=new Map([iI,iU,ij,iz].map(t=>[t._editorType,t]));constructor({uiManager:t,pageIndex:e,div:i,accessibilityManager:s,annotationLayer:a,drawLayer:r,textLayer:n,viewport:o,l10n:l}){let h=[...i$.#F.values()];if(!i$._initialized)for(let e of(i$._initialized=!0,h))e.initialize(l,t);t.registerEditorTypes(h),this.#u=t,this.pageIndex=e,this.div=i,this.#sl=s,this.#rP=a,this.viewport=o,this.#rz=n,this.drawLayer=r,this.#u.addLayer(this)}get isEmpty(){return 0===this.#rO.size}get isInvisible(){return this.isEmpty&&this.#u.getMode()===c.NONE}updateToolbar(t){this.#u.updateToolbar(t)}updateMode(t=this.#u.getMode()){switch(this.#rU(),t){case c.NONE:this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),this.disableClick();return;case c.INK:this.addInkEditorIfNeeded(!1),this.disableTextSelection(),this.togglePointerEvents(!0),this.disableClick();break;case c.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);let{classList:e}=this.div;for(let i of i$.#F.values())e.toggle(`${i._type}Editing`,t===i._editorType);this.div.hidden=!1}hasTextLayer(t){return t===this.#rz?.div}addInkEditorIfNeeded(t){if(this.#u.getMode()===c.INK){if(!t){for(let t of this.#rO.values())if(t.isEmpty()){t.setInBackground();return}}this.createAndAddNewEditor({offsetX:0,offsetY:0},!1).setInBackground()}}setEditingState(t){this.#u.setEditingState(t)}addCommands(t){this.#u.addCommands(t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){this.#rP?.div.classList.toggle("disabled",!t)}enable(){this.div.tabIndex=0,this.togglePointerEvents(!0);let t=new Set;for(let e of this.#rO.values())e.enableEditing(),e.show(!0),e.annotationElementId&&(this.#u.removeChangedExistingAnnotation(e),t.add(e.annotationElementId));if(!!this.#rP)for(let e of this.#rP.getEditableAnnotations()){if(e.hide(),this.#u.isDeletedAnnotationElement(e.data.id)||t.has(e.data.id))continue;let i=this.deserialize(e);if(!!i)this.addOrRebuild(i),i.enableEditing()}}disable(){this.#rH=!0,this.div.tabIndex=-1,this.togglePointerEvents(!1);let t=new Map,e=new Map;for(let i of this.#rO.values()){if(i.disableEditing(),!!i.annotationElementId){if(null!==i.serialize()){t.set(i.annotationElementId,i);continue}e.set(i.annotationElementId,i);this.getEditableAnnotation(i.annotationElementId)?.show(),i.remove()}}if(this.#rP)for(let i of this.#rP.getEditableAnnotations()){let{id:s}=i.data;if(this.#u.isDeletedAnnotationElement(s))continue;let a=e.get(s);if(a){a.resetAnnotationElement(i),a.show(!1),i.show();continue}(a=t.get(s))&&(this.#u.addChangedExistingAnnotation(a),a.renderAnnotationElement(i),a.show(!1)),i.show()}this.#rU(),this.isEmpty&&(this.div.hidden=!0);let{classList:i}=this.div;for(let t of i$.#F.values())i.remove(`${t._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),this.#rH=!1}getEditableAnnotation(t){return this.#rP?.getEditableAnnotation(t)||null}setActiveEditor(t){if(this.#u.getActive()!==t)this.#u.setActiveEditor(t)}enableTextSelection(){this.div.tabIndex=-1,this.#rz?.div&&!this.#rD&&(this.#rD=this.#rj.bind(this),this.#rz.div.addEventListener("pointerdown",this.#rD),this.#rz.div.classList.add("highlighting"))}disableTextSelection(){this.div.tabIndex=0,this.#rz?.div&&this.#rD&&(this.#rz.div.removeEventListener("pointerdown",this.#rD),this.#rD=null,this.#rz.div.classList.remove("highlighting"))}#rj(t){if(this.#u.unselectAll(),t.target===this.#rz.div){let{isMac:e}=z.platform;if(0===t.button&&(!t.ctrlKey||!e))this.#u.showAllEditors("highlight",!0,!0),this.#rz.div.classList.add("free"),iz.startHighlighting(this,"ltr"===this.#u.direction,t),this.#rz.div.addEventListener("pointerup",()=>{this.#rz.div.classList.remove("free")},{once:!0}),t.preventDefault()}}enableClick(){if(!this.#rI)this.#rI=this.pointerdown.bind(this),this.#rR=this.pointerup.bind(this),this.div.addEventListener("pointerdown",this.#rI),this.div.addEventListener("pointerup",this.#rR)}disableClick(){if(!!this.#rI)this.div.removeEventListener("pointerdown",this.#rI),this.div.removeEventListener("pointerup",this.#rR),this.#rI=null,this.#rR=null}attach(t){this.#rO.set(t.id,t);let{annotationElementId:e}=t;e&&this.#u.isDeletedAnnotationElement(e)&&this.#u.removeDeletedAnnotationElement(t)}detach(t){this.#rO.delete(t.id),this.#sl?.removePointerInTextLayer(t.contentDiv),!this.#rH&&t.annotationElementId&&this.#u.addDeletedAnnotationElement(t)}remove(t){this.detach(t),this.#u.removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1,!this.#rB&&this.addInkEditorIfNeeded(!1)}changeParent(t){if(t.parent!==this)t.parent&&t.annotationElementId&&(this.#u.addDeletedAnnotationElement(t.annotationElementId),tP.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),t.parent?.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div))}add(t){if(t.parent!==this||!t.isAttachedToDOM){if(this.changeParent(t),this.#u.addEditor(t),this.attach(t),!t.isAttachedToDOM){let e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(),this.#u.addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;let{activeElement:e}=document;t.div.contains(e)&&!this.#rF&&(t._focusEventsAllowed=!1,this.#rF=setTimeout(()=>{this.#rF=null,t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0}),e.focus())},0)),t._structTreeParentId=this.#sl?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||=this,t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return this.#u.getId()}get #r$(){return i$.#F.get(this.#u.getMode())}#rG(t){let e=this.#r$;return e?new e.prototype.constructor(t):null}canCreateNewEmptyEditor(){return this.#r$?.canCreateNewEmptyEditor()}pasteEditor(t,e){this.#u.updateToolbar(t),this.#u.updateMode(t);let{offsetX:i,offsetY:s}=this.#rV(),a=this.getNextId(),r=this.#rG({parent:this,id:a,x:i,y:s,uiManager:this.#u,isCentered:!0,...e});r&&this.add(r)}deserialize(t){return i$.#F.get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,this.#u)||null}createAndAddNewEditor(t,e,i={}){let s=this.getNextId(),a=this.#rG({parent:this,id:s,x:t.offsetX,y:t.offsetY,uiManager:this.#u,isCentered:e,...i});return a&&this.add(a),a}#rV(){let{x:t,y:e,width:i,height:s}=this.div.getBoundingClientRect(),a=Math.max(0,t),r=Math.max(0,e),n=Math.min(window.innerWidth,t+i),o=Math.min(window.innerHeight,e+s),l=(a+n)/2-t,h=(r+o)/2-e,[d,c]=this.viewport.rotation%180==0?[l,h]:[h,l];return{offsetX:d,offsetY:c}}addNewEditor(){this.createAndAddNewEditor(this.#rV(),!0)}setSelected(t){this.#u.setSelected(t)}toggleSelected(t){this.#u.toggleSelected(t)}isSelected(t){return this.#u.isSelected(t)}unselect(t){this.#u.unselect(t)}pointerup(t){let{isMac:e}=z.platform;if(0===t.button&&(!t.ctrlKey||!e)&&t.target===this.div&&!!this.#rN){if(this.#rN=!1,!this.#rL){this.#rL=!0;return}if(this.#u.getMode()===c.STAMP){this.#u.unselectAll();return}this.createAndAddNewEditor(t,!1)}}pointerdown(t){if(this.#u.getMode()===c.HIGHLIGHT&&this.enableTextSelection(),this.#rN){this.#rN=!1;return}let{isMac:e}=z.platform;if(0!==t.button||t.ctrlKey&&e||t.target!==this.div)return;this.#rN=!0;let i=this.#u.getActive();this.#rL=!i||i.isEmpty()}findNewParent(t,e,i){let s=this.#u.findParent(e,i);return null!==s&&s!==this&&(s.changeParent(t),!0)}destroy(){for(let t of(this.#u.getActive()?.parent===this&&(this.#u.commitOrRemove(),this.#u.setActiveEditor(null)),this.#rF&&(clearTimeout(this.#rF),this.#rF=null),this.#rO.values()))this.#sl?.removePointerInTextLayer(t.contentDiv),t.setParent(null),t.isAttachedToDOM=!1,t.div.remove();this.div=null,this.#rO.clear(),this.#u.removeLayer(this)}#rU(){for(let t of(this.#rB=!0,this.#rO.values()))t.isEmpty()&&t.remove();this.#rB=!1}render({viewport:t}){for(let e of(this.viewport=t,ty(this.div,t),this.#u.getEditors(this.pageIndex)))this.add(e),e.rebuild();this.updateMode()}update({viewport:t}){this.#u.commitOrRemove(),this.#rU();let e=this.viewport.rotation,i=t.rotation;if(this.viewport=t,ty(this.div,{rotation:i}),e!==i)for(let t of this.#rO.values())t.rotate(i);this.addInkEditorIfNeeded(!1)}get pageDimensions(){let{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return this.#u.viewParameters.realScale}}class iG{#iJ=null;#m=0;#rq=new Map;#rW=new Map;constructor({pageIndex:t}){this.pageIndex=t}setParent(t){if(!this.#iJ){this.#iJ=t;return}if(this.#iJ!==t){if(this.#rq.size>0)for(let e of this.#rq.values())e.remove(),t.append(e);this.#iJ=t}}static get _svgFactory(){return M(this,"_svgFactory",new ta)}static #rK(t,{x:e=0,y:i=0,width:s=1,height:a=1}={}){let{style:r}=t;r.top=`${100*i}%`,r.left=`${100*e}%`,r.width=`${100*s}%`,r.height=`${100*a}%`}#rX(t){let e=iG._svgFactory.create(1,1,!0);return this.#iJ.append(e),e.setAttribute("aria-hidden",!0),iG.#rK(e,t),e}#rY(t,e){let i=iG._svgFactory.createElement("clipPath");t.append(i);let s=`clip_${e}`;i.setAttribute("id",s),i.setAttribute("clipPathUnits","objectBoundingBox");let a=iG._svgFactory.createElement("use");return i.append(a),a.setAttribute("href",`#${e}`),a.classList.add("clip"),s}highlight(t,e,i,s=!1){let a=this.#m++,r=this.#rX(t.box);r.classList.add("highlight"),t.free&&r.classList.add("free");let n=iG._svgFactory.createElement("defs");r.append(n);let o=iG._svgFactory.createElement("path");n.append(o);let l=`path_p${this.pageIndex}_${a}`;o.setAttribute("id",l),o.setAttribute("d",t.toSVGPath()),s&&this.#rW.set(a,o);let h=this.#rY(n,l),d=iG._svgFactory.createElement("use");return r.append(d),r.setAttribute("fill",e),r.setAttribute("fill-opacity",i),d.setAttribute("href",`#${l}`),this.#rq.set(a,r),{id:a,clipPathId:`url(#${h})`}}highlightOutline(t){let e;let i=this.#m++,s=this.#rX(t.box);s.classList.add("highlightOutline");let a=iG._svgFactory.createElement("defs");s.append(a);let r=iG._svgFactory.createElement("path");a.append(r);let n=`path_p${this.pageIndex}_${i}`;if(r.setAttribute("id",n),r.setAttribute("d",t.toSVGPath()),r.setAttribute("vector-effect","non-scaling-stroke"),t.free){s.classList.add("free");let t=iG._svgFactory.createElement("mask");a.append(t),e=`mask_p${this.pageIndex}_${i}`,t.setAttribute("id",e),t.setAttribute("maskUnits","objectBoundingBox");let r=iG._svgFactory.createElement("rect");t.append(r),r.setAttribute("width","1"),r.setAttribute("height","1"),r.setAttribute("fill","white");let o=iG._svgFactory.createElement("use");t.append(o),o.setAttribute("href",`#${n}`),o.setAttribute("stroke","none"),o.setAttribute("fill","black"),o.setAttribute("fill-rule","nonzero"),o.classList.add("mask")}let o=iG._svgFactory.createElement("use");s.append(o),o.setAttribute("href",`#${n}`),e&&o.setAttribute("mask",`url(#${e})`);let l=o.cloneNode();return s.append(l),o.classList.add("mainOutline"),l.classList.add("secondaryOutline"),this.#rq.set(i,s),i}finalizeLine(t,e){let i=this.#rW.get(t);this.#rW.delete(t),this.updateBox(t,e.box),i.setAttribute("d",e.toSVGPath())}updateLine(t,e){this.#rq.get(t).firstChild.firstChild.setAttribute("d",e.toSVGPath())}removeFreeHighlight(t){this.remove(t),this.#rW.delete(t)}updatePath(t,e){this.#rW.get(t).setAttribute("d",e.toSVGPath())}updateBox(t,e){iG.#rK(this.#rq.get(t),e)}show(t,e){this.#rq.get(t).classList.toggle("hidden",!e)}rotate(t,e){this.#rq.get(t).setAttribute("data-main-rotation",e)}changeColor(t,e){this.#rq.get(t).setAttribute("fill",e)}changeOpacity(t,e){this.#rq.get(t).setAttribute("fill-opacity",e)}addClass(t,e){this.#rq.get(t).classList.add(e)}removeClass(t,e){this.#rq.get(t).classList.remove(e)}remove(t){if(null!==this.#iJ)this.#rq.get(t).remove(),this.#rq.delete(t)}destroy(){for(let t of(this.#iJ=null,this.#rq.values()))t.remove();this.#rq.clear()}}r.AbortException,r.AnnotationEditorLayer,r.AnnotationEditorParamsType,r.AnnotationEditorType,r.AnnotationEditorUIManager,r.AnnotationLayer,r.AnnotationMode,r.CMapCompressionType,r.ColorPicker,r.DOMSVGFactory,r.DrawLayer,r.FeatureTest;var iV=r.GlobalWorkerOptions;r.ImageKind,r.InvalidPDFException,r.MissingPDFException,r.OPS,r.Outliner,r.PDFDataRangeTransport,r.PDFDateString,r.PDFWorker,r.PasswordResponses,r.PermissionFlag,r.PixelsPerInch,r.RenderingCancelledException,r.TextLayer,r.UnexpectedResponseException,r.Util,r.VerbosityLevel,r.XfaLayer,r.build,r.createValidAbsoluteUrl,r.fetchData;var iq=r.getDocument;r.getFilenameFromUrl,r.getPdfFilenameFromUrl,r.getXfaPageViewport,r.isDataScheme,r.isPdfFile,r.noContextMenu,r.normalizeUnicode,r.renderTextLayer,r.setLayerDimensions,r.shadow,r.updateTextLayer,r.version}}]);
//# sourceMappingURL=7841.8f3290c8.js.map