{"version": 3, "file": "static/js/async/6840.13c9af22.js", "sources": ["webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/monaco-editor@0.45.0/node_modules/monaco-editor/esm/vs/language/html/htmlMode.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/language/html/workerManager.ts\nvar STOP_WHEN_IDLE_FOR = 2 * 60 * 1e3;\nvar WorkerManager = class {\n  _defaults;\n  _idleCheckInterval;\n  _lastUsedTime;\n  _configChangeListener;\n  _worker;\n  _client;\n  constructor(defaults) {\n    this._defaults = defaults;\n    this._worker = null;\n    this._client = null;\n    this._idleCheckInterval = window.setInterval(() => this._checkIfIdle(), 30 * 1e3);\n    this._lastUsedTime = 0;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  dispose() {\n    clearInterval(this._idleCheckInterval);\n    this._configChangeListener.dispose();\n    this._stopWorker();\n  }\n  _checkIfIdle() {\n    if (!this._worker) {\n      return;\n    }\n    let timePassedSinceLastUsed = Date.now() - this._lastUsedTime;\n    if (timePassedSinceLastUsed > STOP_WHEN_IDLE_FOR) {\n      this._stopWorker();\n    }\n  }\n  _getClient() {\n    this._lastUsedTime = Date.now();\n    if (!this._client) {\n      this._worker = monaco_editor_core_exports.editor.createWebWorker({\n        moduleId: \"vs/language/html/htmlWorker\",\n        createData: {\n          languageSettings: this._defaults.options,\n          languageId: this._defaults.languageId\n        },\n        label: this._defaults.languageId\n      });\n      this._client = this._worker.getProxy();\n    }\n    return this._client;\n  }\n  getLanguageServiceWorker(...resources) {\n    let _client;\n    return this._getClient().then((client) => {\n      _client = client;\n    }).then((_) => {\n      if (this._worker) {\n        return this._worker.withSyncedResources(resources);\n      }\n    }).then((_) => _client);\n  }\n};\n\n// node_modules/vscode-languageserver-types/lib/esm/main.js\nvar integer;\n(function(integer2) {\n  integer2.MIN_VALUE = -2147483648;\n  integer2.MAX_VALUE = 2147483647;\n})(integer || (integer = {}));\nvar uinteger;\n(function(uinteger2) {\n  uinteger2.MIN_VALUE = 0;\n  uinteger2.MAX_VALUE = 2147483647;\n})(uinteger || (uinteger = {}));\nvar Position;\n(function(Position3) {\n  function create(line, character) {\n    if (line === Number.MAX_VALUE) {\n      line = uinteger.MAX_VALUE;\n    }\n    if (character === Number.MAX_VALUE) {\n      character = uinteger.MAX_VALUE;\n    }\n    return { line, character };\n  }\n  Position3.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n  }\n  Position3.is = is;\n})(Position || (Position = {}));\nvar Range;\n(function(Range3) {\n  function create(one, two, three, four) {\n    if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n      return { start: Position.create(one, two), end: Position.create(three, four) };\n    } else if (Position.is(one) && Position.is(two)) {\n      return { start: one, end: two };\n    } else {\n      throw new Error(\"Range#create called with invalid arguments[\" + one + \", \" + two + \", \" + three + \", \" + four + \"]\");\n    }\n  }\n  Range3.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n  }\n  Range3.is = is;\n})(Range || (Range = {}));\nvar Location;\n(function(Location2) {\n  function create(uri, range) {\n    return { uri, range };\n  }\n  Location2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n  }\n  Location2.is = is;\n})(Location || (Location = {}));\nvar LocationLink;\n(function(LocationLink2) {\n  function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n    return { targetUri, targetRange, targetSelectionRange, originSelectionRange };\n  }\n  LocationLink2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && (Range.is(candidate.targetSelectionRange) || Is.undefined(candidate.targetSelectionRange)) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n  }\n  LocationLink2.is = is;\n})(LocationLink || (LocationLink = {}));\nvar Color;\n(function(Color2) {\n  function create(red, green, blue, alpha) {\n    return {\n      red,\n      green,\n      blue,\n      alpha\n    };\n  }\n  Color2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);\n  }\n  Color2.is = is;\n})(Color || (Color = {}));\nvar ColorInformation;\n(function(ColorInformation2) {\n  function create(range, color) {\n    return {\n      range,\n      color\n    };\n  }\n  ColorInformation2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Range.is(candidate.range) && Color.is(candidate.color);\n  }\n  ColorInformation2.is = is;\n})(ColorInformation || (ColorInformation = {}));\nvar ColorPresentation;\n(function(ColorPresentation2) {\n  function create(label, textEdit, additionalTextEdits) {\n    return {\n      label,\n      textEdit,\n      additionalTextEdits\n    };\n  }\n  ColorPresentation2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n  }\n  ColorPresentation2.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\nvar FoldingRangeKind;\n(function(FoldingRangeKind2) {\n  FoldingRangeKind2[\"Comment\"] = \"comment\";\n  FoldingRangeKind2[\"Imports\"] = \"imports\";\n  FoldingRangeKind2[\"Region\"] = \"region\";\n})(FoldingRangeKind || (FoldingRangeKind = {}));\nvar FoldingRange;\n(function(FoldingRange2) {\n  function create(startLine, endLine, startCharacter, endCharacter, kind) {\n    var result = {\n      startLine,\n      endLine\n    };\n    if (Is.defined(startCharacter)) {\n      result.startCharacter = startCharacter;\n    }\n    if (Is.defined(endCharacter)) {\n      result.endCharacter = endCharacter;\n    }\n    if (Is.defined(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  FoldingRange2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n  }\n  FoldingRange2.is = is;\n})(FoldingRange || (FoldingRange = {}));\nvar DiagnosticRelatedInformation;\n(function(DiagnosticRelatedInformation2) {\n  function create(location, message) {\n    return {\n      location,\n      message\n    };\n  }\n  DiagnosticRelatedInformation2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n  }\n  DiagnosticRelatedInformation2.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\nvar DiagnosticSeverity;\n(function(DiagnosticSeverity2) {\n  DiagnosticSeverity2.Error = 1;\n  DiagnosticSeverity2.Warning = 2;\n  DiagnosticSeverity2.Information = 3;\n  DiagnosticSeverity2.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\nvar DiagnosticTag;\n(function(DiagnosticTag2) {\n  DiagnosticTag2.Unnecessary = 1;\n  DiagnosticTag2.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\nvar CodeDescription;\n(function(CodeDescription2) {\n  function is(value) {\n    var candidate = value;\n    return candidate !== void 0 && candidate !== null && Is.string(candidate.href);\n  }\n  CodeDescription2.is = is;\n})(CodeDescription || (CodeDescription = {}));\nvar Diagnostic;\n(function(Diagnostic2) {\n  function create(range, message, severity, code, source, relatedInformation) {\n    var result = { range, message };\n    if (Is.defined(severity)) {\n      result.severity = severity;\n    }\n    if (Is.defined(code)) {\n      result.code = code;\n    }\n    if (Is.defined(source)) {\n      result.source = source;\n    }\n    if (Is.defined(relatedInformation)) {\n      result.relatedInformation = relatedInformation;\n    }\n    return result;\n  }\n  Diagnostic2.create = create;\n  function is(value) {\n    var _a;\n    var candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n  }\n  Diagnostic2.is = is;\n})(Diagnostic || (Diagnostic = {}));\nvar Command;\n(function(Command2) {\n  function create(title, command) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n      args[_i - 2] = arguments[_i];\n    }\n    var result = { title, command };\n    if (Is.defined(args) && args.length > 0) {\n      result.arguments = args;\n    }\n    return result;\n  }\n  Command2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n  }\n  Command2.is = is;\n})(Command || (Command = {}));\nvar TextEdit;\n(function(TextEdit2) {\n  function replace(range, newText) {\n    return { range, newText };\n  }\n  TextEdit2.replace = replace;\n  function insert(position, newText) {\n    return { range: { start: position, end: position }, newText };\n  }\n  TextEdit2.insert = insert;\n  function del(range) {\n    return { range, newText: \"\" };\n  }\n  TextEdit2.del = del;\n  function is(value) {\n    var candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);\n  }\n  TextEdit2.is = is;\n})(TextEdit || (TextEdit = {}));\nvar ChangeAnnotation;\n(function(ChangeAnnotation2) {\n  function create(label, needsConfirmation, description) {\n    var result = { label };\n    if (needsConfirmation !== void 0) {\n      result.needsConfirmation = needsConfirmation;\n    }\n    if (description !== void 0) {\n      result.description = description;\n    }\n    return result;\n  }\n  ChangeAnnotation2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate !== void 0 && Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  ChangeAnnotation2.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nvar ChangeAnnotationIdentifier;\n(function(ChangeAnnotationIdentifier2) {\n  function is(value) {\n    var candidate = value;\n    return typeof candidate === \"string\";\n  }\n  ChangeAnnotationIdentifier2.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nvar AnnotatedTextEdit;\n(function(AnnotatedTextEdit2) {\n  function replace(range, newText, annotation) {\n    return { range, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.replace = replace;\n  function insert(position, newText, annotation) {\n    return { range: { start: position, end: position }, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.insert = insert;\n  function del(range, annotation) {\n    return { range, newText: \"\", annotationId: annotation };\n  }\n  AnnotatedTextEdit2.del = del;\n  function is(value) {\n    var candidate = value;\n    return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  AnnotatedTextEdit2.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\nvar TextDocumentEdit;\n(function(TextDocumentEdit2) {\n  function create(textDocument, edits) {\n    return { textDocument, edits };\n  }\n  TextDocumentEdit2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);\n  }\n  TextDocumentEdit2.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nvar CreateFile;\n(function(CreateFile2) {\n  function create(uri, options, annotation) {\n    var result = {\n      kind: \"create\",\n      uri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  CreateFile2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate && candidate.kind === \"create\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  CreateFile2.is = is;\n})(CreateFile || (CreateFile = {}));\nvar RenameFile;\n(function(RenameFile2) {\n  function create(oldUri, newUri, options, annotation) {\n    var result = {\n      kind: \"rename\",\n      oldUri,\n      newUri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  RenameFile2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate && candidate.kind === \"rename\" && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  RenameFile2.is = is;\n})(RenameFile || (RenameFile = {}));\nvar DeleteFile;\n(function(DeleteFile2) {\n  function create(uri, options, annotation) {\n    var result = {\n      kind: \"delete\",\n      uri\n    };\n    if (options !== void 0 && (options.recursive !== void 0 || options.ignoreIfNotExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  DeleteFile2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate && candidate.kind === \"delete\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.recursive === void 0 || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === void 0 || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  DeleteFile2.is = is;\n})(DeleteFile || (DeleteFile = {}));\nvar WorkspaceEdit;\n(function(WorkspaceEdit2) {\n  function is(value) {\n    var candidate = value;\n    return candidate && (candidate.changes !== void 0 || candidate.documentChanges !== void 0) && (candidate.documentChanges === void 0 || candidate.documentChanges.every(function(change) {\n      if (Is.string(change.kind)) {\n        return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n      } else {\n        return TextDocumentEdit.is(change);\n      }\n    }));\n  }\n  WorkspaceEdit2.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextEditChangeImpl = function() {\n  function TextEditChangeImpl2(edits, changeAnnotations) {\n    this.edits = edits;\n    this.changeAnnotations = changeAnnotations;\n  }\n  TextEditChangeImpl2.prototype.insert = function(position, newText, annotation) {\n    var edit;\n    var id;\n    if (annotation === void 0) {\n      edit = TextEdit.insert(position, newText);\n    } else if (ChangeAnnotationIdentifier.is(annotation)) {\n      id = annotation;\n      edit = AnnotatedTextEdit.insert(position, newText, annotation);\n    } else {\n      this.assertChangeAnnotations(this.changeAnnotations);\n      id = this.changeAnnotations.manage(annotation);\n      edit = AnnotatedTextEdit.insert(position, newText, id);\n    }\n    this.edits.push(edit);\n    if (id !== void 0) {\n      return id;\n    }\n  };\n  TextEditChangeImpl2.prototype.replace = function(range, newText, annotation) {\n    var edit;\n    var id;\n    if (annotation === void 0) {\n      edit = TextEdit.replace(range, newText);\n    } else if (ChangeAnnotationIdentifier.is(annotation)) {\n      id = annotation;\n      edit = AnnotatedTextEdit.replace(range, newText, annotation);\n    } else {\n      this.assertChangeAnnotations(this.changeAnnotations);\n      id = this.changeAnnotations.manage(annotation);\n      edit = AnnotatedTextEdit.replace(range, newText, id);\n    }\n    this.edits.push(edit);\n    if (id !== void 0) {\n      return id;\n    }\n  };\n  TextEditChangeImpl2.prototype.delete = function(range, annotation) {\n    var edit;\n    var id;\n    if (annotation === void 0) {\n      edit = TextEdit.del(range);\n    } else if (ChangeAnnotationIdentifier.is(annotation)) {\n      id = annotation;\n      edit = AnnotatedTextEdit.del(range, annotation);\n    } else {\n      this.assertChangeAnnotations(this.changeAnnotations);\n      id = this.changeAnnotations.manage(annotation);\n      edit = AnnotatedTextEdit.del(range, id);\n    }\n    this.edits.push(edit);\n    if (id !== void 0) {\n      return id;\n    }\n  };\n  TextEditChangeImpl2.prototype.add = function(edit) {\n    this.edits.push(edit);\n  };\n  TextEditChangeImpl2.prototype.all = function() {\n    return this.edits;\n  };\n  TextEditChangeImpl2.prototype.clear = function() {\n    this.edits.splice(0, this.edits.length);\n  };\n  TextEditChangeImpl2.prototype.assertChangeAnnotations = function(value) {\n    if (value === void 0) {\n      throw new Error(\"Text edit change is not configured to manage change annotations.\");\n    }\n  };\n  return TextEditChangeImpl2;\n}();\nvar ChangeAnnotations = function() {\n  function ChangeAnnotations2(annotations) {\n    this._annotations = annotations === void 0 ? /* @__PURE__ */ Object.create(null) : annotations;\n    this._counter = 0;\n    this._size = 0;\n  }\n  ChangeAnnotations2.prototype.all = function() {\n    return this._annotations;\n  };\n  Object.defineProperty(ChangeAnnotations2.prototype, \"size\", {\n    get: function() {\n      return this._size;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  ChangeAnnotations2.prototype.manage = function(idOrAnnotation, annotation) {\n    var id;\n    if (ChangeAnnotationIdentifier.is(idOrAnnotation)) {\n      id = idOrAnnotation;\n    } else {\n      id = this.nextId();\n      annotation = idOrAnnotation;\n    }\n    if (this._annotations[id] !== void 0) {\n      throw new Error(\"Id \" + id + \" is already in use.\");\n    }\n    if (annotation === void 0) {\n      throw new Error(\"No annotation provided for id \" + id);\n    }\n    this._annotations[id] = annotation;\n    this._size++;\n    return id;\n  };\n  ChangeAnnotations2.prototype.nextId = function() {\n    this._counter++;\n    return this._counter.toString();\n  };\n  return ChangeAnnotations2;\n}();\nvar WorkspaceChange = function() {\n  function WorkspaceChange2(workspaceEdit) {\n    var _this = this;\n    this._textEditChanges = /* @__PURE__ */ Object.create(null);\n    if (workspaceEdit !== void 0) {\n      this._workspaceEdit = workspaceEdit;\n      if (workspaceEdit.documentChanges) {\n        this._changeAnnotations = new ChangeAnnotations(workspaceEdit.changeAnnotations);\n        workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n        workspaceEdit.documentChanges.forEach(function(change) {\n          if (TextDocumentEdit.is(change)) {\n            var textEditChange = new TextEditChangeImpl(change.edits, _this._changeAnnotations);\n            _this._textEditChanges[change.textDocument.uri] = textEditChange;\n          }\n        });\n      } else if (workspaceEdit.changes) {\n        Object.keys(workspaceEdit.changes).forEach(function(key) {\n          var textEditChange = new TextEditChangeImpl(workspaceEdit.changes[key]);\n          _this._textEditChanges[key] = textEditChange;\n        });\n      }\n    } else {\n      this._workspaceEdit = {};\n    }\n  }\n  Object.defineProperty(WorkspaceChange2.prototype, \"edit\", {\n    get: function() {\n      this.initDocumentChanges();\n      if (this._changeAnnotations !== void 0) {\n        if (this._changeAnnotations.size === 0) {\n          this._workspaceEdit.changeAnnotations = void 0;\n        } else {\n          this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n        }\n      }\n      return this._workspaceEdit;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  WorkspaceChange2.prototype.getTextEditChange = function(key) {\n    if (OptionalVersionedTextDocumentIdentifier.is(key)) {\n      this.initDocumentChanges();\n      if (this._workspaceEdit.documentChanges === void 0) {\n        throw new Error(\"Workspace edit is not configured for document changes.\");\n      }\n      var textDocument = { uri: key.uri, version: key.version };\n      var result = this._textEditChanges[textDocument.uri];\n      if (!result) {\n        var edits = [];\n        var textDocumentEdit = {\n          textDocument,\n          edits\n        };\n        this._workspaceEdit.documentChanges.push(textDocumentEdit);\n        result = new TextEditChangeImpl(edits, this._changeAnnotations);\n        this._textEditChanges[textDocument.uri] = result;\n      }\n      return result;\n    } else {\n      this.initChanges();\n      if (this._workspaceEdit.changes === void 0) {\n        throw new Error(\"Workspace edit is not configured for normal text edit changes.\");\n      }\n      var result = this._textEditChanges[key];\n      if (!result) {\n        var edits = [];\n        this._workspaceEdit.changes[key] = edits;\n        result = new TextEditChangeImpl(edits);\n        this._textEditChanges[key] = result;\n      }\n      return result;\n    }\n  };\n  WorkspaceChange2.prototype.initDocumentChanges = function() {\n    if (this._workspaceEdit.documentChanges === void 0 && this._workspaceEdit.changes === void 0) {\n      this._changeAnnotations = new ChangeAnnotations();\n      this._workspaceEdit.documentChanges = [];\n      this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n    }\n  };\n  WorkspaceChange2.prototype.initChanges = function() {\n    if (this._workspaceEdit.documentChanges === void 0 && this._workspaceEdit.changes === void 0) {\n      this._workspaceEdit.changes = /* @__PURE__ */ Object.create(null);\n    }\n  };\n  WorkspaceChange2.prototype.createFile = function(uri, optionsOrAnnotation, options) {\n    this.initDocumentChanges();\n    if (this._workspaceEdit.documentChanges === void 0) {\n      throw new Error(\"Workspace edit is not configured for document changes.\");\n    }\n    var annotation;\n    if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n      annotation = optionsOrAnnotation;\n    } else {\n      options = optionsOrAnnotation;\n    }\n    var operation;\n    var id;\n    if (annotation === void 0) {\n      operation = CreateFile.create(uri, options);\n    } else {\n      id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n      operation = CreateFile.create(uri, options, id);\n    }\n    this._workspaceEdit.documentChanges.push(operation);\n    if (id !== void 0) {\n      return id;\n    }\n  };\n  WorkspaceChange2.prototype.renameFile = function(oldUri, newUri, optionsOrAnnotation, options) {\n    this.initDocumentChanges();\n    if (this._workspaceEdit.documentChanges === void 0) {\n      throw new Error(\"Workspace edit is not configured for document changes.\");\n    }\n    var annotation;\n    if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n      annotation = optionsOrAnnotation;\n    } else {\n      options = optionsOrAnnotation;\n    }\n    var operation;\n    var id;\n    if (annotation === void 0) {\n      operation = RenameFile.create(oldUri, newUri, options);\n    } else {\n      id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n      operation = RenameFile.create(oldUri, newUri, options, id);\n    }\n    this._workspaceEdit.documentChanges.push(operation);\n    if (id !== void 0) {\n      return id;\n    }\n  };\n  WorkspaceChange2.prototype.deleteFile = function(uri, optionsOrAnnotation, options) {\n    this.initDocumentChanges();\n    if (this._workspaceEdit.documentChanges === void 0) {\n      throw new Error(\"Workspace edit is not configured for document changes.\");\n    }\n    var annotation;\n    if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n      annotation = optionsOrAnnotation;\n    } else {\n      options = optionsOrAnnotation;\n    }\n    var operation;\n    var id;\n    if (annotation === void 0) {\n      operation = DeleteFile.create(uri, options);\n    } else {\n      id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n      operation = DeleteFile.create(uri, options, id);\n    }\n    this._workspaceEdit.documentChanges.push(operation);\n    if (id !== void 0) {\n      return id;\n    }\n  };\n  return WorkspaceChange2;\n}();\nvar TextDocumentIdentifier;\n(function(TextDocumentIdentifier2) {\n  function create(uri) {\n    return { uri };\n  }\n  TextDocumentIdentifier2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri);\n  }\n  TextDocumentIdentifier2.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\nvar VersionedTextDocumentIdentifier;\n(function(VersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  VersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n  }\n  VersionedTextDocumentIdentifier2.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\nvar OptionalVersionedTextDocumentIdentifier;\n(function(OptionalVersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  OptionalVersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n  }\n  OptionalVersionedTextDocumentIdentifier2.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\nvar TextDocumentItem;\n(function(TextDocumentItem2) {\n  function create(uri, languageId, version, text) {\n    return { uri, languageId, version, text };\n  }\n  TextDocumentItem2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n  }\n  TextDocumentItem2.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\nvar MarkupKind;\n(function(MarkupKind2) {\n  MarkupKind2.PlainText = \"plaintext\";\n  MarkupKind2.Markdown = \"markdown\";\n})(MarkupKind || (MarkupKind = {}));\n(function(MarkupKind2) {\n  function is(value) {\n    var candidate = value;\n    return candidate === MarkupKind2.PlainText || candidate === MarkupKind2.Markdown;\n  }\n  MarkupKind2.is = is;\n})(MarkupKind || (MarkupKind = {}));\nvar MarkupContent;\n(function(MarkupContent2) {\n  function is(value) {\n    var candidate = value;\n    return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n  }\n  MarkupContent2.is = is;\n})(MarkupContent || (MarkupContent = {}));\nvar CompletionItemKind;\n(function(CompletionItemKind2) {\n  CompletionItemKind2.Text = 1;\n  CompletionItemKind2.Method = 2;\n  CompletionItemKind2.Function = 3;\n  CompletionItemKind2.Constructor = 4;\n  CompletionItemKind2.Field = 5;\n  CompletionItemKind2.Variable = 6;\n  CompletionItemKind2.Class = 7;\n  CompletionItemKind2.Interface = 8;\n  CompletionItemKind2.Module = 9;\n  CompletionItemKind2.Property = 10;\n  CompletionItemKind2.Unit = 11;\n  CompletionItemKind2.Value = 12;\n  CompletionItemKind2.Enum = 13;\n  CompletionItemKind2.Keyword = 14;\n  CompletionItemKind2.Snippet = 15;\n  CompletionItemKind2.Color = 16;\n  CompletionItemKind2.File = 17;\n  CompletionItemKind2.Reference = 18;\n  CompletionItemKind2.Folder = 19;\n  CompletionItemKind2.EnumMember = 20;\n  CompletionItemKind2.Constant = 21;\n  CompletionItemKind2.Struct = 22;\n  CompletionItemKind2.Event = 23;\n  CompletionItemKind2.Operator = 24;\n  CompletionItemKind2.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\nvar InsertTextFormat;\n(function(InsertTextFormat2) {\n  InsertTextFormat2.PlainText = 1;\n  InsertTextFormat2.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\nvar CompletionItemTag;\n(function(CompletionItemTag2) {\n  CompletionItemTag2.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\nvar InsertReplaceEdit;\n(function(InsertReplaceEdit2) {\n  function create(newText, insert, replace) {\n    return { newText, insert, replace };\n  }\n  InsertReplaceEdit2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n  }\n  InsertReplaceEdit2.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\nvar InsertTextMode;\n(function(InsertTextMode2) {\n  InsertTextMode2.asIs = 1;\n  InsertTextMode2.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nvar CompletionItem;\n(function(CompletionItem2) {\n  function create(label) {\n    return { label };\n  }\n  CompletionItem2.create = create;\n})(CompletionItem || (CompletionItem = {}));\nvar CompletionList;\n(function(CompletionList2) {\n  function create(items, isIncomplete) {\n    return { items: items ? items : [], isIncomplete: !!isIncomplete };\n  }\n  CompletionList2.create = create;\n})(CompletionList || (CompletionList = {}));\nvar MarkedString;\n(function(MarkedString2) {\n  function fromPlainText(plainText) {\n    return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\");\n  }\n  MarkedString2.fromPlainText = fromPlainText;\n  function is(value) {\n    var candidate = value;\n    return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);\n  }\n  MarkedString2.is = is;\n})(MarkedString || (MarkedString = {}));\nvar Hover;\n(function(Hover2) {\n  function is(value) {\n    var candidate = value;\n    return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === void 0 || Range.is(value.range));\n  }\n  Hover2.is = is;\n})(Hover || (Hover = {}));\nvar ParameterInformation;\n(function(ParameterInformation2) {\n  function create(label, documentation) {\n    return documentation ? { label, documentation } : { label };\n  }\n  ParameterInformation2.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\nvar SignatureInformation;\n(function(SignatureInformation2) {\n  function create(label, documentation) {\n    var parameters = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n      parameters[_i - 2] = arguments[_i];\n    }\n    var result = { label };\n    if (Is.defined(documentation)) {\n      result.documentation = documentation;\n    }\n    if (Is.defined(parameters)) {\n      result.parameters = parameters;\n    } else {\n      result.parameters = [];\n    }\n    return result;\n  }\n  SignatureInformation2.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\nvar DocumentHighlightKind;\n(function(DocumentHighlightKind2) {\n  DocumentHighlightKind2.Text = 1;\n  DocumentHighlightKind2.Read = 2;\n  DocumentHighlightKind2.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\nvar DocumentHighlight;\n(function(DocumentHighlight2) {\n  function create(range, kind) {\n    var result = { range };\n    if (Is.number(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  DocumentHighlight2.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\nvar SymbolKind;\n(function(SymbolKind2) {\n  SymbolKind2.File = 1;\n  SymbolKind2.Module = 2;\n  SymbolKind2.Namespace = 3;\n  SymbolKind2.Package = 4;\n  SymbolKind2.Class = 5;\n  SymbolKind2.Method = 6;\n  SymbolKind2.Property = 7;\n  SymbolKind2.Field = 8;\n  SymbolKind2.Constructor = 9;\n  SymbolKind2.Enum = 10;\n  SymbolKind2.Interface = 11;\n  SymbolKind2.Function = 12;\n  SymbolKind2.Variable = 13;\n  SymbolKind2.Constant = 14;\n  SymbolKind2.String = 15;\n  SymbolKind2.Number = 16;\n  SymbolKind2.Boolean = 17;\n  SymbolKind2.Array = 18;\n  SymbolKind2.Object = 19;\n  SymbolKind2.Key = 20;\n  SymbolKind2.Null = 21;\n  SymbolKind2.EnumMember = 22;\n  SymbolKind2.Struct = 23;\n  SymbolKind2.Event = 24;\n  SymbolKind2.Operator = 25;\n  SymbolKind2.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\nvar SymbolTag;\n(function(SymbolTag2) {\n  SymbolTag2.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nvar SymbolInformation;\n(function(SymbolInformation2) {\n  function create(name, kind, range, uri, containerName) {\n    var result = {\n      name,\n      kind,\n      location: { uri, range }\n    };\n    if (containerName) {\n      result.containerName = containerName;\n    }\n    return result;\n  }\n  SymbolInformation2.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nvar DocumentSymbol;\n(function(DocumentSymbol2) {\n  function create(name, detail, kind, range, selectionRange, children) {\n    var result = {\n      name,\n      detail,\n      kind,\n      range,\n      selectionRange\n    };\n    if (children !== void 0) {\n      result.children = children;\n    }\n    return result;\n  }\n  DocumentSymbol2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === void 0 || Is.string(candidate.detail)) && (candidate.deprecated === void 0 || Is.boolean(candidate.deprecated)) && (candidate.children === void 0 || Array.isArray(candidate.children)) && (candidate.tags === void 0 || Array.isArray(candidate.tags));\n  }\n  DocumentSymbol2.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\nvar CodeActionKind;\n(function(CodeActionKind2) {\n  CodeActionKind2.Empty = \"\";\n  CodeActionKind2.QuickFix = \"quickfix\";\n  CodeActionKind2.Refactor = \"refactor\";\n  CodeActionKind2.RefactorExtract = \"refactor.extract\";\n  CodeActionKind2.RefactorInline = \"refactor.inline\";\n  CodeActionKind2.RefactorRewrite = \"refactor.rewrite\";\n  CodeActionKind2.Source = \"source\";\n  CodeActionKind2.SourceOrganizeImports = \"source.organizeImports\";\n  CodeActionKind2.SourceFixAll = \"source.fixAll\";\n})(CodeActionKind || (CodeActionKind = {}));\nvar CodeActionContext;\n(function(CodeActionContext2) {\n  function create(diagnostics, only) {\n    var result = { diagnostics };\n    if (only !== void 0 && only !== null) {\n      result.only = only;\n    }\n    return result;\n  }\n  CodeActionContext2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === void 0 || Is.typedArray(candidate.only, Is.string));\n  }\n  CodeActionContext2.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nvar CodeAction;\n(function(CodeAction2) {\n  function create(title, kindOrCommandOrEdit, kind) {\n    var result = { title };\n    var checkKind = true;\n    if (typeof kindOrCommandOrEdit === \"string\") {\n      checkKind = false;\n      result.kind = kindOrCommandOrEdit;\n    } else if (Command.is(kindOrCommandOrEdit)) {\n      result.command = kindOrCommandOrEdit;\n    } else {\n      result.edit = kindOrCommandOrEdit;\n    }\n    if (checkKind && kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  CodeAction2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate && Is.string(candidate.title) && (candidate.diagnostics === void 0 || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === void 0 || Is.string(candidate.kind)) && (candidate.edit !== void 0 || candidate.command !== void 0) && (candidate.command === void 0 || Command.is(candidate.command)) && (candidate.isPreferred === void 0 || Is.boolean(candidate.isPreferred)) && (candidate.edit === void 0 || WorkspaceEdit.is(candidate.edit));\n  }\n  CodeAction2.is = is;\n})(CodeAction || (CodeAction = {}));\nvar CodeLens;\n(function(CodeLens2) {\n  function create(range, data) {\n    var result = { range };\n    if (Is.defined(data)) {\n      result.data = data;\n    }\n    return result;\n  }\n  CodeLens2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n  }\n  CodeLens2.is = is;\n})(CodeLens || (CodeLens = {}));\nvar FormattingOptions;\n(function(FormattingOptions2) {\n  function create(tabSize, insertSpaces) {\n    return { tabSize, insertSpaces };\n  }\n  FormattingOptions2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n  }\n  FormattingOptions2.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\nvar DocumentLink;\n(function(DocumentLink2) {\n  function create(range, target, data) {\n    return { range, target, data };\n  }\n  DocumentLink2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n  }\n  DocumentLink2.is = is;\n})(DocumentLink || (DocumentLink = {}));\nvar SelectionRange;\n(function(SelectionRange2) {\n  function create(range, parent) {\n    return { range, parent };\n  }\n  SelectionRange2.create = create;\n  function is(value) {\n    var candidate = value;\n    return candidate !== void 0 && Range.is(candidate.range) && (candidate.parent === void 0 || SelectionRange2.is(candidate.parent));\n  }\n  SelectionRange2.is = is;\n})(SelectionRange || (SelectionRange = {}));\nvar TextDocument;\n(function(TextDocument2) {\n  function create(uri, languageId, version, content) {\n    return new FullTextDocument(uri, languageId, version, content);\n  }\n  TextDocument2.create = create;\n  function is(value) {\n    var candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n  }\n  TextDocument2.is = is;\n  function applyEdits(document, edits) {\n    var text = document.getText();\n    var sortedEdits = mergeSort(edits, function(a, b) {\n      var diff = a.range.start.line - b.range.start.line;\n      if (diff === 0) {\n        return a.range.start.character - b.range.start.character;\n      }\n      return diff;\n    });\n    var lastModifiedOffset = text.length;\n    for (var i = sortedEdits.length - 1; i >= 0; i--) {\n      var e = sortedEdits[i];\n      var startOffset = document.offsetAt(e.range.start);\n      var endOffset = document.offsetAt(e.range.end);\n      if (endOffset <= lastModifiedOffset) {\n        text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n      } else {\n        throw new Error(\"Overlapping edit\");\n      }\n      lastModifiedOffset = startOffset;\n    }\n    return text;\n  }\n  TextDocument2.applyEdits = applyEdits;\n  function mergeSort(data, compare) {\n    if (data.length <= 1) {\n      return data;\n    }\n    var p = data.length / 2 | 0;\n    var left = data.slice(0, p);\n    var right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    var leftIdx = 0;\n    var rightIdx = 0;\n    var i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n      var ret = compare(left[leftIdx], right[rightIdx]);\n      if (ret <= 0) {\n        data[i++] = left[leftIdx++];\n      } else {\n        data[i++] = right[rightIdx++];\n      }\n    }\n    while (leftIdx < left.length) {\n      data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n      data[i++] = right[rightIdx++];\n    }\n    return data;\n  }\n})(TextDocument || (TextDocument = {}));\nvar FullTextDocument = function() {\n  function FullTextDocument2(uri, languageId, version, content) {\n    this._uri = uri;\n    this._languageId = languageId;\n    this._version = version;\n    this._content = content;\n    this._lineOffsets = void 0;\n  }\n  Object.defineProperty(FullTextDocument2.prototype, \"uri\", {\n    get: function() {\n      return this._uri;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(FullTextDocument2.prototype, \"languageId\", {\n    get: function() {\n      return this._languageId;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(FullTextDocument2.prototype, \"version\", {\n    get: function() {\n      return this._version;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  FullTextDocument2.prototype.getText = function(range) {\n    if (range) {\n      var start = this.offsetAt(range.start);\n      var end = this.offsetAt(range.end);\n      return this._content.substring(start, end);\n    }\n    return this._content;\n  };\n  FullTextDocument2.prototype.update = function(event, version) {\n    this._content = event.text;\n    this._version = version;\n    this._lineOffsets = void 0;\n  };\n  FullTextDocument2.prototype.getLineOffsets = function() {\n    if (this._lineOffsets === void 0) {\n      var lineOffsets = [];\n      var text = this._content;\n      var isLineStart = true;\n      for (var i = 0; i < text.length; i++) {\n        if (isLineStart) {\n          lineOffsets.push(i);\n          isLineStart = false;\n        }\n        var ch = text.charAt(i);\n        isLineStart = ch === \"\\r\" || ch === \"\\n\";\n        if (ch === \"\\r\" && i + 1 < text.length && text.charAt(i + 1) === \"\\n\") {\n          i++;\n        }\n      }\n      if (isLineStart && text.length > 0) {\n        lineOffsets.push(text.length);\n      }\n      this._lineOffsets = lineOffsets;\n    }\n    return this._lineOffsets;\n  };\n  FullTextDocument2.prototype.positionAt = function(offset) {\n    offset = Math.max(Math.min(offset, this._content.length), 0);\n    var lineOffsets = this.getLineOffsets();\n    var low = 0, high = lineOffsets.length;\n    if (high === 0) {\n      return Position.create(0, offset);\n    }\n    while (low < high) {\n      var mid = Math.floor((low + high) / 2);\n      if (lineOffsets[mid] > offset) {\n        high = mid;\n      } else {\n        low = mid + 1;\n      }\n    }\n    var line = low - 1;\n    return Position.create(line, offset - lineOffsets[line]);\n  };\n  FullTextDocument2.prototype.offsetAt = function(position) {\n    var lineOffsets = this.getLineOffsets();\n    if (position.line >= lineOffsets.length) {\n      return this._content.length;\n    } else if (position.line < 0) {\n      return 0;\n    }\n    var lineOffset = lineOffsets[position.line];\n    var nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;\n    return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n  };\n  Object.defineProperty(FullTextDocument2.prototype, \"lineCount\", {\n    get: function() {\n      return this.getLineOffsets().length;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  return FullTextDocument2;\n}();\nvar Is;\n(function(Is2) {\n  var toString = Object.prototype.toString;\n  function defined(value) {\n    return typeof value !== \"undefined\";\n  }\n  Is2.defined = defined;\n  function undefined2(value) {\n    return typeof value === \"undefined\";\n  }\n  Is2.undefined = undefined2;\n  function boolean(value) {\n    return value === true || value === false;\n  }\n  Is2.boolean = boolean;\n  function string(value) {\n    return toString.call(value) === \"[object String]\";\n  }\n  Is2.string = string;\n  function number(value) {\n    return toString.call(value) === \"[object Number]\";\n  }\n  Is2.number = number;\n  function numberRange(value, min, max) {\n    return toString.call(value) === \"[object Number]\" && min <= value && value <= max;\n  }\n  Is2.numberRange = numberRange;\n  function integer2(value) {\n    return toString.call(value) === \"[object Number]\" && -2147483648 <= value && value <= 2147483647;\n  }\n  Is2.integer = integer2;\n  function uinteger2(value) {\n    return toString.call(value) === \"[object Number]\" && 0 <= value && value <= 2147483647;\n  }\n  Is2.uinteger = uinteger2;\n  function func(value) {\n    return toString.call(value) === \"[object Function]\";\n  }\n  Is2.func = func;\n  function objectLiteral(value) {\n    return value !== null && typeof value === \"object\";\n  }\n  Is2.objectLiteral = objectLiteral;\n  function typedArray(value, check) {\n    return Array.isArray(value) && value.every(check);\n  }\n  Is2.typedArray = typedArray;\n})(Is || (Is = {}));\n\n// src/language/common/lspLanguageFeatures.ts\nvar DiagnosticsAdapter = class {\n  constructor(_languageId, _worker, configChangeEvent) {\n    this._languageId = _languageId;\n    this._worker = _worker;\n    const onModelAdd = (model) => {\n      let modeId = model.getLanguageId();\n      if (modeId !== this._languageId) {\n        return;\n      }\n      let handle;\n      this._listener[model.uri.toString()] = model.onDidChangeContent(() => {\n        window.clearTimeout(handle);\n        handle = window.setTimeout(() => this._doValidate(model.uri, modeId), 500);\n      });\n      this._doValidate(model.uri, modeId);\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._languageId, []);\n      let uriStr = model.uri.toString();\n      let listener = this._listener[uriStr];\n      if (listener) {\n        listener.dispose();\n        delete this._listener[uriStr];\n      }\n    };\n    this._disposables.push(monaco_editor_core_exports.editor.onDidCreateModel(onModelAdd));\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n      onModelRemoved(event.model);\n      onModelAdd(event.model);\n    }));\n    this._disposables.push(configChangeEvent((_) => {\n      monaco_editor_core_exports.editor.getModels().forEach((model) => {\n        if (model.getLanguageId() === this._languageId) {\n          onModelRemoved(model);\n          onModelAdd(model);\n        }\n      });\n    }));\n    this._disposables.push({\n      dispose: () => {\n        monaco_editor_core_exports.editor.getModels().forEach(onModelRemoved);\n        for (let key in this._listener) {\n          this._listener[key].dispose();\n        }\n      }\n    });\n    monaco_editor_core_exports.editor.getModels().forEach(onModelAdd);\n  }\n  _disposables = [];\n  _listener = /* @__PURE__ */ Object.create(null);\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables.length = 0;\n  }\n  _doValidate(resource, languageId) {\n    this._worker(resource).then((worker) => {\n      return worker.doValidation(resource.toString());\n    }).then((diagnostics) => {\n      const markers = diagnostics.map((d) => toDiagnostics(resource, d));\n      let model = monaco_editor_core_exports.editor.getModel(resource);\n      if (model && model.getLanguageId() === languageId) {\n        monaco_editor_core_exports.editor.setModelMarkers(model, languageId, markers);\n      }\n    }).then(void 0, (err) => {\n      console.error(err);\n    });\n  }\n};\nfunction toSeverity(lsSeverity) {\n  switch (lsSeverity) {\n    case DiagnosticSeverity.Error:\n      return monaco_editor_core_exports.MarkerSeverity.Error;\n    case DiagnosticSeverity.Warning:\n      return monaco_editor_core_exports.MarkerSeverity.Warning;\n    case DiagnosticSeverity.Information:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n    case DiagnosticSeverity.Hint:\n      return monaco_editor_core_exports.MarkerSeverity.Hint;\n    default:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n}\nfunction toDiagnostics(resource, diag) {\n  let code = typeof diag.code === \"number\" ? String(diag.code) : diag.code;\n  return {\n    severity: toSeverity(diag.severity),\n    startLineNumber: diag.range.start.line + 1,\n    startColumn: diag.range.start.character + 1,\n    endLineNumber: diag.range.end.line + 1,\n    endColumn: diag.range.end.character + 1,\n    message: diag.message,\n    code,\n    source: diag.source\n  };\n}\nvar CompletionAdapter = class {\n  constructor(_worker, _triggerCharacters) {\n    this._worker = _worker;\n    this._triggerCharacters = _triggerCharacters;\n  }\n  get triggerCharacters() {\n    return this._triggerCharacters;\n  }\n  provideCompletionItems(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doComplete(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      const wordInfo = model.getWordUntilPosition(position);\n      const wordRange = new monaco_editor_core_exports.Range(position.lineNumber, wordInfo.startColumn, position.lineNumber, wordInfo.endColumn);\n      const items = info.items.map((entry) => {\n        const item = {\n          label: entry.label,\n          insertText: entry.insertText || entry.label,\n          sortText: entry.sortText,\n          filterText: entry.filterText,\n          documentation: entry.documentation,\n          detail: entry.detail,\n          command: toCommand(entry.command),\n          range: wordRange,\n          kind: toCompletionItemKind(entry.kind)\n        };\n        if (entry.textEdit) {\n          if (isInsertReplaceEdit(entry.textEdit)) {\n            item.range = {\n              insert: toRange(entry.textEdit.insert),\n              replace: toRange(entry.textEdit.replace)\n            };\n          } else {\n            item.range = toRange(entry.textEdit.range);\n          }\n          item.insertText = entry.textEdit.newText;\n        }\n        if (entry.additionalTextEdits) {\n          item.additionalTextEdits = entry.additionalTextEdits.map(toTextEdit);\n        }\n        if (entry.insertTextFormat === InsertTextFormat.Snippet) {\n          item.insertTextRules = monaco_editor_core_exports.languages.CompletionItemInsertTextRule.InsertAsSnippet;\n        }\n        return item;\n      });\n      return {\n        isIncomplete: info.isIncomplete,\n        suggestions: items\n      };\n    });\n  }\n};\nfunction fromPosition(position) {\n  if (!position) {\n    return void 0;\n  }\n  return { character: position.column - 1, line: position.lineNumber - 1 };\n}\nfunction fromRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return {\n    start: {\n      line: range.startLineNumber - 1,\n      character: range.startColumn - 1\n    },\n    end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n  };\n}\nfunction toRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return new monaco_editor_core_exports.Range(range.start.line + 1, range.start.character + 1, range.end.line + 1, range.end.character + 1);\n}\nfunction isInsertReplaceEdit(edit) {\n  return typeof edit.insert !== \"undefined\" && typeof edit.replace !== \"undefined\";\n}\nfunction toCompletionItemKind(kind) {\n  const mItemKind = monaco_editor_core_exports.languages.CompletionItemKind;\n  switch (kind) {\n    case CompletionItemKind.Text:\n      return mItemKind.Text;\n    case CompletionItemKind.Method:\n      return mItemKind.Method;\n    case CompletionItemKind.Function:\n      return mItemKind.Function;\n    case CompletionItemKind.Constructor:\n      return mItemKind.Constructor;\n    case CompletionItemKind.Field:\n      return mItemKind.Field;\n    case CompletionItemKind.Variable:\n      return mItemKind.Variable;\n    case CompletionItemKind.Class:\n      return mItemKind.Class;\n    case CompletionItemKind.Interface:\n      return mItemKind.Interface;\n    case CompletionItemKind.Module:\n      return mItemKind.Module;\n    case CompletionItemKind.Property:\n      return mItemKind.Property;\n    case CompletionItemKind.Unit:\n      return mItemKind.Unit;\n    case CompletionItemKind.Value:\n      return mItemKind.Value;\n    case CompletionItemKind.Enum:\n      return mItemKind.Enum;\n    case CompletionItemKind.Keyword:\n      return mItemKind.Keyword;\n    case CompletionItemKind.Snippet:\n      return mItemKind.Snippet;\n    case CompletionItemKind.Color:\n      return mItemKind.Color;\n    case CompletionItemKind.File:\n      return mItemKind.File;\n    case CompletionItemKind.Reference:\n      return mItemKind.Reference;\n  }\n  return mItemKind.Property;\n}\nfunction toTextEdit(textEdit) {\n  if (!textEdit) {\n    return void 0;\n  }\n  return {\n    range: toRange(textEdit.range),\n    text: textEdit.newText\n  };\n}\nfunction toCommand(c) {\n  return c && c.command === \"editor.action.triggerSuggest\" ? { id: c.command, title: c.title, arguments: c.arguments } : void 0;\n}\nvar HoverAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideHover(model, position, token) {\n    let resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doHover(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      return {\n        range: toRange(info.range),\n        contents: toMarkedStringArray(info.contents)\n      };\n    });\n  }\n};\nfunction isMarkupContent(thing) {\n  return thing && typeof thing === \"object\" && typeof thing.kind === \"string\";\n}\nfunction toMarkdownString(entry) {\n  if (typeof entry === \"string\") {\n    return {\n      value: entry\n    };\n  }\n  if (isMarkupContent(entry)) {\n    if (entry.kind === \"plaintext\") {\n      return {\n        value: entry.value.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\")\n      };\n    }\n    return {\n      value: entry.value\n    };\n  }\n  return { value: \"```\" + entry.language + \"\\n\" + entry.value + \"\\n```\\n\" };\n}\nfunction toMarkedStringArray(contents) {\n  if (!contents) {\n    return void 0;\n  }\n  if (Array.isArray(contents)) {\n    return contents.map(toMarkdownString);\n  }\n  return [toMarkdownString(contents)];\n}\nvar DocumentHighlightAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentHighlights(resource.toString(), fromPosition(position))).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map((entry) => {\n        return {\n          range: toRange(entry.range),\n          kind: toDocumentHighlightKind(entry.kind)\n        };\n      });\n    });\n  }\n};\nfunction toDocumentHighlightKind(kind) {\n  switch (kind) {\n    case DocumentHighlightKind.Read:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Read;\n    case DocumentHighlightKind.Write:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Write;\n    case DocumentHighlightKind.Text:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n  }\n  return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n}\nvar DefinitionAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDefinition(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.findDefinition(resource.toString(), fromPosition(position));\n    }).then((definition) => {\n      if (!definition) {\n        return;\n      }\n      return [toLocation(definition)];\n    });\n  }\n};\nfunction toLocation(location) {\n  return {\n    uri: monaco_editor_core_exports.Uri.parse(location.uri),\n    range: toRange(location.range)\n  };\n}\nvar ReferenceAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.findReferences(resource.toString(), fromPosition(position));\n    }).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map(toLocation);\n    });\n  }\n};\nvar RenameAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doRename(resource.toString(), fromPosition(position), newName);\n    }).then((edit) => {\n      return toWorkspaceEdit(edit);\n    });\n  }\n};\nfunction toWorkspaceEdit(edit) {\n  if (!edit || !edit.changes) {\n    return void 0;\n  }\n  let resourceEdits = [];\n  for (let uri in edit.changes) {\n    const _uri = monaco_editor_core_exports.Uri.parse(uri);\n    for (let e of edit.changes[uri]) {\n      resourceEdits.push({\n        resource: _uri,\n        versionId: void 0,\n        textEdit: {\n          range: toRange(e.range),\n          text: e.newText\n        }\n      });\n    }\n  }\n  return {\n    edits: resourceEdits\n  };\n}\nvar DocumentSymbolAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentSymbols(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return items.map((item) => ({\n        name: item.name,\n        detail: \"\",\n        containerName: item.containerName,\n        kind: toSymbolKind(item.kind),\n        range: toRange(item.location.range),\n        selectionRange: toRange(item.location.range),\n        tags: []\n      }));\n    });\n  }\n};\nfunction toSymbolKind(kind) {\n  let mKind = monaco_editor_core_exports.languages.SymbolKind;\n  switch (kind) {\n    case SymbolKind.File:\n      return mKind.Array;\n    case SymbolKind.Module:\n      return mKind.Module;\n    case SymbolKind.Namespace:\n      return mKind.Namespace;\n    case SymbolKind.Package:\n      return mKind.Package;\n    case SymbolKind.Class:\n      return mKind.Class;\n    case SymbolKind.Method:\n      return mKind.Method;\n    case SymbolKind.Property:\n      return mKind.Property;\n    case SymbolKind.Field:\n      return mKind.Field;\n    case SymbolKind.Constructor:\n      return mKind.Constructor;\n    case SymbolKind.Enum:\n      return mKind.Enum;\n    case SymbolKind.Interface:\n      return mKind.Interface;\n    case SymbolKind.Function:\n      return mKind.Function;\n    case SymbolKind.Variable:\n      return mKind.Variable;\n    case SymbolKind.Constant:\n      return mKind.Constant;\n    case SymbolKind.String:\n      return mKind.String;\n    case SymbolKind.Number:\n      return mKind.Number;\n    case SymbolKind.Boolean:\n      return mKind.Boolean;\n    case SymbolKind.Array:\n      return mKind.Array;\n  }\n  return mKind.Function;\n}\nvar DocumentLinkAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideLinks(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentLinks(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return {\n        links: items.map((item) => ({\n          range: toRange(item.range),\n          url: item.target\n        }))\n      };\n    });\n  }\n};\nvar DocumentFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentFormattingEdits(model, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.format(resource.toString(), null, fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nvar DocumentRangeFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  canFormatMultipleRanges = false;\n  provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.format(resource.toString(), fromRange(range), fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nfunction fromFormattingOptions(options) {\n  return {\n    tabSize: options.tabSize,\n    insertSpaces: options.insertSpaces\n  };\n}\nvar DocumentColorAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentColors(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentColors(resource.toString())).then((infos) => {\n      if (!infos) {\n        return;\n      }\n      return infos.map((item) => ({\n        color: item.color,\n        range: toRange(item.range)\n      }));\n    });\n  }\n  provideColorPresentations(model, info, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.getColorPresentations(resource.toString(), info.color, fromRange(info.range))).then((presentations) => {\n      if (!presentations) {\n        return;\n      }\n      return presentations.map((presentation) => {\n        let item = {\n          label: presentation.label\n        };\n        if (presentation.textEdit) {\n          item.textEdit = toTextEdit(presentation.textEdit);\n        }\n        if (presentation.additionalTextEdits) {\n          item.additionalTextEdits = presentation.additionalTextEdits.map(toTextEdit);\n        }\n        return item;\n      });\n    });\n  }\n};\nvar FoldingRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideFoldingRanges(model, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.getFoldingRanges(resource.toString(), context)).then((ranges) => {\n      if (!ranges) {\n        return;\n      }\n      return ranges.map((range) => {\n        const result = {\n          start: range.startLine + 1,\n          end: range.endLine + 1\n        };\n        if (typeof range.kind !== \"undefined\") {\n          result.kind = toFoldingRangeKind(range.kind);\n        }\n        return result;\n      });\n    });\n  }\n};\nfunction toFoldingRangeKind(kind) {\n  switch (kind) {\n    case FoldingRangeKind.Comment:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Comment;\n    case FoldingRangeKind.Imports:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Imports;\n    case FoldingRangeKind.Region:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Region;\n  }\n  return void 0;\n}\nvar SelectionRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideSelectionRanges(model, positions, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.getSelectionRanges(resource.toString(), positions.map(fromPosition))).then((selectionRanges) => {\n      if (!selectionRanges) {\n        return;\n      }\n      return selectionRanges.map((selectionRange) => {\n        const result = [];\n        while (selectionRange) {\n          result.push({ range: toRange(selectionRange.range) });\n          selectionRange = selectionRange.parent;\n        }\n        return result;\n      });\n    });\n  }\n};\n\n// src/language/html/htmlMode.ts\nvar HTMLCompletionAdapter = class extends CompletionAdapter {\n  constructor(worker) {\n    super(worker, [\".\", \":\", \"<\", '\"', \"=\", \"/\"]);\n  }\n};\nfunction setupMode1(defaults) {\n  const client = new WorkerManager(defaults);\n  const worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  let languageId = defaults.languageId;\n  monaco_editor_core_exports.languages.registerCompletionItemProvider(languageId, new HTMLCompletionAdapter(worker));\n  monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker));\n  monaco_editor_core_exports.languages.registerDocumentHighlightProvider(languageId, new DocumentHighlightAdapter(worker));\n  monaco_editor_core_exports.languages.registerLinkProvider(languageId, new DocumentLinkAdapter(worker));\n  monaco_editor_core_exports.languages.registerFoldingRangeProvider(languageId, new FoldingRangeAdapter(worker));\n  monaco_editor_core_exports.languages.registerDocumentSymbolProvider(languageId, new DocumentSymbolAdapter(worker));\n  monaco_editor_core_exports.languages.registerSelectionRangeProvider(languageId, new SelectionRangeAdapter(worker));\n  monaco_editor_core_exports.languages.registerRenameProvider(languageId, new RenameAdapter(worker));\n  if (languageId === \"html\") {\n    monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(languageId, new DocumentFormattingEditProvider(worker));\n    monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(languageId, new DocumentRangeFormattingEditProvider(worker));\n  }\n}\nfunction setupMode(defaults) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(defaults);\n  disposables.push(client);\n  const worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  function registerProviders() {\n    const { languageId, modeConfiguration } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration.completionItems) {\n      providers.push(monaco_editor_core_exports.languages.registerCompletionItemProvider(languageId, new HTMLCompletionAdapter(worker)));\n    }\n    if (modeConfiguration.hovers) {\n      providers.push(monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker)));\n    }\n    if (modeConfiguration.documentHighlights) {\n      providers.push(monaco_editor_core_exports.languages.registerDocumentHighlightProvider(languageId, new DocumentHighlightAdapter(worker)));\n    }\n    if (modeConfiguration.links) {\n      providers.push(monaco_editor_core_exports.languages.registerLinkProvider(languageId, new DocumentLinkAdapter(worker)));\n    }\n    if (modeConfiguration.documentSymbols) {\n      providers.push(monaco_editor_core_exports.languages.registerDocumentSymbolProvider(languageId, new DocumentSymbolAdapter(worker)));\n    }\n    if (modeConfiguration.rename) {\n      providers.push(monaco_editor_core_exports.languages.registerRenameProvider(languageId, new RenameAdapter(worker)));\n    }\n    if (modeConfiguration.foldingRanges) {\n      providers.push(monaco_editor_core_exports.languages.registerFoldingRangeProvider(languageId, new FoldingRangeAdapter(worker)));\n    }\n    if (modeConfiguration.selectionRanges) {\n      providers.push(monaco_editor_core_exports.languages.registerSelectionRangeProvider(languageId, new SelectionRangeAdapter(worker)));\n    }\n    if (modeConfiguration.documentFormattingEdits) {\n      providers.push(monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(languageId, new DocumentFormattingEditProvider(worker)));\n    }\n    if (modeConfiguration.documentRangeFormattingEdits) {\n      providers.push(monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(languageId, new DocumentRangeFormattingEditProvider(worker)));\n    }\n  }\n  registerProviders();\n  disposables.push(asDisposable(providers));\n  return asDisposable(disposables);\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\nexport {\n  CompletionAdapter,\n  DefinitionAdapter,\n  DiagnosticsAdapter,\n  DocumentColorAdapter,\n  DocumentFormattingEditProvider,\n  DocumentHighlightAdapter,\n  DocumentLinkAdapter,\n  DocumentRangeFormattingEditProvider,\n  DocumentSymbolAdapter,\n  FoldingRangeAdapter,\n  HoverAdapter,\n  ReferenceAdapter,\n  RenameAdapter,\n  SelectionRangeAdapter,\n  WorkerManager,\n  fromPosition,\n  fromRange,\n  setupMode,\n  setupMode1,\n  toRange,\n  toTextEdit\n};\n"], "names": ["mod", "second<PERSON><PERSON><PERSON>", "integer2", "uinteger2", "Position3", "Range3", "Location2", "LocationLink2", "Color2", "ColorInformation2", "ColorPresentation2", "FoldingRangeKind2", "FoldingRange2", "DiagnosticRelatedInformation2", "DiagnosticSeverity2", "DiagnosticTag2", "Diagnostic2", "Command2", "TextEdit2", "ChangeAnnotation2", "AnnotatedTextEdit2", "TextDocumentEdit2", "CreateFile2", "RenameFile2", "DeleteFile2", "TextDocumentIdentifier2", "VersionedTextDocumentIdentifier2", "OptionalVersionedTextDocumentIdentifier2", "TextDocumentItem2", "MarkupKind2", "CompletionItemKind2", "InsertTextFormat2", "InsertReplaceEdit2", "InsertTextMode2", "MarkedString2", "DocumentHighlightKind2", "SymbolKind2", "DocumentSymbol2", "CodeActionKind2", "CodeActionContext2", "CodeAction2", "CodeLens2", "FormattingOptions2", "DocumentLink2", "SelectionRange2", "TextDocument2", "Is2", "toString", "integer", "<PERSON><PERSON><PERSON><PERSON>", "Position", "Range", "Location", "LocationLink", "Color", "ColorInformation", "ColorPresentation", "FoldingRangeKind", "FoldingRange", "DiagnosticRelatedInformation", "DiagnosticSeverity", "DiagnosticTag", "CodeDescription", "Diagnostic", "Command", "TextEdit", "ChangeAnnotation", "ChangeAnnotationIdentifier", "AnnotatedTextEdit", "TextDocumentEdit", "CreateFile", "RenameFile", "DeleteFile", "WorkspaceEdit", "TextDocumentIdentifier", "VersionedTextDocumentIdentifier", "OptionalVersionedTextDocumentIdentifier", "TextDocumentItem", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CompletionItemKind", "InsertTextFormat", "CompletionItemTag", "InsertReplaceEdit", "InsertTextMode", "CompletionItem", "CompletionList", "MarkedString", "Hover", "ParameterInformation", "SignatureInformation", "DocumentHighlightKind", "DocumentHighlight", "SymbolKind", "SymbolTag", "SymbolInformation", "DocumentSymbol", "CodeActionKind", "CodeActionContext", "CodeAction", "CodeLens", "FormattingOptions", "DocumentLink", "SelectionRange", "TextDocument", "Is", "__defProp", "Object", "__getOwnPropDesc", "__getOwnPropNames", "__hasOwnProp", "__copyProps", "to", "from", "except", "desc", "key", "monaco_editor_core_exports", "WorkerManager", "defaults", "window", "clearInterval", "Date", "resources", "_client", "client", "_", "line", "character", "Number", "value", "candidate", "one", "two", "three", "four", "Error", "uri", "range", "targetUri", "targetRange", "targetSelectionRange", "originSelectionRange", "red", "green", "blue", "alpha", "color", "label", "textEdit", "additionalTextEdits", "startLine", "endLine", "startCharacter", "endCharacter", "kind", "result", "location", "message", "CodeDescription2", "severity", "code", "source", "relatedInformation", "_a", "title", "command", "args", "_i", "arguments", "newText", "position", "needsConfirmation", "description", "ChangeAnnotationIdentifier2", "annotation", "textDocument", "edits", "Array", "options", "old<PERSON><PERSON>", "newUri", "WorkspaceEdit2", "change", "TextEditChangeImpl", "TextEditChangeImpl2", "changeAnnotations", "edit", "id", "ChangeAnnotations", "ChangeAnnotations2", "annotations", "idOrAnnotation", "WorkspaceChange2", "workspaceEdit", "_this", "textEditChange", "textDocumentEdit", "optionsOrAnnotation", "operation", "version", "languageId", "text", "MarkupContent2", "CompletionItemTag2", "insert", "replace", "CompletionItem2", "CompletionList2", "items", "isIncomplete", "plainText", "Hover2", "ParameterInformation2", "documentation", "SignatureInformation2", "parameters", "DocumentHighlight2", "SymbolTag2", "SymbolInformation2", "name", "containerName", "detail", "<PERSON><PERSON><PERSON><PERSON>", "children", "diagnostics", "only", "kindOrCommandOrEdit", "checkKind", "data", "tabSize", "insertSpaces", "target", "parent", "content", "FullTextDocument", "document", "sortedEdits", "mergeSort", "compare", "p", "left", "right", "leftIdx", "rightIdx", "i", "ret", "a", "b", "diff", "lastModifiedOffset", "e", "startOffset", "endOffset", "FullTextDocument2", "start", "end", "event", "lineOffsets", "isLineStart", "ch", "offset", "Math", "low", "high", "mid", "lineOffset", "nextLineOffset", "min", "max", "check", "DiagnosticsAdapter", "_languageId", "_worker", "configChangeEvent", "onModelAdd", "model", "handle", "modeId", "onModelRemoved", "uriStr", "listener", "d", "resource", "worker", "markers", "toDiagnostics", "diag", "String", "toSeverity", "lsSeverity", "err", "console", "CompletionAdapter", "_triggerCharacters", "context", "token", "fromPosition", "info", "wordInfo", "wordRange", "entry", "item", "to<PERSON>ommand", "c", "toCompletionItemKind", "mItemKind", "isInsertReplaceEdit", "to<PERSON><PERSON><PERSON>", "toTextEdit", "fromRange", "HoverAdapter", "toMarkedStringArray", "contents", "toMarkdownString", "thing", "DocumentHighlightAdapter", "entries", "toDocumentHighlightKind", "DefinitionAdapter", "definition", "toLocation", "ReferenceAdapter", "RenameAdapter", "newName", "toWorkspaceEdit", "resourceEdits", "_uri", "DocumentSymbolAdapter", "toSymbolKind", "m<PERSON>ind", "DocumentLinkAdapter", "DocumentFormattingEditProvider", "fromFormattingOptions", "DocumentRangeFormattingEditProvider", "DocumentColorAdapter", "infos", "presentations", "presentation", "FoldingRangeAdapter", "ranges", "toFoldingRangeKind", "SelectionRangeAdapter", "positions", "<PERSON><PERSON><PERSON><PERSON>", "HTMLCompletionAdapter", "setupMode1", "uris", "setupMode", "disposables", "providers", "registerProviders", "modeConfiguration", "disposeAll", "asDisposable"], "mappings": ";kIAmB0BA,EAAKC,y1BA0ErBC,EAKAC,EAKAC,EAkBAC,EAkBAC,EAYAC,EAYAC,EAiBAC,EAeAC,EAgBAC,EAMAC,EAyBAC,EAeAC,EAOAC,EAaAC,EA0BAC,EAoBAC,EAoBAC,EA2BAC,EAoBAC,EAYAC,EAsBAC,EAuBAC,EAuTAC,EAYAC,EAYAC,EAYAC,EAYAC,EAIAA,EAgBAC,EA4BAC,EASAC,EAYAC,EAmBAC,EA+CAC,EAiBAC,EAgDAC,EAsBAC,EAYAC,EAgBAC,EAyBAC,EAgBAC,EAYAC,EAYAC,EAYAC,EAuKAC,EACJC,EAzrCFC,EAKAC,EAKAC,GAkBAC,GAkBAC,GAYAC,GAYAC,GAiBAC,GAeAC,GAgBAC,GAMAC,GAyBAC,GAeAC,GAOAC,GAKAC,GAQAC,GA0BAC,GAoBAC,GAoBAC,GAmBAC,GAQAC,GAoBAC,GAYAC,GAsBAC,GAuBAC,GAsBAC,GAiSAC,GAYAC,GAYAC,GAYAC,GAYAC,GAYAC,GAQAC,GA4BAC,GAKAC,GAIAC,GAYAC,GAKAC,GAOAC,GAOAC,GAYAC,GAQAC,GAOAC,GAoBAC,GAMAC,GAWAC,GA6BAC,GAIAC,GAeAC,GAsBAC,GAYAC,GAgBAC,GAyBAC,GAgBAC,GAYAC,GAYAC,GAYAC,GAuKAC,gBA5wCAC,GAAYC,OAAO,cAAc,CACjCC,GAAmBD,OAAO,wBAAwB,CAClDE,GAAoBF,OAAO,mBAAmB,CAC9CG,GAAeH,OAAO,SAAS,CAAC,cAAc,CAC9CI,GAAc,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAQ,AAAgB,UAAhB,OAAOA,GAAqB,AAAgB,YAAhB,OAAOA,EAC7C,IAAK,IAAIG,KAAOP,GAAkBI,GAC5B,CAACH,GAAa,IAAI,CAACE,EAAII,IAAQA,IAAQF,GACzCR,GAAUM,EAAII,EAAK,CAAE,IAAK,IAAMH,CAAI,CAACG,EAAI,CAAE,WAAY,CAAED,CAAAA,EAAOP,GAAiBK,EAAMG,EAAG,GAAMD,EAAK,UAAU,AAAC,GAEtH,OAAOH,CACT,EAIIK,GAA6B,CAAC,EAHeN,GAItCM,GAJerH,EAIa,GAJmC,WAAYC,GAAgB8G,GAAY9G,EAAcD,EAAK,WASrI,IAAIsH,GAAgB,MAClB,SAAU,AACV,mBAAmB,AACnB,cAAc,AACd,sBAAsB,AACtB,QAAQ,AACR,QAAQ,AACR,aAAYC,CAAQ,CAAE,CACpB,IAAI,CAAC,SAAS,CAAGA,EACjB,IAAI,CAAC,OAAO,CAAG,KACf,IAAI,CAAC,OAAO,CAAG,KACf,IAAI,CAAC,kBAAkB,CAAGC,OAAO,WAAW,CAAC,IAAM,IAAI,CAAC,YAAY,GAAI,KACxE,IAAI,CAAC,aAAa,CAAG,EACrB,IAAI,CAAC,qBAAqB,CAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAM,IAAI,CAAC,WAAW,GAChF,CACA,aAAc,CACR,IAAI,CAAC,OAAO,GACd,IAAI,CAAC,OAAO,CAAC,OAAO,GACpB,IAAI,CAAC,OAAO,CAAG,MAEjB,IAAI,CAAC,OAAO,CAAG,IACjB,CACA,SAAU,CACRC,cAAc,IAAI,CAAC,kBAAkB,EACrC,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAClC,IAAI,CAAC,WAAW,EAClB,CACA,cAAe,CACb,IAAI,CAAC,IAAI,CAAC,OAAO,CAGaC,KAAK,GAAG,GAAK,IAAI,CAAC,aAAa,CAhCxC,MAkCnB,IAAI,CAAC,WAAW,EAEpB,CACA,YAAa,CAaX,OAZA,IAAI,CAAC,aAAa,CAAGA,KAAK,GAAG,GACzB,CAAC,IAAI,CAAC,OAAO,GACf,IAAI,CAAC,OAAO,CAAGL,GAA2B,MAAM,CAAC,eAAe,CAAC,CAC/D,SAAU,8BACV,WAAY,CACV,iBAAkB,IAAI,CAAC,SAAS,CAAC,OAAO,CACxC,WAAY,IAAI,CAAC,SAAS,CAAC,UAAU,AACvC,EACA,MAAO,IAAI,CAAC,SAAS,CAAC,UAAU,AAClC,GACA,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,IAE/B,IAAI,CAAC,OAAO,AACrB,CACA,yBAAyB,GAAGM,CAAS,CAAE,CACrC,IAAIC,EACJ,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,AAACC,IAC7BD,EAAUC,CACZ,GAAG,IAAI,CAAC,AAACC,IACP,GAAI,IAAI,CAAC,OAAO,CACd,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAACH,EAE5C,GAAG,IAAI,CAAC,AAACG,GAAMF,EACjB,CACF,CAKE1H,EADQA,EAGP8C,GAAYA,CAAAA,EAAU,CAAC,IAFf,SAAS,CAAG,YACrB9C,EAAS,SAAS,CAAG,WAIrBC,CADQA,EAGP8C,GAAaA,CAAAA,EAAW,CAAC,IAFhB,SAAS,CAAG,EACtB9C,EAAU,SAAS,CAAG,WAatBC,CAVQA,EAgBP8C,IAAaA,CAAAA,GAAW,CAAC,IANhB,MAAM,CAThB,SAAgB6E,CAAI,CAAEC,CAAS,EAO7B,OANID,IAASE,OAAO,SAAS,EAC3BF,CAAAA,EAAO9E,EAAS,SAAS,AAAD,EAEtB+E,IAAcC,OAAO,SAAS,EAChCD,CAAAA,EAAY/E,EAAS,SAAS,AAAD,EAExB,CAAE8E,KAAAA,EAAMC,UAAAA,CAAU,CAC3B,EAMA5H,EAAU,EAAE,CAJZ,SAAY8H,CAAK,EAEf,OAAOzB,GAAG,aAAa,CADPyB,IACsBzB,GAAG,QAAQ,CAAC0B,AADlCD,EAC4C,IAAI,GAAKzB,GAAG,QAAQ,CAAC0B,AADjED,EAC2E,SAAS,CACtG,EAcA7H,CAVQA,EAgBP8C,IAAUA,CAAAA,GAAQ,CAAC,IANb,MAAM,CATb,SAAgBiF,CAAG,CAAEC,CAAG,CAAEC,CAAK,CAAEC,CAAI,EACnC,GAAI9B,GAAG,QAAQ,CAAC2B,IAAQ3B,GAAG,QAAQ,CAAC4B,IAAQ5B,GAAG,QAAQ,CAAC6B,IAAU7B,GAAG,QAAQ,CAAC8B,GAC5E,MAAO,CAAE,MAAOrF,GAAS,MAAM,CAACkF,EAAKC,GAAM,IAAKnF,GAAS,MAAM,CAACoF,EAAOC,EAAM,EACxE,GAAIrF,GAAS,EAAE,CAACkF,IAAQlF,GAAS,EAAE,CAACmF,GACzC,MAAO,CAAE,MAAOD,EAAK,IAAKC,CAAI,CAE9B,OAAM,AAAIG,MAAM,8CAAgDJ,EAAM,KAAOC,EAAM,KAAOC,EAAQ,KAAOC,EAAO,IAEpH,EAMAlI,EAAO,EAAE,CAJT,SAAY6H,CAAK,EAEf,OAAOzB,GAAG,aAAa,CADPyB,IACsBhF,GAAS,EAAE,CAACiF,AADlCD,EAC4C,KAAK,GAAKhF,GAAS,EAAE,CAACiF,AADlED,EAC4E,GAAG,CACjG,EAQA5H,CAJQA,EAUP8C,IAAaA,CAAAA,GAAW,CAAC,IANhB,MAAM,CAHhB,SAAgBqF,CAAG,CAAEC,CAAK,EACxB,MAAO,CAAED,IAAAA,EAAKC,MAAAA,CAAM,CACtB,EAMApI,EAAU,EAAE,CAJZ,SAAY4H,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgB/E,GAAM,EAAE,CAACgF,AADzBD,EACmC,KAAK,GAAMzB,CAAAA,GAAG,MAAM,CAAC0B,AADxDD,EACkE,GAAG,GAAKzB,GAAG,SAAS,CAAC0B,AADvFD,EACiG,GAAG,EACtH,EAQA3H,CAJQA,EAUP8C,IAAiBA,CAAAA,GAAe,CAAC,IANpB,MAAM,CAHpB,SAAgBsF,CAAS,CAAEC,CAAW,CAAEC,CAAoB,CAAEC,CAAoB,EAChF,MAAO,CAAEH,UAAAA,EAAWC,YAAAA,EAAaC,qBAAAA,EAAsBC,qBAAAA,CAAqB,CAC9E,EAMAvI,EAAc,EAAE,CAJhB,SAAY2H,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgB/E,GAAM,EAAE,CAACgF,AADzBD,EACmC,WAAW,GAAKzB,GAAG,MAAM,CAAC0B,AAD7DD,EACuE,SAAS,GAAM/E,CAAAA,GAAM,EAAE,CAACgF,AAD/FD,EACyG,oBAAoB,GAAKzB,GAAG,SAAS,CAAC0B,AAD/ID,EACyJ,oBAAoB,IAAO/E,CAAAA,GAAM,EAAE,CAACgF,AAD7LD,EACuM,oBAAoB,GAAKzB,GAAG,SAAS,CAAC0B,AAD7OD,EACuP,oBAAoB,EAC7R,EAaA1H,CATQA,EAeP8C,IAAUA,CAAAA,GAAQ,CAAC,IANb,MAAM,CARb,SAAgByF,CAAG,CAAEC,CAAK,CAAEC,CAAI,CAAEC,CAAK,EACrC,MAAO,CACLH,IAAAA,EACAC,MAAAA,EACAC,KAAAA,EACAC,MAAAA,CACF,CACF,EAMA1I,EAAO,EAAE,CAJT,SAAY0H,CAAK,EAEf,OAAOzB,GAAG,WAAW,CAAC0B,AADND,EACgB,GAAG,CAAE,EAAG,IAAMzB,GAAG,WAAW,CAAC0B,AAD7CD,EACuD,KAAK,CAAE,EAAG,IAAMzB,GAAG,WAAW,CAAC0B,AADtFD,EACgG,IAAI,CAAE,EAAG,IAAMzB,GAAG,WAAW,CAAC0B,AAD9HD,EACwI,KAAK,CAAE,EAAG,EACpK,EAWAzH,CAPQA,EAaP8C,IAAqBA,CAAAA,GAAmB,CAAC,IANxB,MAAM,CANxB,SAAgBmF,CAAK,CAAES,CAAK,EAC1B,MAAO,CACLT,MAAAA,EACAS,MAAAA,CACF,CACF,EAMA1I,EAAkB,EAAE,CAJpB,SAAYyH,CAAK,EAEf,OAAO/E,GAAM,EAAE,CAACgF,AADAD,EACU,KAAK,GAAK5E,GAAM,EAAE,CAAC6E,AAD7BD,EACuC,KAAK,CAC9D,EAYAxH,CARQA,EAcP8C,IAAsBA,CAAAA,GAAoB,CAAC,IANzB,MAAM,CAPzB,SAAgB4F,CAAK,CAAEC,CAAQ,CAAEC,CAAmB,EAClD,MAAO,CACLF,MAAAA,EACAC,SAAAA,EACAC,oBAAAA,CACF,CACF,EAMA5I,EAAmB,EAAE,CAJrB,SAAYwH,CAAK,EAEf,OAAOzB,GAAG,MAAM,CAAC0B,AADDD,EACW,KAAK,GAAMzB,CAAAA,GAAG,SAAS,CAAC0B,AADnCD,EAC6C,QAAQ,GAAKjE,GAAS,EAAE,CADrEiE,EAC+E,GAAOzB,CAAAA,GAAG,SAAS,CAAC0B,AADnGD,EAC6G,mBAAmB,GAAKzB,GAAG,UAAU,CAAC0B,AADnJD,EAC6J,mBAAmB,CAAEjE,GAAS,EAAE,EAC/M,EAKAtD,CADQA,EAIP8C,IAAqBA,CAAAA,GAAmB,CAAC,IAHxB,OAAU,CAAG,UAC/B9C,EAAkB,OAAU,CAAG,UAC/BA,EAAkB,MAAS,CAAG,SAoB9BC,CAjBQA,EAuBP8C,IAAiBA,CAAAA,GAAe,CAAC,IANpB,MAAM,CAhBpB,SAAgB6F,CAAS,CAAEC,CAAO,CAAEC,CAAc,CAAEC,CAAY,CAAEC,CAAI,EACpE,IAAIC,EAAS,CACXL,UAAAA,EACAC,QAAAA,CACF,EAUA,OATI/C,GAAG,OAAO,CAACgD,IACbG,CAAAA,EAAO,cAAc,CAAGH,CAAa,EAEnChD,GAAG,OAAO,CAACiD,IACbE,CAAAA,EAAO,YAAY,CAAGF,CAAW,EAE/BjD,GAAG,OAAO,CAACkD,IACbC,CAAAA,EAAO,IAAI,CAAGD,CAAG,EAEZC,CACT,EAMAhJ,EAAc,EAAE,CAJhB,SAAYsH,CAAK,EAEf,OAAOzB,GAAG,QAAQ,CAAC0B,AADHD,EACa,SAAS,GAAKzB,GAAG,QAAQ,CAAC0B,AADvCD,EACiD,SAAS,GAAMzB,CAAAA,GAAG,SAAS,CAAC0B,AAD7ED,EACuF,cAAc,GAAKzB,GAAG,QAAQ,CAAC0B,AADtHD,EACgI,cAAc,IAAOzB,CAAAA,GAAG,SAAS,CAAC0B,AADlKD,EAC4K,YAAY,GAAKzB,GAAG,QAAQ,CAAC0B,AADzMD,EACmN,YAAY,IAAOzB,CAAAA,GAAG,SAAS,CAAC0B,AADnPD,EAC6P,IAAI,GAAKzB,GAAG,MAAM,CAAC0B,AADhRD,EAC0R,IAAI,EAChT,EAWArH,CAPQA,EAaP8C,IAAiCA,CAAAA,GAA+B,CAAC,IANpC,MAAM,CANpC,SAAgBkG,CAAQ,CAAEC,CAAO,EAC/B,MAAO,CACLD,SAAAA,EACAC,QAAAA,CACF,CACF,EAMAjJ,EAA8B,EAAE,CAJhC,SAAYqH,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgB9E,GAAS,EAAE,CAAC+E,AAD5BD,EACsC,QAAQ,GAAKzB,GAAG,MAAM,CAAC0B,AAD7DD,EACuE,OAAO,CAChG,EAKApH,CADQA,EAKP8C,IAAuBA,CAAAA,GAAqB,CAAC,IAJ1B,KAAK,CAAG,EAC5B9C,EAAoB,OAAO,CAAG,EAC9BA,EAAoB,WAAW,CAAG,EAClCA,EAAoB,IAAI,CAAG,EAI3BC,CADQA,EAGP8C,IAAkBA,CAAAA,GAAgB,CAAC,IAFrB,WAAW,CAAG,EAC7B9C,EAAe,UAAU,CAAG,EAQ5BgJ,AACCjG,CAAAA,IAAoBA,CAAAA,GAAkB,CAAC,EAAC,EADxB,EAAE,CAJnB,SAAYoE,CAAK,EAEf,OAAOC,MADSD,GACqCzB,GAAG,MAAM,CAAC0B,AAD/CD,EACyD,IAAI,CAC/E,EAqBAlH,CAjBQA,EAwBP+C,IAAeA,CAAAA,GAAa,CAAC,IAPlB,MAAM,CAhBlB,SAAgB2E,CAAK,CAAEoB,CAAO,CAAEE,CAAQ,CAAEC,CAAI,CAAEC,CAAM,CAAEC,CAAkB,EACxE,IAAIP,EAAS,CAAElB,MAAAA,EAAOoB,QAAAA,CAAQ,EAa9B,OAZIrD,GAAG,OAAO,CAACuD,IACbJ,CAAAA,EAAO,QAAQ,CAAGI,CAAO,EAEvBvD,GAAG,OAAO,CAACwD,IACbL,CAAAA,EAAO,IAAI,CAAGK,CAAG,EAEfxD,GAAG,OAAO,CAACyD,IACbN,CAAAA,EAAO,MAAM,CAAGM,CAAK,EAEnBzD,GAAG,OAAO,CAAC0D,IACbP,CAAAA,EAAO,kBAAkB,CAAGO,CAAiB,EAExCP,CACT,EAOA5I,EAAY,EAAE,CALd,SAAYkH,CAAK,EAEf,IADIkC,EAEJ,OAAO3D,GAAG,OAAO,CADDyB,IACgB/E,GAAM,EAAE,CAACgF,AADzBD,EACmC,KAAK,GAAKzB,GAAG,MAAM,CAAC0B,AADvDD,EACiE,OAAO,GAAMzB,CAAAA,GAAG,MAAM,CAAC0B,AADxFD,EACkG,QAAQ,GAAKzB,GAAG,SAAS,CAAC0B,AAD5HD,EACsI,QAAQ,IAAOzB,CAAAA,GAAG,OAAO,CAAC0B,AADhKD,EAC0K,IAAI,GAAKzB,GAAG,MAAM,CAAC0B,AAD7LD,EACuM,IAAI,GAAKzB,GAAG,SAAS,CAAC0B,AAD7ND,EACuO,IAAI,IAAOzB,CAAAA,GAAG,SAAS,CAAC0B,AAD/PD,EACyQ,eAAe,GAAKzB,GAAG,MAAM,CAAC,AAAqC,OAApC2D,CAAAA,EAAKjC,AAD7SD,EACuT,eAAe,AAAD,GAAekC,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG,IAAI,IAAO3D,CAAAA,GAAG,MAAM,CAAC0B,AADrYD,EAC+Y,MAAM,GAAKzB,GAAG,SAAS,CAAC0B,AADvaD,EACib,MAAM,IAAOzB,CAAAA,GAAG,SAAS,CAAC0B,AAD3cD,EACqd,kBAAkB,GAAKzB,GAAG,UAAU,CAAC0B,AAD1fD,EACogB,kBAAkB,CAAEvE,GAA6B,EAAE,EACzkB,EAgBA1C,CAZQA,EAkBP+C,IAAYA,CAAAA,GAAU,CAAC,IANf,MAAM,CAXf,SAAgBqG,CAAK,CAAEC,CAAO,EAE5B,IAAK,IADDC,EAAO,EAAE,CACJC,EAAK,EAAGA,EAAKC,UAAU,MAAM,CAAED,IACtCD,CAAI,CAACC,EAAK,EAAE,CAAGC,SAAS,CAACD,EAAG,CAF9B,IAIIZ,EAAS,CAAES,MAAAA,EAAOC,QAAAA,CAAQ,EAI9B,OAHI7D,GAAG,OAAO,CAAC8D,IAASA,EAAK,MAAM,CAAG,GACpCX,CAAAA,EAAO,SAAS,CAAGW,CAAG,EAEjBX,CACT,EAMA3I,EAAS,EAAE,CAJX,SAAYiH,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgBzB,GAAG,MAAM,CAAC0B,AAD1BD,EACoC,KAAK,GAAKzB,GAAG,MAAM,CAAC0B,AADxDD,EACkE,OAAO,CAC3F,EAQAhH,CAJQA,EAkBP+C,IAAaA,CAAAA,GAAW,CAAC,IAdhB,OAAO,CAHjB,SAAiByE,CAAK,CAAEgC,CAAO,EAC7B,MAAO,CAAEhC,MAAAA,EAAOgC,QAAAA,CAAQ,CAC1B,EAKAxJ,EAAU,MAAM,CAHhB,SAAgByJ,CAAQ,CAAED,CAAO,EAC/B,MAAO,CAAE,MAAO,CAAE,MAAOC,EAAU,IAAKA,CAAS,EAAGD,QAAAA,CAAQ,CAC9D,EAKAxJ,EAAU,GAAG,CAHb,SAAawH,CAAK,EAChB,MAAO,CAAEA,MAAAA,EAAO,QAAS,EAAG,CAC9B,EAMAxH,EAAU,EAAE,CAJZ,SAAYgH,CAAK,EAEf,OAAOzB,GAAG,aAAa,CADPyB,IACsBzB,GAAG,MAAM,CAAC0B,AADhCD,EAC0C,OAAO,GAAK/E,GAAM,EAAE,CAACgF,AAD/DD,EACyE,KAAK,CAChG,EAeA/G,CAXQA,EAiBP+C,IAAqBA,CAAAA,GAAmB,CAAC,IANxB,MAAM,CAVxB,SAAgBkF,CAAK,CAAEwB,CAAiB,CAAEC,CAAW,EACnD,IAAIjB,EAAS,CAAER,MAAAA,CAAM,EAOrB,OAN0B,KAAK,IAA3BwB,GACFhB,CAAAA,EAAO,iBAAiB,CAAGgB,CAAgB,EAEzB,KAAK,IAArBC,GACFjB,CAAAA,EAAO,WAAW,CAAGiB,CAAU,EAE1BjB,CACT,EAMAzI,EAAkB,EAAE,CAJpB,SAAY+G,CAAK,EAEf,OAAOC,AAAc,KAAK,IADVD,GACezB,GAAG,aAAa,CAD/ByB,IAC8CzB,GAAG,MAAM,CAAC0B,AADxDD,EACkE,KAAK,GAAMzB,CAAAA,GAAG,OAAO,CAAC0B,AADxFD,EACkG,iBAAiB,GAAKC,AAAgC,KAAK,IAArCA,AADxHD,EACkI,iBAAiB,AAAU,GAAOzB,CAAAA,GAAG,MAAM,CAAC0B,AAD9KD,EACwL,WAAW,GAAKC,AAA0B,KAAK,IAA/BA,AADxMD,EACkN,WAAW,AAAU,CACzP,EASA4C,AACC3G,CAAAA,IAA+BA,CAAAA,GAA6B,CAAC,EAAC,EADnC,EAAE,CAJ9B,SAAY+D,CAAK,EAEf,MAAO,AAAqB,UAArB,OADSA,CAElB,EAQA9G,CAJQA,EAkBPgD,IAAsBA,CAAAA,GAAoB,CAAC,IAdzB,OAAO,CAH1B,SAAiBsE,CAAK,CAAEgC,CAAO,CAAEK,CAAU,EACzC,MAAO,CAAErC,MAAAA,EAAOgC,QAAAA,EAAS,aAAcK,CAAW,CACpD,EAKA3J,EAAmB,MAAM,CAHzB,SAAgBuJ,CAAQ,CAAED,CAAO,CAAEK,CAAU,EAC3C,MAAO,CAAE,MAAO,CAAE,MAAOJ,EAAU,IAAKA,CAAS,EAAGD,QAAAA,EAAS,aAAcK,CAAW,CACxF,EAKA3J,EAAmB,GAAG,CAHtB,SAAasH,CAAK,CAAEqC,CAAU,EAC5B,MAAO,CAAErC,MAAAA,EAAO,QAAS,GAAI,aAAcqC,CAAW,CACxD,EAMA3J,EAAmB,EAAE,CAJrB,SAAY8G,CAAK,EAEf,OAAOjE,GAAS,EAAE,CADFiE,IACkBhE,CAAAA,GAAiB,EAAE,CAACiE,AADtCD,EACgD,YAAY,GAAK/D,GAA2B,EAAE,CAACgE,AAD/FD,EACyG,YAAY,EACvI,EAQA7G,CAJQA,EAUPgD,IAAqBA,CAAAA,GAAmB,CAAC,IANxB,MAAM,CAHxB,SAAgB2G,CAAY,CAAEC,CAAK,EACjC,MAAO,CAAED,aAAAA,EAAcC,MAAAA,CAAM,CAC/B,EAMA5J,EAAkB,EAAE,CAJpB,SAAY6G,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgBtD,GAAwC,EAAE,CAACuD,AAD3DD,EACqE,YAAY,GAAKgD,MAAM,OAAO,CAAC/C,AADpGD,EAC8G,KAAK,CACrI,EAkBA5G,CAdQA,EAoBPgD,IAAeA,CAAAA,GAAa,CAAC,IANlB,MAAM,CAblB,SAAgBmE,CAAG,CAAE0C,CAAO,CAAEJ,CAAU,EACtC,IAAInB,EAAS,CACX,KAAM,SACNnB,IAAAA,CACF,EAOA,OANgB,KAAK,IAAjB0C,GAAuBA,CAAAA,AAAsB,KAAK,IAA3BA,EAAQ,SAAS,EAAeA,AAA2B,KAAK,IAAhCA,EAAQ,cAAc,AAAU,GACzFvB,CAAAA,EAAO,OAAO,CAAGuB,CAAM,EAEN,KAAK,IAApBJ,GACFnB,CAAAA,EAAO,YAAY,CAAGmB,CAAS,EAE1BnB,CACT,EAMAtI,EAAY,EAAE,CAJd,SAAY4G,CAAK,EAEf,OAAOC,AADSD,GACIC,AAAmB,WAAnBA,AADJD,EACc,IAAI,EAAiBzB,GAAG,MAAM,CAAC0B,AAD7CD,EACuD,GAAG,GAAMC,CAAAA,AAAsB,KAAK,IAA3BA,AADhED,EAC0E,OAAO,EAAe,AAACC,CAAAA,AAAgC,KAAK,IAArCA,AADjGD,EAC2G,OAAO,CAAC,SAAS,EAAezB,GAAG,OAAO,CAAC0B,AADtJD,EACgK,OAAO,CAAC,SAAS,IAAOC,CAAAA,AAAqC,KAAK,IAA1CA,AADxLD,EACkM,OAAO,CAAC,cAAc,EAAezB,GAAG,OAAO,CAAC0B,AADlPD,EAC4P,OAAO,CAAC,cAAc,EAAC,GAAOC,CAAAA,AAA2B,KAAK,IAAhCA,AAD1RD,EACoS,YAAY,EAAe/D,GAA2B,EAAE,CAACgE,AAD7VD,EACuW,YAAY,EACrY,EAmBA3G,CAfQA,EAqBPgD,IAAeA,CAAAA,GAAa,CAAC,IANlB,MAAM,CAdlB,SAAgB6G,CAAM,CAAEC,CAAM,CAAEF,CAAO,CAAEJ,CAAU,EACjD,IAAInB,EAAS,CACX,KAAM,SACNwB,OAAAA,EACAC,OAAAA,CACF,EAOA,OANgB,KAAK,IAAjBF,GAAuBA,CAAAA,AAAsB,KAAK,IAA3BA,EAAQ,SAAS,EAAeA,AAA2B,KAAK,IAAhCA,EAAQ,cAAc,AAAU,GACzFvB,CAAAA,EAAO,OAAO,CAAGuB,CAAM,EAEN,KAAK,IAApBJ,GACFnB,CAAAA,EAAO,YAAY,CAAGmB,CAAS,EAE1BnB,CACT,EAMArI,EAAY,EAAE,CAJd,SAAY2G,CAAK,EAEf,OAAOC,AADSD,GACIC,AAAmB,WAAnBA,AADJD,EACc,IAAI,EAAiBzB,GAAG,MAAM,CAAC0B,AAD7CD,EACuD,MAAM,GAAKzB,GAAG,MAAM,CAAC0B,AAD5ED,EACsF,MAAM,GAAMC,CAAAA,AAAsB,KAAK,IAA3BA,AADlGD,EAC4G,OAAO,EAAe,AAACC,CAAAA,AAAgC,KAAK,IAArCA,AADnID,EAC6I,OAAO,CAAC,SAAS,EAAezB,GAAG,OAAO,CAAC0B,AADxLD,EACkM,OAAO,CAAC,SAAS,IAAOC,CAAAA,AAAqC,KAAK,IAA1CA,AAD1ND,EACoO,OAAO,CAAC,cAAc,EAAezB,GAAG,OAAO,CAAC0B,AADpRD,EAC8R,OAAO,CAAC,cAAc,EAAC,GAAOC,CAAAA,AAA2B,KAAK,IAAhCA,AAD5TD,EACsU,YAAY,EAAe/D,GAA2B,EAAE,CAACgE,AAD/XD,EACyY,YAAY,EACva,EAkBA1G,CAdQA,EAoBPgD,IAAeA,CAAAA,GAAa,CAAC,IANlB,MAAM,CAblB,SAAgBiE,CAAG,CAAE0C,CAAO,CAAEJ,CAAU,EACtC,IAAInB,EAAS,CACX,KAAM,SACNnB,IAAAA,CACF,EAOA,OANgB,KAAK,IAAjB0C,GAAuBA,CAAAA,AAAsB,KAAK,IAA3BA,EAAQ,SAAS,EAAeA,AAA8B,KAAK,IAAnCA,EAAQ,iBAAiB,AAAU,GAC5FvB,CAAAA,EAAO,OAAO,CAAGuB,CAAM,EAEN,KAAK,IAApBJ,GACFnB,CAAAA,EAAO,YAAY,CAAGmB,CAAS,EAE1BnB,CACT,EAMApI,EAAY,EAAE,CAJd,SAAY0G,CAAK,EAEf,OAAOC,AADSD,GACIC,AAAmB,WAAnBA,AADJD,EACc,IAAI,EAAiBzB,GAAG,MAAM,CAAC0B,AAD7CD,EACuD,GAAG,GAAMC,CAAAA,AAAsB,KAAK,IAA3BA,AADhED,EAC0E,OAAO,EAAe,AAACC,CAAAA,AAAgC,KAAK,IAArCA,AADjGD,EAC2G,OAAO,CAAC,SAAS,EAAezB,GAAG,OAAO,CAAC0B,AADtJD,EACgK,OAAO,CAAC,SAAS,IAAOC,CAAAA,AAAwC,KAAK,IAA7CA,AADxLD,EACkM,OAAO,CAAC,iBAAiB,EAAezB,GAAG,OAAO,CAAC0B,AADrPD,EAC+P,OAAO,CAAC,iBAAiB,EAAC,GAAOC,CAAAA,AAA2B,KAAK,IAAhCA,AADhSD,EAC0S,YAAY,EAAe/D,GAA2B,EAAE,CAACgE,AADnWD,EAC6W,YAAY,EAC3Y,EAeAoD,AACC7G,CAAAA,IAAkBA,CAAAA,GAAgB,CAAC,EAAC,EADtB,EAAE,CAVjB,SAAYyD,CAAK,EAEf,OAAOC,AADSD,GACKC,CAAAA,AAAsB,KAAK,IAA3BA,AADLD,EACe,OAAO,EAAeC,AAA8B,KAAK,IAAnCA,AADrCD,EAC+C,eAAe,AAAU,GAAOC,CAAAA,AAA8B,KAAK,IAAnCA,AAD/ED,EACyF,eAAe,EAAeC,AADvHD,EACiI,eAAe,CAAC,KAAK,CAAC,SAASqD,CAAM,SACpL,AAAI9E,GAAG,MAAM,CAAC8E,EAAO,IAAI,EAChBjH,GAAW,EAAE,CAACiH,IAAWhH,GAAW,EAAE,CAACgH,IAAW/G,GAAW,EAAE,CAAC+G,GAEhElH,GAAiB,EAAE,CAACkH,EAE/B,EAAC,CACH,EAGF,IAAIC,GAAqB,WACvB,SAASC,EAAoBR,CAAK,CAAES,CAAiB,EACnD,IAAI,CAAC,KAAK,CAAGT,EACb,IAAI,CAAC,iBAAiB,CAAGS,CAC3B,CAqEA,OApEAD,EAAoB,SAAS,CAAC,MAAM,CAAG,SAASd,CAAQ,CAAED,CAAO,CAAEK,CAAU,MACvEY,EACAC,EAYJ,GAXIb,AAAe,KAAK,IAApBA,EACFY,EAAO1H,GAAS,MAAM,CAAC0G,EAAUD,GACxBvG,GAA2B,EAAE,CAAC4G,IACvCa,EAAKb,EACLY,EAAOvH,GAAkB,MAAM,CAACuG,EAAUD,EAASK,KAEnD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,EACnDa,EAAK,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAACb,GACnCY,EAAOvH,GAAkB,MAAM,CAACuG,EAAUD,EAASkB,IAErD,IAAI,CAAC,KAAK,CAAC,IAAI,CAACD,GACZC,AAAO,KAAK,IAAZA,EACF,OAAOA,CAEX,EACAH,EAAoB,SAAS,CAAC,OAAO,CAAG,SAAS/C,CAAK,CAAEgC,CAAO,CAAEK,CAAU,MACrEY,EACAC,EAYJ,GAXIb,AAAe,KAAK,IAApBA,EACFY,EAAO1H,GAAS,OAAO,CAACyE,EAAOgC,GACtBvG,GAA2B,EAAE,CAAC4G,IACvCa,EAAKb,EACLY,EAAOvH,GAAkB,OAAO,CAACsE,EAAOgC,EAASK,KAEjD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,EACnDa,EAAK,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAACb,GACnCY,EAAOvH,GAAkB,OAAO,CAACsE,EAAOgC,EAASkB,IAEnD,IAAI,CAAC,KAAK,CAAC,IAAI,CAACD,GACZC,AAAO,KAAK,IAAZA,EACF,OAAOA,CAEX,EACAH,EAAoB,SAAS,CAAC,MAAM,CAAG,SAAS/C,CAAK,CAAEqC,CAAU,MAC3DY,EACAC,EAYJ,GAXIb,AAAe,KAAK,IAApBA,EACFY,EAAO1H,GAAS,GAAG,CAACyE,GACXvE,GAA2B,EAAE,CAAC4G,IACvCa,EAAKb,EACLY,EAAOvH,GAAkB,GAAG,CAACsE,EAAOqC,KAEpC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,EACnDa,EAAK,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAACb,GACnCY,EAAOvH,GAAkB,GAAG,CAACsE,EAAOkD,IAEtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAACD,GACZC,AAAO,KAAK,IAAZA,EACF,OAAOA,CAEX,EACAH,EAAoB,SAAS,CAAC,GAAG,CAAG,SAASE,CAAI,EAC/C,IAAI,CAAC,KAAK,CAAC,IAAI,CAACA,EAClB,EACAF,EAAoB,SAAS,CAAC,GAAG,CAAG,WAClC,OAAO,IAAI,CAAC,KAAK,AACnB,EACAA,EAAoB,SAAS,CAAC,KAAK,CAAG,WACpC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CACxC,EACAA,EAAoB,SAAS,CAAC,uBAAuB,CAAG,SAASvD,CAAK,EACpE,GAAIA,AAAU,KAAK,IAAfA,EACF,MAAM,AAAIM,MAAM,mEAEpB,EACOiD,CACT,IACII,GAAoB,WACtB,SAASC,EAAmBC,CAAW,EACrC,IAAI,CAAC,YAAY,CAAGA,AAAgB,KAAK,IAArBA,EAAyCpF,OAAO,MAAM,CAAC,MAAQoF,EACnF,IAAI,CAAC,QAAQ,CAAG,EAChB,IAAI,CAAC,KAAK,CAAG,CACf,CAiCA,OAhCAD,EAAmB,SAAS,CAAC,GAAG,CAAG,WACjC,OAAO,IAAI,CAAC,YAAY,AAC1B,EACAnF,OAAO,cAAc,CAACmF,EAAmB,SAAS,CAAE,OAAQ,CAC1D,IAAK,WACH,OAAO,IAAI,CAAC,KAAK,AACnB,EACA,WAAY,GACZ,aAAc,EAChB,GACAA,EAAmB,SAAS,CAAC,MAAM,CAAG,SAASE,CAAc,CAAEjB,CAAU,EACvE,IAAIa,EAOJ,GANIzH,GAA2B,EAAE,CAAC6H,GAChCJ,EAAKI,GAELJ,EAAK,IAAI,CAAC,MAAM,GAChBb,EAAaiB,GAEX,AAA0B,KAAK,IAA/B,IAAI,CAAC,YAAY,CAACJ,EAAG,CACvB,MAAM,AAAIpD,MAAM,MAAQoD,EAAK,uBAE/B,GAAIb,AAAe,KAAK,IAApBA,EACF,MAAM,AAAIvC,MAAM,iCAAmCoD,GAIrD,OAFA,IAAI,CAAC,YAAY,CAACA,EAAG,CAAGb,EACxB,IAAI,CAAC,KAAK,GACHa,CACT,EACAE,EAAmB,SAAS,CAAC,MAAM,CAAG,WAEpC,OADA,IAAI,CAAC,QAAQ,GACN,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAC/B,EACOA,CACT,KACsB,WACpB,SAASG,EAAiBC,CAAa,EACrC,IAAIC,EAAQ,IAAI,AAChB,KAAI,CAAC,gBAAgB,CAAmBxF,OAAO,MAAM,CAAC,MAClDuF,AAAkB,KAAK,IAAvBA,GACF,IAAI,CAAC,cAAc,CAAGA,EAClBA,EAAc,eAAe,EAC/B,IAAI,CAAC,kBAAkB,CAAG,IAAIL,GAAkBK,EAAc,iBAAiB,EAC/EA,EAAc,iBAAiB,CAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAC7DA,EAAc,eAAe,CAAC,OAAO,CAAC,SAASX,CAAM,EACnD,GAAIlH,GAAiB,EAAE,CAACkH,GAAS,CAC/B,IAAIa,EAAiB,IAAIZ,GAAmBD,EAAO,KAAK,CAAEY,EAAM,kBAAkB,CAClFA,CAAAA,EAAM,gBAAgB,CAACZ,EAAO,YAAY,CAAC,GAAG,CAAC,CAAGa,CACpD,CACF,IACSF,EAAc,OAAO,EAC9BvF,OAAO,IAAI,CAACuF,EAAc,OAAO,EAAE,OAAO,CAAC,SAAS9E,CAAG,EACrD,IAAIgF,EAAiB,IAAIZ,GAAmBU,EAAc,OAAO,CAAC9E,EAAI,CACtE+E,CAAAA,EAAM,gBAAgB,CAAC/E,EAAI,CAAGgF,CAChC,IAGF,IAAI,CAAC,cAAc,CAAG,CAAC,CAE3B,CACAzF,OAAO,cAAc,CAACsF,EAAiB,SAAS,CAAE,OAAQ,CACxD,IAAK,WASH,OARA,IAAI,CAAC,mBAAmB,GACQ,KAAK,IAAjC,IAAI,CAAC,kBAAkB,GACrB,AAAiC,IAAjC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAC9B,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAG,KAAK,EAE7C,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,IAGhE,IAAI,CAAC,cAAc,AAC5B,EACA,WAAY,GACZ,aAAc,EAChB,GACAA,EAAiB,SAAS,CAAC,iBAAiB,CAAG,SAAS7E,CAAG,EACzD,GAAIxC,GAAwC,EAAE,CAACwC,GAAM,CAEnD,GADA,IAAI,CAAC,mBAAmB,GACpB,AAAwC,KAAK,IAA7C,IAAI,CAAC,cAAc,CAAC,eAAe,CACrC,MAAM,AAAIoB,MAAM,0DAElB,IAAIwC,EAAe,CAAE,IAAK5D,EAAI,GAAG,CAAE,QAASA,EAAI,OAAO,AAAC,EACpDwC,EAAS,IAAI,CAAC,gBAAgB,CAACoB,EAAa,GAAG,CAAC,CACpD,GAAI,CAACpB,EAAQ,CACX,IAAIqB,EAAQ,EAAE,CACVoB,EAAmB,CACrBrB,aAAAA,EACAC,MAAAA,CACF,EACA,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAACoB,GACzCzC,EAAS,IAAI4B,GAAmBP,EAAO,IAAI,CAAC,kBAAkB,EAC9D,IAAI,CAAC,gBAAgB,CAACD,EAAa,GAAG,CAAC,CAAGpB,CAC5C,CACA,OAAOA,CACT,CAEE,GADA,IAAI,CAAC,WAAW,GACZ,AAAgC,KAAK,IAArC,IAAI,CAAC,cAAc,CAAC,OAAO,CAC7B,MAAM,AAAIpB,MAAM,kEAElB,IAAIoB,EAAS,IAAI,CAAC,gBAAgB,CAACxC,EAAI,CACvC,GAAI,CAACwC,EAAQ,CACX,IAAIqB,EAAQ,EAAE,AACd,KAAI,CAAC,cAAc,CAAC,OAAO,CAAC7D,EAAI,CAAG6D,EACnCrB,EAAS,IAAI4B,GAAmBP,GAChC,IAAI,CAAC,gBAAgB,CAAC7D,EAAI,CAAGwC,CAC/B,CACA,OAAOA,CAEX,EACAqC,EAAiB,SAAS,CAAC,mBAAmB,CAAG,WACH,KAAK,IAA7C,IAAI,CAAC,cAAc,CAAC,eAAe,EAAe,AAAgC,KAAK,IAArC,IAAI,CAAC,cAAc,CAAC,OAAO,GAC/E,IAAI,CAAC,kBAAkB,CAAG,IAAIJ,GAC9B,IAAI,CAAC,cAAc,CAAC,eAAe,CAAG,EAAE,CACxC,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAEvE,EACAI,EAAiB,SAAS,CAAC,WAAW,CAAG,WACK,KAAK,IAA7C,IAAI,CAAC,cAAc,CAAC,eAAe,EAAe,AAAgC,KAAK,IAArC,IAAI,CAAC,cAAc,CAAC,OAAO,EAC/E,KAAI,CAAC,cAAc,CAAC,OAAO,CAAmBtF,OAAO,MAAM,CAAC,KAAI,CAEpE,EACAsF,EAAiB,SAAS,CAAC,UAAU,CAAG,SAASxD,CAAG,CAAE6D,CAAmB,CAAEnB,CAAO,MAK5EJ,EAMAwB,EACAX,EAVJ,GADA,IAAI,CAAC,mBAAmB,GACpB,AAAwC,KAAK,IAA7C,IAAI,CAAC,cAAc,CAAC,eAAe,CACrC,MAAM,AAAIpD,MAAM,0DAiBlB,GAdItE,GAAiB,EAAE,CAACoI,IAAwBnI,GAA2B,EAAE,CAACmI,GAC5EvB,EAAauB,EAEbnB,EAAUmB,EAIRvB,AAAe,KAAK,IAApBA,EACFwB,EAAYjI,GAAW,MAAM,CAACmE,EAAK0C,IAEnCS,EAAKzH,GAA2B,EAAE,CAAC4G,GAAcA,EAAa,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAACA,GAC7FwB,EAAYjI,GAAW,MAAM,CAACmE,EAAK0C,EAASS,IAE9C,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAACW,GACrCX,AAAO,KAAK,IAAZA,EACF,OAAOA,CAEX,EACAK,EAAiB,SAAS,CAAC,UAAU,CAAG,SAASb,CAAM,CAAEC,CAAM,CAAEiB,CAAmB,CAAEnB,CAAO,MAKvFJ,EAMAwB,EACAX,EAVJ,GADA,IAAI,CAAC,mBAAmB,GACpB,AAAwC,KAAK,IAA7C,IAAI,CAAC,cAAc,CAAC,eAAe,CACrC,MAAM,AAAIpD,MAAM,0DAiBlB,GAdItE,GAAiB,EAAE,CAACoI,IAAwBnI,GAA2B,EAAE,CAACmI,GAC5EvB,EAAauB,EAEbnB,EAAUmB,EAIRvB,AAAe,KAAK,IAApBA,EACFwB,EAAYhI,GAAW,MAAM,CAAC6G,EAAQC,EAAQF,IAE9CS,EAAKzH,GAA2B,EAAE,CAAC4G,GAAcA,EAAa,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAACA,GAC7FwB,EAAYhI,GAAW,MAAM,CAAC6G,EAAQC,EAAQF,EAASS,IAEzD,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAACW,GACrCX,AAAO,KAAK,IAAZA,EACF,OAAOA,CAEX,EACAK,EAAiB,SAAS,CAAC,UAAU,CAAG,SAASxD,CAAG,CAAE6D,CAAmB,CAAEnB,CAAO,MAK5EJ,EAMAwB,EACAX,EAVJ,GADA,IAAI,CAAC,mBAAmB,GACpB,AAAwC,KAAK,IAA7C,IAAI,CAAC,cAAc,CAAC,eAAe,CACrC,MAAM,AAAIpD,MAAM,0DAiBlB,GAdItE,GAAiB,EAAE,CAACoI,IAAwBnI,GAA2B,EAAE,CAACmI,GAC5EvB,EAAauB,EAEbnB,EAAUmB,EAIRvB,AAAe,KAAK,IAApBA,EACFwB,EAAY/H,GAAW,MAAM,CAACiE,EAAK0C,IAEnCS,EAAKzH,GAA2B,EAAE,CAAC4G,GAAcA,EAAa,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAACA,GAC7FwB,EAAY/H,GAAW,MAAM,CAACiE,EAAK0C,EAASS,IAE9C,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAACW,GACrCX,AAAO,KAAK,IAAZA,EACF,OAAOA,CAEX,CAEF,IAMEnK,CAJQA,EAUPiD,IAA2BA,CAAAA,GAAyB,CAAC,IAN9B,MAAM,CAH9B,SAAgB+D,CAAG,EACjB,MAAO,CAAEA,IAAAA,CAAI,CACf,EAMAhH,EAAwB,EAAE,CAJ1B,SAAYyG,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgBzB,GAAG,MAAM,CAAC0B,AAD1BD,EACoC,GAAG,CACzD,EAQAxG,CAJQA,EAUPiD,IAAoCA,CAAAA,GAAkC,CAAC,IANvC,MAAM,CAHvC,SAAgB8D,CAAG,CAAE+D,CAAO,EAC1B,MAAO,CAAE/D,IAAAA,EAAK+D,QAAAA,CAAQ,CACxB,EAMA9K,EAAiC,EAAE,CAJnC,SAAYwG,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgBzB,GAAG,MAAM,CAAC0B,AAD1BD,EACoC,GAAG,GAAKzB,GAAG,OAAO,CAAC0B,AADvDD,EACiE,OAAO,CAC1F,EAQAvG,CAJQA,EAUPiD,IAA4CA,CAAAA,GAA0C,CAAC,IAN/C,MAAM,CAH/C,SAAgB6D,CAAG,CAAE+D,CAAO,EAC1B,MAAO,CAAE/D,IAAAA,EAAK+D,QAAAA,CAAQ,CACxB,EAMA7K,EAAyC,EAAE,CAJ3C,SAAYuG,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgBzB,GAAG,MAAM,CAAC0B,AAD1BD,EACoC,GAAG,GAAMC,CAAAA,AAAsB,OAAtBA,AAD7CD,EACuD,OAAO,EAAazB,GAAG,OAAO,CAAC0B,AADtFD,EACgG,OAAO,EACzH,EAQAtG,CAJQA,EAUPiD,IAAqBA,CAAAA,GAAmB,CAAC,IANxB,MAAM,CAHxB,SAAgB4D,CAAG,CAAEgE,CAAU,CAAED,CAAO,CAAEE,CAAI,EAC5C,MAAO,CAAEjE,IAAAA,EAAKgE,WAAAA,EAAYD,QAAAA,EAASE,KAAAA,CAAK,CAC1C,EAMA9K,EAAkB,EAAE,CAJpB,SAAYsG,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgBzB,GAAG,MAAM,CAAC0B,AAD1BD,EACoC,GAAG,GAAKzB,GAAG,MAAM,CAAC0B,AADtDD,EACgE,UAAU,GAAKzB,GAAG,OAAO,CAAC0B,AAD1FD,EACoG,OAAO,GAAKzB,GAAG,MAAM,CAAC0B,AAD1HD,EACoI,IAAI,CAC1J,EAKArG,CADQA,EAGPiD,IAAeA,CAAAA,GAAa,CAAC,IAFlB,SAAS,CAAG,YACxBjD,EAAY,QAAQ,CAAG,WAOvBA,CALQA,EAMPiD,IAAeA,CAAAA,GAAa,CAAC,IADlB,EAAE,CAJd,SAAYoD,CAAK,EAEf,OAAOC,AADSD,IACKrG,EAAY,SAAS,EAAIsG,AAD9BD,IAC4CrG,EAAY,QAAQ,AAClF,EASA8K,AACC5H,CAAAA,IAAkBA,CAAAA,GAAgB,CAAC,EAAC,EADtB,EAAE,CAJjB,SAAYmD,CAAK,EAEf,OAAOzB,GAAG,aAAa,CAACyB,IAAUpD,GAAW,EAAE,CAACqD,AADhCD,EAC0C,IAAI,GAAKzB,GAAG,MAAM,CAAC0B,AAD7DD,EACuE,KAAK,CAC9F,EAKApG,CADQA,EA0BPkD,IAAuBA,CAAAA,GAAqB,CAAC,IAzB1B,IAAI,CAAG,EAC3BlD,EAAoB,MAAM,CAAG,EAC7BA,EAAoB,QAAQ,CAAG,EAC/BA,EAAoB,WAAW,CAAG,EAClCA,EAAoB,KAAK,CAAG,EAC5BA,EAAoB,QAAQ,CAAG,EAC/BA,EAAoB,KAAK,CAAG,EAC5BA,EAAoB,SAAS,CAAG,EAChCA,EAAoB,MAAM,CAAG,EAC7BA,EAAoB,QAAQ,CAAG,GAC/BA,EAAoB,IAAI,CAAG,GAC3BA,EAAoB,KAAK,CAAG,GAC5BA,EAAoB,IAAI,CAAG,GAC3BA,EAAoB,OAAO,CAAG,GAC9BA,EAAoB,OAAO,CAAG,GAC9BA,EAAoB,KAAK,CAAG,GAC5BA,EAAoB,IAAI,CAAG,GAC3BA,EAAoB,SAAS,CAAG,GAChCA,EAAoB,MAAM,CAAG,GAC7BA,EAAoB,UAAU,CAAG,GACjCA,EAAoB,QAAQ,CAAG,GAC/BA,EAAoB,MAAM,CAAG,GAC7BA,EAAoB,KAAK,CAAG,GAC5BA,EAAoB,QAAQ,CAAG,GAC/BA,EAAoB,aAAa,CAAG,GAIpCC,CADQA,EAGPkD,IAAqBA,CAAAA,GAAmB,CAAC,IAFxB,SAAS,CAAG,EAC9BlD,EAAkB,OAAO,CAAG,EAI5B6K,AACC1H,CAAAA,IAAsBA,CAAAA,GAAoB,CAAC,EAAC,EAD1B,UAAU,CAAG,EAOhClD,CAJQA,EAUPmD,IAAsBA,CAAAA,GAAoB,CAAC,IANzB,MAAM,CAHzB,SAAgBuF,CAAO,CAAEmC,CAAM,CAAEC,CAAO,EACtC,MAAO,CAAEpC,QAAAA,EAASmC,OAAAA,EAAQC,QAAAA,CAAQ,CACpC,EAMA9K,EAAmB,EAAE,CAJrB,SAAYkG,CAAK,EAEf,OAAOC,AADSD,GACIzB,GAAG,MAAM,CAAC0B,AADdD,EACwB,OAAO,GAAK/E,GAAM,EAAE,CAACgF,AAD7CD,EACuD,MAAM,GAAK/E,GAAM,EAAE,CAACgF,AAD3ED,EACqF,OAAO,CAC9G,EAKAjG,CADQA,EAGPmD,IAAmBA,CAAAA,GAAiB,CAAC,IAFtB,IAAI,CAAG,EACvBnD,EAAgB,iBAAiB,CAAG,EAOpC8K,AACC1H,CAAAA,IAAmBA,CAAAA,GAAiB,CAAC,EAAC,EADvB,MAAM,CAHtB,SAAgB+D,CAAK,EACnB,MAAO,CAAEA,MAAAA,CAAM,CACjB,EAQA4D,AACC1H,CAAAA,IAAmBA,CAAAA,GAAiB,CAAC,EAAC,EADvB,MAAM,CAHtB,SAAgB2H,CAAK,CAAEC,CAAY,EACjC,MAAO,CAAE,MAAOD,GAAgB,EAAE,CAAE,aAAc,CAAC,CAACC,CAAa,CACnE,EAQAhL,CAJQA,EAUPqD,IAAiBA,CAAAA,GAAe,CAAC,IANpB,aAAa,CAH3B,SAAuB4H,CAAS,EAC9B,OAAOA,EAAU,OAAO,CAAC,wBAAyB,OACpD,EAMAjL,EAAc,EAAE,CAJhB,SAAYgG,CAAK,EAEf,OAAOzB,GAAG,MAAM,CADAyB,IACezB,GAAG,aAAa,CAD/ByB,IAC8CzB,GAAG,MAAM,CAAC0B,AADxDD,EACkE,QAAQ,GAAKzB,GAAG,MAAM,CAAC0B,AADzFD,EACmG,KAAK,CAC1H,EASAkF,AACC5H,CAAAA,IAAUA,CAAAA,GAAQ,CAAC,EAAC,EADd,EAAE,CAJT,SAAY0C,CAAK,EAEf,MAAO,CAAC,CADQA,GACMzB,GAAG,aAAa,CADtByB,IACsCnD,CAAAA,GAAc,EAAE,CAACoD,AADvDD,EACiE,QAAQ,GAAK3C,GAAa,EAAE,CAAC4C,AAD9FD,EACwG,QAAQ,GAAKzB,GAAG,UAAU,CAAC0B,AADnID,EAC6I,QAAQ,CAAE3C,GAAa,EAAE,IAAO2C,CAAAA,AAAgB,KAAK,IAArBA,EAAM,KAAK,EAAe/E,GAAM,EAAE,CAAC+E,EAAM,KAAK,EAC7O,EAQAmF,AACC5H,CAAAA,IAAyBA,CAAAA,GAAuB,CAAC,EAAC,EAD7B,MAAM,CAH5B,SAAgB2D,CAAK,CAAEkE,CAAa,EAClC,OAAOA,EAAgB,CAAElE,MAAAA,EAAOkE,cAAAA,CAAc,EAAI,CAAElE,MAAAA,CAAM,CAC5D,EAqBAmE,AACC7H,CAAAA,IAAyBA,CAAAA,GAAuB,CAAC,EAAC,EAD7B,MAAM,CAhB5B,SAAgB0D,CAAK,CAAEkE,CAAa,EAElC,IAAK,IADDE,EAAa,EAAE,CACVhD,EAAK,EAAGA,EAAKC,UAAU,MAAM,CAAED,IACtCgD,CAAU,CAAChD,EAAK,EAAE,CAAGC,SAAS,CAACD,EAAG,CAFpC,IAIIZ,EAAS,CAAER,MAAAA,CAAM,EASrB,OARI3C,GAAG,OAAO,CAAC6G,IACb1D,CAAAA,EAAO,aAAa,CAAG0D,CAAY,EAEjC7G,GAAG,OAAO,CAAC+G,GACb5D,EAAO,UAAU,CAAG4D,EAEpB5D,EAAO,UAAU,CAAG,EAAE,CAEjBA,CACT,EAKAzH,CADQA,EAIPwD,IAA0BA,CAAAA,GAAwB,CAAC,IAH7B,IAAI,CAAG,EAC9BxD,EAAuB,IAAI,CAAG,EAC9BA,EAAuB,KAAK,CAAG,EAW/BsL,AACC7H,CAAAA,IAAsBA,CAAAA,GAAoB,CAAC,EAAC,EAD1B,MAAM,CAPzB,SAAgB8C,CAAK,CAAEiB,CAAI,EACzB,IAAIC,EAAS,CAAElB,MAAAA,CAAM,EAIrB,OAHIjC,GAAG,MAAM,CAACkD,IACZC,CAAAA,EAAO,IAAI,CAAGD,CAAG,EAEZC,CACT,EAKAxH,CADQA,EA2BPyD,IAAeA,CAAAA,GAAa,CAAC,IA1BlB,IAAI,CAAG,EACnBzD,EAAY,MAAM,CAAG,EACrBA,EAAY,SAAS,CAAG,EACxBA,EAAY,OAAO,CAAG,EACtBA,EAAY,KAAK,CAAG,EACpBA,EAAY,MAAM,CAAG,EACrBA,EAAY,QAAQ,CAAG,EACvBA,EAAY,KAAK,CAAG,EACpBA,EAAY,WAAW,CAAG,EAC1BA,EAAY,IAAI,CAAG,GACnBA,EAAY,SAAS,CAAG,GACxBA,EAAY,QAAQ,CAAG,GACvBA,EAAY,QAAQ,CAAG,GACvBA,EAAY,QAAQ,CAAG,GACvBA,EAAY,MAAM,CAAG,GACrBA,EAAY,MAAM,CAAG,GACrBA,EAAY,OAAO,CAAG,GACtBA,EAAY,KAAK,CAAG,GACpBA,EAAY,MAAM,CAAG,GACrBA,EAAY,GAAG,CAAG,GAClBA,EAAY,IAAI,CAAG,GACnBA,EAAY,UAAU,CAAG,GACzBA,EAAY,MAAM,CAAG,GACrBA,EAAY,KAAK,CAAG,GACpBA,EAAY,QAAQ,CAAG,GACvBA,EAAY,aAAa,CAAG,GAI5BsL,AACC5H,CAAAA,IAAcA,CAAAA,GAAY,CAAC,EAAC,EADlB,UAAU,CAAG,EAexB6H,AACC5H,CAAAA,IAAsBA,CAAAA,GAAoB,CAAC,EAAC,EAD1B,MAAM,CAXzB,SAAgB6H,CAAI,CAAEjE,CAAI,CAAEjB,CAAK,CAAED,CAAG,CAAEoF,CAAa,EACnD,IAAIjE,EAAS,CACXgE,KAAAA,EACAjE,KAAAA,EACA,SAAU,CAAElB,IAAAA,EAAKC,MAAAA,CAAM,CACzB,EAIA,OAHImF,GACFjE,CAAAA,EAAO,aAAa,CAAGiE,CAAY,EAE9BjE,CACT,EAkBAvH,CAdQA,EAoBP2D,IAAmBA,CAAAA,GAAiB,CAAC,IANtB,MAAM,CAbtB,SAAgB4H,CAAI,CAAEE,CAAM,CAAEnE,CAAI,CAAEjB,CAAK,CAAEqF,CAAc,CAAEC,CAAQ,EACjE,IAAIpE,EAAS,CACXgE,KAAAA,EACAE,OAAAA,EACAnE,KAAAA,EACAjB,MAAAA,EACAqF,eAAAA,CACF,EAIA,OAHiB,KAAK,IAAlBC,GACFpE,CAAAA,EAAO,QAAQ,CAAGoE,CAAO,EAEpBpE,CACT,EAMAvH,EAAgB,EAAE,CAJlB,SAAY6F,CAAK,EAEf,OAAOC,AADSD,GACIzB,GAAG,MAAM,CAAC0B,AADdD,EACwB,IAAI,GAAKzB,GAAG,MAAM,CAAC0B,AAD3CD,EACqD,IAAI,GAAK/E,GAAM,EAAE,CAACgF,AADvED,EACiF,KAAK,GAAK/E,GAAM,EAAE,CAACgF,AADpGD,EAC8G,cAAc,GAAMC,CAAAA,AAAqB,KAAK,IAA1BA,AADlID,EAC4I,MAAM,EAAezB,GAAG,MAAM,CAAC0B,AAD3KD,EACqL,MAAM,IAAOC,CAAAA,AAAyB,KAAK,IAA9BA,AADlMD,EAC4M,UAAU,EAAezB,GAAG,OAAO,CAAC0B,AADhPD,EAC0P,UAAU,IAAOC,CAAAA,AAAuB,KAAK,IAA5BA,AAD3QD,EACqR,QAAQ,EAAegD,MAAM,OAAO,CAAC/C,AAD1TD,EACoU,QAAQ,IAAOC,CAAAA,AAAmB,KAAK,IAAxBA,AADnVD,EAC6V,IAAI,EAAegD,MAAM,OAAO,CAAC/C,AAD9XD,EACwY,IAAI,EAC9Z,EAKA5F,CADQA,EAUP2D,IAAmBA,CAAAA,GAAiB,CAAC,IATtB,KAAK,CAAG,GACxB3D,EAAgB,QAAQ,CAAG,WAC3BA,EAAgB,QAAQ,CAAG,WAC3BA,EAAgB,eAAe,CAAG,mBAClCA,EAAgB,cAAc,CAAG,kBACjCA,EAAgB,eAAe,CAAG,mBAClCA,EAAgB,MAAM,CAAG,SACzBA,EAAgB,qBAAqB,CAAG,yBACxCA,EAAgB,YAAY,CAAG,gBAW/BC,CARQA,EAcP2D,IAAsBA,CAAAA,GAAoB,CAAC,IANzB,MAAM,CAPzB,SAAgB+H,CAAW,CAAEC,CAAI,EAC/B,IAAItE,EAAS,CAAEqE,YAAAA,CAAY,EAI3B,aAHIC,GACFtE,CAAAA,EAAO,IAAI,CAAGsE,CAAG,EAEZtE,CACT,EAMArH,EAAmB,EAAE,CAJrB,SAAY2F,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgBzB,GAAG,UAAU,CAAC0B,AAD9BD,EACwC,WAAW,CAAEnE,GAAW,EAAE,GAAMoE,CAAAA,AAAmB,KAAK,IAAxBA,AADxED,EACkF,IAAI,EAAezB,GAAG,UAAU,CAAC0B,AADnHD,EAC6H,IAAI,CAAEzB,GAAG,MAAM,EAC9J,EAqBAjE,CAjBQA,EAuBP2D,IAAeA,CAAAA,GAAa,CAAC,IANlB,MAAM,CAhBlB,SAAgBkE,CAAK,CAAE8D,CAAmB,CAAExE,CAAI,EAC9C,IAAIC,EAAS,CAAES,MAAAA,CAAM,EACjB+D,EAAY,GAYhB,MAXI,AAA+B,UAA/B,OAAOD,GACTC,EAAY,GACZxE,EAAO,IAAI,CAAGuE,GACLnK,GAAQ,EAAE,CAACmK,GACpBvE,EAAO,OAAO,CAAGuE,EAEjBvE,EAAO,IAAI,CAAGuE,EAEZC,GAAazE,AAAS,KAAK,IAAdA,GACfC,CAAAA,EAAO,IAAI,CAAGD,CAAG,EAEZC,CACT,EAMApH,EAAY,EAAE,CAJd,SAAY0F,CAAK,EAEf,OAAOC,AADSD,GACIzB,GAAG,MAAM,CAAC0B,AADdD,EACwB,KAAK,GAAMC,CAAAA,AAA0B,KAAK,IAA/BA,AADnCD,EAC6C,WAAW,EAAezB,GAAG,UAAU,CAAC0B,AADrFD,EAC+F,WAAW,CAAEnE,GAAW,EAAE,IAAOoE,CAAAA,AAAmB,KAAK,IAAxBA,AADhID,EAC0I,IAAI,EAAezB,GAAG,MAAM,CAAC0B,AADvKD,EACiL,IAAI,IAAOC,CAAAA,AAAmB,KAAK,IAAxBA,AAD5LD,EACsM,IAAI,EAAeC,AAAsB,KAAK,IAA3BA,AADzND,EACmO,OAAO,AAAU,GAAOC,CAAAA,AAAsB,KAAK,IAA3BA,AAD3PD,EACqQ,OAAO,EAAelE,GAAQ,EAAE,CAACmE,AADtSD,EACgT,OAAO,IAAOC,CAAAA,AAA0B,KAAK,IAA/BA,AAD9TD,EACwU,WAAW,EAAezB,GAAG,OAAO,CAAC0B,AAD7WD,EACuX,WAAW,IAAOC,CAAAA,AAAmB,KAAK,IAAxBA,AADzYD,EACmZ,IAAI,EAAezD,GAAc,EAAE,CAAC0D,AADvbD,EACic,IAAI,EACvd,EAYAzF,CARQA,EAcP2D,IAAaA,CAAAA,GAAW,CAAC,IANhB,MAAM,CAPhB,SAAgBsC,CAAK,CAAE2F,CAAI,EACzB,IAAIzE,EAAS,CAAElB,MAAAA,CAAM,EAIrB,OAHIjC,GAAG,OAAO,CAAC4H,IACbzE,CAAAA,EAAO,IAAI,CAAGyE,CAAG,EAEZzE,CACT,EAMAnH,EAAU,EAAE,CAJZ,SAAYyF,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgB/E,GAAM,EAAE,CAACgF,AADzBD,EACmC,KAAK,GAAMzB,CAAAA,GAAG,SAAS,CAAC0B,AAD3DD,EACqE,OAAO,GAAKlE,GAAQ,EAAE,CAACmE,AAD5FD,EACsG,OAAO,EAC/H,EAQAxF,CAJQA,EAUP2D,IAAsBA,CAAAA,GAAoB,CAAC,IANzB,MAAM,CAHzB,SAAgBiI,CAAO,CAAEC,CAAY,EACnC,MAAO,CAAED,QAAAA,EAASC,aAAAA,CAAa,CACjC,EAMA7L,EAAmB,EAAE,CAJrB,SAAYwF,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgBzB,GAAG,QAAQ,CAAC0B,AAD5BD,EACsC,OAAO,GAAKzB,GAAG,OAAO,CAAC0B,AAD7DD,EACuE,YAAY,CACrG,EAQAvF,CAJQA,EAUP2D,IAAiBA,CAAAA,GAAe,CAAC,IANpB,MAAM,CAHpB,SAAgBoC,CAAK,CAAE8F,CAAM,CAAEH,CAAI,EACjC,MAAO,CAAE3F,MAAAA,EAAO8F,OAAAA,EAAQH,KAAAA,CAAK,CAC/B,EAMA1L,EAAc,EAAE,CAJhB,SAAYuF,CAAK,EAEf,OAAOzB,GAAG,OAAO,CADDyB,IACgB/E,GAAM,EAAE,CAACgF,AADzBD,EACmC,KAAK,GAAMzB,CAAAA,GAAG,SAAS,CAAC0B,AAD3DD,EACqE,MAAM,GAAKzB,GAAG,MAAM,CAAC0B,AAD1FD,EACoG,MAAM,EAC5H,EAQAtF,CAJQA,EAUP2D,IAAmBA,CAAAA,GAAiB,CAAC,IANtB,MAAM,CAHtB,SAAgBmC,CAAK,CAAE+F,CAAM,EAC3B,MAAO,CAAE/F,MAAAA,EAAO+F,OAAAA,CAAO,CACzB,EAMA7L,EAAgB,EAAE,CAJlB,SAAYsF,CAAK,EAEf,OAAOC,AAAc,KAAK,IADVD,GACe/E,GAAM,EAAE,CAACgF,AADxBD,EACkC,KAAK,GAAMC,CAAAA,AAAqB,KAAK,IAA1BA,AAD7CD,EACuD,MAAM,EAAetF,EAAgB,EAAE,CAACuF,AAD/FD,EACyG,MAAM,EACjI,EAQArF,CAJQA,EA8DP2D,IAAiBA,CAAAA,GAAe,CAAC,IA1DpB,MAAM,CAHpB,SAAgBiC,CAAG,CAAEgE,CAAU,CAAED,CAAO,CAAEkC,CAAO,EAC/C,OAAO,IAAIC,GAAiBlG,EAAKgE,EAAYD,EAASkC,EACxD,EAMA7L,EAAc,EAAE,CAJhB,SAAYqF,CAAK,EAEf,QAAOzB,CAAAA,GAAG,OAAO,CADDyB,IACgBzB,GAAG,MAAM,CAAC0B,AAD1BD,EACoC,GAAG,GAAMzB,CAAAA,GAAG,SAAS,CAAC0B,AAD1DD,EACoE,UAAU,GAAKzB,GAAG,MAAM,CAAC0B,AAD7FD,EACuG,UAAU,IAAMzB,GAAG,QAAQ,CAAC0B,AADnID,EAC6I,SAAS,GAAKzB,GAAG,IAAI,CAAC0B,AADnKD,EAC6K,OAAO,GAAKzB,GAAG,IAAI,CAAC0B,AADjMD,EAC2M,UAAU,GAAKzB,GAAG,IAAI,CAAC0B,AADlOD,EAC4O,QAAQ,EACtQ,EAyBArF,EAAc,UAAU,CAvBxB,SAAoB+L,CAAQ,CAAE3D,CAAK,EAUjC,IAAK,IATDyB,EAAOkC,EAAS,OAAO,GACvBC,EAAcC,AAsBpB,SAASA,EAAUT,CAAI,CAAEU,CAAO,EAC9B,GAAIV,EAAK,MAAM,EAAI,EACjB,OAAOA,EAET,IAAIW,EAAIX,EAAK,MAAM,CAAG,EAAI,EACtBY,EAAOZ,EAAK,KAAK,CAAC,EAAGW,GACrBE,EAAQb,EAAK,KAAK,CAACW,GACvBF,EAAUG,EAAMF,GAChBD,EAAUI,EAAOH,GAIjB,IAHA,IAAII,EAAU,EACVC,EAAW,EACXC,EAAI,EACDF,EAAUF,EAAK,MAAM,EAAIG,EAAWF,EAAM,MAAM,EAEjDI,AAAO,GADDP,EAAQE,CAAI,CAACE,EAAQ,CAAED,CAAK,CAACE,EAAS,EAE9Cf,CAAI,CAACgB,IAAI,CAAGJ,CAAI,CAACE,IAAU,CAE3Bd,CAAI,CAACgB,IAAI,CAAGH,CAAK,CAACE,IAAW,CAGjC,KAAOD,EAAUF,EAAK,MAAM,EAC1BZ,CAAI,CAACgB,IAAI,CAAGJ,CAAI,CAACE,IAAU,CAE7B,KAAOC,EAAWF,EAAM,MAAM,EAC5Bb,CAAI,CAACgB,IAAI,CAAGH,CAAK,CAACE,IAAW,CAE/B,OAAOf,CACT,EAjD8BpD,EAAO,SAASsE,CAAC,CAAEC,CAAC,EAC9C,IAAIC,EAAOF,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAGC,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,QAClD,AAAIC,AAAS,IAATA,EACKF,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAGC,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAEnDC,CACT,GACIC,EAAqBhD,EAAK,MAAM,CAC3B2C,EAAIR,EAAY,MAAM,CAAG,EAAGQ,GAAK,EAAGA,IAAK,CAChD,IAAIM,EAAId,CAAW,CAACQ,EAAE,CAClBO,EAAchB,EAAS,QAAQ,CAACe,EAAE,KAAK,CAAC,KAAK,EAC7CE,EAAYjB,EAAS,QAAQ,CAACe,EAAE,KAAK,CAAC,GAAG,EAC7C,GAAIE,GAAaH,EACfhD,EAAOA,EAAK,SAAS,CAAC,EAAGkD,GAAeD,EAAE,OAAO,CAAGjD,EAAK,SAAS,CAACmD,EAAWnD,EAAK,MAAM,OAEzF,MAAM,AAAIlE,MAAM,oBAElBkH,EAAqBE,CACvB,CACA,OAAOlD,CACT,EA+BF,IAAIiC,GAAmB,WACrB,SAASmB,EAAkBrH,CAAG,CAAEgE,CAAU,CAAED,CAAO,CAAEkC,CAAO,EAC1D,IAAI,CAAC,IAAI,CAAGjG,EACZ,IAAI,CAAC,WAAW,CAAGgE,EACnB,IAAI,CAAC,QAAQ,CAAGD,EAChB,IAAI,CAAC,QAAQ,CAAGkC,EAChB,IAAI,CAAC,YAAY,CAAG,KAAK,CAC3B,CA8FA,OA7FA/H,OAAO,cAAc,CAACmJ,EAAkB,SAAS,CAAE,MAAO,CACxD,IAAK,WACH,OAAO,IAAI,CAAC,IAAI,AAClB,EACA,WAAY,GACZ,aAAc,EAChB,GACAnJ,OAAO,cAAc,CAACmJ,EAAkB,SAAS,CAAE,aAAc,CAC/D,IAAK,WACH,OAAO,IAAI,CAAC,WAAW,AACzB,EACA,WAAY,GACZ,aAAc,EAChB,GACAnJ,OAAO,cAAc,CAACmJ,EAAkB,SAAS,CAAE,UAAW,CAC5D,IAAK,WACH,OAAO,IAAI,CAAC,QAAQ,AACtB,EACA,WAAY,GACZ,aAAc,EAChB,GACAA,EAAkB,SAAS,CAAC,OAAO,CAAG,SAASpH,CAAK,EAClD,GAAIA,EAAO,CACT,IAAIqH,EAAQ,IAAI,CAAC,QAAQ,CAACrH,EAAM,KAAK,EACjCsH,EAAM,IAAI,CAAC,QAAQ,CAACtH,EAAM,GAAG,EACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAACqH,EAAOC,EACxC,CACA,OAAO,IAAI,CAAC,QAAQ,AACtB,EACAF,EAAkB,SAAS,CAAC,MAAM,CAAG,SAASG,CAAK,CAAEzD,CAAO,EAC1D,IAAI,CAAC,QAAQ,CAAGyD,EAAM,IAAI,CAC1B,IAAI,CAAC,QAAQ,CAAGzD,EAChB,IAAI,CAAC,YAAY,CAAG,KAAK,CAC3B,EACAsD,EAAkB,SAAS,CAAC,cAAc,CAAG,WAC3C,GAAI,AAAsB,KAAK,IAA3B,IAAI,CAAC,YAAY,CAAa,CAIhC,IAAK,IAHDI,EAAc,EAAE,CAChBxD,EAAO,IAAI,CAAC,QAAQ,CACpByD,EAAc,GACTd,EAAI,EAAGA,EAAI3C,EAAK,MAAM,CAAE2C,IAAK,CAChCc,IACFD,EAAY,IAAI,CAACb,GACjBc,EAAc,IAEhB,IAAIC,EAAK1D,EAAK,MAAM,CAAC2C,GACrBc,EAAcC,AAAO,OAAPA,GAAeA,AAAO,OAAPA,EAClB,OAAPA,GAAef,EAAI,EAAI3C,EAAK,MAAM,EAAIA,AAAuB,OAAvBA,EAAK,MAAM,CAAC2C,EAAI,IACxDA,GAEJ,CACIc,GAAezD,EAAK,MAAM,CAAG,GAC/BwD,EAAY,IAAI,CAACxD,EAAK,MAAM,EAE9B,IAAI,CAAC,YAAY,CAAGwD,CACtB,CACA,OAAO,IAAI,CAAC,YAAY,AAC1B,EACAJ,EAAkB,SAAS,CAAC,UAAU,CAAG,SAASO,CAAM,EACtDA,EAASC,KAAK,GAAG,CAACA,KAAK,GAAG,CAACD,EAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAG,GAC1D,IAAIH,EAAc,IAAI,CAAC,cAAc,GACjCK,EAAM,EAAGC,EAAON,EAAY,MAAM,CACtC,GAAIM,AAAS,IAATA,EACF,OAAOtN,GAAS,MAAM,CAAC,EAAGmN,GAE5B,KAAOE,EAAMC,GAAM,CACjB,IAAIC,EAAMH,KAAK,KAAK,CAAC,AAACC,CAAAA,EAAMC,CAAG,EAAK,EAChCN,CAAAA,CAAW,CAACO,EAAI,CAAGJ,EACrBG,EAAOC,EAEPF,EAAME,EAAM,CAEhB,CACA,IAAI1I,EAAOwI,EAAM,EACjB,OAAOrN,GAAS,MAAM,CAAC6E,EAAMsI,EAASH,CAAW,CAACnI,EAAK,CACzD,EACA+H,EAAkB,SAAS,CAAC,QAAQ,CAAG,SAASnF,CAAQ,EACtD,IAAIuF,EAAc,IAAI,CAAC,cAAc,GACrC,GAAIvF,EAAS,IAAI,EAAIuF,EAAY,MAAM,CACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CACtB,GAAIvF,EAAS,IAAI,CAAG,EACzB,OAAO,EAET,IAAI+F,EAAaR,CAAW,CAACvF,EAAS,IAAI,CAAC,CACvCgG,EAAiBhG,EAAS,IAAI,CAAG,EAAIuF,EAAY,MAAM,CAAGA,CAAW,CAACvF,EAAS,IAAI,CAAG,EAAE,CAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CACnH,OAAO2F,KAAK,GAAG,CAACA,KAAK,GAAG,CAACI,EAAa/F,EAAS,SAAS,CAAEgG,GAAiBD,EAC7E,EACA/J,OAAO,cAAc,CAACmJ,EAAkB,SAAS,CAAE,YAAa,CAC9D,IAAK,WACH,OAAO,IAAI,CAAC,cAAc,GAAG,MAAM,AACrC,EACA,WAAY,GACZ,aAAc,EAChB,GACOA,CACT,IAEUhN,EA8CP2D,IAAOA,CAAAA,GAAK,CAAC,GA7CV1D,EAAW4D,OAAO,SAAS,CAAC,QAAQ,CAIxC7D,EAAI,OAAO,CAHX,SAAiBoF,CAAK,EACpB,OAAO,AAAiB,SAAVA,CAChB,EAKApF,EAAI,SAAS,CAHb,SAAoBoF,CAAK,EACvB,OAAO,AAAiB,SAAVA,CAChB,EAKApF,EAAI,OAAO,CAHX,SAAiBoF,CAAK,EACpB,MAAOA,AAAU,KAAVA,GAAkBA,AAAU,KAAVA,CAC3B,EAKApF,EAAI,MAAM,CAHV,SAAgBoF,CAAK,EACnB,MAAOnF,AAAyB,oBAAzBA,EAAS,IAAI,CAACmF,EACvB,EAKApF,EAAI,MAAM,CAHV,SAAgBoF,CAAK,EACnB,MAAOnF,AAAyB,oBAAzBA,EAAS,IAAI,CAACmF,EACvB,EAKApF,EAAI,WAAW,CAHf,SAAqBoF,CAAK,CAAE0I,CAAG,CAAEC,CAAG,EAClC,MAAO9N,AAAyB,oBAAzBA,EAAS,IAAI,CAACmF,IAAgC0I,GAAO1I,GAASA,GAAS2I,CAChF,EAKA/N,EAAI,OAAO,CAHX,SAAkBoF,CAAK,EACrB,MAAOnF,AAAyB,oBAAzBA,EAAS,IAAI,CAACmF,IAAgC,aAAeA,GAASA,GAAS,UACxF,EAKApF,EAAI,QAAQ,CAHZ,SAAmBoF,CAAK,EACtB,MAAOnF,AAAyB,oBAAzBA,EAAS,IAAI,CAACmF,IAAgC,GAAKA,GAASA,GAAS,UAC9E,EAKApF,EAAI,IAAI,CAHR,SAAcoF,CAAK,EACjB,MAAOnF,AAAyB,sBAAzBA,EAAS,IAAI,CAACmF,EACvB,EAKApF,EAAI,aAAa,CAHjB,SAAuBoF,CAAK,EAC1B,OAAOA,AAAU,OAAVA,GAAkB,AAAiB,UAAjB,OAAOA,CAClC,EAKApF,EAAI,UAAU,CAHd,SAAoBoF,CAAK,CAAE4I,CAAK,EAC9B,OAAO5F,MAAM,OAAO,CAAChD,IAAUA,EAAM,KAAK,CAAC4I,EAC7C,EAKF,IAAIC,GAAqB,MACvB,YAAYC,CAAW,CAAEC,CAAO,CAAEC,CAAiB,CAAE,CACnD,IAAI,CAAC,WAAW,CAAGF,EACnB,IAAI,CAAC,OAAO,CAAGC,EACf,IAAME,EAAa,AAACC,IAClB,IAIIC,EAJAC,EAASF,EAAM,aAAa,GAChC,GAAIE,IAAW,IAAI,CAAC,WAAW,CAI/B,IAAI,CAAC,SAAS,CAACF,EAAM,GAAG,CAAC,QAAQ,GAAG,CAAGA,EAAM,kBAAkB,CAAC,KAC9D5J,OAAO,YAAY,CAAC6J,GACpBA,EAAS7J,OAAO,UAAU,CAAC,IAAM,IAAI,CAAC,WAAW,CAAC4J,EAAM,GAAG,CAAEE,GAAS,IACxE,GACA,IAAI,CAAC,WAAW,CAACF,EAAM,GAAG,CAAEE,EAC9B,EACMC,EAAiB,AAACH,IACtB/J,GAA2B,MAAM,CAAC,eAAe,CAAC+J,EAAO,IAAI,CAAC,WAAW,CAAE,EAAE,EAC7E,IAAII,EAASJ,EAAM,GAAG,CAAC,QAAQ,GAC3BK,EAAW,IAAI,CAAC,SAAS,CAACD,EAAO,CACjCC,IACFA,EAAS,OAAO,GAChB,OAAO,IAAI,CAAC,SAAS,CAACD,EAAO,CAEjC,EACA,IAAI,CAAC,YAAY,CAAC,IAAI,CAACnK,GAA2B,MAAM,CAAC,gBAAgB,CAAC8J,IAC1E,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC9J,GAA2B,MAAM,CAAC,kBAAkB,CAACkK,IAC5E,IAAI,CAAC,YAAY,CAAC,IAAI,CAAClK,GAA2B,MAAM,CAAC,wBAAwB,CAAC,AAAC4I,IACjFsB,EAAetB,EAAM,KAAK,EAC1BkB,EAAWlB,EAAM,KAAK,CACxB,IACA,IAAI,CAAC,YAAY,CAAC,IAAI,CAACiB,EAAkB,AAACpJ,IACxCT,GAA2B,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,AAAC+J,IACjDA,EAAM,aAAa,KAAO,IAAI,CAAC,WAAW,GAC5CG,EAAeH,GACfD,EAAWC,GAEf,EACF,IACA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CACrB,QAAS,KAEP,IAAK,IAAIhK,KADTC,GAA2B,MAAM,CAAC,SAAS,GAAG,OAAO,CAACkK,GACtC,IAAI,CAAC,SAAS,CAC5B,IAAI,CAAC,SAAS,CAACnK,EAAI,CAAC,OAAO,EAE/B,CACF,GACAC,GAA2B,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC8J,EACxD,CACA,aAAe,EAAE,AAAC,AAClB,WAA4BxK,OAAO,MAAM,CAAC,KAAM,AAChD,UAAU,CACR,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,AAAC+K,GAAMA,GAAKA,EAAE,OAAO,IAC/C,IAAI,CAAC,YAAY,CAAC,MAAM,CAAG,CAC7B,CACA,YAAYC,CAAQ,CAAElF,CAAU,CAAE,CAChC,IAAI,CAAC,OAAO,CAACkF,GAAU,IAAI,CAAC,AAACC,GACpBA,EAAO,YAAY,CAACD,EAAS,QAAQ,KAC3C,IAAI,CAAC,AAAC1D,IACP,IAAM4D,EAAU5D,EAAY,GAAG,CAAC,AAACyD,GAAMI,AAwB7C,UAAuBH,CAAQ,CAAEI,CAAI,EACnC,IAAI9H,EAAO,AAAqB,UAArB,OAAO8H,EAAK,IAAI,CAAgBC,OAAOD,EAAK,IAAI,EAAIA,EAAK,IAAI,CACxE,MAAO,CACL,SAAUE,AAjBd,SAAoBC,CAAU,EAC5B,OAAQA,GACN,KAAKtO,GAAmB,KAAK,CAC3B,OAAOyD,GAA2B,cAAc,CAAC,KAAK,AACxD,MAAKzD,GAAmB,OAAO,CAC7B,OAAOyD,GAA2B,cAAc,CAAC,OAAO,AAC1D,MAAKzD,GAAmB,WAAW,CACjC,OAAOyD,GAA2B,cAAc,CAAC,IAAI,AACvD,MAAKzD,GAAmB,IAAI,CAC1B,OAAOyD,GAA2B,cAAc,CAAC,IAAI,AACvD,SACE,OAAOA,GAA2B,cAAc,CAAC,IAAI,AACzD,CACF,EAIyB0K,EAAK,QAAQ,EAClC,gBAAiBA,EAAK,KAAK,CAAC,KAAK,CAAC,IAAI,CAAG,EACzC,YAAaA,EAAK,KAAK,CAAC,KAAK,CAAC,SAAS,CAAG,EAC1C,cAAeA,EAAK,KAAK,CAAC,GAAG,CAAC,IAAI,CAAG,EACrC,UAAWA,EAAK,KAAK,CAAC,GAAG,CAAC,SAAS,CAAG,EACtC,QAASA,EAAK,OAAO,CACrB9H,KAAAA,EACA,OAAQ8H,EAAK,MAAM,AACrB,CACF,GApC2DJ,EAAUD,IAC3DN,EAAQ/J,GAA2B,MAAM,CAAC,QAAQ,CAACsK,GACnDP,GAASA,EAAM,aAAa,KAAO3E,GACrCpF,GAA2B,MAAM,CAAC,eAAe,CAAC+J,EAAO3E,EAAYoF,EAEzE,GAAG,IAAI,CAAC,KAAK,EAAG,AAACM,IACfC,QAAQ,KAAK,CAACD,EAChB,EACF,CACF,EA4BIE,GAAoB,MACtB,YAAYpB,CAAO,CAAEqB,CAAkB,CAAE,CACvC,IAAI,CAAC,OAAO,CAAGrB,EACf,IAAI,CAAC,kBAAkB,CAAGqB,CAC5B,CACA,IAAI,mBAAoB,CACtB,OAAO,IAAI,CAAC,kBAAkB,AAChC,CACA,uBAAuBlB,CAAK,CAAEzG,CAAQ,CAAE4H,CAAO,CAAEC,CAAK,CAAE,CACtD,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAC3BA,EAAO,UAAU,CAACD,EAAS,QAAQ,GAAIc,GAAa9H,KAC1D,IAAI,CAAC,AAAC+H,IACP,GAAI,CAACA,EACH,OAEF,IAAMC,EAAWvB,EAAM,oBAAoB,CAACzG,GACtCiI,EAAY,IAAIvL,GAA2B,KAAK,CAACsD,EAAS,UAAU,CAAEgI,EAAS,WAAW,CAAEhI,EAAS,UAAU,CAAEgI,EAAS,SAAS,EACnI1F,EAAQyF,EAAK,KAAK,CAAC,GAAG,CAAC,AAACG,IAC5B,IAAMC,EAAO,CACX,MAAOD,EAAM,KAAK,CAClB,WAAYA,EAAM,UAAU,EAAIA,EAAM,KAAK,CAC3C,SAAUA,EAAM,QAAQ,CACxB,WAAYA,EAAM,UAAU,CAC5B,cAAeA,EAAM,aAAa,CAClC,OAAQA,EAAM,MAAM,CACpB,QAASE,AA4GnB,SAAmBC,CAAC,EAClB,OAAOA,GAAKA,AAAc,iCAAdA,EAAE,OAAO,CAAsC,CAAE,GAAIA,EAAE,OAAO,CAAE,MAAOA,EAAE,KAAK,CAAE,UAAWA,EAAE,SAAS,AAAC,EAAI,KAAK,CAC9H,EA9G6BH,EAAM,OAAO,EAChC,MAAOD,EACP,KAAMK,AAuDhB,SAA8BtJ,CAAI,EAChC,IAAMuJ,EAAY7L,GAA2B,SAAS,CAAC,kBAAkB,CACzE,OAAQsC,GACN,KAAK3E,GAAmB,IAAI,CAC1B,OAAOkO,EAAU,IAAI,AACvB,MAAKlO,GAAmB,MAAM,CAC5B,OAAOkO,EAAU,MAAM,AACzB,MAAKlO,GAAmB,QAAQ,CAC9B,OAAOkO,EAAU,QAAQ,AAC3B,MAAKlO,GAAmB,WAAW,CACjC,OAAOkO,EAAU,WAAW,AAC9B,MAAKlO,GAAmB,KAAK,CAC3B,OAAOkO,EAAU,KAAK,AACxB,MAAKlO,GAAmB,QAAQ,CAC9B,OAAOkO,EAAU,QAAQ,AAC3B,MAAKlO,GAAmB,KAAK,CAC3B,OAAOkO,EAAU,KAAK,AACxB,MAAKlO,GAAmB,SAAS,CAC/B,OAAOkO,EAAU,SAAS,AAC5B,MAAKlO,GAAmB,MAAM,CAC5B,OAAOkO,EAAU,MAAM,AACzB,MAAKlO,GAAmB,QAAQ,CAC9B,KACF,MAAKA,GAAmB,IAAI,CAC1B,OAAOkO,EAAU,IAAI,AACvB,MAAKlO,GAAmB,KAAK,CAC3B,OAAOkO,EAAU,KAAK,AACxB,MAAKlO,GAAmB,IAAI,CAC1B,OAAOkO,EAAU,IAAI,AACvB,MAAKlO,GAAmB,OAAO,CAC7B,OAAOkO,EAAU,OAAO,AAC1B,MAAKlO,GAAmB,OAAO,CAC7B,OAAOkO,EAAU,OAAO,AAC1B,MAAKlO,GAAmB,KAAK,CAC3B,OAAOkO,EAAU,KAAK,AACxB,MAAKlO,GAAmB,IAAI,CAC1B,OAAOkO,EAAU,IAAI,AACvB,MAAKlO,GAAmB,SAAS,CAC/B,OAAOkO,EAAU,SAAS,AAC9B,CACA,OAAOA,EAAU,QAAQ,AAC3B,EAhGqCL,EAAM,IAAI,CACvC,EAkBA,OAjBIA,EAAM,QAAQ,GACZM,AAiDd,SAA6BxH,CAAI,EAC/B,OAAO,AAAuB,SAAhBA,EAAK,MAAM,EAAoB,AAAwB,SAAjBA,EAAK,OAAO,AAClE,EAnDkCkH,EAAM,QAAQ,EACpCC,EAAK,KAAK,CAAG,CACX,OAAQM,GAAQP,EAAM,QAAQ,CAAC,MAAM,EACrC,QAASO,GAAQP,EAAM,QAAQ,CAAC,OAAO,CACzC,EAEAC,EAAK,KAAK,CAAGM,GAAQP,EAAM,QAAQ,CAAC,KAAK,EAE3CC,EAAK,UAAU,CAAGD,EAAM,QAAQ,CAAC,OAAO,EAEtCA,EAAM,mBAAmB,EAC3BC,CAAAA,EAAK,mBAAmB,CAAGD,EAAM,mBAAmB,CAAC,GAAG,CAACQ,GAAU,EAEjER,EAAM,gBAAgB,GAAK5N,GAAiB,OAAO,EACrD6N,CAAAA,EAAK,eAAe,CAAGzL,GAA2B,SAAS,CAAC,4BAA4B,CAAC,eAAe,AAAD,EAElGyL,CACT,GACA,MAAO,CACL,aAAcJ,EAAK,YAAY,CAC/B,YAAazF,CACf,CACF,EACF,CACF,EACA,SAASwF,GAAa9H,CAAQ,EAC5B,IAAI,CAACA,EAGL,MAAO,CAAE,UAAWA,EAAS,MAAM,CAAG,EAAG,KAAMA,EAAS,UAAU,CAAG,CAAE,CACzE,CACA,SAAS2I,GAAU5K,CAAK,EACtB,IAAI,CAACA,EAGL,MAAO,CACL,MAAO,CACL,KAAMA,EAAM,eAAe,CAAG,EAC9B,UAAWA,EAAM,WAAW,CAAG,CACjC,EACA,IAAK,CAAE,KAAMA,EAAM,aAAa,CAAG,EAAG,UAAWA,EAAM,SAAS,CAAG,CAAE,CACvE,CACF,CACA,SAAS0K,GAAQ1K,CAAK,EACpB,IAAI,CAACA,EAGL,OAAO,IAAIrB,GAA2B,KAAK,CAACqB,EAAM,KAAK,CAAC,IAAI,CAAG,EAAGA,EAAM,KAAK,CAAC,SAAS,CAAG,EAAGA,EAAM,GAAG,CAAC,IAAI,CAAG,EAAGA,EAAM,GAAG,CAAC,SAAS,CAAG,EACzI,CA8CA,SAAS2K,GAAWhK,CAAQ,EAC1B,IAAI,CAACA,EAGL,MAAO,CACL,MAAO+J,GAAQ/J,EAAS,KAAK,EAC7B,KAAMA,EAAS,OAAO,AACxB,CACF,CAIA,IAAIkK,GAAe,MACjB,YAAYtC,CAAO,CAAE,CACnB,IAAI,CAAC,OAAO,CAAGA,CACjB,CACA,aAAaG,CAAK,CAAEzG,CAAQ,CAAE6H,CAAK,CAAE,CACnC,IAAIb,EAAWP,EAAM,GAAG,CACxB,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAC3BA,EAAO,OAAO,CAACD,EAAS,QAAQ,GAAIc,GAAa9H,KACvD,IAAI,CAAC,AAAC+H,IACP,IAAI,CAACA,EAGL,MAAO,CACL,MAAOU,GAAQV,EAAK,KAAK,EACzB,SAAUc,AA0BlB,SAA6BC,CAAQ,SACnC,AAAKA,EAGDvI,MAAM,OAAO,CAACuI,GACTA,EAAS,GAAG,CAACC,IAEf,CAACA,GAAiBD,GAAU,CALjC,MAMJ,EAlCsCf,EAAK,QAAQ,CAC7C,CACF,EACF,CACF,EAIA,SAASgB,GAAiBb,CAAK,MAHNc,EAIvB,GAAI,AAAiB,UAAjB,OAAOd,EACT,MAAO,CACL,MAAOA,CACT,EAEF,GAROc,CADgBA,EASHd,IARJ,AAAiB,UAAjB,OAAOc,GAAsB,AAAsB,UAAtB,OAAOA,EAAM,IAAI,OAS5D,AAAId,AAAe,cAAfA,EAAM,IAAI,CACL,CACL,MAAOA,EAAM,KAAK,CAAC,OAAO,CAAC,wBAAyB,OACtD,EAEK,CACL,MAAOA,EAAM,KAAK,AACpB,EAEF,MAAO,CAAE,MAAO,MAAQA,EAAM,QAAQ,CAAG,KAAOA,EAAM,KAAK,CAAG,SAAU,CAC1E,CAUA,IAAIe,GAA2B,MAC7B,YAAY3C,CAAO,CAAE,CACnB,IAAI,CAAC,OAAO,CAAGA,CACjB,CACA,0BAA0BG,CAAK,CAAEzG,CAAQ,CAAE6H,CAAK,CAAE,CAChD,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAAWA,EAAO,sBAAsB,CAACD,EAAS,QAAQ,GAAIc,GAAa9H,KAAY,IAAI,CAAC,AAACkJ,IAC/H,IAAI,CAACA,EAGL,OAAOA,EAAQ,GAAG,CAAC,AAAChB,GACX,EACL,MAAOO,GAAQP,EAAM,KAAK,EAC1B,KAAMiB,AAMhB,SAAiCnK,CAAI,EACnC,OAAQA,GACN,KAAKhE,GAAsB,IAAI,CAC7B,OAAO0B,GAA2B,SAAS,CAAC,qBAAqB,CAAC,IAAI,AACxE,MAAK1B,GAAsB,KAAK,CAC9B,OAAO0B,GAA2B,SAAS,CAAC,qBAAqB,CAAC,KAAK,AACzE,MAAK1B,GAAsB,IAAI,CAEjC,CACA,OAAO0B,GAA2B,SAAS,CAAC,qBAAqB,CAAC,IAAI,AACxE,EAhBwCwL,EAAM,IAAI,CAC1C,GAEJ,EACF,CACF,EAYIkB,GAAoB,MACtB,YAAY9C,CAAO,CAAE,CACnB,IAAI,CAAC,OAAO,CAAGA,CACjB,CACA,kBAAkBG,CAAK,CAAEzG,CAAQ,CAAE6H,CAAK,CAAE,CACxC,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAC3BA,EAAO,cAAc,CAACD,EAAS,QAAQ,GAAIc,GAAa9H,KAC9D,IAAI,CAAC,AAACqJ,IACP,IAAI,CAACA,EAGL,MAAO,CAACC,GAAWD,GAAY,AACjC,EACF,CACF,EACA,SAASC,GAAWpK,CAAQ,EAC1B,MAAO,CACL,IAAKxC,GAA2B,GAAG,CAAC,KAAK,CAACwC,EAAS,GAAG,EACtD,MAAOuJ,GAAQvJ,EAAS,KAAK,CAC/B,CACF,CACA,IAAIqK,GAAmB,MACrB,YAAYjD,CAAO,CAAE,CACnB,IAAI,CAAC,OAAO,CAAGA,CACjB,CACA,kBAAkBG,CAAK,CAAEzG,CAAQ,CAAE4H,CAAO,CAAEC,CAAK,CAAE,CACjD,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAC3BA,EAAO,cAAc,CAACD,EAAS,QAAQ,GAAIc,GAAa9H,KAC9D,IAAI,CAAC,AAACkJ,IACP,IAAI,CAACA,EAGL,OAAOA,EAAQ,GAAG,CAACI,GACrB,EACF,CACF,EACIE,GAAgB,MAClB,YAAYlD,CAAO,CAAE,CACnB,IAAI,CAAC,OAAO,CAAGA,CACjB,CACA,mBAAmBG,CAAK,CAAEzG,CAAQ,CAAEyJ,CAAO,CAAE5B,CAAK,CAAE,CAClD,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAC3BA,EAAO,QAAQ,CAACD,EAAS,QAAQ,GAAIc,GAAa9H,GAAWyJ,IACnE,IAAI,CAAC,AAACzI,GACA0I,AAIb,UAAyB1I,CAAI,EAC3B,GAAI,CAACA,GAAQ,CAACA,EAAK,OAAO,CACxB,OAEF,IAAI2I,EAAgB,EAAE,CACtB,IAAK,IAAI7L,KAAOkD,EAAK,OAAO,CAAE,CAC5B,IAAM4I,EAAOlN,GAA2B,GAAG,CAAC,KAAK,CAACoB,GAClD,IAAK,IAAIkH,KAAKhE,EAAK,OAAO,CAAClD,EAAI,CAC7B6L,EAAc,IAAI,CAAC,CACjB,SAAUC,EACV,UAAW,KAAK,EAChB,SAAU,CACR,MAAOnB,GAAQzD,EAAE,KAAK,EACtB,KAAMA,EAAE,OAAO,AACjB,CACF,EAEJ,CACA,MAAO,CACL,MAAO2E,CACT,CACF,GAzB6B3I,GAE3B,CACF,EAuBI6I,GAAwB,MAC1B,YAAYvD,CAAO,CAAE,CACnB,IAAI,CAAC,OAAO,CAAGA,CACjB,CACA,uBAAuBG,CAAK,CAAEoB,CAAK,CAAE,CACnC,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAAWA,EAAO,mBAAmB,CAACD,EAAS,QAAQ,KAAK,IAAI,CAAC,AAAC1E,IACpG,IAAI,CAACA,EAGL,OAAOA,EAAM,GAAG,CAAC,AAAC6F,GAAU,EAC1B,KAAMA,EAAK,IAAI,CACf,OAAQ,GACR,cAAeA,EAAK,aAAa,CACjC,KAAM2B,AAQd,SAAsB9K,CAAI,EACxB,IAAI+K,EAAQrN,GAA2B,SAAS,CAAC,UAAU,CAC3D,OAAQsC,GACN,KAAK9D,GAAW,IAAI,CAClB,OAAO6O,EAAM,KAAK,AACpB,MAAK7O,GAAW,MAAM,CACpB,OAAO6O,EAAM,MAAM,AACrB,MAAK7O,GAAW,SAAS,CACvB,OAAO6O,EAAM,SAAS,AACxB,MAAK7O,GAAW,OAAO,CACrB,OAAO6O,EAAM,OAAO,AACtB,MAAK7O,GAAW,KAAK,CACnB,OAAO6O,EAAM,KAAK,AACpB,MAAK7O,GAAW,MAAM,CACpB,OAAO6O,EAAM,MAAM,AACrB,MAAK7O,GAAW,QAAQ,CACtB,OAAO6O,EAAM,QAAQ,AACvB,MAAK7O,GAAW,KAAK,CACnB,OAAO6O,EAAM,KAAK,AACpB,MAAK7O,GAAW,WAAW,CACzB,OAAO6O,EAAM,WAAW,AAC1B,MAAK7O,GAAW,IAAI,CAClB,OAAO6O,EAAM,IAAI,AACnB,MAAK7O,GAAW,SAAS,CACvB,OAAO6O,EAAM,SAAS,AACxB,MAAK7O,GAAW,QAAQ,CACtB,KACF,MAAKA,GAAW,QAAQ,CACtB,OAAO6O,EAAM,QAAQ,AACvB,MAAK7O,GAAW,QAAQ,CACtB,OAAO6O,EAAM,QAAQ,AACvB,MAAK7O,GAAW,MAAM,CACpB,OAAO6O,EAAM,MAAM,AACrB,MAAK7O,GAAW,MAAM,CACpB,OAAO6O,EAAM,MAAM,AACrB,MAAK7O,GAAW,OAAO,CACrB,OAAO6O,EAAM,OAAO,AACtB,MAAK7O,GAAW,KAAK,CACnB,OAAO6O,EAAM,KAAK,AACtB,CACA,OAAOA,EAAM,QAAQ,AACvB,EAjD2B5B,EAAK,IAAI,EAC5B,MAAOM,GAAQN,EAAK,QAAQ,CAAC,KAAK,EAClC,eAAgBM,GAAQN,EAAK,QAAQ,CAAC,KAAK,EAC3C,KAAM,EAAE,AACV,GACF,EACF,CACF,EA2CI6B,GAAsB,MACxB,YAAY1D,CAAO,CAAE,CACnB,IAAI,CAAC,OAAO,CAAGA,CACjB,CACA,aAAaG,CAAK,CAAEoB,CAAK,CAAE,CACzB,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAAWA,EAAO,iBAAiB,CAACD,EAAS,QAAQ,KAAK,IAAI,CAAC,AAAC1E,IAClG,IAAI,CAACA,EAGL,MAAO,CACL,MAAOA,EAAM,GAAG,CAAC,AAAC6F,GAAU,EAC1B,MAAOM,GAAQN,EAAK,KAAK,EACzB,IAAKA,EAAK,MAAM,AAClB,GACF,CACF,EACF,CACF,EACI8B,GAAiC,MACnC,YAAY3D,CAAO,CAAE,CACnB,IAAI,CAAC,OAAO,CAAGA,CACjB,CACA,+BAA+BG,CAAK,CAAEjG,CAAO,CAAEqH,CAAK,CAAE,CACpD,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAC3BA,EAAO,MAAM,CAACD,EAAS,QAAQ,GAAI,KAAMkD,GAAsB1J,IAAU,IAAI,CAAC,AAACF,IACpF,GAAI,EAACA,GAASA,AAAiB,IAAjBA,EAAM,MAAM,CAG1B,OAAOA,EAAM,GAAG,CAACoI,GACnB,GAEJ,CACF,EACIyB,GAAsC,MACxC,YAAY7D,CAAO,CAAE,CACnB,IAAI,CAAC,OAAO,CAAGA,CACjB,CACA,wBAA0B,EAAM,AAChC,qCAAoCG,CAAK,CAAE1I,CAAK,CAAEyC,CAAO,CAAEqH,CAAK,CAAE,CAChE,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAC3BA,EAAO,MAAM,CAACD,EAAS,QAAQ,GAAI2B,GAAU5K,GAAQmM,GAAsB1J,IAAU,IAAI,CAAC,AAACF,IAChG,GAAI,EAACA,GAASA,AAAiB,IAAjBA,EAAM,MAAM,CAG1B,OAAOA,EAAM,GAAG,CAACoI,GACnB,GAEJ,CACF,EACA,SAASwB,GAAsB1J,CAAO,EACpC,MAAO,CACL,QAASA,EAAQ,OAAO,CACxB,aAAcA,EAAQ,YAAY,AACpC,CACF,CACA,IAAI4J,GAAuB,MACzB,YAAY9D,CAAO,CAAE,CACnB,IAAI,CAAC,OAAO,CAAGA,CACjB,CACA,sBAAsBG,CAAK,CAAEoB,CAAK,CAAE,CAClC,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAAWA,EAAO,kBAAkB,CAACD,EAAS,QAAQ,KAAK,IAAI,CAAC,AAACqD,IACnG,IAAI,CAACA,EAGL,OAAOA,EAAM,GAAG,CAAC,AAAClC,GAAU,EAC1B,MAAOA,EAAK,KAAK,CACjB,MAAOM,GAAQN,EAAK,KAAK,CAC3B,GACF,EACF,CACA,0BAA0B1B,CAAK,CAAEsB,CAAI,CAAEF,CAAK,CAAE,CAC5C,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAAWA,EAAO,qBAAqB,CAACD,EAAS,QAAQ,GAAIe,EAAK,KAAK,CAAEY,GAAUZ,EAAK,KAAK,IAAI,IAAI,CAAC,AAACuC,IACzI,IAAI,CAACA,EAGL,OAAOA,EAAc,GAAG,CAAC,AAACC,IACxB,IAAIpC,EAAO,CACT,MAAOoC,EAAa,KAAK,AAC3B,EAOA,OANIA,EAAa,QAAQ,EACvBpC,CAAAA,EAAK,QAAQ,CAAGO,GAAW6B,EAAa,QAAQ,GAE9CA,EAAa,mBAAmB,EAClCpC,CAAAA,EAAK,mBAAmB,CAAGoC,EAAa,mBAAmB,CAAC,GAAG,CAAC7B,GAAU,EAErEP,CACT,EACF,EACF,CACF,EACIqC,GAAsB,MACxB,YAAYlE,CAAO,CAAE,CACnB,IAAI,CAAC,OAAO,CAAGA,CACjB,CACA,qBAAqBG,CAAK,CAAEmB,CAAO,CAAEC,CAAK,CAAE,CAC1C,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAAWA,EAAO,gBAAgB,CAACD,EAAS,QAAQ,GAAIY,IAAU,IAAI,CAAC,AAAC6C,IAC1G,IAAI,CAACA,EAGL,OAAOA,EAAO,GAAG,CAAC,AAAC1M,IACjB,IAAMkB,EAAS,CACb,MAAOlB,EAAM,SAAS,CAAG,EACzB,IAAKA,EAAM,OAAO,CAAG,CACvB,EAIA,OAH0B,SAAfA,EAAM,IAAI,EACnBkB,CAAAA,EAAO,IAAI,CAAGyL,AAOxB,SAA4B1L,CAAI,EAC9B,OAAQA,GACN,KAAKlG,GAAiB,OAAO,CAC3B,OAAO4D,GAA2B,SAAS,CAAC,gBAAgB,CAAC,OAAO,AACtE,MAAK5D,GAAiB,OAAO,CAC3B,OAAO4D,GAA2B,SAAS,CAAC,gBAAgB,CAAC,OAAO,AACtE,MAAK5D,GAAiB,MAAM,CAC1B,OAAO4D,GAA2B,SAAS,CAAC,gBAAgB,CAAC,MAAM,AACvE,CAEF,EAjB2CqB,EAAM,IAAI,GAEtCkB,CACT,EACF,EACF,CACF,EAYI0L,GAAwB,MAC1B,YAAYrE,CAAO,CAAE,CACnB,IAAI,CAAC,OAAO,CAAGA,CACjB,CACA,uBAAuBG,CAAK,CAAEmE,CAAS,CAAE/C,CAAK,CAAE,CAC9C,IAAMb,EAAWP,EAAM,GAAG,CAC1B,OAAO,IAAI,CAAC,OAAO,CAACO,GAAU,IAAI,CAAC,AAACC,GAAWA,EAAO,kBAAkB,CAACD,EAAS,QAAQ,GAAI4D,EAAU,GAAG,CAAC9C,MAAgB,IAAI,CAAC,AAAC+C,IAChI,IAAI,CAACA,EAGL,OAAOA,EAAgB,GAAG,CAAC,AAACzH,IAC1B,IAAMnE,EAAS,EAAE,CACjB,KAAOmE,GACLnE,EAAO,IAAI,CAAC,CAAE,MAAOwJ,GAAQrF,EAAe,KAAK,CAAE,GACnDA,EAAiBA,EAAe,MAAM,CAExC,OAAOnE,CACT,EACF,EACF,CACF,EAGI6L,GAAwB,cAAcpD,GACxC,YAAYT,CAAM,CAAE,CAClB,KAAK,CAACA,EAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CAC9C,CACF,EACA,SAAS8D,GAAWnO,CAAQ,EAC1B,IAAMM,EAAS,IAAIP,GAAcC,GAC3BqK,EAAS,CAAC,GAAG+D,IACV9N,EAAO,wBAAwB,IAAI8N,GAExClJ,EAAalF,EAAS,UAAU,CACpCF,GAA2B,SAAS,CAAC,8BAA8B,CAACoF,EAAY,IAAIgJ,GAAsB7D,IAC1GvK,GAA2B,SAAS,CAAC,qBAAqB,CAACoF,EAAY,IAAI8G,GAAa3B,IACxFvK,GAA2B,SAAS,CAAC,iCAAiC,CAACoF,EAAY,IAAImH,GAAyBhC,IAChHvK,GAA2B,SAAS,CAAC,oBAAoB,CAACoF,EAAY,IAAIkI,GAAoB/C,IAC9FvK,GAA2B,SAAS,CAAC,4BAA4B,CAACoF,EAAY,IAAI0I,GAAoBvD,IACtGvK,GAA2B,SAAS,CAAC,8BAA8B,CAACoF,EAAY,IAAI+H,GAAsB5C,IAC1GvK,GAA2B,SAAS,CAAC,8BAA8B,CAACoF,EAAY,IAAI6I,GAAsB1D,IAC1GvK,GAA2B,SAAS,CAAC,sBAAsB,CAACoF,EAAY,IAAI0H,GAAcvC,IACvE,SAAfnF,IACFpF,GAA2B,SAAS,CAAC,sCAAsC,CAACoF,EAAY,IAAImI,GAA+BhD,IAC3HvK,GAA2B,SAAS,CAAC,2CAA2C,CAACoF,EAAY,IAAIqI,GAAoClD,IAEzI,CACA,SAASgE,GAAUrO,CAAQ,EACzB,IAAMsO,EAAc,EAAE,CAChBC,EAAY,EAAE,CACdjO,EAAS,IAAIP,GAAcC,GACjCsO,EAAY,IAAI,CAAChO,GACjB,IAAM+J,EAAS,CAAC,GAAG+D,IACV9N,EAAO,wBAAwB,IAAI8N,GAsC5C,OAFAI,AAlCA,WACE,GAAM,CAAEtJ,WAAAA,CAAU,CAAEuJ,kBAAAA,CAAiB,CAAE,CAAGzO,EAC1C0O,GAAWH,GACPE,EAAkB,eAAe,EACnCF,EAAU,IAAI,CAACzO,GAA2B,SAAS,CAAC,8BAA8B,CAACoF,EAAY,IAAIgJ,GAAsB7D,KAEvHoE,EAAkB,MAAM,EAC1BF,EAAU,IAAI,CAACzO,GAA2B,SAAS,CAAC,qBAAqB,CAACoF,EAAY,IAAI8G,GAAa3B,KAErGoE,EAAkB,kBAAkB,EACtCF,EAAU,IAAI,CAACzO,GAA2B,SAAS,CAAC,iCAAiC,CAACoF,EAAY,IAAImH,GAAyBhC,KAE7HoE,EAAkB,KAAK,EACzBF,EAAU,IAAI,CAACzO,GAA2B,SAAS,CAAC,oBAAoB,CAACoF,EAAY,IAAIkI,GAAoB/C,KAE3GoE,EAAkB,eAAe,EACnCF,EAAU,IAAI,CAACzO,GAA2B,SAAS,CAAC,8BAA8B,CAACoF,EAAY,IAAI+H,GAAsB5C,KAEvHoE,EAAkB,MAAM,EAC1BF,EAAU,IAAI,CAACzO,GAA2B,SAAS,CAAC,sBAAsB,CAACoF,EAAY,IAAI0H,GAAcvC,KAEvGoE,EAAkB,aAAa,EACjCF,EAAU,IAAI,CAACzO,GAA2B,SAAS,CAAC,4BAA4B,CAACoF,EAAY,IAAI0I,GAAoBvD,KAEnHoE,EAAkB,eAAe,EACnCF,EAAU,IAAI,CAACzO,GAA2B,SAAS,CAAC,8BAA8B,CAACoF,EAAY,IAAI6I,GAAsB1D,KAEvHoE,EAAkB,uBAAuB,EAC3CF,EAAU,IAAI,CAACzO,GAA2B,SAAS,CAAC,sCAAsC,CAACoF,EAAY,IAAImI,GAA+BhD,KAExIoE,EAAkB,4BAA4B,EAChDF,EAAU,IAAI,CAACzO,GAA2B,SAAS,CAAC,2CAA2C,CAACoF,EAAY,IAAIqI,GAAoClD,IAExJ,IAEAiE,EAAY,IAAI,CAACK,GAAaJ,IACvBI,GAAaL,EACtB,CACA,SAASK,GAAaL,CAAW,EAC/B,MAAO,CAAE,QAAS,IAAMI,GAAWJ,EAAa,CAClD,CACA,SAASI,GAAWJ,CAAW,EAC7B,KAAOA,EAAY,MAAM,EACvBA,EAAY,GAAG,GAAG,OAAO,EAE7B"}