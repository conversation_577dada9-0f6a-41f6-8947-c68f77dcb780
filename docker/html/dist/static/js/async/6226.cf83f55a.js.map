{"version": 3, "file": "static/js/async/6226.cf83f55a.js", "sources": ["webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/debug-components/debug-params.tsx", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@douyinfe+semi-icons@2.81.0_react@18.2.0/node_modules/@douyinfe/semi-icons/lib/es/icons/IconDeleteStroked.js", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/debug-components/params-form/columns/utils.ts", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/components/item-error-tip/index.module.less?7d14", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/components/item-error-tip/index.tsx", "webpack://@coze-studio/app/../../packages/studio/common/file-kit/src/accept.ts", "webpack://@coze-studio/app/../../packages/studio/common/file-kit/src/assets/file/xlsx-success.svg", "webpack://@coze-studio/app/../../packages/studio/common/file-kit/src/assets/file/docx-success.svg", "webpack://@coze-studio/app/../../packages/studio/common/file-kit/src/assets/file/image-success.svg", "webpack://@coze-studio/app/../../packages/studio/common/file-kit/src/assets/file/unknown-success.svg", "webpack://@coze-studio/app/../../packages/studio/common/file-kit/src/assets/file/audio-success.svg", "webpack://@coze-studio/app/../../packages/studio/common/file-kit/src/assets/file/code-success.svg", "webpack://@coze-studio/app/../../packages/studio/common/file-kit/src/assets/file/zip-success.svg", "webpack://@coze-studio/app/../../packages/studio/common/file-kit/src/assets/file/ppt-success.svg", "webpack://@coze-studio/app/../../packages/studio/common/file-kit/src/assets/file/video-success.svg", "webpack://@coze-studio/app/../../packages/studio/common/file-kit/src/assets/file/txt-success.svg", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/file.ts", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/components/file-upload-item/upload.tsx", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/components/file-upload-item/index.module.less?1e8a", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/components/file-upload-item/index.tsx", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/debug-components/params-form/columns/param-value-col.tsx", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/debug-components/params-form/index.tsx", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/debug-components/diy-mdbox.tsx", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/debug-components/debug-check.tsx", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/debug.tsx", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/debug-components/index.module.less?38ac", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/index.module.less?47a5", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/components/file-upload-item/index.module.less", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/components/item-error-tip/index.module.less", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/debug-components/index.module.less", "webpack://@coze-studio/app/../../packages/agent-ide/bot-plugin/tools/src/components/plugin_modal/index.module.less"], "sourcesContent": ["/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React, { useMemo, useRef, useState } from 'react';\n\nimport { withSlardarIdButton } from '@coze-studio/bot-utils';\nimport { I18n } from '@coze-arch/i18n';\nimport { UIButton, Toast } from '@coze-arch/bot-semi';\nimport {\n  DebugExampleStatus,\n  PluginType,\n  type APIParameter,\n} from '@coze-arch/bot-api/plugin_develop';\nimport { PluginDevelopApi } from '@coze-arch/bot-api';\n\nimport {\n  transformTreeToObj,\n  sleep,\n  scrollToErrorElement,\n  transformParamsToTree,\n} from '../utils';\nimport { type CheckParamsProps, STATUS } from '../types';\nimport s from '../index.module.less';\nimport ParamsForm from './params-form';\n\n/** Stringify indent */\nconst INDENTATION_SPACES = 2;\nconst SLEEP_NUM = 100;\n\nexport const DebugParams: React.FC<{\n  requestParams: APIParameter[] | undefined;\n  pluginId: string;\n  apiId: string;\n  operation?: number;\n  btnText?: string;\n  callback?: (val: CheckParamsProps) => void;\n  disabled: boolean;\n  debugExampleStatus?: DebugExampleStatus;\n  showExampleTag?: boolean;\n  pluginType?: PluginType;\n}> = ({\n  requestParams = [],\n  pluginId,\n  apiId,\n  operation = 1,\n  btnText = I18n.t('Create_newtool_s4_run'),\n  callback,\n  disabled,\n  debugExampleStatus = DebugExampleStatus.Default,\n  showExampleTag = false,\n  pluginType,\n}) => {\n  const [loading, setLoading] = useState<boolean>(false);\n  const [check, setCheck] = useState<number>(0);\n  const paramsFormRef = useRef<{ data: Array<APIParameter> }>(null);\n\n  const handleAction = async () => {\n    // Verification is required\n    setCheck(check + 1);\n    await sleep(SLEEP_NUM);\n    const errorEle = document.getElementsByClassName('errorDebugClassTag');\n    if (!apiId || errorEle.length > 0) {\n      scrollToErrorElement('.errorDebugClassTag');\n      Toast.error({\n        content: withSlardarIdButton(I18n.t('tool_new_S2_feedback_failed')),\n        duration: 3,\n        theme: 'light',\n        showClose: false,\n      });\n      return false;\n    }\n\n    let reqParams = {};\n    setLoading(true);\n    if (\n      Array.isArray(paramsFormRef.current?.data) &&\n      (paramsFormRef.current?.data || []).length > 0\n    ) {\n      reqParams = transformTreeToObj(paramsFormRef.current?.data);\n    }\n    try {\n      const resData = await PluginDevelopApi.DebugAPI({\n        plugin_id: pluginId,\n        api_id: apiId,\n        parameters: JSON.stringify(reqParams),\n        operation,\n      });\n\n      callback?.({\n        status: resData.success ? STATUS.PASS : STATUS.FAIL,\n        request: resData.raw_req,\n        response: resData.resp,\n        failReason: resData.reason,\n        response_params: resData.response_params,\n        rawResp: resData.raw_resp,\n      });\n    } catch (e) {\n      callback?.({\n        status: STATUS.FAIL,\n        request: JSON.stringify(reqParams, null, INDENTATION_SPACES),\n        response: I18n.t('plugin_exception'),\n        failReason: I18n.t('plugin_exception'),\n      });\n    }\n    setLoading(false);\n  };\n  const requestParamsData = useMemo(\n    () => transformParamsToTree(requestParams),\n    [requestParams],\n  );\n  return (\n    <div className={s['debug-params-box']}>\n      <ParamsForm\n        height={443}\n        ref={paramsFormRef}\n        requestParams={requestParamsData}\n        defaultKey=\"global_default\"\n        disabled={disabled}\n        check={check}\n        debugExampleStatus={debugExampleStatus}\n        showExampleTag={showExampleTag}\n        supportFileTypeUpload\n      />\n      {!disabled && (\n        <div className={s.runbtn}>\n          <UIButton\n            disabled={disabled || pluginType === PluginType.LOCAL}\n            style={{ width: 98 }}\n            loading={loading}\n            // theme=\"solid\"\n            type=\"tertiary\"\n            onClick={handleAction}\n          >\n            {btnText === I18n.t('Create_newtool_s3_button_auto') &&\n              (loading\n                ? I18n.t('plugin_s3_Parsing')\n                : I18n.t('Create_newtool_s3_button_auto'))}\n            {btnText === I18n.t('Create_newtool_s4_run') &&\n              (loading\n                ? I18n.t('plugin_s3_running')\n                : I18n.t('Create_newtool_s4_run'))}\n          </UIButton>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import * as React from 'react';\nimport { convertIcon } from '../components/Icon';\nfunction SvgComponent(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"1em\",\n    height: \"1em\",\n    focusable: false,\n    \"aria-hidden\": true\n  }, props), /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    clipRule: \"evenodd\",\n    d: \"M10 5V4h4v1h-4ZM8 5V3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v2h4a1 1 0 1 1 0 2h-1v14a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7H4a1 1 0 0 1 0-2h4Zm7 2H7v13h10V7h-2ZM9 9.5c0-.28.22-.5.5-.5h1c.28 0 .5.22.5.5v7a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-7Zm4 0c0-.28.22-.5.5-.5h1c.28 0 .5.22.5.5v7a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-7Z\",\n    fill: \"currentColor\"\n  }));\n}\nconst IconComponent = convertIcon(SvgComponent, 'delete_stroked');\nexport default IconComponent;", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type APIParameterRecord } from '../../../types/params';\n\nexport const getColumnClass = (record: APIParameterRecord) =>\n  record.global_disable ? 'disable' : 'normal';\n", "\n      import API from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React, { type FC } from 'react';\n\nimport cl from 'classnames';\nimport { I18n } from '@coze-arch/i18n';\n\nimport s from './index.module.less';\n\nexport const ItemErrorTip: FC<{ withDescription?: boolean; tip?: string }> = ({\n  withDescription = false,\n  tip = I18n.t('plugin_empty'),\n}) => (\n  <div className={s['check-box']}>\n    <span\n      className={cl(\n        'whitespace-nowrap',\n        s['form-check-tip'],\n        withDescription ? '!top-[16px]' : '!top-0',\n        'errorDebugClassTag',\n      )}\n    >\n      {tip}\n    </span>\n  </div>\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { I18n } from '@coze-arch/i18n';\n\nimport {\n  ZipIcon,\n  VideoIcon,\n  TextIcon as TxtIcon,\n  ImageIcon,\n  AudioIcon,\n  CodeIcon,\n  PptIcon,\n  DocxIcon as DocIcon,\n  XlsxIcon as TableIcon,\n  UnknownIcon,\n} from './icon';\nimport { FileTypeEnum } from './const';\n\nconst uploadTableConfig = {\n  label: I18n.t('shortcut_modal_upload_component_file_format_table'),\n  icon: TableIcon,\n};\n\nconst uploadDocConfig = {\n  label: I18n.t('shortcut_modal_upload_component_file_format_doc'),\n  icon: DocIcon,\n};\n\nexport const ACCEPT_UPLOAD_TYPES: Record<\n  FileTypeEnum,\n  {\n    label: string;\n    icon: string;\n  }\n> = {\n  [FileTypeEnum.IMAGE]: {\n    label: I18n.t('shortcut_modal_upload_component_file_format_img'),\n    icon: ImageIcon,\n  },\n  [FileTypeEnum.EXCEL]: uploadTableConfig,\n  [FileTypeEnum.CSV]: uploadTableConfig,\n  [FileTypeEnum.PDF]: uploadDocConfig,\n  [FileTypeEnum.DOCX]: uploadDocConfig,\n  [FileTypeEnum.DEFAULT_UNKNOWN]: {\n    label: I18n.t('plugin_file_unknown'),\n    icon: UnknownIcon,\n  },\n  [FileTypeEnum.AUDIO]: {\n    label: I18n.t('shortcut_modal_upload_component_file_format_audio'),\n    icon: AudioIcon,\n  },\n  [FileTypeEnum.CODE]: {\n    label: I18n.t('shortcut_modal_upload_component_file_format_code'),\n    icon: CodeIcon,\n  },\n  [FileTypeEnum.ARCHIVE]: {\n    label: I18n.t('shortcut_modal_upload_component_file_format_zip'),\n    icon: ZipIcon,\n  },\n  [FileTypeEnum.PPT]: {\n    label: I18n.t('shortcut_modal_upload_component_file_format_ppt'),\n    icon: PptIcon,\n  },\n  [FileTypeEnum.VIDEO]: {\n    label: I18n.t('shortcut_modal_upload_component_file_format_video'),\n    icon: VideoIcon,\n  },\n  [FileTypeEnum.TXT]: {\n    label: I18n.t('shortcut_modal_upload_component_file_format_txt'),\n    icon: TxtIcon,\n  },\n};\n", "import * as React from \"react\";\nconst SvgXlsxSuccess = props => <svg xmlns=\"http://www.w3.org/2000/svg\" width={32} height={40} fill=\"none\" viewBox=\"0 0 32 40\" {...props}><path fill=\"#32A645\" d=\"M1 5a3.333 3.333 0 0 1 3.333-3.333H20.31c.442 0 .866.175 1.178.488l9.024 9.023c.312.313.488.737.488 1.179V35a3.333 3.333 0 0 1-3.333 3.333H4.333A3.333 3.333 0 0 1 1 35z\" /><path fill=\"#258832\" d=\"M21 2.27a.25.25 0 0 1 .427-.177l9.146 9.147a.25.25 0 0 1-.177.427h-6.063A3.333 3.333 0 0 1 21 8.333z\" opacity={0.9} /><path fill=\"#fff\" d=\"M10.244 16.515h1.82c.08 0 .156.04.202.106l3.498 5.069 3.516-5.07a.25.25 0 0 1 .201-.105h1.82a.245.245 0 0 1 .2.388l-4.555 6.39 4.917 6.926a.246.246 0 0 1-.2.387h-1.82a.25.25 0 0 1-.201-.106l-3.878-5.605-3.86 5.605a.25.25 0 0 1-.202.106h-1.82a.245.245 0 0 1-.2-.387l4.88-6.927-4.518-6.39a.245.245 0 0 1 .2-.387\" /></svg>;\nexport { SvgXlsxSuccess as ReactComponent };\nexport default \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjYiIGQ9Ik0xIDQuOTk5OTZDMSAzLjE1OTAxIDIuNDkyMzggMS42NjY2MyA0LjMzMzMzIDEuNjY2NjNIMjAuMzA5NkMyMC43NTE3IDEuNjY2NjMgMjEuMTc1NiAxLjg0MjIyIDIxLjQ4ODIgMi4xNTQ3OEwzMC41MTE4IDExLjE3ODVDMzAuODI0NCAxMS40OTEgMzEgMTEuOTE1IDMxIDEyLjM1N1YzNUMzMSAzNi44NDA5IDI5LjUwNzYgMzguMzMzMyAyNy42NjY3IDM4LjMzMzNINC4zMzMzM0MyLjQ5MjM4IDM4LjMzMzMgMSAzNi44NDA5IDEgMzVWNC45OTk5NloiIGZpbGw9IiMzMkE2NDUiLz4KPHBhdGggaWQ9IlJlY3RhbmdsZSAyNTI3IiBvcGFjaXR5PSIwLjkiIGQ9Ik0yMSAyLjI3MDE4QzIxIDIuMDQ3NDUgMjEuMjY5MyAxLjkzNTkxIDIxLjQyNjggMi4wOTM0TDMwLjU3MzIgMTEuMjM5OEMzMC43MzA3IDExLjM5NzMgMzAuNjE5MiAxMS42NjY2IDMwLjM5NjQgMTEuNjY2NkgyNC4zMzMzQzIyLjQ5MjQgMTEuNjY2NiAyMSAxMC4xNzQyIDIxIDguMzMzMjlWMi4yNzAxOFoiIGZpbGw9IiMyNTg4MzIiLz4KPHBhdGggaWQ9Imljb25fZmlsZV9leGNlbF9ub3IiIGQ9Ik0xMC4yNDQzIDE2LjUxNTFIMTIuMDY0QzEyLjE0NDcgMTYuNTE1MSAxMi4yMjAyIDE2LjU1NDggMTIuMjY2IDE2LjYyMTJMMTUuNzYzNyAyMS42ODk4TDE5LjI3OTggMTYuNjIwN0MxOS4zMjU2IDE2LjU1NDYgMTkuNDAxIDE2LjUxNTEgMTkuNDgxNSAxNi41MTUxSDIxLjMwMDlDMjEuNDM2NSAxNi41MTUxIDIxLjU0NjQgMTYuNjI1IDIxLjU0NjQgMTYuNzYwNkMyMS41NDY0IDE2LjgxMTcgMjEuNTMwNCAxNi44NjE1IDIxLjUwMDggMTYuOTAzMUwxNi45NDU4IDIzLjI5MjVMMjEuODYzMiAzMC4yMTg1QzIxLjk0MTYgMzAuMzI5IDIxLjkxNTYgMzAuNDgyMyAyMS44MDUxIDMwLjU2MDdDMjEuNzYzNiAzMC41OTAyIDIxLjcxMzkgMzAuNjA2IDIxLjY2MyAzMC42MDZIMTkuODQzNEMxOS43NjI4IDMwLjYwNiAxOS42ODc0IDMwLjU2NjUgMTkuNjQxNiAzMC41MDAyTDE1Ljc2MzYgMjQuODk1NEwxMS45MDQyIDMwLjQ5OThDMTEuODU4NCAzMC41NjYzIDExLjc4MjggMzAuNjA2IDExLjcwMjEgMzAuNjA2SDkuODgyMTdDOS43NDY2MSAzMC42MDYgOS42MzY3MiAzMC40OTYyIDkuNjM2NzIgMzAuMzYwNkM5LjYzNjcyIDMwLjMxIDkuNjUyMzcgMzAuMjYwNiA5LjY4MTUzIDMwLjIxOTJMMTQuNTYyMyAyMy4yOTI1TDEwLjA0MzggMTYuOTAyM0M5Ljk2NTU4IDE2Ljc5MTYgOS45OTE4NiAxNi42Mzg0IDEwLjEwMjUgMTYuNTYwMkMxMC4xNDQgMTYuNTMwOSAxMC4xOTM1IDE2LjUxNTEgMTAuMjQ0MyAxNi41MTUxWiIgZmlsbD0id2hpdGUiLz4KPC9nPgo8L3N2Zz4K\";", "import * as React from \"react\";\nconst SvgDocxSuccess = props => <svg xmlns=\"http://www.w3.org/2000/svg\" width={32} height={40} fill=\"none\" viewBox=\"0 0 32 40\" {...props}><path fill=\"#336DF4\" d=\"M4 5c0-1.841 1.194-3.333 2.667-3.333h12.78c.354 0 .694.175.944.488l7.219 9.024c.25.312.39.736.39 1.178V35c0 1.84-1.194 3.333-2.667 3.333H6.667C5.194 38.333 4 36.841 4 35z\" /><path fill=\"#336DF4\" d=\"M1 5a3.333 3.333 0 0 1 3.333-3.333H20.31c.442 0 .866.175 1.178.488l9.024 9.024c.312.312.488.736.488 1.178V35a3.333 3.333 0 0 1-3.333 3.333H4.333A3.333 3.333 0 0 1 1 35z\" /><path fill=\"#0442D2\" d=\"M21 2.27a.25.25 0 0 1 .427-.177l9.146 9.147a.25.25 0 0 1-.177.427h-6.063A3.333 3.333 0 0 1 21 8.333z\" opacity={0.7} /><path fill=\"#fff\" d=\"M16.007 20.416 13.55 29.51a.25.25 0 0 1-.244.187h-1.448a.25.25 0 0 1-.243-.184L8.282 17.746a.253.253 0 0 1 .244-.322h1.451c.116 0 .216.078.245.19l2.372 9.172 2.462-9.174c.03-.11.13-.188.245-.188h1.414c.115 0 .215.077.245.188l2.447 9.172 2.37-9.17a.25.25 0 0 1 .246-.19h1.451a.253.253 0 0 1 .244.322l-3.333 11.767a.25.25 0 0 1-.243.184h-1.448a.25.25 0 0 1-.244-.187z\" /></svg>;\nexport { SvgDocxSuccess as ReactComponent };\nexport default \"data:image/svg+xml;base64,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\";", "import * as React from \"react\";\nconst SvgImageSuccess = props => <svg xmlns=\"http://www.w3.org/2000/svg\" width={32} height={40} fill=\"none\" viewBox=\"0 0 32 40\" {...props}><path fill=\"#FFC60A\" d=\"M1 5a3.333 3.333 0 0 1 3.333-3.333H20.31c.442 0 .866.175 1.178.488l9.024 9.023c.312.313.488.737.488 1.179V35a3.333 3.333 0 0 1-3.333 3.333H4.333A3.333 3.333 0 0 1 1 35z\" /><path fill=\"#D99904\" d=\"M21 2.27a.25.25 0 0 1 .427-.177l9.146 9.147a.25.25 0 0 1-.177.427h-6.063A3.333 3.333 0 0 1 21 8.333z\" opacity={0.8} /><g fill=\"#fff\"><path d=\"M9.953 16.667c-.92 0-1.667.746-1.667 1.666v.303c0 .92.747 1.667 1.667 1.667h.303c.92 0 1.667-.746 1.667-1.667v-.303c0-.92-.746-1.666-1.667-1.666zM23.825 21.266c.618-.667 1.734-.23 1.734.68v8.887c0 .46-.373.834-.833.834H8.335a.667.667 0 0 1-.511-1.095l4.876-5.817a1.667 1.667 0 0 1 2.554 0l2.538 3.027z\" /></g></svg>;\nexport { SvgImageSuccess as ReactComponent };\nexport default \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjgiIGQ9Ik0xIDQuOTk5OTZDMSAzLjE1OTAxIDIuNDkyMzggMS42NjY2MyA0LjMzMzMzIDEuNjY2NjNIMjAuMzA5NkMyMC43NTE3IDEuNjY2NjMgMjEuMTc1NiAxLjg0MjIyIDIxLjQ4ODIgMi4xNTQ3OEwzMC41MTE4IDExLjE3ODVDMzAuODI0NCAxMS40OTEgMzEgMTEuOTE1IDMxIDEyLjM1N1YzNUMzMSAzNi44NDA5IDI5LjUwNzYgMzguMzMzMyAyNy42NjY3IDM4LjMzMzNINC4zMzMzM0MyLjQ5MjM4IDM4LjMzMzMgMSAzNi44NDA5IDEgMzVWNC45OTk5NloiIGZpbGw9IiNGRkM2MEEiLz4KPHBhdGggaWQ9IlJlY3RhbmdsZSAyNTI5IiBvcGFjaXR5PSIwLjgiIGQ9Ik0yMSAyLjI3MDE4QzIxIDIuMDQ3NDUgMjEuMjY5MyAxLjkzNTkxIDIxLjQyNjggMi4wOTM0TDMwLjU3MzIgMTEuMjM5OEMzMC43MzA3IDExLjM5NzMgMzAuNjE5MiAxMS42NjY2IDMwLjM5NjQgMTEuNjY2NkgyNC4zMzMzQzIyLjQ5MjQgMTEuNjY2NiAyMSAxMC4xNzQyIDIxIDguMzMzMjlWMi4yNzAxOFoiIGZpbGw9IiNEOTk5MDQiLz4KPGcgaWQ9IiYjMjI5OyYjMTg5OyYjMTYyOyYjMjMxOyYjMTM4OyYjMTgyOyYjMjMxOyYjMTg3OyYjMTQ3OyYjMjI5OyYjMTQ0OyYjMTM2OyI+CjxwYXRoIGQ9Ik05Ljk1MzEyIDE2LjY2NjZDOS4wMzI2NSAxNi42NjY2IDguMjg2NDYgMTcuNDEyOCA4LjI4NjQ2IDE4LjMzMzNWMTguNjM2M0M4LjI4NjQ2IDE5LjU1NjggOS4wMzI2NSAyMC4zMDMgOS45NTMxMiAyMC4zMDNIMTAuMjU2MkMxMS4xNzY2IDIwLjMwMyAxMS45MjI4IDE5LjU1NjggMTEuOTIyOCAxOC42MzYzVjE4LjMzMzNDMTEuOTIyOCAxNy40MTI4IDExLjE3NjYgMTYuNjY2NiAxMC4yNTYyIDE2LjY2NjZIOS45NTMxMloiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yMy44MjU0IDIxLjI2NjNDMjQuNDQzNCAyMC41OTg5IDI1LjU1OTIgMjEuMDM2MiAyNS41NTkyIDIxLjk0NTdWMzAuODMzM0MyNS41NTkyIDMxLjI5MzUgMjUuMTg2MSAzMS42NjY2IDI0LjcyNTkgMzEuNjY2Nkw4LjMzNDk0IDMxLjY2NjZDNy43Njg3OSAzMS42NjY2IDcuNDYwMzMgMzEuMDA1NSA3LjgyNDA1IDMwLjU3MTZMMTIuNyAyNC43NTU0QzEzLjM2NjEgMjMuOTYwOSAxNC41ODgzIDIzLjk2MDkgMTUuMjU0NCAyNC43NTU0TDE3Ljc5MiAyNy43ODI0TDIzLjgyNTQgMjEuMjY2M1oiIGZpbGw9IndoaXRlIi8+CjwvZz4KPC9nPgo8L3N2Zz4K\";", "import * as React from \"react\";\nconst SvgUnknownSuccess = props => <svg xmlns=\"http://www.w3.org/2000/svg\" width={32} height={40} fill=\"none\" viewBox=\"0 0 32 40\" {...props}><path fill=\"#8F959E\" d=\"M1 5a3.333 3.333 0 0 1 3.333-3.333H20.31c.442 0 .866.175 1.178.488l9.024 9.024c.312.312.488.736.488 1.178V35a3.333 3.333 0 0 1-3.333 3.333H4.333A3.333 3.333 0 0 1 1 35z\" opacity={0.9} /><path fill=\"#646A73\" d=\"M21 2.27a.25.25 0 0 1 .427-.177l9.146 9.147a.25.25 0 0 1-.177.427h-6.063A3.333 3.333 0 0 1 21 8.333z\" opacity={0.6} /><path fill=\"#fff\" d=\"M19.814 16.44q-1.363-1.29-3.588-1.289c-1.668 0-2.975.535-3.922 1.624-.802.936-1.234 1.833-1.296 3.247l-.004.165a.414.414 0 0 0 .415.419h.986a.42.42 0 0 0 .418-.414c.001-.07.002-.129.004-.16.049-.977.303-1.488.758-2.057.574-.765 1.416-1.142 2.573-1.142 1.031 0 1.805.263 2.339.797.509.53.774 1.252.774 2.158 0 .618-.224 1.206-.68 1.792-.144.181-.353.397-1.026 1.07-1.063.929-1.72 1.685-2.066 2.428a4.25 4.25 0 0 0-.43 1.914v.47c0 .23.186.417.416.417h1.008c.23 0 .417-.187.417-.417v-.47c0-.59.145-1.12.446-1.635.225-.383.515-.684 1.02-1.125 1.011-.916 1.572-1.461 1.824-1.781.604-.797.912-1.71.912-2.709 0-1.378-.433-2.484-1.298-3.301m-4.306 13.257a.417.417 0 0 0-.417.416v.985c0 .23.187.417.417.417h.985c.23 0 .417-.187.417-.417v-.985a.417.417 0 0 0-.417-.416z\" /></svg>;\nexport { SvgUnknownSuccess as ReactComponent };\nexport default \"data:image/svg+xml;base64,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\";", "import * as React from \"react\";\nconst SvgAudioSuccess = props => <svg xmlns=\"http://www.w3.org/2000/svg\" width={32} height={40} fill=\"none\" viewBox=\"0 0 32 40\" {...props}><path fill=\"#32A645\" d=\"M1 5a3.333 3.333 0 0 1 3.333-3.333H20.31c.442 0 .866.175 1.178.488l9.024 9.023c.312.313.488.737.488 1.179V35a3.333 3.333 0 0 1-3.333 3.333H4.333A3.333 3.333 0 0 1 1 35z\" /><path fill=\"#258832\" d=\"M21 2.27a.25.25 0 0 1 .427-.177l9.146 9.147a.25.25 0 0 1-.177.427h-6.063A3.333 3.333 0 0 1 21 8.333z\" opacity={0.9} /><path fill=\"#fff\" d=\"M13.083 31.25a3.2 3.2 0 0 1-2.354-.98 3.2 3.2 0 0 1-.979-2.353q0-1.375.98-2.355a3.2 3.2 0 0 1 2.353-.979q.48 0 .886.115.406.114.781.344V17.5c0-.69.56-1.25 1.25-1.25h5v2.5h-3.958a.625.625 0 0 0-.625.625v8.542a3.2 3.2 0 0 1-.98 2.354 3.2 3.2 0 0 1-2.354.979\" /></svg>;\nexport { SvgAudioSuccess as ReactComponent };\nexport default \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjYiIGQ9Ik0xIDQuOTk5OTZDMSAzLjE1OTAxIDIuNDkyMzggMS42NjY2MyA0LjMzMzMzIDEuNjY2NjNIMjAuMzA5NkMyMC43NTE3IDEuNjY2NjMgMjEuMTc1NiAxLjg0MjIyIDIxLjQ4ODIgMi4xNTQ3OEwzMC41MTE4IDExLjE3ODVDMzAuODI0NCAxMS40OTEgMzEgMTEuOTE1IDMxIDEyLjM1N1YzNUMzMSAzNi44NDA5IDI5LjUwNzYgMzguMzMzMyAyNy42NjY3IDM4LjMzMzNINC4zMzMzM0MyLjQ5MjM4IDM4LjMzMzMgMSAzNi44NDA5IDEgMzVWNC45OTk5NloiIGZpbGw9IiMzMkE2NDUiLz4KPHBhdGggaWQ9IlJlY3RhbmdsZSAyNTI3IiBvcGFjaXR5PSIwLjkiIGQ9Ik0yMSAyLjI3MDE4QzIxIDIuMDQ3NDUgMjEuMjY5MyAxLjkzNTkxIDIxLjQyNjggMi4wOTM0TDMwLjU3MzIgMTEuMjM5OEMzMC43MzA3IDExLjM5NzMgMzAuNjE5MiAxMS42NjY2IDMwLjM5NjQgMTEuNjY2NkgyNC4zMzMzQzIyLjQ5MjQgMTEuNjY2NiAyMSAxMC4xNzQyIDIxIDguMzMzMjlWMi4yNzAxOFoiIGZpbGw9IiMyNTg4MzIiLz4KPHBhdGggaWQ9Im11c2ljX25vdGUiIGQ9Ik0xMy4wODMzIDMxLjI1QzEyLjE2NjcgMzEuMjUgMTEuMzgxOSAzMC45MjM2IDEwLjcyOTIgMzAuMjcwOEMxMC4wNzY0IDI5LjYxODEgOS43NSAyOC44MzMzIDkuNzUgMjcuOTE2N0M5Ljc1IDI3IDEwLjA3NjQgMjYuMjE1MyAxMC43MjkyIDI1LjU2MjVDMTEuMzgxOSAyNC45MDk3IDEyLjE2NjcgMjQuNTgzMyAxMy4wODMzIDI0LjU4MzNDMTMuNDAyOCAyNC41ODMzIDEzLjY5NzkgMjQuNjIxNSAxMy45Njg4IDI0LjY5NzlDMTQuMjM5NiAyNC43NzQzIDE0LjUgMjQuODg4OSAxNC43NSAyNS4wNDE3VjE3LjVDMTQuNzUgMTYuODA5NiAxNS4zMDk2IDE2LjI1IDE2IDE2LjI1SDIxVjE4Ljc1SDE3LjA0MTdDMTYuNjk2NSAxOC43NSAxNi40MTY3IDE5LjAyOTggMTYuNDE2NyAxOS4zNzVWMjcuOTE2N0MxNi40MTY3IDI4LjgzMzMgMTYuMDkwMyAyOS42MTgxIDE1LjQzNzUgMzAuMjcwOEMxNC43ODQ3IDMwLjkyMzYgMTQgMzEuMjUgMTMuMDgzMyAzMS4yNVoiIGZpbGw9IndoaXRlIi8+CjwvZz4KPC9zdmc+Cg==\";", "import * as React from \"react\";\nconst SvgCodeSuccess = props => <svg xmlns=\"http://www.w3.org/2000/svg\" width={32} height={40} fill=\"none\" viewBox=\"0 0 32 40\" {...props}><path fill=\"#336DF4\" d=\"M1 5a3.333 3.333 0 0 1 3.333-3.333H20.31c.442 0 .866.175 1.178.488l9.024 9.024c.312.312.488.736.488 1.178V35a3.333 3.333 0 0 1-3.333 3.333H4.333A3.333 3.333 0 0 1 1 35z\" /><path fill=\"#0442D2\" d=\"M21 2.27a.25.25 0 0 1 .427-.176l9.146 9.146a.25.25 0 0 1-.177.427h-6.063A3.333 3.333 0 0 1 21 8.333z\" opacity={0.7} /><path fill=\"#fff\" d=\"m13.158 19.127-4.211 4.434 4.211 4.433a.41.41 0 0 1-.006.57l-.001.001-.566.564a.383.383 0 0 1-.552-.008l-5.015-5.279a.41.41 0 0 1 0-.563l5.015-5.28a.383.383 0 0 1 .552-.007l.566.564c.155.154.16.41.009.57zm10.036 4.434-3.911-4.434a.435.435 0 0 1 .005-.57l.002-.001.525-.564a.34.34 0 0 1 .512.008l4.657 5.279a.435.435 0 0 1 0 .563l-4.657 5.28a.338.338 0 0 1-.512.007l-.525-.564a.435.435 0 0 1-.009-.57l.002-.001zm-6.15-8.407.82.088c.224.024.385.212.359.42L16.266 31.18c-.026.208-.228.356-.452.332l-.82-.087c-.223-.024-.384-.212-.358-.42l1.957-15.52c.026-.207.228-.355.452-.331\" /></svg>;\nexport { SvgCodeSuccess as ReactComponent };\nexport default \"data:image/svg+xml;base64,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\";", "import * as React from \"react\";\nconst SvgZipSuccess = props => <svg xmlns=\"http://www.w3.org/2000/svg\" width={32} height={40} fill=\"none\" viewBox=\"0 0 32 40\" {...props}><path fill=\"#336DF4\" d=\"M1 5a3.333 3.333 0 0 1 3.333-3.333H20.31c.442 0 .866.175 1.178.488l9.024 9.024c.312.312.488.736.488 1.178V35a3.333 3.333 0 0 1-3.333 3.333H4.333A3.333 3.333 0 0 1 1 35z\" /><path fill=\"#0442D2\" d=\"M21 2.27a.25.25 0 0 1 .427-.177l9.146 9.147a.25.25 0 0 1-.177.427h-6.063A3.333 3.333 0 0 1 21 8.333z\" opacity={0.7} /><path fill=\"#fff\" d=\"M21.714 15.833c.526 0 .952.42.952.938v13.125a.945.945 0 0 1-.952.937H10.285a.945.945 0 0 1-.952-.937V16.77c0-.518.426-.938.952-.938zm-3.81 10.313h-3.81v3.281h3.81zm-.952.937v1.085h-1.905v-1.085zm.996-7.5H16v1.875h-1.905v1.875H16v1.875h1.948v-1.875h-1.904v-1.875h1.904zM16 17.708h-1.905v1.875H16z\" /></svg>;\nexport { SvgZipSuccess as ReactComponent };\nexport default \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjgiIGQ9Ik0xIDQuOTk5OTdDMSAzLjE1OTAyIDIuNDkyMzggMS42NjY2NCA0LjMzMzMzIDEuNjY2NjRIMjAuMzA5NkMyMC43NTE3IDEuNjY2NjQgMjEuMTc1NiAxLjg0MjI0IDIxLjQ4ODIgMi4xNTQ4TDMwLjUxMTggMTEuMTc4NUMzMC44MjQ0IDExLjQ5MSAzMSAxMS45MTUgMzEgMTIuMzU3VjM1QzMxIDM2Ljg0MDkgMjkuNTA3NiAzOC4zMzMzIDI3LjY2NjcgMzguMzMzM0g0LjMzMzMzQzIuNDkyMzggMzguMzMzMyAxIDM2Ljg0MDkgMSAzNVY0Ljk5OTk3WiIgZmlsbD0iIzMzNkRGNCIvPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjkiIG9wYWNpdHk9IjAuNyIgZD0iTTIxIDIuMjcwMTlDMjEgMi4wNDc0NyAyMS4yNjkzIDEuOTM1OTMgMjEuNDI2OCAyLjA5MzQyTDMwLjU3MzIgMTEuMjM5OUMzMC43MzA3IDExLjM5NzQgMzAuNjE5MiAxMS42NjY2IDMwLjM5NjQgMTEuNjY2NkgyNC4zMzMzQzIyLjQ5MjQgMTEuNjY2NiAyMSAxMC4xNzQzIDIxIDguMzMzMzFWMi4yNzAxOVoiIGZpbGw9IiMwNDQyRDIiLz4KPHBhdGggaWQ9IiYjMjI5OyYjMTQ0OyYjMTM2OyYjMjI5OyYjMTg1OyYjMTgyOyYjMjI5OyYjMTg5OyYjMTYyOyYjMjMxOyYjMTM4OyYjMTgyOyIgZD0iTTIxLjcxNCAxNS44MzM0QzIyLjIzOTkgMTUuODMzNCAyMi42NjYzIDE2LjI1MzEgMjIuNjY2MyAxNi43NzA5VjI5Ljg5NTlDMjIuNjY2MyAzMC40MTM2IDIyLjIzOTkgMzAuODMzNCAyMS43MTQgMzAuODMzNEgxMC4yODU0QzkuNzU5NCAzMC44MzM0IDkuMzMzMDEgMzAuNDEzNiA5LjMzMzAxIDI5Ljg5NTlWMTYuNzcwOUM5LjMzMzAxIDE2LjI1MzEgOS43NTk0IDE1LjgzMzQgMTAuMjg1NCAxNS44MzM0SDIxLjcxNFpNMTcuOTA0NCAyNi4xNDU5SDE0LjA5NDlWMjkuNDI3MUgxNy45MDQ0VjI2LjE0NTlaTTE2Ljk1MjEgMjcuMDgzNFYyOC4xNjgySDE1LjA0NzNWMjcuMDgzNEgxNi45NTIxWk0xNy45NDg0IDE5LjU4MzRIMTUuOTk5N1YyMS40NTg0SDE0LjA5NDlWMjMuMzMzNEgxNS45OTk3VjI1LjIwODRIMTcuOTQ4NFYyMy4zMzM0SDE2LjA0MzZWMjEuNDU4NEgxNy45NDg0VjE5LjU4MzRaTTE1Ljk5OTcgMTcuNzA4NEgxNC4wOTQ5VjE5LjU4MzRIMTUuOTk5N1YxNy43MDg0WiIgZmlsbD0id2hpdGUiLz4KPC9nPgo8L3N2Zz4K\";", "import * as React from \"react\";\nconst SvgPptSuccess = props => <svg xmlns=\"http://www.w3.org/2000/svg\" width={32} height={40} fill=\"none\" viewBox=\"0 0 32 40\" {...props}><path fill=\"#FF811A\" d=\"M1 5a3.333 3.333 0 0 1 3.333-3.333H20.31c.442 0 .866.175 1.178.488l9.024 9.024c.312.312.488.736.488 1.178V35a3.333 3.333 0 0 1-3.333 3.333H4.333A3.333 3.333 0 0 1 1 35z\" /><path fill=\"#ED6D0C\" d=\"M21 2.27a.25.25 0 0 1 .427-.176l9.146 9.146a.25.25 0 0 1-.177.427h-6.063A3.333 3.333 0 0 1 21 8.333z\" opacity={0.8} /><path fill=\"#fff\" d=\"M13.304 30.825v-5.977h2.836q1.023 0 2.035-.18a5.8 5.8 0 0 0 1.85-.644 3.8 3.8 0 0 0 1.355-1.281q.529-.825.528-2.062 0-.814-.22-1.56a3.25 3.25 0 0 0-.798-1.359q-.574-.59-1.537-.92-.953-.329-2.393-.327H11.69a.236.236 0 0 0-.236.236v14.074c0 .**************.236h1.38c.13 0 .235-.106.235-.236m4.559-7.703q-.741.114-1.683.114h-2.876v-5.11h3.276c1.244 0 2.137.216 2.68.633.531.408.797 1.009.797 1.825q-.002.846-.263 1.347a1.96 1.96 0 0 1-.732.784q-.485.296-1.2.407\" /></svg>;\nexport { SvgPptSuccess as ReactComponent };\nexport default \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MzAiIGQ9Ik0xIDUuMDAwMDhDMSAzLjE1OTEzIDIuNDkyMzggMS42NjY3NSA0LjMzMzMzIDEuNjY2NzVIMjAuMzA5NkMyMC43NTE3IDEuNjY2NzUgMjEuMTc1NiAxLjg0MjM0IDIxLjQ4ODIgMi4xNTQ5TDMwLjUxMTggMTEuMTc4NkMzMC44MjQ0IDExLjQ5MTIgMzEgMTEuOTE1MSAzMSAxMi4zNTcxVjM1LjAwMDFDMzEgMzYuODQxIDI5LjUwNzYgMzguMzMzNCAyNy42NjY3IDM4LjMzMzRINC4zMzMzM0MyLjQ5MjM4IDM4LjMzMzQgMSAzNi44NDEgMSAzNS4wMDAxVjUuMDAwMDhaIiBmaWxsPSIjRkY4MTFBIi8+CjxwYXRoIGlkPSJSZWN0YW5nbGUgMjUzMSIgb3BhY2l0eT0iMC44IiBkPSJNMjEgMi4yNzAzQzIxIDIuMDQ3NTcgMjEuMjY5MyAxLjkzNjAzIDIxLjQyNjggMi4wOTM1MkwzMC41NzMyIDExLjI0QzMwLjczMDcgMTEuMzk3NSAzMC42MTkyIDExLjY2NjcgMzAuMzk2NCAxMS42NjY3SDI0LjMzMzNDMjIuNDkyNCAxMS42NjY3IDIxIDEwLjE3NDQgMjEgOC4zMzM0MVYyLjI3MDNaIiBmaWxsPSIjRUQ2RDBDIi8+CjxwYXRoIGlkPSJTdWJ0cmFjdCIgZD0iTTEzLjMwMzggMzAuODI1MVYyNC44NDc3SDE2LjE0MDJDMTYuODIxOCAyNC44NDc3IDE3LjUwMDEgMjQuNzg3OCAxOC4xNzQ5IDI0LjY2OEMxOC44NjIxIDI0LjU0NiAxOS40NzkgMjQuMzMxNiAyMC4wMjQzIDI0LjAyNDRDMjAuNTc5MyAyMy43MTE3IDIxLjAzMiAyMy4yODM3IDIxLjM3OTkgMjIuNzQyOUMyMS43MzMyIDIyLjE5MzggMjEuOTA3NyAyMS41MDUzIDIxLjkwNzcgMjAuNjgxNEMyMS45MDc3IDIwLjEzODMgMjEuODM0NSAxOS42MTc5IDIxLjY4OCAxOS4xMjA2QzIxLjUzNzEgMTguNjA4MyAyMS4yNzA1IDE4LjE1NDYgMjAuODkwMiAxNy43NjIyQzIwLjUwODIgMTcuMzY4MSAxOS45OTQ3IDE3LjA2MTcgMTkuMzUyOSAxNi44NDEzQzE4LjcxNyAxNi42MjI5IDE3LjkyIDE2LjUxNTEgMTYuOTYwNSAxNi41MTUxSDExLjY4ODZDMTEuNTU4NiAxNi41MTUxIDExLjQ1MzEgMTYuNjIwNiAxMS40NTMxIDE2Ljc1MDZWMzAuODI1MUMxMS40NTMxIDMwLjk1NTIgMTEuNTU4NiAzMS4wNjA2IDExLjY4ODYgMzEuMDYwNkgxMy4wNjgzQzEzLjE5ODMgMzEuMDYwNiAxMy4zMDM4IDMwLjk1NTIgMTMuMzAzOCAzMC44MjUxWk0xNy44NjI3IDIzLjEyMTdDMTcuMzY5MiAyMy4xOTc4IDE2LjgwODIgMjMuMjM2IDE2LjE4MDEgMjMuMjM2SDEzLjMwMzdWMTguMTI2OEgxNi41ODAzQzE3LjgyNDMgMTguMTI2OCAxOC43MTcyIDE4LjM0MjQgMTkuMjYwMSAxOC43NTk0QzE5Ljc5MTIgMTkuMTY3MyAyMC4wNTcgMTkuNzY3OSAyMC4wNTcgMjAuNTgzN0MyMC4wNTcgMjEuMTQ3OSAxOS45Njc1IDIxLjU5NjggMTkuNzkzNSAyMS45MzA4QzE5LjYyMDIgMjIuMjYzNCAxOS4zNzczIDIyLjUyMzYgMTkuMDYyIDIyLjcxNTRDMTguNzM4IDIyLjkxMjMgMTguMzM4NyAyMy4wNDgzIDE3Ljg2MjcgMjMuMTIxN1oiIGZpbGw9IndoaXRlIi8+CjwvZz4KPC9zdmc+Cg==\";", "import * as React from \"react\";\nconst SvgVideoSuccess = props => <svg xmlns=\"http://www.w3.org/2000/svg\" width={32} height={40} fill=\"none\" viewBox=\"0 0 32 40\" {...props}><path fill=\"#336DF4\" d=\"M1 5a3.333 3.333 0 0 1 3.333-3.333H20.31c.442 0 .866.175 1.178.488l9.024 9.024c.312.312.488.736.488 1.178V35a3.333 3.333 0 0 1-3.333 3.333H4.333A3.333 3.333 0 0 1 1 35z\" /><path fill=\"#0442D2\" d=\"M21 2.27a.25.25 0 0 1 .427-.176l9.146 9.146a.25.25 0 0 1-.177.427h-6.063A3.333 3.333 0 0 1 21 8.333z\" opacity={0.7} /><path fill=\"#fff\" d=\"M7.664 17.917c0-.69.56-1.25 1.25-1.25h8.75c.69 0 1.25.56 1.25 1.25V19.4l1.887-1.062a1.25 1.25 0 0 1 1.863 1.09v5.725a1.25 1.25 0 0 1-1.863 1.09l-1.887-1.062v1.485c0 .69-.56 1.25-1.25 1.25h-8.75c-.69 0-1.25-.56-1.25-1.25zm3.75 3.75a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5\" /></svg>;\nexport { SvgVideoSuccess as ReactComponent };\nexport default \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjgiIGQ9Ik0xIDUuMDAwMDhDMSAzLjE1OTEzIDIuNDkyMzggMS42NjY3NSA0LjMzMzMzIDEuNjY2NzVIMjAuMzA5NkMyMC43NTE3IDEuNjY2NzUgMjEuMTc1NiAxLjg0MjM0IDIxLjQ4ODIgMi4xNTQ5TDMwLjUxMTggMTEuMTc4NkMzMC44MjQ0IDExLjQ5MTIgMzEgMTEuOTE1MSAzMSAxMi4zNTcxVjM1LjAwMDFDMzEgMzYuODQxIDI5LjUwNzYgMzguMzMzNCAyNy42NjY3IDM4LjMzMzRINC4zMzMzM0MyLjQ5MjM4IDM4LjMzMzQgMSAzNi44NDEgMSAzNS4wMDAxVjUuMDAwMDhaIiBmaWxsPSIjMzM2REY0Ii8+CjxwYXRoIGlkPSJSZWN0YW5nbGUgMjUyOSIgb3BhY2l0eT0iMC43IiBkPSJNMjEgMi4yNzAzQzIxIDIuMDQ3NTcgMjEuMjY5MyAxLjkzNjAzIDIxLjQyNjggMi4wOTM1MkwzMC41NzMyIDExLjI0QzMwLjczMDcgMTEuMzk3NSAzMC42MTkyIDExLjY2NjcgMzAuMzk2NCAxMS42NjY3SDI0LjMzMzNDMjIuNDkyNCAxMS42NjY3IDIxIDEwLjE3NDQgMjEgOC4zMzM0MVYyLjI3MDNaIiBmaWxsPSIjMDQ0MkQyIi8+CjxwYXRoIGlkPSImIzIyOTsmIzE4OTsmIzE2MjsmIzIzMTsmIzEzODsmIzE4MjsiIGQ9Ik03LjY2NDA2IDE3LjkxNjVDNy42NjQwNiAxNy4yMjYxIDguMjIzNzEgMTYuNjY2NSA4LjkxNDA2IDE2LjY2NjVIMTcuNjY0MUMxOC4zNTQ0IDE2LjY2NjUgMTguOTE0MSAxNy4yMjYxIDE4LjkxNDEgMTcuOTE2NVYxOS40MDA5TDIwLjgwMTIgMTguMzM5M0MyMS42MzQ1IDE3Ljg3MDYgMjIuNjY0MSAxOC40NzI4IDIyLjY2NDEgMTkuNDI4OFYyNS4xNTQyQzIyLjY2NDEgMjYuMTEwMiAyMS42MzQ1IDI2LjcxMjQgMjAuODAxMiAyNi4yNDM3TDE4LjkxNDEgMjUuMTgyMVYyNi42NjY1QzE4LjkxNDEgMjcuMzU2OSAxOC4zNTQ0IDI3LjkxNjUgMTcuNjY0MSAyNy45MTY1SDguOTE0MDZDOC4yMjM3MSAyNy45MTY1IDcuNjY0MDYgMjcuMzU2OSA3LjY2NDA2IDI2LjY2NjVWMTcuOTE2NVpNMTEuNDE0MSAyMS42NjY1QzEyLjEwNDQgMjEuNjY2NSAxMi42NjQxIDIxLjEwNjkgMTIuNjY0MSAyMC40MTY1QzEyLjY2NDEgMTkuNzI2MSAxMi4xMDQ0IDE5LjE2NjUgMTEuNDE0MSAxOS4xNjY1QzEwLjcyMzcgMTkuMTY2NSAxMC4xNjQxIDE5LjcyNjEgMTAuMTY0MSAyMC40MTY1QzEwLjE2NDEgMjEuMTA2OSAxMC43MjM3IDIxLjY2NjUgMTEuNDE0MSAyMS42NjY1WiIgZmlsbD0id2hpdGUiLz4KPC9nPgo8L3N2Zz4K\";", "import * as React from \"react\";\nconst SvgTxtSuccess = props => <svg xmlns=\"http://www.w3.org/2000/svg\" width={32} height={40} fill=\"none\" viewBox=\"0 0 32 40\" {...props}><path fill=\"#336DF4\" d=\"M1 5a3.333 3.333 0 0 1 3.333-3.333H20.31c.442 0 .866.175 1.178.488l9.024 9.024c.312.312.488.736.488 1.178V35a3.333 3.333 0 0 1-3.333 3.333H4.333A3.333 3.333 0 0 1 1 35z\" /><path fill=\"#0442D2\" d=\"M21 2.27a.25.25 0 0 1 .427-.176l9.146 9.146a.25.25 0 0 1-.177.427h-6.063A3.333 3.333 0 0 1 21 8.333z\" opacity={0.7} /><path fill=\"#fff\" d=\"M16.907 17.879v12.5a.227.227 0 0 1-.227.227h-1.364a.227.227 0 0 1-.227-.227v-12.5H9.407a.227.227 0 0 1-.227-.228v-1.363c0-.126.101-.227.227-.227h13.182c.125 0 .227.101.227.227v1.363a.227.227 0 0 1-.227.228z\" /></svg>;\nexport { SvgTxtSuccess as ReactComponent };\nexport default \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMiA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9Imljb24iPgo8cGF0aCBpZD0iUmVjdGFuZ2xlIDI1MjYiIGQ9Ik0xIDUuMDAwMDhDMSAzLjE1OTEzIDIuNDkyMzggMS42NjY3NSA0LjMzMzMzIDEuNjY2NzVIMjAuMzA5NkMyMC43NTE3IDEuNjY2NzUgMjEuMTc1NiAxLjg0MjM0IDIxLjQ4ODIgMi4xNTQ5TDMwLjUxMTggMTEuMTc4NkMzMC44MjQ0IDExLjQ5MTIgMzEgMTEuOTE1MSAzMSAxMi4zNTcxVjM1LjAwMDFDMzEgMzYuODQxIDI5LjUwNzYgMzguMzMzNCAyNy42NjY3IDM4LjMzMzRINC4zMzMzM0MyLjQ5MjM4IDM4LjMzMzQgMSAzNi44NDEgMSAzNS4wMDAxVjUuMDAwMDhaIiBmaWxsPSIjMzM2REY0Ii8+CjxwYXRoIGlkPSJSZWN0YW5nbGUgMjUyNyIgb3BhY2l0eT0iMC43IiBkPSJNMjEgMi4yNzAzQzIxIDIuMDQ3NTcgMjEuMjY5MyAxLjkzNjAzIDIxLjQyNjggMi4wOTM1MkwzMC41NzMyIDExLjI0QzMwLjczMDcgMTEuMzk3NSAzMC42MTkyIDExLjY2NjcgMzAuMzk2NCAxMS42NjY3SDI0LjMzMzNDMjIuNDkyNCAxMS42NjY3IDIxIDEwLjE3NDQgMjEgOC4zMzM0MVYyLjI3MDNaIiBmaWxsPSIjMDQ0MkQyIi8+CjxwYXRoIGlkPSJVbmlvbiIgZD0iTTE2LjkwNyAxNy44Nzg3VjMwLjM3ODdDMTYuOTA3IDMwLjUwNDIgMTYuODA1MiAzMC42MDYgMTYuNjc5NyAzMC42MDZIMTUuMzE2MUMxNS4xOTA1IDMwLjYwNiAxNS4wODg4IDMwLjUwNDIgMTUuMDg4OCAzMC4zNzg3VjE3Ljg3ODdIOS40MDY5NkM5LjI4MTQ0IDE3Ljg3ODcgOS4xNzk2OSAxNy43NzcgOS4xNzk2OSAxNy42NTE1VjE2LjI4NzhDOS4xNzk2OSAxNi4xNjIzIDkuMjgxNDQgMTYuMDYwNSA5LjQwNjk2IDE2LjA2MDVIMjIuNTg4OEMyMi43MTQzIDE2LjA2MDUgMjIuODE2MSAxNi4xNjIzIDIyLjgxNjEgMTYuMjg3OFYxNy42NTE1QzIyLjgxNjEgMTcuNzc3IDIyLjcxNDMgMTcuODc4NyAyMi41ODg4IDE3Ljg3ODdIMTYuOTA3WiIgZmlsbD0id2hpdGUiLz4KPC9nPgo8L3N2Zz4K\";", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type AssistParameterType } from '@coze-arch/bot-api/plugin_develop';\nimport {\n  FILE_TYPE_CONFIG,\n  type FileTypeEnum,\n} from '@coze-studio/file-kit/logic';\nimport { ACCEPT_UPLOAD_TYPES } from '@coze-studio/file-kit/config';\n\nimport { assistToExtend, parameterTypeExtendMap } from './config';\n\nexport const getFileAccept = (type: AssistParameterType) => {\n  const { fileTypes } = parameterTypeExtendMap[assistToExtend(type)];\n\n  const accept = fileTypes?.reduce((prev, curr) => {\n    const config = FILE_TYPE_CONFIG.find(c => c.fileType === curr);\n\n    if (!config) {\n      return prev;\n    }\n\n    prev = `${prev}${prev ? ',' : ''}${config.accept.join(',')}`;\n\n    return prev;\n  }, '');\n\n  if (!accept || accept === '*') {\n    return undefined;\n  }\n\n  return accept;\n};\n\nexport const getFileTypeFromAssistType = (\n  type: AssistParameterType,\n): FileTypeEnum | null => {\n  if (!type) {\n    return null;\n  }\n\n  const extendType = assistToExtend(type);\n\n  const config = Object.entries(parameterTypeExtendMap).find(\n    ([key]) => Number(key) === extendType,\n  );\n\n  if (!config) {\n    return null;\n  }\n\n  for (const fileType of config[1].fileTypes) {\n    const iconConfig = ACCEPT_UPLOAD_TYPES[fileType];\n\n    if (iconConfig) {\n      return fileType;\n    }\n  }\n\n  return null;\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type FC, type ReactNode, useReducer } from 'react';\n\nimport { merge } from 'lodash-es';\nimport { produce } from 'immer';\nimport { userStoreService } from '@coze-studio/user-store';\nimport { I18n } from '@coze-arch/i18n';\nimport { uploadFileV2 } from '@coze-arch/bot-utils';\nimport { FileTypeEnum, getFileInfo } from '@coze-studio/file-kit/logic';\nimport { Upload, Toast, type UploadProps } from '@coze-arch/coze-design';\n\ninterface PluginFileUploadProps {\n  render: (props: { fileState: FileState; clearFile: () => void }) => ReactNode;\n  onUploadSuccess?: (uri: string) => void;\n  uploadProps?: Partial<UploadProps>;\n  disabled?: boolean;\n  defaultUrl?: string;\n  defaultFileType: FileTypeEnum | null;\n}\n\ninterface FileState {\n  uri: string;\n  url: string;\n  name: string;\n  type: FileTypeEnum | null;\n  uploading: boolean;\n  abortSignal: AbortSignal;\n}\n\nconst getDefaultFileState = (states?: Partial<FileState>): FileState =>\n  merge(\n    {\n      uri: '',\n      url: '',\n      name: '',\n      type: null,\n      uploading: false,\n      abortSignal: new AbortController().signal,\n    } satisfies FileState,\n    states,\n  );\n\ntype Action = Partial<Omit<FileState, 'abortSignal'>>;\n\nexport const PluginFileUpload: FC<PluginFileUploadProps> = ({\n  disabled = false,\n  uploadProps,\n  render,\n  onUploadSuccess,\n  defaultUrl,\n  defaultFileType,\n}) => {\n  // @ts-expect-error -- linter-disable-autofix\n  const userId = userStoreService.useUserInfo().user_id_str;\n  const [fileState, setFileState] = useReducer(\n    (states: FileState, payload: Action) =>\n      produce(states, draft => {\n        if (!payload) {\n          return;\n        }\n\n        Object.keys(payload).forEach(key => {\n          // @ts-expect-error -- linter-disable-autofix\n          draft[key] = payload[key] ?? draft[key];\n        });\n      }),\n    getDefaultFileState({\n      url: defaultUrl ?? '',\n      type: defaultFileType ?? null,\n    }),\n  );\n\n  const clearFile = () => setFileState(getDefaultFileState());\n\n  const customRequest: UploadProps['customRequest'] = async ({\n    file,\n    fileInstance,\n  }) => {\n    // @ts-expect-error -- linter-disable-autofix\n    const type = getFileInfo(fileInstance).fileType;\n    setFileState({\n      uploading: true,\n      url: file.url,\n      name: file.name,\n    });\n\n    await uploadFileV2({\n      userId,\n      fileItemList: [\n        {\n          file: fileInstance,\n          fileType: type === FileTypeEnum.IMAGE ? 'image' : 'object',\n        },\n      ],\n      signal: fileState.abortSignal,\n      timeout: undefined,\n      onSuccess: info => {\n        const uri = info?.uploadResult?.Uri;\n\n        if (!uri) {\n          return;\n        }\n\n        setFileState({\n          uploading: false,\n          uri,\n          type,\n        });\n\n        onUploadSuccess?.(uri);\n      },\n      onUploadError: () => {\n        setFileState({\n          uploading: false,\n        });\n      },\n    });\n  };\n\n  if (typeof render !== 'function') {\n    return null;\n  }\n\n  return (\n    <Upload\n      className=\"w-full\"\n      draggable\n      limit={1}\n      disabled={disabled}\n      onAcceptInvalid={() => {\n        Toast.error(I18n.t('shortcut_Illegal_file_format'));\n      }}\n      onSizeError={() => {\n        if (uploadProps?.maxSize) {\n          Toast.error(\n            I18n.t('file_too_large', {\n              max_size: `${uploadProps.maxSize / 1024}MB`,\n            }),\n          );\n        }\n      }}\n      customRequest={customRequest}\n      showUploadList={false}\n      {...uploadProps}\n    >\n      {render({ fileState, clearFile })}\n    </Upload>\n  );\n};\n", "\n      import API from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React, { type FC, useEffect, useState } from 'react';\n\nimport classNames from 'classnames';\nimport { I18n } from '@coze-arch/i18n';\nimport { UIButton, Typography, UIIconButton } from '@coze-arch/bot-semi';\nimport { AssistParameterType } from '@coze-arch/bot-api/plugin_develop';\nimport { FileTypeEnum } from '@coze-studio/file-kit/logic';\nimport { ACCEPT_UPLOAD_TYPES } from '@coze-studio/file-kit/config';\nimport { IconDeleteOutline, IconUploadOutlined1 } from '@coze-arch/bot-icons';\n\nimport { ItemErrorTip } from '../item-error-tip';\nimport { getFileAccept, getFileTypeFromAssistType } from '../../file';\nimport { PluginFileUpload } from './upload';\n\nimport styles from './index.module.less';\n\nconst { Text } = Typography;\n\nconst fileUnknownIcon = ACCEPT_UPLOAD_TYPES[FileTypeEnum.DEFAULT_UNKNOWN].icon;\n\nexport const FileUploadItem: FC<{\n  assistParameterType: AssistParameterType;\n  onChange?: (uri: string) => void;\n  required?: boolean;\n  withDescription?: boolean;\n  defaultValue?: string;\n  check?: number;\n  disabled?: boolean;\n}> = ({\n  onChange,\n  required = false,\n  withDescription = false,\n  check = 0,\n  defaultValue,\n  disabled = false,\n  assistParameterType,\n}) => {\n  const [isErrorStatus, setIsErrorStatus] = useState(false);\n  const [value, setValue] = useState(defaultValue);\n  const defaultFileType = getFileTypeFromAssistType(assistParameterType);\n  const isImageString = assistParameterType === AssistParameterType.IMAGE;\n  const btnText = isImageString\n    ? I18n.t('plugin_file_upload_image')\n    : I18n.t('plugin_file_upload');\n  const errorTip = isImageString\n    ? I18n.t('plugin_file_upload_mention_image')\n    : I18n.t('plugin_file_upload_mention');\n  const accept = getFileAccept(assistParameterType);\n\n  useEffect(() => {\n    if (check === 0) {\n      return;\n    }\n\n    setIsErrorStatus(required && !value);\n  }, [check]);\n\n  const onChangeHandler = (uri: string) => {\n    setValue(uri);\n    onChange?.(uri);\n    setIsErrorStatus(required && !uri);\n  };\n\n  return (\n    <>\n      <PluginFileUpload\n        defaultUrl={value}\n        defaultFileType={defaultFileType}\n        onUploadSuccess={onChangeHandler}\n        uploadProps={{\n          accept,\n          disabled,\n          maxSize: 20480,\n        }}\n        render={({ fileState, clearFile }) => {\n          const { uploading, uri, url, name, type } = fileState;\n\n          /**\n           * Echo, only one url (string), need to be compatible = > do not show icon, url as file name\n           */\n          const onlyUrlString = !!url && !uri;\n          const displayName = onlyUrlString ? value : name;\n\n          let icon: string | undefined = url;\n\n          const uploadButton = (\n            <UIButton\n              icon={<IconUploadOutlined1 className={styles.icon} />}\n              loading={uploading}\n              disabled={disabled}\n              className=\"w-full\"\n            >\n              {uploading ? I18n.t('plugin_file_uploading') : btnText}\n            </UIButton>\n          );\n\n          if (uploading) {\n            return uploadButton;\n          } else if (onlyUrlString && type === FileTypeEnum.IMAGE) {\n            /** The image is not uploaded immediately and cannot be confirmed as a legitimate resource path. */\n            icon = fileUnknownIcon;\n          } else if (!isImageString) {\n            // @ts-expect-error -- linter-disable-autofix\n            const typeIcon = ACCEPT_UPLOAD_TYPES[type]?.icon;\n            if (typeIcon) {\n              icon = typeIcon;\n            } else {\n              icon = undefined;\n            }\n          }\n\n          if (onlyUrlString || uri) {\n            return (\n              <div\n                className={classNames(\n                  'flex items-center justify-between w-full h-[32px]',\n                  disabled ? 'cursor-not-allowed' : '',\n                )}\n              >\n                <div className=\"flex items-center min-w-0\">\n                  {icon ? (\n                    <img\n                      src={icon}\n                      className=\"w-[20px] h-[20px] mr-[5px] rounded-[0.5px]\"\n                    />\n                  ) : null}\n                  <Text ellipsis={{ showTooltip: true }} className=\"mr-[2px]\">\n                    {displayName}\n                  </Text>\n                </div>\n                <UIIconButton\n                  icon={<IconDeleteOutline />}\n                  disabled={disabled}\n                  onClick={e => {\n                    e.stopPropagation();\n                    clearFile();\n                    onChangeHandler('');\n                  }}\n                />\n              </div>\n            );\n          }\n\n          return uploadButton;\n        }}\n      />\n      {isErrorStatus ? (\n        <ItemErrorTip withDescription={withDescription} tip={errorTip} />\n      ) : null}\n    </>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React, { type FC, useEffect, useState } from 'react';\n\nimport { Typography, UIInput } from '@coze-arch/bot-semi';\nimport {\n  type APIParameter,\n  ParameterType,\n} from '@coze-arch/bot-api/plugin_develop';\n\nimport { updateNodeById } from '../../../utils';\nimport { type APIParameterRecord } from '../../../types/params';\nimport { ARRAYTAG, ROOTTAG, ROWKEY } from '../../../config';\nimport { ItemErrorTip } from '../../../components/item-error-tip';\nimport { FileUploadItem } from '../../../components/file-upload-item';\nimport { getColumnClass } from './utils';\n\ninterface InputItemProps {\n  val?: string;\n  width?: number | string;\n  height?: number;\n  check?: number;\n  callback: (val: string) => void;\n  useCheck?: boolean;\n  useBlockWrap?: boolean;\n  disabled: boolean;\n  desc: string;\n}\n\nconst InputItem = ({\n  val = '',\n  callback,\n  check = 0,\n  width = '100%',\n  useCheck = false,\n  useBlockWrap = false,\n  disabled,\n  desc,\n}: InputItemProps): JSX.Element => {\n  const [value, setValue] = useState(val);\n  const [errorStatus, setErrorStatus] = useState(false);\n  // Trigger validation via check (when committed)\n  useEffect(() => {\n    if (check === 0 || value === ARRAYTAG || value === ROOTTAG) {\n      return;\n    }\n    handleCheck(value);\n  }, [check]);\n  const handleCheck = (v: string) => {\n    if (!useCheck) {\n      return;\n    }\n\n    const filterVal = v ? String(v).replace(/\\s+/g, '') : '';\n    setErrorStatus(filterVal === '');\n  };\n\n  return (\n    <span\n      style={{ width, ...(useBlockWrap ? { display: 'inline-block' } : {}) }}\n    >\n      <UIInput\n        disabled={disabled}\n        value={value}\n        validateStatus={errorStatus ? 'error' : 'default'}\n        onChange={(e: string) => {\n          setValue(e);\n          callback(e);\n          handleCheck(e);\n        }}\n      />\n      <br />\n      {errorStatus ? <ItemErrorTip withDescription={!!desc} /> : null}\n    </span>\n  );\n};\n\nexport const ValueColRender: FC<{\n  record: APIParameterRecord;\n  disabled?: boolean;\n  check: number;\n  needCheck: boolean;\n  defaultKey: string;\n  data: Array<APIParameter>;\n  supportFileTypeUpload: boolean;\n}> = ({\n  record,\n  data,\n  disabled = false,\n  check,\n  needCheck,\n  defaultKey,\n  supportFileTypeUpload = false,\n}) => {\n  const showInput = !(\n    record?.type === ParameterType.Object ||\n    record?.type === ParameterType.Array ||\n    (disabled && record.value === undefined)\n  );\n\n  const showFile =\n    record?.type === ParameterType.String && !!record?.assist_type;\n\n  let renderItem = <></>;\n\n  if (supportFileTypeUpload && showFile) {\n    renderItem = (\n      <FileUploadItem\n        // @ts-expect-error -- linter-disable-autofix\n        defaultValue={record.value || record?.[defaultKey]}\n        // @ts-expect-error -- linter-disable-autofix\n        assistParameterType={record.assist_type}\n        onChange={uri => {\n          updateNodeById({\n            data,\n            targetKey: record[ROWKEY] as string,\n            field: 'value',\n            value: uri ? uri : null,\n          });\n        }}\n        withDescription={!!record?.desc}\n        required={needCheck || record?.is_required}\n        check={check}\n        disabled={disabled}\n      />\n    );\n  } else if (showInput) {\n    renderItem = (\n      <div className={getColumnClass(record)}>\n        <InputItem\n          disabled={disabled}\n          useBlockWrap={true}\n          // @ts-expect-error -- linter-disable-autofix\n          val={record.value || record?.[defaultKey]}\n          check={check}\n          useCheck={needCheck || record?.is_required}\n          callback={(e: string) => {\n            updateNodeById({\n              data,\n              targetKey: record[ROWKEY] as string,\n              field: 'value',\n              value: e,\n            });\n            updateNodeById({\n              data,\n              targetKey: record[ROWKEY] as string,\n              field: defaultKey,\n              value: e,\n            });\n          }}\n          // @ts-expect-error -- linter-disable-autofix\n          desc={record.desc}\n        />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"mr-[3px]\">\n      {renderItem}\n      {record.desc ? (\n        <Typography.Text\n          size=\"small\"\n          ellipsis={{\n            showTooltip: {\n              opts: { content: record.desc },\n            },\n          }}\n          style={{ verticalAlign: showInput ? 'top' : 'middle' }}\n        >\n          {record.desc}\n        </Typography.Text>\n      ) : null}\n    </div>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React, {\n  type Ref,\n  forwardRef,\n  useImperativeHandle,\n  useMemo,\n  useState,\n  useEffect,\n} from 'react';\n\nimport { set as ObjectSet, get as ObjectGet, cloneDeep } from 'lodash-es';\nimport { I18n } from '@coze-arch/i18n';\nimport { Tag } from '@coze-arch/coze-design';\nimport { UIButton, Table, Typography, UITag, Space } from '@coze-arch/bot-semi';\nimport { IconAddChildOutlined } from '@coze-arch/bot-icons';\nimport {\n  type APIParameter,\n  ParameterType,\n  DebugExampleStatus,\n} from '@coze-arch/bot-api/plugin_develop';\nimport { IconDeleteStroked } from '@douyinfe/semi-icons';\n\nimport styles from '../index.module.less';\nimport {\n  findPathById,\n  deleteNode,\n  findTemplateNodeByPath,\n  cloneWithRandomKey,\n  handleIsShowDelete,\n  checkHasArray,\n  maxDeep,\n} from '../../utils';\nimport { type APIParameterRecord } from '../../types/params';\nimport {\n  ARRAYTAG,\n  ROWKEY,\n  childrenRecordName,\n  getParameterTypeLabelFromRecord,\n} from '../../config';\nimport { getColumnClass } from './columns/utils';\nimport { ValueColRender } from './columns/param-value-col';\n\nconst getName = (record: APIParameterRecord) => {\n  const paramType = getParameterTypeLabelFromRecord(record);\n\n  return (\n    <span className={getColumnClass(record)}>\n      <Typography.Text\n        component=\"span\"\n        ellipsis={{\n          showTooltip: {\n            type: 'tooltip',\n            opts: { style: { maxWidth: '100%' } },\n          },\n        }}\n        style={{\n          maxWidth: `calc(100% - ${20 * (record.deep || 1) + 49}px)`,\n        }}\n      >\n        {record?.name}\n      </Typography.Text>\n      {record?.is_required ? (\n        <Typography.Text style={{ color: 'red' }}>{' * '}</Typography.Text>\n      ) : null}\n      {paramType ? (\n        <Tag\n          size=\"mini\"\n          prefixIcon={null}\n          className=\"!coz-fg-color-blue !coz-mg-color-blue shrink-0 font-normal px-6px rounded-[36px] ml-4px align-middle\"\n        >\n          {paramType}\n        </Tag>\n      ) : null}\n    </span>\n  );\n};\n\nexport interface ParamsFormProps {\n  requestParams?: Array<APIParameter>;\n  disabled: boolean;\n  check: number;\n  needCheck?: boolean;\n  height?: number;\n  defaultKey?: 'global_default' | 'local_default';\n  debugExampleStatus?: DebugExampleStatus;\n  showExampleTag?: boolean;\n  supportFileTypeUpload?: boolean;\n}\n\nconst getParamsTitle = (isShowExampleTag: boolean, disabled: boolean) =>\n  isShowExampleTag ? (\n    <Space>\n      <div>\n        {I18n.t(\n          disabled\n            ? 'mkpl_plugin_tool_parameter_description'\n            : 'Create_newtool_s4_value',\n        )}\n      </div>\n      <UITag>{I18n.t('plugin_edit_tool_test_run_example_tip')}</UITag>\n    </Space>\n  ) : (\n    I18n.t(\n      disabled\n        ? 'mkpl_plugin_tool_parameter_description'\n        : 'Create_newtool_s4_value',\n    )\n  );\n\n// eslint-disable-next-line @coze-arch/max-line-per-function -- already dismantling\nconst ParamsForm = (\n  props: ParamsFormProps,\n  ref: Ref<{ data: Array<APIParameter> } | null>,\n) => {\n  const {\n    requestParams,\n    disabled,\n    check,\n    needCheck = false,\n    height = 236,\n    defaultKey = 'global_default',\n    debugExampleStatus = DebugExampleStatus.Default,\n    showExampleTag = false,\n    supportFileTypeUpload = false,\n  } = props;\n  const [data, setData] = useState(\n    cloneDeep(requestParams ? requestParams : []),\n  );\n  const [resourceData, setResourceData] = useState(\n    cloneDeep(requestParams ? requestParams : []),\n  );\n  useEffect(() => {\n    setData(requestParams ? cloneDeep(requestParams) : []);\n    setResourceData(requestParams ? cloneDeep(requestParams) : []);\n  }, [requestParams]);\n\n  useImperativeHandle(ref, () => ({\n    data,\n  }));\n\n  const [flag, setFlag] = useState<boolean>(false);\n  // Add sub-node\n  const addChildNode = (record: APIParameter) => {\n    if (!data) {\n      return;\n    }\n    let result: APIParameter & {\n      path?: Array<number>;\n    } = {};\n    // 1. Find the path\n    findPathById({\n      data,\n      callback: (item: APIParameter, path: Array<number>) => {\n        if (item[ROWKEY] === record[ROWKEY]) {\n          result = { ...item, path };\n        }\n      },\n    });\n\n    // 2. Splicing path\n    const path = (result?.path || [])\n      .map((v: number) => [v, childrenRecordName])\n      .flat();\n    // newPath is the path of the template. The following node newNode can be directly referenced from this path\n    const newPath = findTemplateNodeByPath(resourceData, path);\n    // 3. Add a node\n    const newData = cloneDeep(data);\n    if (Array.isArray(ObjectGet(newData, path))) {\n      // This step is to find the corresponding root node according to newPath and clone a new node\n      const newNode = cloneWithRandomKey(ObjectGet(resourceData, newPath)[0]);\n      ObjectSet(newData, path, [...ObjectGet(newData, path), newNode]);\n    }\n    setData(newData);\n  };\n  const isShowExampleTag =\n    disabled &&\n    showExampleTag &&\n    debugExampleStatus === DebugExampleStatus.Enable;\n  const maxNum = maxDeep(data);\n\n  const columns = [\n    {\n      title: I18n.t('Create_newtool_s4_name'),\n      key: 'name',\n      className: styles['no-wrap'],\n      width: 180 + 20 * (maxNum - 1),\n      minWidth: 220,\n      render: (record: APIParameterRecord) => getName(record),\n    },\n    {\n      title: getParamsTitle(isShowExampleTag, disabled),\n      key: 'value',\n      className: styles['no-wrap'],\n      width: 200,\n      // @ts-expect-error -- linter-disable-autofix\n      render: record => (\n        <ValueColRender\n          record={record}\n          data={data}\n          disabled={disabled}\n          check={check}\n          needCheck={needCheck}\n          defaultKey={defaultKey}\n          supportFileTypeUpload={supportFileTypeUpload}\n        />\n      ),\n    },\n    {\n      title: I18n.t('dataset_detail_tableTitle_actions'),\n      key: 'operation',\n      width: 120,\n      render: (record: APIParameter) => (\n        <div className={getColumnClass(record)}>\n          {record?.type === ParameterType.Array && (\n            <UIButton\n              onClick={() => {\n                addChildNode(record);\n                setFlag(!flag);\n              }}\n              icon={<IconAddChildOutlined />}\n              type=\"secondary\"\n              theme=\"borderless\"\n            />\n          )}\n          {record?.name === ARRAYTAG &&\n            handleIsShowDelete(data, record[ROWKEY]) && (\n              <UIButton\n                onClick={() => {\n                  const clone = cloneDeep(data);\n                  if (record?.id) {\n                    deleteNode(clone, record?.id);\n                    setData(clone);\n                  }\n                }}\n                icon={<IconDeleteStroked />}\n                type=\"secondary\"\n                theme=\"borderless\"\n              />\n            )}\n        </div>\n      ),\n    },\n  ];\n\n  const filterColumns =\n    disabled || !checkHasArray(requestParams)\n      ? columns.filter(item => item.key !== 'operation')\n      : columns;\n\n  const scroll = useMemo(() => ({ y: height, x: '100%' }), []);\n\n  return (\n    <Table\n      className={styles['debug-params-table']}\n      pagination={false}\n      columns={filterColumns}\n      dataSource={data}\n      rowKey={ROWKEY}\n      childrenRecordName={childrenRecordName}\n      expandAllRows={true}\n      scroll={scroll}\n      empty={\n        !disabled && (\n          <div className={styles.empty}>\n            {I18n.t('plugin_form_no_result_desc')}\n          </div>\n        )\n      }\n    />\n  );\n};\n\nexport default forwardRef(ParamsForm);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport copy from 'copy-to-clipboard';\nimport classNames from 'classnames';\nimport { I18n } from '@coze-arch/i18n';\nimport { Space, Toast } from '@coze-arch/bot-semi';\nimport { MdBoxLazy } from '@coze-arch/bot-md-box-adapter/lazy';\nimport { IconCopy } from '@coze-arch/bot-icons';\n\nimport s from './index.module.less';\nexport enum HeadingType {\n  Request = 1,\n  Response = 2,\n}\ninterface MdBoxProps {\n  markDown: string;\n  headingType: HeadingType;\n  rawResponse?: string;\n  showRaw: boolean;\n}\n\nconst MAX_LENGTH = 30000;\n\nexport const DiyMdBox = ({\n  markDown,\n  headingType,\n  rawResponse,\n  showRaw,\n}: MdBoxProps) => {\n  const getContent = () => {\n    if (!rawResponse) {\n      return '{}';\n    }\n    if (rawResponse.length < MAX_LENGTH) {\n      return rawResponse;\n    }\n    return `${rawResponse.slice(0, MAX_LENGTH)}...`;\n  };\n  return (\n    <div className={s['mb-content']}>\n      <div className={s['mb-header']}>\n        <Space spacing={8}>\n          <span>Json</span>\n          <IconCopy\n            className={s['icon-copy']}\n            onClick={() => {\n              copy(markDown);\n              Toast.success(I18n.t('copy_success'));\n            }}\n          ></IconCopy>\n        </Space>\n      </div>\n      <div className={s['mb-main']}>\n        <div\n          className={classNames(s['mb-left'], {\n            [s['half-width']]: showRaw && headingType === HeadingType.Response,\n          })}\n        >\n          <MdBoxLazy markDown={`\\`\\`\\`json\\n${markDown}\\n\\`\\`\\``} />\n        </div>\n        {showRaw && headingType === HeadingType.Response ? (\n          <div className={s['mb-right']}>\n            <MdBoxLazy markDown={`\\`\\`\\`json\\n${getContent()}\\n\\`\\`\\``} />\n          </div>\n        ) : null}\n      </div>\n    </div>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useState, type PropsWithChildren, type FC } from 'react';\n\nimport classNames from 'classnames';\nimport { I18n } from '@coze-arch/i18n';\nimport { Banner, Space } from '@coze-arch/bot-semi';\nimport { IconPullDown } from '@coze-arch/bot-icons';\n\nimport { type CheckParamsProps } from '../types';\nimport { DiyMdBox, HeadingType } from './diy-mdbox';\n\nimport s from './index.module.less';\n\nconst Header = ({\n  // @ts-expect-error -- linter-disable-autofix\n  activeTab,\n  // @ts-expect-error -- linter-disable-autofix\n  setActiveTab,\n  // @ts-expect-error -- linter-disable-autofix\n  hideRawResponse,\n  // @ts-expect-error -- linter-disable-autofix\n  showRaw,\n  // @ts-expect-error -- linter-disable-autofix\n  setShowRaw,\n}) => {\n  const handleOpenRawResponse = () => {\n    setShowRaw(!showRaw);\n  };\n  return (\n    <div className={s['debug-check-header']}>\n      <div className={s['debug-check-tab']}>\n        <div\n          className={classNames(s['debug-check-tab-item'], {\n            [s['debug-check-tab-item-active']]:\n              activeTab === HeadingType.Request,\n          })}\n          onClick={() => setActiveTab(HeadingType.Request)}\n        >\n          Request\n        </div>\n        <div className={s['debug-check-tab-line']}></div>\n        <div\n          className={classNames(s['debug-check-tab-item'], {\n            [s['debug-check-tab-item-active']]:\n              activeTab === HeadingType.Response,\n          })}\n          onClick={() => setActiveTab(HeadingType.Response)}\n        >\n          Response\n        </div>\n      </div>\n      {activeTab === HeadingType.Response && !hideRawResponse ? (\n        <Space spacing={8}>\n          <span>Raw Response</span>\n          <IconPullDown\n            className={classNames(s.icon, {\n              [s.open]: showRaw,\n            })}\n            onClick={handleOpenRawResponse}\n          ></IconPullDown>\n        </Space>\n      ) : null}\n    </div>\n  );\n};\n\nconst ProcessContent: FC<PropsWithChildren> = ({ children }) => (\n  <div className={s['process-content']}>{children}</div>\n);\n\n/** Stringify indent */\nconst INDENTATION_SPACES = 2;\nconst LLMAndAPIContent: FC<{\n  toolMessageUnit: CheckParamsProps;\n}> = ({ toolMessageUnit }) => {\n  const { request, response, failReason, rawResp } = toolMessageUnit;\n  const [activeTab, setActiveTab] = useState(1);\n\n  const [showRaw, setShowRaw] = useState(false);\n  return (\n    <>\n      {!request && !response ? (\n        <div className={s['llm-debug-empty']}>\n          <div className={s['llm-debug-empty-content']}>\n            {I18n.t('plugin_s4_debug_empty')}\n          </div>\n        </div>\n      ) : (\n        <div className={s['debug-result-content']}>\n          <Header\n            activeTab={activeTab}\n            setActiveTab={setActiveTab}\n            hideRawResponse={!(!failReason && rawResp)}\n            showRaw={showRaw}\n            setShowRaw={setShowRaw}\n          />\n          {activeTab === 1 ? (\n            <>\n              <div className={s['llm-api-content']}>\n                <DiyMdBox\n                  markDown={\n                    request\n                      ? JSON.stringify(\n                          JSON.parse(request || '{}'),\n                          null,\n                          INDENTATION_SPACES,\n                        )\n                      : ''\n                  }\n                  headingType={activeTab}\n                  showRaw={showRaw}\n                />\n              </div>\n            </>\n          ) : (\n            <>\n              <div className={s['llm-api-content']}>\n                {failReason ? (\n                  <div className={s['error-reason-box']}>\n                    <Banner\n                      className={s['error-reason']}\n                      fullMode={false}\n                      icon={null}\n                      closeIcon={null}\n                      type=\"danger\"\n                      description={\n                        <div>\n                          <div>{I18n.t('plugin_s4_debug_detail')}</div>\n                          <div style={{ wordBreak: 'break-word' }}>\n                            {failReason}\n                          </div>\n                        </div>\n                      }\n                    />\n                  </div>\n                ) : (\n                  <DiyMdBox\n                    headingType={activeTab}\n                    markDown={JSON.stringify(\n                      JSON.parse(response || '{}'),\n                      null,\n                      INDENTATION_SPACES,\n                    )}\n                    rawResponse={JSON.stringify(\n                      JSON.parse(rawResp || '{}'),\n                      null,\n                      INDENTATION_SPACES,\n                    )}\n                    showRaw={showRaw}\n                  />\n                )}\n              </div>\n            </>\n          )}\n        </div>\n      )}\n    </>\n  );\n};\n\nexport const DebugCheck: FC<{\n  checkParams: CheckParamsProps;\n}> = ({ checkParams }) => (\n  <ProcessContent>\n    <LLMAndAPIContent toolMessageUnit={checkParams} />\n  </ProcessContent>\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useEffect, useState } from 'react';\n\nimport { I18n } from '@coze-arch/i18n';\nimport { UITag, Typography, Space, Col, Row } from '@coze-arch/bot-semi';\nimport {\n  type DebugExample,\n  type PluginType,\n  type PluginAPIInfo,\n} from '@coze-arch/bot-api/plugin_develop';\n\nimport { type CheckParamsProps, STATUS } from './types';\nimport { DebugParams } from './debug-components/debug-params';\nimport { DebugCheck } from './debug-components/debug-check';\n\nimport s from './index.module.less';\n\nconst { Text } = Typography;\n\n// @ts-expect-error -- linter-disable-autofix\nconst getApiTitle = (pluginName, name, labelKey) => (\n  <Text\n    className={s['card-title']}\n    ellipsis={{\n      showTooltip: {\n        opts: {\n          content: `${pluginName}.${name}`,\n          style: { wordBreak: 'break-word' },\n        },\n      },\n    }}\n  >\n    {pluginName}.{name} {I18n.t(labelKey)}\n  </Text>\n);\n\nexport const Debug: React.FC<{\n  pluginType?: PluginType;\n  disabled: boolean;\n  apiInfo: PluginAPIInfo;\n  pluginId: string;\n  apiId: string;\n  pluginName: string;\n  debugExample?: DebugExample;\n  setDebugStatus?: (status: STATUS | undefined) => void;\n  setDebugExample?: (v: DebugExample) => void;\n  isViewExample?: boolean; // Look at the example mode, the title is different\n  onSuccessCallback?: () => void;\n}> = ({\n  disabled,\n  apiInfo,\n  pluginId,\n  apiId,\n  pluginName,\n  setDebugStatus,\n  debugExample,\n  setDebugExample,\n  isViewExample = false,\n  pluginType,\n  onSuccessCallback,\n}) => {\n  const [checkParams, setCheckParams] = useState<CheckParamsProps>({});\n  const [status, setStatus] = useState<STATUS | undefined>();\n  const handleAction = ({\n    status: innerStatus,\n    request,\n    response,\n    failReason,\n    rawResp,\n  }: CheckParamsProps) => {\n    setStatus(innerStatus);\n    setCheckParams({\n      status: innerStatus,\n      request,\n      response,\n      failReason,\n      rawResp,\n    });\n    setDebugStatus?.(innerStatus);\n    innerStatus === STATUS.PASS &&\n      setDebugExample?.({ req_example: request, resp_example: response });\n    // Callback after successful debugging\n    innerStatus === STATUS.PASS && onSuccessCallback?.();\n  };\n\n  useEffect(() => {\n    if (debugExample) {\n      setCheckParams({\n        ...checkParams,\n        request: debugExample?.req_example,\n        response: debugExample?.resp_example,\n        failReason: '',\n      });\n    } else {\n      setCheckParams({});\n    }\n  }, [debugExample]);\n\n  return (\n    <div\n      className={s['debug-check']}\n      data-testid=\"plugin.tool.debug-modal-content\"\n    >\n      <Row gutter={16}>\n        <Col span={12}>\n          <div className={s['main-container']}>\n            <div className={s['card-header']}>\n              {isViewExample ? (\n                <Text className={s['card-title']}>\n                  {I18n.t('Create_newtool_s4_title')}\n                </Text>\n              ) : (\n                getApiTitle(pluginName, apiInfo.name, 'Create_newtool_s4_title')\n              )}\n            </div>\n            <div\n              style={{\n                maxHeight: isViewExample ? 'calc(100% - 55px)' : 542,\n                display: 'flex',\n              }}\n            >\n              <DebugParams\n                pluginType={pluginType}\n                disabled={disabled}\n                pluginId={pluginId}\n                apiId={apiId}\n                requestParams={apiInfo?.request_params}\n                callback={handleAction}\n                debugExampleStatus={apiInfo?.debug_example_status}\n                showExampleTag={!isViewExample}\n              />\n            </div>\n          </div>\n        </Col>\n        <Col span={12}>\n          <div className={s['main-container']}>\n            <div className={s['card-header']}>\n              <Space style={{ width: '100%' }}>\n                {isViewExample ? (\n                  <Text className={s['card-title']}>\n                    {I18n.t('plugin_edit_tool_test_run_debugging_example')}\n                  </Text>\n                ) : (\n                  getApiTitle(\n                    pluginName,\n                    apiInfo.name,\n                    'Create_newtool_s4_result',\n                  )\n                )}\n                {status === STATUS.PASS && (\n                  <UITag color=\"green\">{I18n.t('plugin_s4_debug_pass')}</UITag>\n                )}\n                {status === STATUS.FAIL && (\n                  <UITag color=\"red\">{I18n.t('plugin_s4_debug_failed')}</UITag>\n                )}\n              </Space>\n            </div>\n            <div\n              className={s['card-debug-check']}\n              style={{\n                height: isViewExample ? '100%' : 542,\n              }}\n            >\n              <DebugCheck checkParams={checkParams} />\n            </div>\n          </div>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n", "\n      import API from \"!../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.icon-HWVZml>svg{width:16px;height:15px}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"icon\": `icon-HWVZml`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.check-box-DZ400_{position:absolute}.form-check-tip-gccXJ5{transform-origin:0;color:var(--semi-color-danger);line-height:16px;display:inline-block;position:absolute;top:4px;left:0;right:0;font-size:12px!important}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"check-box\": `check-box-DZ400_`,\n\t\"checkBox\": `check-box-DZ400_`,\n\t\"form-check-tip\": `form-check-tip-gccXJ5`,\n\t\"formCheckTip\": `form-check-tip-gccXJ5`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.process-content-V3jtEI{white-space:break-spaces;background:var(--light-color-white-white,#fff);width:100%;height:100%;font-size:12px;font-weight:400;line-height:15px}.process-content-V3jtEI .debug-result-content-PPl7j5{flex-direction:column;height:100%;display:flex}.process-content-V3jtEI .debug-result-content-PPl7j5 .llm-api-content-eJW9aE{word-break:break-word;height:calc(100% - 40px)}.llm-debug-empty-_v9mkR{height:100%;color:var(--light-usage-text-color-text-3,var(--light-usage-disabled-color-disabled-text,rgba(28,31,35,.35)));justify-content:center;align-items:center;font-size:14px;display:flex}.llm-debug-empty-_v9mkR .llm-debug-empty-content-MR20uI{padding:16px}.error-reason-box-GrLuLI{border-top:1px solid rgba(29,28,36,.08);padding:12px}.mb-content-dLGdvZ{flex-direction:column;height:100%;display:flex}.mb-content-dLGdvZ .auto-hide-last-sibling-br>div{border-top-left-radius:0;border-top-right-radius:0}.mb-content-dLGdvZ .auto-hide-last-sibling-br>div>div:first-child{display:none}.mb-content-dLGdvZ .flow-markdown-body{flex:1}.mb-content-dLGdvZ .mb-header-Adjz56{color:#f7f7fa;background-color:#41414d;justify-content:space-between;align-items:center;height:40px;padding:0 12px;font-size:12px;line-height:40px;display:flex}.mb-content-dLGdvZ .mb-header-Adjz56 .icon-copy-X6T571{cursor:pointer;padding:6px}.mb-content-dLGdvZ .mb-header-Adjz56 .icon-copy-X6T571 svg{width:16px;height:16px}.mb-content-dLGdvZ .mb-header-Adjz56 .icon-copy-X6T571 svg path{fill:#fff;fill-opacity:1}.mb-content-dLGdvZ .mb-main-WzKrz4{background-color:#12131b;border-top-left-radius:0;border-top-right-radius:0;height:100%;display:flex;overflow-y:auto}.mb-content-dLGdvZ .mb-main-WzKrz4 .mb-left-L8YH5L{width:100%}.mb-content-dLGdvZ .mb-main-WzKrz4 .mb-left-L8YH5L.half-width-h9H0Gp{width:50%}.mb-content-dLGdvZ .mb-main-WzKrz4 .mb-right-bqZ0Ik{flex:none;width:50%;position:relative}.mb-content-dLGdvZ .mb-main-WzKrz4 .mb-right-bqZ0Ik:before{content:\"\";background-color:#565563;width:1px;position:absolute;top:0;bottom:0;left:0}.debug-params-table-DrAahj{flex:1;width:100%;display:flex;overflow:auto}.debug-params-table-DrAahj .empty-X0VDR7{margin-top:90px}.debug-params-table-DrAahj .semi-spin-block.semi-spin{display:flex}.debug-params-table-DrAahj .semi-spin-children{display:flex}.debug-params-table-DrAahj .semi-table-fixed-header{display:flex}.debug-params-table-DrAahj .semi-table-body{padding-bottom:12px;max-height:calc(100% - 40px)!important}.debug-params-table-DrAahj .semi-table-row:has(.disable){display:none}.debug-params-table-DrAahj .semi-table-header{position:relative}.debug-params-table-DrAahj .semi-table-header:after{content:\"\";background:var(--semi-color-border);width:calc(100% - 32px);height:1px;position:absolute;bottom:0;left:16px}.debug-params-table-DrAahj .semi-table-placeholder{border-bottom:0}.debug-params-table-DrAahj .semi-table-tbody>.semi-table-row>.semi-table-row-cell{vertical-align:top;border-bottom:none;padding:8px 16px}.debug-params-table-DrAahj .semi-table-thead>.semi-table-row>.semi-table-row-head{border-bottom:none;padding-top:9px;padding-bottom:9px}.debug-params-table-DrAahj .semi-table-tbody>.semi-table-row:hover>.semi-table-row-cell{background-color:transparent;background-image:none}.debug-check-header-RJbVVF{flex-shrink:0;justify-content:space-between;align-items:center;width:100%;height:40px;padding:0 16px;display:flex}.debug-check-header-RJbVVF .debug-check-tab-BDFxZ9{align-items:center;gap:12px;font-size:14px;font-style:normal;font-weight:600;line-height:20px;display:flex}.debug-check-header-RJbVVF .debug-check-tab-BDFxZ9 .debug-check-tab-line-kXLbEO{background:var(--Light-usage-border---color-border,rgba(28,29,37,.12));width:1px;height:16px}.debug-check-header-RJbVVF .debug-check-tab-BDFxZ9 .debug-check-tab-item-XT192G{cursor:pointer;color:var(--Light-usage-text---color-text-2,rgba(29,28,36,.6))}.debug-check-header-RJbVVF .debug-check-tab-BDFxZ9 .debug-check-tab-item-active-Zsr8_M{color:var(--Light-color-brand---brand-5,#4c54f0)}.debug-check-header-RJbVVF .icon-K15_kw{cursor:pointer;border:.75px solid rgba(29,28,36,.12);border-radius:6px;justify-content:center;align-items:center;width:32px;height:24px;display:flex}.debug-check-header-RJbVVF .icon-K15_kw svg{width:12px;height:12px;transform:rotate(-90deg)}.debug-check-header-RJbVVF .icon-K15_kw.open-vtnWZZ svg{transform:rotate(90deg)}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"process-content\": `process-content-V3jtEI`,\n\t\"processContent\": `process-content-V3jtEI`,\n\t\"debug-result-content\": `debug-result-content-PPl7j5`,\n\t\"debugResultContent\": `debug-result-content-PPl7j5`,\n\t\"llm-api-content\": `llm-api-content-eJW9aE`,\n\t\"llmApiContent\": `llm-api-content-eJW9aE`,\n\t\"llm-debug-empty\": `llm-debug-empty-_v9mkR`,\n\t\"llmDebugEmpty\": `llm-debug-empty-_v9mkR`,\n\t\"llm-debug-empty-content\": `llm-debug-empty-content-MR20uI`,\n\t\"llmDebugEmptyContent\": `llm-debug-empty-content-MR20uI`,\n\t\"error-reason-box\": `error-reason-box-GrLuLI`,\n\t\"errorReasonBox\": `error-reason-box-GrLuLI`,\n\t\"mb-content\": `mb-content-dLGdvZ`,\n\t\"mbContent\": `mb-content-dLGdvZ`,\n\t\"mb-header\": `mb-header-Adjz56`,\n\t\"mbHeader\": `mb-header-Adjz56`,\n\t\"icon-copy\": `icon-copy-X6T571`,\n\t\"iconCopy\": `icon-copy-X6T571`,\n\t\"mb-main\": `mb-main-WzKrz4`,\n\t\"mbMain\": `mb-main-WzKrz4`,\n\t\"mb-left\": `mb-left-L8YH5L`,\n\t\"mbLeft\": `mb-left-L8YH5L`,\n\t\"half-width\": `half-width-h9H0Gp`,\n\t\"halfWidth\": `half-width-h9H0Gp`,\n\t\"mb-right\": `mb-right-bqZ0Ik`,\n\t\"mbRight\": `mb-right-bqZ0Ik`,\n\t\"debug-params-table\": `debug-params-table-DrAahj`,\n\t\"debugParamsTable\": `debug-params-table-DrAahj`,\n\t\"empty\": `empty-X0VDR7`,\n\t\"debug-check-header\": `debug-check-header-RJbVVF`,\n\t\"debugCheckHeader\": `debug-check-header-RJbVVF`,\n\t\"debug-check-tab\": `debug-check-tab-BDFxZ9`,\n\t\"debugCheckTab\": `debug-check-tab-BDFxZ9`,\n\t\"debug-check-tab-line\": `debug-check-tab-line-kXLbEO`,\n\t\"debugCheckTabLine\": `debug-check-tab-line-kXLbEO`,\n\t\"debug-check-tab-item\": `debug-check-tab-item-XT192G`,\n\t\"debugCheckTabItem\": `debug-check-tab-item-XT192G`,\n\t\"debug-check-tab-item-active\": `debug-check-tab-item-active-Zsr8_M`,\n\t\"debugCheckTabItemActive\": `debug-check-tab-item-active-Zsr8_M`,\n\t\"icon\": `icon-K15_kw`,\n\t\"open\": `open-vtnWZZ`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.create-modal-pzySQx .semi-modal{max-width:1800px}.create-modal-pzySQx .semi-table-row-cell{overflow:hidden}.create-modal-pzySQx.big-modal-qaPc4l .modal-steps-nt2rbx{width:810px;margin:0 auto 24px}.textarea-single-line-QVMjQB .semi-input-textarea-counter{position:absolute;top:3px;right:0}.no-wrap-qLNXMk,.no-wrap-min-width-Q50Srr{white-space:nowrap}.params-layout-R3YGX6{justify-content:space-between;display:flex}.params-tag-_Ge8iA{margin-bottom:18px;padding-top:22px;font-size:18px;font-weight:600}.request-params-alN3YH .semi-table-placeholder{border-bottom:0;padding:1px 12px}.response-params-eA46dX .semi-table-placeholder{border-bottom:0;padding:1px 12px}.request-params-edit-nuqgXc .semi-table-thead .semi-table-row-head:first-child{padding-left:32px!important}.response-params-edit-ND7Hmz .semi-table-thead .semi-table-row-head:first-child{padding-left:32px!important}.request-params-edit-nuqgXc .semi-table-placeholder{border-bottom:0;padding:1px 12px}.response-params-edit-ND7Hmz .semi-table-placeholder{border-bottom:0;padding:1px 12px}.check-box-VG3Qxx{position:absolute}.form-check-tip-yT8BGr{transform-origin:0;color:var(--semi-color-danger);line-height:16px;display:inline-block;position:absolute;top:4px;left:0;right:0;font-size:12px!important}.w110-sD8csv{width:110%}.plugin-icon-error-c3Fxnp{margin-right:4px;font-size:13px;position:relative;top:2px}.plugin-tooltip-error-iJp55a{width:calc(100% - 20px);color:var(--semi-color-danger)!important;font-size:12px!important;line-height:16px!important}.add-params-btn-wrap-F6qzt5{border-top:1px solid var(--semi-color-border);margin:0 24px;padding-bottom:12px}.empty-content-y1oXMo{color:var(--light-usage-text-color-text-2,rgba(28,31,35,.6));text-align:center;margin:36px 0 54px;font-size:14px}.table-style-list-uzSG8W .semi-table-body{padding:12px 0}.table-style-list-uzSG8W .semi-select{border-radius:8px}.table-style-list-uzSG8W .semi-table-row-cell{padding:12px 2px!important}.table-style-list-uzSG8W .semi-table-expand-icon{margin-right:8px}.table-style-list-uzSG8W .semi-table-header{position:relative}.table-style-list-uzSG8W .semi-table-header:after{content:\"\";background:var(--semi-color-border);width:calc(100% - 48px);height:1px;position:absolute;bottom:0;left:24px}.table-style-list-uzSG8W .semi-table-thead .semi-table-row-head:first-child{padding-left:32px!important}.table-style-list-uzSG8W .semi-table-tbody>.semi-table-row>.semi-table-row-cell{border-bottom-color:transparent}.table-style-list-uzSG8W .semi-table-thead>.semi-table-row>.semi-table-row-head{color:var(--light-usage-text-color-text-1,rgba(28,29,35,.8));background:#f7f7fa;border-bottom:1px solid transparent;padding-left:10px;padding-right:10px;font-size:12px;font-weight:600}.table-style-list-uzSG8W .semi-table-row:hover>.semi-table-row-cell{background:0 0!important;border-bottom:1px solid transparent!important}.table-style-list-uzSG8W .semi-table-tbody>.semi-table-row{cursor:pointer;color:var(--light-usage-text-color-text-2,rgba(28,29,35,.6));background:#f7f7fa;font-size:12px;font-style:normal;font-weight:400}.table-style-list-uzSG8W .semi-table-tbody>.semi-table-row>.semi-table-cell-fixed-left{cursor:pointer;color:var(--light-usage-text-color-text-2,rgba(28,29,35,.6));background:#f7f7fa;font-size:12px;font-style:normal;font-weight:400}.table-style-list-uzSG8W .semi-table-tbody>.semi-table-row>.semi-table-cell-fixed-right{cursor:pointer;color:var(--light-usage-text-color-text-2,rgba(28,29,35,.6));background:#f7f7fa;font-size:12px;font-style:normal;font-weight:400}.table-style-list-uzSG8W .semi-table-thead>.semi-table-row>.semi-table-row-head.semi-table-cell-fixed-left:before{cursor:pointer;color:var(--light-usage-text-color-text-2,rgba(28,29,35,.6));background:#f7f7fa;font-size:12px;font-style:normal;font-weight:400}.table-style-list-uzSG8W .semi-table-thead>.semi-table-row>.semi-table-row-head.semi-table-cell-fixed-right:before{cursor:pointer;color:var(--light-usage-text-color-text-2,rgba(28,29,35,.6));background:#f7f7fa;font-size:12px;font-style:normal;font-weight:400}.table-style-list-uzSG8W .semi-spin-block.semi-spin{height:100%}.table-style-list-uzSG8W .semi-table-row:hover>.semi-table-row-cell:first-child{border-top-left-radius:8px!important;border-bottom-left-radius:8px!important}.table-style-list-uzSG8W .semi-table-row:hover>.semi-table-row-cell:last-child{border-top-right-radius:8px!important;border-bottom-right-radius:8px!important}.table-style-list-uzSG8W .semi-icon-chevron_down{opacity:.6}.table-style-list-uzSG8W.request-params-alN3YH .semi-table-tbody>.semi-table-row>.semi-table-row-cell{padding-left:16px!important}.table-style-list-uzSG8W.response-params-eA46dX .semi-table-tbody>.semi-table-row>.semi-table-row-cell{padding-left:16px!important}.input-modal-m6oSCe .runbtn-Q5S8QY{text-align:right;padding:12px}.input-modal-m6oSCe .semi-modal-footer{margin:0 0 12px}.input-modal-m6oSCe .debug-params-box-oHdlL1 .semi-table-thead>.semi-table-row>.semi-table-row-head{border-bottom-width:1px}.input-modal-m6oSCe .debug-params-box-oHdlL1 .semi-table-tbody>.semi-table-row:hover>.semi-table-row-cell{background:0 0!important;border-bottom:1px solid transparent!important}.debug-check-u44fx1{height:100%;padding-bottom:11px;overflow:hidden}.debug-check-u44fx1 .semi-row{height:100%}.debug-check-u44fx1 .semi-col{height:100%}.debug-check-u44fx1 .main-container-vNgRcA{flex-direction:column;max-width:100vw;height:100%;display:flex}.debug-check-u44fx1 .card-header-Yaroon{margin-bottom:14px;padding:8px 0}.debug-check-u44fx1 .card-title-fsPCJk{color:var(--light-usage-text-color-text-0,#1c1f23);text-overflow:ellipsis;font-size:18px;font-style:normal;font-weight:600}.debug-check-u44fx1 .card-debug-check-caBquS{border:1px solid var(--Light-usage-border---color-border,rgba(29,28,37,.08));background:#fff;border-radius:8px;height:100%;max-height:542px;overflow:auto}.debug-check-u44fx1 .card-debug-check-caBquS .markdown-body{overflow:hidden}.debug-check-u44fx1 .debug-params-box-oHdlL1{border:1px solid rgba(29,28,35,.08);border-radius:8px;flex-direction:column;width:100%;display:flex}.debug-check-u44fx1 .debug-params-box-oHdlL1 .runbtn-Q5S8QY{text-align:right;border-top:1px solid var(--semi-color-border);margin:0 16px;padding:12px 0}.debug-check-u44fx1 .debug-params-box-oHdlL1 .runbtn-Q5S8QY .semi-button.semi-button-loading{color:rgba(29,28,35,.2)}.safe-check-error-gid8KR{color:#f93920}.safe-check-error-gid8KR a{color:#4d53e8}.base-info-form-x1Lkuo .semi-icon-chevron_down{opacity:.6}.base-info-form-x1Lkuo .plugin-url-input-V6CAcc .semi-input-prepend{border:none}.base-info-form-x1Lkuo .plugin-url-prefix-ahens8{max-width:480px}.table-wrapper-RSx6UB{border:1px solid rgba(29,28,35,.08);border-radius:8px}.cascader-dropdown-R0RBcC .semi-cascader-option-label{color:#1d1c23;font-weight:400}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"create-modal\": `create-modal-pzySQx`,\n\t\"createModal\": `create-modal-pzySQx`,\n\t\"big-modal\": `big-modal-qaPc4l`,\n\t\"bigModal\": `big-modal-qaPc4l`,\n\t\"modal-steps\": `modal-steps-nt2rbx`,\n\t\"modalSteps\": `modal-steps-nt2rbx`,\n\t\"textarea-single-line\": `textarea-single-line-QVMjQB`,\n\t\"textareaSingleLine\": `textarea-single-line-QVMjQB`,\n\t\"no-wrap\": `no-wrap-qLNXMk`,\n\t\"noWrap\": `no-wrap-qLNXMk`,\n\t\"no-wrap-min-width\": `no-wrap-min-width-Q50Srr`,\n\t\"noWrapMinWidth\": `no-wrap-min-width-Q50Srr`,\n\t\"params-layout\": `params-layout-R3YGX6`,\n\t\"paramsLayout\": `params-layout-R3YGX6`,\n\t\"params-tag\": `params-tag-_Ge8iA`,\n\t\"paramsTag\": `params-tag-_Ge8iA`,\n\t\"request-params\": `request-params-alN3YH`,\n\t\"requestParams\": `request-params-alN3YH`,\n\t\"response-params\": `response-params-eA46dX`,\n\t\"responseParams\": `response-params-eA46dX`,\n\t\"request-params-edit\": `request-params-edit-nuqgXc`,\n\t\"requestParamsEdit\": `request-params-edit-nuqgXc`,\n\t\"response-params-edit\": `response-params-edit-ND7Hmz`,\n\t\"responseParamsEdit\": `response-params-edit-ND7Hmz`,\n\t\"check-box\": `check-box-VG3Qxx`,\n\t\"checkBox\": `check-box-VG3Qxx`,\n\t\"form-check-tip\": `form-check-tip-yT8BGr`,\n\t\"formCheckTip\": `form-check-tip-yT8BGr`,\n\t\"w110\": `w110-sD8csv`,\n\t\"plugin-icon-error\": `plugin-icon-error-c3Fxnp`,\n\t\"pluginIconError\": `plugin-icon-error-c3Fxnp`,\n\t\"plugin-tooltip-error\": `plugin-tooltip-error-iJp55a`,\n\t\"pluginTooltipError\": `plugin-tooltip-error-iJp55a`,\n\t\"add-params-btn-wrap\": `add-params-btn-wrap-F6qzt5`,\n\t\"addParamsBtnWrap\": `add-params-btn-wrap-F6qzt5`,\n\t\"empty-content\": `empty-content-y1oXMo`,\n\t\"emptyContent\": `empty-content-y1oXMo`,\n\t\"table-style-list\": `table-style-list-uzSG8W`,\n\t\"tableStyleList\": `table-style-list-uzSG8W`,\n\t\"input-modal\": `input-modal-m6oSCe`,\n\t\"inputModal\": `input-modal-m6oSCe`,\n\t\"runbtn\": `runbtn-Q5S8QY`,\n\t\"debug-params-box\": `debug-params-box-oHdlL1`,\n\t\"debugParamsBox\": `debug-params-box-oHdlL1`,\n\t\"debug-check\": `debug-check-u44fx1`,\n\t\"debugCheck\": `debug-check-u44fx1`,\n\t\"main-container\": `main-container-vNgRcA`,\n\t\"mainContainer\": `main-container-vNgRcA`,\n\t\"card-header\": `card-header-Yaroon`,\n\t\"cardHeader\": `card-header-Yaroon`,\n\t\"card-title\": `card-title-fsPCJk`,\n\t\"cardTitle\": `card-title-fsPCJk`,\n\t\"card-debug-check\": `card-debug-check-caBquS`,\n\t\"cardDebugCheck\": `card-debug-check-caBquS`,\n\t\"safe-check-error\": `safe-check-error-gid8KR`,\n\t\"safeCheckError\": `safe-check-error-gid8KR`,\n\t\"base-info-form\": `base-info-form-x1Lkuo`,\n\t\"baseInfoForm\": `base-info-form-x1Lkuo`,\n\t\"plugin-url-input\": `plugin-url-input-V6CAcc`,\n\t\"pluginUrlInput\": `plugin-url-input-V6CAcc`,\n\t\"plugin-url-prefix\": `plugin-url-prefix-ahens8`,\n\t\"pluginUrlPrefix\": `plugin-url-prefix-ahens8`,\n\t\"table-wrapper\": `table-wrapper-RSx6UB`,\n\t\"tableWrapper\": `table-wrapper-RSx6UB`,\n\t\"cascader-dropdown\": `cascader-dropdown-R0RBcC`,\n\t\"cascaderDropdown\": `cascader-dropdown-R0RBcC`\n};\nexport default ___CSS_LOADER_EXPORT___;\n"], "names": ["DebugParams", "requestParams", "pluginId", "apiId", "operation", "btnText", "I18n", "callback", "disabled", "debugExampleStatus", "DebugExampleStatus", "showExampleTag", "pluginType", "loading", "setLoading", "useState", "check", "<PERSON><PERSON><PERSON><PERSON>", "paramsFormRef", "useRef", "handleAction", "sleep", "_paramsFormRef_current", "_paramsFormRef_current1", "_paramsFormRef_current2", "errorEle", "document", "scrollToErrorElement", "Toast", "withSlardarIdButton", "reqParams", "Array", "transformTreeToObj", "resData", "PluginDevelopApi", "JSON", "STATUS", "e", "requestParamsData", "useMemo", "transformParamsToTree", "s", "ParamsForm", "UIButton", "PluginType", "IconComponent", "props", "Object", "getColumnClass", "record", "options", "undefined", "ItemErrorTip", "withDescription", "tip", "cl", "uploadTableConfig", "uploadDocConfig", "ACCEPT_UPLOAD_TYPES", "FileTypeEnum", "getFileAccept", "type", "fileTypes", "parameterTypeExtendMap", "assistToExtend", "accept", "prev", "curr", "config", "FILE_TYPE_CONFIG", "c", "getFileTypeFromAssistType", "extendType", "key", "Number", "fileType", "getDefaultFileState", "states", "merge", "AbortController", "PluginFileUpload", "uploadProps", "render", "onUploadSuccess", "defaultUrl", "defaultFileType", "userId", "userStoreService", "fileState", "setFileState", "useReducer", "payload", "produce", "draft", "_payload_key", "customRequest", "file", "fileInstance", "getFileInfo", "uploadFileV2", "info", "_info_uploadResult", "uri", "Upload", "clearFile", "Text", "Typography", "fileUnknownIcon", "FileUploadItem", "onChange", "required", "defaultValue", "assistParameterType", "isErrorStatus", "setIsErrorStatus", "value", "setValue", "isImageString", "AssistParameterType", "errorTip", "useEffect", "onChangeHandler", "uploading", "url", "name", "onlyUrlString", "icon", "uploadButton", "IconUploadOutlined1", "styles", "_ACCEPT_UPLOAD_TYPES_type", "typeIcon", "classNames", "UIIconButton", "IconDeleteOutline", "InputItem", "val", "width", "useCheck", "useBlockWrap", "desc", "errorStatus", "setErrorStatus", "ARRAYTAG", "ROOTTAG", "handleCheck", "v", "filterVal", "String", "UIInput", "ValueColRender", "data", "<PERSON><PERSON><PERSON><PERSON>", "defaultKey", "supportFileTypeUpload", "showInput", "ParameterType", "showFile", "renderItem", "updateNodeById", "ROWKEY", "getName", "paramType", "getParameterTypeLabelFromRecord", "Tag", "getParamsTitle", "isShowExampleTag", "Space", "UITag", "forwardRef", "ref", "height", "setData", "cloneDeep", "resourceData", "setResourceData", "useImperativeHandle", "flag", "setFlag", "addChildNode", "result", "findPathById", "item", "path", "childrenRecordName", "newPath", "findTemplateNodeByPath", "newData", "ObjectGet", "newNode", "cloneWithRandomKey", "ObjectSet", "maxNum", "maxDeep", "columns", "IconAddChildOutlined", "handleIsShowDelete", "clone", "deleteNode", "IconDeleteStroked", "filterColumns", "checkHasArray", "scroll", "Table", "HeadingType", "DiyMdBox", "markDown", "headingType", "rawResponse", "showRaw", "IconCopy", "copy", "MdBoxLazy", "Header", "activeTab", "setActiveTab", "hideRawResponse", "setShowRaw", "IconPullDown", "ProcessContent", "children", "LLMAndAPIContent", "toolMessageUnit", "request", "response", "failReason", "rawResp", "Banner", "DebugCheck", "checkParams", "getApiTitle", "pluginName", "labelKey", "Debug", "apiInfo", "setDebugStatus", "debugExample", "setDebugExample", "is<PERSON>iew<PERSON><PERSON><PERSON>", "onSuccessCallback", "setCheckParams", "status", "setStatus", "Row", "Col", "innerStatus", "___CSS_LOADER_EXPORT___", "module"], "mappings": "kTA0CaA,EAWR,AAAC,I,MAAA,CACJC,cAAAA,EAAgB,EAAE,CAClBC,SAAAA,CAAQ,CACRC,MAAAA,CAAK,CACLC,UAAAA,EAAY,CAAC,CACbC,QAAAA,EAAUC,EAAAA,CAAAA,CAAAA,CAAM,CAAC,wBAAwB,CACzCC,SAAAA,CAAQ,CACRC,SAAAA,CAAQ,CACRC,mBAAAA,EAAqBC,EAAAA,EAAAA,CAAAA,OAA0B,CAC/CC,eAAAA,EAAiB,EAAK,CACtBC,WAAAA,CAAU,CACX,GACO,CAACC,EAASC,EAAW,CAAGC,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAkB,IAC1C,CAACC,EAAOC,EAAS,CAAGF,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAiB,GACrCG,EAAgBC,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,EAAsC,MAE5D,IAAMC,G,EAAe,oBAEnBH,EAASD,EAAQ,GACjB,KAAMK,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAhCQ,KAiCd,IAegBC,EACbC,EAE8BC,EAlB3BC,EAAWC,SAAS,sBAAsB,CAAC,sBACjD,GAAI,CAACvB,GAASsB,EAAS,MAAM,CAAG,EAQ9B,MAPAE,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAqB,uBACrBC,EAAAA,EAAAA,CAAAA,KAAW,CAAC,CACV,QAASC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAoBvB,EAAAA,CAAAA,CAAAA,CAAM,CAAC,gCACpC,SAAU,EACV,MAAO,QACP,UAAW,EACb,GACO,GAGT,IAAIwB,EAAY,CAAC,EACjBhB,EAAW,IAETiB,MAAM,OAAO,CAAC,AAAqB,OAArBT,CAAAA,EAAAA,EAAc,OAAO,AAAD,GAApBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAuB,IAAI,GACxCC,AAAAA,CAAAA,CAAqB,OAArBA,CAAAA,EAAAA,EAAc,OAAO,AAAD,GAApBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAuB,IAAI,AAAD,GAAK,EAAC,EAAG,MAAM,CAAG,GAE7CO,CAAAA,EAAYE,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAmB,AAAqB,OAArBR,CAAAA,EAAAA,EAAc,OAAO,AAAD,GAApBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAuB,IAAI,GAE5D,GAAI,CACF,IAAMS,EAAU,MAAMC,EAAAA,EAAAA,CAAAA,QAAyB,CAAC,CAC9C,UAAWhC,EACX,OAAQC,EACR,WAAYgC,KAAK,SAAS,CAACL,GAC3B1B,UAAAA,CACF,EAEAG,OAAAA,GAAAA,EAAW,CACT,OAAQ0B,EAAQ,OAAO,CAAGG,EAAAA,CAAAA,CAAAA,IAAW,CAAGA,EAAAA,CAAAA,CAAAA,IAAW,CACnD,QAASH,EAAQ,OAAO,CACxB,SAAUA,EAAQ,IAAI,CACtB,WAAYA,EAAQ,MAAM,CAC1B,gBAAiBA,EAAQ,eAAe,CACxC,QAASA,EAAQ,QAAQ,AAC3B,EACF,CAAE,MAAOI,EAAG,CACV9B,MAAAA,GAAAA,EAAW,CACT,OAAQ6B,EAAAA,CAAAA,CAAAA,IAAW,CACnB,QAASD,KAAK,SAAS,CAACL,EAAW,KAzEhB,GA0EnB,SAAUxB,EAAAA,CAAAA,CAAAA,CAAM,CAAC,oBACjB,WAAYA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,mBACrB,EACF,CACAQ,EAAW,GACb,G,4CACMwB,EAAoBC,AAAAA,GAAAA,EAAAA,OAAAA,AAAAA,EACxB,IAAMC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAsBvC,GAC5B,CAACA,EAAc,EAEjB,MACE,WAAC,OAAI,UAAWwC,EAAAA,CAAAA,CAAAA,mBAAqB,C,UACnC,UAACC,EAAAA,CAAUA,CAAAA,CACT,OAAQ,IACR,IAAKxB,EACL,cAAeoB,EACf,WAAW,iBACX,SAAU9B,EACV,MAAOQ,EACP,mBAAoBP,EACpB,eAAgBE,EAChB,sBAAqB,E,GAEtB,CAACH,GACA,UAAC,OAAI,UAAWiC,EAAAA,CAAAA,CAAAA,MAAQ,C,SACtB,WAACE,EAAAA,EAAQA,CAAAA,CACP,SAAUnC,GAAYI,IAAegC,EAAAA,EAAAA,CAAAA,KAAgB,CACrD,MAAO,CAAE,MAAO,EAAG,EACnB,QAAS/B,EAET,KAAK,WACL,QAASO,E,UAERf,IAAYC,EAAAA,CAAAA,CAAAA,CAAM,CAAC,kCACjBO,CAAAA,EACGP,EAAAA,CAAAA,CAAAA,CAAM,CAAC,qBACPA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,gCAA+B,EAC3CD,IAAYC,EAAAA,CAAAA,CAAAA,CAAM,CAAC,0BACjBO,CAAAA,EACGP,EAAAA,CAAAA,CAAAA,CAAM,CAAC,qBACPA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,wBAAuB,E,OAMhD,C,oOC7IA,IAAMuC,EAAgB,QAhBtB,SAAsBC,CAAK,EACzB,OAAoB,eAAmB,CAAC,MAAOC,OAAO,MAAM,CAAC,CAC3D,QAAS,YACT,KAAM,OACN,MAAO,6BACP,MAAO,MACP,OAAQ,MACR,UAAW,GACX,cAAe,EACjB,EAAGD,GAAqB,eAAmB,CAAC,OAAQ,CAClD,SAAU,UACV,SAAU,UACV,EAAG,uTACH,KAAM,cACR,GACF,EACgD,kB,6CCAnCE,EAAiB,AAACC,GAC7BA,EAAO,cAAc,CAAG,UAAY,S,+KCRlCC,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,MAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGC,KAAAA,ECHtDC,EAAgE,AAAC,I,GAAA,CAC5EC,gBAAAA,EAAkB,EAAK,CACvBC,IAAAA,EAAMhD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,eAAe,CAC7B,G,MACC,UAAC,OAAI,UAAWmC,CAAAA,CAAAA,YAAc,C,SAC5B,UAAC,QACC,UAAWc,IACT,oBACAd,CAAAA,CAAAA,iBAAmB,CACnBY,EAAkB,cAAgB,SAClC,sB,SAGDC,C,kCCJDE,EAAoB,CACxB,MAAOlD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,qDACd,KC/Ba,o6DDgCf,EAEMmD,EAAkB,CACtB,MAAOnD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,mDACd,KEpCa,wzEFqCf,EAEaoD,EAMT,CACF,CAACC,EAAAA,CAAAA,CAAAA,KAAkB,CAAC,CAAE,CACpB,MAAOrD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,mDACd,KGhDW,gvDHiDb,EACA,CAACqD,EAAAA,CAAAA,CAAAA,KAAkB,CAAC,CAAEH,EACtB,CAACG,EAAAA,CAAAA,CAAAA,GAAgB,CAAC,CAAEH,EACpB,CAACG,EAAAA,CAAAA,CAAAA,GAAgB,CAAC,CAAEF,EACpB,CAACE,EAAAA,CAAAA,CAAAA,IAAiB,CAAC,CAAEF,EACrB,CAACE,EAAAA,CAAAA,CAAAA,eAA4B,CAAC,CAAE,CAC9B,MAAOrD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,uBACd,KIxDW,4rFJyDb,EACA,CAACqD,EAAAA,CAAAA,CAAAA,KAAkB,CAAC,CAAE,CACpB,MAAOrD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,qDACd,KK5DW,okDL6Db,EACA,CAACqD,EAAAA,CAAAA,CAAAA,IAAiB,CAAC,CAAE,CACnB,MAAOrD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,oDACd,KMhEW,4zENiEb,EACA,CAACqD,EAAAA,CAAAA,CAAAA,OAAoB,CAAC,CAAE,CACtB,MAAOrD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,mDACd,KOpEW,wqDPqEb,EACA,CAACqD,EAAAA,CAAAA,CAAAA,GAAgB,CAAC,CAAE,CAClB,MAAOrD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,mDACd,KQxEW,wnERyEb,EACA,CAACqD,EAAAA,CAAAA,CAAAA,KAAkB,CAAC,CAAE,CACpB,MAAOrD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,qDACd,KS5EW,owDT6Eb,EACA,CAACqD,EAAAA,CAAAA,CAAAA,GAAgB,CAAC,CAAE,CAClB,MAAOrD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,mDACd,KUhFW,o6CViFb,CACF,EW5DasD,EAAgB,AAACC,IAC5B,GAAM,CAAEC,UAAAA,CAAS,CAAE,CAAGC,EAAAA,EAAsB,CAACC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAeH,GAAM,CAE5DI,EAASH,MAAAA,EAAAA,KAAAA,EAAAA,EAAW,MAAM,CAAC,CAACI,EAAMC,KACtC,IAAMC,EAASC,EAAAA,EAAAA,CAAAA,IAAqB,CAACC,AAAAA,GAAKA,EAAE,QAAQ,GAAKH,UAEzD,AAAKC,EAILF,EAAQ,GAASA,MAAAA,CAAPA,GAAyBE,MAAAA,CAAlBF,EAAO,IAAM,IAA6B,OAAxBE,EAAO,MAAM,CAAC,IAAI,CAAC,MAH7CF,CAMX,EAAG,IAEH,GAAI,EAACD,GAAUA,AAAW,MAAXA,EAIf,OAAOA,CACT,EAEaM,EAA4B,AACvCV,IAEA,GAAI,CAACA,EACH,OAAO,KAGT,IAAMW,EAAaR,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAeH,GAE5BO,EAASrB,OAAO,OAAO,CAACgB,EAAAA,EAAsBA,EAAE,IAAI,CACxD,AAAC,I,GAAA,CAACU,EAAI,G,OAAKC,OAAOD,KAASD,C,GAG7B,GAAI,CAACJ,EACH,OAAO,KAGT,IAAK,IAAMO,KAAYP,CAAM,CAAC,EAAE,CAAC,SAAS,CAGxC,GAFmBV,CAAmB,CAACiB,EAAS,CAG9C,OAAOA,EAIX,OAAO,IACT,E,qEC7BMC,EAAsB,AAACC,GAC3BC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EACE,CACE,IAAK,GACL,IAAK,GACL,KAAM,GACN,KAAM,KACN,UAAW,GACX,YAAa,IAAIC,kBAAkB,MAAM,AAC3C,EACAF,GAKSG,EAA8C,AAAC,I,MAAA,CAC1DxE,SAAAA,EAAW,EAAK,CAChByE,YAAAA,CAAW,CACXC,OAAAA,CAAM,CACNC,gBAAAA,CAAe,CACfC,WAAAA,CAAU,CACVC,gBAAAA,CAAe,CAChB,GAEOC,EAASC,EAAAA,CAAAA,CAAAA,WAA4B,GAAG,WAAW,CACnD,CAACC,EAAWC,EAAa,CAAGC,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EAChC,CAACb,EAAmBc,IAClBC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAQf,EAAQgB,AAAAA,IACd,IAAI,CAACF,EAIL5C,OAAO,IAAI,CAAC4C,GAAS,OAAO,CAAClB,AAAAA,I,IAEdqB,CAAbD,CAAAA,CAAK,CAACpB,EAAI,CAAGqB,AAAY,OAAZA,CAAAA,EAAAA,CAAO,CAACrB,EAAI,AAAD,GAAXqB,AAAAA,KAAAA,IAAAA,EAAAA,EAAgBD,CAAK,CAACpB,EAAI,AACzC,EACF,GACFG,EAAoB,CAClB,IAAKQ,MAAAA,EAAAA,EAAc,GACnB,KAAMC,MAAAA,EAAAA,EAAmB,IAC3B,IAKF,IAAMU,G,EAA8C,qB,GAAO,CACzDC,KAAAA,CAAI,CACJC,aAAAA,CAAY,CACb,GAEOpC,EAAOqC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAYD,GAAc,QAAQ,CAC/CR,EAAa,CACX,UAAW,GACX,IAAKO,EAAK,GAAG,CACb,KAAMA,EAAK,IAAI,AACjB,GAEA,KAAMG,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAa,CACjBb,OAAAA,EACA,aAAc,CACZ,CACE,KAAMW,EACN,SAAUpC,IAASF,EAAAA,EAAAA,CAAAA,KAAkB,CAAG,QAAU,QACpD,EACD,CACD,OAAQ6B,EAAU,WAAW,CAC7B,QAASrC,KAAAA,EACT,UAAWiD,AAAAA,IACT,IAAYC,EAANC,EAAMD,MAAAA,EAAAA,KAAAA,EAAAA,AAAkB,OAAlBA,CAAAA,EAAAA,EAAM,YAAY,AAAD,GAAjBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAoB,GAAG,CAEnC,IAAI,CAACC,EAILb,EAAa,CACX,UAAW,GACXa,IAAAA,EACAzC,KAAAA,CACF,GAEAsB,MAAAA,GAAAA,EAAkBmB,EACpB,EACA,cAAe,KACbb,EAAa,CACX,UAAW,EACb,EACF,CACF,EACF,G,SA3CMM,CAAAA,E,uCA6CN,AAAI,AAAkB,YAAlB,OAAOb,EACF,KAIP,UAACqB,EAAAA,EAAMA,CAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,CACL,UAAU,SACV,UAAS,GACT,MAAO,EACP,SAAU/F,EACV,gBAAiB,KACfoB,EAAAA,EAAAA,CAAAA,KAAW,CAACtB,EAAAA,CAAAA,CAAAA,CAAM,CAAC,gCACrB,EACA,YAAa,KACP2E,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAa,OAAO,AAAD,GACrBrD,EAAAA,EAAAA,CAAAA,KAAW,CACTtB,EAAAA,CAAAA,CAAAA,CAAM,CAAC,iBAAkB,CACvB,SAAW,GAA6B,OAA3B2E,EAAY,OAAO,CAAG,KAAK,KAC1C,GAGN,EACA,cAAec,EACf,eAAgB,E,EACZd,GAAAA,C,SAEHC,EAAO,CAAEM,UAAAA,EAAWgB,UAzEP,IAAMf,EAAab,IAyEF,E,GAGrC,E,cCxJI,GAAU,CAAC,CAEf,IAAQ,iBAAiB,CAAG,IAC5B,GAAQ,aAAa,CAAG,IAElB,GAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3C,GAAQ,MAAM,CAAG,IACjB,GAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAE,IAKnB,OAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGzB,KAAAA,ECM7D,CAAEsD,KAAAA,EAAI,CAAE,CAAGC,EAAAA,EAAUA,CAErBC,GAAkBjD,CAAmB,CAACC,EAAAA,EAAAA,CAAAA,eAA4B,CAAC,CAAC,IAAI,CAEjEiD,GAQR,AAAC,I,GAAA,CACJC,SAAAA,CAAQ,CACRC,SAAAA,EAAW,EAAK,CAChBzD,gBAAAA,EAAkB,EAAK,CACvBrC,MAAAA,EAAQ,CAAC,CACT+F,aAAAA,CAAY,CACZvG,SAAAA,EAAW,EAAK,CAChBwG,oBAAAA,CAAmB,CACpB,GACO,CAACC,EAAeC,EAAiB,CAAGnG,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IAC7C,CAACoG,EAAOC,EAAS,CAAGrG,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAASgG,GAC7B1B,EAAkBd,EAA0ByC,GAC5CK,EAAgBL,IAAwBM,EAAAA,EAAAA,CAAAA,KAAyB,CACjEjH,EAAUgH,EACZ/G,EAAAA,CAAAA,CAAAA,CAAM,CAAC,4BACPA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,sBACLiH,EAAWF,EACb/G,EAAAA,CAAAA,CAAAA,CAAM,CAAC,oCACPA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,8BACL2D,EAASL,EAAcoD,GAE7BQ,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACR,GAAIxG,AAAU,IAAVA,EAIJkG,EAAiBJ,GAAY,CAACK,EAChC,EAAG,CAACnG,EAAM,EAEV,IAAMyG,EAAkB,AAACnB,IACvBc,EAASd,GACTO,MAAAA,GAAAA,EAAWP,GACXY,EAAiBJ,GAAY,CAACR,EAChC,EAEA,MACE,uB,UACE,UAACtB,EAAgBA,CACf,WAAYmC,EACZ,gBAAiB9B,EACjB,gBAAiBoC,EACjB,YAAa,CACXxD,OAAAA,EACAzD,SAAAA,EACA,QAAS,KACX,EACA,OAAQ,AAAC,I,GAAA,CAAEgF,UAAAA,CAAS,CAAEgB,UAAAA,CAAS,CAAE,GACzB,CAAEkB,UAAAA,CAAS,CAAEpB,IAAAA,CAAG,CAAEqB,IAAAA,CAAG,CAAEC,KAAAA,CAAI,CAAE/D,KAAAA,CAAI,CAAE,CAAG2B,EAKtCqC,EAAgB,CAAC,CAACF,GAAO,CAACrB,EAG5BwB,EAA2BH,EAEzBI,EACJ,UAACpF,EAAAA,EAAQA,CAAAA,CACP,KAAM,UAACqF,EAAAA,GAAmBA,CAAAA,CAAC,UAAWC,GAAAA,IAAW,A,GACjD,QAASP,EACT,SAAUlH,EACV,UAAU,S,SAETkH,EAAYpH,EAAAA,CAAAA,CAAAA,CAAM,CAAC,yBAA2BD,C,GAInD,GAAIqH,EACF,OAAOK,EACF,GAAIF,GAAiBhE,IAASF,EAAAA,EAAAA,CAAAA,KAAkB,CAErDmE,EAAOnB,QACF,GAAI,CAACU,EAAe,CAEzB,IAAiBa,EAAXC,EAAW,AAAyB,OAAzBD,CAAAA,EAAAA,CAAmB,CAACrE,EAAK,AAAD,GAAxBqE,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA2B,IAAI,CAE9CJ,EADEK,EACKA,EAEAhF,KAAAA,CAEX,QAEA,AAAI0E,GAAiBvB,EAEjB,WAAC,OACC,UAAW8B,IACT,oDACA5H,EAAW,qBAAuB,I,UAGpC,WAAC,OAAI,UAAU,4B,UACZsH,EACC,UAAC,OACC,IAAKA,EACL,UAAU,4C,GAEV,KACJ,UAACrB,GAAAA,CAAK,SAAU,CAAE,YAAa,EAAK,EAAG,UAAU,W,SA7CrCoB,EAAgBV,EAAQS,C,MAiDtC,UAACS,EAAAA,EAAYA,CAAAA,CACX,KAAM,UAACC,EAAAA,GAAiBA,CAAAA,CAAAA,GACxB,SAAU9H,EACV,QAAS6B,AAAAA,IACPA,EAAE,eAAe,GACjBmE,IACAiB,EAAgB,GAClB,C,MAMDM,CACT,C,GAEDd,EACC,UAAC7D,EAAYA,CAAC,gBAAiBC,EAAiB,IAAKkE,C,GACnD,K,EAGV,EC5HMgB,GAAY,AAAC,I,GAAA,CACjBC,IAAAA,EAAM,EAAE,CACRjI,SAAAA,CAAQ,CACRS,MAAAA,EAAQ,CAAC,CACTyH,MAAAA,EAAQ,MAAM,CACdC,SAAAA,EAAW,EAAK,CAChBC,aAAAA,EAAe,EAAK,CACpBnI,SAAAA,CAAQ,CACRoI,KAAAA,CAAI,CACW,GACT,CAACzB,EAAOC,EAAS,CAAGrG,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAASyH,GAC7B,CAACK,EAAaC,EAAe,CAAG/H,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IAE/CyG,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACR,GAAIxG,AAAU,IAAVA,GAAemG,IAAU4B,EAAAA,EAAQA,EAAI5B,IAAU6B,EAAAA,EAAOA,CAG1DC,EAAY9B,EACd,EAAG,CAACnG,EAAM,EACV,IAAMiI,EAAc,AAACC,IACnB,IAAI,CAACR,EAKLI,EAAeK,AAAc,KADXD,CAAAA,EAAIE,OAAOF,GAAG,OAAO,CAAC,OAAQ,IAAM,EAAC,EAEzD,EAEA,MACE,WAAC,QACC,MAAO,SAAET,MAAAA,C,EAAWE,EAAe,CAAE,QAAS,cAAe,EAAI,CAAC,G,UAElE,UAACU,EAAAA,EAAOA,CAAAA,CACN,SAAU7I,EACV,MAAO2G,EACP,eAAgB0B,EAAc,QAAU,UACxC,SAAU,AAACxG,IACT+E,EAAS/E,GACT9B,EAAS8B,GACT4G,EAAY5G,EACd,C,GAEF,UAAC,SACAwG,EAAc,UAACzF,EAAYA,CAAC,gBAAiB,CAAC,CAACwF,C,GAAW,K,EAGjE,EAEaU,GAQR,AAAC,I,GAAA,CACJrG,OAAAA,CAAM,CACNsG,KAAAA,CAAI,CACJ/I,SAAAA,EAAW,EAAK,CAChBQ,MAAAA,CAAK,CACLwI,UAAAA,CAAS,CACTC,WAAAA,CAAU,CACVC,sBAAAA,EAAwB,EAAK,CAC9B,GACOC,EAAY,CAChB1G,CAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,IAAI,AAAD,IAAM2G,EAAAA,EAAAA,CAAAA,MAAoB,EACrC3G,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,IAAI,AAAD,IAAM2G,EAAAA,EAAAA,CAAAA,KAAmB,EACnCpJ,GAAYyC,AAAiBE,KAAAA,IAAjBF,EAAO,KAAK,AAAa,EAGlC4G,EACJ5G,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,IAAI,AAAD,IAAM2G,EAAAA,EAAAA,CAAAA,MAAoB,EAAI,CAAC,CAAC3G,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,WAAW,AAAD,EAE3D6G,EAAa,yBAsDjB,OApDIJ,GAAyBG,EAC3BC,EACE,UAAClD,GAAcA,CAEb,aAAc3D,EAAO,KAAK,EAAIA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,CAAQ,CAACwG,EAAW,AAAD,EAEjD,oBAAqBxG,EAAO,WAAW,CACvC,SAAUqD,AAAAA,IACRyD,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAe,CACbR,KAAAA,EACA,UAAWtG,CAAM,CAAC+G,EAAAA,EAAMA,CAAC,CACzB,MAAO,QACP,MAAO1D,GAAY,IACrB,EACF,EACA,gBAAiB,CAAC,CAACrD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,IAAI,AAAD,EAC9B,SAAUuG,GAAavG,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,WAAW,AAAD,EACzC,MAAOjC,EACP,SAAUR,C,GAGLmJ,GACTG,CAAAA,EACE,UAAC,OAAI,UAAW9G,EAAeC,G,SAC7B,UAACsF,GAAAA,CACC,SAAU/H,EACV,aAAc,GAEd,IAAKyC,EAAO,KAAK,EAAIA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,CAAQ,CAACwG,EAAW,AAAD,EACxC,MAAOzI,EACP,SAAUwI,GAAavG,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,WAAW,AAAD,EACzC,SAAU,AAACZ,IACT0H,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAe,CACbR,KAAAA,EACA,UAAWtG,CAAM,CAAC+G,EAAAA,EAAMA,CAAC,CACzB,MAAO,QACP,MAAO3H,CACT,GACA0H,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAe,CACbR,KAAAA,EACA,UAAWtG,CAAM,CAAC+G,EAAAA,EAAMA,CAAC,CACzB,MAAOP,EACP,MAAOpH,CACT,EACF,EAEA,KAAMY,EAAO,IAAI,A,MAOvB,WAAC,OAAI,UAAU,W,UACZ6G,EACA7G,EAAO,IAAI,CACV,UAACyD,EAAAA,EAAAA,CAAAA,IAAe,EACd,KAAK,QACL,SAAU,CACR,YAAa,CACX,KAAM,CAAE,QAASzD,EAAO,IAAI,AAAC,CAC/B,CACF,EACA,MAAO,CAAE,cAAe0G,EAAY,MAAQ,QAAS,E,SAEpD1G,EAAO,IAAI,A,GAEZ,K,EAGV,ECpIMgH,GAAU,AAAChH,IACf,IAAMiH,EAAYC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAgClH,GAElD,MACE,WAAC,QAAK,UAAWD,EAAeC,G,UAC9B,UAACyD,EAAAA,EAAAA,CAAAA,IAAe,EACd,UAAU,OACV,SAAU,CACR,YAAa,CACX,KAAM,UACN,KAAM,CAAE,MAAO,CAAE,SAAU,MAAO,CAAE,CACtC,CACF,EACA,MAAO,CACL,SAAW,eAA2C,OAA7B,GAAMzD,CAAAA,EAAO,IAAI,EAAI,GAAK,GAAG,MACxD,E,SAECA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,IAAI,A,GAEdA,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,WAAW,AAAD,EACjB,UAACyD,EAAAA,EAAAA,CAAAA,IAAe,EAAC,MAAO,CAAE,MAAO,KAAM,E,SAAI,K,GACzC,KACHwD,EACC,UAACE,EAAAA,EAAGA,CAAAA,CACF,KAAK,OACL,WAAY,KACZ,UAAU,uG,SAETF,C,GAED,K,EAGV,EAcMG,GAAiB,CAACC,EAA2B9J,IACjD8J,EACE,WAACC,EAAAA,CAAKA,CAAAA,C,UACJ,UAAC,O,SACEjK,EAAAA,CAAAA,CAAAA,CAAM,CACLE,EACI,yCACA,0B,GAGR,UAACgK,EAAAA,EAAKA,CAAAA,C,SAAElK,EAAAA,CAAAA,CAAAA,CAAM,CAAC,wC,MAGjBA,EAAAA,CAAAA,CAAAA,CAAM,CACJE,EACI,yCACA,2BAuKV,GAAeiK,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EAlKI,CACjB3H,EACA4H,KAEA,GAAM,CACJzK,cAAAA,CAAa,CACbO,SAAAA,CAAQ,CACRQ,MAAAA,CAAK,CACLwI,UAAAA,EAAY,EAAK,CACjBmB,OAAAA,EAAS,GAAG,CACZlB,WAAAA,EAAa,gBAAgB,CAC7BhJ,mBAAAA,EAAqBC,EAAAA,EAAAA,CAAAA,OAA0B,CAC/CC,eAAAA,EAAiB,EAAK,CACtB+I,sBAAAA,EAAwB,EAAK,CAC9B,CAAG5G,EACE,CAACyG,EAAMqB,EAAQ,CAAG7J,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EACtB8J,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAU5K,GAAgC,EAAE,GAExC,CAAC6K,EAAcC,EAAgB,CAAGhK,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EACtC8J,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAU5K,GAAgC,EAAE,GAE9CuH,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACRoD,EAAQ3K,EAAgB4K,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAU5K,GAAiB,EAAE,EACrD8K,EAAgB9K,EAAgB4K,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAU5K,GAAiB,EAAE,CAC/D,EAAG,CAACA,EAAc,EAElB+K,AAAAA,GAAAA,EAAAA,mBAAAA,AAAAA,EAAoBN,EAAK,IAAO,EAC9BnB,KAAAA,CACF,IAEA,GAAM,CAAC0B,EAAMC,EAAQ,CAAGnK,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAkB,IAEpCoK,EAAe,AAAClI,IACpB,IAAI,CAACsG,GAGL,IAAI6B,EAEA,CAAC,EAELC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAa,CACX9B,KAAAA,EACA,SAAU,CAAC+B,EAAoBC,KACzBD,CAAI,CAACtB,EAAAA,EAAMA,CAAC,GAAK/G,CAAM,CAAC+G,EAAAA,EAAMA,CAAC,EACjCoB,CAAAA,EAAS,mBAAKE,GAAAA,CAAMC,KAAAA,C,GAExB,CACF,GAGA,IAAMA,EAAQH,AAAAA,CAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,IAAI,AAAD,GAAK,EAAC,EAC5B,GAAG,CAAC,AAAClC,GAAc,CAACA,EAAGsC,EAAAA,EAAkBA,CAAC,EAC1C,IAAI,GAEDC,EAAUC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAuBZ,EAAcS,GAE/CI,EAAUd,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUtB,GAC1B,GAAIxH,MAAM,OAAO,CAAC6J,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUD,EAASJ,IAAQ,CAE3C,IAAMM,EAAUC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAmBF,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUd,EAAcW,EAAQ,CAAC,EAAE,EACtEM,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUJ,EAASJ,EAAM,IAAIK,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUD,EAASJ,GAAOM,EAAQ,CACjE,CACAjB,EAAQe,GACV,EACMrB,EACJ9J,GACAG,GACAF,IAAuBC,EAAAA,EAAAA,CAAAA,MAAyB,CAC5CsL,EAASC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAQ1C,GAEjB2C,EAAU,CACd,CACE,MAAO5L,EAAAA,CAAAA,CAAAA,CAAM,CAAC,0BACd,IAAK,OACL,UAAW2H,EAAAA,CAAAA,CAAAA,UAAiB,CAC5B,MAAO,IAAM,GAAM+D,CAAAA,EAAS,GAC5B,SAAU,IACV,OAAQ,AAAC/I,GAA+BgH,GAAQhH,EAClD,EACA,CACE,MAAOoH,GAAeC,EAAkB9J,GACxC,IAAK,QACL,UAAWyH,EAAAA,CAAAA,CAAAA,UAAiB,CAC5B,MAAO,IAEP,OAAQhF,AAAAA,GACN,UAACqG,GAAcA,CACb,OAAQrG,EACR,KAAMsG,EACN,SAAU/I,EACV,MAAOQ,EACP,UAAWwI,EACX,WAAYC,EACZ,sBAAuBC,C,EAG7B,EACA,CACE,MAAOpJ,EAAAA,CAAAA,CAAAA,CAAM,CAAC,qCACd,IAAK,YACL,MAAO,IACP,OAAQ,AAAC2C,GACP,WAAC,OAAI,UAAWD,EAAeC,G,UAC5BA,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,IAAI,AAAD,IAAM2G,EAAAA,EAAAA,CAAAA,KAAmB,EACnC,UAACjH,EAAAA,EAAQA,CAAAA,CACP,QAAS,KACPwI,EAAalI,GACbiI,EAAQ,CAACD,EACX,EACA,KAAM,UAACkB,EAAAA,GAAoBA,CAAAA,CAAAA,GAC3B,KAAK,YACL,MAAM,Y,GAGTlJ,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,IAAI,AAAD,IAAM8F,EAAAA,EAAQA,EACxBqD,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAmB7C,EAAMtG,CAAM,CAAC+G,EAAAA,EAAMA,CAAC,GACrC,UAACrH,EAAAA,EAAQA,CAAAA,CACP,QAAS,KACP,IAAM0J,EAAQxB,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUtB,GACpBtG,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,EAAE,AAAD,IACXqJ,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAWD,EAAOpJ,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,EAAE,EAC5B2H,EAAQyB,GAEZ,EACA,KAAM,UpBtOPxJ,EoBsOyB0J,CAAAA,GACxB,KAAK,YACL,MAAM,Y,KAKlB,EACD,CAEKC,EACJhM,GAAY,CAACiM,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAcxM,GACvBiM,EAAQ,MAAM,CAACZ,AAAAA,GAAQA,AAAa,cAAbA,EAAK,GAAG,EAC/BY,EAEAQ,EAASnK,AAAAA,GAAAA,EAAAA,OAAAA,AAAAA,EAAQ,IAAO,EAAE,EAAGoI,EAAQ,EAAG,MAAO,GAAI,EAAE,EAE3D,MACE,UAACgC,EAAAA,EAAKA,CAAAA,CACJ,UAAW1E,EAAAA,CAAAA,CAAAA,qBAA4B,CACvC,WAAY,GACZ,QAASuE,EACT,WAAYjD,EACZ,OAAQS,EAAAA,EAAMA,CACd,mBAAoBwB,EAAAA,EAAkBA,CACtC,cAAe,GACf,OAAQkB,EACR,MACE,CAAClM,GACC,UAAC,OAAI,UAAWyH,EAAAA,CAAAA,CAAAA,KAAY,C,SACzB3H,EAAAA,CAAAA,CAAAA,CAAM,CAAC,6B,IAMpB,E,yDCrQYsM,E,sMAAL,IAAKA,G,CAAAA,E,sDAAAA,GAaCC,EAAW,AAAC,I,GAAA,CACvBC,SAAAA,CAAQ,CACRC,YAAAA,CAAW,CACXC,YAAAA,CAAW,CACXC,QAAAA,CAAO,CACI,GAUX,MACE,WAAC,OAAI,UAAWxK,EAAAA,CAAAA,CAAAA,aAAe,C,UAC7B,UAAC,OAAI,UAAWA,EAAAA,CAAAA,CAAAA,YAAc,C,SAC5B,WAAC8H,EAAAA,CAAKA,CAAAA,CAAC,QAAS,E,UACd,UAAC,Q,SAAK,M,GACN,UAAC2C,EAAAA,GAAQA,CAAAA,CACP,UAAWzK,EAAAA,CAAAA,CAAAA,YAAc,CACzB,QAAS,KACP0K,IAAKL,GACLlL,EAAAA,EAAAA,CAAAA,OAAa,CAACtB,EAAAA,CAAAA,CAAAA,CAAM,CAAC,gBACvB,C,QAIN,WAAC,OAAI,UAAWmC,EAAAA,CAAAA,CAAAA,UAAY,C,UAC1B,UAAC,OACC,UAAW2F,IAAW3F,EAAAA,CAAAA,CAAAA,UAAY,CAAE,CAClC,CAACA,EAAAA,CAAAA,CAAAA,aAAe,CAAC,CAAEwK,GAAWF,AAAgB,IAAhBA,CAChC,G,SAEA,UAACK,EAAAA,EAASA,CAAAA,CAAC,SAAW,YAAuB,OAATN,EAAS,Q,KAE9CG,GAAWF,AAAgB,IAAhBA,EACV,UAAC,OAAI,UAAWtK,EAAAA,CAAAA,CAAAA,WAAa,C,SAC3B,UAAC2K,EAAAA,EAASA,CAAAA,CAAC,SAAW,YAA2B,OAhCzD,AAAKJ,EAGDA,EAAY,MAAM,CAZP,IAaNA,EAED,GAAmC,OAAjCA,EAAY,KAAK,CAAC,EAfb,KAe4B,OALlC,KA+BgD,Q,KAEjD,K,KAIZ,ECtDMK,EAAS,AAAC,I,GAAA,CAEdC,UAAAA,CAAS,CAETC,aAAAA,CAAY,CAEZC,gBAAAA,CAAe,CAEfP,QAAAA,CAAO,CAEPQ,WAAAA,CAAU,CACX,GAIC,MACE,WAAC,OAAI,UAAWhL,EAAAA,CAAAA,CAAAA,qBAAuB,C,UACrC,WAAC,OAAI,UAAWA,EAAAA,CAAAA,CAAAA,kBAAoB,C,UAClC,UAAC,OACC,UAAW2F,IAAW3F,EAAAA,CAAAA,CAAAA,uBAAyB,CAAE,CAC/C,CAACA,EAAAA,CAAAA,CAAAA,8BAAgC,CAAC,CAChC6K,IAAcV,EAAAA,OAAmB,AACrC,GACA,QAAS,IAAMW,EAAaX,EAAAA,OAAmB,E,SAChD,S,GAGD,UAAC,OAAI,UAAWnK,EAAAA,CAAAA,CAAAA,uBAAyB,A,GACzC,UAAC,OACC,UAAW2F,IAAW3F,EAAAA,CAAAA,CAAAA,uBAAyB,CAAE,CAC/C,CAACA,EAAAA,CAAAA,CAAAA,8BAAgC,CAAC,CAChC6K,IAAcV,EAAAA,QAAoB,AACtC,GACA,QAAS,IAAMW,EAAaX,EAAAA,QAAoB,E,SACjD,U,MAIFU,IAAcV,EAAAA,QAAoB,EAAKY,EAUpC,KATF,WAACjD,EAAAA,CAAKA,CAAAA,CAAC,QAAS,E,UACd,UAAC,Q,SAAK,c,GACN,UAACmD,EAAAA,GAAYA,CAAAA,CACX,UAAWtF,IAAW3F,EAAAA,CAAAA,CAAAA,IAAM,CAAE,CAC5B,CAACA,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAEwK,CACZ,GACA,QAjCoB,KAC5BQ,EAAW,CAACR,EACd,C,QAqCF,EAEMU,EAAwC,AAAC,I,GAAA,CAAEC,SAAAA,CAAQ,CAAE,G,MACzD,UAAC,OAAI,UAAWnL,EAAAA,CAAAA,CAAAA,kBAAoB,C,SAAGmL,C,IAKnCC,EAED,AAAC,I,GAAA,CAAEC,gBAAAA,CAAe,CAAE,GACjB,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,WAAAA,CAAU,CAAEC,QAAAA,CAAO,CAAE,CAAGJ,EAC7C,CAACR,EAAWC,EAAa,CAAGxM,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,GAErC,CAACkM,EAASQ,EAAW,CAAG1M,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IACvC,MACE,sB,SACG,AAACgN,GAAYC,EAOZ,WAAC,OAAI,UAAWvL,EAAAA,CAAAA,CAAAA,uBAAyB,C,UACvC,UAAC4K,EAAAA,CACC,UAAWC,EACX,aAAcC,EACd,gBAAiB,CAAE,EAACU,GAAcC,CAAM,EACxC,QAASjB,EACT,WAAYQ,C,GAEbH,AAAc,IAAdA,EACC,sB,SACE,UAAC,OAAI,UAAW7K,EAAAA,CAAAA,CAAAA,kBAAoB,C,SAClC,UAACoK,EAAQA,CACP,SACEkB,EACI5L,KAAK,SAAS,CACZA,KAAK,KAAK,CAAC4L,GAAW,MACtB,KAjCC,GAoCH,GAEN,YAAaT,EACb,QAASL,C,OAKf,sB,SACE,UAAC,OAAI,UAAWxK,EAAAA,CAAAA,CAAAA,kBAAoB,C,SACjCwL,EACC,UAAC,OAAI,UAAWxL,EAAAA,CAAAA,CAAAA,mBAAqB,C,SACnC,UAAC0L,EAAAA,EAAMA,CAAAA,CACL,UAAW1L,EAAAA,CAAAA,CAAAA,eAAiB,CAC5B,SAAU,GACV,KAAM,KACN,UAAW,KACX,KAAK,SACL,YACE,WAAC,O,UACC,UAAC,O,SAAKnC,EAAAA,CAAAA,CAAAA,CAAM,CAAC,yB,GACb,UAAC,OAAI,MAAO,CAAE,UAAW,YAAa,E,SACnC2N,C,UAOX,UAACpB,EAAQA,CACP,YAAaS,EACb,SAAUnL,KAAK,SAAS,CACtBA,KAAK,KAAK,CAAC6L,GAAY,MACvB,KArEK,GAwEP,YAAa7L,KAAK,SAAS,CACzBA,KAAK,KAAK,CAAC+L,GAAW,MACtB,KA1EK,GA6EP,QAASjB,C,UAlErB,UAAC,OAAI,UAAWxK,EAAAA,CAAAA,CAAAA,kBAAoB,C,SAClC,UAAC,OAAI,UAAWA,EAAAA,CAAAA,CAAAA,0BAA4B,C,SACzCnC,EAAAA,CAAAA,CAAAA,CAAM,CAAC,wB,MA0EpB,EAEa8N,EAER,AAAC,I,GAAA,CAAEC,YAAAA,CAAW,CAAE,G,MACnB,UAACV,EAAAA,C,SACC,UAACE,EAAAA,CAAiB,gBAAiBQ,C,oBCnJjC,CAAE5H,KAAAA,CAAI,CAAE,CAAGC,EAAAA,EAAUA,CAGrB4H,EAAc,CAACC,EAAY3G,EAAM4G,IACrC,WAAC/H,EAAAA,CACC,UAAWhE,EAAAA,CAAAA,CAAAA,aAAe,CAC1B,SAAU,CACR,YAAa,CACX,KAAM,CACJ,QAAU,GAAgBmF,MAAAA,CAAd2G,EAAW,KAAQ,OAAL3G,GAC1B,MAAO,CAAE,UAAW,YAAa,CACnC,CACF,CACF,E,UAEC2G,EAAW,IAAE3G,EAAK,IAAEtH,EAAAA,CAAAA,CAAAA,CAAM,CAACkO,G,GAInBC,EAYR,AAAC,I,GAAA,CACJjO,SAAAA,CAAQ,CACRkO,QAAAA,CAAO,CACPxO,SAAAA,CAAQ,CACRC,MAAAA,CAAK,CACLoO,WAAAA,CAAU,CACVI,eAAAA,CAAc,CACdC,aAAAA,CAAY,CACZC,gBAAAA,CAAe,CACfC,cAAAA,EAAgB,EAAK,CACrBlO,WAAAA,CAAU,CACVmO,kBAAAA,CAAiB,CAClB,GACO,CAACV,EAAaW,EAAe,CAAGjO,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAA2B,CAAC,GAC5D,CAACkO,EAAQC,EAAU,CAAGnO,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,IAoC5B,MAbAyG,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACJoH,EACFI,EAAe,mBACVX,GAAAA,CACH,QAASO,MAAAA,EAAAA,KAAAA,EAAAA,EAAc,WAAW,CAClC,SAAUA,MAAAA,EAAAA,KAAAA,EAAAA,EAAc,YAAY,CACpC,WAAY,E,IAGdI,EAAe,CAAC,EAEpB,EAAG,CAACJ,EAAa,EAGf,UAAC,OACC,UAAWnM,EAAAA,CAAAA,CAAAA,cAAgB,CAC3B,cAAY,kC,SAEZ,WAAC0M,EAAAA,EAAGA,CAAAA,CAAC,OAAQ,G,UACX,UAACC,EAAAA,EAAGA,CAAAA,CAAC,KAAM,G,SACT,WAAC,OAAI,UAAW3M,EAAAA,CAAAA,CAAAA,iBAAmB,C,UACjC,UAAC,OAAI,UAAWA,EAAAA,CAAAA,CAAAA,cAAgB,C,SAC7BqM,EACC,UAACrI,EAAAA,CAAK,UAAWhE,EAAAA,CAAAA,CAAAA,aAAe,C,SAC7BnC,EAAAA,CAAAA,CAAAA,CAAM,CAAC,0B,GAGVgO,EAAYC,EAAYG,EAAQ,IAAI,CAAE,0B,GAG1C,UAAC,OACC,MAAO,CACL,UAAWI,EAAgB,oBAAsB,IACjD,QAAS,MACX,E,SAEA,UAAC9O,EAAAA,CAAWA,CAAAA,CACV,WAAYY,EACZ,SAAUJ,EACV,SAAUN,EACV,MAAOC,EACP,cAAeuO,MAAAA,EAAAA,KAAAA,EAAAA,EAAS,cAAc,CACtC,SAhEO,AAAC,I,GAAA,CACpB,OAAQW,CAAW,CACnBtB,QAAAA,CAAO,CACPC,SAAAA,CAAQ,CACRC,WAAAA,CAAU,CACVC,QAAAA,CAAO,CACU,GACjBgB,EAAUG,GACVL,EAAe,CACb,OAAQK,EACRtB,QAAAA,EACAC,SAAAA,EACAC,WAAAA,EACAC,QAAAA,CACF,GACAS,MAAAA,GAAAA,EAAiBU,GACjBA,IAAgBjN,EAAAA,CAAAA,CAAAA,IAAW,EACzByM,CAAAA,MAAAA,GAAAA,EAAkB,CAAE,YAAad,EAAS,aAAcC,CAAS,EAAC,EAEpEqB,IAAgBjN,EAAAA,CAAAA,CAAAA,IAAW,EAAI2M,CAAAA,MAAAA,GAAAA,GAAAA,CACjC,EA6Cc,mBAAoBL,MAAAA,EAAAA,KAAAA,EAAAA,EAAS,oBAAoB,CACjD,eAAgB,CAACI,C,UAKzB,UAACM,EAAAA,EAAGA,CAAAA,CAAC,KAAM,G,SACT,WAAC,OAAI,UAAW3M,EAAAA,CAAAA,CAAAA,iBAAmB,C,UACjC,UAAC,OAAI,UAAWA,EAAAA,CAAAA,CAAAA,cAAgB,C,SAC9B,WAAC8H,EAAAA,CAAKA,CAAAA,CAAC,MAAO,CAAE,MAAO,MAAO,E,UAC3BuE,EACC,UAACrI,EAAAA,CAAK,UAAWhE,EAAAA,CAAAA,CAAAA,aAAe,C,SAC7BnC,EAAAA,CAAAA,CAAAA,CAAM,CAAC,8C,GAGVgO,EACEC,EACAG,EAAQ,IAAI,CACZ,4BAGHO,IAAW7M,EAAAA,CAAAA,CAAAA,IAAW,EACrB,UAACoI,EAAAA,EAAKA,CAAAA,CAAC,MAAM,Q,SAASlK,EAAAA,CAAAA,CAAAA,CAAM,CAAC,uB,GAE9B2O,IAAW7M,EAAAA,CAAAA,CAAAA,IAAW,EACrB,UAACoI,EAAAA,EAAKA,CAAAA,CAAC,MAAM,M,SAAOlK,EAAAA,CAAAA,CAAAA,CAAM,CAAC,yB,QAIjC,UAAC,OACC,UAAWmC,EAAAA,CAAAA,CAAAA,mBAAqB,CAChC,MAAO,CACL,OAAQqM,EAAgB,OAAS,GACnC,E,SAEA,UAACV,EAAUA,CAAC,YAAaC,C,cAOvC,C,sKC9KInL,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGC,KAAAA,C,uKCf/DD,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGC,KAAAA,C,8DCvB/DmM,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,2CAA4C,GAAG,EAExFD,EAAwB,MAAM,CAAG,CAChC,KAAQ,aACT,EACA,IAAeA,C,8DCPXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,wNAAyN,GAAG,EAErQD,EAAwB,MAAM,CAAG,CAChC,YAAa,mBACb,SAAY,mBACZ,iBAAkB,wBAClB,aAAgB,uBACjB,EACA,IAAeA,C,8DCVXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,0wIAA2wI,GAAG,EAEvzID,EAAwB,MAAM,CAAG,CAChC,kBAAmB,yBACnB,eAAkB,yBAClB,uBAAwB,8BACxB,mBAAsB,8BACtB,kBAAmB,yBACnB,cAAiB,yBACjB,kBAAmB,yBACnB,cAAiB,yBACjB,0BAA2B,iCAC3B,qBAAwB,iCACxB,mBAAoB,0BACpB,eAAkB,0BAClB,aAAc,oBACd,UAAa,oBACb,YAAa,mBACb,SAAY,mBACZ,YAAa,mBACb,SAAY,mBACZ,UAAW,iBACX,OAAU,iBACV,UAAW,iBACX,OAAU,iBACV,aAAc,oBACd,UAAa,oBACb,WAAY,kBACZ,QAAW,kBACX,qBAAsB,4BACtB,iBAAoB,4BACpB,MAAS,eACT,qBAAsB,4BACtB,iBAAoB,4BACpB,kBAAmB,yBACnB,cAAiB,yBACjB,uBAAwB,8BACxB,kBAAqB,8BACrB,uBAAwB,8BACxB,kBAAqB,8BACrB,8BAA+B,qCAC/B,wBAA2B,qCAC3B,KAAQ,cACR,KAAQ,aACT,EACA,IAAeA,C,8DC/CXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,woNAAyoN,GAAG,EAErrND,EAAwB,MAAM,CAAG,CAChC,eAAgB,sBAChB,YAAe,sBACf,YAAa,mBACb,SAAY,mBACZ,cAAe,qBACf,WAAc,qBACd,uBAAwB,8BACxB,mBAAsB,8BACtB,UAAW,iBACX,OAAU,iBACV,oBAAqB,2BACrB,eAAkB,2BAClB,gBAAiB,uBACjB,aAAgB,uBAChB,aAAc,oBACd,UAAa,oBACb,iBAAkB,wBAClB,cAAiB,wBACjB,kBAAmB,yBACnB,eAAkB,yBAClB,sBAAuB,6BACvB,kBAAqB,6BACrB,uBAAwB,8BACxB,mBAAsB,8BACtB,YAAa,mBACb,SAAY,mBACZ,iBAAkB,wBAClB,aAAgB,wBAChB,KAAQ,cACR,oBAAqB,2BACrB,gBAAmB,2BACnB,uBAAwB,8BACxB,mBAAsB,8BACtB,sBAAuB,6BACvB,iBAAoB,6BACpB,gBAAiB,uBACjB,aAAgB,uBAChB,mBAAoB,0BACpB,eAAkB,0BAClB,cAAe,qBACf,WAAc,qBACd,OAAU,gBACV,mBAAoB,0BACpB,eAAkB,0BAClB,cAAe,qBACf,WAAc,qBACd,iBAAkB,wBAClB,cAAiB,wBACjB,cAAe,qBACf,WAAc,qBACd,aAAc,oBACd,UAAa,oBACb,mBAAoB,0BACpB,eAAkB,0BAClB,mBAAoB,0BACpB,eAAkB,0BAClB,iBAAkB,wBAClB,aAAgB,wBAChB,mBAAoB,0BACpB,eAAkB,0BAClB,oBAAqB,2BACrB,gBAAmB,2BACnB,gBAAiB,uBACjB,aAAgB,uBAChB,oBAAqB,2BACrB,iBAAoB,0BACrB,EACA,IAAeA,C"}