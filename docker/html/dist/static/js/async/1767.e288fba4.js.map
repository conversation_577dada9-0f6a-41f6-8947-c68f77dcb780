{"version": 3, "file": "static/js/async/1767.e288fba4.js", "sources": ["webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/latex.mjs", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/r.mjs", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/tex.mjs"], "sourcesContent": ["import tex from './tex.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"LaTeX\\\",\\\"name\\\":\\\"latex\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\\\\\\\\\(?:[@\\\\\\\\w]|[@\\\\\\\\w]{2}|[@\\\\\\\\w]{3}|[@\\\\\\\\w]{4}|[@\\\\\\\\w]{5}|[@\\\\\\\\w]{6}))\\\\\\\\s\\\",\\\"name\\\":\\\"meta.space-after-command.latex\\\"},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)(?:usepackage|documentclass))\\\\\\\\b(?=[\\\\\\\\[{])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preamble.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"name\\\":\\\"meta.preamble.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg\\\"},{\\\"begin\\\":\\\"((?:\\\\\\\\G|(?<=]))\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"support.class.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)in(?:clude|put))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.include.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"name\\\":\\\"meta.include.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)((?:sub){0,2}section|(?:sub)?paragraph|chapter|part|addpart|addchap|addsec|minisec|frametitle)\\\\\\\\*?)((?:\\\\\\\\[[^\\\\\\\\[]*?]){0,2})(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.section.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-bracket\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"entity.name.section.latex\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"name\\\":\\\"meta.function.section.$3.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{songs}\\\\\\\\{.*})\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"contentName\\\":\\\"meta.data.environment.songs.latex\\\",\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\end\\\\\\\\{songs}(?:\\\\\\\\s*\\\\\\\\n)?)\\\",\\\"name\\\":\\\"meta.function.environment.songs.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#songs-chords\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*((\\\\\\\\\\\\\\\\)beginsong)(?=\\\\\\\\{)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.be.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"end\\\":\\\"((\\\\\\\\\\\\\\\\)endsong)(?:\\\\\\\\s*\\\\\\\\n)?\\\",\\\"name\\\":\\\"meta.function.environment.song.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-arg-no-highlight\\\"},{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=[]}]))\\\\\\\\s*\\\",\\\"contentName\\\":\\\"meta.data.environment.song.latex\\\",\\\"end\\\":\\\"\\\\\\\\s*(?=\\\\\\\\\\\\\\\\endsong)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#songs-chords\\\"}]}]},{\\\"begin\\\":\\\"(?:^\\\\\\\\s*)?\\\\\\\\\\\\\\\\begin\\\\\\\\{(lstlisting|minted|pyglist)}(?=[\\\\\\\\[{])\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\\\\\\\\\end\\\\\\\\{\\\\\\\\1}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(asy(?:|mptote))(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.asy\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.asy\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(bash)(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.shell\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.shell\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(c(?:|pp))(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.cpp.embedded.latex\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cpp.embedded.latex\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(css)(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.css\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(gnuplot)(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.gnuplot\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.gnuplot\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(h(?:s|askell))(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.haskell\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.haskell\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(html)(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"text.html\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(java)(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.java\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.java\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(j(?:l|ulia))(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.julia\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.julia\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(j(?:s|avascript))(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.js\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(lua)(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.lua\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lua\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(py|python|sage)(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.python\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(r(?:b|uby))(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.ruby\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ruby\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(rust)(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.rust\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.rust\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(t(?:s|ypescript))(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.ts\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(xml)(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"text.xml\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)(yaml)(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"source.yaml\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:minted|lstlisting|pyglist)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.yaml\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)([A-Za-z]*)(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"meta.function.embedded.latex\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:lstlisting|minted|pyglist)})\\\",\\\"name\\\":\\\"meta.embedded.block.generic.latex\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{asy(?:|code)\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{asy(?:|code)\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.asymptote\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{asy(?:|code)\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.asymptote\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{cppcode\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{cppcode\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.cpp.embedded.latex\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{cppcode\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cpp.embedded.latex\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{dot(?:2tex|code)\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{dot(?:2tex|code)\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.dot\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{dot(?:2tex|code)\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.dot\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{gnuplot\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{gnuplot\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.gnuplot\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{gnuplot\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.gnuplot\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{hscode\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{hscode\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.haskell\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{hscode\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.haskell\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{java(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{java(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.java\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{java(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.java\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{jl(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{jl(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.julia\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{jl(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.julia\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{julia(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{julia(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.julia\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{julia(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.julia\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{luacode\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{luacode\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.lua\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{luacode\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lua\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{py(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{py(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.python\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{py(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{pylab(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{pylab(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.python\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{pylab(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{(?:sageblock|sagesilent|sageverbatim|sageexample|sagecommandline|pythonq??|pythonrepl)\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{(?:sageblock|sagesilent|sageverbatim|sageexample|sagecommandline|pythonq??|pythonrepl)\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.python\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:sageblock|sagesilent|sageverbatim|sageexample|sagecommandline|pythonq??|pythonrepl)\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{scalacode\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{scalacode\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.scala\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{scalacode\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.scala\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{sympy(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?}(?:\\\\\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\\\\\[{]|\\\\\\\\s*$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{sympy(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}}},{\\\"begin\\\":\\\"^(?=\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.python\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{sympy(?:code|verbatim|block|concode|console|converbatim)\\\\\\\\*?})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{((?:[A-Za-z]*code|lstlisting|minted|pyglist)\\\\\\\\*?)}(?:\\\\\\\\[.*])?(?:\\\\\\\\{.*})?\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"contentName\\\":\\\"meta.function.embedded.latex\\\",\\\"end\\\":\\\"\\\\\\\\\\\\\\\\end\\\\\\\\{\\\\\\\\1}(?:\\\\\\\\s*\\\\\\\\n)?\\\",\\\"name\\\":\\\"meta.embedded.block.generic.latex\\\"},{\\\"begin\\\":\\\"((?:^\\\\\\\\s*)?\\\\\\\\\\\\\\\\begin\\\\\\\\{((?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?))})(?:\\\\\\\\[[^]]*]){0,2}(?=\\\\\\\\{)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\end\\\\\\\\{\\\\\\\\2})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:asy(?:|mptote))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.asy\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.asy\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:bash)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.shell\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.shell\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:c(?:|pp))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.cpp.embedded.latex\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cpp.embedded.latex\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:css)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.css\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:gnuplot)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.gnuplot\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.gnuplot\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:h(?:s|askell))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.haskell\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.haskell\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:html)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"text.html\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:java)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.java\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.java\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:j(?:l|ulia))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.julia\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.julia\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:j(?:s|avascript))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.js\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:lua)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.lua\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lua\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:py|python|sage)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.python\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:r(?:b|uby))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.ruby\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ruby\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:rust)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.rust\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.rust\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:t(?:s|ypescript))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.ts\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:xml)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"text.xml\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:yaml)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"source.yaml\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.yaml\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)(?:__|[a-z\\\\\\\\s]*)(?i:tikz(?:|picture))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"text.tex.latex\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\G(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(})\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)\\\",\\\"contentName\\\":\\\"meta.function.embedded.latex\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\\\\\*?|PlaceholderFromCode\\\\\\\\*?|SetPlaceholderCode\\\\\\\\*?)})\\\",\\\"name\\\":\\\"meta.embedded.block.generic.latex\\\"}]}]},{\\\"begin\\\":\\\"(?:^\\\\\\\\s*)?\\\\\\\\\\\\\\\\begin\\\\\\\\{(terminal\\\\\\\\*?)}(?=[\\\\\\\\[{])\\\",\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"\\\\\\\\\\\\\\\\end\\\\\\\\{\\\\\\\\1}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)([A-Za-z]*)(})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"meta.function.embedded.latex\\\",\\\"end\\\":\\\"^\\\\\\\\s*(?=\\\\\\\\\\\\\\\\end\\\\\\\\{terminal\\\\\\\\*?})\\\",\\\"name\\\":\\\"meta.embedded.block.generic.latex\\\"}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:asy(?:|mptote))\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.asy\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.asy\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:bash)\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.shell\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.shell\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:c(?:|pp))\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.cpp.embedded.latex\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.cpp.embedded.latex\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:css)\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.css\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.css\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:gnuplot)\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.gnuplot\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.gnuplot\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:h(?:s|askell))\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.haskell\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.haskell\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:html)\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"text.html\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:java)\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.java\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.java\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:j(?:l|ulia))\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.julia\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.julia\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:j(?:s|avascript))\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.js\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:lua)\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.lua\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.lua\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:py|python|sage)\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.python\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:r(?:b|uby))\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.ruby\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.ruby\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:rust)\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.rust\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.rust\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:t(?:s|ypescript))\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.ts\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:xml)\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"text.xml\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:yaml)\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.yaml\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.yaml\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=\\\\\\\\[(?i:tikz(?:|picture))\\\\\\\\b|\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"text.tex.latex\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)cacheMeCode)(?=[\\\\\\\\[{])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?<=])(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"meta.embedded.block.generic.latex\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"}]}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)addplot)\\\\\\\\+?(\\\\\\\\[[^\\\\\\\\[]*])*\\\\\\\\s*(gnuplot)\\\\\\\\s*(\\\\\\\\[[^\\\\\\\\[]*])*\\\\\\\\s*(\\\\\\\\{)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.be.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-bracket\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-bracket\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(};)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"%\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.latex\\\"}},\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.percentage.latex\\\"},{\\\"include\\\":\\\"source.gnuplot\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{((?:fboxv|boxedv|[Vv]|spv)erbatim\\\\\\\\*?)})\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"contentName\\\":\\\"markup.raw.verbatim.latex\\\",\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\end\\\\\\\\{\\\\\\\\2})\\\",\\\"name\\\":\\\"meta.function.verbatim.latex\\\"},{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{VerbatimOut}\\\\\\\\{[^}]*})\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"contentName\\\":\\\"markup.raw.verbatim.latex\\\",\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\end\\\\\\\\{VerbatimOut})\\\",\\\"name\\\":\\\"meta.function.verbatim.latex\\\"},{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{alltt})\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"contentName\\\":\\\"markup.raw.verbatim.latex\\\",\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\end\\\\\\\\{alltt})\\\",\\\"name\\\":\\\"meta.function.alltt.latex\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)[A-Za-z]+\\\",\\\"name\\\":\\\"support.function.general.latex\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{([Cc]omment)})\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"contentName\\\":\\\"comment.line.percentage.latex\\\",\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\end\\\\\\\\{\\\\\\\\2})\\\",\\\"name\\\":\\\"meta.function.verbatim.latex\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*((\\\\\\\\\\\\\\\\)h(?:ref|yperref|yperimage))(?=[\\\\\\\\[{])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.url.latex\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"name\\\":\\\"meta.function.hyperlink.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#multiline-optional-arg-no-highlight\\\"},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(\\\\\\\\{)([^}]*)(})(?:\\\\\\\\{[^}]*}){2}?(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.underline.link.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"meta.variable.parameter.function.latex\\\",\\\"end\\\":\\\"(?=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=]))(?:(\\\\\\\\{)[^}]*(}))?(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"meta.variable.parameter.function.latex\\\",\\\"end\\\":\\\"(?=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.url.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"4\\\":{\\\"name\\\":\\\"markup.underline.link.latex\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"match\\\":\\\"\\\\\\\\s*((\\\\\\\\\\\\\\\\)(?:url|path))(\\\\\\\\{)([^}]*)(})\\\",\\\"name\\\":\\\"meta.function.link.url.latex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{document})\\\",\\\"name\\\":\\\"meta.function.begin-document.latex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{document})\\\",\\\"name\\\":\\\"meta.function.end-document.latex\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*((\\\\\\\\\\\\\\\\)begin)(\\\\\\\\{)((?:\\\\\\\\+?array|equation|(?:IEEE)?eqnarray|multline|align|aligned|alignat|alignedat|flalign|flaligned|flalignat|split|gather|gathered|\\\\\\\\+?cases|(?:display)?math|\\\\\\\\+?[A-Za-z]*matrix|[BVbpv]?NiceMatrix|[BVbpv]?NiceArray|(?:arg)?m(?:ini|axi))[!*]?)(})(\\\\\\\\s*\\\\\\\\n)?\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.be.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"contentName\\\":\\\"meta.math.block.latex support.class.math.block.environment.latex\\\",\\\"end\\\":\\\"\\\\\\\\s*((\\\\\\\\\\\\\\\\)end)(\\\\\\\\{)(\\\\\\\\4)(})(?:\\\\\\\\s*\\\\\\\\n)?\\\",\\\"name\\\":\\\"meta.function.environment.math.latex\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)&\\\",\\\"name\\\":\\\"keyword.control.equation.align.latex\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"keyword.control.equation.newline.latex\\\"},{\\\"include\\\":\\\"#definition-label\\\"},{\\\"include\\\":\\\"text.tex#math-content\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\\\\\\\\\begin\\\\\\\\{empheq}(?:\\\\\\\\[.*])?)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"contentName\\\":\\\"meta.math.block.latex support.class.math.block.environment.latex\\\",\\\"end\\\":\\\"\\\\\\\\s*(\\\\\\\\\\\\\\\\end\\\\\\\\{empheq})\\\",\\\"name\\\":\\\"meta.function.environment.math.latex\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)&\\\",\\\"name\\\":\\\"keyword.control.equation.align.latex\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"keyword.control.equation.newline.latex\\\"},{\\\"include\\\":\\\"#definition-label\\\"},{\\\"include\\\":\\\"text.tex#math-content\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{(tabular[*xy]?|xltabular|longtable|(?:long)?tabu|(?:long|tall)?tblr|NiceTabular[*X]?|booktabs)}(\\\\\\\\s*\\\\\\\\n)?)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"contentName\\\":\\\"meta.data.environment.tabular.latex\\\",\\\"end\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\end\\\\\\\\{(\\\\\\\\2)}(?:\\\\\\\\s*\\\\\\\\n)?)\\\",\\\"name\\\":\\\"meta.function.environment.tabular.latex\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)&\\\",\\\"name\\\":\\\"keyword.control.table.cell.latex\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"keyword.control.table.newline.latex\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{(itemize|enumerate|description|list)})\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\end\\\\\\\\{\\\\\\\\2}(?:\\\\\\\\s*\\\\\\\\n)?)\\\",\\\"name\\\":\\\"meta.function.environment.list.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{tikzpicture})\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\end\\\\\\\\{tikzpicture}(?:\\\\\\\\s*\\\\\\\\n)?)\\\",\\\"name\\\":\\\"meta.function.environment.latex.tikz\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{frame})\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\end\\\\\\\\{frame})\\\",\\\"name\\\":\\\"meta.function.environment.frame.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{(mpost\\\\\\\\*?)})\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\end\\\\\\\\{\\\\\\\\2}(?:\\\\\\\\s*\\\\\\\\n)?)\\\",\\\"name\\\":\\\"meta.function.environment.latex.mpost\\\"},{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{markdown})\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"contentName\\\":\\\"meta.embedded.markdown_latex_combined\\\",\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\end\\\\\\\\{markdown})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.markdown_latex_combined\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\\\\\\\\\begin\\\\\\\\{(\\\\\\\\p{Alphabetic}+\\\\\\\\*?)})\\\",\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#begin-env-tokenizer\\\"}]}},\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\end\\\\\\\\{\\\\\\\\2}(?:\\\\\\\\s*\\\\\\\\n)?)\\\",\\\"name\\\":\\\"meta.function.environment.general.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.begin.latex\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.function.general.latex\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.end.latex\\\"}},\\\"match\\\":\\\"((\\\\\\\\\\\\\\\\)(?:newcommand|renewcommand|(?:re)?newrobustcmd|DeclareRobustCommand))\\\\\\\\*?(\\\\\\\\{)((\\\\\\\\\\\\\\\\)[^}]*)(})\\\"},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)marginpar)((?:\\\\\\\\[[^\\\\\\\\[]*?])*)(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.marginpar.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-bracket\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.marginpar.begin.latex\\\"}},\\\"contentName\\\":\\\"meta.paragraph.margin.latex\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.marginpar.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)footnote)((?:\\\\\\\\[[^\\\\\\\\[]*?])*)(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.footnote.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-bracket\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.footnote.begin.latex\\\"}},\\\"contentName\\\":\\\"entity.name.footnote.latex\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.footnote.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)emph)(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.emph.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.emph.begin.latex\\\"}},\\\"contentName\\\":\\\"markup.italic.emph.latex\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.emph.end.latex\\\"}},\\\"name\\\":\\\"meta.function.emph.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)textit)(\\\\\\\\{)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.textit.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.textit.begin.latex\\\"}},\\\"contentName\\\":\\\"markup.italic.textit.latex\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.textit.end.latex\\\"}},\\\"name\\\":\\\"meta.function.textit.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)textbf)(\\\\\\\\{)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.textbf.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.textbf.begin.latex\\\"}},\\\"contentName\\\":\\\"markup.bold.textbf.latex\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.textbf.end.latex\\\"}},\\\"name\\\":\\\"meta.function.textbf.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)texttt)(\\\\\\\\{)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.texttt.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.texttt.begin.latex\\\"}},\\\"contentName\\\":\\\"markup.raw.texttt.latex\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.texttt.end.latex\\\"}},\\\"name\\\":\\\"meta.function.texttt.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#braces\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.item.latex\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.latex\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)item\\\\\\\\b\\\",\\\"name\\\":\\\"meta.scope.item.latex\\\"},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)(?:[Aa]uto|foot|full|no|ref|short|[Tt]ext|[Pp]aren|[Ss]mart)?[Cc]ite(?:al)?(?:[pst]|author|year(?:par)?|title)?[ANP]*\\\\\\\\*?)((?:(?:\\\\\\\\([^)]*\\\\\\\\)){0,2}(?:\\\\\\\\[[^]]*]){0,2}\\\\\\\\{[-.:_\\\\\\\\p{Alphabetic}\\\\\\\\p{N}]*})*)(<[^]<>]*>)?((?:\\\\\\\\[[^]]*])*)(\\\\\\\\{)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.cite.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.latex\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#autocites-arg\\\"}]},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-angle-no-highlight\\\"}]},\\\"5\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-bracket-no-highlight\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"name\\\":\\\"meta.citation.latex\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line.percentage.tex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.tex\\\"}},\\\"match\\\":\\\"((%).*)$\\\"},{\\\"match\\\":\\\"[-.:\\\\\\\\p{Alphabetic}\\\\\\\\p{N}]+\\\",\\\"name\\\":\\\"constant.other.reference.citation.latex\\\"}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)bibentry)(\\\\\\\\{)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.cite.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"name\\\":\\\"meta.citation.latex\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"[.:\\\\\\\\p{Alphabetic}\\\\\\\\p{N}]+\\\",\\\"name\\\":\\\"constant.other.reference.citation.latex\\\"}]},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)\\\\\\\\w*[Rr]ef\\\\\\\\*?)(?:\\\\\\\\[[^]]*])?(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.ref.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"name\\\":\\\"meta.reference.label.latex\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"[!*,-/:^_\\\\\\\\p{Alphabetic}\\\\\\\\p{N}]+\\\",\\\"name\\\":\\\"constant.other.reference.label.latex\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.ref.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.other.reference.label.latex\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"7\\\":{\\\"name\\\":\\\"constant.other.reference.label.latex\\\"},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"match\\\":\\\"((\\\\\\\\\\\\\\\\)\\\\\\\\w*[Rr]efrange\\\\\\\\*?)(?:\\\\\\\\[[^]]*])?(\\\\\\\\{)([!*,-/:^_\\\\\\\\p{Alphabetic}\\\\\\\\p{N}]+)(})(\\\\\\\\{)([!*,-/:^_\\\\\\\\p{Alphabetic}\\\\\\\\p{N}]+)(})\\\"},{\\\"include\\\":\\\"#definition-label\\\"},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)(?:[Vv]|spv)erb\\\\\\\\*?)\\\\\\\\s*((\\\\\\\\\\\\\\\\)scantokens)(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.begin.latex\\\"}},\\\"contentName\\\":\\\"markup.raw.verb.latex\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.end.latex\\\"}},\\\"name\\\":\\\"meta.function.verb.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"4\\\":{\\\"name\\\":\\\"markup.raw.verb.latex\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"}},\\\"match\\\":\\\"((\\\\\\\\\\\\\\\\)(?:[Vv]|spv)erb\\\\\\\\*?)\\\\\\\\s*((?<=\\\\\\\\s)\\\\\\\\S|[^A-Za-z])(.*?)(\\\\\\\\3|$)\\\",\\\"name\\\":\\\"meta.function.verb.latex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-bracket\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"7\\\":{\\\"name\\\":\\\"markup.raw.verb.latex\\\"},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"10\\\":{\\\"name\\\":\\\"markup.raw.verb.latex\\\"},\\\"11\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"}},\\\"match\\\":\\\"((\\\\\\\\\\\\\\\\)mint(?:|inline))((?:\\\\\\\\[[^\\\\\\\\[]*?])?)(\\\\\\\\{)[A-Za-z]*(})(?:([^A-Za-{])(.*?)(\\\\\\\\6)|(\\\\\\\\{)(.*?)(}))\\\",\\\"name\\\":\\\"meta.function.verb.latex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-bracket\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"5\\\":{\\\"name\\\":\\\"markup.raw.verb.latex\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"8\\\":{\\\"name\\\":\\\"markup.raw.verb.latex\\\"},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"}},\\\"match\\\":\\\"((\\\\\\\\\\\\\\\\)[a-z]+inline)((?:\\\\\\\\[[^\\\\\\\\[]*?])?)(?:([^A-Za-{])(.*?)(\\\\\\\\4)|(\\\\\\\\{)(.*?)(}))\\\",\\\"name\\\":\\\"meta.function.verb.latex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-bracket\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"5\\\":{\\\"name\\\":\\\"source.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"8\\\":{\\\"name\\\":\\\"source.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"}},\\\"match\\\":\\\"((\\\\\\\\\\\\\\\\)(?:(?:py|pycon|pylab|pylabcon|sympy|sympycon)[cv]?|pyq|pycq|pyif))((?:\\\\\\\\[[^\\\\\\\\[]*?])?)(?:([^A-Za-{])(.*?)(\\\\\\\\4)|(\\\\\\\\{)(.*?)(}))\\\",\\\"name\\\":\\\"meta.function.verb.latex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-bracket\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"5\\\":{\\\"name\\\":\\\"source.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.julia\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"},\\\"8\\\":{\\\"name\\\":\\\"source.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.julia\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.verb.latex\\\"}},\\\"match\\\":\\\"((\\\\\\\\\\\\\\\\)j(?:l|ulia)[cv]?)((?:\\\\\\\\[[^\\\\\\\\[]*?])?)(?:([^A-Za-{])(.*?)(\\\\\\\\4)|(\\\\\\\\{)(.*?)(}))\\\",\\\"name\\\":\\\"meta.function.verb.latex\\\"},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)(?:directlua|luadirect|luaexec))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.verb.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"contentName\\\":\\\"source.lua\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.lua\\\"}]},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:newline|pagebreak|clearpage|linebreak|pause)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.layout.latex\\\"},{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.latex\\\"}},\\\"end\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.latex\\\"}},\\\"name\\\":\\\"meta.math.block.latex support.class.math.block.environment.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#math-content\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\$\\\\\\\\$\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.latex\\\"}},\\\"end\\\":\\\"\\\\\\\\$\\\\\\\\$\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.latex\\\"}},\\\"name\\\":\\\"meta.math.block.latex support.class.math.block.environment.latex\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\$\\\",\\\"name\\\":\\\"constant.character.escape.latex\\\"},{\\\"include\\\":\\\"text.tex#math-content\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\$\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.tex\\\"}},\\\"end\\\":\\\"\\\\\\\\$\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.tex\\\"}},\\\"name\\\":\\\"meta.math.block.tex support.class.math.block.tex\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\$\\\",\\\"name\\\":\\\"constant.character.escape.latex\\\"},{\\\"include\\\":\\\"text.tex#math-content\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.latex\\\"}},\\\"end\\\":\\\"\\\\\\\\\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.latex\\\"}},\\\"name\\\":\\\"meta.math.block.latex support.class.math.block.environment.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex#math-content\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.latex\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)(text(s(terling|ixoldstyle|urd|e(ction|venoldstyle|rvicemark))|yen|n(ineoldstyle|umero|aira)|c(ircledP|o(py(left|right)|lonmonetary)|urrency|e(nt(oldstyle)?|lsius))|t(hree(superior|oldstyle|quarters(emdash)?)|i(ldelow|mes)|w(o(superior|oldstyle)|elveudash)|rademark)|interrobang(down)?|zerooldstyle|o(hm|ne(superior|half|oldstyle|quarter)|penbullet|rd((?:femin|mascul)ine))|d(i(scount|ed|v(orced)?)|o(ng|wnarrow|llar(oldstyle)?)|egree|agger(dbl)?|blhyphen(char)?)|uparrow|p(ilcrow|e(so|r(t((?:|ent)housand)|iodcentered))|aragraph|m)|e(stimated|ightoldstyle|uro)|quotes(traight((?:dbl|)base)|ingle)|f(iveoldstyle|ouroldstyle|lorin|ractionsolidus)|won|l(not|ira|e(ftarrow|af)|quill|angle|brackdbl)|a(s(cii(caron|dieresis|acute|grave|macron|breve)|teriskcentered)|cutedbl)|r(ightarrow|e(cipe|ferencemark|gistered)|quill|angle|brackdbl)|g(uarani|ravedbl)|m(ho|inus|u(sicalnote)?|arried)|b(igcircle|orn|ullet|lank|a(ht|rdbl)|rokenbar)))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.character.latex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.latex\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)(?:[cgl]_+[@_\\\\\\\\p{Alphabetic}]+_[a-z]+|[qs]_[@_\\\\\\\\p{Alphabetic}]+[@\\\\\\\\p{Alphabetic}])\\\",\\\"name\\\":\\\"variable.other.latex3.latex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.column-specials.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.column-specials.end.latex\\\"}},\\\"match\\\":\\\"[<>](\\\\\\\\{)\\\\\\\\$(})\\\",\\\"name\\\":\\\"meta.column-specials.latex\\\"},{\\\"include\\\":\\\"text.tex\\\"}],\\\"repository\\\":{\\\"autocites-arg\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-parenthesis-no-highlight\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-bracket-no-highlight\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.other.reference.citation.latex\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#autocites-arg\\\"}]}},\\\"match\\\":\\\"((?:\\\\\\\\([^)]*\\\\\\\\)){0,2})((?:\\\\\\\\[[^]]*]){0,2})(\\\\\\\\{)([-.:_\\\\\\\\p{Alphabetic}\\\\\\\\p{N}]+)(})(.*)\\\"}]},\\\"begin-env-tokenizer\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.be.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.begin.latex\\\"},\\\"7\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.end.latex\\\"},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"},\\\"10\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"11\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"match\\\":\\\"\\\\\\\\s*((\\\\\\\\\\\\\\\\)(?:begin|end))(\\\\\\\\{)(\\\\\\\\p{Alphabetic}+\\\\\\\\*?)(})(?:(\\\\\\\\[)([^]]*)(])){0,2}(?:(\\\\\\\\{)([^{}]*)(}))?\\\"},\\\"definition-label\\\":{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)z?label)((?:\\\\\\\\[[^\\\\\\\\[]*?])*)(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.label.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.latex\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#optional-arg-bracket\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"name\\\":\\\"meta.definition.label.latex\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"[!*,-/:^_\\\\\\\\p{Alphabetic}\\\\\\\\p{N}]+\\\",\\\"name\\\":\\\"variable.parameter.definition.label.latex\\\"}]},\\\"multiline-arg-no-highlight\\\":{\\\"begin\\\":\\\"\\\\\\\\G\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.latex\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.latex\\\"}},\\\"name\\\":\\\"meta.parameter.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"multiline-optional-arg\\\":{\\\"begin\\\":\\\"\\\\\\\\G\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.begin.latex\\\"}},\\\"contentName\\\":\\\"variable.parameter.function.latex\\\",\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.end.latex\\\"}},\\\"name\\\":\\\"meta.parameter.optional.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"multiline-optional-arg-no-highlight\\\":{\\\"begin\\\":\\\"(?:\\\\\\\\G|(?<=}))\\\\\\\\s*\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.begin.latex\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.end.latex\\\"}},\\\"name\\\":\\\"meta.parameter.optional.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"optional-arg-angle-no-highlight\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.end.latex\\\"}},\\\"match\\\":\\\"(<)[^<]*?(>)\\\",\\\"name\\\":\\\"meta.parameter.optional.latex\\\"}]},\\\"optional-arg-bracket\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.end.latex\\\"}},\\\"match\\\":\\\"(\\\\\\\\[)([^\\\\\\\\[]*?)(])\\\",\\\"name\\\":\\\"meta.parameter.optional.latex\\\"}]},\\\"optional-arg-bracket-no-highlight\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.end.latex\\\"}},\\\"match\\\":\\\"(\\\\\\\\[)[^\\\\\\\\[]*?(])\\\",\\\"name\\\":\\\"meta.parameter.optional.latex\\\"}]},\\\"optional-arg-parenthesis\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.latex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.end.latex\\\"}},\\\"match\\\":\\\"(\\\\\\\\()([^(]*?)(\\\\\\\\))\\\",\\\"name\\\":\\\"meta.parameter.optional.latex\\\"}]},\\\"optional-arg-parenthesis-no-highlight\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.begin.latex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.optional.end.latex\\\"}},\\\"match\\\":\\\"(\\\\\\\\()[^(]*?(\\\\\\\\))\\\",\\\"name\\\":\\\"meta.parameter.optional.latex\\\"}]},\\\"songs-chords\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\[\\\",\\\"end\\\":\\\"]\\\",\\\"name\\\":\\\"meta.chord.block.latex support.class.chord.block.environment.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"match\\\":\\\"\\\\\\\\^\\\",\\\"name\\\":\\\"meta.chord.block.latex support.class.chord.block.environment.latex\\\"},{\\\"include\\\":\\\"$self\\\"}]}},\\\"scopeName\\\":\\\"text.tex.latex\\\",\\\"embeddedLangs\\\":[\\\"tex\\\"],\\\"embeddedLangsLazy\\\":[\\\"shellscript\\\",\\\"css\\\",\\\"gnuplot\\\",\\\"haskell\\\",\\\"html\\\",\\\"java\\\",\\\"julia\\\",\\\"javascript\\\",\\\"lua\\\",\\\"python\\\",\\\"ruby\\\",\\\"rust\\\",\\\"typescript\\\",\\\"xml\\\",\\\"yaml\\\",\\\"scala\\\"]}\"))\n\nexport default [\n...tex,\nlang\n]\n", "const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"R\\\",\\\"name\\\":\\\"r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#roxygen\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#accessor\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#storage-type\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"#function-declarations\\\"},{\\\"include\\\":\\\"#lambda-functions\\\"},{\\\"include\\\":\\\"#builtin-functions\\\"},{\\\"include\\\":\\\"#function-calls\\\"}],\\\"repository\\\":{\\\"accessor\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\$)(?=[.A-Z_a-z][.\\\\\\\\w]*|`[^`]+`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.accessor.dollar.r\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-calls\\\"}]},{\\\"begin\\\":\\\"(:::?)(?=[.A-Z_a-z][.\\\\\\\\w]*|`[^`]+`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.accessor.colons.r\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-calls\\\"}]}]},\\\"brackets\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parameters.begin.bracket.round.r\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parameters.end.bracket.round.r\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\[(?!\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.single.begin.r\\\"}},\\\"end\\\":\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.single.end.r\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\[\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.double.begin.r\\\"}},\\\"contentName\\\":\\\"meta.item-access.arguments.r\\\",\\\"end\\\":\\\"]]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.double.end.r\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.r\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.r\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]}]},\\\"builtin-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(?:(base)(::))?(abbreviate|abs|acosh??|activeBindingFunction|addNA|addTaskCallback|agrepl??|alist|all|all\\\\\\\\.equal|all\\\\\\\\.equal\\\\\\\\.character|all\\\\\\\\.equal\\\\\\\\.default|all\\\\\\\\.equal\\\\\\\\.environment|all\\\\\\\\.equal\\\\\\\\.envRefClass|all\\\\\\\\.equal\\\\\\\\.factor|all\\\\\\\\.equal\\\\\\\\.formula|all\\\\\\\\.equal\\\\\\\\.function|all\\\\\\\\.equal\\\\\\\\.language|all\\\\\\\\.equal\\\\\\\\.list|all\\\\\\\\.equal\\\\\\\\.numeric|all\\\\\\\\.equal\\\\\\\\.POSIXt|all\\\\\\\\.equal\\\\\\\\.raw|all\\\\\\\\.names|all\\\\\\\\.vars|allowInterrupts|any|anyDuplicated|anyDuplicated\\\\\\\\.array|anyDuplicated\\\\\\\\.data\\\\\\\\.frame|anyDuplicated\\\\\\\\.default|anyDuplicated\\\\\\\\.matrix|anyNA|anyNA\\\\\\\\.data\\\\\\\\.frame|anyNA\\\\\\\\.numeric_version|anyNA\\\\\\\\.POSIXlt|aperm|aperm\\\\\\\\.default|aperm\\\\\\\\.table|append|apply|Arg|args|array|array2DF|arrayInd|as\\\\\\\\.array|as\\\\\\\\.array\\\\\\\\.default|as\\\\\\\\.call|as\\\\\\\\.character|as\\\\\\\\.character\\\\\\\\.condition|as\\\\\\\\.character\\\\\\\\.Date|as\\\\\\\\.character\\\\\\\\.default|as\\\\\\\\.character\\\\\\\\.error|as\\\\\\\\.character\\\\\\\\.factor|as\\\\\\\\.character\\\\\\\\.hexmode|as\\\\\\\\.character\\\\\\\\.numeric_version|as\\\\\\\\.character\\\\\\\\.octmode|as\\\\\\\\.character\\\\\\\\.POSIXt|as\\\\\\\\.character\\\\\\\\.srcref|as\\\\\\\\.complex|as\\\\\\\\.data\\\\\\\\.frame|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.array|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.AsIs|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.character|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.complex|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.data\\\\\\\\.frame|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.Date|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.default|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.difftime|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.factor|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.integer|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.list|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.logical|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.matrix|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.model\\\\\\\\.matrix|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.noquote|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.numeric|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.numeric_version|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.ordered|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.POSIXct|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.POSIXlt|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.raw|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.table|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.ts|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.vector|as\\\\\\\\.Date|as\\\\\\\\.Date\\\\\\\\.character|as\\\\\\\\.Date\\\\\\\\.default|as\\\\\\\\.Date\\\\\\\\.factor|as\\\\\\\\.Date\\\\\\\\.numeric|as\\\\\\\\.Date\\\\\\\\.POSIXct|as\\\\\\\\.Date\\\\\\\\.POSIXlt|as\\\\\\\\.difftime|as\\\\\\\\.double|as\\\\\\\\.double\\\\\\\\.difftime|as\\\\\\\\.double\\\\\\\\.POSIXlt|as\\\\\\\\.environment|as\\\\\\\\.expression|as\\\\\\\\.expression\\\\\\\\.default|as\\\\\\\\.factor|as\\\\\\\\.function|as\\\\\\\\.function\\\\\\\\.default|as\\\\\\\\.hexmode|as\\\\\\\\.integer|as\\\\\\\\.list|as\\\\\\\\.list\\\\\\\\.data\\\\\\\\.frame|as\\\\\\\\.list\\\\\\\\.Date|as\\\\\\\\.list\\\\\\\\.default|as\\\\\\\\.list\\\\\\\\.difftime|as\\\\\\\\.list\\\\\\\\.environment|as\\\\\\\\.list\\\\\\\\.factor|as\\\\\\\\.list\\\\\\\\.function|as\\\\\\\\.list\\\\\\\\.numeric_version|as\\\\\\\\.list\\\\\\\\.POSIXct|as\\\\\\\\.list\\\\\\\\.POSIXlt|as\\\\\\\\.logical|as\\\\\\\\.logical\\\\\\\\.factor|as\\\\\\\\.matrix|as\\\\\\\\.matrix\\\\\\\\.data\\\\\\\\.frame|as\\\\\\\\.matrix\\\\\\\\.default|as\\\\\\\\.matrix\\\\\\\\.noquote|as\\\\\\\\.matrix\\\\\\\\.POSIXlt|as\\\\\\\\.name|as\\\\\\\\.null|as\\\\\\\\.null\\\\\\\\.default|as\\\\\\\\.numeric|as\\\\\\\\.numeric_version|as\\\\\\\\.octmode|as\\\\\\\\.ordered|as\\\\\\\\.package_version|as\\\\\\\\.pairlist|as\\\\\\\\.POSIXct|as\\\\\\\\.POSIXct\\\\\\\\.Date|as\\\\\\\\.POSIXct\\\\\\\\.default|as\\\\\\\\.POSIXct\\\\\\\\.numeric|as\\\\\\\\.POSIXct\\\\\\\\.POSIXlt|as\\\\\\\\.POSIXlt|as\\\\\\\\.POSIXlt\\\\\\\\.character|as\\\\\\\\.POSIXlt\\\\\\\\.Date|as\\\\\\\\.POSIXlt\\\\\\\\.default|as\\\\\\\\.POSIXlt\\\\\\\\.factor|as\\\\\\\\.POSIXlt\\\\\\\\.numeric|as\\\\\\\\.POSIXlt\\\\\\\\.POSIXct|as\\\\\\\\.qr|as\\\\\\\\.raw|as\\\\\\\\.single|as\\\\\\\\.single\\\\\\\\.default|as\\\\\\\\.symbol|as\\\\\\\\.table|as\\\\\\\\.table\\\\\\\\.default|as\\\\\\\\.vector|as\\\\\\\\.vector\\\\\\\\.data\\\\\\\\.frame|as\\\\\\\\.vector\\\\\\\\.factor|as\\\\\\\\.vector\\\\\\\\.POSIXlt|asinh??|asNamespace|asplit|asS3|asS4|assign|atan2??|atanh|attach|attachNamespace|attr|attr\\\\\\\\.all\\\\\\\\.equal|attributes|autoload|autoloader|backsolve|balancePOSIXlt|baseenv|basename|besselI|besselJ|besselK|besselY|beta|bindingIsActive|bindingIsLocked|bindtextdomain|bitwAnd|bitwNot|bitwOr|bitwShiftL|bitwShiftR|bitwXor|body|bquote|break|browser|browserCondition|browserSetDebug|browserText|builtins|by|by\\\\\\\\.data\\\\\\\\.frame|by\\\\\\\\.default|bzfile|c|c\\\\\\\\.Date|c\\\\\\\\.difftime|c\\\\\\\\.factor|c\\\\\\\\.noquote|c\\\\\\\\.numeric_version|c\\\\\\\\.POSIXct|c\\\\\\\\.POSIXlt|c\\\\\\\\.warnings|call|callCC|capabilities|casefold|cat|cbind|cbind\\\\\\\\.data\\\\\\\\.frame|ceiling|char\\\\\\\\.expand|character|charmatch|charToRaw|chartr|chkDots|chol|chol\\\\\\\\.default|chol2inv|choose|chooseOpsMethod|chooseOpsMethod\\\\\\\\.default|class|clearPushBack|close|close\\\\\\\\.connection|close\\\\\\\\.srcfile|close\\\\\\\\.srcfilealias|closeAllConnections|col|colMeans|colnames|colSums|commandArgs|comment|complex|computeRestarts|conditionCall|conditionCall\\\\\\\\.condition|conditionMessage|conditionMessage\\\\\\\\.condition|conflictRules|conflicts|Conj|contributors|cosh??|cospi|crossprod|Cstack_info|cummax|cummin|cumprod|cumsum|curlGetHeaders|cut|cut\\\\\\\\.Date|cut\\\\\\\\.default|cut\\\\\\\\.POSIXt|data\\\\\\\\.class|data\\\\\\\\.frame|data\\\\\\\\.matrix|date|debug|debuggingState|debugonce|declare|default\\\\\\\\.stringsAsFactors|delayedAssign|deparse1??|det|detach|determinant|determinant\\\\\\\\.matrix|dget|diag|diff|diff\\\\\\\\.Date|diff\\\\\\\\.default|diff\\\\\\\\.difftime|diff\\\\\\\\.POSIXt|difftime|digamma|dim|dim\\\\\\\\.data\\\\\\\\.frame|dimnames|dimnames\\\\\\\\.data\\\\\\\\.frame|dir|dir\\\\\\\\.create|dir\\\\\\\\.exists|dirname|do\\\\\\\\.call|dontCheck|double|dput|dQuote|drop|droplevels|droplevels\\\\\\\\.data\\\\\\\\.frame|droplevels\\\\\\\\.factor|dump|duplicated|duplicated\\\\\\\\.array|duplicated\\\\\\\\.data\\\\\\\\.frame|duplicated\\\\\\\\.default|duplicated\\\\\\\\.matrix|duplicated\\\\\\\\.numeric_version|duplicated\\\\\\\\.POSIXlt|duplicated\\\\\\\\.warnings|dyn\\\\\\\\.load|dyn\\\\\\\\.unload|dynGet|eapply|eigen|emptyenv|enc2native|enc2utf8|encodeString|Encoding|endsWith|enquote|env\\\\\\\\.profile|environment|environmentIsLocked|environmentName|errorCondition|eval|eval\\\\\\\\.parent|evalq|Exec|exists|exp|expand\\\\\\\\.grid|expm1|expression|extSoftVersion|factor|factorial|fifo|file|file\\\\\\\\.access|file\\\\\\\\.append|file\\\\\\\\.choose|file\\\\\\\\.copy|file\\\\\\\\.create|file\\\\\\\\.exists|file\\\\\\\\.info|file\\\\\\\\.link|file\\\\\\\\.mode|file\\\\\\\\.mtime|file\\\\\\\\.path|file\\\\\\\\.remove|file\\\\\\\\.rename|file\\\\\\\\.show|file\\\\\\\\.size|file\\\\\\\\.symlink|Filter|Find|find\\\\\\\\.package|findInterval|findPackageEnv|findRestart|floor|flush|flush\\\\\\\\.connection|for|force|forceAndCall|formals|format|format\\\\\\\\.AsIs|format\\\\\\\\.data\\\\\\\\.frame|format\\\\\\\\.Date|format\\\\\\\\.default|format\\\\\\\\.difftime|format\\\\\\\\.factor|format\\\\\\\\.hexmode|format\\\\\\\\.info|format\\\\\\\\.libraryIQR|format\\\\\\\\.numeric_version|format\\\\\\\\.octmode|format\\\\\\\\.packageInfo|format\\\\\\\\.POSIXct|format\\\\\\\\.POSIXlt|format\\\\\\\\.pval|format\\\\\\\\.summaryDefault|formatC|formatDL|forwardsolve|function|gamma|gc|gc\\\\\\\\.time|gcinfo|gctorture2??|get0??|getAllConnections|getCallingDLLe??|getConnection|getDLLRegisteredRoutines|getDLLRegisteredRoutines\\\\\\\\.character|getDLLRegisteredRoutines\\\\\\\\.DLLInfo|getElement|geterrmessage|getExportedValue|getHook|getLoadedDLLs|getNamespace|getNamespaceExports|getNamespaceImports|getNamespaceInfo|getNamespaceName|getNamespaceUsers|getNamespaceVersion|getNativeSymbolInfo|getOption|getRversion|getSrcLines|getTaskCallbackNames|gettextf??|getwd|gl|globalCallingHandlers|globalenv|gregexec|gregexpr|grepl??|grepRaw|grepv|grouping|gsub|gzcon|gzfile|I|iconv|iconvlist|icuGetCollate|icuSetCollate|identical|identity|if|ifelse|Im|importIntoEnv|infoRDS|inherits|integer|interaction|interactive|intersect|intToBits|intToUtf8|inverse\\\\\\\\.rle|invisible|invokeRestart|invokeRestartInteractively|is\\\\\\\\.array|is\\\\\\\\.atomic|is\\\\\\\\.call|is\\\\\\\\.character|is\\\\\\\\.complex|is\\\\\\\\.data\\\\\\\\.frame|is\\\\\\\\.double|is\\\\\\\\.element|is\\\\\\\\.environment|is\\\\\\\\.expression|is\\\\\\\\.factor|is\\\\\\\\.finite|is\\\\\\\\.finite\\\\\\\\.POSIXlt|is\\\\\\\\.function|is\\\\\\\\.infinite|is\\\\\\\\.infinite\\\\\\\\.POSIXlt|is\\\\\\\\.integer|is\\\\\\\\.language|is\\\\\\\\.list|is\\\\\\\\.loaded|is\\\\\\\\.logical|is\\\\\\\\.matrix|is\\\\\\\\.na|is\\\\\\\\.na\\\\\\\\.data\\\\\\\\.frame|is\\\\\\\\.na\\\\\\\\.numeric_version|is\\\\\\\\.na\\\\\\\\.POSIXlt|is\\\\\\\\.name|is\\\\\\\\.nan|is\\\\\\\\.nan\\\\\\\\.POSIXlt|is\\\\\\\\.null|is\\\\\\\\.numeric|is\\\\\\\\.numeric_version|is\\\\\\\\.numeric\\\\\\\\.Date|is\\\\\\\\.numeric\\\\\\\\.difftime|is\\\\\\\\.numeric\\\\\\\\.POSIXt|is\\\\\\\\.object|is\\\\\\\\.ordered|is\\\\\\\\.package_version|is\\\\\\\\.pairlist|is\\\\\\\\.primitive|is\\\\\\\\.qr|is\\\\\\\\.R|is\\\\\\\\.raw|is\\\\\\\\.recursive|is\\\\\\\\.single|is\\\\\\\\.symbol|is\\\\\\\\.table|is\\\\\\\\.unsorted|is\\\\\\\\.vector|isa|isatty|isBaseNamespace|isdebugged|isFALSE|isIncomplete|isNamespace|isNamespaceLoaded|ISOdate|ISOdatetime|isOpen|isRestart|isS4|isSeekable|isSymmetric|isSymmetric\\\\\\\\.matrix|isTRUE|jitter|julian|julian\\\\\\\\.Date|julian\\\\\\\\.POSIXt|kappa|kappa\\\\\\\\.default|kappa\\\\\\\\.lm|kappa\\\\\\\\.qr|kronecker|l10n_info|La_library|La_version|La\\\\\\\\.svd|labels|labels\\\\\\\\.default|lapply|lazyLoad|lazyLoadDBexec|lazyLoadDBfetch|lbeta|lchoose|length|length\\\\\\\\.POSIXlt|lengths|levels|levels\\\\\\\\.default|lfactorial|lgamma|libcurlVersion|library|library\\\\\\\\.dynam|library\\\\\\\\.dynam\\\\\\\\.unload|licence|license|list|list\\\\\\\\.dirs|list\\\\\\\\.files|list2DF|list2env|load|loadedNamespaces|loadingNamespaceInfo|loadNamespace|local|lockBinding|lockEnvironment|log|log10|log1p|log2|logb|logical|lower\\\\\\\\.tri|ls|make\\\\\\\\.names|make\\\\\\\\.unique|makeActiveBinding|Map|mapply|margin\\\\\\\\.table|marginSums|mat\\\\\\\\.or\\\\\\\\.vec|match|match\\\\\\\\.arg|match\\\\\\\\.call|match\\\\\\\\.fun|Math\\\\\\\\.data\\\\\\\\.frame|Math\\\\\\\\.Date|Math\\\\\\\\.difftime|Math\\\\\\\\.factor|Math\\\\\\\\.POSIXt|matrix|max|max\\\\\\\\.col|mean|mean\\\\\\\\.Date|mean\\\\\\\\.default|mean\\\\\\\\.difftime|mean\\\\\\\\.POSIXct|mean\\\\\\\\.POSIXlt|mem\\\\\\\\.maxNSize|mem\\\\\\\\.maxVSize|memCompress|memDecompress|memory\\\\\\\\.profile|merge|merge\\\\\\\\.data\\\\\\\\.frame|merge\\\\\\\\.default|message|mget|min|missing|Mod|mode|months|months\\\\\\\\.Date|months\\\\\\\\.POSIXt|mtfrm|mtfrm\\\\\\\\.default|mtfrm\\\\\\\\.POSIXct|mtfrm\\\\\\\\.POSIXlt|nameOfClass|nameOfClass\\\\\\\\.default|names|names\\\\\\\\.POSIXlt|namespaceExport|namespaceImport|namespaceImportClasses|namespaceImportFrom|namespaceImportMethods|nargs|nchar|ncol|NCOL|Negate|new\\\\\\\\.env|next|NextMethod|ngettext|nlevels|noquote|norm|normalizePath|nrow|NROW|nullfile|numeric|numeric_version|numToBits|numToInts|nzchar|objects|oldClass|OlsonNames|on\\\\\\\\.exit|open|open\\\\\\\\.connection|open\\\\\\\\.srcfile|open\\\\\\\\.srcfilealias|open\\\\\\\\.srcfilecopy|Ops\\\\\\\\.data\\\\\\\\.frame|Ops\\\\\\\\.Date|Ops\\\\\\\\.difftime|Ops\\\\\\\\.factor|Ops\\\\\\\\.numeric_version|Ops\\\\\\\\.ordered|Ops\\\\\\\\.POSIXt|options|order|ordered|outer|package_version|packageEvent|packageHasNamespace|packageNotFoundError|packageStartupMessage|packBits|pairlist|parent\\\\\\\\.env|parent\\\\\\\\.frame|parse|parseNamespaceFile|paste0??|path\\\\\\\\.expand|path\\\\\\\\.package|pcre_config|pipe|plot|pmatch|pmax|pmax\\\\\\\\.int|pmin|pmin\\\\\\\\.int|polyroot|pos\\\\\\\\.to\\\\\\\\.env|Position|pretty|pretty\\\\\\\\.default|prettyNum|print|print\\\\\\\\.AsIs|print\\\\\\\\.by|print\\\\\\\\.condition|print\\\\\\\\.connection|print\\\\\\\\.data\\\\\\\\.frame|print\\\\\\\\.Date|print\\\\\\\\.default|print\\\\\\\\.difftime|print\\\\\\\\.Dlist|print\\\\\\\\.DLLInfo|print\\\\\\\\.DLLInfoList|print\\\\\\\\.DLLRegisteredRoutines|print\\\\\\\\.eigen|print\\\\\\\\.factor|print\\\\\\\\.function|print\\\\\\\\.hexmode|print\\\\\\\\.libraryIQR|print\\\\\\\\.listof|print\\\\\\\\.NativeRoutineList|print\\\\\\\\.noquote|print\\\\\\\\.numeric_version|print\\\\\\\\.octmode|print\\\\\\\\.packageInfo|print\\\\\\\\.POSIXct|print\\\\\\\\.POSIXlt|print\\\\\\\\.proc_time|print\\\\\\\\.restart|print\\\\\\\\.rle|print\\\\\\\\.simple\\\\\\\\.list|print\\\\\\\\.srcfile|print\\\\\\\\.srcref|print\\\\\\\\.summary\\\\\\\\.table|print\\\\\\\\.summary\\\\\\\\.warnings|print\\\\\\\\.summaryDefault|print\\\\\\\\.table|print\\\\\\\\.warnings|prmatrix|proc\\\\\\\\.time|prod|prop\\\\\\\\.table|proportions|provideDimnames|psigamma|pushBack|pushBackLength|qr??|qr\\\\\\\\.coef|qr\\\\\\\\.default|qr\\\\\\\\.fitted|qr\\\\\\\\.Q|qr\\\\\\\\.qty|qr\\\\\\\\.qy|qr\\\\\\\\.R|qr\\\\\\\\.resid|qr\\\\\\\\.solve|qr\\\\\\\\.X|quarters|quarters\\\\\\\\.Date|quarters\\\\\\\\.POSIXt|quit|quote|R_compiled_by|R_system_version|R\\\\\\\\.home|R\\\\\\\\.Version|range|range\\\\\\\\.Date|range\\\\\\\\.default|range\\\\\\\\.POSIXct|rank|rapply|raw|rawConnection|rawConnectionValue|rawShift|rawToBits|rawToChar|rbind|rbind\\\\\\\\.data\\\\\\\\.frame|rcond|Re|read\\\\\\\\.dcf|readBin|readChar|readline|readLines|readRDS|readRenviron|Recall|Reduce|reg\\\\\\\\.finalizer|regexec|regexpr|registerS3methods??|regmatches|remove|removeTaskCallback|rep|rep_len|rep\\\\\\\\.Date|rep\\\\\\\\.difftime|rep\\\\\\\\.factor|rep\\\\\\\\.int|rep\\\\\\\\.numeric_version|rep\\\\\\\\.POSIXct|rep\\\\\\\\.POSIXlt|repeat|replace|replicate|require|requireNamespace|restartDescription|restartFormals|retracemem|return|returnValue|rev|rev\\\\\\\\.default|rle|rm|RNGkind|RNGversion|round|round\\\\\\\\.Date|round\\\\\\\\.POSIXt|row|row\\\\\\\\.names|row\\\\\\\\.names\\\\\\\\.data\\\\\\\\.frame|row\\\\\\\\.names\\\\\\\\.default|rowMeans|rownames|rowsum|rowsum\\\\\\\\.data\\\\\\\\.frame|rowsum\\\\\\\\.default|rowSums|sample|sample\\\\\\\\.int|sapply|save|save\\\\\\\\.image|saveRDS|scale|scale\\\\\\\\.default|scan|search|searchpaths|seek|seek\\\\\\\\.connection|seq|seq_along|seq_len|seq\\\\\\\\.Date|seq\\\\\\\\.default|seq\\\\\\\\.int|seq\\\\\\\\.POSIXt|sequence|sequence\\\\\\\\.default|serialize|serverSocket|set\\\\\\\\.seed|setdiff|setequal|setHook|setNamespaceInfo|setSessionTimeLimit|setTimeLimit|setwd|showConnections|shQuote|sign|signalCondition|signif|simpleCondition|simpleError|simpleMessage|simpleWarning|simplify2array|sin|single|sinh|sink|sink\\\\\\\\.number|sinpi|slice\\\\\\\\.index|socketAccept|socketConnection|socketSelect|socketTimeout|solve|solve\\\\\\\\.default|solve\\\\\\\\.qr|sort|sort_by|sort_by\\\\\\\\.data\\\\\\\\.frame|sort_by\\\\\\\\.default|sort\\\\\\\\.default|sort\\\\\\\\.int|sort\\\\\\\\.list|sort\\\\\\\\.POSIXlt|source|split|split\\\\\\\\.data\\\\\\\\.frame|split\\\\\\\\.Date|split\\\\\\\\.default|split\\\\\\\\.POSIXct|sprintf|sqrt|sQuote|srcfile|srcfilealias|srcfilecopy|srcref|standardGeneric|startsWith|stderr|stdin|stdout|stop|stopifnot|storage\\\\\\\\.mode|str2expression|str2lang|strftime|strptime|strrep|strsplit|strtoi|strtrim|structure|strwrap|sub|subset|subset\\\\\\\\.data\\\\\\\\.frame|subset\\\\\\\\.default|subset\\\\\\\\.matrix|substitute|substr|substring|sum|summary|summary\\\\\\\\.connection|summary\\\\\\\\.data\\\\\\\\.frame|Summary\\\\\\\\.data\\\\\\\\.frame|summary\\\\\\\\.Date|Summary\\\\\\\\.Date|summary\\\\\\\\.default|summary\\\\\\\\.difftime|Summary\\\\\\\\.difftime|summary\\\\\\\\.factor|Summary\\\\\\\\.factor|summary\\\\\\\\.matrix|Summary\\\\\\\\.numeric_version|Summary\\\\\\\\.ordered|summary\\\\\\\\.POSIXct|Summary\\\\\\\\.POSIXct|summary\\\\\\\\.POSIXlt|Summary\\\\\\\\.POSIXlt|summary\\\\\\\\.proc_time|summary\\\\\\\\.srcfile|summary\\\\\\\\.srcref|summary\\\\\\\\.table|summary\\\\\\\\.warnings|suppressMessages|suppressPackageStartupMessages|suppressWarnings|suspendInterrupts|svd|sweep|switch|sys\\\\\\\\.calls??|Sys\\\\\\\\.chmod|Sys\\\\\\\\.Date|sys\\\\\\\\.frames??|sys\\\\\\\\.function|Sys\\\\\\\\.getenv|Sys\\\\\\\\.getlocale|Sys\\\\\\\\.getpid|Sys\\\\\\\\.glob|Sys\\\\\\\\.info|sys\\\\\\\\.load\\\\\\\\.image|Sys\\\\\\\\.localeconv|sys\\\\\\\\.nframe|sys\\\\\\\\.on\\\\\\\\.exit|sys\\\\\\\\.parents??|Sys\\\\\\\\.readlink|sys\\\\\\\\.save\\\\\\\\.image|Sys\\\\\\\\.setenv|Sys\\\\\\\\.setFileTime|Sys\\\\\\\\.setLanguage|Sys\\\\\\\\.setlocale|Sys\\\\\\\\.sleep|sys\\\\\\\\.source|sys\\\\\\\\.status|Sys\\\\\\\\.time|Sys\\\\\\\\.timezone|Sys\\\\\\\\.umask|Sys\\\\\\\\.unsetenv|Sys\\\\\\\\.which|system|system\\\\\\\\.file|system\\\\\\\\.time|system2|t|t\\\\\\\\.data\\\\\\\\.frame|t\\\\\\\\.default|table|tabulate|Tailcall|tanh??|tanpi|tapply|taskCallbackManager|tcrossprod|tempdir|tempfile|textConnection|textConnectionValue|tolower|topenv|toString|toString\\\\\\\\.default|toupper|trace|traceback|tracemem|tracingState|transform|transform\\\\\\\\.data\\\\\\\\.frame|transform\\\\\\\\.default|trigamma|trimws|trunc|trunc\\\\\\\\.Date|trunc\\\\\\\\.POSIXt|truncate|truncate\\\\\\\\.connection|try|tryCatch|tryInvokeRestart|typeof|unCfillPOSIXlt|unclass|undebug|union|unique|unique\\\\\\\\.array|unique\\\\\\\\.data\\\\\\\\.frame|unique\\\\\\\\.default|unique\\\\\\\\.matrix|unique\\\\\\\\.numeric_version|unique\\\\\\\\.POSIXlt|unique\\\\\\\\.warnings|units|units\\\\\\\\.difftime|unix\\\\\\\\.time|unlink|unlist|unloadNamespace|unlockBinding|unname|unserialize|unsplit|untrace|untracemem|unz|upper\\\\\\\\.tri|url|use|UseMethod|utf8ToInt|validEnc|validUTF8|vapply|vector|Vectorize|warning|warningCondition|warnings|weekdays|weekdays\\\\\\\\.Date|weekdays\\\\\\\\.POSIXt|which|which\\\\\\\\.max|which\\\\\\\\.min|while|with|with\\\\\\\\.default|withAutoprint|withCallingHandlers|within|within\\\\\\\\.data\\\\\\\\.frame|within\\\\\\\\.list|withRestarts|withVisible|write|write\\\\\\\\.dcf|writeBin|writeChar|writeLines|xor|xpdrows\\\\\\\\.data\\\\\\\\.frame|xtfrm|xtfrm\\\\\\\\.AsIs|xtfrm\\\\\\\\.data\\\\\\\\.frame|xtfrm\\\\\\\\.Date|xtfrm\\\\\\\\.default|xtfrm\\\\\\\\.difftime|xtfrm\\\\\\\\.factor|xtfrm\\\\\\\\.numeric_version|xtfrm\\\\\\\\.POSIXct|xtfrm\\\\\\\\.POSIXlt|xzfile|zapsmall|zstdfile)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.namespace.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.colons.r\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.r\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.r\\\"}},\\\"contentName\\\":\\\"meta.function-call.arguments.r\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.r\\\"}},\\\"name\\\":\\\"meta.function-call.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-arguments\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(?:(graphics)(::))?(abline|arrows|assocplot|axis|Axis|axis\\\\\\\\.Date|Axis\\\\\\\\.Date|Axis\\\\\\\\.default|axis\\\\\\\\.POSIXct|Axis\\\\\\\\.POSIXt|Axis\\\\\\\\.table|axTicks|barplot|barplot\\\\\\\\.default|barplot\\\\\\\\.formula|box|boxplot|boxplot\\\\\\\\.default|boxplot\\\\\\\\.formula|boxplot\\\\\\\\.matrix|bxp|cdplot|cdplot\\\\\\\\.default|cdplot\\\\\\\\.formula|clip|close\\\\\\\\.screen|co\\\\\\\\.intervals|contour|contour\\\\\\\\.default|coplot|curve|dotchart|erase\\\\\\\\.screen|extendDateTimeFormat|filled\\\\\\\\.contour|fourfoldplot|frame|grconvertX|grconvertY|grid|hist|hist\\\\\\\\.Date|hist\\\\\\\\.default|hist\\\\\\\\.POSIXt|identify|identify\\\\\\\\.default|image|image\\\\\\\\.default|layout|layout\\\\\\\\.show|lcm|legend|lines|lines\\\\\\\\.default|lines\\\\\\\\.formula|lines\\\\\\\\.histogram|lines\\\\\\\\.table|locator|matlines|matplot|matpoints|mosaicplot|mosaicplot\\\\\\\\.default|mosaicplot\\\\\\\\.formula|mtext|pairs|pairs\\\\\\\\.default|pairs\\\\\\\\.formula|panel\\\\\\\\.smooth|par|persp|persp\\\\\\\\.default|pie|piechart|plot\\\\\\\\.data\\\\\\\\.frame|plot\\\\\\\\.default|plot\\\\\\\\.design|plot\\\\\\\\.factor|plot\\\\\\\\.formula|plot\\\\\\\\.function|plot\\\\\\\\.histogram|plot\\\\\\\\.new|plot\\\\\\\\.raster|plot\\\\\\\\.table|plot\\\\\\\\.window|plot\\\\\\\\.xy|plotHclust|points|points\\\\\\\\.default|points\\\\\\\\.formula|points\\\\\\\\.table|polygon|polypath|rasterImage|rect|rug|screen|segments|smoothScatter|spineplot|spineplot\\\\\\\\.default|spineplot\\\\\\\\.formula|split\\\\\\\\.screen|stars|stem|strheight|stripchart|stripchart\\\\\\\\.default|stripchart\\\\\\\\.formula|strwidth|sunflowerplot|sunflowerplot\\\\\\\\.default|sunflowerplot\\\\\\\\.formula|symbols|text|text\\\\\\\\.default|text\\\\\\\\.formula|title|xinch|xspline|xyinch|yinch)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.namespace.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.colons.r\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.r\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.r\\\"}},\\\"contentName\\\":\\\"meta.function-call.arguments.r\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.r\\\"}},\\\"name\\\":\\\"meta.function-call.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-arguments\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(?:(grDevices)(::))?(adjustcolor|anyNA\\\\\\\\.raster|as\\\\\\\\.graphicsAnnot|as\\\\\\\\.matrix\\\\\\\\.raster|as\\\\\\\\.raster|as\\\\\\\\.raster\\\\\\\\.array|as\\\\\\\\.raster\\\\\\\\.character|as\\\\\\\\.raster\\\\\\\\.logical|as\\\\\\\\.raster\\\\\\\\.matrix|as\\\\\\\\.raster\\\\\\\\.numeric|as\\\\\\\\.raster\\\\\\\\.raster|as\\\\\\\\.raster\\\\\\\\.raw|axisTicks|bitmap|bmp|boxplot\\\\\\\\.stats|c2to3|cairo_pdf|cairo_ps|cairoFT|cairoSymbolFont|cairoVersion|check_gs_type|check\\\\\\\\.options|checkFont|checkFont\\\\\\\\.CIDFont|checkFont\\\\\\\\.default|checkFont\\\\\\\\.Type1Font|checkFontInUse|checkIntFormat|checkQuartzFont|checkSymbolFont|checkX11Font|chromaticAdaptation|chull|CIDFont|cm|cm\\\\\\\\.colors|col2rgb|colorConverter|colorRamp|colorRampPalette|colors|colours|comparePangoVersion|contourLines|convertColor|densCols|dev\\\\\\\\.capabilities|dev\\\\\\\\.capture|dev\\\\\\\\.control|dev\\\\\\\\.copy|dev\\\\\\\\.copy2eps|dev\\\\\\\\.copy2pdf|dev\\\\\\\\.cur|dev\\\\\\\\.displaylist|dev\\\\\\\\.flush|dev\\\\\\\\.hold|dev\\\\\\\\.interactive|dev\\\\\\\\.list|dev\\\\\\\\.new|dev\\\\\\\\.next|dev\\\\\\\\.off|dev\\\\\\\\.prev|dev\\\\\\\\.print|dev\\\\\\\\.set|dev\\\\\\\\.size|dev2bitmap|devAskNewPage|deviceIsInteractive|embedFonts|embedGlyphs|extendrange|getGraphicsEvent|getGraphicsEventEnv|glyphAnchor|glyphFont|glyphFontList|glyphHeight|glyphHeightBottom|glyphInfo|glyphJust|glyphJust\\\\\\\\.character|glyphJust\\\\\\\\.GlyphJust|glyphJust\\\\\\\\.numeric|glyphWidth|glyphWidthLeft|graphics\\\\\\\\.off|gray|gray\\\\\\\\.colors|grey|grey\\\\\\\\.colors|grSoftVersion|guessEncoding|hcl|hcl\\\\\\\\.colors|hcl\\\\\\\\.pals|heat\\\\\\\\.colors|hsv|initPSandPDFfonts|invertStyle|is\\\\\\\\.na\\\\\\\\.raster|is\\\\\\\\.raster|isPDF|jpeg|make\\\\\\\\.rgb|mapCharWeight|mapStyle|mapWeight|matchEncoding|matchEncoding\\\\\\\\.CIDFont|matchEncoding\\\\\\\\.Type1Font|matchFont|n2mfrow|nclass\\\\\\\\.FD|nclass\\\\\\\\.scott|nclass\\\\\\\\.Sturges|Ops\\\\\\\\.raster|optionSymbolFont|palette|palette\\\\\\\\.colors|palette\\\\\\\\.match|palette\\\\\\\\.pals|pangoVersion|pattern|pdf|pdf\\\\\\\\.options|pdfFonts|pictex|png|postscript|postscriptFonts|pow3|prettyDate|print\\\\\\\\.colorConverter|print\\\\\\\\.raster|print\\\\\\\\.recordedplot|print\\\\\\\\.RGBcolorConverter|print\\\\\\\\.RGlyphFont|printFont|printFont\\\\\\\\.CIDFont|printFont\\\\\\\\.Type1Font|printFonts|ps\\\\\\\\.options|quartz|quartz\\\\\\\\.options|quartz\\\\\\\\.save|quartzFonts??|rainbow|recordGraphics|recordPalette|recordPlot|replayPlot|restoreRecordedPlot|rgb|rgb2hsv|RGBcolorConverter|savePlot|seqDtime|setEPS|setFonts|setGraphicsEventEnv|setGraphicsEventHandlers|setPS|setQuartzFonts|setX11Fonts|svg|symbolfamilyDefault|symbolType1support|terrain\\\\\\\\.colors|tiff|topo\\\\\\\\.colors|trans3d|trunc_POSIXt|Type1Font|vectorizeConverter|warnLogCoords|x11|X11|X11\\\\\\\\.options|X11Font|X11FontError|X11Fonts|xfig|xy\\\\\\\\.coords|xyTable|xyz\\\\\\\\.coords)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.namespace.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.colons.r\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.r\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.r\\\"}},\\\"contentName\\\":\\\"meta.function-call.arguments.r\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.r\\\"}},\\\"name\\\":\\\"meta.function-call.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-arguments\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(?:(methods)(::))?(addNextMethod|allNames|Arith|as|asMethodDefinition|assignClassDef|assignMethodsMetaData|balanceMethodsList|bind_activation|cacheGenericsMetaData|cacheMetaData|cacheMethod|cacheOnAssign|callGeneric|callNextMethod|canCoerce|cbind2??|checkAtAssignment|checkSlotAssignment|classesToAM|classGeneratorFunction|classLabel|classMetaName|className|coerce|Compare|completeClassDefinition|completeExtends|completeSubclasses|Complex|conformMethod|defaultDumpName|defaultPrototype|dispatchIsInternal|doPrimitiveMethod|dumpMethods??|el|elNamed|empty\\\\\\\\.dump|emptyMethodsList|envRefInferField|envRefSetField|evalOnLoad|evalqOnLoad|evalSource|existsFunction|existsMethod|extends|externalRefMethod|finalDefaultMethod|findClass|findFunction|findMethods??|findMethodSignatures|findUnique|fixPre1\\\\\\\\.8|formalArgs|fromNextMethod|functionBody|generic\\\\\\\\.skeleton|genericForBasic|getAllSuperClasses|getClass|getClassDef|getClasses|getDataPart|getFunction|getGeneric|getGenericFromCall|getGenerics|getGroup|getGroupMembers|getLoadActions|getMethods??|getMethodsAndAccessors|getMethodsForDispatch|getMethodsMetaData|getPackageName|getRefClass|getRefSuperClasses|getSlots|getValidity|hasArg|hasLoadAction|hasMethods??|implicitGeneric|inBasicFuns|inferProperties|inheritedSlotNames|inheritedSubMethodLists|initFieldArgs|initialize|initMethodDispatch|initRefFields|insertClassMethods|insertMethod|insertMethodInEmptyList|insertSource|installClassMethod|is|isBaseFun|isClass|isClassDef|isClassUnion|isGeneric|isGrammarSymbol|isGroup|isMixin|isRematched|isS3Generic|isSealedClass|isSealedMethod|isVirtualClass|isXS3Class|kronecker|languageEl|listFromMethods|loadMethod|Logic|makeClassMethod|makeClassRepresentation|makeEnvRefMethods|makeExtends|makeGeneric|makeMethodsList|makePrototypeFromClassDef|makeStandardGeneric|matchDefaults|matchSignature|Math2??|matrixOps|mergeMethods|metaNameUndo|method\\\\\\\\.skeleton|MethodAddCoerce|methodSignatureMatrix|MethodsList|MethodsListSelect|methodsPackageMetaName|missingArg|multipleClasses|new|newBasic|newClassRepresentation|newEmptyObject|Ops|outerLabels|packageSlot|possibleExtends|printClassRepresentation|printPropertiesList|prohibitGeneric|promptClass|promptMethods|prototype|Quote|rbind2??|reconcilePropertiesAndPrototype|refClassFields|refClassInformation|refClassMethods|refClassPrompt|refObjectClass|registerImplicitGenerics|rematchDefinition|removeClass|removeGeneric|removeMethods??|representation|requireMethods|resetClass|resetGeneric|S3Class|S3forS4Methods|S3Part|sealClass|selectMethod|selectSuperClasses|setAs|setCacheOnAssign|setClass|setClassUnion|setDataPart|setGeneric|setGenericImplicit|setGroupGeneric|setIs|setLoadActions??|setMethod|setNames|setOldClass|setPackageName|setPackageSlot|setPrimitiveMethods|setRefClass|setReplaceMethod|setValidity|show|showClass|showClassMethod|showDefault|showExtends|showExtraSlots|showMethods|showRefClassDef|signature|SignatureMethod|sigToEnv|slot|slotNames|slotsFromS3|substituteDirect|substituteFunctionArgs|Summary|superClassDepth|superClassMethodName|tableNames|testInheritedMethods|testVirtual|tryNew|unRematchDefinition|useMTable|validObject|validSlotNames)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.namespace.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.colons.r\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.r\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.r\\\"}},\\\"contentName\\\":\\\"meta.function-call.arguments.r\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.r\\\"}},\\\"name\\\":\\\"meta.function-call.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-arguments\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(?:(stats)(::))?(acf|acf2AR|add\\\\\\\\.scope|add1|add1\\\\\\\\.default|add1\\\\\\\\.glm|add1\\\\\\\\.lm|add1\\\\\\\\.mlm|addmargins|aggregate|aggregate\\\\\\\\.data\\\\\\\\.frame|aggregate\\\\\\\\.default|aggregate\\\\\\\\.formula|aggregate\\\\\\\\.ts|AIC|AIC\\\\\\\\.default|AIC\\\\\\\\.logLik|alias|alias\\\\\\\\.formula|alias\\\\\\\\.lm|anova|anova\\\\\\\\.glm|anova\\\\\\\\.glmlist|anova\\\\\\\\.lm|anova\\\\\\\\.lmlist|anova\\\\\\\\.loess|anova\\\\\\\\.mlm|anova\\\\\\\\.mlmlist|anova\\\\\\\\.nls|anovalist\\\\\\\\.nls|ansari\\\\\\\\.test|ansari\\\\\\\\.test\\\\\\\\.default|ansari\\\\\\\\.test\\\\\\\\.formula|aov|approx|approxfun|ar|ar\\\\\\\\.burg|ar\\\\\\\\.burg\\\\\\\\.default|ar\\\\\\\\.burg\\\\\\\\.mts|ar\\\\\\\\.mle|ar\\\\\\\\.ols|ar\\\\\\\\.yw|ar\\\\\\\\.yw\\\\\\\\.default|ar\\\\\\\\.yw\\\\\\\\.mts|arima|arima\\\\\\\\.sim|arima0|arima0\\\\\\\\.diag|ARMAacf|ARMAtoMA|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.aovproj|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.ftable|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.logLik|as\\\\\\\\.dendrogram|as\\\\\\\\.dendrogram\\\\\\\\.dendrogram|as\\\\\\\\.dendrogram\\\\\\\\.hclust|as\\\\\\\\.dist|as\\\\\\\\.dist\\\\\\\\.default|as\\\\\\\\.formula|as\\\\\\\\.hclust|as\\\\\\\\.hclust\\\\\\\\.default|as\\\\\\\\.hclust\\\\\\\\.dendrogram|as\\\\\\\\.hclust\\\\\\\\.twins|as\\\\\\\\.matrix\\\\\\\\.dist|as\\\\\\\\.matrix\\\\\\\\.ftable|as\\\\\\\\.stepfun|as\\\\\\\\.stepfun\\\\\\\\.default|as\\\\\\\\.stepfun\\\\\\\\.isoreg|as\\\\\\\\.table\\\\\\\\.ftable|as\\\\\\\\.ts|as\\\\\\\\.ts\\\\\\\\.default|asOneSidedFormula|assert_NULL_or_prob|ave|bandwidth\\\\\\\\.kernel|bartlett\\\\\\\\.test|bartlett\\\\\\\\.test\\\\\\\\.default|bartlett\\\\\\\\.test\\\\\\\\.formula|BIC|BIC\\\\\\\\.default|BIC\\\\\\\\.logLik|binom\\\\\\\\.test|binomial|binomInitialize|biplot|biplot\\\\\\\\.default|biplot\\\\\\\\.prcomp|biplot\\\\\\\\.princomp|Box\\\\\\\\.test|bw_pair_cnts|bw\\\\\\\\.bcv|bw\\\\\\\\.nrd0??|bw\\\\\\\\.SJ|bw\\\\\\\\.ucv|C|cancor|case\\\\\\\\.names|case\\\\\\\\.names\\\\\\\\.default|case\\\\\\\\.names\\\\\\\\.lm|cbind\\\\\\\\.ts|ccf|check_exact|chisq\\\\\\\\.test|cmdscale|coef|coef\\\\\\\\.aov|coef\\\\\\\\.Arima|coef\\\\\\\\.default|coef\\\\\\\\.listof|coef\\\\\\\\.maov|coef\\\\\\\\.nls|coefficients|complete\\\\\\\\.cases|confint|confint\\\\\\\\.default|confint\\\\\\\\.glm|confint\\\\\\\\.lm|confint\\\\\\\\.nls|confint\\\\\\\\.profile\\\\\\\\.glm|confint\\\\\\\\.profile\\\\\\\\.nls|constrOptim|contr\\\\\\\\.helmert|contr\\\\\\\\.poly|contr\\\\\\\\.SAS|contr\\\\\\\\.sum|contr\\\\\\\\.treatment|contrasts|convolve|cooks\\\\\\\\.distance|cooks\\\\\\\\.distance\\\\\\\\.glm|cooks\\\\\\\\.distance\\\\\\\\.lm|cophenetic|cophenetic\\\\\\\\.default|cophenetic\\\\\\\\.dendrogram|cor|cor\\\\\\\\.test|cor\\\\\\\\.test\\\\\\\\.default|cor\\\\\\\\.test\\\\\\\\.formula|cov|cov\\\\\\\\.wt|cov2cor|covratio|cpgram|cut\\\\\\\\.dendrogram|cutree|cycle|cycle\\\\\\\\.default|cycle\\\\\\\\.ts|D|dbeta|dbinom|dcauchy|dchisq|decompose|delete\\\\\\\\.response|deltat|deltat\\\\\\\\.default|dendrapply|density|density\\\\\\\\.default|deparse2|deriv|deriv\\\\\\\\.default|deriv\\\\\\\\.formula|deriv3|deriv3\\\\\\\\.default|deriv3\\\\\\\\.formula|deviance|deviance\\\\\\\\.default|deviance\\\\\\\\.glm|deviance\\\\\\\\.lm|deviance\\\\\\\\.mlm|deviance\\\\\\\\.nls|dexp|df|df\\\\\\\\.kernel|df\\\\\\\\.residual|df\\\\\\\\.residual\\\\\\\\.default|df\\\\\\\\.residual\\\\\\\\.nls|DF2formula|dfbeta|dfbeta\\\\\\\\.lm|dfbetas|dfbetas\\\\\\\\.lm|dffits|dgamma|dgeom|dhyper|diff\\\\\\\\.ts|diffinv|diffinv\\\\\\\\.default|diffinv\\\\\\\\.ts|diffinv\\\\\\\\.vector|dist|dlnorm|dlogis|dmultinom|dnbinom|dnorm|dpois|drop\\\\\\\\.scope|drop\\\\\\\\.terms|drop1|drop1\\\\\\\\.default|drop1\\\\\\\\.glm|drop1\\\\\\\\.lm|drop1\\\\\\\\.mlm|dsignrank|dt|dummy\\\\\\\\.coef|dummy\\\\\\\\.coef\\\\\\\\.aovlist|dummy\\\\\\\\.coef\\\\\\\\.lm|dunif|dweibull|dwilcox|ecdf|eff\\\\\\\\.aovlist|effects|effects\\\\\\\\.glm|effects\\\\\\\\.lm|embed|end|end\\\\\\\\.default|estVar|estVar\\\\\\\\.mlm|estVar\\\\\\\\.SSD|expand\\\\\\\\.model\\\\\\\\.frame|extractAIC|extractAIC\\\\\\\\.aov|extractAIC\\\\\\\\.coxph|extractAIC\\\\\\\\.glm|extractAIC\\\\\\\\.lm|extractAIC\\\\\\\\.negbin|extractAIC\\\\\\\\.survreg|factanal|factanal\\\\\\\\.fit\\\\\\\\.mle|factor\\\\\\\\.scope|family|family\\\\\\\\.glm|family\\\\\\\\.lm|fft|filter|fisher\\\\\\\\.test|fitted|fitted\\\\\\\\.default|fitted\\\\\\\\.isoreg|fitted\\\\\\\\.kmeans|fitted\\\\\\\\.nls|fitted\\\\\\\\.smooth\\\\\\\\.spline|fitted\\\\\\\\.values|fivenum|fligner\\\\\\\\.test|fligner\\\\\\\\.test\\\\\\\\.default|fligner\\\\\\\\.test\\\\\\\\.formula|format_perc|format\\\\\\\\.dist|format\\\\\\\\.ftable|formula|formula\\\\\\\\.character|formula\\\\\\\\.data\\\\\\\\.frame|formula\\\\\\\\.default|formula\\\\\\\\.formula|formula\\\\\\\\.glm|formula\\\\\\\\.lm|formula\\\\\\\\.nls|formula\\\\\\\\.terms|frequency|frequency\\\\\\\\.default|friedman\\\\\\\\.test|friedman\\\\\\\\.test\\\\\\\\.default|friedman\\\\\\\\.test\\\\\\\\.formula|ftable|ftable\\\\\\\\.default|ftable\\\\\\\\.formula|Gamma|gaussian|get_all_vars|getCall|getCall\\\\\\\\.default|getInitial|getInitial\\\\\\\\.default|getInitial\\\\\\\\.formula|getInitial\\\\\\\\.selfStart|glm|glm\\\\\\\\.control|glm\\\\\\\\.fit|hasTsp|hat|hatvalues|hatvalues\\\\\\\\.lm|hatvalues\\\\\\\\.smooth\\\\\\\\.spline|hclust|head\\\\\\\\.ts|heatmap|HL|HoltWinters|hyman_filter|identify\\\\\\\\.hclust|influence|influence\\\\\\\\.glm|influence\\\\\\\\.lm|influence\\\\\\\\.measures|integrate|interaction\\\\\\\\.plot|inverse\\\\\\\\.gaussian|IQR|is\\\\\\\\.empty\\\\\\\\.model|is\\\\\\\\.leaf|is\\\\\\\\.mts|is\\\\\\\\.stepfun|is\\\\\\\\.ts|is\\\\\\\\.tskernel|isoreg|KalmanForecast|KalmanLike|KalmanRun|KalmanSmooth|kernapply|kernapply\\\\\\\\.default|kernapply\\\\\\\\.ts|kernapply\\\\\\\\.tskernel|kernapply\\\\\\\\.vector|kernel|kmeans|knots|knots\\\\\\\\.stepfun|kruskal\\\\\\\\.test|kruskal\\\\\\\\.test\\\\\\\\.default|kruskal\\\\\\\\.test\\\\\\\\.formula|ks\\\\\\\\.test|ks\\\\\\\\.test\\\\\\\\.default|ks\\\\\\\\.test\\\\\\\\.formula|ksmooth|labels\\\\\\\\.dendrogram|labels\\\\\\\\.dist|labels\\\\\\\\.lm|labels\\\\\\\\.terms|lag|lag\\\\\\\\.default|lag\\\\\\\\.plot|line|lines\\\\\\\\.isoreg|lines\\\\\\\\.stepfun|lines\\\\\\\\.ts|lm|lm\\\\\\\\.fit|lm\\\\\\\\.influence|lm\\\\\\\\.wfit|loadings|loess|loess\\\\\\\\.control|loess\\\\\\\\.smooth|logLik|logLik\\\\\\\\.Arima|logLik\\\\\\\\.glm|logLik\\\\\\\\.lm|logLik\\\\\\\\.logLik|logLik\\\\\\\\.nls|loglin|lowess|ls\\\\\\\\.diag|ls\\\\\\\\.print|lsfit|mad|mahalanobis|make\\\\\\\\.link|make\\\\\\\\.tables\\\\\\\\.aovproj|make\\\\\\\\.tables\\\\\\\\.aovprojlist|makeARIMA|makepredictcall|makepredictcall\\\\\\\\.default|makepredictcall\\\\\\\\.poly|manova|mantelhaen\\\\\\\\.test|mauchly\\\\\\\\.test|mauchly\\\\\\\\.test\\\\\\\\.mlm|mauchly\\\\\\\\.test\\\\\\\\.SSD|mcnemar\\\\\\\\.test|median|median\\\\\\\\.default|medpolish|merge\\\\\\\\.dendrogram|midcache\\\\\\\\.dendrogram|model\\\\\\\\.extract|model\\\\\\\\.frame|model\\\\\\\\.frame\\\\\\\\.aovlist|model\\\\\\\\.frame\\\\\\\\.default|model\\\\\\\\.frame\\\\\\\\.glm|model\\\\\\\\.frame\\\\\\\\.lm|model\\\\\\\\.matrix|model\\\\\\\\.matrix\\\\\\\\.default|model\\\\\\\\.matrix\\\\\\\\.lm|model\\\\\\\\.offset|model\\\\\\\\.response|model\\\\\\\\.tables|model\\\\\\\\.tables\\\\\\\\.aov|model\\\\\\\\.tables\\\\\\\\.aovlist|model\\\\\\\\.weights|monthplot|monthplot\\\\\\\\.default|monthplot\\\\\\\\.stl|monthplot\\\\\\\\.StructTS|monthplot\\\\\\\\.ts|mood\\\\\\\\.test|mood\\\\\\\\.test\\\\\\\\.default|mood\\\\\\\\.test\\\\\\\\.formula|mvfft|na\\\\\\\\.action|na\\\\\\\\.action\\\\\\\\.default|na\\\\\\\\.contiguous|na\\\\\\\\.contiguous\\\\\\\\.default|na\\\\\\\\.exclude|na\\\\\\\\.exclude\\\\\\\\.data\\\\\\\\.frame|na\\\\\\\\.exclude\\\\\\\\.default|na\\\\\\\\.fail|na\\\\\\\\.fail\\\\\\\\.default|na\\\\\\\\.omit|na\\\\\\\\.omit\\\\\\\\.data\\\\\\\\.frame|na\\\\\\\\.omit\\\\\\\\.default|na\\\\\\\\.omit\\\\\\\\.ts|na\\\\\\\\.pass|napredict|napredict\\\\\\\\.default|napredict\\\\\\\\.exclude|naprint|naprint\\\\\\\\.default|naprint\\\\\\\\.exclude|naprint\\\\\\\\.omit|naresid|naresid\\\\\\\\.default|naresid\\\\\\\\.exclude|nextn|nleaves|nlm|nlminb|nls|nls_port_fit|nls\\\\\\\\.control|nlsModel|nlsModel\\\\\\\\.plinear|NLSstAsymptotic|NLSstAsymptotic\\\\\\\\.sortedXyData|NLSstClosestX|NLSstClosestX\\\\\\\\.sortedXyData|NLSstLfAsymptote|NLSstLfAsymptote\\\\\\\\.sortedXyData|NLSstRtAsymptote|NLSstRtAsymptote\\\\\\\\.sortedXyData|nobs|nobs\\\\\\\\.default|nobs\\\\\\\\.dendrogram|nobs\\\\\\\\.glm|nobs\\\\\\\\.lm|nobs\\\\\\\\.logLik|nobs\\\\\\\\.nls|numericDeriv|offset|oneway\\\\\\\\.test|Ops\\\\\\\\.ts|optim|optimHess|optimise|optimize|order\\\\\\\\.dendrogram|p\\\\\\\\.adjust|pacf|pacf\\\\\\\\.default|Pair|pairs\\\\\\\\.profile|pairwise\\\\\\\\.prop\\\\\\\\.test|pairwise\\\\\\\\.t\\\\\\\\.test|pairwise\\\\\\\\.table|pairwise\\\\\\\\.wilcox\\\\\\\\.test|pbeta|pbinom|pbirthday|pcauchy|pchisq|pexp|pf|pgamma|pgeom|phyper|Pillai|pkolmogorov|pkolmogorov_one_asymp|pkolmogorov_one_exact|pkolmogorov_two_asymp|pkolmogorov_two_exact|plclust|plnorm|plogis|plot\\\\\\\\.acf|plot\\\\\\\\.decomposed\\\\\\\\.ts|plot\\\\\\\\.dendrogram|plot\\\\\\\\.density|plot\\\\\\\\.ecdf|plot\\\\\\\\.hclust|plot\\\\\\\\.HoltWinters|plot\\\\\\\\.isoreg|plot\\\\\\\\.lm|plot\\\\\\\\.medpolish|plot\\\\\\\\.mlm|plot\\\\\\\\.ppr|plot\\\\\\\\.prcomp|plot\\\\\\\\.princomp|plot\\\\\\\\.profile|plot\\\\\\\\.profile\\\\\\\\.nls|plot\\\\\\\\.spec|plot\\\\\\\\.spec\\\\\\\\.coherency|plot\\\\\\\\.spec\\\\\\\\.phase|plot\\\\\\\\.stepfun|plot\\\\\\\\.stl|plot\\\\\\\\.ts|plot\\\\\\\\.tskernel|plot\\\\\\\\.TukeyHSD|plotNode|plotNodeLimit|pnbinom|pnorm|pointwise|poisson|poisson\\\\\\\\.test|polym??|port_get_named_v|port_msg|power|power\\\\\\\\.anova\\\\\\\\.test|power\\\\\\\\.prop\\\\\\\\.test|power\\\\\\\\.t\\\\\\\\.test|PP\\\\\\\\.test|ppoints|ppois|ppr|ppr\\\\\\\\.default|ppr\\\\\\\\.formula|prcomp|prcomp\\\\\\\\.default|prcomp\\\\\\\\.formula|predict|predict\\\\\\\\.ar|predict\\\\\\\\.Arima|predict\\\\\\\\.arima0|predict\\\\\\\\.glm|predict\\\\\\\\.HoltWinters|predict\\\\\\\\.lm|predict\\\\\\\\.loess|predict\\\\\\\\.mlm|predict\\\\\\\\.nls|predict\\\\\\\\.poly|predict\\\\\\\\.ppr|predict\\\\\\\\.prcomp|predict\\\\\\\\.princomp|predict\\\\\\\\.smooth\\\\\\\\.spline|predict\\\\\\\\.smooth\\\\\\\\.spline\\\\\\\\.fit|predict\\\\\\\\.StructTS|predLoess|preplot|princomp|princomp\\\\\\\\.default|princomp\\\\\\\\.formula|print\\\\\\\\.acf|print\\\\\\\\.anova|print\\\\\\\\.aov|print\\\\\\\\.aovlist|print\\\\\\\\.ar|print\\\\\\\\.Arima|print\\\\\\\\.arima0|print\\\\\\\\.dendrogram|print\\\\\\\\.density|print\\\\\\\\.dist|print\\\\\\\\.dummy_coef|print\\\\\\\\.dummy_coef_list|print\\\\\\\\.ecdf|print\\\\\\\\.factanal|print\\\\\\\\.family|print\\\\\\\\.formula|print\\\\\\\\.ftable|print\\\\\\\\.glm|print\\\\\\\\.hclust|print\\\\\\\\.HoltWinters|print\\\\\\\\.htest|print\\\\\\\\.infl|print\\\\\\\\.integrate|print\\\\\\\\.isoreg|print\\\\\\\\.kmeans|print\\\\\\\\.lm|print\\\\\\\\.loadings|print\\\\\\\\.loess|print\\\\\\\\.logLik|print\\\\\\\\.medpolish|print\\\\\\\\.mtable|print\\\\\\\\.nls|print\\\\\\\\.pairwise\\\\\\\\.htest|print\\\\\\\\.power\\\\\\\\.htest|print\\\\\\\\.ppr|print\\\\\\\\.prcomp|print\\\\\\\\.princomp|print\\\\\\\\.smooth\\\\\\\\.spline|print\\\\\\\\.stepfun|print\\\\\\\\.stl|print\\\\\\\\.StructTS|print\\\\\\\\.summary\\\\\\\\.aov|print\\\\\\\\.summary\\\\\\\\.aovlist|print\\\\\\\\.summary\\\\\\\\.ecdf|print\\\\\\\\.summary\\\\\\\\.glm|print\\\\\\\\.summary\\\\\\\\.lm|print\\\\\\\\.summary\\\\\\\\.loess|print\\\\\\\\.summary\\\\\\\\.manova|print\\\\\\\\.summary\\\\\\\\.nls|print\\\\\\\\.summary\\\\\\\\.ppr|print\\\\\\\\.summary\\\\\\\\.prcomp|print\\\\\\\\.summary\\\\\\\\.princomp|print\\\\\\\\.tables_aov|print\\\\\\\\.terms|print\\\\\\\\.ts|print\\\\\\\\.tskernel|print\\\\\\\\.TukeyHSD|print\\\\\\\\.tukeyline|print\\\\\\\\.tukeysmooth|print\\\\\\\\.xtabs|printCoefmat|profile|profile\\\\\\\\.glm|profile\\\\\\\\.nls|profiler|profiler\\\\\\\\.nls|proj|Proj|proj\\\\\\\\.aov|proj\\\\\\\\.aovlist|proj\\\\\\\\.default|proj\\\\\\\\.lm|promax|prop\\\\\\\\.test|prop\\\\\\\\.trend\\\\\\\\.test|psignrank|psmirnov|psmirnov_asymp|psmirnov_exact|psmirnov_simul|pt|ptukey|punif|pweibull|pwilcox|qbeta|qbinom|qbirthday|qcauchy|qchisq|qexp|qf|qgamma|qgeom|qhyper|qlnorm|qlogis|qnbinom|qnorm|qpois|qqline|qqnorm|qqnorm\\\\\\\\.default|qqplot|qr\\\\\\\\.influence|qr\\\\\\\\.lm|qsignrank|qsmirnov|qt|qtukey|quade\\\\\\\\.test|quade\\\\\\\\.test\\\\\\\\.default|quade\\\\\\\\.test\\\\\\\\.formula|quantile|quantile\\\\\\\\.default|quantile\\\\\\\\.ecdf|quantile\\\\\\\\.POSIXt|quasi|quasibinomial|quasipoisson|qunif|qweibull|qwilcox|r2dtable|Rank|rbeta|rbinom|rcauchy|rchisq|read\\\\\\\\.ftable|rect\\\\\\\\.hclust|reformulate|regularize\\\\\\\\.values|relevel|relevel\\\\\\\\.default|relevel\\\\\\\\.factor|relevel\\\\\\\\.ordered|reorder|reorder\\\\\\\\.default|reorder\\\\\\\\.dendrogram|replications|reshape|resid|residuals|residuals\\\\\\\\.default|residuals\\\\\\\\.glm|residuals\\\\\\\\.HoltWinters|residuals\\\\\\\\.isoreg|residuals\\\\\\\\.lm|residuals\\\\\\\\.nls|residuals\\\\\\\\.smooth\\\\\\\\.spline|residuals\\\\\\\\.tukeyline|rev\\\\\\\\.dendrogram|rexp|rf|rgamma|rgeom|rhyper|rlnorm|rlogis|rmultinom|rnbinom|rnorm|Roy|rpois|rsignrank|rsmirnov|rstandard|rstandard\\\\\\\\.glm|rstandard\\\\\\\\.lm|rstudent|rstudent\\\\\\\\.glm|rstudent\\\\\\\\.lm|rt|runif|runmed|rweibull|rwilcox|rWishart|safe_pchisq|safe_pf|scatter\\\\\\\\.smooth|screeplot|screeplot\\\\\\\\.default|sd|se\\\\\\\\.aov|se\\\\\\\\.aovlist|se\\\\\\\\.contrast|se\\\\\\\\.contrast\\\\\\\\.aov|se\\\\\\\\.contrast\\\\\\\\.aovlist|selfStart|selfStart\\\\\\\\.default|selfStart\\\\\\\\.formula|setNames|shapiro\\\\\\\\.test|sigma|sigma\\\\\\\\.default|sigma\\\\\\\\.glm|sigma\\\\\\\\.mlm|simpleLoess|simulate|simulate\\\\\\\\.lm|smooth|smooth\\\\\\\\.spline|smoothEnds|sortedXyData|sortedXyData\\\\\\\\.default|spec\\\\\\\\.ar|spec\\\\\\\\.pgram|spec\\\\\\\\.taper|spectrum|sphericity|spl_coef_conv|spline|splinefunH??|splinefunH0|SSasymp|SSasympOff|SSasympOrig|SSbiexp|SSD|SSD\\\\\\\\.mlm|SSfol|SSfpl|SSgompertz|SSlogis|SSmicmen|SSweibull|start|start\\\\\\\\.default|stat\\\\\\\\.anova|step|stepfun|stl|str\\\\\\\\.dendrogram|str\\\\\\\\.logLik|StructTS|summary\\\\\\\\.aov|summary\\\\\\\\.aovlist|summary\\\\\\\\.ecdf|summary\\\\\\\\.glm|summary\\\\\\\\.infl|summary\\\\\\\\.lm|summary\\\\\\\\.loess|summary\\\\\\\\.manova|summary\\\\\\\\.mlm|summary\\\\\\\\.nls|summary\\\\\\\\.ppr|summary\\\\\\\\.prcomp|summary\\\\\\\\.princomp|summary\\\\\\\\.stepfun|summary\\\\\\\\.stl|summary\\\\\\\\.tukeysmooth|supsmu|symnum|t\\\\\\\\.test|t\\\\\\\\.test\\\\\\\\.default|t\\\\\\\\.test\\\\\\\\.formula|t\\\\\\\\.ts|tail\\\\\\\\.ts|termplot|terms|terms\\\\\\\\.aovlist|terms\\\\\\\\.default|terms\\\\\\\\.formula|terms\\\\\\\\.terms|Thin\\\\\\\\.col|Thin\\\\\\\\.row|time|time\\\\\\\\.default|time\\\\\\\\.ts|toeplitz2??|Tr|ts|ts\\\\\\\\.intersect|ts\\\\\\\\.plot|ts\\\\\\\\.union|tsdiag|tsdiag\\\\\\\\.Arima|tsdiag\\\\\\\\.arima0|tsdiag\\\\\\\\.StructTS|tsp|tsSmooth|tsSmooth\\\\\\\\.StructTS|TukeyHSD|TukeyHSD\\\\\\\\.aov|uniroot|update|update\\\\\\\\.default|update\\\\\\\\.formula|update\\\\\\\\.packageStatus|var|var\\\\\\\\.test|var\\\\\\\\.test\\\\\\\\.default|var\\\\\\\\.test\\\\\\\\.formula|variable\\\\\\\\.names|variable\\\\\\\\.names\\\\\\\\.default|variable\\\\\\\\.names\\\\\\\\.lm|varimax|vcov|vcov\\\\\\\\.aov|vcov\\\\\\\\.Arima|vcov\\\\\\\\.glm|vcov\\\\\\\\.lm|vcov\\\\\\\\.mlm|vcov\\\\\\\\.nls|vcov\\\\\\\\.summary\\\\\\\\.glm|vcov\\\\\\\\.summary\\\\\\\\.lm|weighted\\\\\\\\.mean|weighted\\\\\\\\.mean\\\\\\\\.Date|weighted\\\\\\\\.mean\\\\\\\\.default|weighted\\\\\\\\.mean\\\\\\\\.difftime|weighted\\\\\\\\.mean\\\\\\\\.POSIXct|weighted\\\\\\\\.mean\\\\\\\\.POSIXlt|weighted\\\\\\\\.residuals|weights|weights\\\\\\\\.default|weights\\\\\\\\.glm|weights\\\\\\\\.nls|wilcox\\\\\\\\.test|wilcox\\\\\\\\.test\\\\\\\\.default|wilcox\\\\\\\\.test\\\\\\\\.formula|Wilks|window|window\\\\\\\\.default|window\\\\\\\\.ts|write\\\\\\\\.ftable|xtabs)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.namespace.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.colons.r\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.r\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.r\\\"}},\\\"contentName\\\":\\\"meta.function-call.arguments.r\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.r\\\"}},\\\"name\\\":\\\"meta.function-call.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-arguments\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(?:(utils)(::))?(adist|alarm|apropos|aregexec|argNames|argsAnywhere|as\\\\\\\\.bibentry|as\\\\\\\\.bibentry\\\\\\\\.bibentry|as\\\\\\\\.bibentry\\\\\\\\.citation|as\\\\\\\\.character\\\\\\\\.person|as\\\\\\\\.character\\\\\\\\.roman|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.bibentry|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.citation|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.person|as\\\\\\\\.environment\\\\\\\\.hashtab|as\\\\\\\\.person|as\\\\\\\\.person\\\\\\\\.default|as\\\\\\\\.personList|as\\\\\\\\.personList\\\\\\\\.default|as\\\\\\\\.personList\\\\\\\\.person|as\\\\\\\\.relistable|as\\\\\\\\.roman|asDateBuilt|askYesNo|aspell|aspell_filter_LaTeX_commands_from_Aspell_tex_filter_info|aspell_filter_LaTeX_worker|aspell_find_dictionaries|aspell_find_program|aspell_inspect_context|aspell_package|aspell_package_C_files|aspell_package_description|aspell_package_pot_files|aspell_package_R_files|aspell_package_Rd_files|aspell_package_vignettes|aspell_query_wiktionary_categories|aspell_R_C_files|aspell_R_manuals|aspell_R_R_files|aspell_R_Rd_files|aspell_R_vignettes|aspell_update_dictionary|aspell_write_personal_dictionary_file|assignInMyNamespace|assignInNamespace|attachedPackageCompletions|available\\\\\\\\.packages|bibentry|blank_out_character_ranges|blank_out_ignores_in_lines|blank_out_regexp_matches|browseEnv|browseURL|browseVignettes|bug\\\\\\\\.report|bug\\\\\\\\.report\\\\\\\\.info|c\\\\\\\\.bibentry|c\\\\\\\\.person|capture\\\\\\\\.output|changedFiles|charClass|check_for_XQuartz|check_screen_device|checkCRAN|checkHT|chooseBioCmirror|chooseCRANmirror|citation|cite|citeNatbib|citEntry|citFooter|citHeader|close\\\\\\\\.socket|close\\\\\\\\.txtProgressBar|clrhash|combn|compareVersion|conformToProto|contrib\\\\\\\\.url|correctFilenameToken|count\\\\\\\\.fields|create\\\\\\\\.post|data|data\\\\\\\\.entry|dataentry|de|de\\\\\\\\.ncols|de\\\\\\\\.restore|de\\\\\\\\.setup|debugcall|debugger|defaultUserAgent|demo|download\\\\\\\\.file|download\\\\\\\\.packages|dump\\\\\\\\.frames|edit|edit\\\\\\\\.data\\\\\\\\.frame|edit\\\\\\\\.default|edit\\\\\\\\.matrix|edit\\\\\\\\.vignette|emacs|example|expr2token|file_test|file\\\\\\\\.edit|fileCompletionPreferred|fileCompletions|fileSnapshot|filter_packages_by_depends_predicates|find|find_files_in_directories|findCRANmirror|findExactMatches|findFuzzyMatches|findGeneric|findLineNum|findMatches|fix|fixInNamespace|flush\\\\\\\\.console|fnLineNum|format\\\\\\\\.aspell|format\\\\\\\\.aspell_inspect_context|format\\\\\\\\.bibentry|format\\\\\\\\.citation|format\\\\\\\\.hashtab|format\\\\\\\\.MethodsFunction|format\\\\\\\\.news_db|format\\\\\\\\.object_size|format\\\\\\\\.person|format\\\\\\\\.roman|formatOL|formatUL|functionArgs|fuzzyApropos|get_parse_data_for_message_strings|getAnywhere|getCRANmirrors|getDependencies|getFromNamespace|gethash|getIsFirstArg|getKnownS3generics|getParseData|getParseText|getRcode|getRcode\\\\\\\\.vignette|getS3method|getSrcByte|getSrcDirectory|getSrcfile|getSrcFilename|getSrcLocation|getSrcref|getTxtProgressBar|glob2rx|globalVariables|hashtab|hasName|head|head\\\\\\\\.array|head\\\\\\\\.default|head\\\\\\\\.ftable|head\\\\\\\\.function|head\\\\\\\\.matrix|help|help\\\\\\\\.request|help\\\\\\\\.search|help\\\\\\\\.start|helpCompletions|history|hsearch_db|hsearch_db_concepts|hsearch_db_keywords|index\\\\\\\\.search|inFunction|install\\\\\\\\.packages|installed\\\\\\\\.packages|is\\\\\\\\.hashtab|is\\\\\\\\.relistable|isBasePkg|isInsideQuotes|isS3method|isS3stdGeneric|keywordCompletions|length\\\\\\\\.hashtab|limitedLabels|loadedPackageCompletions|loadhistory|localeToCharset|ls\\\\\\\\.str|lsf\\\\\\\\.str|macDynLoads|maintainer|make_sysdata_rda|make\\\\\\\\.packages\\\\\\\\.html|make\\\\\\\\.socket|makeRegexpSafe|makeRweaveLatexCodeRunner|makeUserAgent|maphash|matchAvailableTopics|memory\\\\\\\\.limit|memory\\\\\\\\.size|menu|merge_demo_index|merge_vignette_index|methods|mirror2html|modifyList|new\\\\\\\\.packages|news|normalCompletions|nsl|numhash|object\\\\\\\\.size|offline_help_helper|old\\\\\\\\.packages|Ops\\\\\\\\.roman|package\\\\\\\\.skeleton|packageDate|packageDescription|packageName|packageStatus|packageVersion|page|person|personList|pico|print\\\\\\\\.aspell|print\\\\\\\\.aspell_inspect_context|print\\\\\\\\.bibentry|print\\\\\\\\.Bibtex|print\\\\\\\\.browseVignettes|print\\\\\\\\.changedFiles|print\\\\\\\\.citation|print\\\\\\\\.fileSnapshot|print\\\\\\\\.findLineNumResult|print\\\\\\\\.getAnywhere|print\\\\\\\\.hashtab|print\\\\\\\\.help_files_with_topic|print\\\\\\\\.hsearch|print\\\\\\\\.hsearch_db|print\\\\\\\\.Latex|print\\\\\\\\.ls_str|print\\\\\\\\.MethodsFunction|print\\\\\\\\.news_db|print\\\\\\\\.object_size|print\\\\\\\\.packageDescription|print\\\\\\\\.packageIQR|print\\\\\\\\.packageStatus|print\\\\\\\\.person|print\\\\\\\\.roman|print\\\\\\\\.sessionInfo|print\\\\\\\\.socket|print\\\\\\\\.summary\\\\\\\\.packageStatus|print\\\\\\\\.vignette|printhsearchInternal|process\\\\\\\\.events|prompt|prompt\\\\\\\\.data\\\\\\\\.frame|prompt\\\\\\\\.default|promptData|promptImport|promptPackage|rc\\\\\\\\.getOption|rc\\\\\\\\.options|rc\\\\\\\\.settings|rc\\\\\\\\.status|read\\\\\\\\.csv2??|read\\\\\\\\.delim2??|read\\\\\\\\.DIF|read\\\\\\\\.fortran|read\\\\\\\\.fwf|read\\\\\\\\.socket|read\\\\\\\\.table|readCitationFile|recover|registerNames|regquote|relist|relist\\\\\\\\.default|relist\\\\\\\\.factor|relist\\\\\\\\.list|relist\\\\\\\\.matrix|remhash|remove\\\\\\\\.packages|removeSource|rep\\\\\\\\.bibentry|rep\\\\\\\\.person|rep\\\\\\\\.roman|resolvePkgType|Rprof|Rprof_memory_summary|Rprofmem|RShowDoc|RSiteSearch|rtags|rtags\\\\\\\\.file|Rtangle|RtangleFinish|RtangleRuncode|RtangleSetup|RtangleWritedoc|RweaveChunkPrefix|RweaveEvalWithOpt|RweaveLatex|RweaveLatexFinish|RweaveLatexOptions|RweaveLatexRuncode|RweaveLatexSetup|RweaveLatexWritedoc|RweaveTryStop|savehistory|select\\\\\\\\.list|sessionInfo|setBreakpoint|sethash|setIsFirstArg|setRepositories|setTxtProgressBar|shorten\\\\\\\\.to\\\\\\\\.string|simplifyRepos|sort\\\\\\\\.bibentry|specialCompletions|specialFunctionArgs|specialOpCompletionsHelper|specialOpLocs|stack|stack\\\\\\\\.data\\\\\\\\.frame|stack\\\\\\\\.default|Stangle|str|str\\\\\\\\.data\\\\\\\\.frame|str\\\\\\\\.Date|str\\\\\\\\.default|str\\\\\\\\.hashtab|str\\\\\\\\.POSIXt|str2logical|strcapture|strextract|strOptions|strslice|subset\\\\\\\\.news_db|substr_with_tabs|summary\\\\\\\\.aspell|summary\\\\\\\\.packageStatus|Summary\\\\\\\\.roman|summaryRprof|suppressForeignCheck|Sweave|SweaveGetSyntax|SweaveHooks|SweaveParseOptions|SweaveReadFile|SweaveSyntConv|tail|tail\\\\\\\\.array|tail\\\\\\\\.default|tail\\\\\\\\.ftable|tail\\\\\\\\.function|tail\\\\\\\\.matrix|tar|timestamp|toBibtex|toBibtex\\\\\\\\.bibentry|toBibtex\\\\\\\\.person|toLatex|toLatex\\\\\\\\.sessionInfo|toLatexPDlist|topicName|transform\\\\\\\\.bibentry|txtProgressBar|type\\\\\\\\.convert|type\\\\\\\\.convert\\\\\\\\.data\\\\\\\\.frame|type\\\\\\\\.convert\\\\\\\\.default|type\\\\\\\\.convert\\\\\\\\.list|typhash|undebugcall|unique\\\\\\\\.bibentry|unique\\\\\\\\.person|unlist\\\\\\\\.relistable|unstack|unstack\\\\\\\\.data\\\\\\\\.frame|unstack\\\\\\\\.default|untar2??|unzip|update\\\\\\\\.packages|update\\\\\\\\.packageStatus|upgrade|upgrade\\\\\\\\.packageStatus|url\\\\\\\\.show|URLdecode|URLencode|vi|View|vignette|warnErrList|write\\\\\\\\.csv2??|write\\\\\\\\.ctags|write\\\\\\\\.etags|write\\\\\\\\.socket|write\\\\\\\\.table|wsbrowser|xedit|xemacs|zip)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.namespace.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.accessor.colons.r\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.r\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.r\\\"}},\\\"contentName\\\":\\\"meta.function-call.arguments.r\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.r\\\"}},\\\"name\\\":\\\"meta.function-call.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-arguments\\\"}]}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line.pragma.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.pragma.name.r\\\"}},\\\"match\\\":\\\"^(#pragma[\\\\\\\\t ]+mark)[\\\\\\\\t ](.*)\\\",\\\"name\\\":\\\"comment.line.pragma-mark.r\\\"},{\\\"begin\\\":\\\"(^[\\\\\\\\t ]+)?(?=#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.r\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.r\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.r\\\"}]}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(pi|letters|LETTERS|month\\\\\\\\.abb|month\\\\\\\\.name)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.misc.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b(TRUE|FALSE|NULL|NA|NA_integer_|NA_real_|NA_complex_|NA_character_|Inf|NaN)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b0([Xx])\\\\\\\\h+i\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.imaginary.hexadecimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9]+\\\\\\\\.?[0-9]*(?:([Ee])([-+])?[0-9]+)?i\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.imaginary.decimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\.[0-9]+(?:([Ee])([-+])?[0-9]+)?i\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.imaginary.decimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b0([Xx])\\\\\\\\h+L\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.hexadecimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9]+\\\\\\\\.?[0-9]*(?:([Ee])([-+])?[0-9]+)?L\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.decimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b0([Xx])\\\\\\\\h+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.hexadecimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9]+\\\\\\\\.?[0-9]*(?:([Ee])([-+])?[0-9]+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.decimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\.[0-9]+(?:([Ee])([-+])?[0-9]+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.decimal.r\\\"}]},\\\"function-call-arguments\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:[.A-Z_a-z][.\\\\\\\\w]*|`[^`]+`)(?=\\\\\\\\s*=[^=])\\\",\\\"name\\\":\\\"variable.parameter.function-call.r\\\"},{\\\"begin\\\":\\\"(?==)\\\",\\\"end\\\":\\\"(?=[),])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.parameters.r\\\"},{\\\"include\\\":\\\"source.r\\\"}]},\\\"function-calls\\\":{\\\"begin\\\":\\\"(?:[.A-Z_a-z][.\\\\\\\\w]*|`[^`]+`)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.r\\\"}},\\\"contentName\\\":\\\"meta.function-call.arguments.r\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.r\\\"}},\\\"name\\\":\\\"meta.function-call.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-arguments\\\"}]},\\\"function-declarations\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"([.A-Z_a-z][.\\\\\\\\w]*|`[^`]+`)\\\\\\\\s*(<?<-|=(?!=))\\\\\\\\s*\\\\\\\\b(function)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.r\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.r\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.r\\\"}},\\\"contentName\\\":\\\"meta.function.parameters.r\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.r\\\"}},\\\"name\\\":\\\"meta.function.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"[.A-Z_a-z][.\\\\\\\\w]*|`[^`]+`\\\",\\\"name\\\":\\\"variable.parameter.function.language.r\\\"},{\\\"begin\\\":\\\"(?==)\\\",\\\"end\\\":\\\"(?=[),])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.parameters.r\\\"}]}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bif\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"keyword.control.conditional.if.r\\\"},{\\\"match\\\":\\\"\\\\\\\\belse\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.conditional.else.r\\\"},{\\\"match\\\":\\\"\\\\\\\\bbreak\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.break.r\\\"},{\\\"match\\\":\\\"\\\\\\\\bnext\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.continue.r\\\"},{\\\"match\\\":\\\"\\\\\\\\breturn(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"keyword.control.flow.return.r\\\"},{\\\"match\\\":\\\"\\\\\\\\brepeat\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.loop.repeat.r\\\"},{\\\"match\\\":\\\"\\\\\\\\bfor\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"keyword.control.loop.for.r\\\"},{\\\"match\\\":\\\"\\\\\\\\bwhile\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"keyword.control.loop.while.r\\\"},{\\\"match\\\":\\\"\\\\\\\\bin\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.word.r\\\"}]},\\\"lambda-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(function)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.r\\\"}},\\\"contentName\\\":\\\"meta.function.parameters.r\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.r\\\"}},\\\"name\\\":\\\"meta.function.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"[.A-Z_a-z][.\\\\\\\\w]*|`[^`]+`\\\",\\\"name\\\":\\\"variable.parameter.function.language.r\\\"},{\\\"begin\\\":\\\"(?==)\\\",\\\"end\\\":\\\"(?=[),])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.parameters.r\\\"}]}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"%[*/ox]%\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.r\\\"},{\\\"match\\\":\\\"(<<-|->>)\\\",\\\"name\\\":\\\"keyword.operator.assignment.r\\\"},{\\\"match\\\":\\\"%(between|chin|do|dopar|in|like|\\\\\\\\+replace|[+:]|T>|<>|[$>])%\\\",\\\"name\\\":\\\"keyword.operator.other.r\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"name\\\":\\\"keyword.other.r\\\"},{\\\"match\\\":\\\":::?\\\",\\\"name\\\":\\\"punctuation.accessor.colons.r\\\"},{\\\"match\\\":\\\"(%%|\\\\\\\\*\\\\\\\\*)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.r\\\"},{\\\"match\\\":\\\"(<-|->)\\\",\\\"name\\\":\\\"keyword.operator.assignment.r\\\"},{\\\"match\\\":\\\"\\\\\\\\|>\\\",\\\"name\\\":\\\"keyword.operator.pipe.r\\\"},{\\\"match\\\":\\\"(==|!=|<>|<=?|>=?)\\\",\\\"name\\\":\\\"keyword.operator.comparison.r\\\"},{\\\"match\\\":\\\"(&&?|\\\\\\\\|\\\\\\\\|?)\\\",\\\"name\\\":\\\"keyword.operator.logical.r\\\"},{\\\"match\\\":\\\":=\\\",\\\"name\\\":\\\"keyword.operator.other.r\\\"},{\\\"match\\\":\\\"[-*+/^]\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.r\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.r\\\"},{\\\"match\\\":\\\"!\\\",\\\"name\\\":\\\"keyword.operator.logical.r\\\"},{\\\"match\\\":\\\"[:@~]\\\",\\\"name\\\":\\\"keyword.other.r\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.semicolon.r\\\"}]},\\\"roxygen\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#')\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.r\\\"}},\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.roxygen.r\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.r\\\"}},\\\"match\\\":\\\"(@param)\\\\\\\\s*([.A-Z_a-z][.\\\\\\\\w]*|`[^`]+`)\\\"},{\\\"match\\\":\\\"@[0-9A-Za-z]+\\\",\\\"name\\\":\\\"keyword.other.r\\\"}]}]},\\\"storage-type\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(character|complex|double|expression|integer|list|logical|numeric|single|raw|pairlist)\\\\\\\\b\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.r\\\"}},\\\"contentName\\\":\\\"meta.function-call.arguments.r\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.r\\\"}},\\\"name\\\":\\\"meta.function-call.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-arguments\\\"}]}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"[Rr]\\\\\\\"(-*)\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.begin.r\\\"}},\\\"end\\\":\\\"]\\\\\\\\1\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.end.r\\\"}},\\\"name\\\":\\\"string.quoted.double.raw.r\\\"},{\\\"begin\\\":\\\"[Rr]'(-*)\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.begin.r\\\"}},\\\"end\\\":\\\"]\\\\\\\\1'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.end.r\\\"}},\\\"name\\\":\\\"string.quoted.single.raw.r\\\"},{\\\"begin\\\":\\\"[Rr]\\\\\\\"(-*)\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.begin.r\\\"}},\\\"end\\\":\\\"}\\\\\\\\1\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.end.r\\\"}},\\\"name\\\":\\\"string.quoted.double.raw.r\\\"},{\\\"begin\\\":\\\"[Rr]'(-*)\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.begin.r\\\"}},\\\"end\\\":\\\"}\\\\\\\\1'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.end.r\\\"}},\\\"name\\\":\\\"string.quoted.single.raw.r\\\"},{\\\"begin\\\":\\\"[Rr]\\\\\\\"(-*)\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\\\\\\1\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.end.r\\\"}},\\\"name\\\":\\\"string.quoted.double.raw.r\\\"},{\\\"begin\\\":\\\"[Rr]'(-*)\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\\\\\\1'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.end.r\\\"}},\\\"name\\\":\\\"string.quoted.single.raw.r\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.r\\\"}},\\\"name\\\":\\\"string.quoted.double.r\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.r\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.r\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.r\\\"}},\\\"name\\\":\\\"string.quoted.single.r\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.r\\\"}]}]}},\\\"scopeName\\\":\\\"source.r\\\"}\"))\n\nexport default [\nlang\n]\n", "import r from './r.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"TeX\\\",\\\"name\\\":\\\"tex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#iffalse-block\\\"},{\\\"include\\\":\\\"#macro-control\\\"},{\\\"include\\\":\\\"#catcode\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"match\\\":\\\"[]\\\\\\\\[]\\\",\\\"name\\\":\\\"punctuation.definition.brackets.tex\\\"},{\\\"include\\\":\\\"#dollar-math\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"name\\\":\\\"keyword.control.newline.tex\\\"},{\\\"include\\\":\\\"#macro-general\\\"}],\\\"repository\\\":{\\\"braces\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.group.begin.tex\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.group.end.tex\\\"}},\\\"name\\\":\\\"meta.group.braces.tex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#braces\\\"}]},\\\"catcode\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.catcode.tex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.tex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.tex\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.category.tex\\\"}},\\\"match\\\":\\\"((\\\\\\\\\\\\\\\\)catcode)`\\\\\\\\\\\\\\\\?.(=)(\\\\\\\\d+)\\\",\\\"name\\\":\\\"meta.catcode.tex\\\"},\\\"comment\\\":{\\\"begin\\\":\\\"(^[\\\\\\\\t ]+)?(?=%)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.tex\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"%:?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.tex\\\"}},\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.percentage.tex\\\"},{\\\"begin\\\":\\\"^(%!TEX) (\\\\\\\\S*) =\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.tex\\\"}},\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.percentage.directive.tex\\\"}]},\\\"conditionals\\\":{\\\"begin\\\":\\\"(?<=^\\\\\\\\s*)\\\\\\\\\\\\\\\\if[a-z]*\\\",\\\"end\\\":\\\"(?<=^\\\\\\\\s*)\\\\\\\\\\\\\\\\fi\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#conditionals\\\"}]},\\\"dollar-math\\\":{\\\"begin\\\":\\\"(\\\\\\\\$\\\\\\\\$?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.tex\\\"}},\\\"end\\\":\\\"(\\\\\\\\1)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.tex\\\"}},\\\"name\\\":\\\"meta.math.block.tex support.class.math.block.tex\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\$\\\",\\\"name\\\":\\\"constant.character.escape.tex\\\"},{\\\"include\\\":\\\"#math-content\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"iffalse-block\\\":{\\\"begin\\\":\\\"(?<=^\\\\\\\\s*)((\\\\\\\\\\\\\\\\)iffalse)(?!\\\\\\\\s*[{}]\\\\\\\\s*\\\\\\\\\\\\\\\\fi)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.tex\\\"}},\\\"contentName\\\":\\\"comment.line.percentage.tex\\\",\\\"end\\\":\\\"((\\\\\\\\\\\\\\\\)(?:else|fi))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.tex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#braces\\\"},{\\\"include\\\":\\\"#conditionals\\\"}]},\\\"macro-control\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.tex\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)(backmatter|csname|else|endcsname|fi|frontmatter|mainmatter|unless|if(case|cat|csname|defined|dim|eof|false|fontchar|hbox|hmode|inner|mmode|num|odd|true|vbox|vmode|void|x)?)(?![@-Za-z])\\\",\\\"name\\\":\\\"keyword.control.tex\\\"},\\\"macro-general\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.function.tex\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)_*[@\\\\\\\\p{Alphabetic}]+(?:_[@\\\\\\\\p{Alphabetic}]+)*:[DFNTVcefnopvwx]*\\\",\\\"name\\\":\\\"support.class.general.latex3.tex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.function.tex\\\"}},\\\"match\\\":\\\"(\\\\\\\\.)[@\\\\\\\\p{Alphabetic}]+(?:_[@\\\\\\\\p{Alphabetic}]+)*:[DFNTVcefnopvwx]*\\\",\\\"name\\\":\\\"support.class.general.latex3.tex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.function.tex\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)(?:[,;]|[@\\\\\\\\p{Alphabetic}]+)\\\",\\\"name\\\":\\\"support.function.general.tex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.tex\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)[^@-Za-z]\\\",\\\"name\\\":\\\"constant.character.escape.tex\\\"}]},\\\"math-content\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)(?:text|mbox))(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.math.tex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.tex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.tex meta.text.normal.tex\\\"}},\\\"contentName\\\":\\\"meta.text.normal.tex\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.tex meta.text.normal.tex\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#math-content\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[{}]\\\",\\\"name\\\":\\\"punctuation.math.bracket.pair.tex\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(left|right|((bigg??|Bigg??)[lr]?))([]().<>\\\\\\\\[|]|\\\\\\\\\\\\\\\\[{|}]|\\\\\\\\\\\\\\\\[lr]?[Vv]ert|\\\\\\\\\\\\\\\\[lr]angle)\\\",\\\"name\\\":\\\"punctuation.math.bracket.pair.big.tex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.math.tex\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)(s(s(earrow|warrow|lash)|h(ort(downarrow|uparrow|parallel|leftarrow|rightarrow|mid)|arp)|tar|i(gma|m(eq)?)|u(cc(sim|n(sim|approx)|curlyeq|eq|approx)?|pset(neq(q)?|plus(eq)?|eq(q)?)?|rd|m|bset(neq(q)?|plus(eq)?|eq(q)?)?)|p(hericalangle|adesuit)|e(tminus|arrow)|q(su(pset(eq)?|bset(eq)?)|c([au]p)|uare)|warrow|m(ile|all(s(etminus|mile)|frown)))|h(slash|ook((?:lef|righ)tarrow)|eartsuit|bar)|R(sh|ightarrow|e|bag)|Gam(e|ma)|n(s(hort(parallel|mid)|im|u(cc(eq)?|pseteq(q)?|bseteq))|Rightarrow|n([ew]arrow)|cong|triangle(left(eq(slant)?)?|right(eq(slant)?)?)|i(plus)?|u|p(lus|arallel|rec(eq)?)|e(q|arrow|g|xists)|v([Dd]ash)|warrow|le(ss|q(slant|q)?|ft((?:|right)arrow))|a(tural|bla)|VDash|rightarrow|g(tr|eq(slant|q)?)|mid|Left((?:|right)arrow))|c(hi|irc(eq|le(d(circ|S|dash|ast)|arrow(left|right)))?|o(ng|prod|lon|mplement)|dot([ps])?|u(p|r(vearrow(left|right)|ly(eq(succ|prec)|vee((?:down|up)arrow)?|wedge((?:down|up)arrow)?)))|enterdot|lubsuit|ap)|Xi|Maps(to(char)?|from(char)?)|B(ox|umpeq|bbk)|t(h(ick(sim|approx)|e(ta|refore))|imes|op|wohead((?:lef|righ)tarrow)|a(u|lloblong)|riangle(down|q|left(eq(slant)?)?|right(eq(slant)?)?)?)|i(n(t(er(cal|leave))?|plus|fty)?|ota|math)|S(igma|u([bp]set))|zeta|o(slash|times|int|dot|plus|vee|wedge|lessthan|greaterthan|m(inus|ega)|b(slash|long|ar))|d(i(v(ideontimes)?|a(g(down|up)|mond(suit)?)|gamma)|o(t(plus|eq(dot)?)|ublebarwedge|wn(harpoon(left|right)|downarrows|arrow))|d(ots|agger)|elta|a(sh(v|leftarrow|rightarrow)|leth|gger))|Y(down|up|left|right)|C([au]p)|u(n([lr]hd)|p(silon|harpoon(left|right)|downarrow|uparrows|lus|arrow)|lcorner|rcorner)|jmath|Theta|Im|p(si|hi|i(tchfork)?|erp|ar(tial|allel)|r(ime|o(d|pto)|ec(sim|n(sim|approx)|curlyeq|eq|approx)?)|m)|e(t([ah])|psilon|q(slant(less|gtr)|circ|uiv)|ll|xists|mptyset)|Omega|D(iamond|ownarrow|elta)|v(d(ots|ash)|ee(bar)?|Dash|ar(s(igma|u(psetneq(q)?|bsetneq(q)?))|nothing|curly(vee|wedge)|t(heta|imes|riangle(left|right)?)|o(slash|circle|times|dot|plus|vee|wedge|lessthan|ast|greaterthan|minus|b(slash|ar))|p(hi|i|ropto)|epsilon|kappa|rho|bigcirc))|kappa|Up(silon|downarrow|arrow)|Join|f(orall|lat|a(t(s(emi|lash)|bslash)|llingdotseq)|rown)|P((?:s|h?)i)|w(p|edge|r)|l(hd|n(sim|eq(q)?|approx)|ceil|times|ightning|o(ng(left((?:|right)arrow)|rightarrow|maps(to|from))|zenge|oparrow(left|right))|dot([ps])|e(ss(sim|dot|eq(q?gtr)|approx|gtr)|q(slant|q)?|ft(slice|harpoon(down|up)|threetimes|leftarrows|arrow(t(ail|riangle))?|right(squigarrow|harpoons|arrow(s|triangle|eq)?))|adsto)|vertneqq|floor|l(c(orner|eil)|floor|l|bracket)?|a(ngle|mbda)|rcorner|bag)|a(s(ymp|t)|ngle|pprox(eq)?|l(pha|eph)|rrownot|malg)|V(v??dash)|r(h([do])|ceil|times|i(singdotseq|ght(s(quigarrow|lice)|harpoon(down|up)|threetimes|left(harpoons|arrows)|arrow(t(ail|riangle))?|rightarrows))|floor|angle|r(ceil|parenthesis|floor|bracket)|bag)|g(n(sim|eq(q)?|approx)|tr(sim|dot|eq(q?less)|less|approx)|imel|eq(slant|q)?|vertneqq|amma|g(g)?)|Finv|xi|m(ho|i(nuso|d)|o(o|dels)|u(ltimap)?|p|e(asuredangle|rge)|aps(to|from(char)?))|b(i(n(dnasrepma|ampersand)|g(s(tar|qc([au]p))|nplus|c(irc|u(p|rly(vee|wedge))|ap)|triangle(down|up)|interleave|o(times|dot|plus)|uplus|parallel|vee|wedge|box))|o(t|wtie|x(slash|circle|times|dot|plus|empty|ast|minus|b(slash|ox|ar)))|u(llet|mpeq)|e(cause|t(h|ween|a))|lack(square|triangle(down|left|right)?|lozenge)|a(ck(s(im(eq)?|lash)|prime|epsilon)|r(o|wedge))|bslash)|L(sh|ong(left((?:|right)arrow)|rightarrow|maps(to|from))|eft((?:|right)arrow)|leftarrow|ambda|bag)|Arrownot)(?![@-Za-z])\\\",\\\"name\\\":\\\"constant.character.math.tex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.math.tex\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)(sum|prod|coprod|int|oint|bigcap|bigcup|bigsqcup|bigvee|bigwedge|bigodot|bigotimes|bogoplus|biguplus)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.character.math.tex\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.math.tex\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)(arccos|arcsin|arctan|arg|cosh??|coth??|csc|deg|det|dim|exp|gcd|hom|inf|ker|lg|lim|liminf|limsup|ln|log|max|min|pr|sec|sinh??|sup|tanh??)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.math.tex\\\"},{\\\"begin\\\":\\\"((\\\\\\\\\\\\\\\\)Sexpr(\\\\\\\\{))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.sexpr.math.tex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.math.tex\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.embedded.begin.math.tex\\\"}},\\\"contentName\\\":\\\"support.function.sexpr.math.tex\\\",\\\"end\\\":\\\"(((})))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.sexpr.math.tex\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.embedded.end.math.tex\\\"},\\\"3\\\":{\\\"name\\\":\\\"source.r\\\"}},\\\"name\\\":\\\"meta.embedded.line.r\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?!})\\\",\\\"end\\\":\\\"(?=})\\\",\\\"name\\\":\\\"source.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.math.tex\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)(?!begin\\\\\\\\{|verb)([A-Za-z]+)\\\",\\\"name\\\":\\\"constant.other.general.math.tex\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)\\\\\\\\{\\\",\\\"name\\\":\\\"punctuation.math.begin.bracket.curly.tex\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)}\\\",\\\"name\\\":\\\"punctuation.math.end.bracket.curly.tex\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.math.begin.bracket.round.tex\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.math.end.bracket.round.tex\\\"},{\\\"match\\\":\\\"(([0-9]*\\\\\\\\.[0-9]+)|[0-9]+)\\\",\\\"name\\\":\\\"constant.numeric.math.tex\\\"},{\\\"match\\\":\\\"[-*+/]|(?<!\\\\\\\\^)\\\\\\\\^(?!\\\\\\\\^)|(?<!_)_(?!_)\\\",\\\"name\\\":\\\"punctuation.math.operator.tex\\\"}]}},\\\"scopeName\\\":\\\"text.tex\\\",\\\"embeddedLangs\\\":[\\\"r\\\"]}\"))\n\nexport default [\n...r,\nlang\n]\n"], "names": ["lang", "Object", "JSON"], "mappings": "qJAEA,IAAMA,EAAOC,OAAO,MAAM,CAACC,KAAK,KAAK,CAAC,8pnEAEtC,WAAe,IACZ,SAAG,CACNF,EACC,A,gCCPD,IAAMA,EAAOC,OAAO,MAAM,CAACC,KAAK,KAAK,CAAC,y5sDAEtC,WAAe,CACfF,EACC,A,gDCFD,IAAMA,EAAOC,OAAO,MAAM,CAACC,KAAK,KAAK,CAAC,itSAEtC,WAAe,IACZ,SAAC,CACJF,EACC,A"}