{"version": 3, "file": "static/js/async/5008.664df33c.js", "sources": ["webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/type.ts", "webpack://@coze-studio/app/../../packages/arch/idl/src/auto-generated/intelligence_api/namespaces/method_struct.ts", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/page-utils/predicate.ts", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/page-utils/parameters.ts", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/develop-filter-options.ts", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/page-utils/filters.ts", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/page-utils/copy.ts", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/hooks/use-card-actions.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/hooks/use-intelligence-list.ts", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/hooks/use-intelligence-actions.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/hooks/use-global-event-listeners.ts", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/service/intelligence-copy-task-polling-service.ts", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/hooks/use-project-copy-polling.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/hooks/use-cached-query-params.ts", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/components/bot-card/name.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/components/bot-card/menu-actions.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/components/bot-card/intelligence-tag.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/components/bot-card/description.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/components/bot-card/copy-process-mask.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/pages/develop/components/bot-card/index.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-adapter/src/pages/develop/index.tsx", "webpack://@coze-studio/app/./src/pages/develop.tsx", "webpack://@coze-studio/app/../../packages/agent-ide/space-bot/src/util/auth.ts", "webpack://@coze-studio/app/../../packages/agent-ide/space-bot/src/util/index.ts", "webpack://@coze-studio/app/../../packages/community/component/src/card/type.ts", "webpack://@coze-studio/app/../../packages/community/component/src/official-label/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/infinite-list/hooks/use-scroll.ts", "webpack://@coze-studio/app/../../packages/community/component/src/infinite-list/components/footer/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/infinite-list/components/empty/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/infinite-list/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/connector-list/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/favorite-icon-btn/hooks/use-farvorite-request.ts", "webpack://@coze-studio/app/../../packages/community/component/src/favorite-icon-btn/hooks/use-animation-change.ts", "webpack://@coze-studio/app/../../packages/community/component/src/favorite-icon-btn/hooks/use-favorite-change.ts", "webpack://@coze-studio/app/../../packages/community/component/src/favorite-icon-btn/components/favorite-icon-mobile/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/favorite-icon-btn/components/favorite-icon/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/favorite-icon-btn/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/sub-menu-item/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/card/components/tag/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/card/components/info/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/card/components/container/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/card/components/button/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/card/template/index.tsx", "webpack://@coze-studio/app/../../packages/community/component/src/card/plugin/index.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/components/creator.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/components/layout/list.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/components/workspace-empty.tsx", "webpack://@coze-studio/app/../../packages/studio/workspace/entry-base/src/constants/filter-style.ts", "webpack://@coze-studio/app/../../packages/community/component/src/card/components/button/index.module.less?5ed6", "webpack://@coze-studio/app/../../packages/community/component/src/card/components/container/index.module.less?67ce", "webpack://@coze-studio/app/../../packages/community/component/src/card/plugin/index.module.less?7b1b", "webpack://@coze-studio/app/../../packages/community/component/src/card/template/index.module.less?e900", "webpack://@coze-studio/app/../../packages/community/component/src/connector-list/index.module.less?6dad", "webpack://@coze-studio/app/../../packages/community/component/src/favorite-button/index.module.less?8069", "webpack://@coze-studio/app/../../packages/community/component/src/favorite-icon-btn/components/favorite-icon/index.module.less?6f24", "webpack://@coze-studio/app/../../packages/community/component/src/favorite-icon-btn/index.module.less?b49c", "webpack://@coze-studio/app/../../packages/community/component/src/infinite-list/components/empty/index.module.less?12fc", "webpack://@coze-studio/app/../../packages/community/component/src/infinite-list/components/footer/index.module.less?e80c", "webpack://@coze-studio/app/../../packages/community/component/src/infinite-list/index.module.less?121b", "webpack://@coze-studio/app/../../packages/community/component/src/official-label/index.module.less?cf49", "webpack://@coze-studio/app/../../packages/community/component/src/card/components/button/index.module.less", "webpack://@coze-studio/app/../../packages/community/component/src/card/components/container/index.module.less", "webpack://@coze-studio/app/../../packages/community/component/src/card/plugin/index.module.less", "webpack://@coze-studio/app/../../packages/community/component/src/card/template/index.module.less", "webpack://@coze-studio/app/../../packages/community/component/src/connector-list/index.module.less", "webpack://@coze-studio/app/../../packages/community/component/src/favorite-button/index.module.less", "webpack://@coze-studio/app/../../packages/community/component/src/favorite-icon-btn/components/favorite-icon/index.module.less", "webpack://@coze-studio/app/../../packages/community/component/src/favorite-icon-btn/index.module.less", "webpack://@coze-studio/app/../../packages/community/component/src/infinite-list/components/empty/index.module.less", "webpack://@coze-studio/app/../../packages/community/component/src/infinite-list/components/footer/index.module.less", "webpack://@coze-studio/app/../../packages/community/component/src/infinite-list/index.module.less", "webpack://@coze-studio/app/../../packages/community/component/src/official-label/index.module.less"], "sourcesContent": ["/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  type IntelligenceData,\n  type SearchScope,\n} from '@coze-arch/idl/intelligence_api';\n\nexport enum DevelopCustomPublishStatus {\n  All = 0,\n  Publish = 1,\n  NoPublish = 2,\n}\n\nexport enum DevelopCustomTypeStatus {\n  All = 0,\n  Project = 1,\n  Agent = 2,\n  DouyinAvatarBot = 3, // Single agent type Douyin doppelganger\n}\n\nexport interface DraftIntelligenceList {\n  list: IntelligenceData[];\n  hasMore: boolean;\n  nextCursorId: string | undefined;\n}\n\nexport interface FilterParamsType {\n  searchScope: SearchScope | undefined;\n  searchValue: string;\n  isPublish: DevelopCustomPublishStatus;\n  searchType: DevelopCustomTypeStatus;\n  recentlyOpen: boolean | undefined;\n}\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.\n/* eslint-disable */\n/* tslint:disable */\n// @ts-nocheck\n\nimport * as bot_common from './bot_common';\nimport * as intelligence_common_struct from './intelligence_common_struct';\nimport * as base from './base';\nimport * as common_struct from './common_struct';\nimport * as task_common from './task_common';\n\nexport type Int64 = string | number;\n\nexport enum AttributeValueType {\n  Unknown = 0,\n  String = 1,\n  Boolean = 2,\n  StringList = 11,\n  BooleanList = 12,\n}\n\n/** ************************** collaboration  ************************************** */\nexport enum CollaborationRole {\n  ProjectOwner = 1,\n  ProjectEditor = 2,\n  BotEditor = 3,\n  BotDeveloper = 4,\n  BotOperator = 5,\n  BotOwner = 6,\n}\n\nexport enum DiffMode {\n  PromptDiff = 1,\n  ModelDiff = 2,\n}\n\n/** ************************** DiffMode  ************************************** */\nexport enum DiffModeTargetType {\n  DraftBot = 1,\n}\n\nexport enum IntelligenceTaskStatus {\n  Success = 1,\n  Processing = 2,\n  Failed = 3,\n  Canceled = 4,\n}\n\nexport enum IntelligenceTaskType {\n  /** 存档 */\n  Archive = 1,\n  /** 回滚 */\n  Rollback = 2,\n}\n\nexport enum PermissionResourceType {\n  Account = 1,\n  Workspace = 2,\n  Bot = 4,\n  Enterprise = 15,\n  Project = 19,\n  OceanProject = 24,\n  FinetuneTask = 25,\n}\n\nexport enum TaskAction {\n  ProjectCopyCancel = 1,\n  ProjectCopyRetry = 2,\n}\n\nexport enum TaskType {\n  ProjectCopy = 1,\n  BotCopy = 2,\n}\n\nexport interface AttributeValue {\n  Type: AttributeValueType;\n  Value: string;\n}\n\nexport interface DiffModeConfig {\n  prompt?: string;\n  model_info?: bot_common.ModelInfo;\n  conversation_id?: string;\n}\n\nexport interface DiffModeInfo {\n  diff_mode: DiffMode;\n  diff_mode_config_a: DiffModeConfig;\n  diff_mode_config_b: DiffModeConfig;\n  create_time?: Int64;\n  update_time?: Int64;\n}\n\n/** ************************** task  ************************************** */\nexport interface EntityTaskData {\n  /** 实体ID */\n  entity_id?: string;\n  /** 实体状态 */\n  entity_status?: intelligence_common_struct.IntelligenceStatus;\n}\n\nexport interface EntityTaskListRequest {\n  space_id: string;\n  task_id_list: Array<string>;\n}\n\nexport interface EntityTaskListResponse {\n  data?: EntityTaskListResponseData;\n  code: Int64;\n  msg: string;\n}\n\nexport interface EntityTaskListResponseData {\n  task_list?: Array<TaskData>;\n}\n\nexport interface EntityTaskSearchRequest {\n  task_list?: Array<TaskStruct>;\n}\n\nexport interface EntityTaskSearchResponse {\n  data?: EntityTaskSearchResponseData;\n  code: Int64;\n  msg: string;\n}\n\nexport interface EntityTaskSearchResponseData {\n  /** key: entity_id */\n  entity_task_map?: Record<Int64, EntityTaskData>;\n}\n\nexport interface GetDiffModeInfoData {\n  diff_mode_info?: DiffModeInfo;\n  /** 是否有对比中数据 */\n  has_comparison_data?: boolean;\n}\n\nexport interface GetDiffModeInfoRequest {\n  target_type: DiffModeTargetType;\n  target_id: string;\n  Base?: base.Base;\n}\n\nexport interface GetDiffModeInfoResponse {\n  data?: GetDiffModeInfoData;\n  code: Int64;\n  msg: string;\n  BaseResp?: base.BaseResp;\n}\n\nexport interface GetIntelligenceTaskInfoRequest {\n  task_id: string;\n}\n\nexport interface GetIntelligenceTaskInfoResponse {\n  data?: IntelligenceTaskInfo;\n  code: Int64;\n  msg: string;\n}\n\nexport interface IntelligenceTaskInfo {\n  task_id?: string;\n  task_type?: IntelligenceTaskType;\n  task_status?: IntelligenceTaskStatus;\n}\n\nexport interface ListIntelligenceCollaborationData {\n  owner_info?: common_struct.User;\n  collaborator_info?: Array<common_struct.User>;\n  /** key: uid, value: 角色列表 */\n  collaboration_role_map?: Record<string, Array<CollaborationRole>>;\n}\n\nexport interface ListIntelligenceCollaborationRequest {\n  intelligence_id: string;\n  intelligence_type: intelligence_common_struct.IntelligenceType;\n}\n\nexport interface ListIntelligenceCollaborationResponse {\n  data: ListIntelligenceCollaborationData;\n  code: Int64;\n  msg: string;\n}\n\nexport interface PingRequest {\n  Base?: base.Base;\n}\n\nexport interface PingResponse {\n  BaseResp?: base.BaseResp;\n}\n\nexport interface ProcessEntityTaskRequest {\n  /** 实体ID */\n  entity_id?: string;\n  /** 任务动作 */\n  action?: TaskAction;\n  /** 批量实体ID */\n  task_id_list?: Array<string>;\n}\n\nexport interface ProcessEntityTaskResponse {\n  data?: ProcessEntityTaskResponseData;\n  code: Int64;\n  msg: string;\n}\n\nexport interface ProcessEntityTaskResponseData {\n  entity_task?: EntityTaskData;\n}\n\nexport interface ResourceIdentifier {\n  /** 资源类型 */\n  Type: PermissionResourceType;\n  /** 资源Id */\n  Id: string;\n}\n\nexport interface TaskData {\n  task_info?: task_common.IntelligenceTaskInfo;\n  task_logs?: Array<task_common.IntelligenceTaskLog>;\n  name?: string;\n  icon_url?: string;\n}\n\nexport interface TaskStruct {\n  /** 实体ID */\n  entity_id?: string;\n  /** task类型 */\n  task_type?: TaskType;\n}\n\nexport interface UpdateDiffModeInfoData {\n  diff_mode_info?: DiffModeInfo;\n}\n\nexport interface UpdateDiffModeInfoRequest {\n  target_type: DiffModeTargetType;\n  target_id: string;\n  /** 更新/初始化时传 */\n  diff_mode_info?: DiffModeInfo;\n  /** 退出并保存时传 */\n  exit_and_save?: boolean;\n  /** 退出并丢弃时传 */\n  exit_and_discard?: boolean;\n  Base?: base.Base;\n}\n\nexport interface UpdateDiffModeInfoResponse {\n  data?: UpdateDiffModeInfoData;\n  code: Int64;\n  msg: string;\n  BaseResp?: base.BaseResp;\n}\n/* eslint-enable */\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SearchScope } from '@coze-arch/idl/intelligence_api';\n\nimport { DevelopCustomPublishStatus } from '../type';\n\nexport function isPublishStatus(\n  val: unknown,\n): val is DevelopCustomPublishStatus {\n  const statusList: unknown[] = [\n    DevelopCustomPublishStatus.All,\n    DevelopCustomPublishStatus.NoPublish,\n    DevelopCustomPublishStatus.Publish,\n  ];\n\n  return statusList.includes(val);\n}\n\nexport const isRecentOpen = (val: unknown) => val === 'recentOpened';\n\nexport const isSearchScopeEnum = (val: unknown): val is SearchScope =>\n  val === SearchScope.All || val === SearchScope.CreateByMe;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { IntelligenceType } from '@coze-arch/idl/intelligence_api';\n\nimport { DevelopCustomPublishStatus, DevelopCustomTypeStatus } from '../type';\n\nexport const getPublishRequestParam = (\n  publishStatus: DevelopCustomPublishStatus | undefined,\n) => {\n  if (typeof publishStatus === 'undefined') {\n    return;\n  }\n  if (publishStatus === DevelopCustomPublishStatus.All) {\n    return;\n  }\n  return publishStatus === DevelopCustomPublishStatus.Publish;\n};\n\n/**\n * Project Type Request Front and Back End Parameter Mapping, Mapping DevelopCustomTypeStatus to Intelligence Type []\n * You need to decide whether to deal with DouyinAvatarBot according to whether the Douyin doppelganger can be displayed.\n * @param type\n * @returns\n */\nexport const getTypeRequestParams = ({\n  type,\n}: {\n  type: DevelopCustomTypeStatus;\n}) => {\n  const allIntelligenceTypeParams = [\n    IntelligenceType.Bot,\n    IntelligenceType.Project,\n  ];\n  const typeMap: Record<DevelopCustomTypeStatus, IntelligenceType[]> = {\n    [DevelopCustomTypeStatus.All]: allIntelligenceTypeParams,\n    [DevelopCustomTypeStatus.Agent]: [IntelligenceType.Bot],\n    [DevelopCustomTypeStatus.Project]: [IntelligenceType.Project],\n    [DevelopCustomTypeStatus.DouyinAvatarBot]: [\n      IntelligenceType.DouyinAvatarBot,\n    ],\n  };\n\n  return typeMap[type] || [];\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SearchScope } from '@coze-arch/idl/intelligence_api';\n\nimport {\n  DevelopCustomPublishStatus,\n  DevelopCustomTypeStatus,\n  type FilterParamsType,\n} from './type';\nexport const CREATOR_FILTER_OPTIONS = [\n  {\n    value: SearchScope.All,\n    labelI18NKey: 'bot_list_team',\n  },\n  {\n    value: SearchScope.CreateByMe,\n    labelI18NKey: 'bot_list_mine',\n  },\n] as const;\n\nexport const STATUS_FILTER_OPTIONS = [\n  {\n    value: DevelopCustomPublishStatus.All,\n    labelI18NKey: 'filter_all',\n  },\n  {\n    value: DevelopCustomPublishStatus.Publish,\n    labelI18NKey: 'Published_1',\n  },\n  {\n    value: 'recentOpened',\n    labelI18NKey: 'filter_develop_recent_opened',\n  },\n] as const;\n\nexport const TYPE_FILTER_OPTIONS = [\n  {\n    value: DevelopCustomTypeStatus.All,\n    labelI18NKey: 'filter_develop_all_types',\n  },\n  {\n    value: DevelopCustomTypeStatus.Project,\n    labelI18NKey: 'filter_develop_project',\n  },\n  {\n    value: DevelopCustomTypeStatus.Agent,\n    labelI18NKey: 'filter_develop_agent',\n  },\n] as const;\n\nexport const FILTER_PARAMS_DEFAULT: FilterParamsType = {\n  searchScope: SearchScope.All,\n  searchValue: '',\n  isPublish: DevelopCustomPublishStatus.All,\n  searchType: DevelopCustomTypeStatus.All,\n  recentlyOpen: undefined,\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { exhaustiveCheckForRecord } from '@coze-common/chat-area-utils';\nimport { SearchScope } from '@coze-arch/idl/intelligence_api';\n\nimport { DevelopCustomTypeStatus, type FilterParamsType } from '../type';\nimport { FILTER_PARAMS_DEFAULT } from '../develop-filter-options';\n\nexport const isEqualDefaultFilterParams = ({\n  filterParams,\n}: {\n  filterParams: FilterParamsType;\n}) => {\n  const {\n    searchScope,\n    searchValue,\n    searchType,\n    isPublish,\n    recentlyOpen,\n    ...rest\n  } = filterParams;\n  exhaustiveCheckForRecord(rest);\n  return (\n    searchScope === FILTER_PARAMS_DEFAULT.searchScope &&\n    searchType === FILTER_PARAMS_DEFAULT.searchType &&\n    isPublish === FILTER_PARAMS_DEFAULT.isPublish &&\n    recentlyOpen === FILTER_PARAMS_DEFAULT.recentlyOpen &&\n    !searchValue\n  );\n};\n\nexport const isFilterHighlight = (currentFilterParams: FilterParamsType) => {\n  const {\n    searchValue,\n    searchScope,\n    isPublish,\n    searchType,\n    recentlyOpen,\n    ...rest\n  } = currentFilterParams;\n  exhaustiveCheckForRecord(rest);\n  return {\n    isIntelligenceTypeFilterHighlight:\n      searchType !== DevelopCustomTypeStatus.All,\n    isOwnerFilterHighlight: searchScope !== SearchScope.All,\n    isPublishAndOpenFilterHighlight: isPublish || recentlyOpen,\n  };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { produce } from 'immer';\nimport {\n  type IntelligenceBasicInfo,\n  type IntelligenceData,\n  type User,\n} from '@coze-arch/idl/intelligence_api';\nimport { getUserInfo, getUserLabel } from '@coze-foundation/account-adapter';\n\nexport const produceCopyIntelligenceData = ({\n  originTemplateData,\n  newCopyData,\n}: {\n  originTemplateData: IntelligenceData;\n  newCopyData: {\n    ownerInfo: User | undefined;\n    basicInfo: IntelligenceBasicInfo;\n  };\n}) => {\n  // This is fallback\n  const userInfo = getUserInfo();\n  const userLabel = getUserLabel();\n  return produce<IntelligenceData>(originTemplateData, draft => {\n    const { type } = draft;\n    const { ownerInfo, basicInfo } = newCopyData;\n    return {\n      type,\n      owner_info: ownerInfo || {\n        user_id: userInfo?.user_id_str,\n        nickname: userInfo?.name,\n        avatar_url: userInfo?.avatar_url,\n        user_unique_name: userInfo?.app_user_info.user_unique_name,\n        user_label: userLabel || undefined,\n      },\n      basic_info: basicInfo,\n      permission_info: {\n        in_collaboration: false,\n        can_delete: true,\n        can_view: true,\n      },\n    };\n  });\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type Dispatch, type SetStateAction } from 'react';\n\nimport { cloneDeep, merge } from 'lodash-es';\nimport { produce } from 'immer';\nimport { EVENT_NAMES, sendTeaEvent } from '@coze-arch/bot-tea';\nimport {\n  type IntelligenceBasicInfo,\n  type IntelligenceData,\n} from '@coze-arch/bot-api/intelligence_api';\nimport { useCopyProjectModal } from '@coze-studio/project-entity-adapter';\n\nimport { type DraftIntelligenceList } from '../type';\nimport { produceCopyIntelligenceData } from '../page-utils/copy';\nimport { type AgentCopySuccessCallback } from '../components/bot-card/menu-actions';\n\nexport const useCardActions = ({\n  isPersonalSpace,\n  mutate,\n}: {\n  isPersonalSpace: boolean;\n  mutate: Dispatch<SetStateAction<DraftIntelligenceList | undefined>>;\n}) => {\n  const { modalContextHolder: copyModalHolder, openModal: onCopyProject } =\n    useCopyProjectModal({\n      onSuccess: ({ basicInfo, templateId, ownerInfo }) => {\n        mutate(prev =>\n          produce(prev, draft => {\n            const target = draft?.list.find(\n              intelligence => intelligence.basic_info?.id === templateId,\n            );\n\n            if (!target) {\n              return;\n            }\n\n            const copyData = produceCopyIntelligenceData({\n              originTemplateData: target,\n              newCopyData: { basicInfo, ownerInfo },\n            });\n            draft?.list.unshift(copyData);\n          }),\n        );\n      },\n    });\n\n  const mutateIntelligenceBasicInfo = (info: IntelligenceBasicInfo) => {\n    mutate(prev =>\n      produce(prev, draft => {\n        const target = draft?.list.find(i => i.basic_info?.id === info.id);\n        if (!target) {\n          return;\n        }\n        target.basic_info = info;\n      }),\n    );\n  };\n\n  const onCopyAgent: AgentCopySuccessCallback = param => {\n    mutate(prev =>\n      produce(prev, draft => {\n        const target = draft?.list.find(\n          intelligence => intelligence.basic_info?.id === param.templateId,\n        );\n\n        if (!target) {\n          return;\n        }\n\n        const copyData = produceCopyIntelligenceData({\n          originTemplateData: target,\n          newCopyData: {\n            ownerInfo: param.ownerInfo,\n            basicInfo: merge({}, target.basic_info, {\n              id: param.id,\n              name: param.name,\n            }),\n          },\n        });\n        draft?.list.unshift(copyData);\n      }),\n    );\n  };\n\n  const onDeleteMutate = ({ id }: { id: string }) => {\n    mutate(prev =>\n      produce(prev, draft => {\n        if (!draft?.list) {\n          return;\n        }\n        draft.list = draft.list.filter(item => item.basic_info?.id !== id);\n      }),\n    );\n  };\n\n  const onUpdate = (intelligenceData: IntelligenceData) => {\n    mutate(prev => {\n      if (!prev) {\n        return undefined;\n      }\n      const idx = prev.list.findIndex(\n        item => item.basic_info?.id === intelligenceData.basic_info?.id,\n      );\n\n      if (idx < 0) {\n        return;\n      }\n      const clonedList = cloneDeep(prev?.list ?? []);\n      clonedList.splice(idx, 1, intelligenceData);\n      return {\n        ...prev,\n        list: clonedList,\n      };\n    });\n  };\n\n  const onClick = (intelligenceData: IntelligenceData) => {\n    sendTeaEvent(EVENT_NAMES.workspace_action_front, {\n      space_id: intelligenceData.basic_info?.space_id ?? '',\n      space_type: isPersonalSpace ? 'personal' : 'teamspace',\n      tab_name: 'develop',\n      action: 'click',\n      id: intelligenceData.basic_info?.id,\n      name: intelligenceData.basic_info?.name,\n      type: 'agent',\n    });\n  };\n\n  return {\n    contextHolder: <>{copyModalHolder}</>,\n    actions: {\n      onClick,\n      onCopyProject,\n      onCopyAgent,\n      onUpdate,\n      onRetryCopy: mutateIntelligenceBasicInfo,\n      onCancelCopyAfterFailed: mutateIntelligenceBasicInfo,\n      onDeleteMutate,\n    },\n  };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useEffect, useRef } from 'react';\n\nimport axios, { type CancelTokenSource } from 'axios';\nimport { type InfiniteScrollOptions } from 'ahooks/lib/useInfiniteScroll/types';\nimport { useInfiniteScroll } from 'ahooks';\nimport { withSlardarIdButton } from '@coze-studio/bot-utils';\nimport {\n  createReportEvent,\n  REPORT_EVENTS as ReportEventNames,\n} from '@coze-arch/report-events';\nimport { logger } from '@coze-arch/logger';\nimport {\n  IntelligenceStatus,\n  type IntelligenceType,\n  type search,\n  type SearchScope,\n} from '@coze-arch/idl/intelligence_api';\nimport { I18n } from '@coze-arch/i18n';\nimport { Toast } from '@coze-arch/coze-design';\nimport { intelligenceApi } from '@coze-arch/bot-api';\n\nimport { type DraftIntelligenceList } from '../type';\n\nconst pageSize = 24;\n\nexport interface FilterParamsType {\n  types: IntelligenceType[];\n  spaceId: string;\n  hasPublished?: boolean;\n  searchValue?: string;\n  recentlyOpen?: boolean;\n  searchScope?: SearchScope;\n  orderBy: search.OrderBy;\n}\n\nconst getIntelligenceList = async (\n  dataSource: DraftIntelligenceList | undefined,\n  {\n    spaceId,\n    types,\n    searchValue,\n    hasPublished,\n    recentlyOpen,\n    searchScope,\n    orderBy,\n  }: FilterParamsType,\n  cancelTokenRef: React.MutableRefObject<CancelTokenSource | null>,\n) => {\n  // Reset the cancel token every time a new request is made.\n  const source = axios.CancelToken.source();\n  cancelTokenRef.current = source;\n  const resp = await intelligenceApi\n    .GetDraftIntelligenceList(\n      {\n        space_id: spaceId,\n        name: searchValue,\n        types,\n        size: pageSize,\n        has_published: hasPublished,\n        recently_open: recentlyOpen,\n        cursor_id: dataSource?.nextCursorId,\n        search_scope: searchScope,\n        // Fixed value, from historical code\n        order_by: orderBy,\n        status: [\n          IntelligenceStatus.Using,\n          IntelligenceStatus.Banned,\n          IntelligenceStatus.MoveFailed,\n        ],\n      },\n      { cancelToken: source.token, __disableErrorToast: true },\n    )\n    .catch(e => {\n      if (e.message !== 'canceled') {\n        Toast.error({\n          content: withSlardarIdButton(e.msg || e.message || I18n.t('error')),\n          showClose: false,\n        });\n      }\n    });\n\n  if (resp?.data) {\n    return {\n      list: resp.data.intelligences ?? [],\n      hasMore: Boolean(resp.data.has_more),\n      nextCursorId: resp.data.next_cursor_id,\n    };\n  } else {\n    return {\n      list: [],\n      hasMore: false,\n      nextCursorId: undefined,\n    };\n  }\n};\n\nconst buildBotLogger = logger.createLoggerWith({\n  ctx: {\n    namespace: 'bot_list',\n  },\n});\n\nconst getBotListReportEvent = createReportEvent({\n  eventName: ReportEventNames.getBotList,\n  logger: buildBotLogger,\n});\n\nexport const useIntelligenceList = ({\n  params: {\n    spaceId,\n    types,\n    searchValue,\n    hasPublished,\n    recentlyOpen,\n    searchScope,\n    orderBy,\n  },\n  onBefore,\n  onSuccess,\n  onError,\n}: {\n  params: FilterParamsType;\n} & Pick<\n  InfiniteScrollOptions<DraftIntelligenceList>,\n  'onBefore' | 'onSuccess' | 'onError'\n>) => {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const cancelTokenRef = useRef<CancelTokenSource | null>(null);\n\n  const listResp = useInfiniteScroll<DraftIntelligenceList>(\n    async dataSource =>\n      await getIntelligenceList(\n        dataSource,\n        {\n          spaceId,\n          types,\n          searchValue,\n          hasPublished,\n          recentlyOpen,\n          searchScope,\n          orderBy,\n        },\n        cancelTokenRef,\n      ),\n    {\n      target: containerRef,\n      reloadDeps: [\n        types.join(','),\n        searchValue,\n        hasPublished,\n        recentlyOpen,\n        searchScope,\n        orderBy,\n        spaceId,\n      ],\n      isNoMore: dataSource => !dataSource?.hasMore,\n      onBefore: () => {\n        if (listResp.loadingMore || listResp.loading) {\n          cancelTokenRef.current?.cancel();\n        }\n        getBotListReportEvent.start();\n        onBefore?.();\n      },\n      onSuccess: (...res) => {\n        getBotListReportEvent.success();\n        onSuccess?.(...res);\n      },\n      onError: e => {\n        getBotListReportEvent.error({\n          error: e,\n          reason: e.message,\n        });\n        onError?.(e);\n      },\n    },\n  );\n\n  useEffect(\n    () => () => {\n      // Cancel the requested interface\n      cancelTokenRef.current?.cancel();\n    },\n    [spaceId],\n  );\n\n  return { listResp, containerRef, cancelTokenRef };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useNavigate } from 'react-router-dom';\nimport { type SetStateAction, type Dispatch, type ReactNode } from 'react';\n\nimport { I18n } from '@coze-arch/i18n';\nimport {\n  type DeleteIntelligenceParam,\n  useCreateProjectModal,\n  useDeleteIntelligence,\n  type CreateProjectHookProps,\n} from '@coze-studio/project-entity-adapter';\nimport { cozeMitt } from '@coze-common/coze-mitt';\nimport { Toast } from '@coze-arch/coze-design';\n\nimport { type DraftIntelligenceList } from '../type';\n\nconst showCreateSuccessToast = () => {\n  Toast.success({\n    content: I18n.t('creat_project_toast_success'),\n    showClose: false,\n  });\n};\n\nexport const useIntelligenceActions = ({\n  spaceId,\n  mutateList,\n  reloadList,\n  extraGuideButtonConfigs,\n}: {\n  spaceId: string;\n  reloadList: () => void;\n  mutateList: Dispatch<SetStateAction<DraftIntelligenceList | undefined>>;\n  extraGuideButtonConfigs?: CreateProjectHookProps['extraGuideButtonConfigs'];\n}): {\n  contextHolder: ReactNode;\n  actions: {\n    createIntelligence: () => void;\n    deleteIntelligence: (param: DeleteIntelligenceParam) => void;\n  };\n} => {\n  const navigate = useNavigate();\n\n  const navigateToProjectIDE = (inputProjectId: string) =>\n    navigate(`/space/${spaceId}/project-ide/${inputProjectId}`);\n\n  const {\n    modalContextHolder: createModalContextHolder,\n    createProject: createIntelligence,\n  } = useCreateProjectModal({\n    selectSpace: false,\n    bizCreateFrom: 'space',\n    initialSpaceId: spaceId,\n    extraGuideButtonConfigs,\n    onCreateBotSuccess: botId => {\n      if (botId) {\n        navigate(`/space/${spaceId}/bot/${botId}`);\n      }\n    },\n    onCreateProjectSuccess: ({ projectId }) => {\n      showCreateSuccessToast();\n      navigateToProjectIDE(projectId);\n    },\n    onCopyProjectTemplateSuccess: () => {\n      reloadList();\n    },\n  });\n\n  const handleDeleteIntelligenceAndMutate = (mutateDeleteId: string) => {\n    Toast.success({\n      content: I18n.t('project_ide_toast_delete_success'),\n      showClose: false,\n    });\n    cozeMitt.emit('refreshFavList', { id: mutateDeleteId, numDelta: -1 });\n    mutateList(prev =>\n      prev\n        ? {\n            ...prev,\n            list: prev.list.filter(\n              item => item.basic_info?.id !== mutateDeleteId,\n            ),\n          }\n        : undefined,\n    );\n  };\n\n  const { modalContextHolder: deleteModalContextHolder, deleteIntelligence } =\n    useDeleteIntelligence({\n      onDeleteAgentSuccess: agentParam => {\n        handleDeleteIntelligenceAndMutate(agentParam.agentId);\n      },\n      onDeleteProjectSuccess: projectParam => {\n        handleDeleteIntelligenceAndMutate(projectParam.projectId);\n      },\n    });\n\n  return {\n    contextHolder: (\n      <>\n        {createModalContextHolder}\n        {deleteModalContextHolder}\n      </>\n    ),\n    actions: {\n      createIntelligence,\n      deleteIntelligence,\n    },\n  };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useEffect } from 'react';\n\nimport {\n  cozeMitt,\n  type RefreshFavListParams,\n  type CreateProjectByCopyTemplateFromSidebarParam,\n} from '@coze-common/coze-mitt';\n\nexport const useGlobalEventListeners = ({\n  reload,\n  spaceId,\n}: {\n  reload: () => void;\n  spaceId: string;\n}) => {\n  useEffect(() => {\n    const handlerRefreshFavList = (\n      refreshFavListParams: RefreshFavListParams,\n    ) => {\n      // Refresh the list only when the workspace collection is cancelled and the collection is changed.\n      if (refreshFavListParams.emitPosition === 'favorites-list-item') {\n        reload();\n      }\n    };\n    const handleReloadConditionally = (\n      eventParam: CreateProjectByCopyTemplateFromSidebarParam,\n    ) => {\n      if (eventParam.toSpaceId !== spaceId) {\n        return;\n      }\n      reload();\n    };\n    cozeMitt.on('refreshFavList', handlerRefreshFavList);\n    cozeMitt.on(\n      'createProjectByCopyTemplateFromSidebar',\n      handleReloadConditionally,\n    );\n    return () => {\n      cozeMitt.off('refreshFavList', handlerRefreshFavList);\n      cozeMitt.off(\n        'createProjectByCopyTemplateFromSidebar',\n        handleReloadConditionally,\n      );\n    };\n  }, []);\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport mitt, { type Emitter } from 'mitt';\nimport { uniqBy } from 'lodash-es';\nimport {\n  type EntityTaskData,\n  IntelligenceStatus,\n  type TaskStruct,\n} from '@coze-arch/idl/intelligence_api';\nimport { intelligenceApi } from '@coze-arch/bot-api';\n\n// eslint-disable-next-line @typescript-eslint/consistent-type-definitions\nexport type PollCopyTaskEvent = {\n  onCopyTaskUpdate: EntityTaskData[];\n};\n\nexport class IntelligenceCopyTaskPollingService {\n  readonly defaultTimeout = 2000;\n  readonly timeoutStep = 2000;\n  taskPool: TaskStruct[] = [];\n  timeout = this.defaultTimeout;\n  timerId: ReturnType<typeof setTimeout> | null = null;\n  eventCenter: Emitter<PollCopyTaskEvent>;\n\n  constructor() {\n    this.eventCenter = mitt<PollCopyTaskEvent>();\n  }\n\n  removeTaskPoll = (params: EntityTaskData[]) => {\n    this.taskPool = this.taskPool.filter(\n      task => !params.find(p => p.entity_id === task.entity_id),\n    );\n  };\n\n  poll = async () => {\n    const response = await intelligenceApi.EntityTaskSearch({\n      task_list: this.taskPool,\n    });\n    const taskMap = response.data?.entity_task_map ?? {};\n    const taskList = Object.entries(taskMap).map(([_, task]) => task);\n\n    const finishPollList = taskList.filter(\n      task => task.entity_status !== IntelligenceStatus.Copying,\n    );\n\n    this.removeTaskPoll(finishPollList);\n\n    this.eventCenter.emit('onCopyTaskUpdate', taskList);\n  };\n\n  resetTimeout = () => {\n    this.timeout = this.defaultTimeout;\n  };\n\n  increaseTimeout = () => {\n    this.timeout += this.timeoutStep;\n  };\n\n  checkIsContinuePoll = () => Boolean(this.taskPool.length);\n\n  clearTimer = () => {\n    if (!this.timerId) {\n      return;\n    }\n    clearTimeout(this.timerId);\n  };\n\n  run = () => {\n    this.timerId = setTimeout(async () => {\n      await this.poll();\n      if (!this.checkIsContinuePoll()) {\n        return;\n      }\n      this.increaseTimeout();\n      this.run();\n    }, this.timeout);\n  };\n\n  registerPolling = (params: TaskStruct[]) => {\n    const prevLength = this.taskPool.length;\n\n    this.taskPool = uniqBy(\n      this.taskPool.concat(params),\n      task => task.entity_id,\n    );\n\n    const currentLength = this.taskPool.length;\n\n    if (!prevLength && currentLength) {\n      this.resetTimeout();\n      this.run();\n    }\n  };\n\n  clearAll = () => {\n    this.clearTimer();\n    this.eventCenter.off('onCopyTaskUpdate');\n    this.taskPool = [];\n    this.timerId = null;\n  };\n}\n\nexport const intelligenceCopyTaskPollingService =\n  new IntelligenceCopyTaskPollingService();\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useNavigate } from 'react-router-dom';\nimport { type Dispatch, type SetStateAction, useEffect } from 'react';\n\nimport { produce } from 'immer';\nimport {\n  type IntelligenceData,\n  IntelligenceStatus,\n  IntelligenceType,\n  TaskAction,\n} from '@coze-arch/idl/intelligence_api';\nimport { CopyTaskType } from '@coze-arch/idl';\nimport { I18n } from '@coze-arch/i18n';\nimport { Button, Toast } from '@coze-arch/coze-design';\nimport { intelligenceApi } from '@coze-arch/bot-api';\n\nimport { type DraftIntelligenceList } from '../type';\nimport {\n  intelligenceCopyTaskPollingService,\n  type PollCopyTaskEvent,\n} from '../service/intelligence-copy-task-polling-service';\n\nconst registerCopyTaskPolling = (data: IntelligenceData[]) => {\n  intelligenceCopyTaskPollingService.registerPolling(\n    data\n      .filter(\n        intelligence =>\n          intelligence.type === IntelligenceType.Project &&\n          intelligence.basic_info?.status === IntelligenceStatus.Copying,\n      )\n      .map(i => ({\n        entity_id: i.basic_info?.id,\n        task_type: CopyTaskType.ProjectCopy,\n      })),\n  );\n};\n\nexport const useProjectCopyPolling = ({\n  spaceId,\n  listData,\n  mutate,\n}: {\n  spaceId: string;\n  mutate: Dispatch<SetStateAction<DraftIntelligenceList | undefined>>;\n  listData?: IntelligenceData[];\n}) => {\n  const navigate = useNavigate();\n  const navigateToProjectIDE = (inputProjectId: string) =>\n    navigate(`/space/${spaceId}/project-ide/${inputProjectId}`);\n\n  useEffect(() => {\n    if (listData) {\n      registerCopyTaskPolling(listData);\n    }\n  }, [listData]);\n\n  useEffect(() => {\n    const onTaskUpdate = (list: PollCopyTaskEvent['onCopyTaskUpdate']) => {\n      mutate(prev =>\n        produce(prev, draft => {\n          list.forEach(task => {\n            const target = draft?.list.find(\n              intelligence => intelligence.basic_info?.id === task.entity_id,\n            );\n            if (!target || !target.basic_info) {\n              return;\n            }\n            target.basic_info.status = task.entity_status;\n          });\n        }),\n      );\n      // Need to be re-packaged\n      list.forEach(item => {\n        if (item.entity_status === IntelligenceStatus.Using) {\n          const successToastId = Toast.success({\n            content: (\n              <>\n                {I18n.t('project_ide_toast_duplicate_success')}\n                <Button\n                  className=\"ml-6px\"\n                  color=\"primary\"\n                  onClick={() => {\n                    Toast.close(successToastId);\n                    navigateToProjectIDE(item.entity_id ?? '');\n                  }}\n                >\n                  {I18n.t('project_ide_toast_duplicate_view')}\n                </Button>\n              </>\n            ),\n            showClose: false,\n          });\n          return;\n        }\n        if (item.entity_status === IntelligenceStatus.CopyFailed) {\n          const failedToastId = Toast.error({\n            content: (\n              <>\n                {I18n.t('project_ide_toast_duplicate_fail')}\n                <Button\n                  className=\"ml-6px\"\n                  color=\"primary\"\n                  onClick={async () => {\n                    Toast.close(failedToastId);\n                    const response = await intelligenceApi.ProcessEntityTask({\n                      entity_id: item.entity_id,\n                      action: TaskAction.ProjectCopyRetry,\n                    });\n                    mutate(prev =>\n                      produce(prev, draft => {\n                        const target = draft?.list.find(\n                          intelligence =>\n                            intelligence.basic_info?.id === item.entity_id,\n                        );\n\n                        if (!target || !target.basic_info) {\n                          return;\n                        }\n                        target.basic_info.status =\n                          response.data?.entity_task?.entity_status;\n                      }),\n                    );\n                  }}\n                >\n                  {I18n.t('project_ide_toast_duplicate_fail_retry')}\n                </Button>\n              </>\n            ),\n            showClose: false,\n          });\n          return;\n        }\n      });\n    };\n    intelligenceCopyTaskPollingService.eventCenter.on(\n      'onCopyTaskUpdate',\n      onTaskUpdate,\n    );\n    return () => {\n      intelligenceCopyTaskPollingService.clearAll();\n      intelligenceCopyTaskPollingService.eventCenter.off(\n        'onCopyTaskUpdate',\n        onTaskUpdate,\n      );\n    };\n  }, []);\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useEffect, useState } from 'react';\n\nimport { isObject, merge } from 'lodash-es';\nimport { useDebounceFn, useUpdateEffect } from 'ahooks';\nimport { safeJSONParse } from '@coze-agent-ide/space-bot/util';\nimport { EVENT_NAMES, sendTeaEvent } from '@coze-arch/bot-tea';\nimport { localStorageService } from '@coze-foundation/local-storage';\n\nimport { type FilterParamsType } from '../type';\nimport { FILTER_PARAMS_DEFAULT } from '../develop-filter-options';\n\nconst isPersistentFilterParamsType = (\n  params: unknown,\n): params is Partial<FilterParamsType> => isObject(params);\n\nconst getDefaultFilterParams = async () => {\n  const localFilterParams = await localStorageService.getValueSync(\n    'workspace-develop-filters',\n  );\n  if (!localFilterParams) {\n    return FILTER_PARAMS_DEFAULT;\n  }\n  const parsedFilterParams = safeJSONParse(localFilterParams) as unknown;\n  if (isPersistentFilterParamsType(parsedFilterParams)) {\n    return merge({}, FILTER_PARAMS_DEFAULT, parsedFilterParams);\n  }\n  return FILTER_PARAMS_DEFAULT;\n};\n\nexport const useCachedQueryParams = () => {\n  const [filterParams, setFilterParams] = useState<FilterParamsType>(\n    FILTER_PARAMS_DEFAULT,\n  );\n\n  useUpdateEffect(() => {\n    /** When the filter conditions change, take the appropriate key and store it locally */\n    const { searchScope, isPublish, recentlyOpen, searchType } = filterParams;\n    localStorageService.setValue(\n      'workspace-develop-filters',\n      JSON.stringify({\n        searchScope,\n        isPublish,\n        searchType,\n        recentlyOpen,\n      }),\n    );\n  }, [filterParams]);\n\n  useEffect(() => {\n    /** Asynchronously reads filters from local storage */\n    getDefaultFilterParams().then(filters => {\n      setFilterParams(prev => merge({}, prev, filters));\n    });\n  }, []);\n\n  const debouncedSetSearchValue = useDebounceFn(\n    (searchValue = '') => {\n      setFilterParams(params => ({\n        ...params,\n        searchValue,\n      }));\n      // Tea event tracking\n      sendTeaEvent(EVENT_NAMES.search_front, {\n        full_url: location.href,\n        source: 'develop',\n        search_word: searchValue,\n      });\n    },\n    {\n      wait: 300,\n    },\n  );\n\n  return [filterParams, setFilterParams, debouncedSetSearchValue.run] as const;\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type FC } from 'react';\n\nimport { Typography } from '@coze-arch/coze-design';\n\nexport interface NameProps {\n  name?: string;\n}\n\nconst Name: FC<NameProps> = ({ name }) => (\n  <Typography.Text\n    className=\"text-[16px] font-[500] leading-[22px]\"\n    ellipsis={{\n      showTooltip: {\n        opts: {\n          content: <span onClick={e => e.stopPropagation()}>{name}</span>,\n          style: { wordBreak: 'break-word' },\n          theme: 'dark',\n        },\n        type: 'tooltip',\n      },\n      rows: 1,\n    }}\n  >\n    {name}\n  </Typography.Text>\n);\n\nexport default Name;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useNavigate } from 'react-router-dom';\nimport { useRef, type FC } from 'react';\n\nimport { cozeMitt } from '@coze-common/coze-mitt';\nimport { logger } from '@coze-arch/logger';\nimport { type User } from '@coze-arch/idl/intelligence_api';\nimport { I18n } from '@coze-arch/i18n';\nimport { IconCozWarningCircleFill } from '@coze-arch/coze-design/icons';\nimport { Menu, Toast, Tooltip } from '@coze-arch/coze-design';\nimport { EVENT_NAMES, sendTeaEvent } from '@coze-arch/bot-tea';\nimport { useUIModal } from '@coze-arch/bot-semi';\nimport { CustomError } from '@coze-arch/bot-error';\nimport { DeveloperApi } from '@coze-arch/bot-api';\n\nexport interface MenuCommonProps {\n  id: string;\n  spaceID: string;\n}\n\nexport interface MenuAnalysisProps extends MenuCommonProps {\n  disabled?: boolean;\n}\n\nexport type AgentCopySuccessCallback = (param: {\n  templateId: string;\n  id: string;\n  name: string;\n  ownerInfo: Required<User>;\n}) => void;\n\nexport const MenuAnalysis: FC<MenuAnalysisProps> = ({\n  disabled,\n  spaceID,\n  id,\n}) => {\n  const navigate = useNavigate();\n\n  return (\n    <Menu.Item\n      disabled={disabled}\n      onClick={() => {\n        navigate(`/space/${spaceID}/bot/${id}/analysis`);\n      }}\n    >\n      {I18n.t('analytics_page_title')}\n    </Menu.Item>\n  );\n};\n\nexport interface MenuCopyBotProps extends MenuCommonProps {\n  disabled?: boolean;\n  name?: string;\n\n  onCopySuccess?: AgentCopySuccessCallback;\n  onClose?: () => void;\n}\n\nexport const MenuCopyBot: FC<MenuCopyBotProps> = ({\n  disabled,\n  id,\n  name,\n  spaceID,\n  onCopySuccess,\n  onClose,\n}) => {\n  const lock = useRef(false);\n\n  const copyBot = async () => {\n    try {\n      lock.current = true;\n      const response = await DeveloperApi.DuplicateDraftBot({\n        space_id: spaceID,\n        bot_id: id,\n      });\n      Toast.success({\n        content: I18n.t('bot_duplicateded_toast'),\n        showClose: false,\n      });\n      const {\n        bot_id = '',\n        name: newBotName = '',\n        user_info = {},\n      } = response.data;\n      const {\n        id: userId = '',\n        name: userName = '',\n        avatar_url = '',\n        user_unique_name = '',\n        user_label = {},\n      } = user_info;\n      onCopySuccess?.({\n        templateId: id,\n        id: bot_id,\n        name: newBotName,\n        ownerInfo: {\n          user_id: userId,\n          nickname: userName,\n          avatar_url,\n          user_unique_name,\n          user_label,\n        },\n      });\n    } catch (error) {\n      logger.error({\n        error: new CustomError('copy bot', 'copy bot error'),\n      });\n    } finally {\n      onClose?.();\n      lock.current = false;\n    }\n  };\n\n  return (\n    <Tooltip\n      trigger={disabled ? 'custom' : 'hover'}\n      content={I18n.t('coze_copy_to_tips_1')}\n    >\n      <Menu.Item\n        data-testid=\"bot-card.copy\"\n        disabled={disabled}\n        onClick={() => {\n          if (lock.current) {\n            return;\n          }\n          sendTeaEvent(EVENT_NAMES.bot_duplicate_click, {\n            bot_type: 'team_bot',\n          });\n          // team bot header\n          sendTeaEvent(EVENT_NAMES.bot_duplicate_click_front, {\n            bot_type: 'team_bot',\n            bot_id: id,\n            bot_name: name,\n            from: 'bots_card',\n            source: 'bots_card',\n          });\n          copyBot();\n        }}\n      >\n        {I18n.t('duplicate')}\n      </Menu.Item>\n    </Tooltip>\n  );\n};\n\nexport interface MenuDeleteBotProps extends MenuCommonProps {\n  onDeleteSuccess?: () => void;\n  onClick?: () => void;\n  onClose?: () => void;\n}\n\nexport const MenuDeleteBot: FC<MenuDeleteBotProps> = ({\n  spaceID,\n  id,\n  onDeleteSuccess,\n  onClick,\n  onClose,\n}) => {\n  const deleteBot = async () => {\n    try {\n      await DeveloperApi.DeleteDraftBot({\n        space_id: spaceID,\n        bot_id: id,\n      });\n      Toast.success({\n        content: I18n.t('bot_deleted_toast'),\n        showClose: false,\n      });\n      onDeleteSuccess?.();\n      cozeMitt.emit('refreshFavList', {\n        id,\n        numDelta: -1,\n      });\n    } catch (error) {\n      logger.error({\n        error: new CustomError('delete bot', 'delete bot error'),\n      });\n    }\n  };\n\n  const { open, close, modal } = useUIModal({\n    type: 'info',\n    title: I18n.t('bot_delete_confirm_title'),\n    onOk: async () => await deleteBot(),\n    okText: I18n.t('Delete'),\n    cancelText: I18n.t('Cancel'),\n    icon: <IconCozWarningCircleFill className=\"text-24px coz-fg-hglt-red\" />,\n    onCancel: () => {\n      close();\n      onClose?.();\n    },\n    okButtonProps: {\n      type: 'danger',\n    },\n  });\n\n  return (\n    <>\n      <Menu.Item\n        type=\"danger\"\n        onClick={() => {\n          open();\n          onClick?.();\n        }}\n      >\n        <span className=\"coz-fg-hglt-red\">{I18n.t('Delete')}</span>\n      </Menu.Item>\n      {modal(\n        <>\n          {I18n.t('bot_list_delete_bot', {\n            platform: FLOW_BRAND_NAME,\n          })}\n        </>,\n      )}\n    </>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { IntelligenceType } from '@coze-arch/idl/intelligence_api';\nimport { I18n } from '@coze-arch/i18n';\nimport { Tag } from '@coze-arch/coze-design';\nexport interface IntelligenceTagProps {\n  intelligenceType: IntelligenceType | undefined;\n}\n\nexport const IntelligenceTag: React.FC<IntelligenceTagProps> = ({\n  intelligenceType,\n}) => {\n  if (intelligenceType === IntelligenceType.Project) {\n    return (\n      <Tag color=\"brand\" size=\"small\" className=\"w-fit\">\n        {I18n.t('develop_list_card_tag_project')}\n      </Tag>\n    );\n  }\n  if (intelligenceType === IntelligenceType.Bot) {\n    return (\n      <Tag color=\"primary\" size=\"small\" className=\"w-fit\">\n        {I18n.t('develop_list_card_tag_agent')}\n      </Tag>\n    );\n  }\n  if (intelligenceType === IntelligenceType.DouyinAvatarBot) {\n    return (\n      <Tag color=\"red\" size=\"small\" className=\"w-fit\">\n        {/* TODO: i18n copywriting */}\n        抖音分身\n      </Tag>\n    );\n  }\n  return null;\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type FC } from 'react';\n\nimport { Typography } from '@coze-arch/coze-design';\n\nexport interface DescriptionProps {\n  description?: string;\n}\n\nconst Description: FC<DescriptionProps> = ({ description }) => (\n  <Typography.Text\n    className=\"coz-fg-secondary text-[14px] leading-[20px] break-words\"\n    ellipsis={{\n      showTooltip: {\n        opts: {\n          theme: 'dark',\n          content: (\n            <Typography.Text\n              className=\"break-words break-all coz-fg-white\"\n              onClick={e => e.stopPropagation()}\n              ellipsis={{ showTooltip: false, rows: 16 }}\n            >\n              {description}\n            </Typography.Text>\n          ),\n        },\n        type: 'tooltip',\n      },\n      rows: 2,\n    }}\n  >\n    {description}\n  </Typography.Text>\n);\n\nexport default Description;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useRequest } from 'ahooks';\nimport {\n  type IntelligenceBasicInfo,\n  IntelligenceStatus,\n  TaskAction,\n} from '@coze-arch/idl/intelligence_api';\nimport { I18n } from '@coze-arch/i18n';\nimport {\n  IconCozLoading,\n  IconCozWarningCircleFillPalette,\n} from '@coze-arch/coze-design/icons';\nimport { Button, Space } from '@coze-arch/coze-design';\nimport { intelligenceApi } from '@coze-arch/bot-api';\n\nexport interface CopyProcessMaskProps {\n  intelligenceBasicInfo: IntelligenceBasicInfo;\n  onRetry?: (status: IntelligenceStatus | undefined) => void;\n  onCancelCopyAfterFailed?: (status: IntelligenceStatus | undefined) => void;\n}\n\nexport const CopyProcessMask: React.FC<CopyProcessMaskProps> = ({\n  intelligenceBasicInfo,\n  onRetry,\n  onCancelCopyAfterFailed,\n}) => {\n  const { status } = intelligenceBasicInfo;\n\n  const { run } = useRequest(\n    async (action: TaskAction) => {\n      const response = await intelligenceApi.ProcessEntityTask({\n        entity_id: intelligenceBasicInfo.id,\n        action,\n      });\n      return response.data?.entity_task?.entity_status;\n    },\n    {\n      manual: true,\n      onSuccess: (res, [action]) => {\n        if (action === TaskAction.ProjectCopyCancel) {\n          onCancelCopyAfterFailed?.(res);\n        }\n        if (action === TaskAction.ProjectCopyRetry) {\n          onRetry?.(res);\n        }\n      },\n    },\n  );\n\n  if (\n    status !== IntelligenceStatus.CopyFailed &&\n    status !== IntelligenceStatus.Copying\n  ) {\n    return null;\n  }\n\n  return (\n    <div className=\"absolute w-full h-full flex items-center justify-center backdrop-blur-[6px] bg-[rgba(255,255,255,0.8)] left-0 top-0\">\n      <div className=\"coz-fg-secondary flex flex-col items-center gap-y-[12px]\">\n        {status === IntelligenceStatus.Copying ? (\n          <>\n            <IconCozLoading className=\"animate-spin\" />\n            <div>{I18n.t('project_ide_duplicate_loading')}</div>\n          </>\n        ) : null}\n        {status === IntelligenceStatus.CopyFailed ? (\n          <>\n            <IconCozWarningCircleFillPalette className=\"coz-fg-hglt-red\" />\n            <div>{I18n.t('develop_list_card_copy_fail')}</div>\n            <Space spacing={8}>\n              <Button\n                color=\"primary\"\n                onClick={() => {\n                  run(TaskAction.ProjectCopyCancel);\n                }}\n              >\n                {I18n.t('Cancel')}\n              </Button>\n              <Button\n                color=\"hgltplus\"\n                onClick={() => {\n                  run(TaskAction.ProjectCopyRetry);\n                }}\n              >\n                {I18n.t('project_ide_toast_duplicate_fail_retry')}\n              </Button>\n            </Space>\n          </>\n        ) : null}\n      </div>\n    </div>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint @coze-arch/max-line-per-function: [\"error\", {\"max\": 500}] */\n/* eslint-disable complexity */\nimport { useNavigate } from 'react-router-dom';\nimport { useMemo, useState, type ReactNode } from 'react';\n\nimport { cloneDeep } from 'lodash-es';\nimport classNames from 'classnames';\nimport { FavoriteIconBtn } from '@coze-community/components';\nimport { ProductEntityType } from '@coze-arch/idl/product_api';\nimport {\n  type IntelligenceBasicInfo,\n  type IntelligenceData,\n  IntelligenceStatus,\n  IntelligenceType,\n} from '@coze-arch/idl/intelligence_api';\nimport { I18n } from '@coze-arch/i18n';\nimport {\n  IconCozCheckMarkCircleFillPalette,\n  IconCozMore,\n  IconCozStarFill,\n  IconCozWarningCircleFill,\n} from '@coze-arch/coze-design/icons';\nimport { Avatar, IconButton, Menu, Tooltip } from '@coze-arch/coze-design';\nimport { formatDate, getFormatDateType } from '@coze-arch/bot-utils';\nimport { useSpaceStore } from '@coze-arch/bot-studio-store';\nimport { ConnectorDynamicStatus } from '@coze-arch/bot-api/developer_api';\n\nimport { Creator } from '@/components/creator';\n\nimport Name from './name';\nimport { type AgentCopySuccessCallback, MenuCopyBot } from './menu-actions';\nimport { IntelligenceTag } from './intelligence-tag';\nimport Description from './description';\nimport { CopyProcessMask } from './copy-process-mask';\n\nexport interface BotCardProps {\n  intelligenceInfo: IntelligenceData;\n  timePrefixType?: 'recentOpen' | 'publish' | 'edit';\n  /**\n   * Returning true interrupts the default jump behavior\n   */\n  onClick?: (() => true) | (() => void);\n  onDelete?: (param: {\n    name: string;\n    id: string;\n    type: IntelligenceType;\n  }) => void;\n  onCopyProject?: (basicInfo: IntelligenceBasicInfo) => void;\n  onCopyAgent?: AgentCopySuccessCallback;\n  onUpdateIntelligenceInfo: (info: IntelligenceData) => void;\n  onRetryCopy: (basicInfo: IntelligenceBasicInfo) => void;\n  onCancelCopyAfterFailed: (basicInfo: IntelligenceBasicInfo) => void;\n  extraMenu?: ReactNode;\n  headerExtra?: ReactNode;\n  statusExtra?: ReactNode;\n  actionsMenuVisible?: boolean;\n}\n\n// eslint-disable-next-line max-lines-per-function\nexport const BotCard: React.FC<BotCardProps> = ({\n  intelligenceInfo,\n  timePrefixType,\n  onClick,\n  onDelete,\n  onCopyProject,\n  onCopyAgent,\n  onUpdateIntelligenceInfo,\n  onCancelCopyAfterFailed,\n  onRetryCopy,\n  extraMenu,\n  actionsMenuVisible = true,\n  headerExtra,\n  statusExtra,\n}) => {\n  const navigate = useNavigate();\n\n  const {\n    basic_info,\n    type,\n    permission_info: { in_collaboration, can_delete } = {},\n    publish_info: { publish_time, connectors, has_published } = {},\n    other_info: { recently_open_time } = {},\n    owner_info,\n    favorite_info: { is_fav } = {},\n  } = intelligenceInfo;\n\n  const { id, name, icon_url, space_id, description, update_time, status } =\n    basic_info ?? {};\n\n  const hideOperation = useSpaceStore(store => store.space.hide_operation);\n\n  const renderPublishStatusIcon = () => {\n    if (!has_published) {\n      return null;\n    }\n    if (!connectors?.length) {\n      return (\n        <IconCozCheckMarkCircleFillPalette className=\"text-xxl coz-fg-hglt-green flex-shrink-0\" />\n      );\n    }\n    const isSomeConnectorsFailed = connectors.some(\n      item => item?.connector_status !== ConnectorDynamicStatus.Normal,\n    );\n    if (isSomeConnectorsFailed) {\n      return (\n        <IconCozWarningCircleFill className=\"text-xxl coz-fg-hglt-yellow flex-shrink-0\" />\n      );\n    }\n    return (\n      <IconCozCheckMarkCircleFillPalette className=\"text-xxl coz-fg-hglt-green flex-shrink-0\" />\n    );\n  };\n\n  if (!id || !space_id) {\n    // The id and space id are necessary for the bot card. Here are the constraints on the ts type\n    throw Error('No botID or no spaceID which are necessary');\n  }\n\n  const isBanned = status === IntelligenceStatus.Banned;\n  const isAgent = type === IntelligenceType.Bot;\n  const isProject = type === IntelligenceType.Project;\n\n  const timePrefix = useMemo(() => {\n    switch (timePrefixType) {\n      case 'recentOpen':\n        return I18n.t('develop_list_rank_tag_opened');\n      case 'publish':\n        return I18n.t('bot_list_rank_tag_published');\n      case 'edit':\n        return in_collaboration\n          ? I18n.t('devops_publish_multibranch_RecentSubmit')\n          : I18n.t('bot_list_rank_tag_edited');\n      default:\n    }\n  }, [timePrefixType, in_collaboration]);\n\n  const time = useMemo(() => {\n    let timestamp: string | undefined;\n\n    switch (timePrefixType) {\n      case 'recentOpen':\n        timestamp = recently_open_time;\n        break;\n      case 'publish':\n        timestamp = publish_time;\n        break;\n      case 'edit':\n        timestamp = update_time;\n        break;\n      default:\n    }\n\n    return formatDate(Number(timestamp), getFormatDateType(Number(timestamp)));\n  }, [timePrefixType, publish_time, update_time, recently_open_time]);\n\n  // Whether to display the card layering operation button\n  const [showActions, setShowActions] = useState(false);\n  // Whether to display the menu menu, there are other components actively calling here, which need to be controlled\n  const [showMenu, setShowMenu] = useState(false);\n\n  return (\n    <>\n      <div\n        className={classNames([\n          'flex-grow h-[158px] min-w-[280px]',\n          'rounded-[6px] border-solid\tborder-[1px] ',\n          'relative',\n          'overflow-hidden transition duration-150 ease-out hover:shadow-[0_6px_8px_0_rgba(28,31,35,6%)]',\n          'coz-stroke-primary coz-mg-card',\n        ])}\n      >\n        <div\n          className=\"h-full w-full cursor-pointer flex flex-col gap-[12px] px-[16px] py-[16px]\"\n          onClick={() => {\n            if (onClick?.()) {\n              return;\n            }\n            if (isBanned) {\n              return;\n            }\n            if (isAgent) {\n              navigate(`/space/${space_id}/bot/${id}`);\n              return;\n            }\n            if (isProject) {\n              navigate(`/space/${space_id}/project-ide/${id}`);\n              return;\n            }\n          }}\n          onMouseEnter={() => {\n            setShowActions(true);\n          }}\n          onMouseLeave={() => {\n            setShowActions(false);\n          }}\n          data-testid=\"bot-list-page.bot-card\"\n        >\n          {/* Display migration failure status icon */}\n          {statusExtra}\n\n          {/* Bot basic information */}\n          <div className=\"flex justify-between\">\n            <div className=\"flex flex-col gap-[4px] w-[calc(100%-76px)]\">\n              <div className=\"flex items-center gap-[4px]\">\n                <Name name={name} />\n                {isBanned ? (\n                  // If it fails, display the failure icon\n                  <IconCozWarningCircleFill className=\"text-xxl coz-fg-hglt-red flex-shrink-0\" />\n                ) : (\n                  <>\n                    {/* Publish status icon */}\n                    {renderPublishStatusIcon()}\n                    {headerExtra}\n                  </>\n                )}\n              </div>\n\n              <Description description={description} />\n            </div>\n            <Avatar\n              className=\"w-[64px] h-[64px] rounded-[10px] flex-shrink-0 ml-[12px]\"\n              shape=\"square\"\n              src={icon_url}\n            />\n          </div>\n\n          {/* Projects/Agents */}\n          <IntelligenceTag intelligenceType={type} />\n\n          {/* Bot author information */}\n          {!!owner_info && (\n            <Creator\n              avatar={owner_info.avatar_url}\n              name={owner_info.nickname}\n              extra={`${timePrefix} ${time}`}\n            />\n          )}\n\n          {/* Actions Floating layer action When the floating layer appears, there is a white mask below */}\n          {!hideOperation ? (\n            <>\n              {showActions && actionsMenuVisible ? (\n                <div\n                  className=\"absolute bottom-[16px] right-[16px] w-[100px] h-[16px] \"\n                  style={{\n                    background:\n                      'linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 21.38%)',\n                  }}\n                ></div>\n              ) : null}\n              <div\n                className=\"absolute bottom-[16px] right-[16px] flex gap-[4px]\"\n                onClick={e => {\n                  // Prevent click events from bubbling to the outermost layer of the card\n                  e.stopPropagation();\n                }}\n              >\n                {showActions && actionsMenuVisible ? (\n                  <>\n                    {!isBanned ? (\n                      // Favorite bot\n                      <FavoriteIconBtn\n                        useButton\n                        isVisible\n                        entityId={id}\n                        entityType={\n                          type === IntelligenceType.Bot\n                            ? ProductEntityType.Bot\n                            : ProductEntityType.Project\n                        }\n                        isFavorite={is_fav}\n                        onFavoriteStateChange={isFav => {\n                          const clonedInfo = cloneDeep(intelligenceInfo);\n                          clonedInfo.favorite_info = {\n                            ...(clonedInfo.favorite_info ?? {}),\n                            is_fav: isFav,\n                          };\n                          onUpdateIntelligenceInfo(clonedInfo);\n                        }}\n                      />\n                    ) : null}\n                    {/* dropdown menu */}\n                    <Menu\n                      keepDOM\n                      className=\"w-fit mt-4px mb-4px\"\n                      position=\"bottomRight\"\n                      trigger=\"custom\"\n                      visible={showMenu}\n                      render={\n                        <Menu.SubMenu mode=\"menu\">\n                          {/* Copy bot */}\n                          {isAgent ? (\n                            <MenuCopyBot\n                              id={id}\n                              spaceID={space_id}\n                              disabled={isBanned}\n                              onCopySuccess={onCopyAgent}\n                              onClose={() => setShowActions(false)}\n                            />\n                          ) : null}\n                          {isProject ? (\n                            <Tooltip content={I18n.t('coze_copy_to_tips_1')}>\n                              <Menu.Item\n                                onClick={() => {\n                                  if (!basic_info) {\n                                    return;\n                                  }\n                                  onCopyProject?.(basic_info);\n                                }}\n                                data-testid=\"bot-card.copy\"\n                              >\n                                {I18n.t('project_ide_create_duplicate')}\n                              </Menu.Item>\n                            </Tooltip>\n                          ) : null}\n                          {extraMenu}\n                          {/* Delete bot */}\n                          <Tooltip\n                            position=\"left\"\n                            trigger={can_delete ? 'custom' : 'hover'}\n                            content={I18n.t(\n                              'project_delete_permission_tooltips',\n                            )}\n                          >\n                            <Menu.Item\n                              type=\"danger\"\n                              disabled={!can_delete}\n                              onClick={() => {\n                                if (!name || !type) {\n                                  return;\n                                }\n                                onDelete?.({ name, id, type });\n                              }}\n                            >\n                              <span>{I18n.t('Delete')}</span>\n                            </Menu.Item>\n                          </Tooltip>\n                        </Menu.SubMenu>\n                      }\n                    >\n                      <IconButton\n                        className=\"rotate-90\"\n                        data-testid=\"bot-card.icon-more-button\"\n                        color=\"primary\"\n                        size=\"default\"\n                        icon={<IconCozMore />}\n                        onClick={() => setShowMenu(true)}\n                      />\n                    </Menu>\n                  </>\n                ) : is_fav && !isBanned ? (\n                  // If the bot has already been collected, display the icon when not hovering.\n                  <IconButton\n                    className=\"!pt-[20px]\"\n                    color=\"secondary\"\n                    icon={<IconCozStarFill className=\"coz-fg-color-yellow\" />}\n                  ></IconButton>\n                ) : null}\n              </div>\n            </>\n          ) : null}\n        </div>\n        {basic_info ? (\n          <CopyProcessMask\n            intelligenceBasicInfo={basic_info}\n            onRetry={changedStatus => {\n              onRetryCopy({\n                ...basic_info,\n                status: changedStatus,\n              });\n            }}\n            onCancelCopyAfterFailed={changedStatus => {\n              onCancelCopyAfterFailed({\n                ...basic_info,\n                status: changedStatus,\n              });\n            }}\n          />\n        ) : null}\n      </div>\n    </>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable max-lines-per-function */\n/* eslint @coze-arch/max-line-per-function: [\"error\", {\"max\": 500}] */\n/* eslint-disable complexity */\nimport { type FC, useEffect } from 'react';\n\nimport classNames from 'classnames';\nimport {\n  highlightFilterStyle,\n  WorkspaceEmpty,\n  DevelopCustomPublishStatus,\n  isPublishStatus,\n  isRecentOpen,\n  isSearchScopeEnum,\n  getPublishRequestParam,\n  getTypeRequestParams,\n  isEqualDefaultFilterParams,\n  isF<PERSON>erHighlight,\n  CREATOR_FILTER_OPTIONS,\n  FILTER_PARAMS_DEFAULT,\n  STATUS_FILTER_OPTIONS,\n  TYPE_FILTER_OPTIONS,\n  BotCard,\n  Content,\n  Header,\n  HeaderActions,\n  HeaderTitle,\n  Layout,\n  SubHeader,\n  SubHeaderFilters,\n  SubHeaderSearch,\n  useIntelligenceList,\n  useIntelligenceActions,\n  useCachedQueryParams,\n  useGlobalEventListeners,\n  type DevelopProps,\n  useProjectCopyPolling,\n  useCardActions,\n} from '@coze-studio/workspace-base/develop';\nimport { useSpaceStore } from '@coze-foundation/space-store-adapter';\nimport {\n  IntelligenceType,\n  search,\n  SearchScope,\n} from '@coze-arch/idl/intelligence_api';\nimport { I18n, type I18nKeysNoOptionsType } from '@coze-arch/i18n';\nimport { IconCozLoading, IconCozPlus } from '@coze-arch/coze-design/icons';\nimport {\n  Button,\n  IconButton,\n  Search,\n  Select,\n  Spin,\n} from '@coze-arch/coze-design';\nimport { EVENT_NAMES, sendTeaEvent } from '@coze-arch/bot-tea';\nimport { SpaceType } from '@coze-arch/bot-api/developer_api';\n\nexport const Develop: FC<DevelopProps> = ({ spaceId }) => {\n  const isPersonal = useSpaceStore(\n    state => state.space.space_type === SpaceType.Personal,\n  );\n\n  // Keyword Search & Filtering\n  const [filterParams, setFilterParams, debouncedSetSearchValue] =\n    useCachedQueryParams();\n\n  const {\n    isIntelligenceTypeFilterHighlight,\n    isOwnerFilterHighlight,\n    isPublishAndOpenFilterHighlight,\n  } = isFilterHighlight(filterParams);\n\n  const {\n    listResp: { loading, data, loadingMore, mutate, noMore, reload },\n    containerRef,\n  } = useIntelligenceList({\n    params: {\n      spaceId,\n      searchValue: filterParams.searchValue,\n      types: getTypeRequestParams({\n        type: filterParams.searchType,\n      }),\n      hasPublished: getPublishRequestParam(filterParams.isPublish),\n      recentlyOpen: filterParams.recentlyOpen,\n      searchScope: filterParams.searchScope,\n      // Fixed value, from historical code\n      orderBy: filterParams.isPublish\n        ? search.OrderBy.PublishTime\n        : search.OrderBy.UpdateTime,\n    },\n  });\n\n  useGlobalEventListeners({ reload, spaceId });\n\n  useEffect(() => {\n    setFilterParams(prev => ({\n      ...prev,\n      searchValue: '',\n    }));\n  }, [spaceId]);\n\n  /**\n   * report tea event\n   */\n  useEffect(() => {\n    sendTeaEvent(EVENT_NAMES.view_bot, { tab: 'my_bots' });\n  }, []);\n\n  useProjectCopyPolling({\n    listData: data?.list,\n    spaceId,\n    mutate,\n  });\n\n  const { contextHolder: cardActionsContextHolder, actions: cardActions } =\n    useCardActions({\n      isPersonalSpace: isPersonal,\n      mutate,\n    });\n\n  /**\n   * Create project\n   */\n  const { contextHolder, actions } = useIntelligenceActions({\n    spaceId,\n    mutateList: mutate,\n    reloadList: reload,\n  });\n\n  return (\n    <>\n      {contextHolder}\n      {cardActionsContextHolder}\n      <Layout>\n        <Header>\n          <HeaderTitle>\n            <span>{I18n.t('workspace_develop')}</span>\n          </HeaderTitle>\n          <HeaderActions>\n            <Button icon={<IconCozPlus />} onClick={actions.createIntelligence}>\n              {I18n.t('workspace_create')}\n            </Button>\n          </HeaderActions>\n        </Header>\n        <SubHeader>\n          <SubHeaderFilters>\n            <Select\n              className=\"min-w-[128px]\"\n              style={\n                isIntelligenceTypeFilterHighlight ? highlightFilterStyle : {}\n              }\n              value={filterParams.searchType}\n              onChange={val => {\n                setFilterParams(prev => ({\n                  ...prev,\n                  searchType:\n                    val as (typeof TYPE_FILTER_OPTIONS)[number]['value'],\n                }));\n\n                // Tea event tracking\n                sendTeaEvent(EVENT_NAMES.workspace_action_front, {\n                  space_id: spaceId,\n                  space_type: isPersonal ? 'personal' : 'teamspace',\n                  tab_name: 'develop',\n                  action: 'filter',\n                  filter_type: 'types',\n                  filter_name: I18n.t(\n                    TYPE_FILTER_OPTIONS.find(opt => opt.value === val)\n                      ?.labelI18NKey as I18nKeysNoOptionsType,\n                  ),\n                });\n              }}\n            >\n              {TYPE_FILTER_OPTIONS.map(opt => (\n                <Select.Option key={opt.value} value={opt.value}>\n                  {I18n.t(opt.labelI18NKey)}\n                </Select.Option>\n              ))}\n            </Select>\n            {!isPersonal ? (\n              /**\n               * Search Scope\n               * Everybody.\n               * Created by me\n               */\n              <Select\n                className=\"min-w-[128px]\"\n                style={isOwnerFilterHighlight ? highlightFilterStyle : {}}\n                value={filterParams.searchScope}\n                onChange={val => {\n                  if (!isSearchScopeEnum(val)) {\n                    return;\n                  }\n                  setFilterParams(p => {\n                    if (val === SearchScope.CreateByMe && p.recentlyOpen) {\n                      return {\n                        ...p,\n                        recentlyOpen: false,\n                        isPublish: DevelopCustomPublishStatus.All,\n                        searchScope: val,\n                      };\n                    }\n                    return {\n                      ...p,\n                      searchScope: val,\n                    };\n                  });\n                  // Tea event tracking\n                  sendTeaEvent(EVENT_NAMES.workspace_action_front, {\n                    space_id: spaceId,\n                    space_type: isPersonal ? 'personal' : 'teamspace',\n                    tab_name: 'develop',\n                    action: 'filter',\n                    filter_type: 'creators',\n                    filter_name: I18n.t(\n                      CREATOR_FILTER_OPTIONS.find(opt => opt.value === val)\n                        ?.labelI18NKey as I18nKeysNoOptionsType,\n                    ),\n                  });\n                }}\n              >\n                {CREATOR_FILTER_OPTIONS.map(opt => (\n                  <Select.Option key={opt.value} value={opt.value}>\n                    {I18n.t(opt.labelI18NKey)}\n                  </Select.Option>\n                ))}\n              </Select>\n            ) : null}\n            {/*\n              all\n              Published\n              Recently opened\n            */}\n            <Select\n              className=\"min-w-[128px]\"\n              style={\n                isPublishAndOpenFilterHighlight ? highlightFilterStyle : {}\n              }\n              value={\n                filterParams.recentlyOpen\n                  ? 'recentOpened'\n                  : filterParams.isPublish\n              }\n              onChange={val => {\n                setFilterParams(p => ({\n                  ...p,\n                  searchScope: SearchScope.All,\n                  recentlyOpen: isRecentOpen(val),\n                  isPublish: isPublishStatus(val)\n                    ? val\n                    : DevelopCustomPublishStatus.All,\n                }));\n                // Tea event tracking\n                sendTeaEvent(EVENT_NAMES.workspace_action_front, {\n                  space_id: spaceId,\n                  space_type: isPersonal ? 'personal' : 'teamspace',\n                  tab_name: 'develop',\n                  action: 'filter',\n                  filter_type: 'status',\n                  filter_name: I18n.t(\n                    STATUS_FILTER_OPTIONS.find(opt => opt.value === val)\n                      ?.labelI18NKey as I18nKeysNoOptionsType,\n                  ),\n                });\n              }}\n            >\n              {STATUS_FILTER_OPTIONS.map(opt => (\n                <Select.Option key={opt.value} value={opt.value}>\n                  {I18n.t(opt.labelI18NKey)}\n                </Select.Option>\n              ))}\n            </Select>\n          </SubHeaderFilters>\n          <SubHeaderSearch>\n            <Search\n              disabled={filterParams.recentlyOpen}\n              showClear={true}\n              className=\"w-[200px]\"\n              style={filterParams.searchValue ? highlightFilterStyle : {}}\n              placeholder={I18n.t('workspace_develop_search_project')}\n              value={filterParams.searchValue}\n              onChange={val => {\n                debouncedSetSearchValue(val);\n              }}\n            />\n          </SubHeaderSearch>\n        </SubHeader>\n        <Content ref={containerRef}>\n          <Spin spinning={loading} wrapperClassName=\"w-full !h-[80vh]\">\n            {/* When data is available */}\n            {data?.list.length ? (\n              <div\n                className={classNames(\n                  'grid grid-cols-3 auto-rows-min gap-[20px]',\n                  '[@media(min-width:1600px)]:grid-cols-4',\n                )}\n              >\n                {data.list.map((project, index) => (\n                  <BotCard\n                    key={`${project.basic_info?.id}-${index}`}\n                    intelligenceInfo={project}\n                    onRetryCopy={cardActions.onRetryCopy}\n                    onCancelCopyAfterFailed={\n                      cardActions.onCancelCopyAfterFailed\n                    }\n                    onClick={() => {\n                      cardActions.onClick(project);\n                    }}\n                    onUpdateIntelligenceInfo={cardActions.onUpdate}\n                    onDelete={({ name, id, type }) => {\n                      if (type === IntelligenceType.Bot) {\n                        actions.deleteIntelligence({\n                          name,\n                          spaceId,\n                          agentId: id,\n                        });\n                        return;\n                      }\n                      if (type === IntelligenceType.Project) {\n                        actions.deleteIntelligence({ name, projectId: id });\n                        return;\n                      }\n                    }}\n                    onCopyAgent={cardActions.onCopyAgent}\n                    onCopyProject={params => {\n                      cardActions.onCopyProject({\n                        initialValue: {\n                          project_id: params.id ?? '',\n                          to_space_id: spaceId,\n                          name: params.name ?? '',\n                          description: params.description,\n                          icon_uri: [\n                            {\n                              uid: params.icon_uri,\n                              url: params.icon_url ?? '',\n                            },\n                          ],\n                        },\n                      });\n                    }}\n                    timePrefixType={\n                      filterParams.recentlyOpen\n                        ? 'recentOpen'\n                        : filterParams.isPublish\n                        ? 'publish'\n                        : 'edit'\n                    }\n                  />\n                ))}\n              </div>\n            ) : null}\n\n            {!data?.list?.length && !loading ? (\n              <WorkspaceEmpty\n                onClear={() => {\n                  setFilterParams(FILTER_PARAMS_DEFAULT);\n                }}\n                hasFilter={\n                  !isEqualDefaultFilterParams({\n                    filterParams,\n                  })\n                }\n              />\n            ) : null}\n\n            {/* Show loading at the bottom. */}\n            {data?.list.length && loadingMore ? (\n              <div className=\"flex items-center justify-center w-full h-[38px] my-[20px] coz-fg-secondary text-[12px]\">\n                <IconButton\n                  icon={<IconCozLoading />}\n                  loading\n                  color=\"secondary\"\n                />\n                <div>{I18n.t('Loading')}...</div>\n              </div>\n            ) : null}\n            {/* Show a placeholder when there is no more data */}\n            {noMore && data?.list.length ? (\n              <div className=\"h-[38px] my-[20px]\"></div>\n            ) : null}\n          </Spin>\n        </Content>\n      </Layout>\n    </>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useParams } from 'react-router-dom';\n\nimport { Develop } from '@coze-studio/workspace-adapter/develop';\n\nconst Page = () => {\n  const { space_id } = useParams();\n  return space_id ? <Develop spaceId={space_id} /> : null;\n};\n\nexport default Page;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { isEmpty } from 'lodash-es';\nimport { useRequest } from 'ahooks';\nimport { withSlardarIdButton } from '@coze-studio/bot-utils';\nimport { logger } from '@coze-arch/logger';\nimport { I18n } from '@coze-arch/i18n';\nimport { CustomError } from '@coze-arch/bot-error';\nimport { type AuthLoginInfo } from '@coze-arch/bot-api/developer_api';\nimport { DeveloperApi } from '@coze-arch/bot-api';\nimport { connector2Redirect } from '@coze-foundation/account-adapter';\nimport { Toast } from '@coze-arch/coze-design';\n\nexport const useRevokeAuth = ({\n  id,\n  onRevokeSuccess,\n  onRevokeFinally,\n}: {\n  id: string;\n  onRevokeSuccess?: (id: string) => void;\n  onRevokeFinally?: () => void;\n}) => {\n  const { loading, runAsync } = useRequest(\n    async () =>\n      await DeveloperApi.CancelUserAuth({\n        connector_id: id,\n      }),\n    {\n      manual: true,\n      onSuccess: () => {\n        onRevokeSuccess?.(id);\n      },\n      onFinally: () => {\n        onRevokeFinally?.();\n      },\n    },\n  );\n\n  return {\n    revokeLoading: loading,\n    runRevoke: runAsync,\n  };\n};\n\nexport const executeAuthRedirect = async ({\n  id,\n  authInfo,\n  origin,\n}: {\n  id: string;\n  authInfo: AuthLoginInfo;\n  origin?: 'setting' | 'publish';\n}) => {\n  const resp = await DeveloperApi.GetConnectorAuthState({\n    connector_id: id,\n  });\n  const state = resp?.data?.state ?? {};\n  connector2Redirect(\n    {\n      navigatePath: location.pathname,\n      type: 'oauth',\n      extra: {\n        origin,\n        ...state,\n      },\n    },\n    id,\n    authInfo,\n  );\n};\n\nexport const checkAuthInfoValid = (authInfo?: AuthLoginInfo) =>\n  !isEmpty(authInfo) && !!authInfo?.authorize_url;\n\nexport const logAndToastAuthInfoError = () => {\n  logger.error({\n    message: 'connection_missing_oauth_info',\n    error: new CustomError(\n      'normal_error',\n      'Connection missing oauth information',\n    ),\n  });\n  Toast.error({\n    content: withSlardarIdButton(I18n.t('error')),\n  });\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { I18n } from '@coze-arch/i18n';\nimport { UIModal } from '@coze-arch/bot-semi';\nimport { type DiffDisplayNode } from '@coze-arch/bot-api/dp_manage_api';\nimport {\n  PublishResultStatus,\n  type ConnectorBindResult,\n  type PublishConnectorInfo,\n} from '@coze-arch/bot-api/developer_api';\n\nimport { type ConnectResultInfo } from '../type';\n\nexport { skillKeyToApiStatusKeyTransformer } from '@coze-arch/bot-utils';\n\nexport {\n  checkAuthInfoValid,\n  executeAuthRedirect,\n  logAndToastAuthInfoError,\n  useRevokeAuth,\n} from './auth';\n\n/**\n * Get the path to bot details pages, not explore pages\n * @param {String} spaceID - space ID\n * @param {String} botID - bot ID\n */\nexport const getBotDetailPagePath = (spaceID: string, botID: string) =>\n  `/space/${spaceID}/bot/${botID}`;\n\n/**\n * Flatten bot diff data, flat subtree structure within array, and add hierarchy to each node\n * @param {Array < DiffDisplayNode & {level?: number} >} dataSource - space ID\n * @param {String} botID - bot ID\n */\nexport const flatDataSource = (\n  dataSource: Array<DiffDisplayNode & { level?: number }>,\n  level = 0,\n) => {\n  const res: DiffDisplayNode[] = [];\n  dataSource?.forEach(item => {\n    item.level = level;\n    res.push(item);\n    if (item.sub_nodes) {\n      res.push(...flatDataSource(item.sub_nodes, level + 1));\n    }\n  });\n  return res;\n};\n\nexport const setPCBodyWithDebugPanel = () => {\n  const bodyStyle = document.body.style;\n  const htmlStyle = document.getElementsByTagName('html')[0].style;\n  bodyStyle.minHeight = '600px';\n  htmlStyle.minHeight = '600px';\n  bodyStyle.minWidth = '1680px';\n  htmlStyle.minWidth = '1680px';\n};\n\n// The old service number id to be offline.\nexport const OLD_WX_FWH_ID = '10000114';\n\n// New WeChat service ID\nexport const NEW_WX_FWH_ID = '10000120';\n\n// Store channel id\nexport const STORE_CONNECTOR_ID = '10000122';\n\nexport const getPublishResult: (\n  publishResult: Record<string, ConnectorBindResult>,\n  connectInfoList: PublishConnectorInfo[],\n) => ConnectResultInfo[] = (publishResult, connectInfoList) => {\n  if (!connectInfoList?.length) {\n    return [];\n  }\n  return connectInfoList.map(item => {\n    const result = publishResult?.[item.id] ?? {};\n    return {\n      ...item,\n      publish_status:\n        result.publish_result_status ?? PublishResultStatus.Failed,\n      fail_text: result.msg ?? '',\n      share_link: result.connector?.share_link ?? '',\n      bind_info: result.connector?.bind_info ?? item.bind_info,\n    };\n  });\n};\n\n// New and old WeChat official account migration special judgment logic: When binding a new WeChat channel, it is prompted to unbind the bound old channel first to prevent repeated binding with the bot\nexport const isWeChatMigration = (\n  record: PublishConnectorInfo,\n  dataSource: PublishConnectorInfo[],\n): boolean => {\n  const hasBindOldWeChatId = dataSource.find(\n    i => i.id === OLD_WX_FWH_ID,\n  )?.bind_id;\n  if (hasBindOldWeChatId && record.id === NEW_WX_FWH_ID) {\n    UIModal.warning({\n      title: I18n.t('publish_wechat_old_disconnect_title'),\n      content: I18n.t('publish_wechat_old_disconnect'),\n      okText: I18n.t('got_it'),\n      hasCancel: false,\n    });\n    return true;\n  } else {\n    return false;\n  }\n};\nexport const safeJSONParse = (data?: string) => {\n  try {\n    return JSON.parse(data);\n  } catch (e) {\n    return '';\n  }\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type explore } from '@coze-studio/api-schema';\nimport { type UserInfo as ProductUserInfo } from '@coze-arch/bot-api/product_api';\ntype UserInfo = explore.product_common.UserInfo;\n\nexport interface CardInfoProps {\n  title?: string;\n  imgSrc?: string;\n  description?: string;\n  userInfo?: UserInfo | ProductUserInfo;\n}\n\n/** for open coze */\nexport enum PluginAuthMode {\n  /** No authorization required */\n  NoAuth = 0,\n  /** Authorization is required, but not configured */\n  Required = 1,\n  /** Authorization is required and has been configured */\n  Configured = 2,\n  /** Authorization is required, but the configuration can be empty */\n  Supported = 3,\n}\n\nexport interface AuthMode {\n  /** for open coze */\n  auth_mode?: PluginAuthMode;\n}\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React from 'react';\n\nimport classNames from 'classnames';\nimport { I18n } from '@coze-arch/i18n';\nimport { Tooltip } from '@coze-arch/coze-design';\nimport { IconOfficialLabel } from '@coze-arch/bot-icons';\n\nimport styles from './index.module.less';\n\n/**\n * small 16px\n * default 20px\n * large 32px\n */\nexport type OfficialLabelSize = 'small' | 'default' | 'large';\n\nexport interface OfficialLabelProps {\n  size?: OfficialLabelSize;\n  visible: boolean;\n  children?: React.ReactNode;\n  className?: string;\n}\n\nexport const OfficialLabelSizeMap = {\n  small: styles.small,\n  default: styles.default,\n  large: styles.large,\n};\n\nexport const OfficialLabel: React.FC<OfficialLabelProps> = ({\n  size = 'default',\n  children,\n  visible,\n  className,\n}) => (\n  <div className=\"relative w-fit h-fit\">\n    <Tooltip\n      spacing={12}\n      trigger={visible ? 'hover' : 'custom'}\n      content={I18n.t('mkpl_plugin_tooltip_official')}\n    >\n      {visible ? (\n        <IconOfficialLabel\n          className={classNames(\n            styles['official-label'],\n            OfficialLabelSizeMap[size],\n            className,\n          )}\n        />\n      ) : null}\n    </Tooltip>\n    {children}\n  </div>\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  useState,\n  useRef,\n  useEffect,\n  type Dispatch,\n  type SetStateAction,\n} from 'react';\n\nimport {\n  useInfiniteScroll,\n  useUpdateEffect,\n  useMemoizedFn,\n  useDebounceFn,\n} from 'ahooks';\n\nimport { type ScrollProps, type InfiniteListDataProps } from '../type';\n\n/* Rolling Hooks */\n\nfunction useForwardFunc<T>(\n  dataInfo: InfiniteListDataProps<T>,\n  mutate: Dispatch<SetStateAction<InfiniteListDataProps<T>>>,\n) {\n  // Insert data manually, without going through the interface\n  const insertData = (item, index) => {\n    dataInfo.list.splice(index, 0, item);\n    mutate({\n      ...dataInfo,\n      list: [...(dataInfo?.list || [])],\n    });\n  };\n\n  // Delete data manually, without going through the interface\n  const removeData = index => {\n    dataInfo.list.splice(index, 1);\n    mutate({\n      ...dataInfo,\n      list: [...(dataInfo?.list || [])],\n    });\n  };\n\n  const getDataList = () => dataInfo?.list;\n\n  return { insertData, removeData, getDataList };\n}\n\n// eslint-disable-next-line max-lines-per-function, @coze-arch/max-line-per-function -- the number of lines of code is not very good optimization\nfunction useScroll<T>(props: ScrollProps<T>) {\n  const {\n    targetRef,\n    loadData,\n    threshold,\n    reloadDeps,\n    isNeedBtnLoadMore,\n    resetDataIfReload = true,\n  } = props;\n  const [isLoadingError, setIsLoadingError] = useState<boolean>(false);\n  const refFetchNo = useRef<number>(0);\n  const refResolve = useRef<(value) => void>();\n  const {\n    loading,\n    data: dataInfo,\n    loadingMore,\n    loadMore,\n    noMore,\n    cancel,\n    mutate,\n    reload,\n  } = useInfiniteScroll<InfiniteListDataProps<T>>(\n    async current => {\n      // The logic here is so complex that it solves the bug in Scroll.\n      // The cancel in useInfiniteScroll simply cancels a request, but the data is reset based on the current.\n      const fetchNo = refFetchNo.current;\n      if (refResolve.current) {\n        // Guaranteed sequential execution, if there is a current method, cancel the last request to prevent data overwriting problems due to network reasons\n        // At the same time, A1, A2, and three requests were issued, but A1 arrived first, and then B1 was requested, but A1 was too slow, causing A1 to overwrite B1's request.\n        refResolve.current({\n          ...(current || {}),\n          list: [],\n        });\n      }\n\n      const result = await new Promise((resolve, reject) => {\n        refResolve.current = resolve;\n        loadData(current)\n          .then(value => resolve(value))\n          .catch(err => reject(err));\n      });\n\n      // @ts-expect-error -- linter-disable-autofix\n      refResolve.current = null;\n\n      // When switching tabs, if you are requesting at this time, prevent the residual interface display of the data\n      if (refFetchNo.current !== fetchNo) {\n        if (current) {\n          current.list = [];\n        }\n        return {\n          list: [],\n          nextPage: 1,\n        };\n      }\n      return result as InfiniteListDataProps<T>;\n    },\n    {\n      target: isLoadingError || isNeedBtnLoadMore ? null : targetRef, //When it fails, scrolling loading is prohibited by removing the event binding of the target.\n      threshold,\n      onBefore: () => {\n        //setIsLoadingError(false);\n      },\n      isNoMore: data => data?.hasMore !== undefined && !data?.hasMore,\n      onSuccess: () => {\n        if (isLoadingError) {\n          setIsLoadingError(false);\n        }\n      },\n      onError: e => {\n        // If an error occurs when requesting the first page of data and the current list is not empty, reset the data\n        // This case only occurs when resetDataIfReload is set to false\n        // @ts-expect-error -- linter-disable-autofix\n        if (dataInfo.nextPage === 1 && (dataInfo?.list?.length ?? 0) > 0) {\n          // @ts-expect-error -- linter-disable-autofix\n          mutate({\n            ...dataInfo,\n            list: [],\n          });\n        }\n        setIsLoadingError(true);\n      },\n    },\n  );\n\n  const { insertData, removeData, getDataList } = useForwardFunc(\n    // @ts-expect-error -- linter-disable-autofix\n    dataInfo,\n    mutate,\n  );\n\n  useEffect(() => {\n    if (isNeedBtnLoadMore && !(loading || loadingMore)) {\n      reload();\n    }\n  }, []);\n\n  const reloadData = useMemoizedFn(() => {\n    mutate({\n      list: resetDataIfReload ? [] : dataInfo?.list ?? [],\n      hasMore: undefined,\n      nextPage: 1,\n    });\n    cancel();\n    setIsLoadingError(false);\n    reload();\n  });\n\n  useUpdateEffect(() => {\n    refFetchNo.current++;\n    reloadData();\n  }, [...(reloadDeps || [])]);\n  const isLoading = loading || loadingMore || props.isLoading;\n  const { run: loadMoreDebounce } = useDebounceFn(\n    () => {\n      if (isLoading) {\n        return;\n      }\n      if (!isNeedBtnLoadMore) {\n        loadMore();\n      }\n    },\n    { wait: 500 },\n  );\n  useEffect(() => {\n    const resize = () => {\n      loadMoreDebounce();\n    };\n    window.addEventListener('resize', resize);\n    return () => {\n      window.removeEventListener('resize', resize);\n    };\n  }, []);\n  const { list } = dataInfo || {};\n  return {\n    dataList: list,\n    isLoading,\n    loadMore: () => {\n      if (!isLoading) {\n        //If there is already data loading, you need to prohibit repeated loading.\n        loadMore();\n      }\n    },\n    reload: reloadData,\n    noMore,\n    cancel,\n    isLoadingError,\n    mutate,\n    insertData,\n    removeData,\n    getDataList,\n  };\n}\n\nexport default useScroll;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React from 'react';\n\nimport classNames from 'classnames';\nimport { I18n } from '@coze-arch/i18n';\nimport { Spin, UIButton } from '@coze-arch/bot-semi';\nimport { useIsResponsive } from '@coze-arch/bot-hooks';\n\nimport { type FooterProps } from '../../type';\n\nimport s from './index.module.less';\n\n/* Plugin header */\n\nfunction Index(props: FooterProps) {\n  const {\n    isLoading,\n    loadRetry,\n    isError,\n    renderFooter,\n    isNeedBtnLoadMore,\n    noMore,\n  } = props;\n  const isResponsive = useIsResponsive();\n\n  return (\n    <div\n      className={classNames(s['footer-container'], {\n        [s['responsive-foot-container']]: isResponsive,\n      })}\n    >\n      {renderFooter?.(props) ||\n        (isLoading ? (\n          <>\n            <Spin />\n            <span className={s.loading}>{I18n.t('Loading')}</span>\n          </>\n        ) : isError ? (\n          <>\n            <Spin />\n            <span className={s['error-retry']} onClick={loadRetry}>\n              {I18n.t('inifinit_list_retry')}\n            </span>\n          </>\n        ) : isNeedBtnLoadMore && !noMore ? (\n          <UIButton\n            onClick={loadRetry}\n            className={s['load-more-btn']}\n            theme=\"borderless\"\n          >\n            {I18n.t('mkpl_load_btn')}\n          </UIButton>\n        ) : null)}\n    </div>\n  );\n}\n\nexport default Index;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React from 'react';\n\nimport { I18n } from '@coze-arch/i18n';\nimport { UIEmpty, Spin } from '@coze-arch/bot-semi';\nimport { IllustrationFailure } from '@douyinfe/semi-illustrations';\n\nimport { type EmptyProps } from '../../type';\n\nimport s from './index.module.less';\n\n/* Plugin header */\n\nfunction Index(props: EmptyProps) {\n  const {\n    isLoading,\n    isSearching,\n    loadRetry,\n    isError,\n    renderEmpty,\n    text,\n    btn,\n    icon,\n  } = props;\n  return (\n    <div className={s.empty}>\n      {renderEmpty?.(props) ||\n        (!isError ? (\n          isLoading ? (\n            <Spin\n              tip={\n                <span className={s['loading-text']}>{I18n.t('Loading')}</span>\n              }\n              wrapperClassName={s.spin}\n              size=\"middle\"\n            />\n          ) : (\n            <UIEmpty\n              isNotFound={!!isSearching}\n              empty={{\n                title: text?.emptyTitle || I18n.t('inifinit_list_empty_title'),\n                description: text?.emptyTitle ? text?.emptyDesc : '',\n                btnText: btn?.emptyText,\n                btnOnClick: btn?.emptyClick,\n                icon,\n              }}\n              notFound={{\n                title:\n                  text?.searchEmptyTitle || I18n.t('inifinit_search_not_found'),\n              }}\n            />\n          )\n        ) : (\n          <UIEmpty\n            empty={{\n              title: I18n.t('inifinit_list_load_fail'),\n              icon: <IllustrationFailure />,\n              btnText: loadRetry && I18n.t('inifinit_list_retry'),\n              btnOnClick: () => {\n                loadRetry?.();\n              },\n            }}\n          />\n        ))}\n    </div>\n  );\n}\n\nexport default Index;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  forwardRef,\n  useImperativeHandle,\n  type RefObject,\n  useEffect,\n} from 'react';\n\nimport cls from 'classnames';\nimport { ResponsiveList } from '@coze-arch/responsive-kit';\nimport { List } from '@coze-arch/bot-semi';\n\nimport { type InfiniteListProps, type InfiniteListRef } from './type';\nimport useScroll from './hooks/use-scroll';\nimport Footer from './components/footer';\nimport Empty from './components/empty';\n\nimport s from './index.module.less';\n\n/* Plugin header */\n\nfunction Index<T extends object>(props: InfiniteListProps<T>, ref) {\n  const {\n    isSearching,\n    className,\n    emptyContent,\n    grid,\n    renderItem,\n    itemClassName,\n    renderFooter,\n    scrollConf,\n    emptyConf,\n    onChangeState,\n    canShowData = true,\n    isNeedBtnLoadMore = false,\n    isResponsive,\n    retryFunc,\n    responsiveConf,\n    containerClassName,\n  } = props;\n\n  const {\n    dataList,\n    isLoading,\n    loadMore,\n    noMore,\n    isLoadingError,\n    mutate,\n    reload,\n    insertData,\n    removeData,\n    getDataList,\n  } = useScroll<T>({ ...scrollConf, isNeedBtnLoadMore });\n\n  useImperativeHandle(\n    ref,\n    () => ({ mutate, reload, insertData, removeData, getDataList }),\n    [mutate, reload, insertData, removeData, getDataList],\n  );\n  useEffect(() => {\n    onChangeState?.(isLoading, dataList);\n  }, [dataList, isLoading]);\n\n  // Adapt the mobile end of the list according to the whitelist\n\n  return (\n    <div className={cls(s['height-whole-100'], containerClassName)}>\n      {!dataList?.length || !canShowData ? (\n        /** How to display an empty page when the data is empty */\n        <Empty\n          isError={canShowData ? isLoadingError : false}\n          isSearching={isSearching}\n          isLoading={canShowData ? isLoading : true}\n          loadRetry={retryFunc || loadMore}\n          {...emptyConf}\n        />\n      ) : isResponsive ? (\n        <ResponsiveList<T>\n          className={className}\n          emptyContent={isLoading ? <></> : emptyContent}\n          dataSource={dataList}\n          renderItem={(item, number) => renderItem?.(item, number)}\n          gridCols={responsiveConf?.gridCols}\n          gridGapXs={{\n            basic: 4,\n          }}\n          footer={\n            <div className=\"text-sm px-6 py-3\">\n              <Footer\n                isError={isLoadingError}\n                noMore={noMore}\n                isLoading={isLoading}\n                loadRetry={retryFunc || loadMore}\n                renderFooter={renderFooter}\n                isNeedBtnLoadMore={isNeedBtnLoadMore}\n              />\n            </div>\n          }\n        />\n      ) : (\n        <List\n          {...{ className, emptyContent, grid }}\n          emptyContent={isLoading ? <></> : emptyContent}\n          dataSource={dataList}\n          split={false}\n          renderItem={(item, number) => (\n            <List.Item\n              className={\n                typeof itemClassName === 'string'\n                  ? itemClassName\n                  : itemClassName?.(item) // Support dynamic row className\n              }\n            >\n              {renderItem?.(item, number)}\n            </List.Item>\n          )}\n          footer={\n            <Footer\n              isError={isLoadingError}\n              noMore={noMore}\n              isLoading={isLoading}\n              loadRetry={retryFunc || loadMore}\n              renderFooter={renderFooter}\n              isNeedBtnLoadMore={isNeedBtnLoadMore}\n              dataNum={dataList?.length}\n            />\n          }\n        />\n      )}\n    </div>\n  );\n}\n\nexport const InfiniteList = forwardRef(Index) as <T>(\n  props: InfiniteListProps<T> & { ref?: RefObject<InfiniteListRef> },\n) => JSX.Element;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport classNames from 'classnames';\nimport { CozAvatar, Tag, Tooltip, Typography } from '@coze-arch/coze-design';\nimport { type PluginConnectorInfo } from '@coze-arch/bot-api/product_api';\n\nimport styles from './index.module.less';\n\ninterface ConnectorListProps {\n  connectors: PluginConnectorInfo[];\n  className?: string;\n  visibleNum?: number;\n}\n\nconst DEFAULT_VISIBLE_NUM = 3;\n\nexport const ConnectorList: React.FC<ConnectorListProps> = ({\n  connectors,\n  className,\n  visibleNum = DEFAULT_VISIBLE_NUM,\n}) => {\n  const moreNum = connectors.length - visibleNum;\n  return (\n    <div className={classNames('ml-auto flex gap-4px', className)}>\n      {connectors.slice(0, visibleNum).map(item => (\n        <Tooltip key={item.id} content={item.name} theme=\"dark\">\n          <CozAvatar\n            className=\"border coz-stroke-primary border-solid\"\n            size=\"micro\"\n            src={item.icon}\n            type=\"platform\"\n          />\n        </Tooltip>\n      ))}\n      {moreNum > 0 ? (\n        <Tooltip\n          position=\"right\"\n          content={\n            <div className=\"flex flex-col gap-8px max-w-[200px] max-h-[188px] overflow-y-auto overflow-x-hidden\">\n              {connectors.slice(visibleNum).map(item => (\n                <div\n                  key={item.id}\n                  className=\"flex gap-8px items-center max-w-full\"\n                >\n                  <CozAvatar\n                    className=\"border coz-stroke-primary border-solid\"\n                    size=\"micro\"\n                    src={item.icon}\n                    type=\"platform\"\n                  />\n                  <Typography.Text\n                    ellipsis={true}\n                    className=\"flex-1 overflow-hidden\"\n                  >\n                    {item.name}\n                  </Typography.Text>\n                </div>\n              ))}\n            </div>\n          }\n        >\n          <Tag className={styles.more} size=\"mini\" color=\"primary\">\n            +{moreNum}\n          </Tag>\n        </Tooltip>\n      ) : null}\n    </div>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useMemoizedFn } from 'ahooks';\nimport { type ProductEntityType } from '@coze-arch/bot-api/product_api';\nimport { ProductApi } from '@coze-arch/bot-api';\nimport { cozeMitt } from '@coze-common/coze-mitt';\n\nimport { type FavoriteCommParams } from '../type';\nexport const useFavoriteStatusRequest = ({\n  productId,\n  entityType,\n  entityId,\n  topicId,\n  onChange,\n  setIsFavorite,\n}: FavoriteCommParams & {\n  setIsFavorite: (isFavorite: boolean) => void;\n}) => {\n  const changeFavoriteStatus = useMemoizedFn(\n    async (isCurFavorite: boolean, action: string) => {\n      setIsFavorite(!isCurFavorite);\n      try {\n        await ProductApi.PublicFavoriteProduct({\n          // Backend cannot handle empty strings\n          product_id: productId || undefined,\n          entity_type: entityType as ProductEntityType,\n          is_cancel: isCurFavorite,\n          entity_id: entityId,\n          topic_id: topicId,\n        });\n        onChange?.(isCurFavorite ? -1 : 1);\n        cozeMitt.emit('refreshFavList', {\n          id: entityId,\n          numDelta: action === 'add' ? 1 : -1,\n          emitPosition: 'favorite-icon-btn',\n        });\n      } catch (_err) {\n        setIsFavorite(isCurFavorite);\n      }\n    },\n  );\n  return { changeFavoriteStatus };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useEffect, useState } from 'react';\n\nimport { useMemoizedFn } from 'ahooks';\n\nexport const useAnimationChange = ({ isVisible }: { isVisible?: boolean }) => {\n  const [isShowAni, setIsShowAni] = useState(false);\n  useEffect(() => {\n    if (!isVisible) {\n      setIsShowAni(false);\n    }\n  }, [isVisible]);\n  const changeAnimationStatus = useMemoizedFn((isCurFavorite: boolean) => {\n    if (!isCurFavorite) {\n      setIsShowAni(true);\n    } else {\n      setIsShowAni(false);\n    }\n  });\n  return { isShowAni, changeAnimationStatus };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useState, useCallback, useRef, type MouseEvent } from 'react';\n\nimport { useMemoizedFn, useUpdateEffect } from 'ahooks';\n\nimport { type FavoriteCommParams } from '../type';\nimport { useFavoriteStatusRequest } from './use-farvorite-request';\nimport { useAnimationChange } from './use-animation-change';\n\ntype ClickAction = 'cancel' | 'add';\nconst getClickAction = (isCurFavoriteStatus: boolean): ClickAction =>\n  isCurFavoriteStatus ? 'cancel' : 'add';\n\nexport const useFavoriteChange = ({\n  isFavoriteDefault,\n  onReportTea,\n  productId,\n  entityId,\n  entityType,\n  onChange,\n  onClickBefore,\n  topicId,\n  isVisible,\n  onFavoriteStateChange,\n}: FavoriteCommParams & {\n  onReportTea?: (action: 'cancel' | 'add') => void;\n  isFavoriteDefault?: boolean;\n  isVisible?: boolean;\n  onFavoriteStateChange?: (isFavorite: boolean) => void;\n}) => {\n  const [isFavorite, setIsFavorite] = useState<boolean>(\n    isFavoriteDefault ?? false,\n  );\n  const { isShowAni, changeAnimationStatus } = useAnimationChange({\n    isVisible,\n  });\n  const refIsChange = useRef(false);\n\n  // Before changing the state, make a pre-request to determine whether the state change needs to be abandoned. If onClickBefore returns false, no change will be made.\n  const onClickBeforeHandle = useMemoizedFn(\n    async (\n      action: ClickAction,\n      event?: MouseEvent<HTMLDivElement, globalThis.MouseEvent>,\n    ) => (await onClickBefore?.(action, event)) !== false,\n  );\n  const { changeFavoriteStatus } = useFavoriteStatusRequest({\n    productId,\n    entityType,\n    entityId,\n    topicId,\n    onChange,\n    setIsFavorite,\n  });\n\n  const onClick = useCallback(\n    async (event?: MouseEvent<HTMLDivElement, globalThis.MouseEvent>) => {\n      if (refIsChange.current) {\n        // In progress, return directly, no processing\n        event?.stopPropagation?.();\n        event?.preventDefault?.();\n        return;\n      }\n      const action = getClickAction(isFavorite);\n      refIsChange.current = true;\n      try {\n        if ((await onClickBeforeHandle(action, event)) !== false) {\n          event?.stopPropagation?.();\n          onReportTea?.(action);\n          changeAnimationStatus(isFavorite);\n          await changeFavoriteStatus(isFavorite, action);\n        }\n      } catch (_err) {\n        console.error('useFavoriteChange:', _err);\n      }\n      refIsChange.current = false;\n    },\n    [isFavorite, onReportTea],\n  );\n\n  useUpdateEffect(() => {\n    onFavoriteStateChange?.(isFavorite);\n  }, [isFavorite]);\n  return { isFavorite, onClick, isShowAni };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React from 'react';\n\nimport { IconMobileCollect, IconMobileCollectFill } from '@coze-arch/bot-icons';\n\nexport const FavoriteIconMobile = (props: { isFavorite?: boolean }) => {\n  const { isFavorite } = props;\n  return <>{isFavorite ? <IconMobileCollectFill /> : <IconMobileCollect />}</>;\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React from 'react';\n\nimport cls from 'classnames';\nimport { IconButton } from '@coze-arch/coze-design';\nimport { IconCollectFilled, IconCollectStroked } from '@coze-arch/bot-icons';\n\nimport styles from './index.module.less';\n\nexport const FavoriteIcon = (props: {\n  isFavorite?: boolean;\n  isShowAni: boolean;\n  unCollectedIconCls?: string;\n  isMobile?: boolean;\n  useButton?: boolean;\n  className?: string;\n}) => {\n  const { isFavorite, isShowAni, className, unCollectedIconCls, useButton } =\n    props;\n\n  const iconProps = {\n    className: cls(\n      isFavorite ? styles['icon-filled'] : styles['icon-stroked'],\n      isFavorite ? className : unCollectedIconCls,\n      {\n        [styles['show-ani']]: isFavorite && isShowAni,\n        [styles['show-btn']]: useButton,\n      },\n    ),\n  };\n\n  const icon = isFavorite ? (\n    <IconCollectFilled {...iconProps} />\n  ) : (\n    <IconCollectStroked {...iconProps} />\n  );\n\n  if (useButton) {\n    return <IconButton size=\"default\" color=\"primary\" icon={icon} />;\n  }\n\n  return icon;\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React, { forwardRef, useImperativeHandle } from 'react';\n\nimport { type FavoriteIconBtnProps } from './type';\nimport { useFavoriteChange } from './hooks/use-favorite-change';\nimport { FavoriteIconMobile } from './components/favorite-icon-mobile';\nimport { FavoriteIcon } from './components/favorite-icon';\n\nimport styles from './index.module.less';\n\nexport interface FavoriteIconBtnRef {\n  favorite: (event) => void;\n}\n\nexport const FavoriteIconBtn = forwardRef(\n  (props: FavoriteIconBtnProps, ref) => {\n    const {\n      topicId,\n      productId,\n      entityType,\n      entityId,\n      isFavorite: isFavoriteDefault,\n      onChange,\n      isVisible,\n      onReportTea,\n      unCollectedIconCls,\n      onClickBefore,\n      onFavoriteStateChange,\n      isMobile,\n      className,\n      useButton = false,\n      isForbiddenClick = false,\n    } = props;\n\n    const { isFavorite, onClick, isShowAni } = useFavoriteChange({\n      isFavoriteDefault,\n      onReportTea,\n      productId,\n      entityId,\n      entityType,\n      onChange,\n      onClickBefore,\n      topicId,\n      isVisible,\n      onFavoriteStateChange,\n    });\n\n    useImperativeHandle(\n      ref,\n      () => ({\n        favorite: onClick,\n      }),\n      [onClick],\n    );\n\n    if (!isVisible) {\n      return null;\n    }\n    return (\n      <div\n        onClick={isForbiddenClick ? undefined : onClick}\n        className={styles['favorite-icon-btn']}\n        data-testid=\"bot-card-favorite-icon\"\n      >\n        {isMobile ? (\n          <FavoriteIconMobile isFavorite={isFavorite} />\n        ) : (\n          <FavoriteIcon\n            useButton={useButton}\n            isFavorite={isFavorite}\n            isShowAni={isShowAni}\n            unCollectedIconCls={unCollectedIconCls}\n            className={className}\n          />\n        )}\n      </div>\n    );\n  },\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type FC, type ReactNode } from 'react';\n\nimport classNames from 'classnames';\nimport { Typography } from '@coze-arch/coze-design';\n\nexport interface SubMenuItemProps {\n  icon?: ReactNode;\n  title?: string;\n  activeIcon?: ReactNode;\n  isActive: boolean;\n  suffix?: ReactNode;\n  onClick: () => void;\n}\n\nexport const SubMenuItem: FC<SubMenuItemProps> = ({\n  icon = null,\n  title,\n  activeIcon = null,\n  isActive,\n  suffix,\n  onClick,\n}) => (\n  <div\n    onClick={onClick}\n    className={classNames(\n      'flex items-center gap-[8px]',\n      'transition-colors',\n      'rounded-[8px]',\n      'h-[32px] w-full',\n      'px-[8px]',\n      'cursor-pointer',\n      'hover:coz-mg-primary-hovered',\n      isActive ? 'coz-bg-primary coz-fg-plus' : 'coz-fg-primary coz-bg-max',\n    )}\n  >\n    <div className=\"text-[16px] leading-none leading-none w-[16px] h-[16px]\">\n      {isActive ? activeIcon : icon}\n    </div>\n    <Typography.Text\n      ellipsis={{ showTooltip: true, rows: 1 }}\n      fontSize=\"14px\"\n      weight={500}\n      className=\"flex-1 text-[14px] leading-[20px] font-[500]\"\n    >\n      {title}\n    </Typography.Text>\n    {suffix}\n  </div>\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type ReactNode } from 'react';\n\nimport { I18n, type I18nKeysNoOptionsType } from '@coze-arch/i18n';\nimport {\n  IconCozBot,\n  IconCozWorkflow,\n  IconCozWorkspace,\n} from '@coze-arch/coze-design/icons';\nimport { Tag, type TagProps } from '@coze-arch/coze-design';\nimport { ProductEntityType } from '@coze-arch/bot-api/product_api';\n\ninterface IProps {\n  type: ProductEntityType;\n}\n\ninterface TagConfig {\n  icon: ReactNode;\n  i18nKey: I18nKeysNoOptionsType;\n}\n\nconst TYPE_ICON_MAP: Partial<Record<ProductEntityType, TagConfig>> = {\n  [ProductEntityType.BotTemplate]: {\n    icon: <IconCozBot />,\n    i18nKey: 'template_agent',\n  },\n  [ProductEntityType.WorkflowTemplateV2]: {\n    icon: <IconCozWorkflow />,\n    i18nKey: 'template_workflow',\n  },\n  [ProductEntityType.ImageflowTemplateV2]: {\n    icon: <IconCozWorkflow />,\n    i18nKey: 'template_workflow',\n  },\n  [ProductEntityType.ProjectTemplate]: {\n    icon: <IconCozWorkspace />,\n    i18nKey: 'project_store_search',\n  },\n};\n\nconst TYPE_COLOR_MAP: Partial<Record<ProductEntityType, TagProps['color']>> = {\n  [ProductEntityType.BotTemplate]: 'primary',\n  [ProductEntityType.WorkflowTemplateV2]: 'primary',\n  [ProductEntityType.ImageflowTemplateV2]: 'primary',\n  [ProductEntityType.ProjectTemplate]: 'brand',\n};\n\nexport const CardTag = ({ type }: IProps) => {\n  const config = TYPE_ICON_MAP[type];\n  if (!config) {\n    return null;\n  }\n\n  return (\n    <Tag\n      color={TYPE_COLOR_MAP[type] ?? 'primary'}\n      className=\"h-[20px] !px-[4px] !py-[2px] coz-fg-primary font-medium shrink-0\"\n    >\n      {config.icon}\n      <span className=\"ml-[2px]\">{I18n.t(config.i18nKey)}</span>\n    </Tag>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type FC } from 'react';\n\nimport cls from 'classnames';\nimport { AvatarName } from '@coze-studio/components';\nimport { type explore } from '@coze-studio/api-schema';\nimport { type UserInfo as ProductUserInfo } from '@coze-arch/bot-api/product_api';\nimport { Typography } from '@coze-arch/coze-design';\n\ntype UserInfo = explore.product_common.UserInfo | ProductUserInfo;\ninterface TemplateCardBodyProps {\n  title?: string;\n  description?: string;\n  userInfo?: UserInfo;\n  descClassName?: string;\n  renderCardTag?: () => React.ReactNode;\n  renderDescBottomSlot?: () => React.ReactNode;\n}\nexport const CardInfo: FC<TemplateCardBodyProps> = ({\n  title,\n  description,\n  userInfo,\n  renderCardTag,\n  descClassName,\n  renderDescBottomSlot,\n}) => (\n  <div className={cls('mt-[8px] px-[4px] grow', 'flex flex-col')}>\n    <div className=\"flex items-center gap-[8px] overflow-hidden\">\n      <Typography.Text\n        className=\"!font-medium text-[16px] leading-[22px] coz-fg-primary !max-w-[180px]\"\n        ellipsis={{ showTooltip: true, rows: 1 }}\n      >\n        {title}\n      </Typography.Text>\n      {renderCardTag?.()}\n    </div>\n\n    <AvatarName\n      className=\"mt-[4px]\"\n      avatar={userInfo?.avatar_url}\n      name={userInfo?.name}\n      username={userInfo?.user_name}\n      label={{\n        name: userInfo?.user_label?.label_name,\n        icon: userInfo?.user_label?.icon_url,\n        href: userInfo?.user_label?.jump_link,\n      }}\n    />\n\n    <div\n      className={cls(\n        'mt-[8px] flex flex-col justify-between grow',\n        descClassName,\n      )}\n    >\n      <Typography.Text\n        className=\"min-h-[40px] leading-[20px] coz-fg-secondary\"\n        ellipsis={{ showTooltip: true, rows: 2 }}\n      >\n        {description}\n      </Typography.Text>\n      {renderDescBottomSlot?.()}\n    </div>\n  </div>\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React from 'react';\n\nimport classNames from 'classnames';\n\nimport s from './index.module.less';\n\nconst Container = (props: {\n  className?: string;\n  children?: React.ReactNode;\n  shadowMode?: 'default' | 'primary';\n  onClick?: () => void;\n}) => {\n  const { className, children, onClick, shadowMode } = props;\n\n  return (\n    <div\n      className={classNames(\n        'coz-bg-max',\n        s.container,\n        s.width100,\n        className,\n        s[`shadow-${shadowMode}`],\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  );\n};\n\nconst SkeletonContainer = (props: {\n  children?: React.ReactNode;\n  className?: string;\n}) => (\n  <div\n    className={classNames(\n      'coz-mg-primary',\n      s.container,\n      s.width100,\n      s.skeleton,\n      props.className,\n    )}\n  >\n    {props?.children}\n  </div>\n);\n\nexport const CardContainer = Container;\nexport const CardSkeletonContainer = SkeletonContainer;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type FC, type PropsWithChildren } from 'react';\n\nimport cls from 'classnames';\n\nimport styles from './index.module.less';\n\nexport const CardButton: FC<\n  PropsWithChildren<{\n    className?: string;\n    onClick?: () => void;\n  }>\n> = ({ className, onClick, children }) => (\n  <button\n    className={cls(styles['card-button'], className)}\n    color=\"primary\"\n    onClick={onClick}\n  >\n    {children}\n  </button>\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type FC, useState } from 'react';\n\nimport cls from 'classnames';\nimport { explore } from '@coze-studio/api-schema';\nimport { useSpaceList } from '@coze-foundation/space-store';\nimport { I18n } from '@coze-arch/i18n';\nimport { Image, Input, Modal, Space, Toast } from '@coze-arch/coze-design';\nimport { type ProductEntityType } from '@coze-arch/bot-api/product_api';\n\nimport { type CardInfoProps } from '../type';\nimport { CardTag } from '../components/tag';\nimport { CardInfo } from '../components/info';\nimport { CardContainer, CardSkeletonContainer } from '../components/container';\nimport { CardButton } from '../components/button';\n\ntype ProductInfo = explore.ProductInfo;\nimport styles from './index.module.less';\n\nexport type TemplateCardProps = ProductInfo;\n\nexport const TemplateCard: FC<TemplateCardProps> = props => {\n  const [visible, setVisible] = useState(false);\n  return (\n    <CardContainer className={styles.template} shadowMode=\"default\">\n      <div className={styles['template-wrapper']}>\n        <TempCardBody\n          {...{\n            title: props.meta_info?.name,\n            description: props.meta_info?.description,\n            userInfo: props.meta_info?.user_info,\n            entityType: props.meta_info.entity_type,\n            imgSrc: props.meta_info.covers?.[0].url,\n          }}\n        />\n        <Space className={styles['btn-container']}>\n          <CardButton\n            onClick={() => {\n              setVisible(true);\n            }}\n            className=\"w-full\"\n          >\n            {I18n.t('copy')}\n          </CardButton>\n        </Space>\n      </div>\n      {visible ? (\n        <DuplicateModal\n          productId={props.meta_info.id}\n          entityType={props.meta_info.entity_type}\n          defaultTitle={`${props.meta_info?.name}(${I18n.t(\n            'duplicate_rename_copy',\n          )})`}\n          hide={() => setVisible(false)}\n        />\n      ) : null}\n    </CardContainer>\n  );\n};\n\nconst DuplicateModal: FC<{\n  defaultTitle: string;\n  productId: string;\n  entityType: explore.product_common.ProductEntityType;\n  hide: () => void;\n}> = ({ defaultTitle, hide, productId, entityType }) => {\n  const [title, setTitle] = useState(defaultTitle);\n  const { spaces } = useSpaceList();\n  const spaceId = spaces?.[0]?.id;\n  return (\n    <Modal\n      type=\"modal\"\n      title={I18n.t('creat_project_use_template')}\n      visible={true}\n      onOk={async () => {\n        try {\n          await explore.PublicDuplicateProduct({\n            product_id: productId,\n            entity_type: entityType,\n            space_id: spaceId,\n            name: title,\n          });\n          Toast.success(I18n.t('copy_success'));\n          hide();\n        } catch (err) {\n          console.error('PublicDuplicateProduct', err);\n          Toast.error(I18n.t('copy_failed'));\n        }\n      }}\n      onCancel={hide}\n      cancelText={I18n.t('Cancel')}\n      okText={I18n.t('Confirm')}\n    >\n      <Space vertical spacing={4} className=\"w-full\">\n        <Space className=\"w-full\">\n          <span className=\"coz-fg-primary font-medium leading-[20px]\">\n            {I18n.t('creat_project_project_name')}\n          </span>\n          <span className=\"coz-fg-hglt-red\">*</span>\n        </Space>\n        <Input\n          className=\"w-full\"\n          placeholder=\"\"\n          defaultValue={defaultTitle}\n          onChange={value => {\n            setTitle(value);\n          }}\n        />\n      </Space>\n    </Modal>\n  );\n};\n\nexport const TemplateCardSkeleton = () => (\n  <CardSkeletonContainer className={cls('h-[278px]', styles.template)} />\n);\n\nexport const TempCardBody: FC<\n  CardInfoProps & {\n    entityType?: explore.product_common.ProductEntityType | ProductEntityType;\n    renderImageBottomSlot?: () => React.ReactNode;\n    renderDescBottomSlot?: () => React.ReactNode;\n  }\n> = ({\n  title,\n  imgSrc,\n  description,\n  entityType,\n  userInfo,\n  renderImageBottomSlot,\n  renderDescBottomSlot,\n}) => (\n  <div>\n    <div className=\"relative w-full h-[140px] rounded-[8px] overflow-hidden\">\n      <Image\n        preview={false}\n        src={imgSrc}\n        className=\"w-full h-full\"\n        imgCls=\"w-full h-full object-cover object-center\"\n      />\n      {renderImageBottomSlot?.()}\n    </div>\n    <CardInfo\n      {...{\n        title,\n        description,\n        userInfo,\n        renderCardTag: () =>\n          entityType ? <CardTag type={entityType} /> : null,\n        descClassName: styles.description,\n        renderDescBottomSlot,\n      }}\n    />\n  </div>\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type FC } from 'react';\n\nimport cls from 'classnames';\nimport { type explore } from '@coze-studio/api-schema';\nimport { I18n } from '@coze-arch/i18n';\nimport { Avatar, Space, Tag, Toast, Tooltip } from '@coze-arch/coze-design';\n\nimport { PluginAuthMode, type AuthMode } from '../type';\nimport { CardInfo } from '../components/info';\nimport { CardContainer, CardSkeletonContainer } from '../components/container';\nimport { CardButton } from '../components/button';\n\nimport styles from './index.module.less';\n\ninterface ProductInfo extends explore.ProductInfo {\n  plugin_extra: explore.ProductInfo['plugin_extra'] & AuthMode;\n}\n\nexport type PluginCardProps = ProductInfo & {\n  isInstalled?: boolean;\n  isShowInstallButton?: boolean;\n};\n\nexport const PluginCard: FC<PluginCardProps> = props => (\n  <CardContainer className={styles.plugin} shadowMode=\"default\">\n    <div className={styles['plugin-wrapper']}>\n      <PluginCardBody {...props} />\n\n      <Space\n        className={cls(styles['btn-container'], {\n          [styles['one-column-grid']]:\n            props.isInstalled || !props.isShowInstallButton,\n        })}\n      >\n        {!props.isInstalled && props.isShowInstallButton ? (\n          <CardButton\n            onClick={() => {\n              Toast.success(I18n.t('plugin_install_success'));\n            }}\n          >\n            {I18n.t('plugin_store_install')}\n          </CardButton>\n        ) : null}\n      </Space>\n    </div>\n  </CardContainer>\n);\n\nexport const PluginCardSkeleton = () => (\n  <CardSkeletonContainer className={cls('h-[186px]', styles.plugin)} />\n);\n\nconst PluginCardBody: FC<PluginCardProps> = props => {\n  const renderCardTag = () => {\n    if (\n      props.plugin_extra.auth_mode === PluginAuthMode.Required ||\n      props.plugin_extra.auth_mode === PluginAuthMode.Supported\n    ) {\n      return (\n        <Tag\n          color={'yellow'}\n          className=\"h-[20px] !px-[4px] !py-[2px] coz-fg-primary font-medium shrink-0\"\n        >\n          <span className=\"ml-[2px]\">\n            {I18n.t('plugin_store_unauthorized')}\n          </span>\n        </Tag>\n      );\n    } else if (props.plugin_extra.auth_mode === PluginAuthMode.Configured) {\n      return (\n        <Tooltip content={I18n.t('plugin_store_contact_deployer')}>\n          <Tag\n            color={'brand'}\n            className=\"h-[20px] !px-[4px] !py-[2px] coz-fg-primary font-medium shrink-0\"\n          >\n            <span className=\"ml-[2px]\">\n              {I18n.t('plugin_store_authorized')}\n            </span>\n          </Tag>\n        </Tooltip>\n      );\n    }\n    return null;\n  };\n  return (\n    <div>\n      <Avatar\n        className={styles['card-avatar']}\n        src={props.meta_info?.icon_url}\n        shape=\"square\"\n      />\n      <CardInfo\n        {...{\n          title: props.meta_info?.name,\n          description: props.meta_info?.description,\n          userInfo: props.meta_info?.user_info,\n          authMode: props.plugin_extra.auth_mode,\n          renderCardTag,\n        }}\n      />\n    </div>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type FC } from 'react';\n\nimport { Avatar } from '@coze-arch/coze-design';\n\nexport interface CreatorProps {\n  avatar?: string;\n  name?: string;\n  extra?: string;\n}\n\nexport const Creator: FC<CreatorProps> = ({ avatar, name, extra }) => (\n  <div className=\"flex items-center gap-x-[4px] h-[16px] coz-fg-secondary text-[12px] leading-16px\">\n    {/* The open-source version has no multi-person collaboration function and does not display resource owner information */}\n    {IS_OPEN_SOURCE ? null : (\n      <>\n        <Avatar className=\"w-[16px] h-[16px] flex-shrink-0\" src={avatar} />\n        <div className=\"text-nowrap\">{name}</div>\n        <div className=\"w-3px h-3px rounded-full bg-[var(--coz-fg-secondary)]\" />\n      </>\n    )}\n    <div className=\"text-ellipsis whitespace-nowrap overflow-hidden\">\n      {extra}\n    </div>\n  </div>\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React, { type HTMLAttributes, forwardRef } from 'react';\n\nimport classNames from 'classnames';\n\nexport type LayoutBaseProps = HTMLAttributes<HTMLDivElement>;\n\nexport const Layout = forwardRef<HTMLDivElement, LayoutBaseProps>(\n  ({ children, ...restProps }, ref) => (\n    <div\n      {...restProps}\n      ref={ref}\n      className={classNames(\n        restProps.className,\n        'min-h-[100%]',\n        'flex flex-col gap-[16px]',\n        'overflow-hidden',\n        'px-[24px] pt-[24px]',\n      )}\n    >\n      {children}\n    </div>\n  ),\n);\n\nexport const Header = forwardRef<HTMLDivElement, LayoutBaseProps>(\n  ({ children, ...restProps }, ref) => (\n    <div\n      {...restProps}\n      ref={ref}\n      className={classNames(\n        restProps.className,\n        'flex-shrink-0',\n        'w-full h-[32px]',\n        'flex items-center justify-between',\n      )}\n    >\n      {children}\n    </div>\n  ),\n);\n\nexport const HeaderTitle = forwardRef<HTMLDivElement, LayoutBaseProps>(\n  ({ children, ...restProps }, ref) => (\n    <div\n      {...restProps}\n      ref={ref}\n      className={classNames(\n        restProps.className,\n        'text-[20px] font-[500]',\n        'flex items-center gap-[8px]',\n      )}\n    >\n      {children}\n    </div>\n  ),\n);\n\nexport const HeaderActions = forwardRef<HTMLDivElement, LayoutBaseProps>(\n  ({ children, ...restProps }, ref) => (\n    <div\n      {...restProps}\n      ref={ref}\n      className={classNames(\n        restProps.className,\n        'flex items-center gap-[8px] ml-[32px]',\n      )}\n    >\n      {children}\n    </div>\n  ),\n);\n\nexport const SubHeader = forwardRef<HTMLDivElement, LayoutBaseProps>(\n  ({ children, ...restProps }, ref) => (\n    <div\n      {...restProps}\n      ref={ref}\n      className={classNames(\n        restProps.className,\n        'flex-shrink-0',\n        'w-full h-[32px]',\n        'flex items-center justify-between',\n      )}\n    >\n      {children}\n    </div>\n  ),\n);\n\nexport const SubHeaderFilters = forwardRef<HTMLDivElement, LayoutBaseProps>(\n  ({ children, ...restProps }, ref) => (\n    <div\n      {...restProps}\n      ref={ref}\n      className={classNames(restProps.className, 'flex items-center gap-[8px]')}\n    >\n      {children}\n    </div>\n  ),\n);\n\nexport const SubHeaderSearch = forwardRef<HTMLDivElement, LayoutBaseProps>(\n  ({ children, ...restProps }, ref) => (\n    <div {...restProps} ref={ref} className={classNames(restProps.className)}>\n      {children}\n    </div>\n  ),\n);\n\nexport const Content = forwardRef<HTMLDivElement, LayoutBaseProps>(\n  ({ children, ...restProps }, ref) => (\n    <div\n      {...restProps}\n      ref={ref}\n      className={classNames(\n        restProps.className,\n        'flex-grow',\n        'overflow-x-hidden overflow-y-auto',\n      )}\n    >\n      {children}\n    </div>\n  ),\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type FC } from 'react';\n\nimport { I18n } from '@coze-arch/i18n';\nimport { IconCozEmpty, IconCozBroom } from '@coze-arch/coze-design/icons';\nimport { Button } from '@coze-arch/coze-design';\n\ninterface WorkspaceEmptyProps {\n  onClear?: () => void; // Clear button click event\n  hasFilter?: boolean; // Is there a filter\n}\n\nexport const WorkspaceEmpty: FC<WorkspaceEmptyProps> = ({\n  onClear,\n  hasFilter = false,\n}) => (\n  <div className=\"w-full h-full flex flex-col items-center pt-[120px]\">\n    <IconCozEmpty className=\"w-[48px] h-[48px] coz-fg-dim\" />\n    <div className=\"text-[16px] font-[500] leading-[22px] mt-[8px] mb-[16px] coz-fg-primary\">\n      {I18n.t(\n        hasFilter ? 'library_empty_no_results_found_under' : 'search_not_found',\n      )}\n    </div>\n    {hasFilter ? (\n      <Button\n        color=\"primary\"\n        icon={<IconCozBroom />}\n        onClick={() => {\n          onClear?.();\n        }}\n      >\n        {I18n.t('library_empty_clear_filters')}\n      </Button>\n    ) : null}\n  </div>\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const highlightFilterStyle = {\n  border: '1px solid var(--semi-color-focus-border)',\n};\n", "\n      import API from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./index.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.card-button-ULgKj1{cursor:pointer;text-align:center;border:1px solid;border-color:rgba(var(--coze-stroke-5),var(--coze-stroke-5-alpha));background-color:rgba(var(--coze-bg-1),var(--coze-bg-1-alpha));height:32px;color:rgba(var(--coze-fg-3),var(--coze-fg-3-alpha));border-radius:8px;padding:6px 16px;font-size:14px;font-weight:500;line-height:20px}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"card-button\": `card-button-ULgKj1`,\n\t\"cardButton\": `card-button-ULgKj1`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.container-orqglg{border:1px solid;border-color:rgba(var(--coze-stroke-5),var(--coze-stroke-5-alpha));border-radius:8px;margin-bottom:20px;padding:16px;position:relative}.container-orqglg:not(.skeleton-L9KNpP):hover{background-color:rgba(var(--coze-bg-3),var(--coze-bg-3-alpha));border-color:rgba(var(--coze-stroke-5),var(--coze-stroke-5-alpha));box-shadow:0 6px 8px rgba(28,31,35,.06)}.container-orqglg:not(.skeleton-L9KNpP):hover.shadow-primary-LnKwQ9{box-shadow:0 6px 8px rgba(0,8,16,.12)}.container-orqglg.skeleton-L9KNpP{cursor:default;border-color:transparent}.container-orqglg .check-n6pq7x{z-index:1;position:absolute;top:16px;right:16px}.width100-mBTC1U{width:100%;overflow:hidden}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"container\": `container-orqglg`,\n\t\"skeleton\": `skeleton-L9KNpP`,\n\t\"shadow-primary\": `shadow-primary-LnKwQ9`,\n\t\"shadowPrimary\": `shadow-primary-LnKwQ9`,\n\t\"check\": `check-n6pq7x`,\n\t\"width100\": `width100-mBTC1U`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.plugin-xTWad1{margin-bottom:0}.plugin-xTWad1 .plugin-wrapper-Kvdkvq{position:relative}.plugin-xTWad1 .btn-container-dVepz3{grid-template-columns:repeat(2,minmax(0,1fr));width:100%;display:none;position:absolute;bottom:0;left:0}.plugin-xTWad1 .btn-container-dVepz3.one-column-grid-IDA6oM{grid-template-columns:repeat(1,minmax(0,1fr))}.plugin-xTWad1:hover .btn-container-dVepz3{display:grid}.plugin-xTWad1:hover .description-cP3CQP{visibility:hidden}.plugin-xTWad1 .card-avatar-tPlZkK{border-radius:6px;width:48px;height:48px}.plugin-xTWad1 .card-avatar-tPlZkK:after{content:\"\";z-index:2;border-style:solid;border-width:1px;border-color:rgba(var(--coze-stroke-5),var(--coze-stroke-5-alpha));border-radius:6px;width:calc(100% - 2px);height:calc(100% - 2px);position:absolute}.plugin-xTWad1 .card-avatar-tPlZkK:before{content:\"\";z-index:1;background-color:rgba(var(--coze-stroke-5),var(--coze-stroke-5-alpha));border-radius:5px;width:calc(100% - 2px);height:calc(100% - 2px);position:absolute;top:1px;left:1px}.plugin-xTWad1 .card-avatar-tPlZkK>img{z-index:2}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"plugin\": `plugin-xTWad1`,\n\t\"plugin-wrapper\": `plugin-wrapper-Kvdkvq`,\n\t\"pluginWrapper\": `plugin-wrapper-Kvdkvq`,\n\t\"btn-container\": `btn-container-dVepz3`,\n\t\"btnContainer\": `btn-container-dVepz3`,\n\t\"one-column-grid\": `one-column-grid-IDA6oM`,\n\t\"oneColumnGrid\": `one-column-grid-IDA6oM`,\n\t\"description\": `description-cP3CQP`,\n\t\"card-avatar\": `card-avatar-tPlZkK`,\n\t\"cardAvatar\": `card-avatar-tPlZkK`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.template-QsIUVW{margin-bottom:0}.template-QsIUVW .template-wrapper-ogPHKn{position:relative}.template-QsIUVW .btn-container-vkmxIx{width:100%;display:none;position:absolute;bottom:0;left:0}.template-QsIUVW:hover .btn-container-vkmxIx{display:block}.template-QsIUVW:hover .description-WS94RD{visibility:hidden}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"template\": `template-QsIUVW`,\n\t\"template-wrapper\": `template-wrapper-ogPHKn`,\n\t\"templateWrapper\": `template-wrapper-ogPHKn`,\n\t\"btn-container\": `btn-container-vkmxIx`,\n\t\"btnContainer\": `btn-container-vkmxIx`,\n\t\"description\": `description-WS94RD`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.more-HdPRWb.coz-tag-mini{padding:0 4px}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"more\": `more-HdPRWb`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.favorite-btn-CQZ09Q{color:#1d1c23}.favorite-btn-CQZ09Q .semi-button-content-right{color:#1d1c23;margin-left:4px}.favorite-btn-CQZ09Q.dark-SlWeT4 .un-collected-WPNqWl path{fill:#1d1c23}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"favorite-btn\": `favorite-btn-CQZ09Q`,\n\t\"favoriteBtn\": `favorite-btn-CQZ09Q`,\n\t\"dark\": `dark-SlWeT4`,\n\t\"un-collected\": `un-collected-WPNqWl`,\n\t\"unCollected\": `un-collected-WPNqWl`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.icon-filled-TJ6Cgp{color:#ffcc12;width:100%}.icon-stroked-fuKtRz{color:rgba(29,28,35,.35);width:100%}.show-ani-fD8sqq{animation-name:ani-x4fURs;animation-duration:.6s}.show-btn-j0jowu.icon-stroked-fuKtRz{width:100%;color:var(--coz-fg-primary)}@keyframes ani-x4fURs{0%{transform:scale(0)}38%{transform:scale(1.11)}64%{transform:scale(.99)}74%,to{transform:scale(1)}}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"icon-filled\": `icon-filled-TJ6Cgp`,\n\t\"iconFilled\": `icon-filled-TJ6Cgp`,\n\t\"icon-stroked\": `icon-stroked-fuKtRz`,\n\t\"iconStroked\": `icon-stroked-fuKtRz`,\n\t\"show-ani\": `show-ani-fD8sqq`,\n\t\"showAni\": `show-ani-fD8sqq`,\n\t\"ani\": `ani-x4fURs`,\n\t\"show-btn\": `show-btn-j0jowu`,\n\t\"showBtn\": `show-btn-j0jowu`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.favorite-icon-btn-JkKqYz{justify-content:center;align-items:center;width:100%;height:100%;display:flex}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"favorite-icon-btn\": `favorite-icon-btn-JkKqYz`,\n\t\"favoriteIconBtn\": `favorite-icon-btn-JkKqYz`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.empty-jMG4bF{height:100%;overflow:visible}.empty-jMG4bF .spin-WsFW5F{width:100%;height:100%;display:block}.empty-jMG4bF .spin-WsFW5F .semi-spin-wrapper{justify-content:center;display:flex;position:absolute}.empty-jMG4bF .spin-WsFW5F .semi-spin-wrapper svg{width:24px;height:24px}.empty-jMG4bF .spin-WsFW5F .semi-tabs-content{padding:0}.empty-jMG4bF .spin-WsFW5F .semi-spin-children{height:100%}.empty-jMG4bF .spin-WsFW5F .loading-text-X8aAql{color:var(--semi-color-text-3,rgba(29,28,35,.35));margin-left:8px;font-size:16px;font-weight:400;line-height:22px}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"empty\": `empty-jMG4bF`,\n\t\"spin\": `spin-WsFW5F`,\n\t\"loading-text\": `loading-text-X8aAql`,\n\t\"loadingText\": `loading-text-X8aAql`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.footer-container-vf98Hl{text-align:center;padding:12px 0 28px}.footer-container-vf98Hl *{vertical-align:middle}.footer-container-vf98Hl .loading-fwJj8T,.footer-container-vf98Hl .error-retry-pboaKN{color:var(--semi-color-text-3,rgba(29,28,35,.35));margin-left:10px;line-height:20px}.footer-container-vf98Hl .error-retry-pboaKN{cursor:pointer;color:var(--semi-color-focus-border,#4d53e8)}.footer-container-vf98Hl .semi-spin-middle>.semi-spin-wrapper{height:16px}.footer-container-vf98Hl .semi-spin-middle>.semi-spin-wrapper svg{width:16px;height:16px}.footer-container-vf98Hl .load-more-btn-ZZeGvr{background:#fff;border-radius:40px;font-weight:600}.footer-container-vf98Hl .load-more-btn-ZZeGvr span{color:#1d1c23}.footer-container-vf98Hl .load-more-btn-ZZeGvr:hover{background:#fff;border:none}.footer-container-vf98Hl.responsive-foot-container-MZUjCc{padding:0 0 16px}.footer-container-vf98Hl.responsive-foot-container-MZUjCc .load-more-btn-ZZeGvr{height:40px;padding:16px 24px}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"footer-container\": `footer-container-vf98Hl`,\n\t\"footerContainer\": `footer-container-vf98Hl`,\n\t\"loading\": `loading-fwJj8T`,\n\t\"error-retry\": `error-retry-pboaKN`,\n\t\"errorRetry\": `error-retry-pboaKN`,\n\t\"load-more-btn\": `load-more-btn-ZZeGvr`,\n\t\"loadMoreBtn\": `load-more-btn-ZZeGvr`,\n\t\"responsive-foot-container\": `responsive-foot-container-MZUjCc`,\n\t\"responsiveFootContainer\": `responsive-foot-container-MZUjCc`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.height-whole-100-S3_tjJ{height:100%;overflow:visible}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"height-whole-100\": `height-whole-100-S3_tjJ`,\n\t\"heightWhole100\": `height-whole-100-S3_tjJ`\n};\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.official-label-BF1CQ1{z-index:5;justify-content:center;align-items:center;display:flex;position:absolute;bottom:0;right:0;transform:translate(25%,25%)}.official-label-BF1CQ1.default-qFOcIG svg{width:20px;height:20px}.official-label-BF1CQ1.small-gGjycx svg{width:16px;height:16px}.official-label-BF1CQ1.large-XIRzQG svg{width:32px;height:32px}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"official-label\": `official-label-BF1CQ1`,\n\t\"officialLabel\": `official-label-BF1CQ1`,\n\t\"default\": `default-qFOcIG`,\n\t\"small\": `small-gGjycx`,\n\t\"large\": `large-XIRzQG`\n};\nexport default ___CSS_LOADER_EXPORT___;\n"], "names": ["DevelopCustomPublishStatus", "DevelopCustomTypeStatus", "TaskAction", "TaskType", "isRecentOpen", "val", "isSearchScopeEnum", "SearchScope", "getPublishRequestParam", "publishStatus", "getTypeRequestParams", "type", "allIntelligenceTypeParams", "IntelligenceType", "typeMap", "CREATOR_FILTER_OPTIONS", "STATUS_FILTER_OPTIONS", "TYPE_FILTER_OPTIONS", "FILTER_PARAMS_DEFAULT", "undefined", "isEqualDefaultFilterParams", "filterParams", "searchScope", "searchValue", "searchType", "isPublish", "recentlyOpen", "rest", "exhaustiveCheckForRecord", "isFilterHighlight", "currentFilterParams", "produceCopyIntelligenceData", "originTemplateData", "newCopyData", "userInfo", "getUserInfo", "userLabel", "getUserLabel", "produce", "draft", "ownerInfo", "basicInfo", "useCardActions", "isPersonalSpace", "mutate", "copyModalHolder", "onCopyProject", "useCopyProjectModal", "templateId", "prev", "target", "intelligence", "_intelligence_basic_info", "copyData", "mutateIntelligenceBasicInfo", "info", "i", "_i_basic_info", "onClick", "intelligenceData", "_intelligenceData_basic_info_space_id", "_intelligenceData_basic_info1", "_intelligenceData_basic_info2", "sendTeaEvent", "EVENT_NAMES", "onCopyAgent", "param", "merge", "onUpdate", "_prev_list", "idx", "item", "_item_basic_info", "_intelligenceData_basic_info", "clonedList", "cloneDeep", "onDeleteMutate", "id", "getIntelligenceList", "dataSource", "cancelTokenRef", "_resp_data_intelligences", "spaceId", "types", "hasPublished", "orderBy", "source", "axios", "resp", "intelligenceApi", "IntelligenceStatus", "e", "Toast", "withSlardarIdButton", "I18n", "Boolean", "buildBotLogger", "logger", "getBotListReportEvent", "createReportEvent", "ReportEventNames", "useIntelligenceList", "onBefore", "onSuccess", "onError", "containerRef", "useRef", "listResp", "useInfiniteScroll", "_cancelTokenRef_current", "res", "useEffect", "showCreateSuccessToast", "useIntelligenceActions", "mutateList", "reloadList", "extraGuideButtonConfigs", "navigate", "useNavigate", "navigateToProjectIDE", "inputProjectId", "createModalContextHolder", "createIntelligence", "useCreateProjectModal", "botId", "projectId", "handleDeleteIntelligenceAndMutate", "mutateDeleteId", "cozeMitt", "deleteModalContextHolder", "deleteIntelligence", "useDeleteIntelligence", "<PERSON><PERSON><PERSON><PERSON>", "projectParam", "useGlobalEventListeners", "reload", "handlerRefreshFavList", "refreshFavListParams", "handleReloadConditionally", "eventParam", "intelligenceCopyTaskPollingService", "IntelligenceCopyTaskPollingService", "params", "task", "p", "_response_data_entity_task_map", "taskList", "Object", "_", "finishPollList", "clearTimeout", "setTimeout", "prevLength", "uniqBy", "<PERSON><PERSON><PERSON><PERSON>", "mitt", "registerCopyTaskPolling", "data", "CopyTaskType", "useProjectCopyPolling", "listData", "onTaskUpdate", "list", "successToastId", "<PERSON><PERSON>", "_item_entity_id", "failedToastId", "response", "_response_data_entity_task", "isPersistentFilterParamsType", "isObject", "getDefaultFilterParams", "localFilterParams", "localStorageService", "parsedFilterParams", "safeJSONParse", "useCachedQueryParams", "setFilterParams", "useState", "useUpdateEffect", "JSON", "filters", "debouncedSetSearchValue", "useDebounceFn", "location", "name", "Typography", "MenuCopyBot", "disabled", "spaceID", "onCopySuccess", "onClose", "lock", "copyBot", "DeveloperApi", "bot_id", "newBotName", "user_info", "userId", "userName", "avatar_url", "user_unique_name", "user_label", "error", "CustomError", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IntelligenceTag", "intelligenceType", "Tag", "description", "CopyProcessMask", "intelligenceBasicInfo", "onRetry", "onCancelCopyAfterFailed", "status", "run", "useRequest", "action", "IconCozLoading", "IconCozWarningCircleFillPalette", "Space", "BotCard", "intelligenceInfo", "timePrefixType", "onDelete", "onUpdateIntelligenceInfo", "onRetryCopy", "extraMenu", "actionsMenuVisible", "headerExtra", "statusExtra", "basic_info", "in_collaboration", "can_delete", "publish_time", "connectors", "has_published", "recently_open_time", "owner_info", "is_fav", "icon_url", "space_id", "update_time", "hideOperation", "useSpaceStore", "store", "Error", "isBanned", "isAgent", "isProject", "timePrefix", "useMemo", "time", "timestamp", "formatDate", "Number", "getFormatDateType", "showActions", "setShowActions", "showMenu", "setShowMenu", "classNames", "Name", "IconCozWarningCircleFill", "ConnectorDynamicStatus", "IconCozCheckMarkCircleFillPalette", "Description", "Avatar", "Creator", "FavoriteIconBtn", "ProductEntityType", "isFav", "_clonedInfo_favorite_info", "clonedInfo", "IconButton", "IconCozMore", "IconCozStarFill", "changedStatus", "Develop", "_data_list", "isPersonal", "state", "SpaceType", "isIntelligenceTypeFilterHighlight", "isOwnerFilterHighlight", "isPublishAndOpenFilterHighlight", "loading", "loadingMore", "noMore", "search", "cardActionsContextHolder", "cardActions", "contextHolder", "actions", "Layout", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HeaderActions", "IconCozPlus", "SubHeader", "SubHeaderFilters", "Select", "highlightFilterStyle", "_TYPE_FILTER_OPTIONS_find", "opt", "_CREATOR_FILTER_OPTIONS_find", "_STATUS_FILTER_OPTIONS_find", "isPublishStatus", "statusList", "SubHeaderSearch", "Search", "Content", "Spin", "project", "index", "_project_basic_info", "_params_id", "_params_name", "_params_icon_url", "WorkspaceEmpty", "useParams", "useRevokeAuth", "onRevokeSuccess", "onRevokeFinally", "runAsync", "executeAuthRedirect", "_resp_data_state", "authInfo", "origin", "connector2Redirect", "checkAuthInfoValid", "isEmpty", "logAndToastAuthInfoError", "flatDataSource", "level", "setPCBodyWithDebugPanel", "bodyStyle", "document", "htmlStyle", "OLD_WX_FWH_ID", "STORE_CONNECTOR_ID", "getPublishResult", "publishResult", "connectInfoList", "_result_connector_share_link", "_result_connector_bind_info", "_publishResult_item_id", "_result_publish_result_status", "_result_msg", "result", "PublishResultStatus", "PluginAuthMode", "OfficialLabelSizeMap", "styles", "Official<PERSON><PERSON><PERSON>", "size", "children", "visible", "className", "IconOfficialLabel", "props", "dataInfo", "targetRef", "loadData", "threshold", "reloadDeps", "isNeedBtnLoadMore", "resetDataIfReload", "isLoadingError", "setIsLoadingError", "refFetchNo", "refResolve", "loadMore", "cancel", "current", "fetchNo", "Promise", "resolve", "reject", "value", "err", "_dataInfo_list_length", "insertData", "removeData", "getDataList", "reloadData", "useMemoizedFn", "_dataInfo_list", "isLoading", "loadMoreDebounce", "resize", "window", "loadRetry", "isError", "renderFooter", "isResponsive", "useIsResponsive", "s", "UIButton", "isSearching", "renderEmpty", "text", "btn", "icon", "UIEmpty", "IllustrationFailure", "InfiniteList", "forwardRef", "ref", "emptyContent", "grid", "renderItem", "itemClassName", "scrollConf", "emptyConf", "onChangeState", "canShowData", "retryFunc", "responsiveConf", "containerClassName", "dataList", "useScroll", "useImperativeHandle", "cls", "ResponsiveList", "number", "Footer", "List", "Empty", "ConnectorList", "visibleNum", "moreNum", "CozAvatar", "useFavoriteStatusRequest", "productId", "entityType", "entityId", "topicId", "onChange", "setIsFavorite", "changeFavoriteStatus", "isCurFavorite", "ProductApi", "_err", "useAnimationChange", "isVisible", "isShowAni", "setIsShowAni", "changeAnimationStatus", "getClickAction", "isCurFavoriteStatus", "useFavoriteChange", "isFavoriteDefault", "onReportTea", "onClickBefore", "onFavoriteStateChange", "isFavorite", "refIsChange", "onClickBeforeHandle", "event", "useCallback", "console", "FavoriteIconMobile", "IconMobileCollectFill", "IconMobileCollect", "FavoriteIcon", "unCollectedIconCls", "useButton", "iconProps", "IconCollectFilled", "IconCollectStroked", "isMobile", "isForbiddenClick", "SubMenuItem", "title", "activeIcon", "isActive", "suffix", "TYPE_ICON_MAP", "IconCozBot", "IconCozWorkflow", "IconCozWorkspace", "TYPE_COLOR_MAP", "CardTag", "_TYPE_COLOR_MAP_type", "config", "CardInfo", "_userInfo_user_label", "_userInfo_user_label1", "_userInfo_user_label2", "renderCardTag", "descClassName", "renderDescBottomSlot", "AvatarName", "CardContainer", "shadowMode", "CardSkeletonContainer", "CardButton", "TemplateCard", "_props_meta_info", "_props_meta_info1", "_props_meta_info2", "_props_meta_info_covers", "_props_meta_info3", "setVisible", "TempCardBody", "DuplicateModal", "_spaces_", "defaultTitle", "hide", "setTitle", "spaces", "useSpaceList", "Modal", "explore", "Input", "TemplateCardSkeleton", "imgSrc", "renderImageBottomSlot", "Image", "PluginCard", "PluginCardBody", "PluginCardSkeleton", "avatar", "extra", "restProps", "onClear", "<PERSON><PERSON><PERSON>er", "IconCozEmpty", "IconCozBroom", "options", "___CSS_LOADER_EXPORT___", "module"], "mappings": "gKAqBYA,EAMAC,E,ECsDAC,EAKAC,E,yIDjEL,IAAKH,G,CAAAA,E,yEAAAA,GAML,IAAKC,G,CAAAA,E,0GAAAA,G,cEKCG,EAAe,AAACC,GAAiBA,AAAQ,iBAARA,EAEjCC,EAAoB,AAACD,GAChCA,IAAQE,EAAAA,EAAAA,CAAAA,GAAe,EAAIF,IAAQE,EAAAA,EAAAA,CAAAA,UAAsB,C,aCf9CC,EAAyB,AACpCC,IAEA,GAA6B,SAAlBA,GAGPA,IAAkBT,EAAAA,GAA8B,CAGpD,OAAOS,IAAkBT,EAAAA,OAAkC,AAC7D,EAQaU,EAAuB,AAAC,I,GAAA,CACnCC,KAAAA,CAAI,CAGL,GACOC,EAA4B,CAChCC,EAAAA,EAAAA,CAAAA,GAAoB,CACpBA,EAAAA,EAAAA,CAAAA,OAAwB,CACzB,CAUD,MAAOC,AAT8D,EACnE,CAACb,EAAAA,GAA2B,CAAC,CAAEW,EAC/B,CAACX,EAAAA,KAA6B,CAAC,CAAE,CAACY,EAAAA,EAAAA,CAAAA,GAAoB,CAAC,CACvD,CAACZ,EAAAA,OAA+B,CAAC,CAAE,CAACY,EAAAA,EAAAA,CAAAA,OAAwB,CAAC,CAC7D,CAACZ,EAAAA,eAAuC,CAAC,CAAE,CACzCY,EAAAA,EAAAA,CAAAA,eAAgC,CACjC,AACH,EAEc,CAACF,EAAK,EAAI,EAAE,AAC5B,E,4BClCaI,EAAyB,CACpC,CACE,MAAOR,EAAAA,EAAAA,CAAAA,GAAe,CACtB,aAAc,eAChB,EACA,CACE,MAAOA,EAAAA,EAAAA,CAAAA,UAAsB,CAC7B,aAAc,eAChB,EACD,CAEYS,EAAwB,CACnC,CACE,MAAOhB,EAAAA,GAA8B,CACrC,aAAc,YAChB,EACA,CACE,MAAOA,EAAAA,OAAkC,CACzC,aAAc,aAChB,EACA,CACE,MAAO,eACP,aAAc,8BAChB,EACD,CAEYiB,EAAsB,CACjC,CACE,MAAOhB,EAAAA,GAA2B,CAClC,aAAc,0BAChB,EACA,CACE,MAAOA,EAAAA,OAA+B,CACtC,aAAc,wBAChB,EACA,CACE,MAAOA,EAAAA,KAA6B,CACpC,aAAc,sBAChB,EACD,CAEYiB,EAA0C,CACrD,YAAaX,EAAAA,EAAAA,CAAAA,GAAe,CAC5B,YAAa,GACb,UAAWP,EAAAA,GAA8B,CACzC,WAAYC,EAAAA,GAA2B,CACvC,aAAckB,KAAAA,CAChB,EChDaC,EAA6B,AAAC,I,GAAA,CACzCC,aAAAA,CAAY,CAGb,GACO,CACJC,YAAAA,CAAW,CACXC,YAAAA,CAAW,CACXC,WAAAA,CAAU,CACVC,UAAAA,CAAS,CACTC,aAAAA,CAAY,CAEb,CAAGL,EADCM,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EACDN,EAAAA,CANFC,cACAC,cACAC,aACAC,YACAC,e,EAIF,MADAE,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAyBD,GAEvBL,IAAgBJ,EAAsB,WAAW,EACjDM,IAAeN,EAAsB,UAAU,EAC/CO,IAAcP,EAAsB,SAAS,EAC7CQ,IAAiBR,EAAsB,YAAY,EACnD,CAACK,CAEL,EAEaM,EAAoB,AAACC,IAChC,GAAM,CACJP,YAAAA,CAAW,CACXD,YAAAA,CAAW,CACXG,UAAAA,CAAS,CACTD,WAAAA,CAAU,CACVE,aAAAA,CAAY,CAEb,CAAGI,EADCH,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EACDG,EAAAA,CANFP,cACAD,cACAG,YACAD,aACAE,e,EAIF,MADAE,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAyBD,GAClB,CACL,kCACEH,IAAevB,EAAAA,GAA2B,CAC5C,uBAAwBqB,IAAgBf,EAAAA,EAAAA,CAAAA,GAAe,CACvD,gCAAiCkB,GAAaC,CAChD,CACF,E,kFCrCaK,EAA8B,AAAC,I,GAAA,CAC1CC,mBAAAA,CAAkB,CAClBC,YAAAA,CAAW,CAOZ,GAEOC,EAAWC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IACXC,EAAYC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAClB,MAAOC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAA0BN,EAAoBO,AAAAA,IACnD,GAAM,CAAE5B,KAAAA,CAAI,CAAE,CAAG4B,EACX,CAAEC,UAAAA,CAAS,CAAEC,UAAAA,CAAS,CAAE,CAAGR,EACjC,MAAO,CACLtB,KAAAA,EACA,WAAY6B,GAAa,CACvB,QAASN,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,WAAW,CAC9B,SAAUA,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,IAAI,CACxB,WAAYA,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,UAAU,CAChC,iBAAkBA,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,aAAa,CAAC,gBAAgB,CAC1D,WAAYE,GAAajB,KAAAA,CAC3B,EACA,WAAYsB,EACZ,gBAAiB,CACf,iBAAkB,GAClB,WAAY,GACZ,SAAU,EACZ,CACF,CACF,EACF,EC1BaC,EAAiB,AAAC,I,GAAA,CAC7BC,gBAAAA,CAAe,CACfC,OAAAA,CAAM,CAIP,GACO,CAAE,mBAAoBC,CAAe,CAAE,UAAWC,CAAa,CAAE,CACrEC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAoB,CAClB,UAAW,AAAC,I,GAAA,CAAEN,UAAAA,CAAS,CAAEO,WAAAA,CAAU,CAAER,UAAAA,CAAS,CAAE,GAC9CI,EAAOK,AAAAA,GACLX,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAQW,EAAMV,AAAAA,IACZ,IAAMW,EAASX,MAAAA,EAAAA,KAAAA,EAAAA,EAAO,IAAI,CAAC,IAAI,CAC7BY,AAAAA,I,IAAgBC,E,MAAAA,AAAAA,CAAuB,OAAvBA,CAAAA,EAAAA,EAAa,UAAU,AAAD,GAAtBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyB,EAAE,AAAD,IAAMJ,C,GAGlD,IAAI,CAACE,GAIL,IAAMG,EAAWtB,EAA4B,CAC3C,mBAAoBmB,EACpB,YAAa,CAAET,UAAAA,EAAWD,UAAAA,CAAU,CACtC,EACAD,OAAAA,GAAAA,EAAO,IAAI,CAAC,OAAO,CAACc,GACtB,GAEJ,CACF,GAEIC,EAA8B,AAACC,IACnCX,EAAOK,AAAAA,GACLX,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAQW,EAAMV,AAAAA,IACZ,IAAMW,EAASX,MAAAA,EAAAA,KAAAA,EAAAA,EAAO,IAAI,CAAC,IAAI,CAACiB,AAAAA,I,IAAKC,E,MAAAA,AAAAA,CAAY,OAAZA,CAAAA,EAAAA,EAAE,UAAU,AAAD,GAAXA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAc,EAAE,AAAD,IAAMF,EAAK,EAAE,A,GACjE,IAAI,CAACL,EAGLA,EAAO,UAAU,CAAGK,CACtB,GAEJ,EAwEA,MAAO,CACL,cAAe,sB,SAAGV,C,GAClB,QAAS,CACPa,QAfY,AAACC,QAEHC,EAINC,EACEC,EALIF,EADZG,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,sBAAkC,CAAE,CAC/C,SAAUJ,AAAqC,OAArCA,CAAAA,EAAAA,AAA2B,OAA3BA,CAAAA,EAAAA,EAAiB,UAAU,AAAD,GAA1BA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA6B,QAAQ,AAAD,GAApCA,AAAAA,KAAAA,IAAAA,EAAAA,EAAyC,GACnD,WAAYjB,EAAkB,WAAa,YAC3C,SAAU,UACV,OAAQ,QACR,GAAI,AAA2B,OAA3BkB,CAAAA,EAAAA,EAAiB,UAAU,AAAD,GAA1BA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA6B,EAAE,CACnC,KAAM,AAA2B,OAA3BC,CAAAA,EAAAA,EAAiB,UAAU,AAAD,GAA1BA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA6B,IAAI,CACvC,KAAM,OACR,EACF,EAMIhB,cAAAA,EACAmB,YA3E0CC,AAAAA,IAC5CtB,EAAOK,AAAAA,GACLX,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAQW,EAAMV,AAAAA,IACZ,IAAMW,EAASX,MAAAA,EAAAA,KAAAA,EAAAA,EAAO,IAAI,CAAC,IAAI,CAC7BY,AAAAA,I,IAAgBC,E,MAAAA,AAAAA,CAAuB,OAAvBA,CAAAA,EAAAA,EAAa,UAAU,AAAD,GAAtBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyB,EAAE,AAAD,IAAMc,EAAM,UAAU,A,GAGlE,IAAI,CAAChB,GAIL,IAAMG,EAAWtB,EAA4B,CAC3C,mBAAoBmB,EACpB,YAAa,CACX,UAAWgB,EAAM,SAAS,CAC1B,UAAWC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAM,CAAC,EAAGjB,EAAO,UAAU,CAAE,CACtC,GAAIgB,EAAM,EAAE,CACZ,KAAMA,EAAM,IAAI,AAClB,EACF,CACF,EACA3B,OAAAA,GAAAA,EAAO,IAAI,CAAC,OAAO,CAACc,GACtB,GAEJ,EAoDIe,SAvCa,AAACT,IAChBf,EAAOK,AAAAA,IACL,GAAI,CAACA,EACH,OAEF,IAO6BoB,EAPvBC,EAAMrB,EAAK,IAAI,CAAC,SAAS,CAC7BsB,AAAAA,I,IAAQC,EAAwBC,E,MAAxBD,AAAAA,CAAe,OAAfA,CAAAA,EAAAA,EAAK,UAAU,AAAD,GAAdA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiB,EAAE,AAAD,IAAC,CAAgC,OAA3BC,CAAAA,EAAAA,EAAiB,UAAU,AAAD,GAA1BA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA6B,EAAE,AAAD,C,GAGhE,IAAIH,CAAAA,EAAM,IAGV,IAAMI,EAAaC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUN,AAAU,OAAVA,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,IAAI,AAAD,GAATA,AAAAA,KAAAA,IAAAA,EAAAA,EAAc,EAAE,EAE7C,OADAK,EAAW,MAAM,CAACJ,EAAK,EAAGX,GACnB,mBACFV,GAAAA,CACH,KAAMyB,C,GAEV,EACF,EAqBI,YAAapB,EACb,wBAAyBA,EACzBsB,eArDmB,AAAC,I,GAAA,CAAEC,GAAAA,CAAE,CAAkB,GAC5CjC,EAAOK,AAAAA,GACLX,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAQW,EAAMV,AAAAA,IACZ,IAAI,CAACA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAO,IAAI,AAAD,EAGfA,EAAM,IAAI,CAAGA,EAAM,IAAI,CAAC,MAAM,CAACgC,AAAAA,I,IAAQC,E,MAAAA,AAAAA,CAAe,OAAfA,CAAAA,EAAAA,EAAK,UAAU,AAAD,GAAdA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiB,EAAE,AAAD,IAAMK,C,EACjE,GAEJ,CA6CE,CACF,CACF,E,8HCxGA,IAAMC,G,EAAsB,kBAC1BC,CAAU,CAAVA,CAAAA,CAUAC,CAAc,E,IAqCJC,EA9CV,CACEC,QAAAA,CAAO,CACPC,MAAAA,CAAK,CACL5D,YAAAA,CAAW,CACX6D,aAAAA,CAAY,CACZ1D,aAAAA,CAAY,CACZJ,YAAAA,CAAW,CACX+D,QAAAA,CAAO,CACU,GAIbC,EAASC,EAAAA,CAAAA,CAAAA,WAAAA,CAAAA,MAAwB,EACvCP,CAAAA,EAAe,OAAO,CAAGM,EACzB,IAAME,EAAO,MAAMC,EAAAA,EAAAA,CAAAA,wBACQ,CACvB,CACE,SAAUP,EACV,KAAM3D,EACN4D,MAAAA,EACA,KAlCS,GAmCT,cAAeC,EACf,cAAe1D,EACf,UAAWqD,MAAAA,EAAAA,KAAAA,EAAAA,EAAY,YAAY,CACnC,aAAczD,EAEd,SAAU+D,EACV,OAAQ,CACNK,EAAAA,EAAAA,CAAAA,KAAwB,CACxBA,EAAAA,EAAAA,CAAAA,MAAyB,CACzBA,EAAAA,EAAAA,CAAAA,UAA6B,CAC9B,AACH,EACA,CAAE,YAAaJ,EAAO,KAAK,CAAE,oBAAqB,EAAK,GAExD,KAAK,CAACK,AAAAA,IACa,aAAdA,EAAE,OAAO,EACXC,EAAAA,EAAAA,CAAAA,KAAW,CAAC,CACV,QAASC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAoBF,EAAE,GAAG,EAAIA,EAAE,OAAO,EAAIG,EAAAA,CAAAA,CAAAA,CAAM,CAAC,UAC1D,UAAW,EACb,EAEJ,SAEF,AAAIN,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,IAAI,AAAD,EACJ,CACL,KAAMP,AAAuB,OAAvBA,CAAAA,EAAAA,EAAK,IAAI,CAAC,aAAa,AAAD,GAAtBA,AAAAA,KAAAA,IAAAA,EAAAA,EAA2B,EAAE,CACnC,QAASc,CAAAA,CAAQP,EAAK,IAAI,CAAC,QAAQ,CACnC,aAAcA,EAAK,IAAI,CAAC,cAAc,AACxC,EAEO,CACL,KAAM,EAAE,CACR,QAAS,GACT,aAAcrE,KAAAA,CAChB,CAEJ,G,SA1DE4D,CAAU,CAAVA,CAAAA,CAUAC,CAAc,E,iCAkDVgB,EAAiBC,EAAAA,EAAAA,CAAAA,gBAAuB,CAAC,CAC7C,IAAK,CACH,UAAW,UACb,CACF,GAEMC,GAAwBC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAkB,CAC9C,UAAWC,EAAAA,CAAAA,CAAAA,UAA2B,CACtC,OAAQJ,CACV,GAEaK,GAAsB,AAAC,I,MAAA,CAClC,OAAQ,CACNnB,QAAAA,CAAO,CACPC,MAAAA,CAAK,CACL5D,YAAAA,CAAW,CACX6D,aAAAA,CAAY,CACZ1D,aAAAA,CAAY,CACZJ,YAAAA,CAAW,CACX+D,QAAAA,CAAO,CACR,CACDiB,SAAAA,CAAQ,CACRC,UAAAA,CAAS,CACTC,QAAAA,CAAO,CAMR,GACOC,EAAeC,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,EAAuB,MACtC1B,EAAiB0B,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,EAAiC,MAExD,IAAMC,EAAWC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,G,EACf,kBAAM7B,CAAU,EACd,aAAMD,EACJC,EACA,CACEG,QAAAA,EACAC,MAAAA,EACA5D,YAAAA,EACA6D,aAAAA,EACA1D,aAAAA,EACAJ,YAAAA,EACA+D,QAAAA,CACF,EACAL,E,YAZED,CAAU,E,iCAchB,CACE,OAAQ0B,EACR,WAAY,CACVtB,EAAM,IAAI,CAAC,KACX5D,EACA6D,EACA1D,EACAJ,EACA+D,EACAH,EACD,CACD,SAAUH,AAAAA,GAAc,CAACA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAY,OAAO,AAAD,EAC3C,SAAU,KACR,GAAI4B,EAAS,WAAW,EAAIA,EAAS,OAAO,CAAE,C,IAC5CE,C,AAAsB,QAAtBA,CAAAA,EAAAA,EAAe,OAAO,AAAD,GAArBA,AAAAA,KAAAA,IAAAA,GAAAA,EAAwB,MAAM,EAChC,CACAX,GAAsB,KAAK,GAC3BI,MAAAA,GAAAA,GACF,EACA,UAAW,W,2BAAIQ,EAAAA,AAAAA,MAAAA,GAAAA,EAAAA,EAAAA,EAAAA,EAAAA,IAAAA,CAAG,CAAHA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CACbZ,GAAsB,OAAO,GAC7BK,MAAAA,GAAAA,KAAeO,EACjB,EACA,QAASnB,AAAAA,IACPO,GAAsB,KAAK,CAAC,CAC1B,MAAOP,EACP,OAAQA,EAAE,OAAO,AACnB,GACAa,MAAAA,GAAAA,EAAUb,EACZ,CACF,GAWF,MARAoB,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EACE,IAAM,K,IAEJF,C,AAAsB,QAAtBA,CAAAA,EAAAA,EAAe,OAAO,AAAD,GAArBA,AAAAA,KAAAA,IAAAA,GAAAA,EAAwB,MAAM,EAChC,EACA,CAAC3B,EAAQ,EAGJ,CAAEyB,SAAAA,EAAUF,aAAAA,EAAczB,eAAAA,CAAe,CAClD,E,eC3KMgC,GAAyB,KAC7BpB,EAAAA,EAAAA,CAAAA,OAAa,CAAC,CACZ,QAASE,EAAAA,CAAAA,CAAAA,CAAM,CAAC,+BAChB,UAAW,EACb,EACF,EAEamB,GAAyB,AAAC,I,GAAA,CACrC/B,QAAAA,CAAO,CACPgC,WAAAA,CAAU,CACVC,WAAAA,CAAU,CACVC,wBAAAA,CAAuB,CAMxB,GAOOC,EAAWC,AAAAA,GAAAA,EAAAA,WAAAA,AAAAA,IAEXC,EAAuB,AAACC,GAC5BH,EAAU,UAAgCG,MAAAA,CAAvBtC,EAAQ,iBAA8B,OAAfsC,IAEtC,CACJ,mBAAoBC,CAAwB,CAC5C,cAAeC,CAAkB,CAClC,CAAGC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAsB,CACxB,YAAa,GACb,cAAe,QACf,eAAgBzC,EAChBkC,wBAAAA,EACA,mBAAoBQ,AAAAA,IACdA,GACFP,EAAU,UAAwBO,MAAAA,CAAf1C,EAAQ,SAAa,OAAN0C,GAEtC,EACA,uBAAwB,AAAC,I,GAAA,CAAEC,UAAAA,CAAS,CAAE,GACpCb,KACAO,EAAqBM,EACvB,EACA,6BAA8B,KAC5BV,GACF,CACF,GAEMW,EAAoC,AAACC,IACzCnC,EAAAA,EAAAA,CAAAA,OAAa,CAAC,CACZ,QAASE,EAAAA,CAAAA,CAAAA,CAAM,CAAC,oCAChB,UAAW,EACb,GACAkC,GAAAA,CAAAA,CAAAA,IAAa,CAAC,iBAAkB,CAAE,GAAID,EAAgB,SAAU,EAAG,GACnEb,EAAWjE,AAAAA,GACTA,EACI,mBACKA,GAAAA,CACH,KAAMA,EAAK,IAAI,CAAC,MAAM,CACpBsB,AAAAA,I,IAAQC,E,MAAAA,AAAAA,CAAe,OAAfA,CAAAA,EAAAA,EAAK,UAAU,AAAD,GAAdA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiB,EAAE,AAAD,IAAMuD,C,KAGpC5G,KAAAA,EAER,EAEM,CAAE,mBAAoB8G,CAAwB,CAAEC,mBAAAA,CAAkB,CAAE,CACxEC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAsB,CACpB,qBAAsBC,AAAAA,IACpBN,EAAkCM,EAAW,OAAO,CACtD,EACA,uBAAwBC,AAAAA,IACtBP,EAAkCO,EAAa,SAAS,CAC1D,CACF,GAEF,MAAO,CACL,cACE,uB,UACGZ,EACAQ,E,GAGL,QAAS,CACPP,mBAAAA,EACAQ,mBAAAA,CACF,CACF,CACF,EClGaI,GAA0B,AAAC,I,GAAA,CACtCC,OAAAA,CAAM,CACNrD,QAAAA,CAAO,CAIR,GACC6B,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACR,IAAMyB,EAAwB,AAC5BC,IAG0C,wBAAtCA,EAAqB,YAAY,EACnCF,GAEJ,EACMG,EAA4B,AAChCC,IAEA,GAAIA,EAAW,SAAS,GAAKzD,EAG7BqD,GACF,EAMA,OALAP,GAAAA,CAAAA,CAAAA,EAAW,CAAC,iBAAkBQ,GAC9BR,GAAAA,CAAAA,CAAAA,EAAW,CACT,yCACAU,GAEK,KACLV,GAAAA,CAAAA,CAAAA,GAAY,CAAC,iBAAkBQ,GAC/BR,GAAAA,CAAAA,CAAAA,GAAY,CACV,yCACAU,EAEJ,CACF,EAAG,EAAE,CACP,EToBO,IAAKxI,I,CAAAA,E,0FAAAA,GAKL,IAAKC,I,CAAAA,E,4DAAAA,G,8BU8BCyI,GACX,IAvFK,MAAMC,EAQX,aAAc,C,gBAPL,cAAc,CAAG,I,KACjB,WAAW,CAAG,I,KACvB,QAAQ,CAAiB,EAAE,C,KAC3B,OAAO,CAAG,IAAI,CAAC,cAAc,C,KAC7B,OAAO,CAAyC,K,KAOhD,cAAc,CAAG,AAACC,IAChB,IAAI,CAAC,QAAQ,CAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAClCC,AAAAA,GAAQ,CAACD,EAAO,IAAI,CAACE,AAAAA,GAAKA,EAAE,SAAS,GAAKD,EAAK,SAAS,EAE5D,E,KAEA,IAAI,CAAJ,QAAO,YAKL,IADgBE,EAAAA,EACVC,EAAWC,OAAO,OAAO,CADfF,AAA8B,OAA9BA,CAAAA,EAAAA,AAAa,OAAbA,CAAAA,EAAAA,AAHC,OAAMxD,EAAAA,EAAAA,CAAAA,gBAAgC,CAAC,CACtD,UAAW,EAAK,QAAQ,AAC1B,EAAC,EACwB,IAAI,AAAD,GAAZwD,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAe,eAAe,AAAD,GAA7BA,AAAAA,KAAAA,IAAAA,EAAAA,EAAkC,CAAC,GACV,GAAG,CAAC,AAAC,I,GAAA,CAACG,EAAGL,EAAK,G,OAAKA,C,GAEtDM,EAAiBH,EAAS,MAAM,CACpCH,AAAAA,GAAQA,EAAK,aAAa,GAAKrD,EAAAA,EAAAA,CAAAA,OAA0B,EAG3D,EAAK,cAAc,CAAC2D,GAEpB,EAAK,WAAW,CAAC,IAAI,CAAC,mBAAoBH,EAC5C,G,KAEA,YAAY,CAAG,KACb,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,cAAc,AACpC,E,KAEA,eAAe,CAAG,KAChB,IAAI,CAAC,OAAO,EAAI,IAAI,CAAC,WAAW,AAClC,E,KAEA,mBAAmB,CAAG,IAAMnD,CAAAA,CAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,C,KAExD,UAAU,CAAG,KACX,IAAI,CAAC,IAAI,CAAC,OAAO,CAGjBuD,aAAa,IAAI,CAAC,OAAO,CAC3B,E,KAEA,GAAG,CAAG,K,UACJ,KAAI,CAAC,OAAO,CAAGC,WAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAW,YAExB,GADA,MAAM,EAAK,IAAI,IACX,CAAC,EAAK,mBAAmB,GAG7B,EAAK,eAAe,GACpB,EAAK,GAAG,EACV,GAAG,IAAI,CAAC,OAAO,CACjB,E,KAEA,eAAe,CAAG,AAACT,IACjB,IAAMU,EAAa,IAAI,CAAC,QAAQ,CAAC,MAAM,AAEvC,KAAI,CAAC,QAAQ,CAAGC,AAAAA,GAAAA,GAAAA,CAAAA,AAAAA,EACd,IAAI,CAAC,QAAQ,CAAC,MAAM,CAACX,GACrBC,AAAAA,GAAQA,EAAK,SAAS,EAGxB,IAAMW,EAAgB,IAAI,CAAC,QAAQ,CAAC,MAAM,AAEtC,EAACF,GAAcE,IACjB,IAAI,CAAC,YAAY,GACjB,IAAI,CAAC,GAAG,GAEZ,E,KAEA,QAAQ,CAAG,KACT,IAAI,CAAC,UAAU,GACf,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,oBACrB,IAAI,CAAC,QAAQ,CAAG,EAAE,CAClB,IAAI,CAAC,OAAO,CAAG,IACjB,EA1EE,IAAI,CAAC,WAAW,CAAGC,AAAAA,GAAAA,GAAAA,CAAAA,AAAAA,GACrB,CA0EF,EC7EMC,GAA0B,AAACC,IAC/BjB,GAAmC,eAAe,CAChDiB,EACG,MAAM,CACL1G,AAAAA,I,IAEEC,E,OADAD,EAAa,IAAI,GAAKtC,EAAAA,EAAAA,CAAAA,OAAwB,EAC9CuC,AAAAA,CAAuB,OAAvBA,CAAAA,EAAAA,EAAa,UAAU,AAAD,GAAtBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyB,MAAM,AAAD,IAAMsC,EAAAA,EAAAA,CAAAA,OAA0B,A,GAEjE,GAAG,CAAClC,AAAAA,I,IACQC,E,MADF,CACT,UAAW,AAAY,OAAZA,CAAAA,EAAAA,EAAE,UAAU,AAAD,GAAXA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAc,EAAE,CAC3B,UAAWqG,GAAAA,WAAwB,AACrC,C,GAEN,EAEaC,GAAwB,AAAC,I,GAAA,CACpC7E,QAAAA,CAAO,CACP8E,SAAAA,CAAQ,CACRpH,OAAAA,CAAM,CAKP,GACOyE,EAAWC,AAAAA,GAAAA,EAAAA,WAAAA,AAAAA,IACXC,EAAuB,AAACC,GAC5BH,EAAU,UAAgCG,MAAAA,CAAvBtC,EAAQ,iBAA8B,OAAfsC,IAE5CT,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACJiD,GACFJ,GAAwBI,EAE5B,EAAG,CAACA,EAAS,EAEbjD,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACR,IAAMkD,EAAe,AAACC,IACpBtH,EAAOK,AAAAA,GACLX,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAQW,EAAMV,AAAAA,IACZ2H,EAAK,OAAO,CAACnB,AAAAA,IACX,IAAM7F,EAASX,MAAAA,EAAAA,KAAAA,EAAAA,EAAO,IAAI,CAAC,IAAI,CAC7BY,AAAAA,I,IAAgBC,E,MAAAA,AAAAA,CAAuB,OAAvBA,CAAAA,EAAAA,EAAa,UAAU,AAAD,GAAtBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyB,EAAE,AAAD,IAAM2F,EAAK,SAAS,A,GAEhE,GAAI,EAAC7F,IAAU,CAACA,EAAO,UAAU,CAGjCA,EAAO,UAAU,CAAC,MAAM,CAAG6F,EAAK,aAAa,AAC/C,EACF,IAGFmB,EAAK,OAAO,CAAC3F,AAAAA,IACX,GAAIA,EAAK,aAAa,GAAKmB,EAAAA,EAAAA,CAAAA,KAAwB,CAAE,CACnD,IAAMyE,EAAiBvE,EAAAA,EAAAA,CAAAA,OAAa,CAAC,CACnC,QACE,uB,UACGE,EAAAA,CAAAA,CAAAA,CAAM,CAAC,uCACR,UAACsE,EAAAA,EAAMA,CAAAA,CACL,UAAU,SACV,MAAM,UACN,QAAS,SAEcC,EADrBzE,EAAAA,EAAAA,CAAAA,KAAW,CAACuE,GACZ5C,EAAqB8C,AAAc,OAAdA,CAAAA,EAAAA,EAAK,SAAS,AAAD,GAAbA,AAAAA,KAAAA,IAAAA,EAAAA,EAAkB,GACzC,E,SAECvE,EAAAA,CAAAA,CAAAA,CAAM,CAAC,mC,MAId,UAAW,EACb,GACA,MACF,CACA,GAAIvB,EAAK,aAAa,GAAKmB,EAAAA,EAAAA,CAAAA,UAA6B,CAAE,CACxD,IAAM4E,EAAgB1E,EAAAA,EAAAA,CAAAA,KAAW,CAAC,CAChC,QACE,uB,UACGE,EAAAA,CAAAA,CAAAA,CAAM,CAAC,oCACR,UAACsE,EAAAA,EAAMA,CAAAA,CACL,UAAU,SACV,MAAM,UACN,QAAO,QAAE,YACPxE,EAAAA,EAAAA,CAAAA,KAAW,CAAC0E,GACZ,IAAMC,EAAW,MAAM9E,EAAAA,EAAAA,CAAAA,iBAAiC,CAAC,CACvD,UAAWlB,EAAK,SAAS,CACzB,OAAQrE,GAAAA,gBAA2B,AACrC,GACA0C,EAAOK,AAAAA,GACLX,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAQW,EAAMV,AAAAA,IACZ,IASEiI,EAAAA,EATItH,EAASX,MAAAA,EAAAA,KAAAA,EAAAA,EAAO,IAAI,CAAC,IAAI,CAC7BY,AAAAA,I,IACEC,E,MAAAA,AAAAA,CAAuB,OAAvBA,CAAAA,EAAAA,EAAa,UAAU,AAAD,GAAtBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyB,EAAE,AAAD,IAAMmB,EAAK,SAAS,A,GAGlD,GAAI,EAACrB,IAAU,CAACA,EAAO,UAAU,CAGjCA,EAAO,UAAU,CAAC,MAAM,CACT,OAAbsH,CAAAA,EAAAA,EAAS,IAAI,AAAD,GAAZA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,AAA0B,OAA1BA,CAAAA,EAAAA,EAAe,WAAW,AAAD,GAAzBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA4B,aAAa,AAC7C,GAEJ,G,SAEC1E,EAAAA,CAAAA,CAAAA,CAAM,CAAC,yC,MAId,UAAW,EACb,GACA,MACF,CACF,EACF,EAKA,OAJA8C,GAAmC,WAAW,CAAC,EAAE,CAC/C,mBACAqB,GAEK,KACLrB,GAAmC,QAAQ,GAC3CA,GAAmC,WAAW,CAAC,GAAG,CAChD,mBACAqB,EAEJ,CACF,EAAG,EAAE,CACP,E,0ECtIMQ,GAA+B,AACnC3B,GACwC4B,AAAAA,GAAAA,GAAAA,CAAAA,AAAAA,EAAS5B,GAEnD,IAAM6B,I,EAAyB,oBAC7B,IAAMC,EAAoB,MAAMC,GAAAA,CAAAA,CAAAA,YAAgC,CAC9D,6BAEF,GAAI,CAACD,EACH,OAAO1J,EAET,IAAM4J,EAAqBC,AAAAA,GAAAA,GAAAA,EAAAA,AAAAA,EAAcH,UACzC,AAAIH,GAA6BK,GACxB3G,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAM,CAAC,EAAGjD,EAAuB4J,GAEnC5J,CACT,G,4CAEa8J,GAAuB,KAClC,GAAM,CAAC3J,EAAc4J,EAAgB,CAAGC,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EACtChK,GAGFiK,AAAAA,GAAAA,GAAAA,CAAAA,AAAAA,EAAgB,KAEd,GAAM,CAAE7J,YAAAA,CAAW,CAAEG,UAAAA,CAAS,CAAEC,aAAAA,CAAY,CAAEF,WAAAA,CAAU,CAAE,CAAGH,EAC7DwJ,GAAAA,CAAAA,CAAAA,QAA4B,CAC1B,4BACAO,KAAK,SAAS,CAAC,CACb9J,YAAAA,EACAG,UAAAA,EACAD,WAAAA,EACAE,aAAAA,CACF,GAEJ,EAAG,CAACL,EAAa,EAEjB0F,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KAER4D,KAAyB,IAAI,CAACU,AAAAA,IAC5BJ,EAAgBhI,AAAAA,GAAQkB,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAM,CAAC,EAAGlB,EAAMoI,GAC1C,EACF,EAAG,EAAE,EAEL,IAAMC,EAA0BC,AAAAA,GAAAA,GAAAA,CAAAA,AAAAA,EAC9B,W,IAAChK,EAAc,UAAdA,MAAAA,CAAAA,GAAAA,AAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAc,GACb0J,EAAgBnC,AAAAA,GAAW,mBACtBA,GAAAA,CACHvH,YAAAA,C,IAGFwC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,YAAwB,CAAE,CACrC,SAAUwH,SAAS,IAAI,CACvB,OAAQ,UACR,YAAajK,CACf,EACF,EACA,CACE,KAAM,GACR,GAGF,MAAO,CAACF,EAAc4J,EAAiBK,EAAwB,GAAG,CAAC,AACrE,E,wGC/CA,GAnB4B,AAAC,I,GAAA,CAAEG,KAAAA,CAAI,CAAE,G,MACnC,UAACC,EAAAA,EAAAA,CAAAA,IAAe,EACd,UAAU,wCACV,SAAU,CACR,YAAa,CACX,KAAM,CACJ,QAAS,UAAC,QAAK,QAAS/F,AAAAA,GAAKA,EAAE,eAAe,G,SAAK8F,C,GACnD,MAAO,CAAE,UAAW,YAAa,EACjC,MAAO,MACT,EACA,KAAM,SACR,EACA,KAAM,CACR,E,SAECA,C,mCCkCQE,GAAoC,AAAC,I,MAAA,CAChDC,SAAAA,CAAQ,CACR/G,GAAAA,CAAE,CACF4G,KAAAA,CAAI,CACJI,QAAAA,CAAO,CACPC,cAAAA,CAAa,CACbC,QAAAA,CAAO,CACR,GACOC,EAAOtF,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,EAAO,IAEpB,IAAMuF,G,EAAU,oBACd,GAAI,CACFD,EAAK,OAAO,CAAG,GACf,IAAMzB,EAAW,MAAM2B,EAAAA,EAAAA,CAAAA,iBAA8B,CAAC,CACpD,SAAUL,EACV,OAAQhH,CACV,GACAe,EAAAA,EAAAA,CAAAA,OAAa,CAAC,CACZ,QAASE,EAAAA,CAAAA,CAAAA,CAAM,CAAC,0BAChB,UAAW,EACb,GACA,GAAM,CACJqG,OAAAA,EAAS,EAAE,CACX,KAAMC,EAAa,EAAE,CACrBC,UAAAA,EAAY,CAAC,CAAC,CACf,CAAG9B,EAAS,IAAI,CACX,CACJ,GAAI+B,EAAS,EAAE,CACf,KAAMC,EAAW,EAAE,CACnBC,WAAAA,EAAa,EAAE,CACfC,iBAAAA,EAAmB,EAAE,CACrBC,WAAAA,EAAa,CAAC,CAAC,CAChB,CAAGL,CACJP,OAAAA,GAAAA,EAAgB,CACd,WAAYjH,EACZ,GAAIsH,EACJ,KAAMC,EACN,UAAW,CACT,QAASE,EACT,SAAUC,EACVC,WAAAA,EACAC,iBAAAA,EACAC,WAAAA,CACF,CACF,EACF,CAAE,MAAOC,EAAO,CACd1G,EAAAA,EAAAA,CAAAA,KAAY,CAAC,CACX,MAAO,IAAI2G,GAAAA,EAAWA,CAAC,WAAY,iBACrC,EACF,QAAU,CACRb,MAAAA,GAAAA,IACAC,EAAK,OAAO,CAAG,EACjB,CACF,G,4CAEA,MACE,UAACa,EAAAA,CAAOA,CAAAA,CACN,QAASjB,EAAW,SAAW,QAC/B,QAAS9F,EAAAA,CAAAA,CAAAA,CAAM,CAAC,uB,SAEhB,UAACgH,EAAAA,EAAAA,CAAAA,IAAS,EACR,cAAY,gBACZ,SAAUlB,EACV,QAAS,KACP,IAAII,EAAK,OAAO,CAGhBjI,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,mBAA+B,CAAE,CAC5C,SAAU,UACZ,GAEAD,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,yBAAqC,CAAE,CAClD,SAAU,WACV,OAAQa,EACR,SAAU4G,EACV,KAAM,YACN,OAAQ,WACV,GACAQ,GACF,E,SAECnG,EAAAA,CAAAA,CAAAA,CAAM,CAAC,Y,IAIhB,ECvIaiH,GAAkD,AAAC,I,GAAA,CAC9DC,iBAAAA,CAAgB,CACjB,UACC,AAAIA,IAAqBnM,EAAAA,EAAAA,CAAAA,OAAwB,CAE7C,UAACoM,EAAAA,EAAGA,CAAAA,CAAC,MAAM,QAAQ,KAAK,QAAQ,UAAU,Q,SACvCnH,EAAAA,CAAAA,CAAAA,CAAM,CAAC,gC,GAIVkH,IAAqBnM,EAAAA,EAAAA,CAAAA,GAAoB,CAEzC,UAACoM,EAAAA,EAAGA,CAAAA,CAAC,MAAM,UAAU,KAAK,QAAQ,UAAU,Q,SACzCnH,EAAAA,CAAAA,CAAAA,CAAM,CAAC,8B,GAIVkH,IAAqBnM,EAAAA,EAAAA,CAAAA,eAAgC,CAErD,UAACoM,EAAAA,EAAGA,CAAAA,CAAC,MAAM,MAAM,KAAK,QAAQ,UAAU,Q,SACR,M,GAK7B,IACT,ECCA,GA1B0C,AAAC,I,GAAA,CAAEC,YAAAA,CAAW,CAAE,G,MACxD,UAACxB,EAAAA,EAAAA,CAAAA,IAAe,EACd,UAAU,0DACV,SAAU,CACR,YAAa,CACX,KAAM,CACJ,MAAO,OACP,QACE,UAACA,EAAAA,EAAAA,CAAAA,IAAe,EACd,UAAU,qCACV,QAAS/F,AAAAA,GAAKA,EAAE,eAAe,GAC/B,SAAU,CAAE,YAAa,GAAO,KAAM,EAAG,E,SAExCuH,C,EAGP,EACA,KAAM,SACR,EACA,KAAM,CACR,E,SAECA,C,mBCVQC,GAAkD,AAAC,I,MAAA,CAC9DC,sBAAAA,CAAqB,CACrBC,QAAAA,CAAO,CACPC,wBAAAA,CAAuB,CACxB,GACO,CAAEC,OAAAA,CAAM,CAAE,CAAGH,EAEnB,GAAM,CAAEI,IAAAA,CAAG,CAAE,CAAGC,AAAAA,GAAAA,GAAAA,CAAAA,AAAAA,G,EACd,kBAAOC,CAAM,EACX,IAIOlD,EAAAA,EAAP,OAAO,AAAa,OAAbA,CAAAA,EAAAA,AAJU,OAAM/E,EAAAA,EAAAA,CAAAA,iBAAiC,CAAC,CACvD,UAAW2H,EAAsB,EAAE,CACnCM,OAAAA,CACF,EAAC,EACe,IAAI,AAAD,GAAZlD,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,AAA0B,OAA1BA,CAAAA,EAAAA,EAAe,WAAW,AAAD,GAAzBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA4B,aAAa,AAClD,G,SANOkD,CAAM,E,iCAOb,CACE,OAAQ,GACR,UAAW,CAAC5G,EAAK,K,GAAA,CAAC4G,EAAO,GACnBA,IAAWxN,GAAAA,iBAA4B,EACzCoN,CAAAA,MAAAA,GAAAA,EAA0BxG,EAAG,EAE3B4G,IAAWxN,GAAAA,gBAA2B,EACxCmN,CAAAA,MAAAA,GAAAA,EAAUvG,EAAG,CAEjB,CACF,UAGF,AACEyG,IAAW7H,EAAAA,EAAAA,CAAAA,UAA6B,EACxC6H,IAAW7H,EAAAA,EAAAA,CAAAA,OAA0B,CAE9B,KAIP,UAAC,OAAI,UAAU,sH,SACb,WAAC,OAAI,UAAU,2D,UACZ6H,IAAW7H,EAAAA,EAAAA,CAAAA,OAA0B,CACpC,uB,UACE,UAACiI,GAAAA,GAAcA,CAAAA,CAAC,UAAU,c,GAC1B,UAAC,O,SAAK7H,EAAAA,CAAAA,CAAAA,CAAM,CAAC,gC,MAEb,KACHyH,IAAW7H,EAAAA,EAAAA,CAAAA,UAA6B,CACvC,uB,UACE,UAACkI,GAAAA,GAA+BA,CAAAA,CAAC,UAAU,iB,GAC3C,UAAC,O,SAAK9H,EAAAA,CAAAA,CAAAA,CAAM,CAAC,8B,GACb,WAAC+H,EAAAA,CAAKA,CAAAA,CAAC,QAAS,E,UACd,UAACzD,EAAAA,EAAMA,CAAAA,CACL,MAAM,UACN,QAAS,KACPoD,EAAItN,GAAAA,iBAA4B,CAClC,E,SAEC4F,EAAAA,CAAAA,CAAAA,CAAM,CAAC,S,GAEV,UAACsE,EAAAA,EAAMA,CAAAA,CACL,MAAM,WACN,QAAS,KACPoD,EAAItN,GAAAA,gBAA2B,CACjC,E,SAEC4F,EAAAA,CAAAA,CAAAA,CAAM,CAAC,yC,SAIZ,K,IAIZ,EChCagI,GAAkC,AAAC,I,GAAA,CAC9CC,iBAAAA,CAAgB,CAChBC,eAAAA,CAAc,CACdtK,QAAAA,CAAO,CACPuK,SAAAA,CAAQ,CACRnL,cAAAA,CAAa,CACbmB,YAAAA,CAAW,CACXiK,yBAAAA,CAAwB,CACxBZ,wBAAAA,CAAuB,CACvBa,YAAAA,CAAW,CACXC,UAAAA,CAAS,CACTC,mBAAAA,EAAqB,EAAI,CACzBC,YAAAA,CAAW,CACXC,YAAAA,CAAW,CACZ,GACOlH,EAAWC,AAAAA,GAAAA,EAAAA,WAAAA,AAAAA,IAEX,CACJkH,WAAAA,CAAU,CACV7N,KAAAA,CAAI,CACJ,gBAAiB,CAAE8N,iBAAAA,CAAgB,CAAEC,WAAAA,CAAU,CAAE,CAAG,CAAC,CAAC,CACtD,aAAc,CAAEC,aAAAA,CAAY,CAAEC,WAAAA,CAAU,CAAEC,cAAAA,CAAa,CAAE,CAAG,CAAC,CAAC,CAC9D,WAAY,CAAEC,mBAAAA,CAAkB,CAAE,CAAG,CAAC,CAAC,CACvCC,WAAAA,CAAU,CACV,cAAe,CAAEC,OAAAA,CAAM,CAAE,CAAG,CAAC,CAAC,CAC/B,CAAGjB,EAEE,CAAElJ,GAAAA,CAAE,CAAE4G,KAAAA,CAAI,CAAEwD,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEhC,YAAAA,CAAW,CAAEiC,YAAAA,CAAW,CAAE5B,OAAAA,CAAM,CAAE,CACtEiB,MAAAA,EAAAA,EAAc,CAAC,EAEXY,EAAgBC,AAAAA,GAAAA,GAAAA,EAAAA,AAAAA,EAAcC,AAAAA,GAASA,EAAM,KAAK,CAAC,cAAc,EAwBvE,GAAI,CAACzK,GAAM,CAACqK,EAEV,MAAMK,MAAM,8CAGd,IAAMC,EAAWjC,IAAW7H,EAAAA,EAAAA,CAAAA,MAAyB,CAC/C+J,EAAU9O,IAASE,EAAAA,EAAAA,CAAAA,GAAoB,CACvC6O,EAAY/O,IAASE,EAAAA,EAAAA,CAAAA,OAAwB,CAE7C8O,EAAaC,AAAAA,GAAAA,EAAAA,OAAAA,AAAAA,EAAQ,KACzB,OAAQ5B,GACN,IAAK,aACH,OAAOlI,EAAAA,CAAAA,CAAAA,CAAM,CAAC,+BAChB,KAAK,UACH,OAAOA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,8BAChB,KAAK,OACH,OAAO2I,EACH3I,EAAAA,CAAAA,CAAAA,CAAM,CAAC,2CACPA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,2BAEf,CACF,EAAG,CAACkI,EAAgBS,EAAiB,EAE/BoB,EAAOD,AAAAA,GAAAA,EAAAA,OAAAA,AAAAA,EAAQ,KACnB,IAAIE,EAEJ,OAAQ9B,GACN,IAAK,aACH8B,EAAYhB,EACZ,KACF,KAAK,UACHgB,EAAYnB,EACZ,KACF,KAAK,OACHmB,EAAYX,CAGhB,CAEA,MAAOY,AAAAA,GAAAA,GAAAA,EAAAA,AAAAA,EAAWC,OAAOF,GAAYG,AAAAA,GAAAA,GAAAA,EAAAA,AAAAA,EAAkBD,OAAOF,IAChE,EAAG,CAAC9B,EAAgBW,EAAcQ,EAAaL,EAAmB,EAG5D,CAACoB,EAAaC,EAAe,CAAGjF,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IAEzC,CAACkF,EAAUC,EAAY,CAAGnF,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IAEzC,MACE,sB,SACE,WAAC,OACC,UAAWoF,IAAW,CACpB,oCACA,2CACA,WACA,gGACA,iCACD,E,UAED,WAAC,OACC,UAAU,4EACV,QAAS,KACP,GAAI5M,CAAAA,MAAAA,GAAAA,KAAAA,GAAAA,GAAAA,IAGA8L,GAGJ,GAAIC,EAAS,CACXpI,EAAU,UAAyBxC,MAAAA,CAAhBqK,EAAS,SAAU,OAAHrK,IACnC,MACF,CACA,GAAI6K,EAAW,CACbrI,EAAU,UAAiCxC,MAAAA,CAAxBqK,EAAS,iBAAkB,OAAHrK,IAC3C,MACF,EACF,EACA,aAAc,KACZsL,EAAe,GACjB,EACA,aAAc,KACZA,EAAe,GACjB,EACA,cAAY,yB,UAGX5B,EAGD,WAAC,OAAI,UAAU,uB,UACb,WAAC,OAAI,UAAU,8C,UACb,WAAC,OAAI,UAAU,8B,UACb,UAACgC,GAAIA,CAAC,KAAM9E,C,GACX+D,EAEC,UAACgB,GAAAA,GAAwBA,CAAAA,CAAC,UAAU,wC,GAEpC,uB,UArHd,AAAK3B,EAGAD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAY,MAAM,AAAD,EAKSA,EAAW,IAAI,CAC5CrK,AAAAA,GAAQA,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,gBAAgB,AAAD,IAAMkM,GAAAA,EAAAA,CAAAA,MAA6B,EAI9D,UAACD,GAAAA,GAAwBA,CAAAA,CAAC,UAAU,2C,GAItC,UAACE,GAAAA,GAAiCA,CAAAA,CAAC,UAAU,0C,GAZ3C,UAACA,GAAAA,GAAiCA,CAAAA,CAAC,UAAU,0C,GAJxC,KAuHQpC,E,MAKP,UAACqC,GAAWA,CAAC,YAAazD,C,MAE5B,UAAC0D,EAAAA,EAAMA,CAAAA,CACL,UAAU,2DACV,MAAM,SACN,IAAK3B,C,MAKT,UAAClC,GAAeA,CAAC,iBAAkBpM,C,GAGlC,CAAC,CAACoO,GACD,UAAC8B,GAAAA,CAAOA,CAAAA,CACN,OAAQ9B,EAAW,UAAU,CAC7B,KAAMA,EAAW,QAAQ,CACzB,MAAQ,GAAgBc,MAAAA,CAAdF,EAAW,KAAQ,OAALE,E,GAK3B,AAACT,EAyHE,KAxHF,uB,UACGc,GAAe7B,EACd,UAAC,OACC,UAAU,0DACV,MAAO,CACL,WACE,4EACJ,C,GAEA,KACJ,UAAC,OACC,UAAU,qDACV,QAAS1I,AAAAA,IAEPA,EAAE,eAAe,EACnB,E,SAECuK,GAAe7B,EACd,uB,UACG,AAACmB,EAqBE,KAnBF,UAACsB,GAAAA,EAAeA,CAAAA,CACd,UAAS,GACT,UAAS,GACT,SAAUjM,EACV,WACElE,IAASE,EAAAA,EAAAA,CAAAA,GAAoB,CACzBkQ,GAAAA,EAAAA,CAAAA,GAAqB,CACrBA,GAAAA,EAAAA,CAAAA,OAAyB,CAE/B,WAAY/B,EACZ,sBAAuBgC,AAAAA,IACrB,IAEMC,EAFAC,EAAavM,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUoJ,EAC7BmD,CAAAA,EAAW,aAAa,CAAG,mBACrBD,AAAwB,OAAxBA,CAAAA,EAAAA,EAAW,aAAa,AAAD,GAAvBA,AAAAA,KAAAA,IAAAA,EAAAA,EAA4B,CAAC,IACjC,OAAQD,C,GAEV9C,EAAyBgD,EAC3B,C,GAIJ,UAACpE,EAAAA,EAAIA,CAAAA,CACH,QAAO,GACP,UAAU,sBACV,SAAS,cACT,QAAQ,SACR,QAASsD,EACT,OACE,WAACtD,EAAAA,EAAAA,CAAAA,OAAY,EAAC,KAAK,O,UAEhB2C,EACC,UAAC9D,GAAWA,CACV,GAAI9G,EACJ,QAASqK,EACT,SAAUM,EACV,cAAevL,EACf,QAAS,IAAMkM,EAAe,G,GAE9B,KACHT,EACC,UAAC7C,EAAAA,CAAOA,CAAAA,CAAC,QAAS/G,EAAAA,CAAAA,CAAAA,CAAM,CAAC,uB,SACvB,UAACgH,EAAAA,EAAAA,CAAAA,IAAS,EACR,QAAS,KACP,IAAI,CAAC0B,EAGL1L,MAAAA,GAAAA,EAAgB0L,EAClB,EACA,cAAY,gB,SAEX1I,EAAAA,CAAAA,CAAAA,CAAM,CAAC,+B,KAGV,KACHsI,EAED,UAACvB,EAAAA,CAAOA,CAAAA,CACN,SAAS,OACT,QAAS6B,EAAa,SAAW,QACjC,QAAS5I,EAAAA,CAAAA,CAAAA,CAAM,CACb,sC,SAGF,UAACgH,EAAAA,EAAAA,CAAAA,IAAS,EACR,KAAK,SACL,SAAU,CAAC4B,EACX,QAAS,KACP,GAAI,EAACjD,IAAQ,CAAC9K,EAGdsN,MAAAA,GAAAA,EAAW,CAAExC,KAAAA,EAAM5G,GAAAA,EAAIlE,KAAAA,CAAK,EAC9B,E,SAEA,UAAC,Q,SAAMmF,EAAAA,CAAAA,CAAAA,CAAM,CAAC,S,mBAMtB,UAACqL,EAAAA,EAAUA,CAAAA,CACT,UAAU,YACV,cAAY,4BACZ,MAAM,UACN,KAAK,UACL,KAAM,UAACC,GAAAA,GAAWA,CAAAA,CAAAA,GAClB,QAAS,IAAMf,EAAY,G,QAI/BrB,GAAU,CAACQ,EAEb,UAAC2B,EAAAA,EAAUA,CAAAA,CACT,UAAU,aACV,MAAM,YACN,KAAM,UAACE,GAAAA,GAAeA,CAAAA,CAAC,UAAU,qB,KAEjC,I,SAKX7C,EACC,UAACrB,GAAeA,CACd,sBAAuBqB,EACvB,QAAS8C,AAAAA,IACPnD,EAAY,mBACPK,GAAAA,CACH,OAAQ8C,C,GAEZ,EACA,wBAAyBA,AAAAA,IACvBhE,EAAwB,mBACnBkB,GAAAA,CACH,OAAQ8C,C,GAEZ,C,GAEA,K,IAIZ,E,eCtUaC,GAA4B,AAAC,I,IAuS5BC,EAvS4B,CAAEtM,QAAAA,CAAO,CAAE,GAC7CuM,EAAapC,AAAAA,GAAAA,GAAAA,CAAAA,AAAAA,EACjBqC,AAAAA,GAASA,EAAM,KAAK,CAAC,UAAU,GAAKC,GAAAA,EAAAA,CAAAA,QAAkB,EAIlD,CAACtQ,EAAc4J,EAAiBK,EAAwB,CAC5DN,KAEI,CACJ4G,kCAAAA,CAAiC,CACjCC,uBAAAA,CAAsB,CACtBC,gCAAAA,CAA+B,CAChC,CAAGjQ,EAAkBR,GAEhB,CACJ,SAAU,CAAE0Q,QAAAA,CAAO,CAAElI,KAAAA,CAAI,CAAEmI,YAAAA,CAAW,CAAEpP,OAAAA,CAAM,CAAEqP,OAAAA,CAAM,CAAE1J,OAAAA,CAAM,CAAE,CAChE9B,aAAAA,CAAY,CACb,CAAGJ,GAAoB,CACtB,OAAQ,CACNnB,QAAAA,EACA,YAAa7D,EAAa,WAAW,CACrC,MAAOX,EAAqB,CAC1B,KAAMW,EAAa,UAAU,AAC/B,GACA,aAAcb,EAAuBa,EAAa,SAAS,EAC3D,aAAcA,EAAa,YAAY,CACvC,YAAaA,EAAa,WAAW,CAErC,QAASA,EAAa,SAAS,CAC3B6Q,EAAAA,EAAAA,CAAAA,WAA0B,CAC1BA,EAAAA,EAAAA,CAAAA,UAAyB,AAC/B,CACF,GAEA5J,GAAwB,CAAEC,OAAAA,EAAQrD,QAAAA,CAAQ,GAE1C6B,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACRkE,EAAgBhI,AAAAA,GAAS,mBACpBA,GAAAA,CACH,YAAa,E,GAEjB,EAAG,CAACiC,EAAQ,EAKZ6B,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACRhD,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,QAAoB,CAAE,CAAE,IAAK,SAAU,EACtD,EAAG,EAAE,EAEL+F,GAAsB,CACpB,SAAUF,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,IAAI,CACpB3E,QAAAA,EACAtC,OAAAA,CACF,GAEA,GAAM,CAAE,cAAeuP,CAAwB,CAAE,QAASC,CAAW,CAAE,CACrE1P,EAAe,CACb,gBAAiB+O,EACjB7O,OAAAA,CACF,GAKI,CAAEyP,cAAAA,CAAa,CAAEC,QAAAA,CAAO,CAAE,CAAGrL,GAAuB,CACxD/B,QAAAA,EACA,WAAYtC,EACZ,WAAY2F,CACd,GAEA,MACE,uB,UACG8J,EACAF,EACD,WAACI,EAAAA,EAAMA,CAAAA,C,UACL,WAACC,EAAAA,EAAMA,CAAAA,C,UACL,UAACC,EAAAA,EAAWA,CAAAA,C,SACV,UAAC,Q,SAAM3M,EAAAA,CAAAA,CAAAA,CAAM,CAAC,oB,KAEhB,UAAC4M,EAAAA,EAAaA,CAAAA,C,SACZ,UAACtI,EAAAA,EAAMA,CAAAA,CAAC,KAAM,UAACuI,GAAAA,GAAWA,CAAAA,CAAAA,GAAK,QAASL,EAAQ,kBAAkB,C,SAC/DxM,EAAAA,CAAAA,CAAAA,CAAM,CAAC,mB,QAId,WAAC8M,EAAAA,EAASA,CAAAA,C,UACR,WAACC,EAAAA,EAAgBA,CAAAA,C,UACf,UAACC,EAAAA,EAAMA,CAAAA,CACL,UAAU,gBACV,MACElB,EAAoCmB,EAAAA,CAAoBA,CAAG,CAAC,EAE9D,MAAO1R,EAAa,UAAU,CAC9B,SAAUhB,AAAAA,I,IAeJ2S,EAdJ/H,EAAgBhI,AAAAA,GAAS,mBACpBA,GAAAA,CACH,WACE5C,C,IAIJ0D,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,sBAAkC,CAAE,CAC/C,SAAUkB,EACV,WAAYuM,EAAa,WAAa,YACtC,SAAU,UACV,OAAQ,SACR,YAAa,QACb,YAAa3L,EAAAA,CAAAA,CAAAA,CAAM,CAAC,AAC4BzF,OAA9C2S,CAAAA,EAAAA,EAAoB,IAAI,CAACC,AAAAA,GAAOA,EAAI,KAAK,GAAK5S,EAAG,GAAjD2S,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EACI,YAAY,CAEpB,EACF,E,SAEC/R,EAAoB,GAAG,CAACgS,AAAAA,GACvB,UAACH,EAAAA,EAAAA,CAAAA,MAAa,EAAiB,MAAOG,EAAI,KAAK,C,SAC5CnN,EAAAA,CAAAA,CAAAA,CAAM,CAACmN,EAAI,YAAY,C,EADNA,EAAI,KAAK,E,GAKhC,AAACxB,EAgDE,KA1CF,UAACqB,EAAAA,EAAMA,CAAAA,CACL,UAAU,gBACV,MAAOjB,EAAyBkB,EAAAA,CAAoBA,CAAG,CAAC,EACxD,MAAO1R,EAAa,WAAW,CAC/B,SAAUhB,AAAAA,I,IA0BJ6S,EAzBJ,IAAI,CAAC5S,EAAkBD,GAGvB4K,EAAgBjC,AAAAA,GACd,AAAI3I,IAAQE,EAAAA,EAAAA,CAAAA,UAAsB,EAAIyI,EAAE,YAAY,CAC3C,mBACFA,GAAAA,CACH,aAAc,GACd,UAAWhJ,EAAAA,GAA8B,CACzC,YAAaK,C,GAGV,mBACF2I,GAAAA,CACH,YAAa3I,C,IAIjB0D,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,sBAAkC,CAAE,CAC/C,SAAUkB,EACV,WAAYuM,EAAa,WAAa,YACtC,SAAU,UACV,OAAQ,SACR,YAAa,WACb,YAAa3L,EAAAA,CAAAA,CAAAA,CAAM,CAAC,AAC+BzF,OAAjD6S,CAAAA,EAAAA,EAAuB,IAAI,CAACD,AAAAA,GAAOA,EAAI,KAAK,GAAK5S,EAAG,GAApD6S,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EACI,YAAY,CAEpB,EACF,E,SAECnS,EAAuB,GAAG,CAACkS,AAAAA,GAC1B,UAACH,EAAAA,EAAAA,CAAAA,MAAa,EAAiB,MAAOG,EAAI,KAAK,C,SAC5CnN,EAAAA,CAAAA,CAAAA,CAAM,CAACmN,EAAI,YAAY,C,EADNA,EAAI,KAAK,E,GAWnC,UAACH,EAAAA,EAAMA,CAAAA,CACL,UAAU,gBACV,MACEhB,EAAkCiB,EAAAA,CAAoBA,CAAG,CAAC,EAE5D,MACE1R,EAAa,YAAY,CACrB,eACAA,EAAa,SAAS,CAE5B,SAAUhB,AAAAA,I,IAiBJ8S,EAhBJlI,EAAgBjC,AAAAA,QlB9O9B3I,QkB8OoC,mBACjB2I,GAAAA,CACH,YAAazI,EAAAA,EAAAA,CAAAA,GAAe,CAC5B,aAAcH,EAAaC,GAC3B,UAAW+S,ClBlP3B/S,EkBkP2CA,ElB1OpCgT,AANuB,CAC5BrT,EAAAA,GAA8B,CAC9BA,EAAAA,SAAoC,CACpCA,EAAAA,OAAkC,CACnC,CAEiB,QAAQ,CAACK,IkB2OPA,EACAL,EAAAA,GAA8B,A,KAGpC+D,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,sBAAkC,CAAE,CAC/C,SAAUkB,EACV,WAAYuM,EAAa,WAAa,YACtC,SAAU,UACV,OAAQ,SACR,YAAa,SACb,YAAa3L,EAAAA,CAAAA,CAAAA,CAAM,CAAC,AAC8BzF,OAAhD8S,CAAAA,EAAAA,EAAsB,IAAI,CAACF,AAAAA,GAAOA,EAAI,KAAK,GAAK5S,EAAG,GAAnD8S,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EACI,YAAY,CAEpB,EACF,E,SAECnS,EAAsB,GAAG,CAACiS,AAAAA,GACzB,UAACH,EAAAA,EAAAA,CAAAA,MAAa,EAAiB,MAAOG,EAAI,KAAK,C,SAC5CnN,EAAAA,CAAAA,CAAAA,CAAM,CAACmN,EAAI,YAAY,C,EADNA,EAAI,KAAK,E,MAMnC,UAACK,EAAAA,EAAeA,CAAAA,C,SACd,UAACC,EAAAA,EAAMA,CAAAA,CACL,SAAUlS,EAAa,YAAY,CACnC,UAAW,GACX,UAAU,YACV,MAAOA,EAAa,WAAW,CAAG0R,EAAAA,CAAoBA,CAAG,CAAC,EAC1D,YAAajN,EAAAA,CAAAA,CAAAA,CAAM,CAAC,oCACpB,MAAOzE,EAAa,WAAW,CAC/B,SAAUhB,AAAAA,IACRiL,EAAwBjL,EAC1B,C,QAIN,UAACmT,EAAAA,EAAOA,CAAAA,CAAC,IAAK/M,E,SACZ,WAACgN,EAAAA,EAAIA,CAAAA,CAAC,SAAU1B,EAAS,iBAAiB,mB,UAEvClI,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,IAAI,CAAC,MAAM,AAAD,EACf,UAAC,OACC,UAAWyG,IACT,4CACA,0C,SAGDzG,EAAK,IAAI,CAAC,GAAG,CAAC,CAAC6J,EAASC,K,IAEbC,E,MADV,UAAC9F,GAAOA,CAEN,iBAAkB4F,EAClB,YAAatB,EAAY,WAAW,CACpC,wBACEA,EAAY,uBAAuB,CAErC,QAAS,KACPA,EAAY,OAAO,CAACsB,EACtB,EACA,yBAA0BtB,EAAY,QAAQ,CAC9C,SAAU,AAAC,I,GAAA,CAAE3G,KAAAA,CAAI,CAAE5G,GAAAA,CAAE,CAAElE,KAAAA,CAAI,CAAE,GAC3B,GAAIA,IAASE,EAAAA,EAAAA,CAAAA,GAAoB,CAAE,CACjCyR,EAAQ,kBAAkB,CAAC,CACzB7G,KAAAA,EACAvG,QAAAA,EACA,QAASL,CACX,GACA,MACF,CACA,GAAIlE,IAASE,EAAAA,EAAAA,CAAAA,OAAwB,CAAE,CACrCyR,EAAQ,kBAAkB,CAAC,CAAE7G,KAAAA,EAAM,UAAW5G,CAAG,GACjD,MACF,CACF,EACA,YAAauN,EAAY,WAAW,CACpC,cAAetJ,AAAAA,I,IAGG+K,EAENC,EAKGC,EATb3B,EAAY,aAAa,CAAC,CACxB,aAAc,CACZ,WAAYyB,AAAS,OAATA,CAAAA,EAAAA,EAAO,EAAE,AAAD,GAARA,AAAAA,KAAAA,IAAAA,EAAAA,EAAa,GACzB,YAAa3O,EACb,KAAM4O,AAAW,OAAXA,CAAAA,EAAAA,EAAO,IAAI,AAAD,GAAVA,AAAAA,KAAAA,IAAAA,EAAAA,EAAe,GACrB,YAAahL,EAAO,WAAW,CAC/B,SAAU,CACR,CACE,IAAKA,EAAO,QAAQ,CACpB,IAAKiL,AAAe,OAAfA,CAAAA,EAAAA,EAAO,QAAQ,AAAD,GAAdA,AAAAA,KAAAA,IAAAA,EAAAA,EAAmB,EAC1B,EACD,AACH,CACF,EACF,EACA,eACE1S,EAAa,YAAY,CACrB,aACAA,EAAa,SAAS,CACtB,UACA,M,EA9CA,GAA4BsS,MAAAA,CAAAA,AAAR,OAAlBC,CAAAA,EAAAA,EAAQ,UAAU,AAAD,GAAjBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAoB,EAAE,CAAC,KAAS,OAAND,G,KAmDtC,KAEH,AAACnC,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,AAAU,OAAVA,CAAAA,EAAAA,EAAM,IAAI,AAAD,GAATA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAY,MAAM,AAAD,GAAMO,EAWrB,KAVF,UAACiC,EAAAA,CAAcA,CAAAA,CACb,QAAS,KACP/I,EAAgB/J,EAClB,EACA,UACE,CAACE,EAA2B,CAC1BC,aAAAA,CACF,E,GAMLwI,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,IAAI,CAAC,MAAM,AAAD,GAAKmI,EACpB,WAAC,OAAI,UAAU,0F,UACb,UAACb,EAAAA,EAAUA,CAAAA,CACT,KAAM,UAACxD,GAAAA,GAAcA,CAAAA,CAAAA,GACrB,QAAO,GACP,MAAM,W,GAER,WAAC,O,UAAK7H,EAAAA,CAAAA,CAAAA,CAAM,CAAC,WAAW,M,MAExB,KAEHmM,GAAUpI,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,IAAI,CAAC,MAAM,AAAD,EACzB,UAAC,OAAI,UAAU,oB,GACb,K,UAMhB,ECvXA,GALa,KACX,GAAM,CAAEqF,SAAAA,CAAQ,CAAE,CAAG+E,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,IACrB,OAAO/E,EAAW,UAACqC,GAAOA,CAAC,QAASrC,C,GAAe,IACrD,C,2QCIagF,EAAgB,AAAC,I,GAAA,CAC5BrP,GAAAA,CAAE,CACFsP,gBAAAA,CAAe,CACfC,gBAAAA,CAAe,CAKhB,GACO,CAAErC,QAAAA,CAAO,CAAEsC,SAAAA,CAAQ,CAAE,CAAG5G,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUA,AAAC,GAADA,EAAAA,CAAAA,AAAAA,EACtC,YACE,aAAMvB,EAAAA,EAAAA,CAAAA,cAA2B,CAAC,CAChC,aAAcrH,CAChB,E,GACF,CACE,OAAQ,GACR,UAAW,KACTsP,MAAAA,GAAAA,EAAkBtP,EACpB,EACA,UAAW,KACTuP,MAAAA,GAAAA,GACF,CACF,GAGF,MAAO,CACL,cAAerC,EACf,UAAWsC,CACb,CACF,EAEO,IAAMC,G,EAAsB,qB,IAYnBC,EAAAA,EAZ0B,CACxC1P,GAAAA,CAAE,CACF2P,SAAAA,CAAQ,CACRC,OAAAA,CAAM,CAKP,GACOjP,EAAO,MAAM0G,EAAAA,EAAAA,CAAAA,qBAAkC,CAAC,CACpD,aAAcrH,CAChB,GACM6M,EAAQ6C,AAAiB,OAAjBA,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,AAAU,OAAVA,CAAAA,EAAAA,EAAM,IAAI,AAAD,GAATA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAY,KAAK,AAAD,GAAhBA,AAAAA,KAAAA,IAAAA,EAAAA,EAAqB,CAAC,EACpCG,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EACE,CACE,aAAclJ,SAAS,QAAQ,CAC/B,KAAM,QACN,MAAO,SACLiJ,OAAAA,C,EACG/C,EAEP,EACA7M,EACA2P,EAEJ,G,SAzBaF,CAAAA,E,iCA2BAK,EAAqB,AAACH,GACjC,CAACI,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAQJ,IAAa,CAAC,CAACA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,aAAa,AAAD,EAEnCK,EAA2B,KACtC5O,EAAAA,EAAAA,CAAAA,KAAY,CAAC,CACX,QAAS,gCACT,MAAO,IAAI2G,EAAAA,EAAWA,CACpB,eACA,uCAEJ,GACAhH,EAAAA,EAAAA,CAAAA,KAAW,CAAC,CACV,QAASC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAoBC,EAAAA,CAAAA,CAAAA,CAAM,CAAC,SACtC,EACF,C,sQClDO,IAAMgP,EAAiB,SAC5B/P,CAAU,E,IACVgQ,EAAQ,UAARA,MAAAA,CAAAA,GAAAA,AAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAQ,EAEFjO,EAAyB,EAAE,CAQjC,OAPA/B,MAAAA,GAAAA,EAAY,OAAO,CAACR,AAAAA,IAClBA,EAAK,KAAK,CAAGwQ,EACbjO,EAAI,IAAI,CAACvC,GACLA,EAAK,SAAS,EAChBuC,EAAI,IAAI,IAAIgO,EAAevQ,EAAK,SAAS,CAAEwQ,EAAQ,GAEvD,GACOjO,CACT,EAEakO,EAA0B,KACrC,IAAMC,EAAYC,SAAS,IAAI,CAAC,KAAK,CAC/BC,EAAYD,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,AAChED,CAAAA,EAAU,SAAS,CAAG,QACtBE,EAAU,SAAS,CAAG,QACtBF,EAAU,QAAQ,CAAG,SACrBE,EAAU,QAAQ,CAAG,QACvB,EAGaC,EAAgB,WAMhBC,EAAqB,WAErBC,EAGc,CAACC,EAAeC,IACzC,AAAKA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAiB,MAAM,AAAD,EAGpBA,EAAgB,GAAG,CAACjR,AAAAA,IACzB,IAMckR,EACDC,EAPEC,EAIXC,EACSC,EACCJ,EACDC,EAPPI,EAASH,AAAwB,OAAxBA,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,CAAe,CAACpR,EAAK,EAAE,CAAC,AAAD,GAAvBoR,AAAAA,KAAAA,IAAAA,EAAAA,EAA4B,CAAC,EAC5C,MAAO,mBACFpR,GAAAA,CACH,eACEqR,AAA4B,OAA5BA,CAAAA,EAAAA,EAAO,qBAAqB,AAAD,GAA3BA,AAAAA,KAAAA,IAAAA,EAAAA,EAAgCG,EAAAA,EAAAA,CAAAA,MAA0B,CAC5D,UAAWF,AAAU,OAAVA,CAAAA,EAAAA,EAAO,GAAG,AAAD,GAATA,AAAAA,KAAAA,IAAAA,EAAAA,EAAc,GACzB,WAAYJ,AAA4B,OAA5BA,CAAAA,EAAAA,AAAgB,OAAhBA,CAAAA,EAAAA,EAAO,SAAS,AAAD,GAAfA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAkB,UAAU,AAAD,GAA3BA,AAAAA,KAAAA,IAAAA,EAAAA,EAAgC,GAC5C,UAAWC,AAA2B,OAA3BA,CAAAA,EAAAA,AAAgB,OAAhBA,CAAAA,EAAAA,EAAO,SAAS,AAAD,GAAfA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAkB,SAAS,AAAD,GAA1BA,AAAAA,KAAAA,IAAAA,EAAAA,EAA+BnR,EAAK,SAAS,A,EAE5D,GAZS,EAAE,CAmCAwG,EAAgB,AAAClB,IAC5B,GAAI,CACF,OAAOuB,KAAK,KAAK,CAACvB,EACpB,CAAE,MAAOlE,EAAG,CACV,MAAO,EACT,CACF,C,0HCpGYqQ,E,0GCWCC,EAAuB,CAClC,MAAOC,EAAAA,CAAAA,CAAAA,KAAY,CACnB,QAASA,EAAAA,CAAAA,CAAAA,OAAc,CACvB,MAAOA,EAAAA,CAAAA,CAAAA,KAAY,AACrB,EAEaC,EAA8C,AAAC,I,GAAA,CAC1DC,KAAAA,EAAO,SAAS,CAChBC,SAAAA,CAAQ,CACRC,QAAAA,CAAO,CACPC,UAAAA,CAAS,CACV,G,MACC,WAAC,OAAI,UAAU,uB,UACb,UAAC1J,EAAAA,CAAOA,CAAAA,CACN,QAAS,GACT,QAASyJ,EAAU,QAAU,SAC7B,QAASxQ,EAAAA,CAAAA,CAAAA,CAAM,CAAC,gC,SAEfwQ,EACC,UAACE,EAAAA,GAAiBA,CAAAA,CAChB,UAAWlG,IACT4F,EAAAA,CAAAA,CAAAA,iBAAwB,CACxBD,CAAoB,CAACG,EAAK,CAC1BG,E,GAGF,I,GAELF,E,kICsJL,EA1JA,SAAsBI,CAAqB,EACzC,I,EA5BAC,EACA9T,EA2BM,CACJ+T,UAAAA,CAAS,CACTC,SAAAA,CAAQ,CACRC,UAAAA,CAAS,CACTC,WAAAA,CAAU,CACVC,kBAAAA,CAAiB,CACjBC,kBAAAA,EAAoB,EAAI,CACzB,CAAGP,EACE,CAACQ,EAAgBC,EAAkB,CAAGhM,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAkB,IACxDiM,EAAazQ,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,EAAe,GAC5B0Q,EAAa1Q,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,IACnB,GAAM,CACJqL,QAAAA,CAAO,CACP,KAAM2E,CAAQ,CACd1E,YAAAA,CAAW,CACXqF,SAAAA,CAAQ,CACRpF,OAAAA,CAAM,CACNqF,OAAAA,CAAM,CACN1U,OAAAA,CAAM,CACN2F,OAAAA,CAAM,CACP,CAAG3B,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,G,EACF,kBAAM2Q,CAAO,EAGX,IAAMC,EAAUL,EAAW,OAAO,AAC9BC,CAAAA,EAAW,OAAO,EAGpBA,EAAW,OAAO,CAAC,mBACbG,GAAW,CAAC,IAChB,KAAM,EAAE,A,IAIZ,IAAMzB,EAAS,MAAM,IAAI2B,QAAQ,CAACC,EAASC,KACzCP,EAAW,OAAO,CAAGM,EACrBd,EAASW,GACN,IAAI,CAACK,AAAAA,GAASF,EAAQE,IACtB,KAAK,CAACC,AAAAA,GAAOF,EAAOE,GACzB,SAMA,CAHAT,EAAW,OAAO,CAAG,KAGjBD,EAAW,OAAO,GAAKK,IACrBD,GACFA,CAAAA,EAAQ,IAAI,CAAG,EAAE,AAAD,EAEX,CACL,KAAM,EAAE,CACR,SAAU,CACZ,GAEKzB,CACT,G,SAlCMyB,CAAO,E,iCAmCb,CACE,OAAQN,GAAkBF,EAAoB,KAAOJ,EACrDE,UAAAA,EACA,SAAU,KAEV,EACA,SAAUhN,AAAAA,GAAQA,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,OAAO,AAAD,IAAM1I,KAAAA,GAAa,CAAC0I,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,OAAO,AAAD,EAC9D,UAAW,KACLoN,GACFC,EAAkB,GAEtB,EACA,QAASvR,AAAAA,QAIyBmS,EAAAA,CAAN,KAAtBpB,EAAS,QAAQ,EAAWoB,AAAAA,CAAsB,OAAtBA,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,AAAc,OAAdA,CAAAA,EAAAA,EAAU,IAAI,AAAD,GAAbA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAgB,MAAM,AAAD,GAArBA,AAAAA,KAAAA,IAAAA,EAAAA,EAA0B,GAAK,GAE7DlV,EAAO,mBACF8T,GAAAA,CACH,KAAM,EAAE,A,IAGZQ,EAAkB,GACpB,CACF,GAGF,GAAM,CAAEa,WAAAA,CAAU,CAAEC,WAAAA,CAAU,CAAEC,YAAAA,CAAW,CAAE,EAhH7CvB,EAkHEA,EAjHF9T,EAkHEA,EA5FK,CAAEmV,WAnBU,CAACxT,EAAMoP,KACxB+C,EAAS,IAAI,CAAC,MAAM,CAAC/C,EAAO,EAAGpP,GAC/B3B,EAAO,mBACF8T,GAAAA,CACH,KAAM,IAAKA,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,IAAI,AAAD,GAAK,EAAE,CAAE,A,GAErC,EAaqBsB,WAVFrE,AAAAA,IACjB+C,EAAS,IAAI,CAAC,MAAM,CAAC/C,EAAO,GAC5B/Q,EAAO,mBACF8T,GAAAA,CACH,KAAM,IAAKA,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,IAAI,AAAD,GAAK,EAAE,CAAE,A,GAErC,EAIiCuB,YAFb,IAAMvB,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,IAAI,AAEK,GA+F7C3P,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACJgQ,GAAqB,CAAEhF,CAAAA,GAAWC,CAAU,GAC9CzJ,GAEJ,EAAG,EAAE,EAEL,IAAM2P,EAAaC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAc,K,IAEEC,EADjCxV,EAAO,CACL,KAAMoU,EAAoB,EAAE,CAAGoB,AAAc,OAAdA,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,IAAI,AAAD,GAAbA,AAAAA,KAAAA,IAAAA,EAAAA,EAAkB,EAAE,CACnD,QAASjX,KAAAA,EACT,SAAU,CACZ,GACAmW,IACAJ,EAAkB,IAClB3O,GACF,GAEA4C,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAgB,KACdgM,EAAW,OAAO,GAClBe,GACF,EAAG,IAAKpB,GAAc,EAAE,CAAE,EAC1B,IAAMuB,EAAYtG,GAAWC,GAAeyE,EAAM,SAAS,CACrD,CAAE,IAAK6B,CAAgB,CAAE,CAAG/M,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAChC,KACE,IAAI8M,EAGA,CAACtB,GACHM,GAEJ,EACA,CAAE,KAAM,GAAI,GAEdtQ,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACR,IAAMwR,EAAS,KACbD,GACF,EAEA,OADAE,OAAO,gBAAgB,CAAC,SAAUD,GAC3B,KACLC,OAAO,mBAAmB,CAAC,SAAUD,EACvC,CACF,EAAG,EAAE,EACL,GAAM,CAAErO,KAAAA,CAAI,CAAE,CAAGwM,GAAY,CAAC,EAC9B,MAAO,CACL,SAAUxM,EACVmO,UAAAA,EACA,SAAU,KACJ,CAACA,GAEHhB,GAEJ,EACA,OAAQa,EACRjG,OAAAA,EACAqF,OAAAA,EACAL,eAAAA,EACArU,OAAAA,EACAmV,WAAAA,EACAC,WAAAA,EACAC,YAAAA,CACF,CACF,E,2BC/IA,EA3CA,SAAexB,CAAkB,EAC/B,GAAM,CACJ4B,UAAAA,CAAS,CACTI,UAAAA,CAAS,CACTC,QAAAA,CAAO,CACPC,aAAAA,CAAY,CACZ5B,kBAAAA,CAAiB,CACjB9E,OAAAA,CAAM,CACP,CAAGwE,EACEmC,EAAeC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAErB,MACE,UAAC,OACC,UAAWvI,IAAWwI,EAAAA,CAAAA,CAAAA,mBAAqB,CAAE,CAC3C,CAACA,EAAAA,CAAAA,CAAAA,4BAA8B,CAAC,CAAEF,CACpC,G,SAECD,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAelC,EAAK,GAClB4B,CAAAA,EACC,uB,UACE,UAAC5E,EAAAA,EAAIA,CAAAA,CAAAA,GACL,UAAC,QAAK,UAAWqF,EAAAA,CAAAA,CAAAA,OAAS,C,SAAGhT,EAAAA,CAAAA,CAAAA,CAAM,CAAC,U,MAEpC4S,EACF,uB,UACE,UAACjF,EAAAA,EAAIA,CAAAA,CAAAA,GACL,UAAC,QAAK,UAAWqF,EAAAA,CAAAA,CAAAA,cAAgB,CAAE,QAASL,E,SACzC3S,EAAAA,CAAAA,CAAAA,CAAM,CAAC,sB,MAGViR,GAAqB,CAAC9E,EACxB,UAAC8G,EAAAA,EAAQA,CAAAA,CACP,QAASN,EACT,UAAWK,EAAAA,CAAAA,CAAAA,gBAAkB,CAC7B,MAAM,a,SAELhT,EAAAA,CAAAA,CAAAA,CAAM,CAAC,gB,GAER,IAAG,C,EAGf,E,4BCaA,EAvDA,SAAe2Q,CAAiB,EAC9B,GAAM,CACJ4B,UAAAA,CAAS,CACTW,YAAAA,CAAW,CACXP,UAAAA,CAAS,CACTC,QAAAA,CAAO,CACPO,YAAAA,CAAW,CACXC,KAAAA,CAAI,CACJC,IAAAA,CAAG,CACHC,KAAAA,CAAI,CACL,CAAG3C,EACJ,MACE,UAAC,OAAI,UAAWqC,EAAAA,CAAAA,CAAAA,KAAO,C,SACpBG,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAcxC,EAAK,GACjB,CAACiC,EA0BA,UAACW,EAAAA,EAAOA,CAAAA,CACN,MAAO,CACL,MAAOvT,EAAAA,CAAAA,CAAAA,CAAM,CAAC,2BACd,KAAM,UAACwT,EAAAA,EAAmBA,CAAAA,CAAAA,GAC1B,QAASb,GAAa3S,EAAAA,CAAAA,CAAAA,CAAM,CAAC,uBAC7B,WAAY,KACV2S,MAAAA,GAAAA,GACF,CACF,C,GAjCFJ,EACE,UAAC5E,EAAAA,EAAIA,CAAAA,CACH,IACE,UAAC,QAAK,UAAWqF,EAAAA,CAAAA,CAAAA,eAAiB,C,SAAGhT,EAAAA,CAAAA,CAAAA,CAAM,CAAC,U,GAE9C,iBAAkBgT,EAAAA,CAAAA,CAAAA,IAAM,CACxB,KAAK,Q,GAGP,UAACO,EAAAA,EAAOA,CAAAA,CACN,WAAY,CAAC,CAACL,EACd,MAAO,CACL,MAAOE,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,UAAU,AAAD,GAAKpT,EAAAA,CAAAA,CAAAA,CAAM,CAAC,6BAClC,YAAaoT,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,UAAU,AAAD,EAAIA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,SAAS,CAAG,GAClD,QAASC,MAAAA,EAAAA,KAAAA,EAAAA,EAAK,SAAS,CACvB,WAAYA,MAAAA,EAAAA,KAAAA,EAAAA,EAAK,UAAU,CAC3BC,KAAAA,CACF,EACA,SAAU,CACR,MACEF,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,gBAAgB,AAAD,GAAKpT,EAAAA,CAAAA,CAAAA,CAAM,CAAC,4BACrC,C,KAiBd,E,cCmEayT,EAAeC,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EAhH5B,SAAiC/C,CAA2B,CAAEgD,CAAG,EAC/D,GAAM,CACJT,YAAAA,CAAW,CACXzC,UAAAA,CAAS,CACTmD,aAAAA,CAAY,CACZC,KAAAA,CAAI,CACJC,WAAAA,CAAU,CACVC,cAAAA,CAAa,CACblB,aAAAA,CAAY,CACZmB,WAAAA,CAAU,CACVC,UAAAA,CAAS,CACTC,cAAAA,CAAa,CACbC,YAAAA,EAAc,EAAI,CAClBlD,kBAAAA,EAAoB,EAAK,CACzB6B,aAAAA,CAAY,CACZsB,UAAAA,CAAS,CACTC,eAAAA,CAAc,CACdC,mBAAAA,CAAkB,CACnB,CAAG3D,EAEE,CACJ4D,SAAAA,CAAQ,CACRhC,UAAAA,CAAS,CACThB,SAAAA,CAAQ,CACRpF,OAAAA,CAAM,CACNgF,eAAAA,CAAc,CACdrU,OAAAA,CAAM,CACN2F,OAAAA,CAAM,CACNwP,WAAAA,CAAU,CACVC,WAAAA,CAAU,CACVC,YAAAA,CAAW,CACZ,CAAGqC,EAAa,mBAAKR,GAAAA,CAAY/C,kBAAAA,C,IAalC,MAXAwD,AAAAA,GAAAA,EAAAA,mBAAAA,AAAAA,EACEd,EACA,IAAO,EAAE7W,OAAAA,EAAQ2F,OAAAA,EAAQwP,WAAAA,EAAYC,WAAAA,EAAYC,YAAAA,CAAY,GAC7D,CAACrV,EAAQ2F,EAAQwP,EAAYC,EAAYC,EAAY,EAEvDlR,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACRiT,MAAAA,GAAAA,EAAgB3B,EAAWgC,EAC7B,EAAG,CAACA,EAAUhC,EAAU,EAKtB,UAAC,OAAI,UAAWmC,IAAI1B,EAAAA,CAAAA,CAAAA,mBAAqB,CAAEsB,G,SACxC,AAACC,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,MAAM,AAAD,GAAMJ,EASnBrB,EACF,UAAC6B,EAAAA,EAAcA,CAAAA,CACb,UAAWlE,EACX,aAAc8B,EAAY,yBAAQqB,EAClC,WAAYW,EACZ,WAAY,CAAC9V,EAAMmW,IAAWd,MAAAA,EAAAA,KAAAA,EAAAA,EAAarV,EAAMmW,GACjD,SAAUP,MAAAA,EAAAA,KAAAA,EAAAA,EAAgB,QAAQ,CAClC,UAAW,CACT,MAAO,CACT,EACA,OACE,UAAC,OAAI,UAAU,oB,SACb,UAACQ,EAAMA,CACL,QAAS1D,EACT,OAAQhF,EACR,UAAWoG,EACX,UAAW6B,GAAa7C,EACxB,aAAcsB,EACd,kBAAmB5B,C,OAM3B,UAAC6D,EAAAA,EAAIA,CAAAA,CACGrE,UAAAA,EAAWmD,aAAAA,EAAcC,KAAAA,EAC/B,aAActB,EAAY,yBAAQqB,EAClC,WAAYW,EACZ,MAAO,GACP,WAAY,CAAC9V,EAAMmW,IACjB,UAACE,EAAAA,EAAAA,CAAAA,IAAS,EACR,UACE,AAAyB,UAAzB,OAAOf,EACHA,EACAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAgBtV,G,SAGrBqV,MAAAA,EAAAA,KAAAA,EAAAA,EAAarV,EAAMmW,E,GAGxB,OACE,UAACC,EAAMA,CACL,QAAS1D,EACT,OAAQhF,EACR,UAAWoG,EACX,UAAW6B,GAAa7C,EACxB,aAAcsB,EACd,kBAAmB5B,EACnB,QAASsD,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,MAAM,A,KAvD/B,UAACQ,EAAKA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,CACJ,QAASZ,EAAAA,GAAchD,EACvB,YAAa+B,EACb,UAAWiB,CAAAA,GAAc5B,EACzB,UAAW6B,GAAa7C,C,EACpB0C,G,EAyDd,G,cCpHae,EAA8C,AAAC,I,GAAA,CAC1DlM,WAAAA,CAAU,CACV2H,UAAAA,CAAS,CACTwE,WAAAA,EAL0B,CAKM,CACjC,GACOC,EAAUpM,EAAW,MAAM,CAAGmM,EACpC,MACE,WAAC,OAAI,UAAWzK,IAAW,uBAAwBiG,G,UAChD3H,EAAW,KAAK,CAAC,EAAGmM,GAAY,GAAG,CAACxW,AAAAA,GACnC,UAACsI,EAAAA,CAAOA,CAAAA,CAAe,QAAStI,EAAK,IAAI,CAAE,MAAM,O,SAC/C,UAAC0W,EAAAA,EAASA,CAAAA,CACR,UAAU,yCACV,KAAK,QACL,IAAK1W,EAAK,IAAI,CACd,KAAK,U,IALKA,EAAK,EAAE,GAStByW,EAAU,EACT,UAACnO,EAAAA,CAAOA,CAAAA,CACN,SAAS,QACT,QACE,UAAC,OAAI,UAAU,sF,SACZ+B,EAAW,KAAK,CAACmM,GAAY,GAAG,CAACxW,AAAAA,GAChC,WAAC,OAEC,UAAU,uC,UAEV,UAAC0W,EAAAA,EAASA,CAAAA,CACR,UAAU,yCACV,KAAK,QACL,IAAK1W,EAAK,IAAI,CACd,KAAK,U,GAEP,UAACmH,EAAAA,EAAAA,CAAAA,IAAe,EACd,SAAU,GACV,UAAU,yB,SAETnH,EAAK,IAAI,A,KAbPA,EAAK,EAAE,E,YAoBpB,WAAC0I,EAAAA,EAAGA,CAAAA,CAAC,UAAWiJ,EAAAA,CAAAA,CAAAA,IAAW,CAAE,KAAK,OAAO,MAAM,U,UAAU,IACrD8E,E,KAGJ,K,EAGV,E,4BC5DaE,EAA2B,AAAC,I,MAAA,CACvCC,UAAAA,CAAS,CACTC,WAAAA,CAAU,CACVC,SAAAA,CAAQ,CACRC,QAAAA,CAAO,CACPC,SAAAA,CAAQ,CACRC,cAAAA,CAAa,CAGd,GAwBC,MAAO,CAAEC,qBAvBoBtD,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,G,EAC3B,kBAAOuD,CAAa,CAAWhO,CAAM,EACnC8N,EAAc,CAACE,GACf,GAAI,CACF,MAAMC,EAAAA,EAAAA,CAAAA,qBAAgC,CAAC,CAErC,WAAYR,GAAaha,KAAAA,EACzB,YAAaia,EACb,UAAWM,EACX,UAAWL,EACX,SAAUC,CACZ,GACAC,MAAAA,GAAAA,EAAWG,EAAgB,GAAK,GAChC1T,EAAAA,CAAAA,CAAAA,IAAa,CAAC,iBAAkB,CAC9B,GAAIqT,EACJ,SAAU3N,AAAW,QAAXA,EAAmB,EAAI,GACjC,aAAc,mBAChB,EACF,CAAE,MAAOkO,EAAM,CACbJ,EAAcE,EAChB,CACF,G,SApBOA,CAAa,CAAWhO,CAAM,E,iCAsBT,CAChC,ECpCamO,EAAqB,AAAC,I,GAAA,CAAEC,UAAAA,CAAS,CAA2B,GACjE,CAACC,EAAWC,EAAa,CAAG9Q,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IAa3C,MAZAnE,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACJ,CAAC+U,GACHE,EAAa,GAEjB,EAAG,CAACF,EAAU,EAQP,CAAEC,UAAAA,EAAWE,sBAPU9D,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAc,AAACuD,IACtCA,EAGHM,EAAa,IAFbA,EAAa,GAIjB,EAC0C,CAC5C,ECVME,EAAiB,AAACC,GACtBA,EAAsB,SAAW,MAEtBC,EAAoB,AAAC,I,QAAA,CAChCC,kBAAAA,CAAiB,CACjBC,YAAAA,CAAW,CACXnB,UAAAA,CAAS,CACTE,SAAAA,CAAQ,CACRD,WAAAA,CAAU,CACVG,SAAAA,CAAQ,CACRgB,cAAAA,CAAa,CACbjB,QAAAA,CAAO,CACPQ,UAAAA,CAAS,CACTU,sBAAAA,CAAqB,CAMtB,GACO,CAACC,EAAYjB,EAAc,CAAGtQ,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAClCmR,MAAAA,GAAAA,GAEI,CAAEN,UAAAA,CAAS,CAAEE,sBAAAA,CAAqB,CAAE,CAAGJ,EAAmB,CAC9DC,UAAAA,CACF,GACMY,EAAchW,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,EAAO,IAG3B,IAAMiW,EAAsBxE,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,G,EAC1B,kBACEzK,CAAM,CACNkP,CAAK,EACF,MAAC,OAAML,MAAAA,EAAAA,KAAAA,EAAAA,EAAgB7O,EAAQkP,EAAK,IAAO,E,YAF9ClP,CAAM,CACNkP,CAAK,E,kCAGH,CAAEnB,qBAAAA,CAAoB,CAAE,CAAGP,EAAyB,CACxDC,UAAAA,EACAC,WAAAA,EACAC,SAAAA,EACAC,QAAAA,EACAC,SAAAA,EACAC,cAAAA,CACF,GAEA,IAAM9X,EAAUmZ,AAAAA,GAAAA,EAAAA,WAAAA,AAAAA,G,EACd,kBAAOD,CAAK,EACV,GAAIF,EAAY,OAAO,CAAE,CAEvBE,MAAAA,GAAAA,AAAsB,OAAtBA,CAAAA,EAAAA,EAAO,eAAe,AAAD,GAArBA,AAAAA,KAAAA,IAAAA,GAAAA,EAAAA,IAAAA,CAAAA,GACAA,MAAAA,GAAAA,AAAqB,OAArBA,CAAAA,EAAAA,EAAO,cAAc,AAAD,GAApBA,AAAAA,KAAAA,IAAAA,GAAAA,EAAAA,IAAAA,CAAAA,GACA,MACF,CACA,IAJEA,EACAA,EAOEA,EAJElP,EAASwO,EAAeO,EAC9BC,CAAAA,EAAY,OAAO,CAAG,GACtB,GAAI,CACG,OAAMC,EAAoBjP,EAAQkP,EAAK,IAAO,KACjDA,MAAAA,GAAAA,AAAsB,OAAtBA,CAAAA,EAAAA,EAAO,eAAe,AAAD,GAArBA,AAAAA,KAAAA,IAAAA,GAAAA,EAAAA,IAAAA,CAAAA,GACAN,MAAAA,GAAAA,EAAc5O,GACduO,EAAsBQ,GACtB,MAAMhB,EAAqBgB,EAAY/O,GAE3C,CAAE,MAAOkO,EAAM,CACbkB,QAAQ,KAAK,CAAC,qBAAsBlB,EACtC,CACAc,EAAY,OAAO,CAAG,EACxB,G,SApBOE,CAAK,E,iCAqBZ,CAACH,EAAYH,EAAY,EAM3B,MAHAnR,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAgB,KACdqR,MAAAA,GAAAA,EAAwBC,EAC1B,EAAG,CAACA,EAAW,EACR,CAAEA,WAAAA,EAAY/Y,QAAAA,EAASqY,UAAAA,CAAU,CAC1C,EC9EagB,EAAqB,AAACtG,IACjC,GAAM,CAAEgG,WAAAA,CAAU,CAAE,CAAGhG,EACvB,MAAO,sB,SAAGgG,EAAa,UAACO,EAAAA,EAAqBA,CAAAA,CAAAA,GAAM,UAACC,EAAAA,GAAiBA,CAAAA,CAAAA,E,EACvE,E,cCCaC,EAAe,AAACzG,IAQ3B,GAAM,CAAEgG,WAAAA,CAAU,CAAEV,UAAAA,CAAS,CAAExF,UAAAA,CAAS,CAAE4G,mBAAAA,CAAkB,CAAEC,UAAAA,CAAS,CAAE,CACvE3G,EAEI4G,EAAY,CAChB,UAAW7C,IACTiC,EAAavG,EAAAA,CAAAA,CAAAA,cAAqB,CAAGA,EAAAA,CAAAA,CAAAA,eAAsB,CAC3DuG,EAAalG,EAAY4G,EACzB,CACE,CAACjH,EAAAA,CAAAA,CAAAA,WAAkB,CAAC,CAAEuG,GAAcV,EACpC,CAAC7F,EAAAA,CAAAA,CAAAA,WAAkB,CAAC,CAAEkH,CACxB,EAEJ,EAEMhE,EAAOqD,EACX,UAACa,EAAAA,GAAiBA,CAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,CAAAA,EAAKD,IAEvB,UAACE,EAAAA,GAAkBA,CAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,CAAAA,EAAKF,WAG1B,AAAID,EACK,UAACjM,EAAAA,EAAUA,CAAAA,CAAC,KAAK,UAAU,MAAM,UAAU,KAAMiI,C,GAGnDA,CACT,E,cC5BatI,EAAkB0I,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EAC7B,CAAC/C,EAA6BgD,KAC5B,GAAM,CACJ6B,QAAAA,CAAO,CACPH,UAAAA,CAAS,CACTC,WAAAA,CAAU,CACVC,SAAAA,CAAQ,CACR,WAAYgB,CAAiB,CAC7Bd,SAAAA,CAAQ,CACRO,UAAAA,CAAS,CACTQ,YAAAA,CAAW,CACXa,mBAAAA,CAAkB,CAClBZ,cAAAA,CAAa,CACbC,sBAAAA,CAAqB,CACrBgB,SAAAA,CAAQ,CACRjH,UAAAA,CAAS,CACT6G,UAAAA,EAAY,EAAK,CACjBK,iBAAAA,EAAmB,EAAK,CACzB,CAAGhH,EAEE,CAAEgG,WAAAA,CAAU,CAAE/Y,QAAAA,CAAO,CAAEqY,UAAAA,CAAS,CAAE,CAAGK,EAAkB,CAC3DC,kBAAAA,EACAC,YAAAA,EACAnB,UAAAA,EACAE,SAAAA,EACAD,WAAAA,EACAG,SAAAA,EACAgB,cAAAA,EACAjB,QAAAA,EACAQ,UAAAA,EACAU,sBAAAA,CACF,SAUA,CARAjC,AAAAA,GAAAA,EAAAA,mBAAAA,AAAAA,EACEd,EACA,IAAO,EACL,SAAU/V,CACZ,GACA,CAACA,EAAQ,EAGNoY,GAIH,UAAC,OACC,QAAS2B,EAAmBtc,KAAAA,EAAYuC,EACxC,UAAWwS,EAAAA,CAAAA,CAAAA,oBAA2B,CACtC,cAAY,yB,SAEXsH,EACC,UAACT,EAAkBA,CAAC,WAAYN,C,GAEhC,UAACS,EAAYA,CACX,UAAWE,EACX,WAAYX,EACZ,UAAWV,EACX,mBAAoBoB,EACpB,UAAW5G,C,KAhBV,IAqBX,G,YC9DK,IAAMmH,EAAoC,AAAC,I,GAAA,CAChDtE,KAAAA,EAAO,IAAI,CACXuE,MAAAA,CAAK,CACLC,WAAAA,EAAa,IAAI,CACjBC,SAAAA,CAAQ,CACRC,OAAAA,CAAM,CACNpa,QAAAA,CAAO,CACR,G,MACC,WAAC,OACC,QAASA,EACT,UAAW4M,IACT,8BACA,oBACA,gBACA,kBACA,WACA,iBACA,+BACAuN,EAAW,6BAA+B,6B,UAG5C,UAAC,OAAI,UAAU,0D,SACZA,EAAWD,EAAaxE,C,GAE3B,UAAC1N,EAAAA,EAAAA,CAAAA,IAAe,EACd,SAAU,CAAE,YAAa,GAAM,KAAM,CAAE,EACvC,SAAS,OACT,OAAQ,IACR,UAAU,+C,SAETiS,C,GAEFG,E,4DC1BCC,EAA+D,CACnE,CAAChN,EAAAA,EAAAA,CAAAA,WAA6B,CAAC,CAAE,CAC/B,KAAM,UAACiN,EAAAA,GAAUA,CAAAA,CAAAA,GACjB,QAAS,gBACX,EACA,CAACjN,EAAAA,EAAAA,CAAAA,kBAAoC,CAAC,CAAE,CACtC,KAAM,UAACkN,EAAAA,GAAeA,CAAAA,CAAAA,GACtB,QAAS,mBACX,EACA,CAAClN,EAAAA,EAAAA,CAAAA,mBAAqC,CAAC,CAAE,CACvC,KAAM,UAACkN,EAAAA,GAAeA,CAAAA,CAAAA,GACtB,QAAS,mBACX,EACA,CAAClN,EAAAA,EAAAA,CAAAA,eAAiC,CAAC,CAAE,CACnC,KAAM,UAACmN,EAAAA,GAAgBA,CAAAA,CAAAA,GACvB,QAAS,sBACX,CACF,EAEMC,EAAwE,CAC5E,CAACpN,EAAAA,EAAAA,CAAAA,WAA6B,CAAC,CAAE,UACjC,CAACA,EAAAA,EAAAA,CAAAA,kBAAoC,CAAC,CAAE,UACxC,CAACA,EAAAA,EAAAA,CAAAA,mBAAqC,CAAC,CAAE,UACzC,CAACA,EAAAA,EAAAA,CAAAA,eAAiC,CAAC,CAAE,OACvC,EAEaqN,EAAU,AAAC,I,IAQXC,EARW,CAAE1d,KAAAA,CAAI,CAAU,GAChC2d,EAASP,CAAa,CAACpd,EAAK,QAClC,AAAK2d,EAKH,WAACrR,EAAAA,EAAGA,CAAAA,CACF,MAAOoR,AAAoB,OAApBA,CAAAA,EAAAA,CAAc,CAAC1d,EAAK,AAAD,GAAnB0d,AAAAA,KAAAA,IAAAA,EAAAA,EAAwB,UAC/B,UAAU,mE,UAETC,EAAO,IAAI,CACZ,UAAC,QAAK,UAAU,W,SAAYxY,EAAAA,CAAAA,CAAAA,CAAM,CAACwY,EAAO,OAAO,C,MAT5C,IAYX,E,cC5CaC,GAAsC,AAAC,I,IAyBtCC,EACAC,EACAC,EA3BsC,CAClDf,MAAAA,CAAK,CACLzQ,YAAAA,CAAW,CACXhL,SAAAA,CAAQ,CACRyc,cAAAA,CAAa,CACbC,cAAAA,CAAa,CACbC,qBAAAA,CAAoB,CACrB,G,MACC,WAAC,OAAI,UAAWrE,IAAI,yBAA0B,iB,UAC5C,WAAC,OAAI,UAAU,8C,UACb,UAAC9O,EAAAA,EAAAA,CAAAA,IAAe,EACd,UAAU,wEACV,SAAU,CAAE,YAAa,GAAM,KAAM,CAAE,E,SAEtCiS,C,GAEFgB,MAAAA,EAAAA,KAAAA,EAAAA,I,GAGH,UAACG,EAAAA,EAAUA,CAAAA,CACT,UAAU,WACV,OAAQ5c,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,UAAU,CAC5B,KAAMA,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,IAAI,CACpB,SAAUA,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,SAAS,CAC7B,MAAO,CACL,KAAMsc,MAAAA,EAAAA,KAAAA,EAAAA,AAAoB,OAApBA,CAAAA,EAAAA,EAAU,UAAU,AAAD,GAAnBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAsB,UAAU,CACtC,KAAMC,MAAAA,EAAAA,KAAAA,EAAAA,AAAoB,OAApBA,CAAAA,EAAAA,EAAU,UAAU,AAAD,GAAnBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAsB,QAAQ,CACpC,KAAMC,MAAAA,EAAAA,KAAAA,EAAAA,AAAoB,OAApBA,CAAAA,EAAAA,EAAU,UAAU,AAAD,GAAnBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAsB,SAAS,AACvC,C,GAGF,WAAC,OACC,UAAWlE,IACT,8CACAoE,G,UAGF,UAAClT,EAAAA,EAAAA,CAAAA,IAAe,EACd,UAAU,+CACV,SAAU,CAAE,YAAa,GAAM,KAAM,CAAE,E,SAEtCwB,C,GAEF2R,MAAAA,EAAAA,KAAAA,EAAAA,I,sBCbME,GAzCK,AAACtI,IAMjB,GAAM,CAAEF,UAAAA,CAAS,CAAEF,SAAAA,CAAQ,CAAE3S,QAAAA,CAAO,CAAEsb,WAAAA,CAAU,CAAE,CAAGvI,EAErD,MACE,UAAC,OACC,UAAWnG,IACT,aACAwI,GAAAA,CAAAA,CAAAA,SAAW,CACXA,GAAAA,CAAAA,CAAAA,QAAU,CACVvC,EACAuC,GAAAA,CAAC,CAAE,UAAoB,OAAXkG,GAAa,EAE3B,QAAStb,E,SAER2S,C,EAGP,EAoBa4I,GAlBa,AAACxI,GAIzB,UAAC,OACC,UAAWnG,IACT,iBACAwI,GAAAA,CAAAA,CAAAA,SAAW,CACXA,GAAAA,CAAAA,CAAAA,QAAU,CACVA,GAAAA,CAAAA,CAAAA,QAAU,CACVrC,EAAM,SAAS,E,SAGhBA,MAAAA,EAAAA,KAAAA,EAAAA,EAAO,QAAQ,A,kBCrCPyI,GAKT,AAAC,I,GAAA,CAAE3I,UAAAA,CAAS,CAAE7S,QAAAA,CAAO,CAAE2S,SAAAA,CAAQ,CAAE,G,MACnC,UAAC,UACC,UAAWmE,IAAItE,GAAAA,CAAAA,CAAAA,cAAqB,CAAEK,GACtC,MAAM,UACN,QAAS7S,E,SAER2S,C,mBCGQ8I,GAAsC1I,AAAAA,IACjD,IAMiB2I,EACMC,EACHC,EAEFC,EAkBOC,EA5BnB,CAAClJ,EAASmJ,EAAW,CAAGvU,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IACvC,MACE,WAAC6T,GAAaA,CAAC,UAAW7I,GAAAA,CAAAA,CAAAA,QAAe,CAAE,WAAW,U,UACpD,WAAC,OAAI,UAAWA,GAAAA,CAAAA,CAAAA,mBAA0B,C,UACxC,UAACwJ,GAAAA,CAEG,MAAO,AAAe,OAAfN,CAAAA,EAAAA,EAAM,SAAS,AAAD,GAAdA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiB,IAAI,CAC5B,YAAa,AAAe,OAAfC,CAAAA,EAAAA,EAAM,SAAS,AAAD,GAAdA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiB,WAAW,CACzC,SAAU,AAAe,OAAfC,CAAAA,EAAAA,EAAM,SAAS,AAAD,GAAdA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiB,SAAS,CACpC,WAAY7I,EAAM,SAAS,CAAC,WAAW,CACvC,OAAQ,AAAsB,OAAtB8I,CAAAA,EAAAA,EAAM,SAAS,CAAC,MAAM,AAAD,GAArBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,CAAwB,CAAC,EAAE,CAAC,GAAG,A,GAG3C,UAAC1R,EAAAA,CAAKA,CAAAA,CAAC,UAAWqI,GAAAA,CAAAA,CAAAA,gBAAuB,C,SACvC,UAACgJ,GAAUA,CACT,QAAS,KACPO,EAAW,GACb,EACA,UAAU,S,SAET3Z,EAAAA,CAAAA,CAAAA,CAAM,CAAC,O,QAIbwQ,EACC,UAACqJ,GAAAA,CACC,UAAWlJ,EAAM,SAAS,CAAC,EAAE,CAC7B,WAAYA,EAAM,SAAS,CAAC,WAAW,CACvC,aAAe,GAA2B3Q,MAAAA,CAAAA,AAAV,OAAf0Z,CAAAA,EAAAA,EAAM,SAAS,AAAD,GAAdA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiB,IAAI,CAAC,KAErC,OAFwC1Z,EAAAA,CAAAA,CAAAA,CAAM,CAC9C,yBACA,KACF,KAAM,IAAM2Z,EAAW,G,GAEvB,K,EAGV,EAEME,GAKD,AAAC,I,IAGYC,EAHZ,CAAEC,aAAAA,CAAY,CAAEC,KAAAA,CAAI,CAAE3E,UAAAA,CAAS,CAAEC,WAAAA,CAAU,CAAE,GAC3C,CAACuC,EAAOoC,EAAS,CAAG7U,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS2U,GAC7B,CAAEG,OAAAA,CAAM,CAAE,CAAGC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IACb/a,EAAU0a,MAAAA,EAAAA,KAAAA,EAAAA,AAAW,OAAXA,CAAAA,EAAAA,CAAQ,CAAC,EAAE,AAAD,GAAVA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAa,EAAE,CAC/B,MACE,UAACM,EAAAA,EAAKA,CAAAA,CACJ,KAAK,QACL,MAAOpa,EAAAA,CAAAA,CAAAA,CAAM,CAAC,8BACd,QAAS,GACT,KAAI,QAAE,YACJ,GAAI,CACF,MAAMqa,EAAAA,CAAAA,CAAAA,sBAA8B,CAAC,CACnC,WAAYhF,EACZ,YAAaC,EACb,SAAUlW,EACV,KAAMyY,CACR,GACA/X,EAAAA,EAAAA,CAAAA,OAAa,CAACE,EAAAA,CAAAA,CAAAA,CAAM,CAAC,iBACrBga,GACF,CAAE,MAAOjI,EAAK,CACZiF,QAAQ,KAAK,CAAC,yBAA0BjF,GACxCjS,EAAAA,EAAAA,CAAAA,KAAW,CAACE,EAAAA,CAAAA,CAAAA,CAAM,CAAC,eACrB,CACF,GACA,SAAUga,EACV,WAAYha,EAAAA,CAAAA,CAAAA,CAAM,CAAC,UACnB,OAAQA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,W,SAEf,WAAC+H,EAAAA,CAAKA,CAAAA,CAAC,SAAQ,GAAC,QAAS,EAAG,UAAU,S,UACpC,WAACA,EAAAA,CAAKA,CAAAA,CAAC,UAAU,S,UACf,UAAC,QAAK,UAAU,4C,SACb/H,EAAAA,CAAAA,CAAAA,CAAM,CAAC,6B,GAEV,UAAC,QAAK,UAAU,kB,SAAkB,G,MAEpC,UAACsa,EAAAA,EAAKA,CAAAA,CACJ,UAAU,SACV,YAAY,GACZ,aAAcP,EACd,SAAUjI,AAAAA,IACRmI,EAASnI,EACX,C,OAKV,EAEayI,GAAuB,IAClC,UAACpB,GAAqBA,CAAC,UAAWzE,IAAI,YAAatE,GAAAA,CAAAA,CAAAA,QAAe,C,GAGvDwJ,GAMT,AAAC,I,GAAA,CACH/B,MAAAA,CAAK,CACL2C,OAAAA,CAAM,CACNpT,YAAAA,CAAW,CACXkO,WAAAA,CAAU,CACVlZ,SAAAA,CAAQ,CACRqe,sBAAAA,CAAqB,CACrB1B,qBAAAA,CAAoB,CACrB,G,MACC,WAAC,O,UACC,WAAC,OAAI,UAAU,0D,UACb,UAAC2B,EAAAA,EAAKA,CAAAA,CACJ,QAAS,GACT,IAAKF,EACL,UAAU,gBACV,OAAO,0C,GAERC,MAAAA,EAAAA,KAAAA,EAAAA,I,GAEH,UAAChC,GAAQA,CAELZ,MAAAA,EACAzQ,YAAAA,EACAhL,SAAAA,EACA,cAAe,IACbkZ,EAAa,UAACgD,EAAOA,CAAC,KAAMhD,C,GAAiB,KAC/C,cAAelF,GAAAA,CAAAA,CAAAA,WAAkB,CACjC2I,qBAAAA,C,OlBzID,IAAK7I,IACoB,CADpBA,E,GACoB,sBAEoB,2BAEI,+BAEY,6BAPxDA,G,emBWCyK,GAAkChK,AAAAA,GAC7C,UAACsI,GAAaA,CAAC,UAAW7I,GAAAA,CAAAA,CAAAA,MAAa,CAAE,WAAW,U,SAClD,WAAC,OAAI,UAAWA,GAAAA,CAAAA,CAAAA,iBAAwB,C,UACtC,UAACwK,GAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,CAAAA,EAAmBjK,IAEpB,UAAC5I,EAAAA,CAAKA,CAAAA,CACJ,UAAW2M,IAAItE,GAAAA,CAAAA,CAAAA,gBAAuB,CAAE,CACtC,CAACA,GAAAA,CAAAA,CAAAA,kBAAyB,CAAC,CACzBO,EAAM,WAAW,EAAI,CAACA,EAAM,mBAAmB,AACnD,G,SAEC,CAACA,EAAM,WAAW,EAAIA,EAAM,mBAAmB,CAC9C,UAACyI,GAAUA,CACT,QAAS,KACPtZ,EAAAA,EAAAA,CAAAA,OAAa,CAACE,EAAAA,CAAAA,CAAAA,CAAM,CAAC,0BACvB,E,SAECA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,uB,GAER,I,QAMC6a,GAAqB,IAChC,UAAC1B,GAAqBA,CAAC,UAAWzE,IAAI,YAAatE,GAAAA,CAAAA,CAAAA,MAAa,C,GAG5DwK,GAAsCjK,AAAAA,IAC1C,IAmCW2I,EAKIC,EACMC,EACHE,EAXlB,MACE,WAAC,O,UACC,UAAC5O,EAAAA,EAAMA,CAAAA,CACL,UAAWsF,GAAAA,CAAAA,CAAAA,cAAqB,CAChC,IAAG,AAAiB,OAAfkJ,CAAAA,EAAAA,EAAM,SAAS,AAAD,GAAdA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiB,QAAQ,CAC9B,MAAM,Q,GAER,UAACb,GAAQA,CAEL,MAAO,AAAe,OAAfc,CAAAA,EAAAA,EAAM,SAAS,AAAD,GAAdA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiB,IAAI,CAC5B,YAAa,AAAe,OAAfC,CAAAA,EAAAA,EAAM,SAAS,AAAD,GAAdA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiB,WAAW,CACzC,SAAU,AAAe,OAAfE,CAAAA,EAAAA,EAAM,SAAS,AAAD,GAAdA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiB,SAAS,CACpC,SAAU/I,EAAM,YAAY,CAAC,SAAS,CACtCkI,cA5Cc,IACpB,AACElI,EAAM,YAAY,CAAC,SAAS,GAAKT,GAAAA,QAAuB,EACxDS,EAAM,YAAY,CAAC,SAAS,GAAKT,GAAAA,SAAwB,CAGvD,UAAC/I,EAAAA,EAAGA,CAAAA,CACF,MAAO,SACP,UAAU,mE,SAEV,UAAC,QAAK,UAAU,W,SACbnH,EAAAA,CAAAA,CAAAA,CAAM,CAAC,4B,KAIL2Q,EAAM,YAAY,CAAC,SAAS,GAAKT,GAAAA,UAAyB,CAEjE,UAACnJ,EAAAA,CAAOA,CAAAA,CAAC,QAAS/G,EAAAA,CAAAA,CAAAA,CAAM,CAAC,iC,SACvB,UAACmH,EAAAA,EAAGA,CAAAA,CACF,MAAO,QACP,UAAU,mE,SAEV,UAAC,QAAK,UAAU,W,SACbnH,EAAAA,CAAAA,CAAAA,CAAM,CAAC,0B,OAMX,I,KAoBX,C,yEC5Fa+K,EAA4B,AAAC,I,GAAA,CAAE+P,OAAAA,CAAM,CAAEnV,KAAAA,CAAI,CAAEoV,MAAAA,CAAK,CAAE,G,MAC/D,WAAC,OAAI,UAAU,mF,UAEK,KAOlB,UAAC,OAAI,UAAU,kD,SACZA,C,6TCfMtO,EAASiH,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EACpB,CAAC,EAA4BC,K,GAA5B,CAAEpD,SAAAA,CAAQ,CAAgB,GAAXyK,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,EAAAA,CAAbzK,W,QACD,UAAC,yBACKyK,GAAAA,CACJ,IAAKrH,EACL,UAAWnJ,IACTwQ,EAAU,SAAS,CACnB,eACA,2BACA,kBACA,uB,SAGDzK,C,MAKM7D,EAASgH,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EACpB,CAAC,EAA4BC,K,GAA5B,CAAEpD,SAAAA,CAAQ,CAAgB,GAAXyK,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,EAAAA,CAAbzK,W,QACD,UAAC,yBACKyK,GAAAA,CACJ,IAAKrH,EACL,UAAWnJ,IACTwQ,EAAU,SAAS,CACnB,gBACA,kBACA,qC,SAGDzK,C,MAKM5D,EAAc+G,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EACzB,CAAC,EAA4BC,K,GAA5B,CAAEpD,SAAAA,CAAQ,CAAgB,GAAXyK,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,EAAAA,CAAbzK,W,QACD,UAAC,yBACKyK,GAAAA,CACJ,IAAKrH,EACL,UAAWnJ,IACTwQ,EAAU,SAAS,CACnB,yBACA,+B,SAGDzK,C,MAKM3D,EAAgB8G,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EAC3B,CAAC,EAA4BC,K,GAA5B,CAAEpD,SAAAA,CAAQ,CAAgB,GAAXyK,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,EAAAA,CAAbzK,W,QACD,UAAC,yBACKyK,GAAAA,CACJ,IAAKrH,EACL,UAAWnJ,IACTwQ,EAAU,SAAS,CACnB,yC,SAGDzK,C,MAKMzD,EAAY4G,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EACvB,CAAC,EAA4BC,K,GAA5B,CAAEpD,SAAAA,CAAQ,CAAgB,GAAXyK,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,EAAAA,CAAbzK,W,QACD,UAAC,yBACKyK,GAAAA,CACJ,IAAKrH,EACL,UAAWnJ,IACTwQ,EAAU,SAAS,CACnB,gBACA,kBACA,qC,SAGDzK,C,MAKMxD,EAAmB2G,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EAC9B,CAAC,EAA4BC,K,GAA5B,CAAEpD,SAAAA,CAAQ,CAAgB,GAAXyK,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,EAAAA,CAAbzK,W,QACD,UAAC,yBACKyK,GAAAA,CACJ,IAAKrH,EACL,UAAWnJ,IAAWwQ,EAAU,SAAS,CAAE,+B,SAE1CzK,C,MAKM/C,EAAkBkG,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EAC7B,CAAC,EAA4BC,K,GAA5B,CAAEpD,SAAAA,CAAQ,CAAgB,GAAXyK,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,EAAAA,CAAbzK,W,QACD,UAAC,yBAAQyK,GAAAA,CAAW,IAAKrH,EAAK,UAAWnJ,IAAWwQ,EAAU,SAAS,E,SACpEzK,C,MAKM7C,EAAUgG,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EACrB,CAAC,EAA4BC,K,GAA5B,CAAEpD,SAAAA,CAAQ,CAAgB,GAAXyK,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,EAAAA,CAAbzK,W,QACD,UAAC,yBACKyK,GAAAA,CACJ,IAAKrH,EACL,UAAWnJ,IACTwQ,EAAU,SAAS,CACnB,YACA,qC,SAGDzK,C,kHC7GMrC,EAA0C,AAAC,I,GAAA,CACtD+M,QAAAA,CAAO,CACPC,UAAAA,EAAY,EAAK,CAClB,G,MACC,WAAC,OAAI,UAAU,sD,UACb,UAACC,EAAAA,GAAYA,CAAAA,CAAC,UAAU,8B,GACxB,UAAC,OAAI,UAAU,0E,SACZnb,EAAAA,CAAAA,CAAAA,CAAM,CACLkb,EAAY,uCAAyC,mB,GAGxDA,EACC,UAAC5W,EAAAA,EAAMA,CAAAA,CACL,MAAM,UACN,KAAM,UAAC8W,EAAAA,GAAYA,CAAAA,CAAAA,GACnB,QAAS,KACPH,MAAAA,GAAAA,GACF,E,SAECjb,EAAAA,CAAAA,CAAAA,CAAM,CAAC,8B,GAER,K,4DChCD,IAAMiN,EAAuB,CAClC,OAAQ,0CACV,C,sKCPIoO,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGhgB,KAAAA,C,uKCf/DggB,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGhgB,KAAAA,C,uKCf/DggB,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGhgB,KAAAA,C,uKCf/DggB,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGhgB,KAAAA,C,uKCf/DggB,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGhgB,KAAAA,C,uKCf/DggB,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKJ,GAAO,EAAI,UAAc,EAAG,UAAc,A,uKCf5DA,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGhgB,KAAAA,C,uKCf/DggB,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGhgB,KAAAA,C,uKCf/DggB,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGhgB,KAAAA,C,uKCf/DggB,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGhgB,KAAAA,C,uKCf/DggB,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGhgB,KAAAA,C,sKCf/DggB,EAAU,CAAC,CAEfA,CAAAA,EAAQ,iBAAiB,CAAG,IAC5BA,EAAQ,aAAa,CAAG,IAElBA,EAAQ,MAAM,CAAG,QAAa,CAAC,KAAM,QAE3CA,EAAQ,MAAM,CAAG,IACjBA,EAAQ,kBAAkB,CAAG,IAEhB,IAAI,GAAO,CAAEA,GAKnB,IAAe,GAAO,EAAI,UAAc,CAAG,UAAc,CAAGhgB,KAAAA,C,6DCvB/DigB,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,8VAA+V,GAAG,EAE3YD,EAAwB,MAAM,CAAG,CAChC,cAAe,qBACf,WAAc,oBACf,EACA,IAAeA,C,8DCRXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,qrBAAsrB,GAAG,EAEluBD,EAAwB,MAAM,CAAG,CAChC,UAAa,mBACb,SAAY,kBACZ,iBAAkB,wBAClB,cAAiB,wBACjB,MAAS,eACT,SAAY,iBACb,EACA,IAAeA,C,8DCZXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,miCAAoiC,GAAG,EAEhlCD,EAAwB,MAAM,CAAG,CAChC,OAAU,gBACV,iBAAkB,wBAClB,cAAiB,wBACjB,gBAAiB,uBACjB,aAAgB,uBAChB,kBAAmB,yBACnB,cAAiB,yBACjB,YAAe,qBACf,cAAe,qBACf,WAAc,oBACf,EACA,IAAeA,C,8DChBXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,yTAA0T,GAAG,EAEtWD,EAAwB,MAAM,CAAG,CAChC,SAAY,kBACZ,mBAAoB,0BACpB,gBAAmB,0BACnB,gBAAiB,uBACjB,aAAgB,uBAChB,YAAe,oBAChB,EACA,IAAeA,C,8DCZXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,2CAA4C,GAAG,EAExFD,EAAwB,MAAM,CAAG,CAChC,KAAQ,aACT,EACA,IAAeA,C,8DCPXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,4LAA6L,GAAG,EAEzOD,EAAwB,MAAM,CAAG,CAChC,eAAgB,sBAChB,YAAe,sBACf,KAAQ,cACR,eAAgB,sBAChB,YAAe,qBAChB,EACA,IAAeA,C,8DCXXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,iXAAkX,GAAG,EAE9ZD,EAAwB,MAAM,CAAG,CAChC,cAAe,qBACf,WAAc,qBACd,eAAgB,sBAChB,YAAe,sBACf,WAAY,kBACZ,QAAW,kBACX,IAAO,aACP,WAAY,kBACZ,QAAW,iBACZ,EACA,IAAeA,C,8DCfXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,2GAA4G,GAAG,EAExJD,EAAwB,MAAM,CAAG,CAChC,oBAAqB,2BACrB,gBAAmB,0BACpB,EACA,IAAeA,C,8DCRXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,gjBAAijB,GAAG,EAE7lBD,EAAwB,MAAM,CAAG,CAChC,MAAS,eACT,KAAQ,cACR,eAAgB,sBAChB,YAAe,qBAChB,EACA,IAAeA,C,8DCVXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,u9BAAw9B,GAAG,EAEpgCD,EAAwB,MAAM,CAAG,CAChC,mBAAoB,0BACpB,gBAAmB,0BACnB,QAAW,iBACX,cAAe,qBACf,WAAc,qBACd,gBAAiB,uBACjB,YAAe,uBACf,4BAA6B,mCAC7B,wBAA2B,kCAC5B,EACA,IAAeA,C,8DCfXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,yDAA0D,GAAG,EAEtGD,EAAwB,MAAM,CAAG,CAChC,mBAAoB,0BACpB,eAAkB,yBACnB,EACA,IAAeA,C,6DCRXA,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,0VAA2V,GAAG,EAEvYD,EAAwB,MAAM,CAAG,CAChC,iBAAkB,wBAClB,cAAiB,wBACjB,QAAW,iBACX,MAAS,eACT,MAAS,cACV,EACA,IAAeA,C"}