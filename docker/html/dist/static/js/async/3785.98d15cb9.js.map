{"version": 3, "file": "static/js/async/3785.98d15cb9.js", "sources": ["webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/extend@3.0.2/node_modules/extend/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/is-buffer@2.0.5/node_modules/is-buffer/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/style-to-object@0.4.4/node_modules/style-to-object/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/comma-separated-tokens@2.0.3/node_modules/comma-separated-tokens/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/micromark-util-sanitize-uri@1.2.0/node_modules/micromark-util-sanitize-uri/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/unist-util-position@4.0.4/node_modules/unist-util-position/lib/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-definitions@5.1.2/node_modules/mdast-util-definitions/lib/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/revert.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/list-item.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/trim-lines@3.0.1/node_modules/trim-lines/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/blockquote.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/break.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/code.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/delete.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/emphasis.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/footnote.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/heading.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/html.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/image-reference.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/image.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/inline-code.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/link-reference.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/link.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/list.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/paragraph.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/root.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/strong.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/table.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/table-cell.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/table-row.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/text.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/state.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/unist-util-generated@2.0.1/node_modules/unist-util-generated/lib/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/mdast-util-to-hast@12.3.0/node_modules/mdast-util-to-hast/lib/footer.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/remark-rehype@10.1.0/node_modules/remark-rehype/lib/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/util/schema.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/util/merge.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/normalize.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/util/info.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/util/types.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/util/defined-info.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/util/create.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/xlink.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/xml.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/util/case-sensitive-transform.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/util/case-insensitive-transform.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/xmlns.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/aria.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/html.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/svg.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/react-markdown@8.0.7_@types+react@18.2.37_react@18.2.0/node_modules/react-markdown/lib/rehype-filter.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/find.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/hast-to-react.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/react-markdown@8.0.7_@types+react@18.2.37_react@18.2.0/node_modules/react-markdown/lib/ast-to-react.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/style-to-object@0.4.4/node_modules/style-to-object/index.mjs", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/hast-util-whitespace@2.0.1/node_modules/hast-util-whitespace/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/react-markdown@8.0.7_@types+react@18.2.37_react@18.2.0/node_modules/react-markdown/lib/react-markdown.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/react-markdown@8.0.7_@types+react@18.2.37_react@18.2.0/node_modules/react-markdown/lib/uri-transformer.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/remark-parse@10.0.2/node_modules/remark-parse/lib/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/space-separated-tokens@2.0.2/node_modules/space-separated-tokens/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/bail@2.0.2/node_modules/bail/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/is-plain-obj@4.1.0/node_modules/is-plain-obj/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/unified@10.1.2/node_modules/unified/lib/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/trough@2.2.0/node_modules/trough/lib/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/vfile-message@3.1.4/node_modules/vfile-message/lib/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/vfile@5.3.7/node_modules/vfile/lib/minpath.browser.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/vfile@5.3.7/node_modules/vfile/lib/minproc.browser.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/vfile@5.3.7/node_modules/vfile/lib/minurl.shared.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/vfile@5.3.7/node_modules/vfile/lib/index.js", "webpack://@coze-studio/app/../../../common/temp/default/node_modules/.pnpm/vfile@5.3.7/node_modules/vfile/lib/minurl.browser.js"], "sourcesContent": ["'use strict';\n\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\nvar defineProperty = Object.defineProperty;\nvar gOPD = Object.getOwnPropertyDescriptor;\n\nvar isArray = function isArray(arr) {\n\tif (typeof Array.isArray === 'function') {\n\t\treturn Array.isArray(arr);\n\t}\n\n\treturn toStr.call(arr) === '[object Array]';\n};\n\nvar isPlainObject = function isPlainObject(obj) {\n\tif (!obj || toStr.call(obj) !== '[object Object]') {\n\t\treturn false;\n\t}\n\n\tvar hasOwnConstructor = hasOwn.call(obj, 'constructor');\n\tvar hasIsPrototypeOf = obj.constructor && obj.constructor.prototype && hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');\n\t// Not own constructor property must be Object\n\tif (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n\t\treturn false;\n\t}\n\n\t// Own properties are enumerated firstly, so to speed up,\n\t// if last one is own, then all properties are own.\n\tvar key;\n\tfor (key in obj) { /**/ }\n\n\treturn typeof key === 'undefined' || hasOwn.call(obj, key);\n};\n\n// If name is '__proto__', and Object.defineProperty is available, define __proto__ as an own property on target\nvar setProperty = function setProperty(target, options) {\n\tif (defineProperty && options.name === '__proto__') {\n\t\tdefineProperty(target, options.name, {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t\tvalue: options.newValue,\n\t\t\twritable: true\n\t\t});\n\t} else {\n\t\ttarget[options.name] = options.newValue;\n\t}\n};\n\n// Return undefined instead of __proto__ if '__proto__' is not an own property\nvar getProperty = function getProperty(obj, name) {\n\tif (name === '__proto__') {\n\t\tif (!hasOwn.call(obj, name)) {\n\t\t\treturn void 0;\n\t\t} else if (gOPD) {\n\t\t\t// In early versions of node, obj['__proto__'] is buggy when obj has\n\t\t\t// __proto__ as an own property. Object.getOwnPropertyDescriptor() works.\n\t\t\treturn gOPD(obj, name).value;\n\t\t}\n\t}\n\n\treturn obj[name];\n};\n\nmodule.exports = function extend() {\n\tvar options, name, src, copy, copyIsArray, clone;\n\tvar target = arguments[0];\n\tvar i = 1;\n\tvar length = arguments.length;\n\tvar deep = false;\n\n\t// Handle a deep copy situation\n\tif (typeof target === 'boolean') {\n\t\tdeep = target;\n\t\ttarget = arguments[1] || {};\n\t\t// skip the boolean and the target\n\t\ti = 2;\n\t}\n\tif (target == null || (typeof target !== 'object' && typeof target !== 'function')) {\n\t\ttarget = {};\n\t}\n\n\tfor (; i < length; ++i) {\n\t\toptions = arguments[i];\n\t\t// Only deal with non-null/undefined values\n\t\tif (options != null) {\n\t\t\t// Extend the base object\n\t\t\tfor (name in options) {\n\t\t\t\tsrc = getProperty(target, name);\n\t\t\t\tcopy = getProperty(options, name);\n\n\t\t\t\t// Prevent never-ending loop\n\t\t\t\tif (target !== copy) {\n\t\t\t\t\t// Recurse if we're merging plain objects or arrays\n\t\t\t\t\tif (deep && copy && (isPlainObject(copy) || (copyIsArray = isArray(copy)))) {\n\t\t\t\t\t\tif (copyIsArray) {\n\t\t\t\t\t\t\tcopyIsArray = false;\n\t\t\t\t\t\t\tclone = src && isArray(src) ? src : [];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tclone = src && isPlainObject(src) ? src : {};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Never move original objects, clone them\n\t\t\t\t\t\tsetProperty(target, { name: name, newValue: extend(deep, clone, copy) });\n\n\t\t\t\t\t// Don't bring in undefined values\n\t\t\t\t\t} else if (typeof copy !== 'undefined') {\n\t\t\t\t\t\tsetProperty(target, { name: name, newValue: copy });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Return the modified object\n\treturn target;\n};\n", "/*!\n * Determine if an object is a Buffer\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n\nmodule.exports = function isBuffer (obj) {\n  return obj != null && obj.constructor != null &&\n    typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj)\n}\n", "var parse = require('inline-style-parser');\n\n/**\n * Parses inline style to object.\n *\n * @example\n * // returns { 'line-height': '42' }\n * StyleToObject('line-height: 42;');\n *\n * @param  {String}      style      - The inline style.\n * @param  {Function}    [iterator] - The iterator function.\n * @return {null|Object}\n */\nfunction StyleToObject(style, iterator) {\n  var output = null;\n  if (!style || typeof style !== 'string') {\n    return output;\n  }\n\n  var declaration;\n  var declarations = parse(style);\n  var hasIterator = typeof iterator === 'function';\n  var property;\n  var value;\n\n  for (var i = 0, len = declarations.length; i < len; i++) {\n    declaration = declarations[i];\n    property = declaration.property;\n    value = declaration.value;\n\n    if (hasIterator) {\n      iterator(property, value, declaration);\n    } else if (value) {\n      output || (output = {});\n      output[property] = value;\n    }\n  }\n\n  return output;\n}\n\nmodule.exports = StyleToObject;\nmodule.exports.default = StyleToObject; // ESM support\n", "/**\n * @typedef Options\n *   Configuration for `stringify`.\n * @property {boolean} [padLeft=true]\n *   Whether to pad a space before a token.\n * @property {boolean} [padRight=false]\n *   Whether to pad a space after a token.\n */\n\n/**\n * @typedef {Options} StringifyOptions\n *   Please use `StringifyOptions` instead.\n */\n\n/**\n * Parse comma-separated tokens to an array.\n *\n * @param {string} value\n *   Comma-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nexport function parse(value) {\n  /** @type {Array<string>} */\n  const tokens = []\n  const input = String(value || '')\n  let index = input.indexOf(',')\n  let start = 0\n  /** @type {boolean} */\n  let end = false\n\n  while (!end) {\n    if (index === -1) {\n      index = input.length\n      end = true\n    }\n\n    const token = input.slice(start, index).trim()\n\n    if (token || !end) {\n      tokens.push(token)\n    }\n\n    start = index + 1\n    index = input.indexOf(',', start)\n  }\n\n  return tokens\n}\n\n/**\n * Serialize an array of strings or numbers to comma-separated tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @param {Options} [options]\n *   Configuration for `stringify` (optional).\n * @returns {string}\n *   Comma-separated tokens.\n */\nexport function stringify(values, options) {\n  const settings = options || {}\n\n  // Ensure the last empty entry is seen.\n  const input = values[values.length - 1] === '' ? [...values, ''] : values\n\n  return input\n    .join(\n      (settings.padRight ? ' ' : '') +\n        ',' +\n        (settings.padLeft === false ? '' : ' ')\n    )\n    .trim()\n}\n", "import {asciiAlphanumeric} from 'micromark-util-character'\nimport {encode} from 'micromark-util-encode'\n/**\n * Make a value safe for injection as a URL.\n *\n * This encodes unsafe characters with percent-encoding and skips already\n * encoded sequences (see `normalizeUri`).\n * Further unsafe characters are encoded as character references (see\n * `micromark-util-encode`).\n *\n * A regex of allowed protocols can be given, in which case the URL is\n * sanitized.\n * For example, `/^(https?|ircs?|mailto|xmpp)$/i` can be used for `a[href]`, or\n * `/^https?$/i` for `img[src]` (this is what `github.com` allows).\n * If the URL includes an unknown protocol (one not matched by `protocol`, such\n * as a dangerous example, `javascript:`), the value is ignored.\n *\n * @param {string | undefined} url\n *   URI to sanitize.\n * @param {RegExp | null | undefined} [protocol]\n *   Allowed protocols.\n * @returns {string}\n *   Sanitized URI.\n */\nexport function sanitizeUri(url, protocol) {\n  const value = encode(normalizeUri(url || ''))\n  if (!protocol) {\n    return value\n  }\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n  if (\n    // If there is no protocol, it’s relative.\n    colon < 0 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash > -1 && colon > slash) ||\n    (questionMark > -1 && colon > questionMark) ||\n    (numberSign > -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    protocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n  return ''\n}\n\n/**\n * Normalize a URL.\n *\n * Encode unsafe characters with percent-encoding, skipping already encoded\n * sequences.\n *\n * @param {string} value\n *   URI to normalize.\n * @returns {string}\n *   Normalized URI.\n */\nexport function normalizeUri(value) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n  let start = 0\n  let skip = 0\n  while (++index < value.length) {\n    const code = value.charCodeAt(index)\n    /** @type {string} */\n    let replace = ''\n\n    // A correct percent encoded value.\n    if (\n      code === 37 &&\n      asciiAlphanumeric(value.charCodeAt(index + 1)) &&\n      asciiAlphanumeric(value.charCodeAt(index + 2))\n    ) {\n      skip = 2\n    }\n    // ASCII.\n    else if (code < 128) {\n      if (!/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(code))) {\n        replace = String.fromCharCode(code)\n      }\n    }\n    // Astral.\n    else if (code > 55295 && code < 57344) {\n      const next = value.charCodeAt(index + 1)\n\n      // A correct surrogate pair.\n      if (code < 56320 && next > 56319 && next < 57344) {\n        replace = String.fromCharCode(code, next)\n        skip = 1\n      }\n      // Lone surrogate.\n      else {\n        replace = '\\uFFFD'\n      }\n    }\n    // Unicode.\n    else {\n      replace = String.fromCharCode(code)\n    }\n    if (replace) {\n      result.push(value.slice(start, index), encodeURIComponent(replace))\n      start = index + skip + 1\n      replace = ''\n    }\n    if (skip) {\n      index += skip\n      skip = 0\n    }\n  }\n  return result.join('') + value.slice(start)\n}\n", "/**\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Get the starting point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointStart = point('start')\n\n/**\n * Get the ending point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointEnd = point('end')\n\n/**\n * Get the positional info of `node`.\n *\n * @param {NodeLike | Node | null | undefined} [node]\n *   Node.\n * @returns {Position}\n *   Position.\n */\nexport function position(node) {\n  return {start: pointStart(node), end: pointEnd(node)}\n}\n\n/**\n * Get the positional info of `node`.\n *\n * @param {'start' | 'end'} type\n *   Side.\n * @returns\n *   Getter.\n */\nfunction point(type) {\n  return point\n\n  /**\n   * Get the point info of `node` at a bound side.\n   *\n   * @param {NodeLike | Node | null | undefined} [node]\n   * @returns {Point}\n   */\n  function point(node) {\n    const point = (node && node.position && node.position[type]) || {}\n\n    // To do: next major: don’t return points when invalid.\n    return {\n      // @ts-expect-error: in practice, null is allowed.\n      line: point.line || null,\n      // @ts-expect-error: in practice, null is allowed.\n      column: point.column || null,\n      // @ts-expect-error: in practice, null is allowed.\n      offset: point.offset > -1 ? point.offset : null\n    }\n  }\n}\n", "/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Definition} Definition\n */\n\n/**\n * @typedef {Root | Content} Node\n *\n * @callback GetDefinition\n *   Get a definition by identifier.\n * @param {string | null | undefined} [identifier]\n *   Identifier of definition.\n * @returns {Definition | null}\n *   Definition corresponding to `identifier` or `null`.\n */\n\nimport {visit} from 'unist-util-visit'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Find definitions in `tree`.\n *\n * Uses CommonMark precedence, which means that earlier definitions are\n * preferred over duplicate later definitions.\n *\n * @param {Node} tree\n *   Tree to check.\n * @returns {GetDefinition}\n *   Getter.\n */\nexport function definitions(tree) {\n  /** @type {Record<string, Definition>} */\n  const cache = Object.create(null)\n\n  if (!tree || !tree.type) {\n    throw new Error('mdast-util-definitions expected node')\n  }\n\n  visit(tree, 'definition', (definition) => {\n    const id = clean(definition.identifier)\n    if (id && !own.call(cache, id)) {\n      cache[id] = definition\n    }\n  })\n\n  return definition\n\n  /** @type {GetDefinition} */\n  function definition(identifier) {\n    const id = clean(identifier)\n    // To do: next major: return `undefined` when not found.\n    return id && own.call(cache, id) ? cache[id] : null\n  }\n}\n\n/**\n * @param {string | null | undefined} [value]\n * @returns {string}\n */\nfunction clean(value) {\n  return String(value || '').toUpperCase()\n}\n", "/**\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('hast').Element} Element\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnoteReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const safeId = normalizeUri(id.toLowerCase())\n  const index = state.footnoteOrder.indexOf(id)\n  /** @type {number} */\n  let counter\n\n  if (index === -1) {\n    state.footnoteOrder.push(id)\n    state.footnoteCounts[id] = 1\n    counter = state.footnoteOrder.length\n  } else {\n    state.footnoteCounts[id]++\n    counter = index + 1\n  }\n\n  const reuseCounter = state.footnoteCounts[id]\n\n  /** @type {Element} */\n  const link = {\n    type: 'element',\n    tagName: 'a',\n    properties: {\n      href: '#' + state.clobberPrefix + 'fn-' + safeId,\n      id:\n        state.clobberPrefix +\n        'fnref-' +\n        safeId +\n        (reuseCounter > 1 ? '-' + reuseCounter : ''),\n      dataFootnoteRef: true,\n      ariaDescribedBy: ['footnote-label']\n    },\n    children: [{type: 'text', value: String(counter)}]\n  }\n  state.patch(node, link)\n\n  /** @type {Element} */\n  const sup = {\n    type: 'element',\n    tagName: 'sup',\n    properties: {},\n    children: [link]\n  }\n  state.patch(node, sup)\n  return state.applyData(node, sup)\n}\n", "/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Reference} Reference\n * @typedef {import('mdast').Root} Root\n *\n * @typedef {import('./state.js').State} State\n */\n\n/**\n * @typedef {Root | Content} Nodes\n * @typedef {Extract<Nodes, Reference>} References\n */\n\n// To do: next major: always return array.\n\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {References} node\n *   Reference node (image, link).\n * @returns {ElementContent | Array<ElementContent>}\n *   hast content.\n */\nexport function revert(state, node) {\n  const subtype = node.referenceType\n  let suffix = ']'\n\n  if (subtype === 'collapsed') {\n    suffix += '[]'\n  } else if (subtype === 'full') {\n    suffix += '[' + (node.label || node.identifier) + ']'\n  }\n\n  if (node.type === 'imageReference') {\n    return {type: 'text', value: '![' + node.alt + suffix}\n  }\n\n  const contents = state.all(node)\n  const head = contents[0]\n\n  if (head && head.type === 'text') {\n    head.value = '[' + head.value\n  } else {\n    contents.unshift({type: 'text', value: '['})\n  }\n\n  const tail = contents[contents.length - 1]\n\n  if (tail && tail.type === 'text') {\n    tail.value += suffix\n  } else {\n    contents.push({type: 'text', value: suffix})\n  }\n\n  return contents\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Parent} Parent\n * @typedef {import('mdast').Root} Root\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * @typedef {Root | Content} Nodes\n * @typedef {Extract<Nodes, Parent>} Parents\n */\n\n/**\n * Turn an mdast `listItem` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ListItem} node\n *   mdast node.\n * @param {Parents | null | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function listItem(state, node, parent) {\n  const results = state.all(node)\n  const loose = parent ? listLoose(parent) : listItem<PERSON>oose(node)\n  /** @type {Properties} */\n  const properties = {}\n  /** @type {Array<ElementContent>} */\n  const children = []\n\n  if (typeof node.checked === 'boolean') {\n    const head = results[0]\n    /** @type {Element} */\n    let paragraph\n\n    if (head && head.type === 'element' && head.tagName === 'p') {\n      paragraph = head\n    } else {\n      paragraph = {type: 'element', tagName: 'p', properties: {}, children: []}\n      results.unshift(paragraph)\n    }\n\n    if (paragraph.children.length > 0) {\n      paragraph.children.unshift({type: 'text', value: ' '})\n    }\n\n    paragraph.children.unshift({\n      type: 'element',\n      tagName: 'input',\n      properties: {type: 'checkbox', checked: node.checked, disabled: true},\n      children: []\n    })\n\n    // According to github-markdown-css, this class hides bullet.\n    // See: <https://github.com/sindresorhus/github-markdown-css>.\n    properties.className = ['task-list-item']\n  }\n\n  let index = -1\n\n  while (++index < results.length) {\n    const child = results[index]\n\n    // Add eols before nodes, except if this is a loose, first paragraph.\n    if (\n      loose ||\n      index !== 0 ||\n      child.type !== 'element' ||\n      child.tagName !== 'p'\n    ) {\n      children.push({type: 'text', value: '\\n'})\n    }\n\n    if (child.type === 'element' && child.tagName === 'p' && !loose) {\n      children.push(...child.children)\n    } else {\n      children.push(child)\n    }\n  }\n\n  const tail = results[results.length - 1]\n\n  // Add a final eol.\n  if (tail && (loose || tail.type !== 'element' || tail.tagName !== 'p')) {\n    children.push({type: 'text', value: '\\n'})\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'li', properties, children}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * @param {Parents} node\n * @return {Boolean}\n */\nfunction listLoose(node) {\n  let loose = false\n  if (node.type === 'list') {\n    loose = node.spread || false\n    const children = node.children\n    let index = -1\n\n    while (!loose && ++index < children.length) {\n      loose = listItemLoose(children[index])\n    }\n  }\n\n  return loose\n}\n\n/**\n * @param {ListItem} node\n * @return {Boolean}\n */\nfunction listItemLoose(node) {\n  const spread = node.spread\n\n  return spread === undefined || spread === null\n    ? node.children.length > 1\n    : spread\n}\n", "const tab = 9 /* `\\t` */\nconst space = 32 /* ` ` */\n\n/**\n * Remove initial and final spaces and tabs at the line breaks in `value`.\n * Does not trim initial and final spaces and tabs of the value itself.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Trimmed value.\n */\nexport function trimLines(value) {\n  const source = String(value)\n  const search = /\\r?\\n|\\r/g\n  let match = search.exec(source)\n  let last = 0\n  /** @type {Array<string>} */\n  const lines = []\n\n  while (match) {\n    lines.push(\n      trimLine(source.slice(last, match.index), last > 0, true),\n      match[0]\n    )\n\n    last = match.index + match[0].length\n    match = search.exec(source)\n  }\n\n  lines.push(trimLine(source.slice(last), last > 0, false))\n\n  return lines.join('')\n}\n\n/**\n * @param {string} value\n *   Line to trim.\n * @param {boolean} start\n *   Whether to trim the start of the line.\n * @param {boolean} end\n *   Whether to trim the end of the line.\n * @returns {string}\n *   Trimmed line.\n */\nfunction trimLine(value, start, end) {\n  let startIndex = 0\n  let endIndex = value.length\n\n  if (start) {\n    let code = value.codePointAt(startIndex)\n\n    while (code === tab || code === space) {\n      startIndex++\n      code = value.codePointAt(startIndex)\n    }\n  }\n\n  if (end) {\n    let code = value.codePointAt(endIndex - 1)\n\n    while (code === tab || code === space) {\n      endIndex--\n      code = value.codePointAt(endIndex - 1)\n    }\n  }\n\n  return endIndex > startIndex ? value.slice(startIndex, endIndex) : ''\n}\n", "import {blockquote} from './blockquote.js'\nimport {hardBreak} from './break.js'\nimport {code} from './code.js'\nimport {strikethrough} from './delete.js'\nimport {emphasis} from './emphasis.js'\nimport {footnoteReference} from './footnote-reference.js'\nimport {footnote} from './footnote.js'\nimport {heading} from './heading.js'\nimport {html} from './html.js'\nimport {imageReference} from './image-reference.js'\nimport {image} from './image.js'\nimport {inlineCode} from './inline-code.js'\nimport {linkReference} from './link-reference.js'\nimport {link} from './link.js'\nimport {listItem} from './list-item.js'\nimport {list} from './list.js'\nimport {paragraph} from './paragraph.js'\nimport {root} from './root.js'\nimport {strong} from './strong.js'\nimport {table} from './table.js'\nimport {tableRow} from './table-row.js'\nimport {tableCell} from './table-cell.js'\nimport {text} from './text.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/**\n * Default handlers for nodes.\n */\nexport const handlers = {\n  blockquote,\n  break: hardBreak,\n  code,\n  delete: strikethrough,\n  emphasis,\n  footnoteReference,\n  footnote,\n  heading,\n  html,\n  imageReference,\n  image,\n  inlineCode,\n  linkReference,\n  link,\n  listItem,\n  list,\n  paragraph,\n  root,\n  strong,\n  table,\n  tableCell,\n  tableRow,\n  text,\n  thematicBreak,\n  toml: ignore,\n  yaml: ignore,\n  definition: ignore,\n  footnoteDefinition: ignore\n}\n\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n  // To do: next major: return `undefined`.\n  return null\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `blockquote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Blockquote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function blockquote(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'blockquote',\n    properties: {},\n    children: state.wrap(state.all(node), true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `break` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Break} node\n *   mdast node.\n * @returns {Array<Element | Text>}\n *   hast element content.\n */\nexport function hardBreak(state, node) {\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'br', properties: {}, children: []}\n  state.patch(node, result)\n  return [state.applyData(node, result), {type: 'text', value: '\\n'}]\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n\n */\n\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function code(state, node) {\n  const value = node.value ? node.value + '\\n' : ''\n  // To do: next major, use `node.lang` w/o regex, the splitting’s been going\n  // on for years in remark now.\n  const lang = node.lang ? node.lang.match(/^[^ \\t]+(?=[ \\t]|$)/) : null\n  /** @type {Properties} */\n  const properties = {}\n\n  if (lang) {\n    properties.className = ['language-' + lang]\n  }\n\n  // Create `<code>`.\n  /** @type {Element} */\n  let result = {\n    type: 'element',\n    tagName: 'code',\n    properties,\n    children: [{type: 'text', value}]\n  }\n\n  if (node.meta) {\n    result.data = {meta: node.meta}\n  }\n\n  state.patch(node, result)\n  result = state.applyData(node, result)\n\n  // Create `<pre>`.\n  result = {type: 'element', tagName: 'pre', properties: {}, children: [result]}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Delete} Delete\n * @typedef {import('../state.js').State} State\n\n */\n\n/**\n * Turn an mdast `delete` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Delete} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function strikethrough(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'del',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `emphasis` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Emphasis} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function emphasis(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'em',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Footnote} Footnote\n * @typedef {import('../state.js').State} State\n */\n\nimport {footnoteReference} from './footnote-reference.js'\n\n// To do: when both:\n// * <https://github.com/micromark/micromark-extension-footnote>\n// * <https://github.com/syntax-tree/mdast-util-footnote>\n// …are archived, remove this (also from mdast).\n// These inline notes are not used in GFM.\n\n/**\n * Turn an mdast `footnote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Footnote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnote(state, node) {\n  const footnoteById = state.footnoteById\n  let no = 1\n\n  while (no in footnoteById) no++\n\n  const identifier = String(no)\n\n  footnoteById[identifier] = {\n    type: 'footnoteDefinition',\n    identifier,\n    children: [{type: 'paragraph', children: node.children}],\n    position: node.position\n  }\n\n  return footnoteReference(state, {\n    type: 'footnoteReference',\n    identifier,\n    position: node.position\n  })\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `heading` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Heading} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function heading(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'h' + node.depth,\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').HTML} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */\n\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Raw | Element | null}\n *   hast node.\n */\nexport function html(state, node) {\n  if (state.dangerous) {\n    /** @type {Raw} */\n    const result = {type: 'raw', value: node.value}\n    state.patch(node, result)\n    return state.applyData(node, result)\n  }\n\n  // To do: next major: return `undefined`.\n  return null\n}\n", "/**\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {ElementContent | Array<ElementContent>}\n *   hast node.\n */\nexport function imageReference(state, node) {\n  const def = state.definition(node.identifier)\n\n  if (!def) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(def.url || ''), alt: node.alt}\n\n  if (def.title !== null && def.title !== undefined) {\n    properties.title = def.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `image` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Image} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function image(state, node) {\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(node.url)}\n\n  if (node.alt !== null && node.alt !== undefined) {\n    properties.alt = node.alt\n  }\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `inlineCode` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {InlineCode} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function inlineCode(state, node) {\n  /** @type {Text} */\n  const text = {type: 'text', value: node.value.replace(/\\r?\\n|\\r/g, ' ')}\n  state.patch(node, text)\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'code',\n    properties: {},\n    children: [text]\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {ElementContent | Array<ElementContent>}\n *   hast node.\n */\nexport function linkReference(state, node) {\n  const def = state.definition(node.identifier)\n\n  if (!def) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(def.url || '')}\n\n  if (def.title !== null && def.title !== undefined) {\n    properties.title = def.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `link` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Link} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function link(state, node) {\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(node.url)}\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').List} List\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `list` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {List} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function list(state, node) {\n  /** @type {Properties} */\n  const properties = {}\n  const results = state.all(node)\n  let index = -1\n\n  if (typeof node.start === 'number' && node.start !== 1) {\n    properties.start = node.start\n  }\n\n  // Like GitHub, add a class for custom styling.\n  while (++index < results.length) {\n    const child = results[index]\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'li' &&\n      child.properties &&\n      Array.isArray(child.properties.className) &&\n      child.properties.className.includes('task-list-item')\n    ) {\n      properties.className = ['contains-task-list']\n      break\n    }\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: node.ordered ? 'ol' : 'ul',\n    properties,\n    children: state.wrap(results, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `paragraph` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Paragraph} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function paragraph(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'p',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `root` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastRoot} node\n *   mdast node.\n * @returns {HastRoot | HastElement}\n *   hast node.\n */\nexport function root(state, node) {\n  /** @type {HastRoot} */\n  const result = {type: 'root', children: state.wrap(state.all(node))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `strong` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Strong} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function strong(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'strong',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Table} Table\n * @typedef {import('../state.js').State} State\n */\n\nimport {pointStart, pointEnd} from 'unist-util-position'\n\n/**\n * Turn an mdast `table` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Table} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function table(state, node) {\n  const rows = state.all(node)\n  const firstRow = rows.shift()\n  /** @type {Array<Element>} */\n  const tableContent = []\n\n  if (firstRow) {\n    /** @type {Element} */\n    const head = {\n      type: 'element',\n      tagName: 'thead',\n      properties: {},\n      children: state.wrap([firstRow], true)\n    }\n    state.patch(node.children[0], head)\n    tableContent.push(head)\n  }\n\n  if (rows.length > 0) {\n    /** @type {Element} */\n    const body = {\n      type: 'element',\n      tagName: 'tbody',\n      properties: {},\n      children: state.wrap(rows, true)\n    }\n\n    const start = pointStart(node.children[1])\n    const end = pointEnd(node.children[node.children.length - 1])\n    if (start.line && end.line) body.position = {start, end}\n    tableContent.push(body)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'table',\n    properties: {},\n    children: state.wrap(tableContent, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `tableCell` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableCell} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function tableCell(state, node) {\n  // Note: this function is normally not called: see `table-row` for how rows\n  // and their cells are compiled.\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'td', // Assume body cell.\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Parent} Parent\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * @typedef {Root | Content} Nodes\n * @typedef {Extract<Nodes, Parent>} Parents\n */\n\n/**\n * Turn an mdast `tableRow` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableRow} node\n *   mdast node.\n * @param {Parents | null | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function tableRow(state, node, parent) {\n  const siblings = parent ? parent.children : undefined\n  // Generate a body row when without parent.\n  const rowIndex = siblings ? siblings.indexOf(node) : 1\n  const tagName = rowIndex === 0 ? 'th' : 'td'\n  const align = parent && parent.type === 'table' ? parent.align : undefined\n  const length = align ? align.length : node.children.length\n  let cellIndex = -1\n  /** @type {Array<ElementContent>} */\n  const cells = []\n\n  while (++cellIndex < length) {\n    // Note: can also be undefined.\n    const cell = node.children[cellIndex]\n    /** @type {Properties} */\n    const properties = {}\n    const alignValue = align ? align[cellIndex] : undefined\n\n    if (alignValue) {\n      properties.align = alignValue\n    }\n\n    /** @type {Element} */\n    let result = {type: 'element', tagName, properties, children: []}\n\n    if (cell) {\n      result.children = state.all(cell)\n      state.patch(cell, result)\n      result = state.applyData(node, result)\n    }\n\n    cells.push(result)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'tr',\n    properties: {},\n    children: state.wrap(cells, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('mdast').Text} MdastText\n * @typedef {import('../state.js').State} State\n */\n\nimport {trimLines} from 'trim-lines'\n\n/**\n * Turn an mdast `text` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastText} node\n *   mdast node.\n * @returns {HastText | HastElement}\n *   hast node.\n */\nexport function text(state, node) {\n  /** @type {HastText} */\n  const result = {type: 'text', value: trimLines(String(node.value))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `thematicBreak` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ThematicBreak} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function thematicBreak(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'hr',\n    properties: {},\n    children: []\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Content} HastContent\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Content} MdastContent\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Parent} MdastParent\n * @typedef {import('mdast').Root} MdastRoot\n */\n\n/**\n * @typedef {HastRoot | HastContent} HastNodes\n * @typedef {MdastRoot | MdastContent} MdastNodes\n * @typedef {Extract<MdastNodes, MdastParent>} MdastParents\n *\n * @typedef EmbeddedHastFields\n *   hast fields.\n * @property {string | null | undefined} [hName]\n *   Generate a specific element with this tag name instead.\n * @property {HastProperties | null | undefined} [hProperties]\n *   Generate an element with these properties instead.\n * @property {Array<HastElementContent> | null | undefined} [hChildren]\n *   Generate an element with this content instead.\n *\n * @typedef {Record<string, unknown> & EmbeddedHastFields} MdastData\n *   mdast data with embedded hast fields.\n *\n * @typedef {MdastNodes & {data?: MdastData | null | undefined}} MdastNodeWithData\n *   mdast node with embedded hast data.\n *\n * @typedef PointLike\n *   Point-like value.\n * @property {number | null | undefined} [line]\n *   Line.\n * @property {number | null | undefined} [column]\n *   Column.\n * @property {number | null | undefined} [offset]\n *   Offset.\n *\n * @typedef PositionLike\n *   Position-like value.\n * @property {PointLike | null | undefined} [start]\n *   Point-like value.\n * @property {PointLike | null | undefined} [end]\n *   Point-like value.\n *\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | null | undefined} parent\n *   Parent of `node`.\n * @returns {HastElementContent | Array<HastElementContent> | null | undefined}\n *   hast node.\n *\n * @callback HFunctionProps\n *   Signature of `state` for when props are passed.\n * @param {MdastNodes | PositionLike | null | undefined} node\n *   mdast node or unist position.\n * @param {string} tagName\n *   HTML tag name.\n * @param {HastProperties} props\n *   Properties.\n * @param {Array<HastElementContent> | null | undefined} [children]\n *   hast content.\n * @returns {HastElement}\n *   Compiled element.\n *\n * @callback HFunctionNoProps\n *   Signature of `state` for when no props are passed.\n * @param {MdastNodes | PositionLike | null | undefined} node\n *   mdast node or unist position.\n * @param {string} tagName\n *   HTML tag name.\n * @param {Array<HastElementContent> | null | undefined} [children]\n *   hast content.\n * @returns {HastElement}\n *   Compiled element.\n *\n * @typedef HFields\n *   Info on `state`.\n * @property {boolean} dangerous\n *   Whether HTML is allowed.\n * @property {string} clobberPrefix\n *   Prefix to use to prevent DOM clobbering.\n * @property {string} footnoteLabel\n *   Label to use to introduce the footnote section.\n * @property {string} footnoteLabelTagName\n *   HTML used for the footnote label.\n * @property {HastProperties} footnoteLabelProperties\n *   Properties on the HTML tag used for the footnote label.\n * @property {string} footnoteBackLabel\n *   Label to use from backreferences back to their footnote call.\n * @property {(identifier: string) => MdastDefinition | null} definition\n *   Definition cache.\n * @property {Record<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Record<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {Handler} unknownHandler\n *   Handler for any none not in `passThrough` or otherwise handled.\n * @property {(from: MdastNodes, node: HastNodes) => void} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => Type | HastElement} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {(node: MdastNodes, parent: MdastParents | null | undefined) => HastElementContent | Array<HastElementContent> | null | undefined} one\n *   Transform an mdast node to hast.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastContent>(nodes: Array<Type>, loose?: boolean | null | undefined) => Array<Type | HastText>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n * @property {(left: MdastNodeWithData | PositionLike | null | undefined, right: HastElementContent) => HastElementContent} augment\n *   Like `state` but lower-level and usable on non-elements.\n *   Deprecated: use `patch` and `applyData`.\n * @property {Array<string>} passThrough\n *   List of node types to pass through untouched (except for their children).\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree.\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` attribute on footnotes to prevent it from\n *   *clobbering*.\n * @property {string | null | undefined} [footnoteBackLabel='Back to content']\n *   Label to use from backreferences back to their footnote call (affects\n *   screen readers).\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Label to use for the footnotes section (affects screen readers).\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (note that `id: 'footnote-label'`\n *   is always added as footnote calls use it with `aria-describedby` to\n *   provide an accessible label).\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   Tag name to use for the footnote label.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes.\n * @property {Array<string> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes.\n *\n * @typedef {Record<string, Handler>} Handlers\n *   Handle nodes.\n *\n * @typedef {HFunctionProps & HFunctionNoProps & HFields} State\n *   Info passed around.\n */\n\nimport {visit} from 'unist-util-visit'\nimport {position, pointStart, pointEnd} from 'unist-util-position'\nimport {generated} from 'unist-util-generated'\nimport {definitions} from 'mdast-util-definitions'\nimport {handlers} from './handlers/index.js'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {State}\n *   `state` function.\n */\nexport function createState(tree, options) {\n  const settings = options || {}\n  const dangerous = settings.allowDangerousHtml || false\n  /** @type {Record<string, MdastFootnoteDefinition>} */\n  const footnoteById = {}\n\n  // To do: next major: add `options` to state, remove:\n  // `dangerous`, `clobberPrefix`, `footnoteLabel`, `footnoteLabelTagName`,\n  // `footnoteLabelProperties`, `footnoteBackLabel`, `passThrough`,\n  // `unknownHandler`.\n\n  // To do: next major: move to `state.options.allowDangerousHtml`.\n  state.dangerous = dangerous\n  // To do: next major: move to `state.options`.\n  state.clobberPrefix =\n    settings.clobberPrefix === undefined || settings.clobberPrefix === null\n      ? 'user-content-'\n      : settings.clobberPrefix\n  // To do: next major: move to `state.options`.\n  state.footnoteLabel = settings.footnoteLabel || 'Footnotes'\n  // To do: next major: move to `state.options`.\n  state.footnoteLabelTagName = settings.footnoteLabelTagName || 'h2'\n  // To do: next major: move to `state.options`.\n  state.footnoteLabelProperties = settings.footnoteLabelProperties || {\n    className: ['sr-only']\n  }\n  // To do: next major: move to `state.options`.\n  state.footnoteBackLabel = settings.footnoteBackLabel || 'Back to content'\n  // To do: next major: move to `state.options`.\n  state.unknownHandler = settings.unknownHandler\n  // To do: next major: move to `state.options`.\n  state.passThrough = settings.passThrough\n\n  state.handlers = {...handlers, ...settings.handlers}\n\n  // To do: next major: replace utility with `definitionById` object, so we\n  // only walk once (as we need footnotes too).\n  state.definition = definitions(tree)\n  state.footnoteById = footnoteById\n  /** @type {Array<string>} */\n  state.footnoteOrder = []\n  /** @type {Record<string, number>} */\n  state.footnoteCounts = {}\n\n  state.patch = patch\n  state.applyData = applyData\n  state.one = oneBound\n  state.all = allBound\n  state.wrap = wrap\n  // To do: next major: remove `augment`.\n  state.augment = augment\n\n  visit(tree, 'footnoteDefinition', (definition) => {\n    const id = String(definition.identifier).toUpperCase()\n\n    // Mimick CM behavior of link definitions.\n    // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/8290999/index.js#L26>.\n    if (!own.call(footnoteById, id)) {\n      footnoteById[id] = definition\n    }\n  })\n\n  // @ts-expect-error Hush, it’s fine!\n  return state\n\n  /**\n   * Finalise the created `right`, a hast node, from `left`, an mdast node.\n   *\n   * @param {MdastNodeWithData | PositionLike | null | undefined} left\n   * @param {HastElementContent} right\n   * @returns {HastElementContent}\n   */\n  /* c8 ignore start */\n  // To do: next major: remove.\n  function augment(left, right) {\n    // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n    if (left && 'data' in left && left.data) {\n      /** @type {MdastData} */\n      const data = left.data\n\n      if (data.hName) {\n        if (right.type !== 'element') {\n          right = {\n            type: 'element',\n            tagName: '',\n            properties: {},\n            children: []\n          }\n        }\n\n        right.tagName = data.hName\n      }\n\n      if (right.type === 'element' && data.hProperties) {\n        right.properties = {...right.properties, ...data.hProperties}\n      }\n\n      if ('children' in right && right.children && data.hChildren) {\n        right.children = data.hChildren\n      }\n    }\n\n    if (left) {\n      const ctx = 'type' in left ? left : {position: left}\n\n      if (!generated(ctx)) {\n        // @ts-expect-error: fine.\n        right.position = {start: pointStart(ctx), end: pointEnd(ctx)}\n      }\n    }\n\n    return right\n  }\n  /* c8 ignore stop */\n\n  /**\n   * Create an element for `node`.\n   *\n   * @type {HFunctionProps}\n   */\n  /* c8 ignore start */\n  // To do: next major: remove.\n  function state(node, tagName, props, children) {\n    if (Array.isArray(props)) {\n      children = props\n      props = {}\n    }\n\n    // @ts-expect-error augmenting an element yields an element.\n    return augment(node, {\n      type: 'element',\n      tagName,\n      properties: props || {},\n      children: children || []\n    })\n  }\n  /* c8 ignore stop */\n\n  /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | null | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {HastElementContent | Array<HastElementContent> | null | undefined}\n   *   Resulting hast node.\n   */\n  function oneBound(node, parent) {\n    // @ts-expect-error: that’s a state :)\n    return one(state, node, parent)\n  }\n\n  /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */\n  function allBound(parent) {\n    // @ts-expect-error: that’s a state :)\n    return all(state, parent)\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {void}\n *   Nothing.\n */\nfunction patch(from, to) {\n  if (from.position) to.position = position(from)\n}\n\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {Type | HastElement}\n *   Nothing.\n */\nfunction applyData(from, to) {\n  /** @type {Type | HastElement} */\n  let result = to\n\n  // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n  if (from && from.data) {\n    const hName = from.data.hName\n    const hChildren = from.data.hChildren\n    const hProperties = from.data.hProperties\n\n    if (typeof hName === 'string') {\n      // Transforming the node resulted in an element with a different name\n      // than wanted:\n      if (result.type === 'element') {\n        result.tagName = hName\n      }\n      // Transforming the node resulted in a non-element, which happens for\n      // raw, text, and root nodes (unless custom handlers are passed).\n      // The intent is likely to keep the content around (otherwise: pass\n      // `hChildren`).\n      else {\n        result = {\n          type: 'element',\n          tagName: hName,\n          properties: {},\n          children: []\n        }\n\n        // To do: next major: take the children from the `root`, or inject the\n        // raw/text/comment or so into the element?\n        // if ('children' in node) {\n        //   // @ts-expect-error: assume `children` are allowed in elements.\n        //   result.children = node.children\n        // } else {\n        //   // @ts-expect-error: assume `node` is allowed in elements.\n        //   result.children.push(node)\n        // }\n      }\n    }\n\n    if (result.type === 'element' && hProperties) {\n      result.properties = {...result.properties, ...hProperties}\n    }\n\n    if (\n      'children' in result &&\n      result.children &&\n      hChildren !== null &&\n      hChildren !== undefined\n    ) {\n      // @ts-expect-error: assume valid children are defined.\n      result.children = hChildren\n    }\n  }\n\n  return result\n}\n\n/**\n * Transform an mdast node into a hast node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   mdast node.\n * @param {MdastParents | null | undefined} [parent]\n *   Parent of `node`.\n * @returns {HastElementContent | Array<HastElementContent> | null | undefined}\n *   Resulting hast node.\n */\n// To do: next major: do not expose, keep bound.\nexport function one(state, node, parent) {\n  const type = node && node.type\n\n  // Fail on non-nodes.\n  if (!type) {\n    throw new Error('Expected node, got `' + node + '`')\n  }\n\n  if (own.call(state.handlers, type)) {\n    return state.handlers[type](state, node, parent)\n  }\n\n  if (state.passThrough && state.passThrough.includes(type)) {\n    // To do: next major: deep clone.\n    // @ts-expect-error: types of passed through nodes are expected to be added manually.\n    return 'children' in node ? {...node, children: all(state, node)} : node\n  }\n\n  if (state.unknownHandler) {\n    return state.unknownHandler(state, node, parent)\n  }\n\n  return defaultUnknownHandler(state, node)\n}\n\n/**\n * Transform the children of an mdast node into hast nodes.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} parent\n *   mdast node to compile\n * @returns {Array<HastElementContent>}\n *   Resulting hast nodes.\n */\n// To do: next major: do not expose, keep bound.\nexport function all(state, parent) {\n  /** @type {Array<HastElementContent>} */\n  const values = []\n\n  if ('children' in parent) {\n    const nodes = parent.children\n    let index = -1\n    while (++index < nodes.length) {\n      const result = one(state, nodes[index], parent)\n\n      // To do: see if we van clean this? Can we merge texts?\n      if (result) {\n        if (index && nodes[index - 1].type === 'break') {\n          if (!Array.isArray(result) && result.type === 'text') {\n            result.value = result.value.replace(/^\\s+/, '')\n          }\n\n          if (!Array.isArray(result) && result.type === 'element') {\n            const head = result.children[0]\n\n            if (head && head.type === 'text') {\n              head.value = head.value.replace(/^\\s+/, '')\n            }\n          }\n        }\n\n        if (Array.isArray(result)) {\n          values.push(...result)\n        } else {\n          values.push(result)\n        }\n      }\n    }\n  }\n\n  return values\n}\n\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastText | HastElement}\n *   Resulting hast node.\n */\nfunction defaultUnknownHandler(state, node) {\n  const data = node.data || {}\n  /** @type {HastText | HastElement} */\n  const result =\n    'value' in node &&\n    !(own.call(data, 'hProperties') || own.call(data, 'hChildren'))\n      ? {type: 'text', value: node.value}\n      : {\n          type: 'element',\n          tagName: 'div',\n          properties: {},\n          children: all(state, node)\n        }\n\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | null | undefined} [loose=false]\n *   Whether to add line endings at start and end.\n * @returns {Array<Type | HastText>}\n *   Wrapped nodes.\n */\nexport function wrap(nodes, loose) {\n  /** @type {Array<Type | HastText>} */\n  const result = []\n  let index = -1\n\n  if (loose) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  while (++index < nodes.length) {\n    if (index) result.push({type: 'text', value: '\\n'})\n    result.push(nodes[index])\n  }\n\n  if (loose && nodes.length > 0) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  return result\n}\n", "/**\n * @typedef {import('hast').Content} HastContent\n * @typedef {import('hast').Root} HastRoot\n *\n * @typedef {import('mdast').Content} MdastContent\n * @typedef {import('mdast').Root} MdastRoot\n *\n * @typedef {import('./state.js').Options} Options\n */\n\n/**\n * @typedef {HastRoot | HastContent} HastNodes\n * @typedef {MdastRoot | MdastContent} MdastNodes\n */\n\nimport {footer} from './footer.js'\nimport {createState} from './state.js'\n\n/**\n * Transform mdast to hast.\n *\n * ##### Notes\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most utilities ignore `raw` nodes but two notable ones don’t:\n *\n * *   `hast-util-to-html` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful\n *     if you completely trust authors\n * *   `hast-util-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only\n *     way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `mdast-util-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * Example: headings (DOM clobbering) in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @param {MdastNodes} tree\n *   mdast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {HastNodes | null | undefined}\n *   hast tree.\n */\n// To do: next major: always return a single `root`.\nexport function toHast(tree, options) {\n  const state = createState(tree, options)\n  const node = state.one(tree, null)\n  const foot = footer(state)\n\n  if (foot) {\n    // @ts-expect-error If there’s a footer, there were definitions, meaning block\n    // content.\n    // So assume `node` is a parent node.\n    node.children.push({type: 'text', value: '\\n'}, foot)\n  }\n\n  // To do: next major: always return root?\n  return Array.isArray(node) ? {type: 'root', children: node} : node\n}\n", "/**\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef NodeLike\n * @property {PositionLike | null | undefined} [position]\n */\n\n/**\n * Check if `node` is generated.\n *\n * @param {NodeLike | null | undefined} [node]\n *   Node to check.\n * @returns {boolean}\n *   Whether `node` is generated (does not have positional info).\n */\nexport function generated(node) {\n  return (\n    !node ||\n    !node.position ||\n    !node.position.start ||\n    !node.position.start.line ||\n    !node.position.start.column ||\n    !node.position.end ||\n    !node.position.end.line ||\n    !node.position.end.column\n  )\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */\nexport function footer(state) {\n  /** @type {Array<ElementContent>} */\n  const listItems = []\n  let index = -1\n\n  while (++index < state.footnoteOrder.length) {\n    const def = state.footnoteById[state.footnoteOrder[index]]\n\n    if (!def) {\n      continue\n    }\n\n    const content = state.all(def)\n    const id = String(def.identifier).toUpperCase()\n    const safeId = normalizeUri(id.toLowerCase())\n    let referenceIndex = 0\n    /** @type {Array<ElementContent>} */\n    const backReferences = []\n\n    while (++referenceIndex <= state.footnoteCounts[id]) {\n      /** @type {Element} */\n      const backReference = {\n        type: 'element',\n        tagName: 'a',\n        properties: {\n          href:\n            '#' +\n            state.clobberPrefix +\n            'fnref-' +\n            safeId +\n            (referenceIndex > 1 ? '-' + referenceIndex : ''),\n          dataFootnoteBackref: true,\n          className: ['data-footnote-backref'],\n          ariaLabel: state.footnoteBackLabel\n        },\n        children: [{type: 'text', value: '↩'}]\n      }\n\n      if (referenceIndex > 1) {\n        backReference.children.push({\n          type: 'element',\n          tagName: 'sup',\n          children: [{type: 'text', value: String(referenceIndex)}]\n        })\n      }\n\n      if (backReferences.length > 0) {\n        backReferences.push({type: 'text', value: ' '})\n      }\n\n      backReferences.push(backReference)\n    }\n\n    const tail = content[content.length - 1]\n\n    if (tail && tail.type === 'element' && tail.tagName === 'p') {\n      const tailTail = tail.children[tail.children.length - 1]\n      if (tailTail && tailTail.type === 'text') {\n        tailTail.value += ' '\n      } else {\n        tail.children.push({type: 'text', value: ' '})\n      }\n\n      tail.children.push(...backReferences)\n    } else {\n      content.push(...backReferences)\n    }\n\n    /** @type {Element} */\n    const listItem = {\n      type: 'element',\n      tagName: 'li',\n      properties: {id: state.clobberPrefix + 'fn-' + safeId},\n      children: state.wrap(content, true)\n    }\n\n    state.patch(def, listItem)\n\n    listItems.push(listItem)\n  }\n\n  if (listItems.length === 0) {\n    return\n  }\n\n  return {\n    type: 'element',\n    tagName: 'section',\n    properties: {dataFootnotes: true, className: ['footnotes']},\n    children: [\n      {\n        type: 'element',\n        tagName: state.footnoteLabelTagName,\n        properties: {\n          // To do: use structured clone.\n          ...JSON.parse(JSON.stringify(state.footnoteLabelProperties)),\n          id: 'footnote-label'\n        },\n        children: [{type: 'text', value: state.footnoteLabel}]\n      },\n      {type: 'text', value: '\\n'},\n      {\n        type: 'element',\n        tagName: 'ol',\n        properties: {},\n        children: state.wrap(listItems, true)\n      },\n      {type: 'text', value: '\\n'}\n    ]\n  }\n}\n", "/**\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('mdast-util-to-hast').Options} Options\n * @typedef {import('unified').Processor<any, any, any, any>} Processor\n *\n * @typedef {import('mdast-util-to-hast')} DoNotTouchAsThisImportIncludesRawInTree\n */\n\nimport {toHast} from 'mdast-util-to-hast'\n\n// Note: the `<MdastRoot, HastRoot>` overload doesn’t seem to work :'(\n\n/**\n * Plugin that turns markdown into HTML to support rehype.\n *\n * *   If a destination processor is given, that processor runs with a new HTML\n *     (hast) tree (bridge-mode).\n *     As the given processor runs with a hast tree, and rehype plugins support\n *     hast, that means rehype plugins can be used with the given processor.\n *     The hast tree is discarded in the end.\n *     It’s highly unlikely that you want to do this.\n * *   The common case is to not pass a destination processor, in which case the\n *     current processor continues running with a new HTML (hast) tree\n *     (mutate-mode).\n *     As the current processor continues with a hast tree, and rehype plugins\n *     support hast, that means rehype plugins can be used after\n *     `remark-rehype`.\n *     It’s likely that this is what you want to do.\n *\n * @param destination\n *   Optional unified processor.\n * @param options\n *   Options passed to `mdast-util-to-hast`.\n */\nconst remarkRehype =\n  /** @type {(import('unified').Plugin<[Processor, Options?]|[null|undefined, Options?]|[Options]|[], MdastRoot>)} */\n  (\n    function (destination, options) {\n      return destination && 'run' in destination\n        ? bridge(destination, options)\n        : mutate(destination || options)\n    }\n  )\n\nexport default remarkRehype\n\n/**\n * Bridge-mode.\n * Runs the destination with the new hast tree.\n *\n * @type {import('unified').Plugin<[Processor, Options?], MdastRoot>}\n */\nfunction bridge(destination, options) {\n  return (node, file, next) => {\n    destination.run(toHast(node, options), file, (error) => {\n      next(error)\n    })\n  }\n}\n\n/**\n * Mutate-mode.\n * Further plugins run on the hast tree.\n *\n * @type {import('unified').Plugin<[Options?]|void[], MdastRoot, HastRoot>}\n */\nfunction mutate(options) {\n  // @ts-expect-error: assume a corresponding node is returned by `toHast`.\n  return (node) => toHast(node, options)\n}\n", "/**\n * @typedef {import('./info.js').Info} Info\n * @typedef {Record<string, Info>} Properties\n * @typedef {Record<string, string>} Normal\n */\n\nexport class Schema {\n  /**\n   * @constructor\n   * @param {Properties} property\n   * @param {Normal} normal\n   * @param {string} [space]\n   */\n  constructor(property, normal, space) {\n    this.property = property\n    this.normal = normal\n    if (space) {\n      this.space = space\n    }\n  }\n}\n\n/** @type {Properties} */\nSchema.prototype.property = {}\n/** @type {Normal} */\nSchema.prototype.normal = {}\n/** @type {string|null} */\nSchema.prototype.space = null\n", "/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n */\n\nimport {Schema} from './schema.js'\n\n/**\n * @param {Schema[]} definitions\n * @param {string} [space]\n * @returns {Schema}\n */\nexport function merge(definitions, space) {\n  /** @type {Properties} */\n  const property = {}\n  /** @type {Normal} */\n  const normal = {}\n  let index = -1\n\n  while (++index < definitions.length) {\n    Object.assign(property, definitions[index].property)\n    Object.assign(normal, definitions[index].normal)\n  }\n\n  return new Schema(property, normal, space)\n}\n", "/**\n * @param {string} value\n * @returns {string}\n */\nexport function normalize(value) {\n  return value.toLowerCase()\n}\n", "export class Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   */\n  constructor(property, attribute) {\n    /** @type {string} */\n    this.property = property\n    /** @type {string} */\n    this.attribute = attribute\n  }\n}\n\n/** @type {string|null} */\nInfo.prototype.space = null\nInfo.prototype.boolean = false\nInfo.prototype.booleanish = false\nInfo.prototype.overloadedBoolean = false\nInfo.prototype.number = false\nInfo.prototype.commaSeparated = false\nInfo.prototype.spaceSeparated = false\nInfo.prototype.commaOrSpaceSeparated = false\nInfo.prototype.mustUseProperty = false\nInfo.prototype.defined = false\n", "let powers = 0\n\nexport const boolean = increment()\nexport const booleanish = increment()\nexport const overloadedBoolean = increment()\nexport const number = increment()\nexport const spaceSeparated = increment()\nexport const commaSeparated = increment()\nexport const commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return 2 ** ++powers\n}\n", "import {Info} from './info.js'\nimport * as types from './types.js'\n\n/** @type {Array<keyof types>} */\n// @ts-expect-error: hush.\nconst checks = Object.keys(types)\n\nexport class DefinedInfo extends Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   * @param {number|null} [mask]\n   * @param {string} [space]\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1\n\n    super(property, attribute)\n\n    mark(this, 'space', space)\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index]\n        mark(this, checks[index], (mask & types[check]) === types[check])\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true\n\n/**\n * @param {DefinedInfo} values\n * @param {string} key\n * @param {unknown} value\n */\nfunction mark(values, key, value) {\n  if (value) {\n    // @ts-expect-error: assume `value` matches the expected value of `key`.\n    values[key] = value\n  }\n}\n", "/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n *\n * @typedef {Record<string, string>} Attributes\n *\n * @typedef {Object} Definition\n * @property {Record<string, number|null>} properties\n * @property {(attributes: Attributes, property: string) => string} transform\n * @property {string} [space]\n * @property {Attributes} [attributes]\n * @property {Array<string>} [mustUseProperty]\n */\n\nimport {normalize} from '../normalize.js'\nimport {Schema} from './schema.js'\nimport {DefinedInfo} from './defined-info.js'\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {Definition} definition\n * @returns {Schema}\n */\nexport function create(definition) {\n  /** @type {Properties} */\n  const property = {}\n  /** @type {Normal} */\n  const normal = {}\n  /** @type {string} */\n  let prop\n\n  for (prop in definition.properties) {\n    if (own.call(definition.properties, prop)) {\n      const value = definition.properties[prop]\n      const info = new DefinedInfo(\n        prop,\n        definition.transform(definition.attributes || {}, prop),\n        value,\n        definition.space\n      )\n\n      if (\n        definition.mustUseProperty &&\n        definition.mustUseProperty.includes(prop)\n      ) {\n        info.mustUseProperty = true\n      }\n\n      property[prop] = info\n\n      normal[normalize(prop)] = prop\n      normal[normalize(info.attribute)] = prop\n    }\n  }\n\n  return new Schema(property, normal, definition.space)\n}\n", "import {create} from './util/create.js'\n\nexport const xlink = create({\n  space: 'xlink',\n  transform(_, prop) {\n    return 'xlink:' + prop.slice(5).toLowerCase()\n  },\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n})\n", "import {create} from './util/create.js'\n\nexport const xml = create({\n  space: 'xml',\n  transform(_, prop) {\n    return 'xml:' + prop.slice(3).toLowerCase()\n  },\n  properties: {xmlLang: null, xmlBase: null, xmlSpace: null}\n})\n", "/**\n * @param {Record<string, string>} attributes\n * @param {string} attribute\n * @returns {string}\n */\nexport function caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n", "import {caseSensitiveTransform} from './case-sensitive-transform.js'\n\n/**\n * @param {Record<string, string>} attributes\n * @param {string} property\n * @returns {string}\n */\nexport function caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase())\n}\n", "import {create} from './util/create.js'\nimport {caseInsensitiveTransform} from './util/case-insensitive-transform.js'\n\nexport const xmlns = create({\n  space: 'xmlns',\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  transform: caseInsensitiveTransform,\n  properties: {xmlns: null, xmlnsXLink: null}\n})\n", "import {booleanish, number, spaceSeparated} from './util/types.js'\nimport {create} from './util/create.js'\n\nexport const aria = create({\n  transform(_, prop) {\n    return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()\n  },\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  }\n})\n", "import {\n  boolean,\n  overloadedBoolean,\n  booleanish,\n  number,\n  spaceSeparated,\n  commaSeparated\n} from './util/types.js'\nimport {create} from './util/create.js'\nimport {caseInsensitiveTransform} from './util/case-insensitive-transform.js'\n\nexport const html = create({\n  space: 'html',\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  transform: caseInsensitiveTransform,\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: commaSeparated,\n    acceptCharset: spaceSeparated,\n    accessKey: spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: boolean,\n    allowPaymentRequest: boolean,\n    allowUserMedia: boolean,\n    alt: null,\n    as: null,\n    async: boolean,\n    autoCapitalize: null,\n    autoComplete: spaceSeparated,\n    autoFocus: boolean,\n    autoPlay: boolean,\n    blocking: spaceSeparated,\n    capture: null,\n    charSet: null,\n    checked: boolean,\n    cite: null,\n    className: spaceSeparated,\n    cols: number,\n    colSpan: null,\n    content: null,\n    contentEditable: booleanish,\n    controls: boolean,\n    controlsList: spaceSeparated,\n    coords: number | commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: boolean,\n    defer: boolean,\n    dir: null,\n    dirName: null,\n    disabled: boolean,\n    download: overloadedBoolean,\n    draggable: booleanish,\n    encType: null,\n    enterKeyHint: null,\n    fetchPriority: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: boolean,\n    formTarget: null,\n    headers: spaceSeparated,\n    height: number,\n    hidden: boolean,\n    high: number,\n    href: null,\n    hrefLang: null,\n    htmlFor: spaceSeparated,\n    httpEquiv: spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inert: boolean,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: boolean,\n    itemId: null,\n    itemProp: spaceSeparated,\n    itemRef: spaceSeparated,\n    itemScope: boolean,\n    itemType: spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: boolean,\n    low: number,\n    manifest: null,\n    max: null,\n    maxLength: number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: number,\n    multiple: boolean,\n    muted: boolean,\n    name: null,\n    nonce: null,\n    noModule: boolean,\n    noValidate: boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeToggle: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: boolean,\n    optimum: number,\n    pattern: null,\n    ping: spaceSeparated,\n    placeholder: null,\n    playsInline: boolean,\n    popover: null,\n    popoverTarget: null,\n    popoverTargetAction: null,\n    poster: null,\n    preload: null,\n    readOnly: boolean,\n    referrerPolicy: null,\n    rel: spaceSeparated,\n    required: boolean,\n    reversed: boolean,\n    rows: number,\n    rowSpan: number,\n    sandbox: spaceSeparated,\n    scope: null,\n    scoped: boolean,\n    seamless: boolean,\n    selected: boolean,\n    shadowRootClonable: boolean,\n    shadowRootDelegatesFocus: boolean,\n    shadowRootMode: null,\n    shape: null,\n    size: number,\n    sizes: null,\n    slot: null,\n    span: number,\n    spellCheck: booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: number,\n    step: null,\n    style: null,\n    tabIndex: number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: boolean,\n    useMap: null,\n    value: booleanish,\n    width: number,\n    wrap: null,\n    writingSuggestions: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: boolean, // Lists. Use CSS to reduce space between items instead\n    declare: boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: number, // `<img>` and `<object>`\n    leftMargin: number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: number, // `<body>`\n    marginWidth: number, // `<body>`\n    noResize: boolean, // `<frame>`\n    noHref: boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: boolean,\n    disableRemotePlayback: boolean,\n    prefix: null,\n    property: null,\n    results: number,\n    security: null,\n    unselectable: null\n  }\n})\n", "import {\n  boolean,\n  number,\n  spaceSeparated,\n  commaSeparated,\n  commaOrSpaceSeparated\n} from './util/types.js'\nimport {create} from './util/create.js'\nimport {caseSensitiveTransform} from './util/case-sensitive-transform.js'\n\nexport const svg = create({\n  space: 'svg',\n  attributes: {\n    accentHeight: 'accent-height',\n    alignmentBaseline: 'alignment-baseline',\n    arabicForm: 'arabic-form',\n    baselineShift: 'baseline-shift',\n    capHeight: 'cap-height',\n    className: 'class',\n    clipPath: 'clip-path',\n    clipRule: 'clip-rule',\n    colorInterpolation: 'color-interpolation',\n    colorInterpolationFilters: 'color-interpolation-filters',\n    colorProfile: 'color-profile',\n    colorRendering: 'color-rendering',\n    crossOrigin: 'crossorigin',\n    dataType: 'datatype',\n    dominantBaseline: 'dominant-baseline',\n    enableBackground: 'enable-background',\n    fillOpacity: 'fill-opacity',\n    fillRule: 'fill-rule',\n    floodColor: 'flood-color',\n    floodOpacity: 'flood-opacity',\n    fontFamily: 'font-family',\n    fontSize: 'font-size',\n    fontSizeAdjust: 'font-size-adjust',\n    fontStretch: 'font-stretch',\n    fontStyle: 'font-style',\n    fontVariant: 'font-variant',\n    fontWeight: 'font-weight',\n    glyphName: 'glyph-name',\n    glyphOrientationHorizontal: 'glyph-orientation-horizontal',\n    glyphOrientationVertical: 'glyph-orientation-vertical',\n    hrefLang: 'hreflang',\n    horizAdvX: 'horiz-adv-x',\n    horizOriginX: 'horiz-origin-x',\n    horizOriginY: 'horiz-origin-y',\n    imageRendering: 'image-rendering',\n    letterSpacing: 'letter-spacing',\n    lightingColor: 'lighting-color',\n    markerEnd: 'marker-end',\n    markerMid: 'marker-mid',\n    markerStart: 'marker-start',\n    navDown: 'nav-down',\n    navDownLeft: 'nav-down-left',\n    navDownRight: 'nav-down-right',\n    navLeft: 'nav-left',\n    navNext: 'nav-next',\n    navPrev: 'nav-prev',\n    navRight: 'nav-right',\n    navUp: 'nav-up',\n    navUpLeft: 'nav-up-left',\n    navUpRight: 'nav-up-right',\n    onAbort: 'onabort',\n    onActivate: 'onactivate',\n    onAfterPrint: 'onafterprint',\n    onBeforePrint: 'onbeforeprint',\n    onBegin: 'onbegin',\n    onCancel: 'oncancel',\n    onCanPlay: 'oncanplay',\n    onCanPlayThrough: 'oncanplaythrough',\n    onChange: 'onchange',\n    onClick: 'onclick',\n    onClose: 'onclose',\n    onCopy: 'oncopy',\n    onCueChange: 'oncuechange',\n    onCut: 'oncut',\n    onDblClick: 'ondblclick',\n    onDrag: 'ondrag',\n    onDragEnd: 'ondragend',\n    onDragEnter: 'ondragenter',\n    onDragExit: 'ondragexit',\n    onDragLeave: 'ondragleave',\n    onDragOver: 'ondragover',\n    onDragStart: 'ondragstart',\n    onDrop: 'ondrop',\n    onDurationChange: 'ondurationchange',\n    onEmptied: 'onemptied',\n    onEnd: 'onend',\n    onEnded: 'onended',\n    onError: 'onerror',\n    onFocus: 'onfocus',\n    onFocusIn: 'onfocusin',\n    onFocusOut: 'onfocusout',\n    onHashChange: 'onhashchange',\n    onInput: 'oninput',\n    onInvalid: 'oninvalid',\n    onKeyDown: 'onkeydown',\n    onKeyPress: 'onkeypress',\n    onKeyUp: 'onkeyup',\n    onLoad: 'onload',\n    onLoadedData: 'onloadeddata',\n    onLoadedMetadata: 'onloadedmetadata',\n    onLoadStart: 'onloadstart',\n    onMessage: 'onmessage',\n    onMouseDown: 'onmousedown',\n    onMouseEnter: 'onmouseenter',\n    onMouseLeave: 'onmouseleave',\n    onMouseMove: 'onmousemove',\n    onMouseOut: 'onmouseout',\n    onMouseOver: 'onmouseover',\n    onMouseUp: 'onmouseup',\n    onMouseWheel: 'onmousewheel',\n    onOffline: 'onoffline',\n    onOnline: 'ononline',\n    onPageHide: 'onpagehide',\n    onPageShow: 'onpageshow',\n    onPaste: 'onpaste',\n    onPause: 'onpause',\n    onPlay: 'onplay',\n    onPlaying: 'onplaying',\n    onPopState: 'onpopstate',\n    onProgress: 'onprogress',\n    onRateChange: 'onratechange',\n    onRepeat: 'onrepeat',\n    onReset: 'onreset',\n    onResize: 'onresize',\n    onScroll: 'onscroll',\n    onSeeked: 'onseeked',\n    onSeeking: 'onseeking',\n    onSelect: 'onselect',\n    onShow: 'onshow',\n    onStalled: 'onstalled',\n    onStorage: 'onstorage',\n    onSubmit: 'onsubmit',\n    onSuspend: 'onsuspend',\n    onTimeUpdate: 'ontimeupdate',\n    onToggle: 'ontoggle',\n    onUnload: 'onunload',\n    onVolumeChange: 'onvolumechange',\n    onWaiting: 'onwaiting',\n    onZoom: 'onzoom',\n    overlinePosition: 'overline-position',\n    overlineThickness: 'overline-thickness',\n    paintOrder: 'paint-order',\n    panose1: 'panose-1',\n    pointerEvents: 'pointer-events',\n    referrerPolicy: 'referrerpolicy',\n    renderingIntent: 'rendering-intent',\n    shapeRendering: 'shape-rendering',\n    stopColor: 'stop-color',\n    stopOpacity: 'stop-opacity',\n    strikethroughPosition: 'strikethrough-position',\n    strikethroughThickness: 'strikethrough-thickness',\n    strokeDashArray: 'stroke-dasharray',\n    strokeDashOffset: 'stroke-dashoffset',\n    strokeLineCap: 'stroke-linecap',\n    strokeLineJoin: 'stroke-linejoin',\n    strokeMiterLimit: 'stroke-miterlimit',\n    strokeOpacity: 'stroke-opacity',\n    strokeWidth: 'stroke-width',\n    tabIndex: 'tabindex',\n    textAnchor: 'text-anchor',\n    textDecoration: 'text-decoration',\n    textRendering: 'text-rendering',\n    transformOrigin: 'transform-origin',\n    typeOf: 'typeof',\n    underlinePosition: 'underline-position',\n    underlineThickness: 'underline-thickness',\n    unicodeBidi: 'unicode-bidi',\n    unicodeRange: 'unicode-range',\n    unitsPerEm: 'units-per-em',\n    vAlphabetic: 'v-alphabetic',\n    vHanging: 'v-hanging',\n    vIdeographic: 'v-ideographic',\n    vMathematical: 'v-mathematical',\n    vectorEffect: 'vector-effect',\n    vertAdvY: 'vert-adv-y',\n    vertOriginX: 'vert-origin-x',\n    vertOriginY: 'vert-origin-y',\n    wordSpacing: 'word-spacing',\n    writingMode: 'writing-mode',\n    xHeight: 'x-height',\n    // These were camelcased in Tiny. Now lowercased in SVG 2\n    playbackOrder: 'playbackorder',\n    timelineBegin: 'timelinebegin'\n  },\n  transform: caseSensitiveTransform,\n  properties: {\n    about: commaOrSpaceSeparated,\n    accentHeight: number,\n    accumulate: null,\n    additive: null,\n    alignmentBaseline: null,\n    alphabetic: number,\n    amplitude: number,\n    arabicForm: null,\n    ascent: number,\n    attributeName: null,\n    attributeType: null,\n    azimuth: number,\n    bandwidth: null,\n    baselineShift: null,\n    baseFrequency: null,\n    baseProfile: null,\n    bbox: null,\n    begin: null,\n    bias: number,\n    by: null,\n    calcMode: null,\n    capHeight: number,\n    className: spaceSeparated,\n    clip: null,\n    clipPath: null,\n    clipPathUnits: null,\n    clipRule: null,\n    color: null,\n    colorInterpolation: null,\n    colorInterpolationFilters: null,\n    colorProfile: null,\n    colorRendering: null,\n    content: null,\n    contentScriptType: null,\n    contentStyleType: null,\n    crossOrigin: null,\n    cursor: null,\n    cx: null,\n    cy: null,\n    d: null,\n    dataType: null,\n    defaultAction: null,\n    descent: number,\n    diffuseConstant: number,\n    direction: null,\n    display: null,\n    dur: null,\n    divisor: number,\n    dominantBaseline: null,\n    download: boolean,\n    dx: null,\n    dy: null,\n    edgeMode: null,\n    editable: null,\n    elevation: number,\n    enableBackground: null,\n    end: null,\n    event: null,\n    exponent: number,\n    externalResourcesRequired: null,\n    fill: null,\n    fillOpacity: number,\n    fillRule: null,\n    filter: null,\n    filterRes: null,\n    filterUnits: null,\n    floodColor: null,\n    floodOpacity: null,\n    focusable: null,\n    focusHighlight: null,\n    fontFamily: null,\n    fontSize: null,\n    fontSizeAdjust: null,\n    fontStretch: null,\n    fontStyle: null,\n    fontVariant: null,\n    fontWeight: null,\n    format: null,\n    fr: null,\n    from: null,\n    fx: null,\n    fy: null,\n    g1: commaSeparated,\n    g2: commaSeparated,\n    glyphName: commaSeparated,\n    glyphOrientationHorizontal: null,\n    glyphOrientationVertical: null,\n    glyphRef: null,\n    gradientTransform: null,\n    gradientUnits: null,\n    handler: null,\n    hanging: number,\n    hatchContentUnits: null,\n    hatchUnits: null,\n    height: null,\n    href: null,\n    hrefLang: null,\n    horizAdvX: number,\n    horizOriginX: number,\n    horizOriginY: number,\n    id: null,\n    ideographic: number,\n    imageRendering: null,\n    initialVisibility: null,\n    in: null,\n    in2: null,\n    intercept: number,\n    k: number,\n    k1: number,\n    k2: number,\n    k3: number,\n    k4: number,\n    kernelMatrix: commaOrSpaceSeparated,\n    kernelUnitLength: null,\n    keyPoints: null, // SEMI_COLON_SEPARATED\n    keySplines: null, // SEMI_COLON_SEPARATED\n    keyTimes: null, // SEMI_COLON_SEPARATED\n    kerning: null,\n    lang: null,\n    lengthAdjust: null,\n    letterSpacing: null,\n    lightingColor: null,\n    limitingConeAngle: number,\n    local: null,\n    markerEnd: null,\n    markerMid: null,\n    markerStart: null,\n    markerHeight: null,\n    markerUnits: null,\n    markerWidth: null,\n    mask: null,\n    maskContentUnits: null,\n    maskUnits: null,\n    mathematical: null,\n    max: null,\n    media: null,\n    mediaCharacterEncoding: null,\n    mediaContentEncodings: null,\n    mediaSize: number,\n    mediaTime: null,\n    method: null,\n    min: null,\n    mode: null,\n    name: null,\n    navDown: null,\n    navDownLeft: null,\n    navDownRight: null,\n    navLeft: null,\n    navNext: null,\n    navPrev: null,\n    navRight: null,\n    navUp: null,\n    navUpLeft: null,\n    navUpRight: null,\n    numOctaves: null,\n    observer: null,\n    offset: null,\n    onAbort: null,\n    onActivate: null,\n    onAfterPrint: null,\n    onBeforePrint: null,\n    onBegin: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnd: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFocusIn: null,\n    onFocusOut: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onMouseWheel: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRepeat: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onShow: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onZoom: null,\n    opacity: null,\n    operator: null,\n    order: null,\n    orient: null,\n    orientation: null,\n    origin: null,\n    overflow: null,\n    overlay: null,\n    overlinePosition: number,\n    overlineThickness: number,\n    paintOrder: null,\n    panose1: null,\n    path: null,\n    pathLength: number,\n    patternContentUnits: null,\n    patternTransform: null,\n    patternUnits: null,\n    phase: null,\n    ping: spaceSeparated,\n    pitch: null,\n    playbackOrder: null,\n    pointerEvents: null,\n    points: null,\n    pointsAtX: number,\n    pointsAtY: number,\n    pointsAtZ: number,\n    preserveAlpha: null,\n    preserveAspectRatio: null,\n    primitiveUnits: null,\n    propagate: null,\n    property: commaOrSpaceSeparated,\n    r: null,\n    radius: null,\n    referrerPolicy: null,\n    refX: null,\n    refY: null,\n    rel: commaOrSpaceSeparated,\n    rev: commaOrSpaceSeparated,\n    renderingIntent: null,\n    repeatCount: null,\n    repeatDur: null,\n    requiredExtensions: commaOrSpaceSeparated,\n    requiredFeatures: commaOrSpaceSeparated,\n    requiredFonts: commaOrSpaceSeparated,\n    requiredFormats: commaOrSpaceSeparated,\n    resource: null,\n    restart: null,\n    result: null,\n    rotate: null,\n    rx: null,\n    ry: null,\n    scale: null,\n    seed: null,\n    shapeRendering: null,\n    side: null,\n    slope: null,\n    snapshotTime: null,\n    specularConstant: number,\n    specularExponent: number,\n    spreadMethod: null,\n    spacing: null,\n    startOffset: null,\n    stdDeviation: null,\n    stemh: null,\n    stemv: null,\n    stitchTiles: null,\n    stopColor: null,\n    stopOpacity: null,\n    strikethroughPosition: number,\n    strikethroughThickness: number,\n    string: null,\n    stroke: null,\n    strokeDashArray: commaOrSpaceSeparated,\n    strokeDashOffset: null,\n    strokeLineCap: null,\n    strokeLineJoin: null,\n    strokeMiterLimit: number,\n    strokeOpacity: number,\n    strokeWidth: null,\n    style: null,\n    surfaceScale: number,\n    syncBehavior: null,\n    syncBehaviorDefault: null,\n    syncMaster: null,\n    syncTolerance: null,\n    syncToleranceDefault: null,\n    systemLanguage: commaOrSpaceSeparated,\n    tabIndex: number,\n    tableValues: null,\n    target: null,\n    targetX: number,\n    targetY: number,\n    textAnchor: null,\n    textDecoration: null,\n    textRendering: null,\n    textLength: null,\n    timelineBegin: null,\n    title: null,\n    transformBehavior: null,\n    type: null,\n    typeOf: commaOrSpaceSeparated,\n    to: null,\n    transform: null,\n    transformOrigin: null,\n    u1: null,\n    u2: null,\n    underlinePosition: number,\n    underlineThickness: number,\n    unicode: null,\n    unicodeBidi: null,\n    unicodeRange: null,\n    unitsPerEm: number,\n    values: null,\n    vAlphabetic: number,\n    vMathematical: number,\n    vectorEffect: null,\n    vHanging: number,\n    vIdeographic: number,\n    version: null,\n    vertAdvY: number,\n    vertOriginX: number,\n    vertOriginY: number,\n    viewBox: null,\n    viewTarget: null,\n    visibility: null,\n    width: null,\n    widths: null,\n    wordSpacing: null,\n    writingMode: null,\n    x: null,\n    x1: null,\n    x2: null,\n    xChannelSelector: null,\n    xHeight: number,\n    y: null,\n    y1: null,\n    y2: null,\n    yChannelSelector: null,\n    z: null,\n    zoomAndPan: null\n  }\n})\n", "/**\n * @typedef {import('./lib/util/info.js').Info} Info\n * @typedef {import('./lib/util/schema.js').Schema} Schema\n */\n\nimport {merge} from './lib/util/merge.js'\nimport {xlink} from './lib/xlink.js'\nimport {xml} from './lib/xml.js'\nimport {xmlns} from './lib/xmlns.js'\nimport {aria} from './lib/aria.js'\nimport {html as htmlBase} from './lib/html.js'\nimport {svg as svgBase} from './lib/svg.js'\n\nexport {find} from './lib/find.js'\nexport {hastToReact} from './lib/hast-to-react.js'\nexport {normalize} from './lib/normalize.js'\nexport const html = merge([xml, xlink, xmlns, aria, htmlBase], 'html')\nexport const svg = merge([xml, xlink, xmlns, aria, svgBase], 'svg')\n", "import {visit} from 'unist-util-visit'\n\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Element} Element\n *\n * @callback AllowElement\n * @param {Element} element\n * @param {number} index\n * @param {Element|Root} parent\n * @returns {boolean|undefined}\n *\n * @typedef Options\n * @property {Array<string>} [allowedElements]\n * @property {Array<string>} [disallowedElements=[]]\n * @property {AllowElement} [allowElement]\n * @property {boolean} [unwrapDisallowed=false]\n */\n\n/**\n * @type {import('unified').Plugin<[Options], Root>}\n */\nexport default function rehypeFilter(options) {\n  if (options.allowedElements && options.disallowedElements) {\n    throw new TypeError(\n      'Only one of `allowedElements` and `disallowedElements` should be defined'\n    )\n  }\n\n  if (\n    options.allowedElements ||\n    options.disallowedElements ||\n    options.allowElement\n  ) {\n    return (tree) => {\n      visit(tree, 'element', (node, index, parent_) => {\n        const parent = /** @type {Element|Root} */ (parent_)\n        /** @type {boolean|undefined} */\n        let remove\n\n        if (options.allowedElements) {\n          remove = !options.allowedElements.includes(node.tagName)\n        } else if (options.disallowedElements) {\n          remove = options.disallowedElements.includes(node.tagName)\n        }\n\n        if (!remove && options.allowElement && typeof index === 'number') {\n          remove = !options.allowElement(node, index, parent)\n        }\n\n        if (remove && typeof index === 'number') {\n          if (options.unwrapDisallowed && node.children) {\n            parent.children.splice(index, 1, ...node.children)\n          } else {\n            parent.children.splice(index, 1)\n          }\n\n          return index\n        }\n\n        return undefined\n      })\n    }\n  }\n}\n", "/**\n * @typedef {import('./util/schema.js').Schema} Schema\n */\n\nimport {normalize} from './normalize.js'\nimport {DefinedInfo} from './util/defined-info.js'\nimport {Info} from './util/info.js'\n\nconst valid = /^data[-\\w.:]+$/i\nconst dash = /-[a-z]/g\nconst cap = /[A-Z]/g\n\n/**\n * @param {Schema} schema\n * @param {string} value\n * @returns {Info}\n */\nexport function find(schema, value) {\n  const normal = normalize(value)\n  let prop = value\n  let Type = Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase)\n      prop = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1)\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4)\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab)\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes\n        }\n\n        value = 'data' + dashes\n      }\n    }\n\n    Type = DefinedInfo\n  }\n\n  return new Type(prop, value)\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n", "/**\n * `hast` is close to `React`, but differs in a couple of cases.\n *\n * To get a React property from a hast property, check if it is in\n * `hastToReact`, if it is, then use the corresponding value,\n * otherwise, use the hast property.\n *\n * @type {Record<string, string>}\n */\nexport const hastToReact = {\n  classId: 'classID',\n  dataType: 'datatype',\n  itemId: 'itemID',\n  strokeDashArray: 'strokeDasharray',\n  strokeDashOffset: 'strokeDashoffset',\n  strokeLineCap: 'strokeLinecap',\n  strokeLineJoin: 'strokeLinejoin',\n  strokeMiterLimit: 'strokeMiterlimit',\n  typeOf: 'typeof',\n  xLinkActuate: 'xlinkActuate',\n  xLinkArcRole: 'xlinkArcrole',\n  xLinkHref: 'xlinkHref',\n  xLinkRole: 'xlinkRole',\n  xLinkShow: 'xlinkShow',\n  xLinkTitle: 'xlinkTitle',\n  xLinkType: 'xlinkType',\n  xmlnsXLink: 'xmlnsXlink'\n}\n", "/**\n * @template T\n * @typedef {import('react').ComponentType<T>} ComponentType<T>\n */\n\n/**\n * @template {import('react').ElementType} T\n * @typedef {import('react').ComponentPropsWithoutRef<T>} ComponentPropsWithoutRef<T>\n */\n\n/**\n * @typedef {import('react').ReactNode} ReactNode\n * @typedef {import('unist').Position} Position\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').DocType} Doctype\n * @typedef {import('property-information').Info} Info\n * @typedef {import('property-information').Schema} Schema\n * @typedef {import('./complex-types.js').ReactMarkdownProps} ReactMarkdownProps\n *\n * @typedef Raw\n * @property {'raw'} type\n * @property {string} value\n *\n * @typedef Context\n * @property {Options} options\n * @property {Schema} schema\n * @property {number} listDepth\n *\n * @callback TransformLink\n * @param {string} href\n * @param {Array<ElementContent>} children\n * @param {string?} title\n * @returns {string}\n *\n * @callback TransformImage\n * @param {string} src\n * @param {string} alt\n * @param {string?} title\n * @returns {string}\n *\n * @typedef {import('react').HTMLAttributeAnchorTarget} TransformLinkTargetType\n *\n * @callback TransformLinkTarget\n * @param {string} href\n * @param {Array<ElementContent>} children\n * @param {string?} title\n * @returns {TransformLinkTargetType|undefined}\n *\n * @typedef {keyof JSX.IntrinsicElements} ReactMarkdownNames\n *\n * To do: is `data-sourcepos` typeable?\n *\n * @typedef {ComponentPropsWithoutRef<'code'> & ReactMarkdownProps & {inline?: boolean}} CodeProps\n * @typedef {ComponentPropsWithoutRef<'h1'> & ReactMarkdownProps & {level: number}} HeadingProps\n * @typedef {ComponentPropsWithoutRef<'li'> & ReactMarkdownProps & {checked: boolean|null, index: number, ordered: boolean}} LiProps\n * @typedef {ComponentPropsWithoutRef<'ol'> & ReactMarkdownProps & {depth: number, ordered: true}} OrderedListProps\n * @typedef {ComponentPropsWithoutRef<'td'> & ReactMarkdownProps & {style?: Record<string, unknown>, isHeader: false}} TableDataCellProps\n * @typedef {ComponentPropsWithoutRef<'th'> & ReactMarkdownProps & {style?: Record<string, unknown>, isHeader: true}} TableHeaderCellProps\n * @typedef {ComponentPropsWithoutRef<'tr'> & ReactMarkdownProps & {isHeader: boolean}} TableRowProps\n * @typedef {ComponentPropsWithoutRef<'ul'> & ReactMarkdownProps & {depth: number, ordered: false}} UnorderedListProps\n *\n * @typedef {ComponentType<CodeProps>} CodeComponent\n * @typedef {ComponentType<HeadingProps>} HeadingComponent\n * @typedef {ComponentType<LiProps>} LiComponent\n * @typedef {ComponentType<OrderedListProps>} OrderedListComponent\n * @typedef {ComponentType<TableDataCellProps>} TableDataCellComponent\n * @typedef {ComponentType<TableHeaderCellProps>} TableHeaderCellComponent\n * @typedef {ComponentType<TableRowProps>} TableRowComponent\n * @typedef {ComponentType<UnorderedListProps>} UnorderedListComponent\n *\n * @typedef SpecialComponents\n * @property {CodeComponent|ReactMarkdownNames} code\n * @property {HeadingComponent|ReactMarkdownNames} h1\n * @property {HeadingComponent|ReactMarkdownNames} h2\n * @property {HeadingComponent|ReactMarkdownNames} h3\n * @property {HeadingComponent|ReactMarkdownNames} h4\n * @property {HeadingComponent|ReactMarkdownNames} h5\n * @property {HeadingComponent|ReactMarkdownNames} h6\n * @property {LiComponent|ReactMarkdownNames} li\n * @property {OrderedListComponent|ReactMarkdownNames} ol\n * @property {TableDataCellComponent|ReactMarkdownNames} td\n * @property {TableHeaderCellComponent|ReactMarkdownNames} th\n * @property {TableRowComponent|ReactMarkdownNames} tr\n * @property {UnorderedListComponent|ReactMarkdownNames} ul\n *\n * @typedef {Partial<Omit<import('./complex-types.js').NormalComponents, keyof SpecialComponents> & SpecialComponents>} Components\n *\n * @typedef Options\n * @property {boolean} [sourcePos=false]\n * @property {boolean} [rawSourcePos=false]\n * @property {boolean} [skipHtml=false]\n * @property {boolean} [includeElementIndex=false]\n * @property {null|false|TransformLink} [transformLinkUri]\n * @property {TransformImage} [transformImageUri]\n * @property {TransformLinkTargetType|TransformLinkTarget} [linkTarget]\n * @property {Components} [components]\n */\n\nimport React from 'react'\nimport ReactIs from 'react-is'\nimport {whitespace} from 'hast-util-whitespace'\nimport {svg, find, hastToReact} from 'property-information'\nimport {stringify as spaces} from 'space-separated-tokens'\nimport {stringify as commas} from 'comma-separated-tokens'\nimport style from 'style-to-object'\nimport {uriTransformer} from './uri-transformer.js'\n\nconst own = {}.hasOwnProperty\n\n// The table-related elements that must not contain whitespace text according\n// to React.\nconst tableElements = new Set(['table', 'thead', 'tbody', 'tfoot', 'tr'])\n\n/**\n * @param {Context} context\n * @param {Element|Root} node\n */\nexport function childrenToReact(context, node) {\n  /** @type {Array<ReactNode>} */\n  const children = []\n  let childIndex = -1\n  /** @type {Comment|Doctype|Element|Raw|Text} */\n  let child\n\n  while (++childIndex < node.children.length) {\n    child = node.children[childIndex]\n\n    if (child.type === 'element') {\n      children.push(toReact(context, child, childIndex, node))\n    } else if (child.type === 'text') {\n      // Currently, a warning is triggered by react for *any* white space in\n      // tables.\n      // So we drop it.\n      // See: <https://github.com/facebook/react/pull/7081>.\n      // See: <https://github.com/facebook/react/pull/7515>.\n      // See: <https://github.com/remarkjs/remark-react/issues/64>.\n      // See: <https://github.com/remarkjs/react-markdown/issues/576>.\n      if (\n        node.type !== 'element' ||\n        !tableElements.has(node.tagName) ||\n        !whitespace(child)\n      ) {\n        children.push(child.value)\n      }\n    } else if (child.type === 'raw' && !context.options.skipHtml) {\n      // Default behavior is to show (encoded) HTML.\n      children.push(child.value)\n    }\n  }\n\n  return children\n}\n\n/**\n * @param {Context} context\n * @param {Element} node\n * @param {number} index\n * @param {Element|Root} parent\n */\nfunction toReact(context, node, index, parent) {\n  const options = context.options\n  const transform =\n    options.transformLinkUri === undefined\n      ? uriTransformer\n      : options.transformLinkUri\n  const parentSchema = context.schema\n  /** @type {ReactMarkdownNames} */\n  // @ts-expect-error assume a known HTML/SVG element.\n  const name = node.tagName\n  /** @type {Record<string, unknown>} */\n  const properties = {}\n  let schema = parentSchema\n  /** @type {string} */\n  let property\n\n  if (parentSchema.space === 'html' && name === 'svg') {\n    schema = svg\n    context.schema = schema\n  }\n\n  if (node.properties) {\n    for (property in node.properties) {\n      if (own.call(node.properties, property)) {\n        addProperty(properties, property, node.properties[property], context)\n      }\n    }\n  }\n\n  if (name === 'ol' || name === 'ul') {\n    context.listDepth++\n  }\n\n  const children = childrenToReact(context, node)\n\n  if (name === 'ol' || name === 'ul') {\n    context.listDepth--\n  }\n\n  // Restore parent schema.\n  context.schema = parentSchema\n\n  // Nodes created by plugins do not have positional info, in which case we use\n  // an object that matches the position interface.\n  const position = node.position || {\n    start: {line: null, column: null, offset: null},\n    end: {line: null, column: null, offset: null}\n  }\n  const component =\n    options.components && own.call(options.components, name)\n      ? options.components[name]\n      : name\n  const basic = typeof component === 'string' || component === React.Fragment\n\n  if (!ReactIs.isValidElementType(component)) {\n    throw new TypeError(\n      `Component for name \\`${name}\\` not defined or is not renderable`\n    )\n  }\n\n  properties.key = index\n\n  if (name === 'a' && options.linkTarget) {\n    properties.target =\n      typeof options.linkTarget === 'function'\n        ? options.linkTarget(\n            String(properties.href || ''),\n            node.children,\n            typeof properties.title === 'string' ? properties.title : null\n          )\n        : options.linkTarget\n  }\n\n  if (name === 'a' && transform) {\n    properties.href = transform(\n      String(properties.href || ''),\n      node.children,\n      typeof properties.title === 'string' ? properties.title : null\n    )\n  }\n\n  if (\n    !basic &&\n    name === 'code' &&\n    parent.type === 'element' &&\n    parent.tagName !== 'pre'\n  ) {\n    properties.inline = true\n  }\n\n  if (\n    !basic &&\n    (name === 'h1' ||\n      name === 'h2' ||\n      name === 'h3' ||\n      name === 'h4' ||\n      name === 'h5' ||\n      name === 'h6')\n  ) {\n    properties.level = Number.parseInt(name.charAt(1), 10)\n  }\n\n  if (name === 'img' && options.transformImageUri) {\n    properties.src = options.transformImageUri(\n      String(properties.src || ''),\n      String(properties.alt || ''),\n      typeof properties.title === 'string' ? properties.title : null\n    )\n  }\n\n  if (!basic && name === 'li' && parent.type === 'element') {\n    const input = getInputElement(node)\n    properties.checked =\n      input && input.properties ? Boolean(input.properties.checked) : null\n    properties.index = getElementsBeforeCount(parent, node)\n    properties.ordered = parent.tagName === 'ol'\n  }\n\n  if (!basic && (name === 'ol' || name === 'ul')) {\n    properties.ordered = name === 'ol'\n    properties.depth = context.listDepth\n  }\n\n  if (name === 'td' || name === 'th') {\n    if (properties.align) {\n      if (!properties.style) properties.style = {}\n      // @ts-expect-error assume `style` is an object\n      properties.style.textAlign = properties.align\n      delete properties.align\n    }\n\n    if (!basic) {\n      properties.isHeader = name === 'th'\n    }\n  }\n\n  if (!basic && name === 'tr' && parent.type === 'element') {\n    properties.isHeader = Boolean(parent.tagName === 'thead')\n  }\n\n  // If `sourcePos` is given, pass source information (line/column info from markdown source).\n  if (options.sourcePos) {\n    properties['data-sourcepos'] = flattenPosition(position)\n  }\n\n  if (!basic && options.rawSourcePos) {\n    properties.sourcePosition = node.position\n  }\n\n  // If `includeElementIndex` is given, pass node index info to components.\n  if (!basic && options.includeElementIndex) {\n    properties.index = getElementsBeforeCount(parent, node)\n    properties.siblingCount = getElementsBeforeCount(parent)\n  }\n\n  if (!basic) {\n    properties.node = node\n  }\n\n  // Ensure no React warnings are emitted for void elements w/ children.\n  return children.length > 0\n    ? React.createElement(component, properties, children)\n    : React.createElement(component, properties)\n}\n\n/**\n * @param {Element|Root} node\n * @returns {Element?}\n */\nfunction getInputElement(node) {\n  let index = -1\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if (child.type === 'element' && child.tagName === 'input') {\n      return child\n    }\n  }\n\n  return null\n}\n\n/**\n * @param {Element|Root} parent\n * @param {Element} [node]\n * @returns {number}\n */\nfunction getElementsBeforeCount(parent, node) {\n  let index = -1\n  let count = 0\n\n  while (++index < parent.children.length) {\n    if (parent.children[index] === node) break\n    if (parent.children[index].type === 'element') count++\n  }\n\n  return count\n}\n\n/**\n * @param {Record<string, unknown>} props\n * @param {string} prop\n * @param {unknown} value\n * @param {Context} ctx\n */\nfunction addProperty(props, prop, value, ctx) {\n  const info = find(ctx.schema, prop)\n  let result = value\n\n  // Ignore nullish and `NaN` values.\n  // eslint-disable-next-line no-self-compare\n  if (result === null || result === undefined || result !== result) {\n    return\n  }\n\n  // Accept `array`.\n  // Most props are space-separated.\n  if (Array.isArray(result)) {\n    result = info.commaSeparated ? commas(result) : spaces(result)\n  }\n\n  if (info.property === 'style' && typeof result === 'string') {\n    result = parseStyle(result)\n  }\n\n  if (info.space && info.property) {\n    props[\n      own.call(hastToReact, info.property)\n        ? hastToReact[info.property]\n        : info.property\n    ] = result\n  } else if (info.attribute) {\n    props[info.attribute] = result\n  }\n}\n\n/**\n * @param {string} value\n * @returns {Record<string, string>}\n */\nfunction parseStyle(value) {\n  /** @type {Record<string, string>} */\n  const result = {}\n\n  try {\n    style(value, iterator)\n  } catch {\n    // Silent.\n  }\n\n  return result\n\n  /**\n   * @param {string} name\n   * @param {string} v\n   */\n  function iterator(name, v) {\n    const k = name.slice(0, 4) === '-ms-' ? `ms-${name.slice(4)}` : name\n    result[k.replace(/-([a-z])/g, styleReplacer)] = v\n  }\n}\n\n/**\n * @param {unknown} _\n * @param {string} $1\n */\nfunction styleReplacer(_, $1) {\n  return $1.toUpperCase()\n}\n\n/**\n * @param {Position|{start: {line: null, column: null, offset: null}, end: {line: null, column: null, offset: null}}} pos\n * @returns {string}\n */\nfunction flattenPosition(pos) {\n  return [\n    pos.start.line,\n    ':',\n    pos.start.column,\n    '-',\n    pos.end.line,\n    ':',\n    pos.end.column\n  ]\n    .map(String)\n    .join('')\n}\n", "import StyleToObject from './index.js';\n\nexport default StyleToObject;\n", "/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {unknown} thing\n *   Thing to check (typically `Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`).\n *   If a node is passed it must be a `Text` node, whose `value` field is\n *   checked.\n */\nexport function whitespace(thing) {\n  /** @type {string} */\n  const value =\n    // @ts-expect-error looks like a node.\n    thing && typeof thing === 'object' && thing.type === 'text'\n      ? // @ts-expect-error looks like a text.\n        thing.value || ''\n      : thing\n\n  // HTML whitespace expression.\n  // See <https://infra.spec.whatwg.org/#ascii-whitespace>.\n  return typeof value === 'string' && value.replace(/[ \\t\\n\\f\\r]/g, '') === ''\n}\n", "/**\n * @typedef {import('react').ReactNode} ReactNode\n * @typedef {import('react').ReactElement<{}>} ReactElement\n * @typedef {import('unified').PluggableList} PluggableList\n * @typedef {import('hast').Root} Root\n * @typedef {import('./rehype-filter.js').Options} FilterOptions\n * @typedef {import('./ast-to-react.js').Options} TransformOptions\n *\n * @typedef CoreOptions\n * @property {string} children\n *\n * @typedef PluginOptions\n * @property {PluggableList} [remarkPlugins=[]]\n * @property {PluggableList} [rehypePlugins=[]]\n * @property {import('remark-rehype').Options | undefined} [remarkRehypeOptions={}]\n *\n * @typedef LayoutOptions\n * @property {string} [className]\n *\n * @typedef {CoreOptions & PluginOptions & LayoutOptions & FilterOptions & TransformOptions} ReactMarkdownOptions\n *\n * @typedef Deprecation\n * @property {string} id\n * @property {string} [to]\n */\n\nimport React from 'react'\nimport {VFile} from 'vfile'\nimport {unified} from 'unified'\nimport remarkParse from 'remark-parse'\nimport remarkRehype from 'remark-rehype'\nimport PropTypes from 'prop-types'\nimport {html} from 'property-information'\nimport rehypeFilter from './rehype-filter.js'\nimport {childrenToReact} from './ast-to-react.js'\n\nconst own = {}.hasOwnProperty\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/** @type {Record<string, Deprecation>} */\nconst deprecated = {\n  plugins: {to: 'remarkPlugins', id: 'change-plugins-to-remarkplugins'},\n  renderers: {to: 'components', id: 'change-renderers-to-components'},\n  astPlugins: {id: 'remove-buggy-html-in-markdown-parser'},\n  allowDangerousHtml: {id: 'remove-buggy-html-in-markdown-parser'},\n  escapeHtml: {id: 'remove-buggy-html-in-markdown-parser'},\n  source: {to: 'children', id: 'change-source-to-children'},\n  allowNode: {\n    to: 'allowElement',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  allowedTypes: {\n    to: 'allowedElements',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  disallowedTypes: {\n    to: 'disallowedElements',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  includeNodeIndex: {\n    to: 'includeElementIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  }\n}\n\n/**\n * React component to render markdown.\n *\n * @param {ReactMarkdownOptions} options\n * @returns {ReactElement}\n */\nexport function ReactMarkdown(options) {\n  for (const key in deprecated) {\n    if (own.call(deprecated, key) && own.call(options, key)) {\n      const deprecation = deprecated[key]\n      console.warn(\n        `[react-markdown] Warning: please ${\n          deprecation.to ? `use \\`${deprecation.to}\\` instead of` : 'remove'\n        } \\`${key}\\` (see <${changelog}#${deprecation.id}> for more info)`\n      )\n      delete deprecated[key]\n    }\n  }\n\n  const processor = unified()\n    .use(remarkParse)\n    .use(options.remarkPlugins || [])\n    .use(remarkRehype, {\n      ...options.remarkRehypeOptions,\n      allowDangerousHtml: true\n    })\n    .use(options.rehypePlugins || [])\n    .use(rehypeFilter, options)\n\n  const file = new VFile()\n\n  if (typeof options.children === 'string') {\n    file.value = options.children\n  } else if (options.children !== undefined && options.children !== null) {\n    console.warn(\n      `[react-markdown] Warning: please pass a string as \\`children\\` (not: \\`${options.children}\\`)`\n    )\n  }\n\n  const hastNode = processor.runSync(processor.parse(file), file)\n\n  if (hastNode.type !== 'root') {\n    throw new TypeError('Expected a `root` node')\n  }\n\n  /** @type {ReactElement} */\n  let result = React.createElement(\n    React.Fragment,\n    {},\n    childrenToReact({options, schema: html, listDepth: 0}, hastNode)\n  )\n\n  if (options.className) {\n    result = React.createElement('div', {className: options.className}, result)\n  }\n\n  return result\n}\n\nReactMarkdown.propTypes = {\n  // Core options:\n  children: PropTypes.string,\n  // Layout options:\n  className: PropTypes.string,\n  // Filter options:\n  allowElement: PropTypes.func,\n  allowedElements: PropTypes.arrayOf(PropTypes.string),\n  disallowedElements: PropTypes.arrayOf(PropTypes.string),\n  unwrapDisallowed: PropTypes.bool,\n  // Plugin options:\n  remarkPlugins: PropTypes.arrayOf(\n    PropTypes.oneOfType([\n      PropTypes.object,\n      PropTypes.func,\n      PropTypes.arrayOf(\n        PropTypes.oneOfType([\n          PropTypes.bool,\n          PropTypes.string,\n          PropTypes.object,\n          PropTypes.func,\n          PropTypes.arrayOf(\n            // prettier-ignore\n            // type-coverage:ignore-next-line\n            PropTypes.any\n          )\n        ])\n      )\n    ])\n  ),\n  rehypePlugins: PropTypes.arrayOf(\n    PropTypes.oneOfType([\n      PropTypes.object,\n      PropTypes.func,\n      PropTypes.arrayOf(\n        PropTypes.oneOfType([\n          PropTypes.bool,\n          PropTypes.string,\n          PropTypes.object,\n          PropTypes.func,\n          PropTypes.arrayOf(\n            // prettier-ignore\n            // type-coverage:ignore-next-line\n            PropTypes.any\n          )\n        ])\n      )\n    ])\n  ),\n  // Transform options:\n  sourcePos: PropTypes.bool,\n  rawSourcePos: PropTypes.bool,\n  skipHtml: PropTypes.bool,\n  includeElementIndex: PropTypes.bool,\n  transformLinkUri: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  linkTarget: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n  transformImageUri: PropTypes.func,\n  components: PropTypes.object\n}\n", "const protocols = ['http', 'https', 'mailto', 'tel']\n\n/**\n * @param {string} uri\n * @returns {string}\n */\nexport function uriTransformer(uri) {\n  const url = (uri || '').trim()\n  const first = url.charAt(0)\n\n  if (first === '#' || first === '/') {\n    return url\n  }\n\n  const colon = url.indexOf(':')\n  if (colon === -1) {\n    return url\n  }\n\n  let index = -1\n\n  while (++index < protocols.length) {\n    const protocol = protocols[index]\n\n    if (\n      colon === protocol.length &&\n      url.slice(0, protocol.length).toLowerCase() === protocol\n    ) {\n      return url\n    }\n  }\n\n  index = url.indexOf('?')\n  if (index !== -1 && colon > index) {\n    return url\n  }\n\n  index = url.indexOf('#')\n  if (index !== -1 && colon > index) {\n    return url\n  }\n\n  // eslint-disable-next-line no-script-url\n  return 'javascript:void(0)'\n}\n", "/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-from-markdown').Options} Options\n */\n\nimport {fromMarkdown} from 'mdast-util-from-markdown'\n\n/**\n * @this {import('unified').Processor}\n * @type {import('unified').Plugin<[Options?] | void[], string, Root>}\n */\nexport default function remarkParse(options) {\n  /** @type {import('unified').ParserFunction<Root>} */\n  const parser = (doc) => {\n    // Assume options.\n    const settings = /** @type {Options} */ (this.data('settings'))\n\n    return fromMarkdown(\n      doc,\n      Object.assign({}, settings, options, {\n        // Note: these options are not in the readme.\n        // The goal is for them to be set by plugins on `data` instead of being\n        // passed by users.\n        extensions: this.data('micromarkExtensions') || [],\n        mdastExtensions: this.data('fromMarkdownExtensions') || []\n      })\n    )\n  }\n\n  Object.assign(this, {Parser: parser})\n}\n", "/**\n * Parse space-separated tokens to an array of strings.\n *\n * @param {string} value\n *   Space-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nexport function parse(value) {\n  const input = String(value || '').trim()\n  return input ? input.split(/[ \\t\\n\\r\\f]+/g) : []\n}\n\n/**\n * Serialize an array of strings as space separated-tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @returns {string}\n *   Space-separated tokens.\n */\nexport function stringify(values) {\n  return values.join(' ').trim()\n}\n", "/**\n * Throw a given error.\n *\n * @param {Error|null|undefined} [error]\n *   Maybe error.\n * @returns {asserts error is null|undefined}\n */\nexport function bail(error) {\n  if (error) {\n    throw error\n  }\n}\n", "export default function isPlainObject(value) {\n\tif (typeof value !== 'object' || value === null) {\n\t\treturn false;\n\t}\n\n\tconst prototype = Object.getPrototypeOf(value);\n\treturn (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in value) && !(Symbol.iterator in value);\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('vfile').VFileCompatible} VFileCompatible\n * @typedef {import('vfile').VFileValue} VFileValue\n * @typedef {import('..').Processor} Processor\n * @typedef {import('..').Plugin} Plugin\n * @typedef {import('..').Preset} Preset\n * @typedef {import('..').Pluggable} Pluggable\n * @typedef {import('..').PluggableList} PluggableList\n * @typedef {import('..').Transformer} Transformer\n * @typedef {import('..').Parser} Parser\n * @typedef {import('..').Compiler} Compiler\n * @typedef {import('..').RunCallback} RunCallback\n * @typedef {import('..').ProcessCallback} ProcessCallback\n *\n * @typedef Context\n * @property {Node} tree\n * @property {VFile} file\n */\n\nimport {bail} from 'bail'\nimport isBuffer from 'is-buffer'\nimport extend from 'extend'\nimport isPlainObj from 'is-plain-obj'\nimport {trough} from 'trough'\nimport {VFile} from 'vfile'\n\n// Expose a frozen processor.\nexport const unified = base().freeze()\n\nconst own = {}.hasOwnProperty\n\n// Function to create the first processor.\n/**\n * @returns {Processor}\n */\nfunction base() {\n  const transformers = trough()\n  /** @type {Processor['attachers']} */\n  const attachers = []\n  /** @type {Record<string, unknown>} */\n  let namespace = {}\n  /** @type {boolean|undefined} */\n  let frozen\n  let freezeIndex = -1\n\n  // Data management.\n  // @ts-expect-error: overloads are handled.\n  processor.data = data\n  processor.Parser = undefined\n  processor.Compiler = undefined\n\n  // Lock.\n  processor.freeze = freeze\n\n  // Plugins.\n  processor.attachers = attachers\n  // @ts-expect-error: overloads are handled.\n  processor.use = use\n\n  // API.\n  processor.parse = parse\n  processor.stringify = stringify\n  // @ts-expect-error: overloads are handled.\n  processor.run = run\n  processor.runSync = runSync\n  // @ts-expect-error: overloads are handled.\n  processor.process = process\n  processor.processSync = processSync\n\n  // Expose.\n  return processor\n\n  // Create a new processor based on the processor in the current scope.\n  /** @type {Processor} */\n  function processor() {\n    const destination = base()\n    let index = -1\n\n    while (++index < attachers.length) {\n      destination.use(...attachers[index])\n    }\n\n    destination.data(extend(true, {}, namespace))\n\n    return destination\n  }\n\n  /**\n   * @param {string|Record<string, unknown>} [key]\n   * @param {unknown} [value]\n   * @returns {unknown}\n   */\n  function data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', frozen)\n        namespace[key] = value\n        return processor\n      }\n\n      // Get `key`.\n      return (own.call(namespace, key) && namespace[key]) || null\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', frozen)\n      namespace = key\n      return processor\n    }\n\n    // Get space.\n    return namespace\n  }\n\n  /** @type {Processor['freeze']} */\n  function freeze() {\n    if (frozen) {\n      return processor\n    }\n\n    while (++freezeIndex < attachers.length) {\n      const [attacher, ...options] = attachers[freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      /** @type {Transformer|void} */\n      const transformer = attacher.call(processor, ...options)\n\n      if (typeof transformer === 'function') {\n        transformers.use(transformer)\n      }\n    }\n\n    frozen = true\n    freezeIndex = Number.POSITIVE_INFINITY\n\n    return processor\n  }\n\n  /**\n   * @param {Pluggable|null|undefined} [value]\n   * @param {...unknown} options\n   * @returns {Processor}\n   */\n  function use(value, ...options) {\n    /** @type {Record<string, unknown>|undefined} */\n    let settings\n\n    assertUnfrozen('use', frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, ...options)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    if (settings) {\n      namespace.settings = Object.assign(namespace.settings || {}, settings)\n    }\n\n    return processor\n\n    /**\n     * @param {import('..').Pluggable<unknown[]>} value\n     * @returns {void}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value)\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...options] = value\n          addPlugin(plugin, ...options)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {void}\n     */\n    function addPreset(result) {\n      addList(result.plugins)\n\n      if (result.settings) {\n        settings = Object.assign(settings || {}, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList|null|undefined} [plugins]\n     * @returns {void}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {...unknown} [value]\n     * @returns {void}\n     */\n    function addPlugin(plugin, value) {\n      let index = -1\n      /** @type {Processor['attachers'][number]|undefined} */\n      let entry\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entry = attachers[index]\n          break\n        }\n      }\n\n      if (entry) {\n        if (isPlainObj(entry[1]) && isPlainObj(value)) {\n          value = extend(true, entry[1], value)\n        }\n\n        entry[1] = value\n      } else {\n        // @ts-expect-error: fine.\n        attachers.push([...arguments])\n      }\n    }\n  }\n\n  /** @type {Processor['parse']} */\n  function parse(doc) {\n    processor.freeze()\n    const file = vfile(doc)\n    const Parser = processor.Parser\n    assertParser('parse', Parser)\n\n    if (newable(Parser, 'parse')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Parser(String(file), file).parse()\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Parser(String(file), file) // eslint-disable-line new-cap\n  }\n\n  /** @type {Processor['stringify']} */\n  function stringify(node, doc) {\n    processor.freeze()\n    const file = vfile(doc)\n    const Compiler = processor.Compiler\n    assertCompiler('stringify', Compiler)\n    assertNode(node)\n\n    if (newable(Compiler, 'compile')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Compiler(node, file).compile()\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Compiler(node, file) // eslint-disable-line new-cap\n  }\n\n  /**\n   * @param {Node} node\n   * @param {VFileCompatible|RunCallback} [doc]\n   * @param {RunCallback} [callback]\n   * @returns {Promise<Node>|void}\n   */\n  function run(node, doc, callback) {\n    assertNode(node)\n    processor.freeze()\n\n    if (!callback && typeof doc === 'function') {\n      callback = doc\n      doc = undefined\n    }\n\n    if (!callback) {\n      return new Promise(executor)\n    }\n\n    executor(null, callback)\n\n    /**\n     * @param {null|((node: Node) => void)} resolve\n     * @param {(error: Error) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      // @ts-expect-error: `doc` can’t be a callback anymore, we checked.\n      transformers.run(node, vfile(doc), done)\n\n      /**\n       * @param {Error|null} error\n       * @param {Node} tree\n       * @param {VFile} file\n       * @returns {void}\n       */\n      function done(error, tree, file) {\n        tree = tree || node\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(tree)\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, tree, file)\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['runSync']} */\n  function runSync(node, file) {\n    /** @type {Node|undefined} */\n    let result\n    /** @type {boolean|undefined} */\n    let complete\n\n    processor.run(node, file, done)\n\n    assertDone('runSync', 'run', complete)\n\n    // @ts-expect-error: we either bailed on an error or have a tree.\n    return result\n\n    /**\n     * @param {Error|null} [error]\n     * @param {Node} [tree]\n     * @returns {void}\n     */\n    function done(error, tree) {\n      bail(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * @param {VFileCompatible} doc\n   * @param {ProcessCallback} [callback]\n   * @returns {Promise<VFile>|undefined}\n   */\n  function process(doc, callback) {\n    processor.freeze()\n    assertParser('process', processor.Parser)\n    assertCompiler('process', processor.Compiler)\n\n    if (!callback) {\n      return new Promise(executor)\n    }\n\n    executor(null, callback)\n\n    /**\n     * @param {null|((file: VFile) => void)} resolve\n     * @param {(error?: Error|null|undefined) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      const file = vfile(doc)\n\n      processor.run(processor.parse(file), file, (error, tree, file) => {\n        if (error || !tree || !file) {\n          done(error)\n        } else {\n          /** @type {unknown} */\n          const result = processor.stringify(tree, file)\n\n          if (result === undefined || result === null) {\n            // Empty.\n          } else if (looksLikeAVFileValue(result)) {\n            file.value = result\n          } else {\n            file.result = result\n          }\n\n          done(error, file)\n        }\n      })\n\n      /**\n       * @param {Error|null|undefined} [error]\n       * @param {VFile|undefined} [file]\n       * @returns {void}\n       */\n      function done(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, file)\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['processSync']} */\n  function processSync(doc) {\n    /** @type {boolean|undefined} */\n    let complete\n\n    processor.freeze()\n    assertParser('processSync', processor.Parser)\n    assertCompiler('processSync', processor.Compiler)\n\n    const file = vfile(doc)\n\n    processor.process(file, done)\n\n    assertDone('processSync', 'process', complete)\n\n    return file\n\n    /**\n     * @param {Error|null|undefined} [error]\n     * @returns {void}\n     */\n    function done(error) {\n      complete = true\n      bail(error)\n    }\n  }\n}\n\n/**\n * Check if `value` is a constructor.\n *\n * @param {unknown} value\n * @param {string} name\n * @returns {boolean}\n */\nfunction newable(value, name) {\n  return (\n    typeof value === 'function' &&\n    // Prototypes do exist.\n    // type-coverage:ignore-next-line\n    value.prototype &&\n    // A function with keys in its prototype is probably a constructor.\n    // Classes’ prototype methods are not enumerable, so we check if some value\n    // exists in the prototype.\n    // type-coverage:ignore-next-line\n    (keys(value.prototype) || name in value.prototype)\n  )\n}\n\n/**\n * Check if `value` is an object with keys.\n *\n * @param {Record<string, unknown>} value\n * @returns {boolean}\n */\nfunction keys(value) {\n  /** @type {string} */\n  let key\n\n  for (key in value) {\n    if (own.call(value, key)) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!isPlainObj(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new VFile(value)\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is VFileValue}\n */\nfunction looksLikeAVFileValue(value) {\n  return typeof value === 'string' || isBuffer(value)\n}\n", "// To do: remove `void`s\n// To do: remove `null` from output of our APIs, allow it as user APIs.\n\n/**\n * @typedef {(error?: Error | null | undefined, ...output: Array<any>) => void} Callback\n *   Callback.\n *\n * @typedef {(...input: Array<any>) => any} Middleware\n *   Ware.\n *\n * @typedef Pipeline\n *   Pipeline.\n * @property {Run} run\n *   Run the pipeline.\n * @property {Use} use\n *   Add middleware.\n *\n * @typedef {(...input: Array<any>) => void} Run\n *   Call all middleware.\n *\n *   Calls `done` on completion with either an error or the output of the\n *   last middleware.\n *\n *   > 👉 **Note**: as the length of input defines whether async functions get a\n *   > `next` function,\n *   > it’s recommended to keep `input` at one value normally.\n\n *\n * @typedef {(fn: Middleware) => Pipeline} Use\n *   Add middleware.\n */\n\n/**\n * Create new middleware.\n *\n * @returns {Pipeline}\n *   Pipeline.\n */\nexport function trough() {\n  /** @type {Array<Middleware>} */\n  const fns = []\n  /** @type {Pipeline} */\n  const pipeline = {run, use}\n\n  return pipeline\n\n  /** @type {Run} */\n  function run(...values) {\n    let middlewareIndex = -1\n    /** @type {Callback} */\n    const callback = values.pop()\n\n    if (typeof callback !== 'function') {\n      throw new TypeError('Expected function as last argument, not ' + callback)\n    }\n\n    next(null, ...values)\n\n    /**\n     * Run the next `fn`, or we’re done.\n     *\n     * @param {Error | null | undefined} error\n     * @param {Array<any>} output\n     */\n    function next(error, ...output) {\n      const fn = fns[++middlewareIndex]\n      let index = -1\n\n      if (error) {\n        callback(error)\n        return\n      }\n\n      // Copy non-nullish input into values.\n      while (++index < values.length) {\n        if (output[index] === null || output[index] === undefined) {\n          output[index] = values[index]\n        }\n      }\n\n      // Save the newly created `output` for the next call.\n      values = output\n\n      // Next or done.\n      if (fn) {\n        wrap(fn, next)(...output)\n      } else {\n        callback(null, ...output)\n      }\n    }\n  }\n\n  /** @type {Use} */\n  function use(middelware) {\n    if (typeof middelware !== 'function') {\n      throw new TypeError(\n        'Expected `middelware` to be a function, not ' + middelware\n      )\n    }\n\n    fns.push(middelware)\n    return pipeline\n  }\n}\n\n/**\n * Wrap `middleware` into a uniform interface.\n *\n * You can pass all input to the resulting function.\n * `callback` is then called with the output of `middleware`.\n *\n * If `middleware` accepts more arguments than the later given in input,\n * an extra `done` function is passed to it after that input,\n * which must be called by `middleware`.\n *\n * The first value in `input` is the main input value.\n * All other input values are the rest input values.\n * The values given to `callback` are the input values,\n * merged with every non-nullish output value.\n *\n * * if `middleware` throws an error,\n *   returns a promise that is rejected,\n *   or calls the given `done` function with an error,\n *   `callback` is called with that error\n * * if `middleware` returns a value or returns a promise that is resolved,\n *   that value is the main output value\n * * if `middleware` calls `done`,\n *   all non-nullish values except for the first one (the error) overwrite the\n *   output values\n *\n * @param {Middleware} middleware\n *   Function to wrap.\n * @param {Callback} callback\n *   Callback called with the output of `middleware`.\n * @returns {Run}\n *   Wrapped middleware.\n */\nexport function wrap(middleware, callback) {\n  /** @type {boolean} */\n  let called\n\n  return wrapped\n\n  /**\n   * Call `middleware`.\n   * @this {any}\n   * @param {Array<any>} parameters\n   * @returns {void}\n   */\n  function wrapped(...parameters) {\n    const fnExpectsCallback = middleware.length > parameters.length\n    /** @type {any} */\n    let result\n\n    if (fnExpectsCallback) {\n      parameters.push(done)\n    }\n\n    try {\n      result = middleware.apply(this, parameters)\n    } catch (error) {\n      const exception = /** @type {Error} */ (error)\n\n      // Well, this is quite the pickle.\n      // `middleware` received a callback and called it synchronously, but that\n      // threw an error.\n      // The only thing left to do is to throw the thing instead.\n      if (fnExpectsCallback && called) {\n        throw exception\n      }\n\n      return done(exception)\n    }\n\n    if (!fnExpectsCallback) {\n      if (result && result.then && typeof result.then === 'function') {\n        result.then(then, done)\n      } else if (result instanceof Error) {\n        done(result)\n      } else {\n        then(result)\n      }\n    }\n  }\n\n  /**\n   * Call `callback`, only once.\n   *\n   * @type {Callback}\n   */\n  function done(error, ...output) {\n    if (!called) {\n      called = true\n      callback(error, ...output)\n    }\n  }\n\n  /**\n   * Call `done` with one value.\n   *\n   * @param {any} [value]\n   */\n  function then(value) {\n    done(null, value)\n  }\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\nimport {stringifyPosition} from 'unist-util-stringify-position'\n\n/**\n * Message.\n */\nexport class VFileMessage extends Error {\n  /**\n   * Create a message for `reason` at `place` from `origin`.\n   *\n   * When an error is passed in as `reason`, the `stack` is copied.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   *\n   *   > 👉 **Note**: you should use markdown.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns\n   *   Instance of `VFileMessage`.\n   */\n  // To do: next major: expose `undefined` everywhere instead of `null`.\n  constructor(reason, place, origin) {\n    /** @type {[string | null, string | null]} */\n    const parts = [null, null]\n    /** @type {Position} */\n    let position = {\n      // @ts-expect-error: we always follows the structure of `position`.\n      start: {line: null, column: null},\n      // @ts-expect-error: \"\n      end: {line: null, column: null}\n    }\n\n    super()\n\n    if (typeof place === 'string') {\n      origin = place\n      place = undefined\n    }\n\n    if (typeof origin === 'string') {\n      const index = origin.indexOf(':')\n\n      if (index === -1) {\n        parts[1] = origin\n      } else {\n        parts[0] = origin.slice(0, index)\n        parts[1] = origin.slice(index + 1)\n      }\n    }\n\n    if (place) {\n      // Node.\n      if ('type' in place || 'position' in place) {\n        if (place.position) {\n          // To do: next major: deep clone.\n          // @ts-expect-error: looks like a position.\n          position = place.position\n        }\n      }\n      // Position.\n      else if ('start' in place || 'end' in place) {\n        // @ts-expect-error: looks like a position.\n        // To do: next major: deep clone.\n        position = place\n      }\n      // Point.\n      else if ('line' in place || 'column' in place) {\n        // To do: next major: deep clone.\n        position.start = place\n      }\n    }\n\n    // Fields from `Error`.\n    /**\n     * Serialized positional info of error.\n     *\n     * On normal errors, this would be something like `ParseError`, buit in\n     * `VFile` messages we use this space to show where an error happened.\n     */\n    this.name = stringifyPosition(place) || '1:1'\n\n    /**\n     * Reason for message.\n     *\n     * @type {string}\n     */\n    this.message = typeof reason === 'object' ? reason.message : reason\n\n    /**\n     * Stack of message.\n     *\n     * This is used by normal errors to show where something happened in\n     * programming code, irrelevant for `VFile` messages,\n     *\n     * @type {string}\n     */\n    this.stack = ''\n\n    if (typeof reason === 'object' && reason.stack) {\n      this.stack = reason.stack\n    }\n\n    /**\n     * Reason for message.\n     *\n     * > 👉 **Note**: you should use markdown.\n     *\n     * @type {string}\n     */\n    this.reason = this.message\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * State of problem.\n     *\n     * * `true` — marks associated file as no longer processable (error)\n     * * `false` — necessitates a (potential) change (warning)\n     * * `null | undefined` — for things that might not need changing (info)\n     *\n     * @type {boolean | null | undefined}\n     */\n    this.fatal\n\n    /**\n     * Starting line of error.\n     *\n     * @type {number | null}\n     */\n    this.line = position.start.line\n\n    /**\n     * Starting column of error.\n     *\n     * @type {number | null}\n     */\n    this.column = position.start.column\n\n    /**\n     * Full unist position.\n     *\n     * @type {Position | null}\n     */\n    this.position = position\n\n    /**\n     * Namespace of message (example: `'my-package'`).\n     *\n     * @type {string | null}\n     */\n    this.source = parts[0]\n\n    /**\n     * Category of message (example: `'my-rule'`).\n     *\n     * @type {string | null}\n     */\n    this.ruleId = parts[1]\n\n    /**\n     * Path of a file (used throughout the `VFile` ecosystem).\n     *\n     * @type {string | null}\n     */\n    this.file\n\n    // The following fields are “well known”.\n    // Not standard.\n    // Feel free to add other non-standard fields to your messages.\n\n    /**\n     * Specify the source value that’s being reported, which is deemed\n     * incorrect.\n     *\n     * @type {string | null}\n     */\n    this.actual\n\n    /**\n     * Suggest acceptable values that can be used instead of `actual`.\n     *\n     * @type {Array<string> | null}\n     */\n    this.expected\n\n    /**\n     * Link to docs for the message.\n     *\n     * > 👉 **Note**: this must be an absolute URL that can be passed as `x`\n     * > to `new URL(x)`.\n     *\n     * @type {string | null}\n     */\n    this.url\n\n    /**\n     * Long form description of the message (you should use markdown).\n     *\n     * @type {string | null}\n     */\n    this.note\n    /* eslint-enable no-unused-expressions */\n  }\n}\n\nVFileMessage.prototype.file = ''\nVFileMessage.prototype.name = ''\nVFileMessage.prototype.reason = ''\nVFileMessage.prototype.message = ''\nVFileMessage.prototype.stack = ''\nVFileMessage.prototype.fatal = null\nVFileMessage.prototype.column = null\nVFileMessage.prototype.line = null\nVFileMessage.prototype.source = null\nVFileMessage.prototype.ruleId = null\nVFileMessage.prototype.position = null\n", "// A derivative work based on:\n// <https://github.com/browserify/path-browserify>.\n// Which is licensed:\n//\n// MIT License\n//\n// Copyright (c) 2013 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of\n// this software and associated documentation files (the \"Software\"), to deal in\n// the Software without restriction, including without limitation the rights to\n// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n// the Software, and to permit persons to whom the Software is furnished to do so,\n// subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\n// FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\n// IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n// CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n// A derivative work based on:\n//\n// Parts of that are extracted from Node’s internal `path` module:\n// <https://github.com/nodejs/node/blob/master/lib/path.js>.\n// Which is licensed:\n//\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nexport const path = {basename, dirname, extname, join, sep: '/'}\n\n/* eslint-disable max-depth, complexity */\n\n/**\n * Get the basename from a path.\n *\n * @param {string} path\n *   File path.\n * @param {string | undefined} [ext]\n *   Extension to strip.\n * @returns {string}\n *   Stem or basename.\n */\nfunction basename(path, ext) {\n  if (ext !== undefined && typeof ext !== 'string') {\n    throw new TypeError('\"ext\" argument must be a string')\n  }\n\n  assertPath(path)\n  let start = 0\n  let end = -1\n  let index = path.length\n  /** @type {boolean | undefined} */\n  let seenNonSlash\n\n  if (ext === undefined || ext.length === 0 || ext.length > path.length) {\n    while (index--) {\n      if (path.charCodeAt(index) === 47 /* `/` */) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now.\n        if (seenNonSlash) {\n          start = index + 1\n          break\n        }\n      } else if (end < 0) {\n        // We saw the first non-path separator, mark this as the end of our\n        // path component.\n        seenNonSlash = true\n        end = index + 1\n      }\n    }\n\n    return end < 0 ? '' : path.slice(start, end)\n  }\n\n  if (ext === path) {\n    return ''\n  }\n\n  let firstNonSlashEnd = -1\n  let extIndex = ext.length - 1\n\n  while (index--) {\n    if (path.charCodeAt(index) === 47 /* `/` */) {\n      // If we reached a path separator that was not part of a set of path\n      // separators at the end of the string, stop now.\n      if (seenNonSlash) {\n        start = index + 1\n        break\n      }\n    } else {\n      if (firstNonSlashEnd < 0) {\n        // We saw the first non-path separator, remember this index in case\n        // we need it if the extension ends up not matching.\n        seenNonSlash = true\n        firstNonSlashEnd = index + 1\n      }\n\n      if (extIndex > -1) {\n        // Try to match the explicit extension.\n        if (path.charCodeAt(index) === ext.charCodeAt(extIndex--)) {\n          if (extIndex < 0) {\n            // We matched the extension, so mark this as the end of our path\n            // component\n            end = index\n          }\n        } else {\n          // Extension does not match, so our result is the entire path\n          // component\n          extIndex = -1\n          end = firstNonSlashEnd\n        }\n      }\n    }\n  }\n\n  if (start === end) {\n    end = firstNonSlashEnd\n  } else if (end < 0) {\n    end = path.length\n  }\n\n  return path.slice(start, end)\n}\n\n/**\n * Get the dirname from a path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   File path.\n */\nfunction dirname(path) {\n  assertPath(path)\n\n  if (path.length === 0) {\n    return '.'\n  }\n\n  let end = -1\n  let index = path.length\n  /** @type {boolean | undefined} */\n  let unmatchedSlash\n\n  // Prefix `--` is important to not run on `0`.\n  while (--index) {\n    if (path.charCodeAt(index) === 47 /* `/` */) {\n      if (unmatchedSlash) {\n        end = index\n        break\n      }\n    } else if (!unmatchedSlash) {\n      // We saw the first non-path separator\n      unmatchedSlash = true\n    }\n  }\n\n  return end < 0\n    ? path.charCodeAt(0) === 47 /* `/` */\n      ? '/'\n      : '.'\n    : end === 1 && path.charCodeAt(0) === 47 /* `/` */\n    ? '//'\n    : path.slice(0, end)\n}\n\n/**\n * Get an extname from a path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   Extname.\n */\nfunction extname(path) {\n  assertPath(path)\n\n  let index = path.length\n\n  let end = -1\n  let startPart = 0\n  let startDot = -1\n  // Track the state of characters (if any) we see before our first dot and\n  // after any path separator we find.\n  let preDotState = 0\n  /** @type {boolean | undefined} */\n  let unmatchedSlash\n\n  while (index--) {\n    const code = path.charCodeAt(index)\n\n    if (code === 47 /* `/` */) {\n      // If we reached a path separator that was not part of a set of path\n      // separators at the end of the string, stop now.\n      if (unmatchedSlash) {\n        startPart = index + 1\n        break\n      }\n\n      continue\n    }\n\n    if (end < 0) {\n      // We saw the first non-path separator, mark this as the end of our\n      // extension.\n      unmatchedSlash = true\n      end = index + 1\n    }\n\n    if (code === 46 /* `.` */) {\n      // If this is our first dot, mark it as the start of our extension.\n      if (startDot < 0) {\n        startDot = index\n      } else if (preDotState !== 1) {\n        preDotState = 1\n      }\n    } else if (startDot > -1) {\n      // We saw a non-dot and non-path separator before our dot, so we should\n      // have a good chance at having a non-empty extension.\n      preDotState = -1\n    }\n  }\n\n  if (\n    startDot < 0 ||\n    end < 0 ||\n    // We saw a non-dot character immediately before the dot.\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly `..`.\n    (preDotState === 1 && startDot === end - 1 && startDot === startPart + 1)\n  ) {\n    return ''\n  }\n\n  return path.slice(startDot, end)\n}\n\n/**\n * Join segments from a path.\n *\n * @param {Array<string>} segments\n *   Path segments.\n * @returns {string}\n *   File path.\n */\nfunction join(...segments) {\n  let index = -1\n  /** @type {string | undefined} */\n  let joined\n\n  while (++index < segments.length) {\n    assertPath(segments[index])\n\n    if (segments[index]) {\n      joined =\n        joined === undefined ? segments[index] : joined + '/' + segments[index]\n    }\n  }\n\n  return joined === undefined ? '.' : normalize(joined)\n}\n\n/**\n * Normalize a basic file path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   File path.\n */\n// Note: `normalize` is not exposed as `path.normalize`, so some code is\n// manually removed from it.\nfunction normalize(path) {\n  assertPath(path)\n\n  const absolute = path.charCodeAt(0) === 47 /* `/` */\n\n  // Normalize the path according to POSIX rules.\n  let value = normalizeString(path, !absolute)\n\n  if (value.length === 0 && !absolute) {\n    value = '.'\n  }\n\n  if (value.length > 0 && path.charCodeAt(path.length - 1) === 47 /* / */) {\n    value += '/'\n  }\n\n  return absolute ? '/' + value : value\n}\n\n/**\n * Resolve `.` and `..` elements in a path with directory names.\n *\n * @param {string} path\n *   File path.\n * @param {boolean} allowAboveRoot\n *   Whether `..` can move above root.\n * @returns {string}\n *   File path.\n */\nfunction normalizeString(path, allowAboveRoot) {\n  let result = ''\n  let lastSegmentLength = 0\n  let lastSlash = -1\n  let dots = 0\n  let index = -1\n  /** @type {number | undefined} */\n  let code\n  /** @type {number} */\n  let lastSlashIndex\n\n  while (++index <= path.length) {\n    if (index < path.length) {\n      code = path.charCodeAt(index)\n    } else if (code === 47 /* `/` */) {\n      break\n    } else {\n      code = 47 /* `/` */\n    }\n\n    if (code === 47 /* `/` */) {\n      if (lastSlash === index - 1 || dots === 1) {\n        // Empty.\n      } else if (lastSlash !== index - 1 && dots === 2) {\n        if (\n          result.length < 2 ||\n          lastSegmentLength !== 2 ||\n          result.charCodeAt(result.length - 1) !== 46 /* `.` */ ||\n          result.charCodeAt(result.length - 2) !== 46 /* `.` */\n        ) {\n          if (result.length > 2) {\n            lastSlashIndex = result.lastIndexOf('/')\n\n            if (lastSlashIndex !== result.length - 1) {\n              if (lastSlashIndex < 0) {\n                result = ''\n                lastSegmentLength = 0\n              } else {\n                result = result.slice(0, lastSlashIndex)\n                lastSegmentLength = result.length - 1 - result.lastIndexOf('/')\n              }\n\n              lastSlash = index\n              dots = 0\n              continue\n            }\n          } else if (result.length > 0) {\n            result = ''\n            lastSegmentLength = 0\n            lastSlash = index\n            dots = 0\n            continue\n          }\n        }\n\n        if (allowAboveRoot) {\n          result = result.length > 0 ? result + '/..' : '..'\n          lastSegmentLength = 2\n        }\n      } else {\n        if (result.length > 0) {\n          result += '/' + path.slice(lastSlash + 1, index)\n        } else {\n          result = path.slice(lastSlash + 1, index)\n        }\n\n        lastSegmentLength = index - lastSlash - 1\n      }\n\n      lastSlash = index\n      dots = 0\n    } else if (code === 46 /* `.` */ && dots > -1) {\n      dots++\n    } else {\n      dots = -1\n    }\n  }\n\n  return result\n}\n\n/**\n * Make sure `path` is a string.\n *\n * @param {string} path\n *   File path.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError(\n      'Path must be a string. Received ' + JSON.stringify(path)\n    )\n  }\n}\n\n/* eslint-enable max-depth, complexity */\n", "// Somewhat based on:\n// <https://github.com/defunctzombie/node-process/blob/master/browser.js>.\n// But I don’t think one tiny line of code can be copyrighted. 😅\nexport const proc = {cwd}\n\nfunction cwd() {\n  return '/'\n}\n", "/**\n * @typedef URL\n * @property {string} hash\n * @property {string} host\n * @property {string} hostname\n * @property {string} href\n * @property {string} origin\n * @property {string} password\n * @property {string} pathname\n * @property {string} port\n * @property {string} protocol\n * @property {string} search\n * @property {any} searchParams\n * @property {string} username\n * @property {() => string} toString\n * @property {() => string} toJSON\n */\n\n/**\n * Check if `fileUrlOrPath` looks like a URL.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */\n// From: <https://github.com/nodejs/node/blob/fcf8ba4/lib/internal/url.js#L1501>\nexport function isUrl(fileUrlOrPath) {\n  return (\n    fileUrlOrPath !== null &&\n    typeof fileUrlOrPath === 'object' &&\n    // @ts-expect-error: indexable.\n    fileUrlOrPath.href &&\n    // @ts-expect-error: indexable.\n    fileUrlOrPath.origin\n  )\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {import('./minurl.shared.js').URL} URL\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Value} Value\n */\n\n/**\n * @typedef {Record<string, unknown> & {type: string, position?: Position | undefined}} NodeLike\n *\n * @typedef {'ascii' | 'utf8' | 'utf-8' | 'utf16le' | 'ucs2' | 'ucs-2' | 'base64' | 'base64url' | 'latin1' | 'binary' | 'hex'} BufferEncoding\n *   Encodings supported by the buffer class.\n *\n *   This is a copy of the types from Node, copied to prevent Node globals from\n *   being needed.\n *   Copied from: <https://github.com/DefinitelyTyped/DefinitelyTyped/blob/90a4ec8/types/node/buffer.d.ts#L170>\n *\n * @typedef {Options | URL | Value | VFile} Compatible\n *   Things that can be passed to the constructor.\n *\n * @typedef VFileCoreOptions\n *   Set multiple values.\n * @property {Value | null | undefined} [value]\n *   Set `value`.\n * @property {string | null | undefined} [cwd]\n *   Set `cwd`.\n * @property {Array<string> | null | undefined} [history]\n *   Set `history`.\n * @property {URL | string | null | undefined} [path]\n *   Set `path`.\n * @property {string | null | undefined} [basename]\n *   Set `basename`.\n * @property {string | null | undefined} [stem]\n *   Set `stem`.\n * @property {string | null | undefined} [extname]\n *   Set `extname`.\n * @property {string | null | undefined} [dirname]\n *   Set `dirname`.\n * @property {Data | null | undefined} [data]\n *   Set `data`.\n *\n * @typedef Map\n *   Raw source map.\n *\n *   See:\n *   <https://github.com/mozilla/source-map/blob/58819f0/source-map.d.ts#L15-L23>.\n * @property {number} version\n *   Which version of the source map spec this map is following.\n * @property {Array<string>} sources\n *   An array of URLs to the original source files.\n * @property {Array<string>} names\n *   An array of identifiers which can be referenced by individual mappings.\n * @property {string | undefined} [sourceRoot]\n *   The URL root from which all sources are relative.\n * @property {Array<string> | undefined} [sourcesContent]\n *   An array of contents of the original source files.\n * @property {string} mappings\n *   A string of base64 VLQs which contain the actual mappings.\n * @property {string} file\n *   The generated file this source map is associated with.\n *\n * @typedef {{[key: string]: unknown} & VFileCoreOptions} Options\n *   Configuration.\n *\n *   A bunch of keys that will be shallow copied over to the new file.\n *\n * @typedef {Record<string, unknown>} ReporterSettings\n *   Configuration for reporters.\n */\n\n/**\n * @template {ReporterSettings} Settings\n *   Options type.\n * @callback Reporter\n *   Type for a reporter.\n * @param {Array<VFile>} files\n *   Files to report.\n * @param {Settings} options\n *   Configuration.\n * @returns {string}\n *   Report.\n */\n\nimport bufferLike from 'is-buffer'\nimport {VFileMessage} from 'vfile-message'\nimport {path} from './minpath.js'\nimport {proc} from './minproc.js'\nimport {urlToPath, isUrl} from './minurl.js'\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n *\n * @type {Array<'basename' | 'dirname' | 'extname' | 'history' | 'path' | 'stem'>}\n */\nconst order = ['history', 'path', 'basename', 'stem', 'extname', 'dirname']\n\nexport class VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Buffer` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options\n\n    if (!value) {\n      options = {}\n    } else if (typeof value === 'string' || buffer(value)) {\n      options = {value}\n    } else if (isUrl(value)) {\n      options = {path: value}\n    } else {\n      options = value\n    }\n\n    /**\n     * Place to store custom information (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {}\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = []\n\n    /**\n     * List of filepaths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = []\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    this.cwd = proc.cwd()\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result\n\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1\n\n    while (++index < order.length) {\n      const prop = order[index]\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (\n        prop in options &&\n        options[prop] !== undefined &&\n        options[prop] !== null\n      ) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[prop] = prop === 'history' ? [...options[prop]] : options[prop]\n      }\n    }\n\n    /** @type {string} */\n    let prop\n\n    // Set non-path related properties.\n    for (prop in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(prop)) {\n        // @ts-expect-error: fine to set other things.\n        this[prop] = options[prop]\n      }\n    }\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   */\n  get path() {\n    return this.history[this.history.length - 1]\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {string | URL} path\n   */\n  set path(path) {\n    if (isUrl(path)) {\n      path = urlToPath(path)\n    }\n\n    assertNonEmpty(path, 'path')\n\n    if (this.path !== path) {\n      this.history.push(path)\n    }\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   */\n  get dirname() {\n    return typeof this.path === 'string' ? path.dirname(this.path) : undefined\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname')\n    this.path = path.join(dirname || '', this.basename)\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   */\n  get basename() {\n    return typeof this.path === 'string' ? path.basename(this.path) : undefined\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename')\n    assertPart(basename, 'basename')\n    this.path = path.join(this.dirname || '', basename)\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   */\n  get extname() {\n    return typeof this.path === 'string' ? path.extname(this.path) : undefined\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname')\n    assertPath(this.dirname, 'extname')\n\n    if (extname) {\n      if (extname.charCodeAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`')\n      }\n\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots')\n      }\n    }\n\n    this.path = path.join(this.dirname, this.stem + (extname || ''))\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   */\n  get stem() {\n    return typeof this.path === 'string'\n      ? path.basename(this.path, this.extname)\n      : undefined\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem')\n    assertPart(stem, 'stem')\n    this.path = path.join(this.dirname || '', stem + (this.extname || ''))\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * @param {BufferEncoding | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Buffer`\n   *   (default: `'utf8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    return (this.value || '').toString(encoding || undefined)\n  }\n\n  /**\n   * Create a warning message associated with the file.\n   *\n   * Its `fatal` is set to `false` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(reason, place, origin) {\n    const message = new VFileMessage(reason, place, origin)\n\n    if (this.path) {\n      message.name = this.path + ':' + message.name\n      message.file = this.path\n    }\n\n    message.fatal = false\n\n    this.messages.push(message)\n\n    return message\n  }\n\n  /**\n   * Create an info message associated with the file.\n   *\n   * Its `fatal` is set to `null` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(reason, place, origin) {\n    const message = this.message(reason, place, origin)\n\n    message.fatal = null\n\n    return message\n  }\n\n  /**\n   * Create a fatal error associated with the file.\n   *\n   * Its `fatal` is set to `true` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * > 👉 **Note**: a fatal error means that a file is no longer processable.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Message.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(reason, place, origin) {\n    const message = this.message(reason, place, origin)\n\n    message.fatal = true\n\n    throw message\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {void}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(path.sep)) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + path.sep + '`'\n    )\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n\n/**\n * Assert `value` is a buffer.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Buffer}\n *   Whether `value` is a Node.js buffer.\n */\nfunction buffer(value) {\n  return bufferLike(value)\n}\n", "/// <reference lib=\"dom\" />\n\nimport {isUrl} from './minurl.shared.js'\n\n// See: <https://github.com/nodejs/node/blob/fcf8ba4/lib/internal/url.js>\n\n/**\n * @param {string | URL} path\n *   File URL.\n * @returns {string}\n *   File URL.\n */\nexport function urlToPath(path) {\n  if (typeof path === 'string') {\n    path = new URL(path)\n  } else if (!isUrl(path)) {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError(\n      'The \"path\" argument must be of type string or an instance of URL. Received `' +\n        path +\n        '`'\n    )\n    error.code = 'ERR_INVALID_ARG_TYPE'\n    throw error\n  }\n\n  if (path.protocol !== 'file:') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError('The URL must be of scheme file')\n    error.code = 'ERR_INVALID_URL_SCHEME'\n    throw error\n  }\n\n  return getPathFromURLPosix(path)\n}\n\n/**\n * Get a path from a POSIX URL.\n *\n * @param {URL} url\n *   URL.\n * @returns {string}\n *   File path.\n */\nfunction getPathFromURLPosix(url) {\n  if (url.hostname !== '') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError(\n      'File URL host must be \"localhost\" or empty on darwin'\n    )\n    error.code = 'ERR_INVALID_FILE_URL_HOST'\n    throw error\n  }\n\n  const pathname = url.pathname\n  let index = -1\n\n  while (++index < pathname.length) {\n    if (\n      pathname.charCodeAt(index) === 37 /* `%` */ &&\n      pathname.charCodeAt(index + 1) === 50 /* `2` */\n    ) {\n      const third = pathname.charCodeAt(index + 2)\n      if (third === 70 /* `F` */ || third === 102 /* `f` */) {\n        /** @type {NodeJS.ErrnoException} */\n        const error = new TypeError(\n          'File URL path must not include encoded / characters'\n        )\n        error.code = 'ERR_INVALID_FILE_URL_PATH'\n        throw error\n      }\n    }\n  }\n\n  return decodeURIComponent(pathname)\n}\n\nexport {isUrl} from './minurl.shared.js'\n"], "names": ["hasOwn", "Object", "toStr", "defineProperty", "gOPD", "isArray", "arr", "Array", "isPlainObject", "obj", "key", "hasOwnConstructor", "hasIsPrototypeOf", "setProperty", "target", "options", "getProperty", "name", "module", "extend", "src", "copy", "copyIsArray", "clone", "arguments", "i", "length", "deep", "parse", "StyleToObject", "style", "iterator", "declaration", "property", "value", "output", "declarations", "hasIterator", "len", "stringify", "values", "settings", "input", "normalizeUri", "result", "index", "start", "skip", "code", "replace", "String", "next", "encodeURIComponent", "pointStart", "pointEnd", "type", "node", "point", "own", "clean", "footnoteReference", "state", "counter", "id", "safeId", "reuseCounter", "link", "sup", "revert", "subtype", "suffix", "contents", "head", "tail", "listItemLoose", "spread", "trimLine", "end", "startIndex", "endIndex", "handlers", "lang", "properties", "footnoteById", "no", "identifier", "def", "undefined", "text", "parent", "results", "loose", "listLoose", "children", "paragraph", "child", "rows", "firstRow", "tableContent", "body", "siblings", "tagName", "rowIndex", "align", "cellIndex", "cells", "cell", "alignValue", "trimLines", "source", "search", "match", "last", "lines", "ignore", "patch", "from", "to", "applyData", "hName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hProperties", "one", "Error", "defaultUnknownHandler", "data", "nodes", "wrap", "toHast", "tree", "createState", "dangerous", "cache", "definition", "augment", "left", "right", "ctx", "props", "foot", "footer", "listItems", "content", "referenceIndex", "backReferences", "backReference", "tailTail", "listItem", "JSON", "destination", "bridge", "file", "error", "mutate", "<PERSON><PERSON><PERSON>", "normal", "space", "merge", "definitions", "normalize", "Info", "attribute", "powers", "increment", "booleanish", "overloadedBoolean", "number", "spaceSeparated", "commaSeparated", "commaOrSpaceSeparated", "checks", "DefinedInfo", "mask", "mark", "check", "create", "prop", "info", "xlink", "_", "xml", "caseSensitiveTransform", "attributes", "caseInsensitiveTransform", "xmlns", "aria", "svg", "rehypeFilter", "TypeError", "parent_", "remove", "valid", "dash", "cap", "kebab", "$0", "camelcase", "hastToReact", "tableElements", "Set", "childrenToReact", "context", "childIndex", "toReact", "transform", "parentSchema", "schema", "addProperty", "find", "Type", "rest", "dashes", "parseStyle", "v", "k", "styleReplacer", "position", "component", "basic", "Number", "getInputElement", "Boolean", "getElementsBeforeCount", "flattenPosition", "pos", "whitespace", "thing", "count", "$1", "deprecated", "ReactMarkdown", "deprecation", "console", "processor", "hastNode", "protocols", "uriTransformer", "uri", "url", "first", "colon", "protocol", "remark<PERSON><PERSON><PERSON>", "doc", "bail", "prototype", "Symbol", "unified", "base", "frozen", "transformers", "trough", "fns", "pipeline", "run", "middlewareIndex", "callback", "fn", "middleware", "called", "parameters", "fnExpectsCallback", "done", "then", "use", "middelware", "attachers", "namespace", "freezeIndex", "assertUnfrozen", "attacher", "transformer", "addPlugin", "addList", "addPreset", "plugins", "add", "plugin", "entry", "vfile", "<PERSON><PERSON><PERSON>", "assertParser", "newable", "Compiler", "assertCompiler", "assertNode", "Promise", "executor", "resolve", "reject", "complete", "assertDone", "looksLikeAVFileValue", "keys", "asyncName", "looksLikeAVFile", "VFileMessage", "reason", "place", "origin", "parts", "path", "ext", "seenNonSlash", "assertPath", "firstNonSlashEnd", "extIndex", "unmatchedSlash", "startPart", "startDot", "preDotState", "segments", "joined", "absolute", "normalizeString", "allowAboveRoot", "lastSlashIndex", "lastSegmentLength", "lastSlash", "dots", "isUrl", "fileUrlOrPath", "order", "VFile", "buffer", "proc", "urlToPath", "URL", "getPathFromURLPosix", "pathname", "third", "decodeURIComponent", "assertNonEmpty", "dirname", "basename", "assertPart", "extname", "stem", "encoding", "message", "part"], "mappings": ";0HAEA,IAAIA,EAASC,OAAO,SAAS,CAAC,cAAc,CACxCC,EAAQD,OAAO,SAAS,CAAC,QAAQ,CACjCE,EAAiBF,OAAO,cAAc,CACtCG,EAAOH,OAAO,wBAAwB,CAEtCI,EAAU,SAAiBC,CAAG,QACjC,AAAI,AAAyB,YAAzB,OAAOC,MAAM,OAAO,CAChBA,MAAM,OAAO,CAACD,GAGfJ,AAAoB,mBAApBA,EAAM,IAAI,CAACI,EACnB,EAEIE,EAAgB,SAAuBC,CAAG,EAC7C,GAAI,CAACA,GAAOP,AAAoB,oBAApBA,EAAM,IAAI,CAACO,GACtB,MAAO,GAGR,IASIC,EATAC,EAAoBX,EAAO,IAAI,CAACS,EAAK,eACrCG,EAAmBH,EAAI,WAAW,EAAIA,EAAI,WAAW,CAAC,SAAS,EAAIT,EAAO,IAAI,CAACS,EAAI,WAAW,CAAC,SAAS,CAAE,iBAE9G,GAAIA,EAAI,WAAW,EAAI,CAACE,GAAqB,CAACC,EAC7C,MAAO,GAMR,IAAKF,KAAOD,GAEZ,OAAO,AAAe,SAARC,GAAuBV,EAAO,IAAI,CAACS,EAAKC,EACvD,EAGIG,EAAc,SAAqBC,CAAM,CAAEC,CAAO,EACjDZ,GAAkBY,AAAiB,cAAjBA,EAAQ,IAAI,CACjCZ,EAAeW,EAAQC,EAAQ,IAAI,CAAE,CACpC,WAAY,GACZ,aAAc,GACd,MAAOA,EAAQ,QAAQ,CACvB,SAAU,EACX,GAEAD,CAAM,CAACC,EAAQ,IAAI,CAAC,CAAGA,EAAQ,QAAQ,AAEzC,EAGIC,EAAc,SAAqBP,CAAG,CAAEQ,CAAI,EAC/C,GAAIA,AAAS,cAATA,EAAsB,CACzB,GAAI,CAACjB,EAAO,IAAI,CAACS,EAAKQ,GACrB,OACM,GAAIb,EAGV,OAAOA,EAAKK,EAAKQ,GAAM,KAAK,AAE9B,CAEA,OAAOR,CAAG,CAACQ,EAAK,AACjB,CAEAC,CAAAA,EAAO,OAAO,CAAG,SAASC,IAEzB,IADIJ,EAASE,EAAMG,EAAKC,EAAMC,EAAaC,EACvCT,EAASU,SAAS,CAAC,EAAE,CACrBC,EAAI,EACJC,EAASF,UAAU,MAAM,CACzBG,EAAO,GAaX,IAVsB,WAAlB,OAAOb,IACVa,EAAOb,EACPA,EAASU,SAAS,CAAC,EAAE,EAAI,CAAC,EAE1BC,EAAI,GAEDX,CAAAA,AAAU,MAAVA,GAAmB,AAAkB,UAAlB,OAAOA,GAAuB,AAAkB,YAAlB,OAAOA,CAAqB,GAChFA,CAAAA,EAAS,CAAC,GAGJW,EAAIC,EAAQ,EAAED,EAGpB,GAFAV,EAAUS,SAAS,CAACC,EAAE,CAElBV,AAAW,MAAXA,EAEH,IAAKE,KAAQF,EACZK,EAAMJ,EAAYF,EAAQG,GAItBH,IAHJO,CAAAA,EAAOL,EAAYD,EAASE,EAAI,IAK3BU,GAAQN,GAASb,CAAAA,EAAca,IAAUC,CAAAA,EAAcjB,EAAQgB,EAAI,CAAC,GACnEC,GACHA,EAAc,GACdC,EAAQH,GAAOf,EAAQe,GAAOA,EAAM,EAAE,EAEtCG,EAAQH,GAAOZ,EAAcY,GAAOA,EAAM,CAAC,EAI5CP,EAAYC,EAAQ,CAAE,KAAMG,EAAM,SAAUE,EAAOQ,EAAMJ,EAAOF,EAAM,IAG5C,SAATA,GACjBR,EAAYC,EAAQ,CAAE,KAAMG,EAAM,SAAUI,CAAK,IAQtD,OAAOP,CACR,sBC7GAI,EAAO,OAAO,CAAG,SAAmBT,CAAG,EACrC,OAAOA,AAAO,MAAPA,GAAeA,AAAmB,MAAnBA,EAAI,WAAW,EACnC,AAAoC,YAApC,OAAOA,EAAI,WAAW,CAAC,QAAQ,EAAmBA,EAAI,WAAW,CAAC,QAAQ,CAACA,EAC/E,0BCVA,IAAImB,EAAQ,EAAQ,QAapB,SAASC,EAAcC,CAAK,CAAEC,CAAQ,EACpC,IAKIC,EAGAC,EACAC,EATAC,EAAS,KACb,GAAI,CAACL,GAAS,AAAiB,UAAjB,OAAOA,EACnB,OAAOK,EAST,IAAK,IALDC,EAAeR,EAAME,GACrBO,EAAc,AAAoB,YAApB,OAAON,EAIhBN,EAAI,EAAGa,EAAMF,EAAa,MAAM,CAAEX,EAAIa,EAAKb,IAElDQ,EAAWD,AADXA,CAAAA,EAAcI,CAAY,CAACX,EAAE,AAAD,EACL,QAAQ,CAC/BS,EAAQF,EAAY,KAAK,CAErBK,EACFN,EAASE,EAAUC,EAAOF,GACjBE,IACTC,GAAWA,CAAAA,EAAS,CAAC,GACrBA,CAAM,CAACF,EAAS,CAAGC,GAIvB,OAAOC,CACT,CAEAjB,EAAO,OAAO,CAAGW,EACjBX,EAAO,OAAO,CAAd,OAAsB,CAAGW,uCCkBlB,SAASU,EAAUC,CAAM,CAAEzB,CAAO,EACvC,IAAM0B,EAAW1B,GAAW,CAAC,EAK7B,MAAO2B,AAFOF,CAAAA,AAA8B,KAA9BA,CAAM,CAACA,EAAO,MAAM,CAAG,EAAE,CAAU,IAAIA,EAAQ,GAAG,CAAGA,CAAK,EAGrE,IAAI,CACH,AAACC,CAAAA,EAAS,QAAQ,CAAG,IAAM,EAAC,EAC1B,IACCA,CAAAA,AAAqB,KAArBA,EAAS,OAAO,CAAa,GAAK,GAAE,GAExC,IAAI,EACT,0dCdO,SAASE,EAAaT,CAAK,EAEhC,IAAMU,EAAS,EAAE,CACbC,EAAQ,GACRC,EAAQ,EACRC,EAAO,EACX,KAAO,EAAEF,EAAQX,EAAM,MAAM,EAAE,CAC7B,IAAMc,EAAOd,EAAM,UAAU,CAACW,GAE1BI,EAAU,GAGd,GACED,AAAS,KAATA,GACA,SAAkBd,EAAM,UAAU,CAACW,EAAQ,KAC3C,SAAkBX,EAAM,UAAU,CAACW,EAAQ,IAE3CE,EAAO,OAGJ,GAAIC,EAAO,IACV,CAAC,oBAAoB,IAAI,CAACE,OAAO,YAAY,CAACF,KAChDC,CAAAA,EAAUC,OAAO,YAAY,CAACF,EAAI,OAIjC,GAAIA,EAAO,OAASA,EAAO,MAAO,CACrC,IAAMG,EAAOjB,EAAM,UAAU,CAACW,EAAQ,EAGlCG,CAAAA,EAAO,OAASG,EAAO,OAASA,EAAO,OACzCF,EAAUC,OAAO,YAAY,CAACF,EAAMG,GACpCJ,EAAO,GAIPE,EAAU,GAEd,MAGEA,EAAUC,OAAO,YAAY,CAACF,GAE5BC,IACFL,EAAO,IAAI,CAACV,EAAM,KAAK,CAACY,EAAOD,GAAQO,mBAAmBH,IAC1DH,EAAQD,EAAQE,EAAO,EACvBE,EAAU,IAERF,IACFF,GAASE,EACTA,EAAO,EAEX,CACA,OAAOH,EAAO,IAAI,CAAC,IAAMV,EAAM,KAAK,CAACY,EACvC,mBCpFO,IAAMO,EAAa,EAAM,SAUnBC,EAAW,EAAM,OAsB9B,SAAS,EAAMC,CAAI,EACjB,OAQA,SAAeC,CAAI,EACjB,IAAMC,EAAQ,AAACD,GAAQA,EAAK,QAAQ,EAAIA,EAAK,QAAQ,CAACD,EAAK,EAAK,CAAC,EAGjE,MAAO,CAEL,KAAME,EAAM,IAAI,EAAI,KAEpB,OAAQA,EAAM,MAAM,EAAI,KAExB,OAAQA,EAAM,MAAM,CAAG,GAAKA,EAAM,MAAM,CAAG,IAC7C,CACF,CACF,CChEA,IAAMC,EAAM,CAAC,EAAE,cAAc,CA0C7B,SAASC,EAAMzB,CAAK,EAClB,OAAOgB,OAAOhB,GAAS,IAAI,WAAW,EACxC,CC7CO,SAAS0B,EAAkBC,CAAK,CAAEL,CAAI,MAKvCM,EAJJ,IAAMC,EAAKb,OAAOM,EAAK,UAAU,EAAE,WAAW,GACxCQ,EAASrB,EAAaoB,EAAG,WAAW,IACpClB,EAAQgB,EAAM,aAAa,CAAC,OAAO,CAACE,EAItClB,AAAU,MAAVA,GACFgB,EAAM,aAAa,CAAC,IAAI,CAACE,GACzBF,EAAM,cAAc,CAACE,EAAG,CAAG,EAC3BD,EAAUD,EAAM,aAAa,CAAC,MAAM,GAEpCA,EAAM,cAAc,CAACE,EAAG,GACxBD,EAAUjB,EAAQ,GAGpB,IAAMoB,EAAeJ,EAAM,cAAc,CAACE,EAAG,CAGvCG,EAAO,CACX,KAAM,UACN,QAAS,IACT,WAAY,CACV,KAAM,IAAML,EAAM,aAAa,CAAG,MAAQG,EAC1C,GACEH,EAAM,aAAa,CACnB,SACAG,EACCC,CAAAA,EAAe,EAAI,IAAMA,EAAe,EAAC,EAC5C,gBAAiB,GACjB,gBAAiB,CAAC,iBAAiB,AACrC,EACA,SAAU,CAAC,CAAC,KAAM,OAAQ,MAAOf,OAAOY,EAAQ,EAAE,AACpD,EACAD,EAAM,KAAK,CAACL,EAAMU,GAGlB,IAAMC,EAAM,CACV,KAAM,UACN,QAAS,MACT,WAAY,CAAC,EACb,SAAU,CAACD,EAAK,AAClB,EAEA,OADAL,EAAM,KAAK,CAACL,EAAMW,GACXN,EAAM,SAAS,CAACL,EAAMW,EAC/B,CCpCO,SAASC,EAAOP,CAAK,CAAEL,CAAI,EAChC,IAAMa,EAAUb,EAAK,aAAa,CAC9Bc,EAAS,IAQb,GANID,AAAY,cAAZA,EACFC,GAAU,KACW,SAAZD,GACTC,CAAAA,GAAU,IAAOd,CAAAA,EAAK,KAAK,EAAIA,EAAK,UAAU,AAAD,EAAK,GAAE,EAGlDA,AAAc,mBAAdA,EAAK,IAAI,CACX,MAAO,CAAC,KAAM,OAAQ,MAAO,KAAOA,EAAK,GAAG,CAAGc,CAAM,EAGvD,IAAMC,EAAWV,EAAM,GAAG,CAACL,GACrBgB,EAAOD,CAAQ,CAAC,EAAE,AAEpBC,CAAAA,GAAQA,AAAc,SAAdA,EAAK,IAAI,CACnBA,EAAK,KAAK,CAAG,IAAMA,EAAK,KAAK,CAE7BD,EAAS,OAAO,CAAC,CAAC,KAAM,OAAQ,MAAO,GAAG,GAG5C,IAAME,EAAOF,CAAQ,CAACA,EAAS,MAAM,CAAG,EAAE,CAQ1C,OANIE,GAAQA,AAAc,SAAdA,EAAK,IAAI,CACnBA,EAAK,KAAK,EAAIH,EAEdC,EAAS,IAAI,CAAC,CAAC,KAAM,OAAQ,MAAOD,CAAM,GAGrCC,CACT,CC+DA,SAASG,EAAclB,CAAI,EACzB,IAAMmB,EAASnB,EAAK,MAAM,CAE1B,OAAOmB,MAAAA,EACHnB,EAAK,QAAQ,CAAC,MAAM,CAAG,EACvBmB,CACN,CCnFA,SAASC,EAAS1C,CAAK,CAAEY,CAAK,CAAE+B,CAAG,EACjC,IAAIC,EAAa,EACbC,EAAW7C,EAAM,MAAM,CAE3B,GAAIY,EAAO,CACT,IAAIE,EAAOd,EAAM,WAAW,CAAC4C,GAE7B,KAAO9B,AApDC,IAoDDA,GAAgBA,AAnDb,KAmDaA,GACrB8B,IACA9B,EAAOd,EAAM,WAAW,CAAC4C,EAE7B,CAEA,GAAID,EAAK,CACP,IAAI7B,EAAOd,EAAM,WAAW,CAAC6C,EAAW,GAExC,KAAO/B,AA7DC,IA6DDA,GAAgBA,AA5Db,KA4DaA,GACrB+B,IACA/B,EAAOd,EAAM,WAAW,CAAC6C,EAAW,EAExC,CAEA,OAAOA,EAAWD,EAAa5C,EAAM,KAAK,CAAC4C,EAAYC,GAAY,EACrE,CCxCO,IAAMC,EAAW,CACtB,WCbK,SAAoBnB,CAAK,CAAEL,CAAI,EAEpC,IAAMZ,EAAS,CACb,KAAM,UACN,QAAS,aACT,WAAY,CAAC,EACb,SAAUiB,EAAM,IAAI,CAACA,EAAM,GAAG,CAACL,GAAO,GACxC,EAEA,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EDIE,MEbK,SAAmBiB,CAAK,CAAEL,CAAI,EAEnC,IAAMZ,EAAS,CAAC,KAAM,UAAW,QAAS,KAAM,WAAY,CAAC,EAAG,SAAU,EAAE,EAE5E,OADAiB,EAAM,KAAK,CAACL,EAAMZ,GACX,CAACiB,EAAM,SAAS,CAACL,EAAMZ,GAAS,CAAC,KAAM,OAAQ,MAAO,IAAI,EAAE,AACrE,EFSE,KGbK,SAAciB,CAAK,CAAEL,CAAI,EAC9B,IAAMtB,EAAQsB,EAAK,KAAK,CAAGA,EAAK,KAAK,CAAG,KAAO,GAGzCyB,EAAOzB,EAAK,IAAI,CAAGA,EAAK,IAAI,CAAC,KAAK,CAAC,uBAAyB,KAE5D0B,EAAa,CAAC,EAEhBD,GACFC,CAAAA,EAAW,SAAS,CAAG,CAAC,YAAcD,EAAK,AAAD,EAK5C,IAAIrC,EAAS,CACX,KAAM,UACN,QAAS,OACTsC,WAAAA,EACA,SAAU,CAAC,CAAC,KAAM,OAAQhD,MAAAA,CAAK,EAAE,AACnC,EAYA,OAVIsB,EAAK,IAAI,EACXZ,CAAAA,EAAO,IAAI,CAAG,CAAC,KAAMY,EAAK,IAAI,GAGhCK,EAAM,KAAK,CAACL,EAAMZ,GAIlBA,EAAS,CAAC,KAAM,UAAW,QAAS,MAAO,WAAY,CAAC,EAAG,SAAU,CAHrEA,EAASiB,EAAM,SAAS,CAACL,EAAMZ,GAG8C,EAC7EiB,EAAM,KAAK,CAACL,EAAMZ,GACXA,CACT,EHlBE,OIfK,SAAuBiB,CAAK,CAAEL,CAAI,EAEvC,IAAMZ,EAAS,CACb,KAAM,UACN,QAAS,MACT,WAAY,CAAC,EACb,SAAUiB,EAAM,GAAG,CAACL,EACtB,EAEA,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EJME,SKjBK,SAAkBiB,CAAK,CAAEL,CAAI,EAElC,IAAMZ,EAAS,CACb,KAAM,UACN,QAAS,KACT,WAAY,CAAC,EACb,SAAUiB,EAAM,GAAG,CAACL,EACtB,EAEA,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,ELQE,kBAAiB,EACjB,SMXK,SAAkBiB,CAAK,CAAEL,CAAI,EAClC,IAAM2B,EAAetB,EAAM,YAAY,CACnCuB,EAAK,EAET,KAAOA,KAAMD,GAAcC,IAE3B,IAAMC,EAAanC,OAAOkC,GAS1B,OAPAD,CAAY,CAACE,EAAW,CAAG,CACzB,KAAM,qBACNA,WAAAA,EACA,SAAU,CAAC,CAAC,KAAM,YAAa,SAAU7B,EAAK,QAAQ,EAAE,CACxD,SAAUA,EAAK,QAAQ,AACzB,EAEOI,EAAkBC,EAAO,CAC9B,KAAM,oBACNwB,WAAAA,EACA,SAAU7B,EAAK,QAAQ,AACzB,EACF,ENRE,QOpBK,SAAiBK,CAAK,CAAEL,CAAI,EAEjC,IAAMZ,EAAS,CACb,KAAM,UACN,QAAS,IAAMY,EAAK,KAAK,CACzB,WAAY,CAAC,EACb,SAAUK,EAAM,GAAG,CAACL,EACtB,EAEA,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EPWE,KQnBK,SAAciB,CAAK,CAAEL,CAAI,EAC9B,GAAIK,EAAM,SAAS,CAAE,CAEnB,IAAMjB,EAAS,CAAC,KAAM,MAAO,MAAOY,EAAK,KAAK,EAE9C,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,CAGA,OAAO,IACT,ERUE,eSjBK,SAAwBiB,CAAK,CAAEL,CAAI,EACxC,IAAM8B,EAAMzB,EAAM,UAAU,CAACL,EAAK,UAAU,EAE5C,GAAI,CAAC8B,EACH,OAAOlB,EAAOP,EAAOL,GAIvB,IAAM0B,EAAa,CAAC,IAAKvC,EAAa2C,EAAI,GAAG,EAAI,IAAK,IAAK9B,EAAK,GAAG,CAEjD,QAAd8B,EAAI,KAAK,EAAaA,AAAcC,KAAAA,IAAdD,EAAI,KAAK,EACjCJ,CAAAA,EAAW,KAAK,CAAGI,EAAI,KAAK,AAAD,EAI7B,IAAM1C,EAAS,CAAC,KAAM,UAAW,QAAS,MAAOsC,WAAAA,EAAY,SAAU,EAAE,EAEzE,OADArB,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,ETAE,MUpBK,SAAeiB,CAAK,CAAEL,CAAI,EAE/B,IAAM0B,EAAa,CAAC,IAAKvC,EAAaa,EAAK,GAAG,CAAC,CAE9B,QAAbA,EAAK,GAAG,EAAaA,AAAa+B,KAAAA,IAAb/B,EAAK,GAAG,EAC/B0B,CAAAA,EAAW,GAAG,CAAG1B,EAAK,GAAG,AAAD,EAGP,OAAfA,EAAK,KAAK,EAAaA,AAAe+B,KAAAA,IAAf/B,EAAK,KAAK,EACnC0B,CAAAA,EAAW,KAAK,CAAG1B,EAAK,KAAK,AAAD,EAI9B,IAAMZ,EAAS,CAAC,KAAM,UAAW,QAAS,MAAOsC,WAAAA,EAAY,SAAU,EAAE,EAEzE,OADArB,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EVKE,WWvBK,SAAoBiB,CAAK,CAAEL,CAAI,EAEpC,IAAMgC,EAAO,CAAC,KAAM,OAAQ,MAAOhC,EAAK,KAAK,CAAC,OAAO,CAAC,YAAa,IAAI,EACvEK,EAAM,KAAK,CAACL,EAAMgC,GAGlB,IAAM5C,EAAS,CACb,KAAM,UACN,QAAS,OACT,WAAY,CAAC,EACb,SAAU,CAAC4C,EAAK,AAClB,EAEA,OADA3B,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EXUE,cYpBK,SAAuBiB,CAAK,CAAEL,CAAI,EACvC,IAAM8B,EAAMzB,EAAM,UAAU,CAACL,EAAK,UAAU,EAE5C,GAAI,CAAC8B,EACH,OAAOlB,EAAOP,EAAOL,GAIvB,IAAM0B,EAAa,CAAC,KAAMvC,EAAa2C,EAAI,GAAG,EAAI,GAAG,CAEnC,QAAdA,EAAI,KAAK,EAAaA,AAAcC,KAAAA,IAAdD,EAAI,KAAK,EACjCJ,CAAAA,EAAW,KAAK,CAAGI,EAAI,KAAK,AAAD,EAI7B,IAAM1C,EAAS,CACb,KAAM,UACN,QAAS,IACTsC,WAAAA,EACA,SAAUrB,EAAM,GAAG,CAACL,EACtB,EAEA,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EZFE,KavBK,SAAciB,CAAK,CAAEL,CAAI,EAE9B,IAAM0B,EAAa,CAAC,KAAMvC,EAAaa,EAAK,GAAG,CAAC,CAE7B,QAAfA,EAAK,KAAK,EAAaA,AAAe+B,KAAAA,IAAf/B,EAAK,KAAK,EACnC0B,CAAAA,EAAW,KAAK,CAAG1B,EAAK,KAAK,AAAD,EAI9B,IAAMZ,EAAS,CACb,KAAM,UACN,QAAS,IACTsC,WAAAA,EACA,SAAUrB,EAAM,GAAG,CAACL,EACtB,EAEA,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EbOE,SFfK,SAAkBiB,CAAK,CAAEL,CAAI,CAAEiC,CAAM,EAC1C,IAAMC,EAAU7B,EAAM,GAAG,CAACL,GACpBmC,EAAQF,EAASG,AAyEzB,SAAmBpC,CAAI,EACrB,IAAImC,EAAQ,GACZ,GAAInC,AAAc,SAAdA,EAAK,IAAI,CAAa,CACxBmC,EAAQnC,EAAK,MAAM,EAAI,GACvB,IAAMqC,EAAWrC,EAAK,QAAQ,CAC1BX,EAAQ,GAEZ,KAAO,CAAC8C,GAAS,EAAE9C,EAAQgD,EAAS,MAAM,EACxCF,EAAQjB,EAAcmB,CAAQ,CAAChD,EAAM,CAEzC,CAEA,OAAO8C,CACT,EAtFmCF,GAAUf,EAAclB,GAEnD0B,EAAa,CAAC,EAEdW,EAAW,EAAE,CAEnB,GAAI,AAAwB,WAAxB,OAAOrC,EAAK,OAAO,CAAgB,KAGjCsC,EAFJ,IAAMtB,EAAOkB,CAAO,CAAC,EAAE,AAInBlB,CAAAA,GAAQA,AAAc,YAAdA,EAAK,IAAI,EAAkBA,AAAiB,MAAjBA,EAAK,OAAO,CACjDsB,EAAYtB,GAEZsB,EAAY,CAAC,KAAM,UAAW,QAAS,IAAK,WAAY,CAAC,EAAG,SAAU,EAAE,EACxEJ,EAAQ,OAAO,CAACI,IAGdA,EAAU,QAAQ,CAAC,MAAM,CAAG,GAC9BA,EAAU,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAM,OAAQ,MAAO,GAAG,GAGtDA,EAAU,QAAQ,CAAC,OAAO,CAAC,CACzB,KAAM,UACN,QAAS,QACT,WAAY,CAAC,KAAM,WAAY,QAAStC,EAAK,OAAO,CAAE,SAAU,EAAI,EACpE,SAAU,EAAE,AACd,GAIA0B,EAAW,SAAS,CAAG,CAAC,iBAAiB,AAC3C,CAEA,IAAIrC,EAAQ,GAEZ,KAAO,EAAEA,EAAQ6C,EAAQ,MAAM,EAAE,CAC/B,IAAMK,EAAQL,CAAO,CAAC7C,EAAM,CAI1B8C,CAAAA,GACA9C,AAAU,IAAVA,GACAkD,AAAe,YAAfA,EAAM,IAAI,EACVA,AAAkB,MAAlBA,EAAM,OAAO,AAAO,GAEpBF,EAAS,IAAI,CAAC,CAAC,KAAM,OAAQ,MAAO,IAAI,GAGtCE,AAAe,YAAfA,EAAM,IAAI,EAAkBA,AAAkB,MAAlBA,EAAM,OAAO,EAAaJ,EAGxDE,EAAS,IAAI,CAACE,GAFdF,EAAS,IAAI,IAAIE,EAAM,QAAQ,CAInC,CAEA,IAAMtB,EAAOiB,CAAO,CAACA,EAAQ,MAAM,CAAG,EAAE,CAGpCjB,GAASkB,CAAAA,GAASlB,AAAc,YAAdA,EAAK,IAAI,EAAkBA,AAAiB,MAAjBA,EAAK,OAAO,AAAO,GAClEoB,EAAS,IAAI,CAAC,CAAC,KAAM,OAAQ,MAAO,IAAI,GAI1C,IAAMjD,EAAS,CAAC,KAAM,UAAW,QAAS,KAAMsC,WAAAA,EAAYW,SAAAA,CAAQ,EAEpE,OADAhC,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EErDE,Kc3BK,SAAciB,CAAK,CAAEL,CAAI,EAE9B,IAAM0B,EAAa,CAAC,EACdQ,EAAU7B,EAAM,GAAG,CAACL,GACtBX,EAAQ,GAOZ,IAL0B,UAAtB,OAAOW,EAAK,KAAK,EAAiBA,AAAe,IAAfA,EAAK,KAAK,EAC9C0B,CAAAA,EAAW,KAAK,CAAG1B,EAAK,KAAK,AAAD,EAIvB,EAAEX,EAAQ6C,EAAQ,MAAM,EAAE,CAC/B,IAAMK,EAAQL,CAAO,CAAC7C,EAAM,CAE5B,GACEkD,AAAe,YAAfA,EAAM,IAAI,EACVA,AAAkB,OAAlBA,EAAM,OAAO,EACbA,EAAM,UAAU,EAChBxF,MAAM,OAAO,CAACwF,EAAM,UAAU,CAAC,SAAS,GACxCA,EAAM,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,kBACpC,CACAb,EAAW,SAAS,CAAG,CAAC,qBAAqB,CAC7C,KACF,CACF,CAGA,IAAMtC,EAAS,CACb,KAAM,UACN,QAASY,EAAK,OAAO,CAAG,KAAO,KAC/B0B,WAAAA,EACA,SAAUrB,EAAM,IAAI,CAAC6B,EAAS,GAChC,EAEA,OADA7B,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EdPE,Ue7BK,SAAmBiB,CAAK,CAAEL,CAAI,EAEnC,IAAMZ,EAAS,CACb,KAAM,UACN,QAAS,IACT,WAAY,CAAC,EACb,SAAUiB,EAAM,GAAG,CAACL,EACtB,EAEA,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EfoBE,KgB7BK,SAAciB,CAAK,CAAEL,CAAI,EAE9B,IAAMZ,EAAS,CAAC,KAAM,OAAQ,SAAUiB,EAAM,IAAI,CAACA,EAAM,GAAG,CAACL,GAAM,EAEnE,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EhByBE,OiB/BK,SAAgBiB,CAAK,CAAEL,CAAI,EAEhC,IAAMZ,EAAS,CACb,KAAM,UACN,QAAS,SACT,WAAY,CAAC,EACb,SAAUiB,EAAM,GAAG,CAACL,EACtB,EAEA,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EjBsBE,MkB9BK,SAAeiB,CAAK,CAAEL,CAAI,EAC/B,IAAMwC,EAAOnC,EAAM,GAAG,CAACL,GACjByC,EAAWD,EAAK,KAAK,GAErBE,EAAe,EAAE,CAEvB,GAAID,EAAU,CAEZ,IAAMzB,EAAO,CACX,KAAM,UACN,QAAS,QACT,WAAY,CAAC,EACb,SAAUX,EAAM,IAAI,CAAC,CAACoC,EAAS,CAAE,GACnC,EACApC,EAAM,KAAK,CAACL,EAAK,QAAQ,CAAC,EAAE,CAAEgB,GAC9B0B,EAAa,IAAI,CAAC1B,EACpB,CAEA,GAAIwB,EAAK,MAAM,CAAG,EAAG,CAEnB,IAAMG,EAAO,CACX,KAAM,UACN,QAAS,QACT,WAAY,CAAC,EACb,SAAUtC,EAAM,IAAI,CAACmC,EAAM,GAC7B,EAEMlD,EAAQO,EAAWG,EAAK,QAAQ,CAAC,EAAE,EACnCqB,EAAMvB,EAASE,EAAK,QAAQ,CAACA,EAAK,QAAQ,CAAC,MAAM,CAAG,EAAE,CACxDV,CAAAA,EAAM,IAAI,EAAI+B,EAAI,IAAI,EAAEsB,CAAAA,EAAK,QAAQ,CAAG,CAACrD,MAAAA,EAAO+B,IAAAA,CAAG,GACvDqB,EAAa,IAAI,CAACC,EACpB,CAGA,IAAMvD,EAAS,CACb,KAAM,UACN,QAAS,QACT,WAAY,CAAC,EACb,SAAUiB,EAAM,IAAI,CAACqC,EAAc,GACrC,EAEA,OADArC,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,ElBXE,UmBjCK,SAAmBiB,CAAK,CAAEL,CAAI,EAInC,IAAMZ,EAAS,CACb,KAAM,UACN,QAAS,KACT,WAAY,CAAC,EACb,SAAUiB,EAAM,GAAG,CAACL,EACtB,EAEA,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EnBsBE,SoBtBK,SAAkBiB,CAAK,CAAEL,CAAI,CAAEiC,CAAM,EAC1C,IAAMW,EAAWX,EAASA,EAAO,QAAQ,CAAGF,KAAAA,EAGtCc,EAAUC,AAAa,IADZF,CAAAA,EAAWA,EAAS,OAAO,CAAC5C,GAAQ,GACpB,KAAO,KAClC+C,EAAQd,GAAUA,AAAgB,UAAhBA,EAAO,IAAI,CAAeA,EAAO,KAAK,CAAGF,KAAAA,EAC3D7D,EAAS6E,EAAQA,EAAM,MAAM,CAAG/C,EAAK,QAAQ,CAAC,MAAM,CACtDgD,EAAY,GAEVC,EAAQ,EAAE,CAEhB,KAAO,EAAED,EAAY9E,GAAQ,CAE3B,IAAMgF,EAAOlD,EAAK,QAAQ,CAACgD,EAAU,CAE/BtB,EAAa,CAAC,EACdyB,EAAaJ,EAAQA,CAAK,CAACC,EAAU,CAAGjB,KAAAA,EAE1CoB,GACFzB,CAAAA,EAAW,KAAK,CAAGyB,CAAS,EAI9B,IAAI/D,EAAS,CAAC,KAAM,UAAWyD,QAAAA,EAASnB,WAAAA,EAAY,SAAU,EAAE,EAE5DwB,IACF9D,EAAO,QAAQ,CAAGiB,EAAM,GAAG,CAAC6C,GAC5B7C,EAAM,KAAK,CAAC6C,EAAM9D,GAClBA,EAASiB,EAAM,SAAS,CAACL,EAAMZ,IAGjC6D,EAAM,IAAI,CAAC7D,EACb,CAGA,IAAMA,EAAS,CACb,KAAM,UACN,QAAS,KACT,WAAY,CAAC,EACb,SAAUiB,EAAM,IAAI,CAAC4C,EAAO,GAC9B,EAEA,OADA5C,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EpBpBE,KqBhCK,SAAciB,CAAK,CAAEL,CAAI,EAE9B,IAAMZ,EAAS,CAAC,KAAM,OAAQ,MAAOgE,AtBThC,SAAmB1E,CAAK,EAC7B,IAAM2E,EAAS3D,OAAOhB,GAChB4E,EAAS,YACXC,EAAQD,EAAO,IAAI,CAACD,GACpBG,EAAO,EAELC,EAAQ,EAAE,CAEhB,KAAOF,GACLE,EAAM,IAAI,CACRrC,EAASiC,EAAO,KAAK,CAACG,EAAMD,EAAM,KAAK,EAAGC,EAAO,EAAG,IACpDD,CAAK,CAAC,EAAE,EAGVC,EAAOD,EAAM,KAAK,CAAGA,CAAK,CAAC,EAAE,CAAC,MAAM,CACpCA,EAAQD,EAAO,IAAI,CAACD,GAKtB,OAFAI,EAAM,IAAI,CAACrC,EAASiC,EAAO,KAAK,CAACG,GAAOA,EAAO,EAAG,KAE3CC,EAAM,IAAI,CAAC,GACpB,EsBZiD/D,OAAOM,EAAK,KAAK,EAAE,EAElE,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,ErB4BE,csBpCK,SAAuBiB,CAAK,CAAEL,CAAI,EAEvC,IAAMZ,EAAS,CACb,KAAM,UACN,QAAS,KACT,WAAY,CAAC,EACb,SAAU,EAAE,AACd,EAEA,OADAiB,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EtB2BE,KAAMsE,EACN,KAAMA,EACN,WAAYA,EACZ,mBAAoBA,CACtB,EAGA,SAASA,IAEP,OAAO,IACT,CuBwGA,IAAM,EAAM,CAAC,EAAE,cAAc,CA6L7B,SAASC,EAAMC,CAAI,CAAEC,CAAE,EACrB,GAAID,EAAK,QAAQ,CAAEC,K7BpTI7D,C6BoTJ6D,CAAAA,EAAG,QAAQ,C7BnTvB,CAAC,MAAOhE,EADQG,E6BoTmB4D,G7BnTT,IAAK9D,EAASE,EAAK,C6BmTN,CAChD,CAcA,SAAS8D,EAAUF,CAAI,CAAEC,CAAE,EAEzB,IAAIzE,EAASyE,EAGb,GAAID,GAAQA,EAAK,IAAI,CAAE,CACrB,IAAMG,EAAQH,EAAK,IAAI,CAAC,KAAK,CACvBI,EAAYJ,EAAK,IAAI,CAAC,SAAS,CAC/BK,EAAcL,EAAK,IAAI,CAAC,WAAW,AAEpB,WAAjB,OAAOG,IAGL3E,AAAgB,YAAhBA,EAAO,IAAI,CACbA,EAAO,OAAO,CAAG2E,EAOjB3E,EAAS,CACP,KAAM,UACN,QAAS2E,EACT,WAAY,CAAC,EACb,SAAU,EAAE,AACd,GAcgB,YAAhB3E,EAAO,IAAI,EAAkB6E,GAC/B7E,CAAAA,EAAO,UAAU,CAAG,CAAC,GAAGA,EAAO,UAAU,CAAE,GAAG6E,CAAW,GAIzD,aAAc7E,GACdA,EAAO,QAAQ,EAEf4E,MADAA,GAIA5E,CAAAA,EAAO,QAAQ,CAAG4E,CAAQ,CAE9B,CAEA,OAAO5E,CACT,CAeO,SAAS8E,EAAI7D,CAAK,CAAEL,CAAI,CAAEiC,CAAM,EACrC,IAAMlC,EAAOC,GAAQA,EAAK,IAAI,CAG9B,GAAI,CAACD,EACH,MAAM,AAAIoE,MAAM,uBAAyBnE,EAAO,YAGlD,AAAI,EAAI,IAAI,CAACK,EAAM,QAAQ,CAAEN,GACpBM,EAAM,QAAQ,CAACN,EAAK,CAACM,EAAOL,EAAMiC,GAGvC5B,EAAM,WAAW,EAAIA,EAAM,WAAW,CAAC,QAAQ,CAACN,GAG3C,aAAcC,EAAO,CAAC,GAAGA,CAAI,CAAE,SAAU,EAAIK,EAAOL,EAAK,EAAIA,EAGlEK,EAAM,cAAc,CACfA,EAAM,cAAc,CAACA,EAAOL,EAAMiC,GAGpCmC,AA8DT,SAA+B/D,CAAK,CAAEL,CAAI,EACxC,IAAMqE,EAAOrE,EAAK,IAAI,EAAI,CAAC,EAErBZ,EACJ,UAAWY,GACX,CAAE,GAAI,IAAI,CAACqE,EAAM,gBAAkB,EAAI,IAAI,CAACA,EAAM,YAAW,EACzD,CAAC,KAAM,OAAQ,MAAOrE,EAAK,KAAK,EAChC,CACE,KAAM,UACN,QAAS,MACT,WAAY,CAAC,EACb,SAAU,EAAIK,EAAOL,EACvB,EAGN,OADAK,EAAM,KAAK,CAACL,EAAMZ,GACXiB,EAAM,SAAS,CAACL,EAAMZ,EAC/B,EA9E+BiB,EAAOL,EACtC,CAaO,SAAS,EAAIK,CAAK,CAAE4B,CAAM,EAE/B,IAAMjD,EAAS,EAAE,CAEjB,GAAI,aAAciD,EAAQ,CACxB,IAAMqC,EAAQrC,EAAO,QAAQ,CACzB5C,EAAQ,GACZ,KAAO,EAAEA,EAAQiF,EAAM,MAAM,EAAE,CAC7B,IAAMlF,EAAS8E,EAAI7D,EAAOiE,CAAK,CAACjF,EAAM,CAAE4C,GAGxC,GAAI7C,EAAQ,CACV,GAAIC,GAASiF,AAA0B,UAA1BA,CAAK,CAACjF,EAAQ,EAAE,CAAC,IAAI,GAC5B,CAACtC,MAAM,OAAO,CAACqC,IAAWA,AAAgB,SAAhBA,EAAO,IAAI,EACvCA,CAAAA,EAAO,KAAK,CAAGA,EAAO,KAAK,CAAC,OAAO,CAAC,OAAQ,GAAE,EAG5C,CAACrC,MAAM,OAAO,CAACqC,IAAWA,AAAgB,YAAhBA,EAAO,IAAI,EAAgB,CACvD,IAAM4B,EAAO5B,EAAO,QAAQ,CAAC,EAAE,CAE3B4B,GAAQA,AAAc,SAAdA,EAAK,IAAI,EACnBA,CAAAA,EAAK,KAAK,CAAGA,EAAK,KAAK,CAAC,OAAO,CAAC,OAAQ,GAAE,CAE9C,CAGEjE,MAAM,OAAO,CAACqC,GAChBJ,EAAO,IAAI,IAAII,GAEfJ,EAAO,IAAI,CAACI,EAEhB,CACF,CACF,CAEA,OAAOJ,CACT,CA0CO,SAASuF,EAAKD,CAAK,CAAEnC,CAAK,EAE/B,IAAM/C,EAAS,EAAE,CACbC,EAAQ,GAMZ,IAJI8C,GACF/C,EAAO,IAAI,CAAC,CAAC,KAAM,OAAQ,MAAO,IAAI,GAGjC,EAAEC,EAAQiF,EAAM,MAAM,EACvBjF,GAAOD,EAAO,IAAI,CAAC,CAAC,KAAM,OAAQ,MAAO,IAAI,GACjDA,EAAO,IAAI,CAACkF,CAAK,CAACjF,EAAM,EAO1B,OAJI8C,GAASmC,EAAM,MAAM,CAAG,GAC1BlF,EAAO,IAAI,CAAC,CAAC,KAAM,OAAQ,MAAO,IAAI,GAGjCA,CACT,CCheO,SAASoF,EAAOC,CAAI,CAAElH,CAAO,EAClC,IAAM8C,EAAQqE,ADkFT,SAAqBD,CAAI,CAAElH,CAAO,EACvC,IAAM0B,EAAW1B,GAAW,CAAC,EACvBoH,EAAY1F,EAAS,kBAAkB,EAAI,GAE3C0C,EAAe,CAAC,EA2DtB,OAnDAtB,EAAM,SAAS,CAAGsE,EAElBtE,EAAM,aAAa,CACjBpB,AAA2B8C,KAAAA,IAA3B9C,EAAS,aAAa,EAAkBA,AAA2B,OAA3BA,EAAS,aAAa,CAC1D,gBACAA,EAAS,aAAa,CAE5BoB,EAAM,aAAa,CAAGpB,EAAS,aAAa,EAAI,YAEhDoB,EAAM,oBAAoB,CAAGpB,EAAS,oBAAoB,EAAI,KAE9DoB,EAAM,uBAAuB,CAAGpB,EAAS,uBAAuB,EAAI,CAClE,UAAW,CAAC,UAAU,AACxB,EAEAoB,EAAM,iBAAiB,CAAGpB,EAAS,iBAAiB,EAAI,kBAExDoB,EAAM,cAAc,CAAGpB,EAAS,cAAc,CAE9CoB,EAAM,WAAW,CAAGpB,EAAS,WAAW,CAExCoB,EAAM,QAAQ,CAAG,CAAC,GAAGmB,CAAQ,CAAE,GAAGvC,EAAS,QAAQ,EAInDoB,EAAM,UAAU,CAAG,A5BxLd,SAAqBoE,CAAI,EAE9B,IAAMG,EAAQnI,OAAO,MAAM,CAAC,MAE5B,GAAI,CAACgI,GAAQ,CAACA,EAAK,IAAI,CACrB,MAAM,AAAIN,MAAM,wCAUlB,MAPA,SAAMM,EAAM,aAAc,AAACI,IACzB,IAAMtE,EAAKJ,EAAM0E,EAAW,UAAU,EAClCtE,GAAM,CAACL,EAAI,IAAI,CAAC0E,EAAOrE,IACzBqE,CAAAA,CAAK,CAACrE,EAAG,CAAGsE,CAAS,CAEzB,GAKA,SAAoBhD,CAAU,EAC5B,IAAMtB,EAAKJ,EAAM0B,GAEjB,OAAOtB,GAAML,EAAI,IAAI,CAAC0E,EAAOrE,GAAMqE,CAAK,CAACrE,EAAG,CAAG,IACjD,CACF,E4BiKiCkE,GAC/BpE,EAAM,YAAY,CAAGsB,EAErBtB,EAAM,aAAa,CAAG,EAAE,CAExBA,EAAM,cAAc,CAAG,CAAC,EAExBA,EAAM,KAAK,CAAGsD,EACdtD,EAAM,SAAS,CAAGyD,EAClBzD,EAAM,GAAG,CAsGT,SAAkBL,CAAI,CAAEiC,CAAM,EAE5B,OAAOiC,EAAI7D,EAAOL,EAAMiC,EAC1B,EAxGA5B,EAAM,GAAG,CAkHT,SAAkB4B,CAAM,EAEtB,OAAO,EAAI5B,EAAO4B,EACpB,EApHA5B,EAAM,IAAI,CAAGkE,EAEblE,EAAM,OAAO,CAAGyE,EAEhB,SAAML,EAAM,qBAAsB,AAACI,IACjC,IAAMtE,EAAKb,OAAOmF,EAAW,UAAU,EAAE,WAAW,EAIhD,EAAC,EAAI,IAAI,CAAClD,EAAcpB,IAC1BoB,CAAAA,CAAY,CAACpB,EAAG,CAAGsE,CAAS,CAEhC,GAGOxE,EAWP,SAASyE,EAAQC,CAAI,CAAEC,CAAK,EAE1B,GAAID,GAAQ,SAAUA,GAAQA,EAAK,IAAI,CAAE,CAEvC,IAAMV,EAAOU,EAAK,IAAI,AAElBV,CAAAA,EAAK,KAAK,GACO,YAAfW,EAAM,IAAI,EACZA,CAAAA,EAAQ,CACN,KAAM,UACN,QAAS,GACT,WAAY,CAAC,EACb,SAAU,EAAE,AACd,GAGFA,EAAM,OAAO,CAAGX,EAAK,KAAK,EAGT,YAAfW,EAAM,IAAI,EAAkBX,EAAK,WAAW,EAC9CW,CAAAA,EAAM,UAAU,CAAG,CAAC,GAAGA,EAAM,UAAU,CAAE,GAAGX,EAAK,WAAW,GAG1D,aAAcW,GAASA,EAAM,QAAQ,EAAIX,EAAK,SAAS,EACzDW,CAAAA,EAAM,QAAQ,CAAGX,EAAK,SAAS,AAAD,CAElC,CAEA,GAAIU,EAAM,KEnQY/E,EFoQpB,IAAMiF,EAAM,SAAUF,EAAOA,EAAO,CAAC,SAAUA,CAAI,EAEnD,GAAI,CEpQN,GAFsB/E,EFsQLiF,IEnQjB,CAACjF,EAAK,QAAQ,EACd,CAACA,EAAK,QAAQ,CAAC,KAAK,EACpB,CAACA,EAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,EACzB,CAACA,EAAK,QAAQ,CAAC,KAAK,CAAC,MAAM,EAC3B,CAACA,EAAK,QAAQ,CAAC,GAAG,EAClB,CAACA,EAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,EACvB,CAACA,EAAK,QAAQ,CAAC,GAAG,CAAC,MAAM,AAAD,EF+PpBgF,EAAM,QAAQ,CAAG,CAAC,MAAOnF,EAAWoF,GAAM,IAAKnF,EAASmF,EAAI,CAEhE,CAEA,OAAOD,CACT,CAUA,SAAS3E,EAAML,CAAI,CAAE6C,CAAO,CAAEqC,CAAK,CAAE7C,CAAQ,EAO3C,OANItF,MAAM,OAAO,CAACmI,KAChB7C,EAAW6C,EACXA,EAAQ,CAAC,GAIJJ,EAAQ9E,EAAM,CACnB,KAAM,UACN6C,QAAAA,EACA,WAAYqC,GAAS,CAAC,EACtB,SAAU7C,GAAY,EAAE,AAC1B,EACF,CA8BF,ECvP4BoC,EAAMlH,GAC1ByC,EAAOK,EAAM,GAAG,CAACoE,EAAM,MACvBU,EAAOC,AElFR,SAAgB/E,CAAK,EAE1B,IAAMgF,EAAY,EAAE,CAChBhG,EAAQ,GAEZ,KAAO,EAAEA,EAAQgB,EAAM,aAAa,CAAC,MAAM,EAAE,CAC3C,IAAMyB,EAAMzB,EAAM,YAAY,CAACA,EAAM,aAAa,CAAChB,EAAM,CAAC,CAE1D,GAAI,CAACyC,EACH,SAGF,IAAMwD,EAAUjF,EAAM,GAAG,CAACyB,GACpBvB,EAAKb,OAAOoC,EAAI,UAAU,EAAE,WAAW,GACvCtB,EAASrB,EAAaoB,EAAG,WAAW,IACtCgF,EAAiB,EAEfC,EAAiB,EAAE,CAEzB,KAAO,EAAED,GAAkBlF,EAAM,cAAc,CAACE,EAAG,EAAE,CAEnD,IAAMkF,EAAgB,CACpB,KAAM,UACN,QAAS,IACT,WAAY,CACV,KACE,IACApF,EAAM,aAAa,CACnB,SACAG,EACC+E,CAAAA,EAAiB,EAAI,IAAMA,EAAiB,EAAC,EAChD,oBAAqB,GACrB,UAAW,CAAC,wBAAwB,CACpC,UAAWlF,EAAM,iBAAiB,AACpC,EACA,SAAU,CAAC,CAAC,KAAM,OAAQ,MAAO,GAAG,EAAE,AACxC,EAEIkF,EAAiB,GACnBE,EAAc,QAAQ,CAAC,IAAI,CAAC,CAC1B,KAAM,UACN,QAAS,MACT,SAAU,CAAC,CAAC,KAAM,OAAQ,MAAO/F,OAAO6F,EAAe,EAAE,AAC3D,GAGEC,EAAe,MAAM,CAAG,GAC1BA,EAAe,IAAI,CAAC,CAAC,KAAM,OAAQ,MAAO,GAAG,GAG/CA,EAAe,IAAI,CAACC,EACtB,CAEA,IAAMxE,EAAOqE,CAAO,CAACA,EAAQ,MAAM,CAAG,EAAE,CAExC,GAAIrE,GAAQA,AAAc,YAAdA,EAAK,IAAI,EAAkBA,AAAiB,MAAjBA,EAAK,OAAO,CAAU,CAC3D,IAAMyE,EAAWzE,EAAK,QAAQ,CAACA,EAAK,QAAQ,CAAC,MAAM,CAAG,EAAE,AACpDyE,CAAAA,GAAYA,AAAkB,SAAlBA,EAAS,IAAI,CAC3BA,EAAS,KAAK,EAAI,IAElBzE,EAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAM,OAAQ,MAAO,GAAG,GAG9CA,EAAK,QAAQ,CAAC,IAAI,IAAIuE,EACxB,MACEF,EAAQ,IAAI,IAAIE,GAIlB,IAAMG,EAAW,CACf,KAAM,UACN,QAAS,KACT,WAAY,CAAC,GAAItF,EAAM,aAAa,CAAG,MAAQG,CAAM,EACrD,SAAUH,EAAM,IAAI,CAACiF,EAAS,GAChC,EAEAjF,EAAM,KAAK,CAACyB,EAAK6D,GAEjBN,EAAU,IAAI,CAACM,EACjB,CAEA,GAAIN,AAAqB,IAArBA,EAAU,MAAM,CAIpB,MAAO,CACL,KAAM,UACN,QAAS,UACT,WAAY,CAAC,cAAe,GAAM,UAAW,CAAC,YAAY,EAC1D,SAAU,CACR,CACE,KAAM,UACN,QAAShF,EAAM,oBAAoB,CACnC,WAAY,CAEV,GAAGuF,KAAK,KAAK,CAACA,KAAK,SAAS,CAACvF,EAAM,uBAAuB,EAAE,CAC5D,GAAI,gBACN,EACA,SAAU,CAAC,CAAC,KAAM,OAAQ,MAAOA,EAAM,aAAa,EAAE,AACxD,EACA,CAAC,KAAM,OAAQ,MAAO,IAAI,EAC1B,CACE,KAAM,UACN,QAAS,KACT,WAAY,CAAC,EACb,SAAUA,EAAM,IAAI,CAACgF,EAAW,GAClC,EACA,CAAC,KAAM,OAAQ,MAAO,IAAI,EAC3B,AACH,CACF,EF5BsBhF,GAUpB,OARI8E,GAIFnF,EAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAM,OAAQ,MAAO,IAAI,EAAGmF,GAI3CpI,MAAM,OAAO,CAACiD,GAAQ,CAAC,KAAM,OAAQ,SAAUA,CAAI,EAAIA,CAChE,CGjEA,MAPI,SAAU6F,CAAW,CAAEtI,CAAO,EAC5B,OAAOsI,GAAe,QAASA,EAC3BC,AAaV,SAAgBD,CAAW,CAAEtI,CAAO,EAClC,MAAO,CAACyC,EAAM+F,EAAMpG,KAClBkG,EAAY,GAAG,CAACrB,EAAOxE,EAAMzC,GAAUwI,EAAM,AAACC,IAC5CrG,EAAKqG,EACP,EACF,CACF,EAnBiBH,EAAatI,GACpB0I,AA0BV,SAAgB1I,CAAO,EAErB,OAAO,AAACyC,GAASwE,EAAOxE,EAAMzC,EAChC,EA7BiBsI,GAAetI,EAC5B,eCpCG,OAAM2I,EAOX,YAAYzH,CAAQ,CAAE0H,CAAM,CAAEC,CAAK,CAAE,CACnC,IAAI,CAAC,QAAQ,CAAG3H,EAChB,IAAI,CAAC,MAAM,CAAG0H,EACVC,GACF,KAAI,CAAC,KAAK,CAAGA,CAAI,CAErB,CACF,CCRO,SAASC,EAAMC,CAAW,CAAEF,CAAK,EAEtC,IAAM3H,EAAW,CAAC,EAEZ0H,EAAS,CAAC,EACZ9G,EAAQ,GAEZ,KAAO,EAAEA,EAAQiH,EAAY,MAAM,EACjC7J,OAAO,MAAM,CAACgC,EAAU6H,CAAW,CAACjH,EAAM,CAAC,QAAQ,EACnD5C,OAAO,MAAM,CAAC0J,EAAQG,CAAW,CAACjH,EAAM,CAAC,MAAM,EAGjD,OAAO,IAAI6G,EAAOzH,EAAU0H,EAAQC,EACtC,CCrBO,SAASG,EAAU7H,CAAK,EAC7B,OAAOA,EAAM,WAAW,EAC1B,CFiBAwH,EAAO,SAAS,CAAC,QAAQ,CAAG,CAAC,EAE7BA,EAAO,SAAS,CAAC,MAAM,CAAG,CAAC,EAE3BA,EAAO,SAAS,CAAC,KAAK,CAAG,IG3BlB,OAAMM,EAMX,YAAY/H,CAAQ,CAAEgI,CAAS,CAAE,CAE/B,IAAI,CAAC,QAAQ,CAAGhI,EAEhB,IAAI,CAAC,SAAS,CAAGgI,CACnB,CACF,CAGAD,EAAK,SAAS,CAAC,KAAK,CAAG,KACvBA,EAAK,SAAS,CAAC,OAAO,CAAG,GACzBA,EAAK,SAAS,CAAC,UAAU,CAAG,GAC5BA,EAAK,SAAS,CAAC,iBAAiB,CAAG,GACnCA,EAAK,SAAS,CAAC,MAAM,CAAG,GACxBA,EAAK,SAAS,CAAC,cAAc,CAAG,GAChCA,EAAK,SAAS,CAAC,cAAc,CAAG,GAChCA,EAAK,SAAS,CAAC,qBAAqB,CAAG,GACvCA,EAAK,SAAS,CAAC,eAAe,CAAG,GACjCA,EAAK,SAAS,CAAC,OAAO,CAAG,GCxBzB,IAAIE,EAAS,EAEA,EAAUC,IACVC,EAAaD,IACbE,EAAoBF,IACpBG,EAASH,IACTI,EAAiBJ,IACjBK,EAAiBL,IACjBM,EAAwBN,IAErC,SAASA,IACP,OAAO,GAAK,EAAED,CAChB,CCPA,IAAMQ,EAASzK,OAAO,IAAI,CAAC,EAEpB,OAAM0K,UAAoBX,EAQ/B,YAAY/H,CAAQ,CAAEgI,CAAS,CAAEW,CAAI,CAAEhB,CAAK,CAAE,CAC5C,IAAI/G,EAAQ,GAMZ,GAJA,KAAK,CAACZ,EAAUgI,GAEhBY,AAkBJ,SAAcrI,CAAM,CAAE9B,CAAG,CAAEwB,CAAK,EAC1BA,GAEFM,CAAAA,CAAM,CAAC9B,EAAI,CAAGwB,CAAI,CAEtB,EAvBS,IAAI,CAAE,QAAS0H,GAEhB,AAAgB,UAAhB,OAAOgB,EACT,KAAO,EAAE/H,EAAQ6H,EAAO,MAAM,EAAE,CAC9B,IAAMI,EAAQJ,CAAM,CAAC7H,EAAM,CAC3BgI,AAaR,UAAcrI,CAAM,CAAE9B,CAAG,CAAEwB,CAAK,EAC1BA,GAEFM,CAAAA,CAAM,CAAC9B,EAAI,CAAGwB,CAAI,CAEtB,GAlBa,IAAI,CAAEwI,CAAM,CAAC7H,EAAM,CAAE,AAAC+H,CAAAA,EAAO,CAAK,CAACE,EAAM,AAAD,IAAO,CAAK,CAACA,EAAM,CAClE,CAEJ,CACF,CASA,SAASD,EAAKrI,CAAM,CAAE9B,CAAG,CAAEwB,CAAK,EAC1BA,GAEFM,CAAAA,CAAM,CAAC9B,EAAI,CAAGwB,CAAI,CAEtB,CAZAyI,EAAY,SAAS,CAAC,OAAO,CAAG,GCbhC,IAAM,EAAM,CAAC,EAAE,cAAc,CAMtB,SAASI,EAAO1C,CAAU,MAM3B2C,EAJJ,IAAM/I,EAAW,CAAC,EAEZ0H,EAAS,CAAC,EAIhB,IAAKqB,KAAQ3C,EAAW,UAAU,CAChC,GAAI,EAAI,IAAI,CAACA,EAAW,UAAU,CAAE2C,GAAO,CACzC,IAAM9I,EAAQmG,EAAW,UAAU,CAAC2C,EAAK,CACnCC,EAAO,IAAIN,EACfK,EACA3C,EAAW,SAAS,CAACA,EAAW,UAAU,EAAI,CAAC,EAAG2C,GAClD9I,EACAmG,EAAW,KAAK,CAIhBA,CAAAA,EAAW,eAAe,EAC1BA,EAAW,eAAe,CAAC,QAAQ,CAAC2C,IAEpCC,CAAAA,EAAK,eAAe,CAAG,EAAG,EAG5BhJ,CAAQ,CAAC+I,EAAK,CAAGC,EAEjBtB,CAAM,CAACI,EAAUiB,GAAM,CAAGA,EAC1BrB,CAAM,CAACI,EAAUkB,EAAK,SAAS,EAAE,CAAGD,CACtC,CAGF,OAAO,IAAItB,EAAOzH,EAAU0H,EAAQtB,EAAW,KAAK,CACtD,CCvDO,IAAM6C,EAAQH,EAAO,CAC1B,MAAO,QACP,WAAUI,EAAGH,IACJ,SAAWA,EAAK,KAAK,CAAC,GAAG,WAAW,GAE7C,WAAY,CACV,aAAc,KACd,aAAc,KACd,UAAW,KACX,UAAW,KACX,UAAW,KACX,WAAY,KACZ,UAAW,IACb,CACF,GCdaI,EAAML,EAAO,CACxB,MAAO,MACP,WAAUI,EAAGH,IACJ,OAASA,EAAK,KAAK,CAAC,GAAG,WAAW,GAE3C,WAAY,CAAC,QAAS,KAAM,QAAS,KAAM,SAAU,IAAI,CAC3D,GCHO,SAASK,EAAuBC,CAAU,CAAErB,CAAS,EAC1D,OAAOA,KAAaqB,EAAaA,CAAU,CAACrB,EAAU,CAAGA,CAC3D,CCAO,SAASsB,EAAyBD,CAAU,CAAErJ,CAAQ,EAC3D,OAAOoJ,EAAuBC,EAAYrJ,EAAS,WAAW,GAChE,CCNO,IAAMuJ,EAAQT,EAAO,CAC1B,MAAO,QACP,WAAY,CAAC,WAAY,aAAa,EACtC,UAAWQ,EACX,WAAY,CAAC,MAAO,KAAM,WAAY,IAAI,CAC5C,GCLaE,GAAOV,EAAO,CACzB,WAAUI,EAAGH,IACJA,AAAS,SAATA,EAAkBA,EAAO,QAAUA,EAAK,KAAK,CAAC,GAAG,WAAW,GAErE,WAAY,CACV,qBAAsB,KACtB,WAAYZ,EACZ,iBAAkB,KAClB,SAAUA,EACV,YAAaA,EACb,aAAcE,EACd,aAAcA,EACd,YAAaA,EACb,aAAcC,EACd,YAAa,KACb,gBAAiBA,EACjB,YAAa,KACb,aAAcH,EACd,eAAgBG,EAChB,iBAAkB,KAClB,aAAcH,EACd,WAAYG,EACZ,YAAaH,EACb,aAAc,KACd,WAAYA,EACZ,YAAa,KACb,iBAAkB,KAClB,UAAW,KACX,eAAgBG,EAChB,UAAWD,EACX,SAAU,KACV,UAAWF,EACX,cAAeA,EACf,oBAAqBA,EACrB,gBAAiB,KACjB,SAAUG,EACV,gBAAiB,KACjB,aAAcD,EACd,YAAaF,EACb,aAAcA,EACd,aAAc,KACd,aAAcA,EACd,oBAAqBG,EACrB,aAAcD,EACd,aAAcA,EACd,YAAaA,EACb,aAAcF,EACd,YAAaE,EACb,SAAU,KACV,aAAcA,EACd,aAAcA,EACd,aAAcA,EACd,cAAe,KACf,KAAM,IACR,CACF,GC/Ca,GAAOS,EAAO,CACzB,MAAO,OACP,WAAY,CACV,cAAe,iBACf,UAAW,QACX,QAAS,MACT,UAAW,YACb,EACA,UAAWQ,EACX,gBAAiB,CAAC,UAAW,WAAY,QAAS,WAAW,CAC7D,WAAY,CAEV,KAAM,KACN,OAAQf,EACR,cAAeD,EACf,UAAWA,EACX,OAAQ,KACR,MAAO,KACP,gBAAiB,EACjB,oBAAqB,EACrB,eAAgB,EAChB,IAAK,KACL,GAAI,KACJ,MAAO,EACP,eAAgB,KAChB,aAAcA,EACd,UAAW,EACX,SAAU,EACV,SAAUA,EACV,QAAS,KACT,QAAS,KACT,QAAS,EACT,KAAM,KACN,UAAWA,EACX,KAAMD,EACN,QAAS,KACT,QAAS,KACT,gBAAiBF,EACjB,SAAU,EACV,aAAcG,EACd,OAAQD,EAASE,EACjB,YAAa,KACb,KAAM,KACN,SAAU,KACV,SAAU,KACV,QAAS,EACT,MAAO,EACP,IAAK,KACL,QAAS,KACT,SAAU,EACV,SAAUH,EACV,UAAWD,EACX,QAAS,KACT,aAAc,KACd,cAAe,KACf,KAAM,KACN,WAAY,KACZ,YAAa,KACb,WAAY,KACZ,eAAgB,EAChB,WAAY,KACZ,QAASG,EACT,OAAQD,EACR,OAAQ,EACR,KAAMA,EACN,KAAM,KACN,SAAU,KACV,QAASC,EACT,UAAWA,EACX,GAAI,KACJ,WAAY,KACZ,YAAa,KACb,MAAO,EACP,UAAW,KACX,UAAW,KACX,GAAI,KACJ,MAAO,EACP,OAAQ,KACR,SAAUA,EACV,QAASA,EACT,UAAW,EACX,SAAUA,EACV,KAAM,KACN,MAAO,KACP,KAAM,KACN,SAAU,KACV,KAAM,KACN,QAAS,KACT,KAAM,EACN,IAAKD,EACL,SAAU,KACV,IAAK,KACL,UAAWA,EACX,MAAO,KACP,OAAQ,KACR,IAAK,KACL,UAAWA,EACX,SAAU,EACV,MAAO,EACP,KAAM,KACN,MAAO,KACP,SAAU,EACV,WAAY,EACZ,QAAS,KACT,aAAc,KACd,WAAY,KACZ,cAAe,KACf,cAAe,KACf,eAAgB,KAChB,eAAgB,KAChB,OAAQ,KACR,SAAU,KACV,UAAW,KACX,iBAAkB,KAClB,SAAU,KACV,QAAS,KACT,QAAS,KACT,cAAe,KACf,cAAe,KACf,kBAAmB,KACnB,OAAQ,KACR,YAAa,KACb,MAAO,KACP,WAAY,KACZ,OAAQ,KACR,UAAW,KACX,YAAa,KACb,WAAY,KACZ,YAAa,KACb,WAAY,KACZ,YAAa,KACb,OAAQ,KACR,iBAAkB,KAClB,UAAW,KACX,QAAS,KACT,QAAS,KACT,QAAS,KACT,WAAY,KACZ,aAAc,KACd,QAAS,KACT,UAAW,KACX,UAAW,KACX,WAAY,KACZ,QAAS,KACT,iBAAkB,KAClB,OAAQ,KACR,aAAc,KACd,iBAAkB,KAClB,UAAW,KACX,YAAa,KACb,UAAW,KACX,eAAgB,KAChB,YAAa,KACb,aAAc,KACd,aAAc,KACd,YAAa,KACb,WAAY,KACZ,YAAa,KACb,UAAW,KACX,UAAW,KACX,SAAU,KACV,WAAY,KACZ,WAAY,KACZ,QAAS,KACT,QAAS,KACT,OAAQ,KACR,UAAW,KACX,WAAY,KACZ,WAAY,KACZ,aAAc,KACd,mBAAoB,KACpB,QAAS,KACT,SAAU,KACV,SAAU,KACV,YAAa,KACb,0BAA2B,KAC3B,SAAU,KACV,UAAW,KACX,SAAU,KACV,aAAc,KACd,UAAW,KACX,UAAW,KACX,SAAU,KACV,UAAW,KACX,aAAc,KACd,SAAU,KACV,qBAAsB,KACtB,SAAU,KACV,eAAgB,KAChB,UAAW,KACX,QAAS,KACT,KAAM,EACN,QAASA,EACT,QAAS,KACT,KAAMC,EACN,YAAa,KACb,YAAa,EACb,QAAS,KACT,cAAe,KACf,oBAAqB,KACrB,OAAQ,KACR,QAAS,KACT,SAAU,EACV,eAAgB,KAChB,IAAKA,EACL,SAAU,EACV,SAAU,EACV,KAAMD,EACN,QAASA,EACT,QAASC,EACT,MAAO,KACP,OAAQ,EACR,SAAU,EACV,SAAU,EACV,mBAAoB,EACpB,yBAA0B,EAC1B,eAAgB,KAChB,MAAO,KACP,KAAMD,EACN,MAAO,KACP,KAAM,KACN,KAAMA,EACN,WAAYF,EACZ,IAAK,KACL,OAAQ,KACR,QAAS,KACT,OAAQ,KACR,MAAOE,EACP,KAAM,KACN,MAAO,KACP,SAAUA,EACV,OAAQ,KACR,MAAO,KACP,UAAW,KACX,KAAM,KACN,cAAe,EACf,OAAQ,KACR,MAAOF,EACP,MAAOE,EACP,KAAM,KACN,mBAAoB,KAIpB,MAAO,KACP,MAAO,KACP,QAASC,EACT,KAAM,KACN,WAAY,KACZ,QAAS,KACT,OAAQD,EACR,YAAa,KACb,aAAcA,EACd,YAAa,KACb,YAAa,KACb,KAAM,KACN,QAAS,KACT,QAAS,KACT,MAAO,KACP,KAAM,KACN,SAAU,KACV,SAAU,KACV,MAAO,KACP,QAAS,EACT,QAAS,EACT,MAAO,KACP,KAAM,KACN,MAAO,KACP,YAAa,KACb,OAAQA,EACR,WAAYA,EACZ,KAAM,KACN,SAAU,KACV,OAAQ,KACR,aAAcA,EACd,YAAaA,EACb,SAAU,EACV,OAAQ,EACR,QAAS,EACT,OAAQ,EACR,OAAQ,KACR,QAAS,KACT,OAAQ,KACR,IAAK,KACL,YAAaA,EACb,MAAO,KACP,OAAQ,KACR,UAAWF,EACX,QAAS,KACT,QAAS,KACT,KAAM,KACN,UAAWE,EACX,UAAW,KACX,QAAS,KACT,OAAQ,KACR,MAAO,KACP,OAAQA,EAGR,kBAAmB,KACnB,YAAa,KACb,SAAU,KACV,wBAAyB,EACzB,sBAAuB,EACvB,OAAQ,KACR,SAAU,KACV,QAASA,EACT,SAAU,KACV,aAAc,IAChB,CACF,GCvTaoB,GAAMX,EAAO,CACxB,MAAO,MACP,WAAY,CACV,aAAc,gBACd,kBAAmB,qBACnB,WAAY,cACZ,cAAe,iBACf,UAAW,aACX,UAAW,QACX,SAAU,YACV,SAAU,YACV,mBAAoB,sBACpB,0BAA2B,8BAC3B,aAAc,gBACd,eAAgB,kBAChB,YAAa,cACb,SAAU,WACV,iBAAkB,oBAClB,iBAAkB,oBAClB,YAAa,eACb,SAAU,YACV,WAAY,cACZ,aAAc,gBACd,WAAY,cACZ,SAAU,YACV,eAAgB,mBAChB,YAAa,eACb,UAAW,aACX,YAAa,eACb,WAAY,cACZ,UAAW,aACX,2BAA4B,+BAC5B,yBAA0B,6BAC1B,SAAU,WACV,UAAW,cACX,aAAc,iBACd,aAAc,iBACd,eAAgB,kBAChB,cAAe,iBACf,cAAe,iBACf,UAAW,aACX,UAAW,aACX,YAAa,eACb,QAAS,WACT,YAAa,gBACb,aAAc,iBACd,QAAS,WACT,QAAS,WACT,QAAS,WACT,SAAU,YACV,MAAO,SACP,UAAW,cACX,WAAY,eACZ,QAAS,UACT,WAAY,aACZ,aAAc,eACd,cAAe,gBACf,QAAS,UACT,SAAU,WACV,UAAW,YACX,iBAAkB,mBAClB,SAAU,WACV,QAAS,UACT,QAAS,UACT,OAAQ,SACR,YAAa,cACb,MAAO,QACP,WAAY,aACZ,OAAQ,SACR,UAAW,YACX,YAAa,cACb,WAAY,aACZ,YAAa,cACb,WAAY,aACZ,YAAa,cACb,OAAQ,SACR,iBAAkB,mBAClB,UAAW,YACX,MAAO,QACP,QAAS,UACT,QAAS,UACT,QAAS,UACT,UAAW,YACX,WAAY,aACZ,aAAc,eACd,QAAS,UACT,UAAW,YACX,UAAW,YACX,WAAY,aACZ,QAAS,UACT,OAAQ,SACR,aAAc,eACd,iBAAkB,mBAClB,YAAa,cACb,UAAW,YACX,YAAa,cACb,aAAc,eACd,aAAc,eACd,YAAa,cACb,WAAY,aACZ,YAAa,cACb,UAAW,YACX,aAAc,eACd,UAAW,YACX,SAAU,WACV,WAAY,aACZ,WAAY,aACZ,QAAS,UACT,QAAS,UACT,OAAQ,SACR,UAAW,YACX,WAAY,aACZ,WAAY,aACZ,aAAc,eACd,SAAU,WACV,QAAS,UACT,SAAU,WACV,SAAU,WACV,SAAU,WACV,UAAW,YACX,SAAU,WACV,OAAQ,SACR,UAAW,YACX,UAAW,YACX,SAAU,WACV,UAAW,YACX,aAAc,eACd,SAAU,WACV,SAAU,WACV,eAAgB,iBAChB,UAAW,YACX,OAAQ,SACR,iBAAkB,oBAClB,kBAAmB,qBACnB,WAAY,cACZ,QAAS,WACT,cAAe,iBACf,eAAgB,iBAChB,gBAAiB,mBACjB,eAAgB,kBAChB,UAAW,aACX,YAAa,eACb,sBAAuB,yBACvB,uBAAwB,0BACxB,gBAAiB,mBACjB,iBAAkB,oBAClB,cAAe,iBACf,eAAgB,kBAChB,iBAAkB,oBAClB,cAAe,iBACf,YAAa,eACb,SAAU,WACV,WAAY,cACZ,eAAgB,kBAChB,cAAe,iBACf,gBAAiB,mBACjB,OAAQ,SACR,kBAAmB,qBACnB,mBAAoB,sBACpB,YAAa,eACb,aAAc,gBACd,WAAY,eACZ,YAAa,eACb,SAAU,YACV,aAAc,gBACd,cAAe,iBACf,aAAc,gBACd,SAAU,aACV,YAAa,gBACb,YAAa,gBACb,YAAa,eACb,YAAa,eACb,QAAS,WAET,cAAe,gBACf,cAAe,eACjB,EACA,UAAWM,EACX,WAAY,CACV,MAAOZ,EACP,aAAcH,EACd,WAAY,KACZ,SAAU,KACV,kBAAmB,KACnB,WAAYA,EACZ,UAAWA,EACX,WAAY,KACZ,OAAQA,EACR,cAAe,KACf,cAAe,KACf,QAASA,EACT,UAAW,KACX,cAAe,KACf,cAAe,KACf,YAAa,KACb,KAAM,KACN,MAAO,KACP,KAAMA,EACN,GAAI,KACJ,SAAU,KACV,UAAWA,EACX,UAAWC,EACX,KAAM,KACN,SAAU,KACV,cAAe,KACf,SAAU,KACV,MAAO,KACP,mBAAoB,KACpB,0BAA2B,KAC3B,aAAc,KACd,eAAgB,KAChB,QAAS,KACT,kBAAmB,KACnB,iBAAkB,KAClB,YAAa,KACb,OAAQ,KACR,GAAI,KACJ,GAAI,KACJ,EAAG,KACH,SAAU,KACV,cAAe,KACf,QAASD,EACT,gBAAiBA,EACjB,UAAW,KACX,QAAS,KACT,IAAK,KACL,QAASA,EACT,iBAAkB,KAClB,SAAU,EACV,GAAI,KACJ,GAAI,KACJ,SAAU,KACV,SAAU,KACV,UAAWA,EACX,iBAAkB,KAClB,IAAK,KACL,MAAO,KACP,SAAUA,EACV,0BAA2B,KAC3B,KAAM,KACN,YAAaA,EACb,SAAU,KACV,OAAQ,KACR,UAAW,KACX,YAAa,KACb,WAAY,KACZ,aAAc,KACd,UAAW,KACX,eAAgB,KAChB,WAAY,KACZ,SAAU,KACV,eAAgB,KAChB,YAAa,KACb,UAAW,KACX,YAAa,KACb,WAAY,KACZ,OAAQ,KACR,GAAI,KACJ,KAAM,KACN,GAAI,KACJ,GAAI,KACJ,GAAIE,EACJ,GAAIA,EACJ,UAAWA,EACX,2BAA4B,KAC5B,yBAA0B,KAC1B,SAAU,KACV,kBAAmB,KACnB,cAAe,KACf,QAAS,KACT,QAASF,EACT,kBAAmB,KACnB,WAAY,KACZ,OAAQ,KACR,KAAM,KACN,SAAU,KACV,UAAWA,EACX,aAAcA,EACd,aAAcA,EACd,GAAI,KACJ,YAAaA,EACb,eAAgB,KAChB,kBAAmB,KACnB,GAAI,KACJ,IAAK,KACL,UAAWA,EACX,EAAGA,EACH,GAAIA,EACJ,GAAIA,EACJ,GAAIA,EACJ,GAAIA,EACJ,aAAcG,EACd,iBAAkB,KAClB,UAAW,KACX,WAAY,KACZ,SAAU,KACV,QAAS,KACT,KAAM,KACN,aAAc,KACd,cAAe,KACf,cAAe,KACf,kBAAmBH,EACnB,MAAO,KACP,UAAW,KACX,UAAW,KACX,YAAa,KACb,aAAc,KACd,YAAa,KACb,YAAa,KACb,KAAM,KACN,iBAAkB,KAClB,UAAW,KACX,aAAc,KACd,IAAK,KACL,MAAO,KACP,uBAAwB,KACxB,sBAAuB,KACvB,UAAWA,EACX,UAAW,KACX,OAAQ,KACR,IAAK,KACL,KAAM,KACN,KAAM,KACN,QAAS,KACT,YAAa,KACb,aAAc,KACd,QAAS,KACT,QAAS,KACT,QAAS,KACT,SAAU,KACV,MAAO,KACP,UAAW,KACX,WAAY,KACZ,WAAY,KACZ,SAAU,KACV,OAAQ,KACR,QAAS,KACT,WAAY,KACZ,aAAc,KACd,cAAe,KACf,QAAS,KACT,SAAU,KACV,UAAW,KACX,iBAAkB,KAClB,SAAU,KACV,QAAS,KACT,QAAS,KACT,OAAQ,KACR,YAAa,KACb,MAAO,KACP,WAAY,KACZ,OAAQ,KACR,UAAW,KACX,YAAa,KACb,WAAY,KACZ,YAAa,KACb,WAAY,KACZ,YAAa,KACb,OAAQ,KACR,iBAAkB,KAClB,UAAW,KACX,MAAO,KACP,QAAS,KACT,QAAS,KACT,QAAS,KACT,UAAW,KACX,WAAY,KACZ,aAAc,KACd,QAAS,KACT,UAAW,KACX,UAAW,KACX,WAAY,KACZ,QAAS,KACT,OAAQ,KACR,aAAc,KACd,iBAAkB,KAClB,YAAa,KACb,UAAW,KACX,YAAa,KACb,aAAc,KACd,aAAc,KACd,YAAa,KACb,WAAY,KACZ,YAAa,KACb,UAAW,KACX,aAAc,KACd,UAAW,KACX,SAAU,KACV,WAAY,KACZ,WAAY,KACZ,QAAS,KACT,QAAS,KACT,OAAQ,KACR,UAAW,KACX,WAAY,KACZ,WAAY,KACZ,aAAc,KACd,SAAU,KACV,QAAS,KACT,SAAU,KACV,SAAU,KACV,SAAU,KACV,UAAW,KACX,SAAU,KACV,OAAQ,KACR,UAAW,KACX,UAAW,KACX,SAAU,KACV,UAAW,KACX,aAAc,KACd,SAAU,KACV,SAAU,KACV,eAAgB,KAChB,UAAW,KACX,OAAQ,KACR,QAAS,KACT,SAAU,KACV,MAAO,KACP,OAAQ,KACR,YAAa,KACb,OAAQ,KACR,SAAU,KACV,QAAS,KACT,iBAAkBA,EAClB,kBAAmBA,EACnB,WAAY,KACZ,QAAS,KACT,KAAM,KACN,WAAYA,EACZ,oBAAqB,KACrB,iBAAkB,KAClB,aAAc,KACd,MAAO,KACP,KAAMC,EACN,MAAO,KACP,cAAe,KACf,cAAe,KACf,OAAQ,KACR,UAAWD,EACX,UAAWA,EACX,UAAWA,EACX,cAAe,KACf,oBAAqB,KACrB,eAAgB,KAChB,UAAW,KACX,SAAUG,EACV,EAAG,KACH,OAAQ,KACR,eAAgB,KAChB,KAAM,KACN,KAAM,KACN,IAAKA,EACL,IAAKA,EACL,gBAAiB,KACjB,YAAa,KACb,UAAW,KACX,mBAAoBA,EACpB,iBAAkBA,EAClB,cAAeA,EACf,gBAAiBA,EACjB,SAAU,KACV,QAAS,KACT,OAAQ,KACR,OAAQ,KACR,GAAI,KACJ,GAAI,KACJ,MAAO,KACP,KAAM,KACN,eAAgB,KAChB,KAAM,KACN,MAAO,KACP,aAAc,KACd,iBAAkBH,EAClB,iBAAkBA,EAClB,aAAc,KACd,QAAS,KACT,YAAa,KACb,aAAc,KACd,MAAO,KACP,MAAO,KACP,YAAa,KACb,UAAW,KACX,YAAa,KACb,sBAAuBA,EACvB,uBAAwBA,EACxB,OAAQ,KACR,OAAQ,KACR,gBAAiBG,EACjB,iBAAkB,KAClB,cAAe,KACf,eAAgB,KAChB,iBAAkBH,EAClB,cAAeA,EACf,YAAa,KACb,MAAO,KACP,aAAcA,EACd,aAAc,KACd,oBAAqB,KACrB,WAAY,KACZ,cAAe,KACf,qBAAsB,KACtB,eAAgBG,EAChB,SAAUH,EACV,YAAa,KACb,OAAQ,KACR,QAASA,EACT,QAASA,EACT,WAAY,KACZ,eAAgB,KAChB,cAAe,KACf,WAAY,KACZ,cAAe,KACf,MAAO,KACP,kBAAmB,KACnB,KAAM,KACN,OAAQG,EACR,GAAI,KACJ,UAAW,KACX,gBAAiB,KACjB,GAAI,KACJ,GAAI,KACJ,kBAAmBH,EACnB,mBAAoBA,EACpB,QAAS,KACT,YAAa,KACb,aAAc,KACd,WAAYA,EACZ,OAAQ,KACR,YAAaA,EACb,cAAeA,EACf,aAAc,KACd,SAAUA,EACV,aAAcA,EACd,QAAS,KACT,SAAUA,EACV,YAAaA,EACb,YAAaA,EACb,QAAS,KACT,WAAY,KACZ,WAAY,KACZ,MAAO,KACP,OAAQ,KACR,YAAa,KACb,YAAa,KACb,EAAG,KACH,GAAI,KACJ,GAAI,KACJ,iBAAkB,KAClB,QAASA,EACT,EAAG,KACH,GAAI,KACJ,GAAI,KACJ,iBAAkB,KAClB,EAAG,KACH,WAAY,IACd,CACF,GCtiBa,GAAOT,EAAM,CAACuB,EAAKF,EAAOM,EAAOC,GAAM,GAAS,CAAE,QAClD,GAAM5B,EAAM,CAACuB,EAAKF,EAAOM,EAAOC,GAAMC,GAAQ,CAAE,OCM9C,SAASC,GAAa5K,CAAO,EAC1C,GAAIA,EAAQ,eAAe,EAAIA,EAAQ,kBAAkB,CACvD,MAAM,AAAI6K,UACR,4EAIJ,GACE7K,EAAQ,eAAe,EACvBA,EAAQ,kBAAkB,EAC1BA,EAAQ,YAAY,CAEpB,OAAO,AAACkH,IACN,SAAMA,EAAM,UAAW,CAACzE,EAAMX,EAAOgJ,SAG/BC,EAYJ,GAVI/K,EAAQ,eAAe,CACzB+K,EAAS,CAAC/K,EAAQ,eAAe,CAAC,QAAQ,CAACyC,EAAK,OAAO,EAC9CzC,EAAQ,kBAAkB,EACnC+K,CAAAA,EAAS/K,EAAQ,kBAAkB,CAAC,QAAQ,CAACyC,EAAK,OAAO,GAGvD,CAACsI,GAAU/K,EAAQ,YAAY,EAAI,AAAiB,UAAjB,OAAO8B,GAC5CiJ,CAAAA,EAAS,CAAC/K,EAAQ,YAAY,CAACyC,EAAMX,EAXKgJ,EAWQ,EAGhDC,GAAU,AAAiB,UAAjB,OAAOjJ,EAOnB,OANI9B,EAAQ,gBAAgB,EAAIyC,EAAK,QAAQ,CAC3CiC,AAhBwCoG,EAgBjC,QAAQ,CAAC,MAAM,CAAChJ,EAAO,KAAMW,EAAK,QAAQ,EAEjDiC,AAlBwCoG,EAkBjC,QAAQ,CAAC,MAAM,CAAChJ,EAAO,GAGzBA,CAIX,EACF,CAEJ,oBCzDA,IAAMkJ,GAAQ,kBACRC,GAAO,UACPC,GAAM,SA+CZ,SAASC,GAAMC,CAAE,EACf,MAAO,IAAMA,EAAG,WAAW,EAC7B,CAMA,SAASC,GAAUD,CAAE,EACnB,OAAOA,EAAG,MAAM,CAAC,GAAG,WAAW,EACjC,CC1DO,IAAME,GAAc,CACzB,QAAS,UACT,SAAU,WACV,OAAQ,SACR,gBAAiB,kBACjB,iBAAkB,mBAClB,cAAe,gBACf,eAAgB,iBAChB,iBAAkB,mBAClB,OAAQ,SACR,aAAc,eACd,aAAc,eACd,UAAW,YACX,UAAW,YACX,UAAW,YACX,WAAY,aACZ,UAAW,YACX,WAAY,YACd,kECoFA,IAAM,GAAM,CAAC,EAAE,cAAc,CAIvBC,GAAgB,IAAIC,IAAI,CAAC,QAAS,QAAS,QAAS,QAAS,KAAK,EAMjE,SAASC,GAAgBC,CAAO,CAAEjJ,CAAI,MAKvCuC,EAHJ,IAAMF,EAAW,EAAE,CACf6G,EAAa,GAIjB,KAAO,EAAEA,EAAalJ,EAAK,QAAQ,CAAC,MAAM,EAGpCuC,AAAe,YAAfA,AAFJA,CAAAA,EAAQvC,EAAK,QAAQ,CAACkJ,EAAW,AAAD,EAEtB,IAAI,CACZ7G,EAAS,IAAI,CAAC8G,AA+BpB,SAAiBF,CAAO,CAAEjJ,CAAI,CAAEX,CAAK,CAAE4C,CAAM,MAcvCxD,EAbJ,IAAMlB,EAAU0L,EAAQ,OAAO,CACzBG,EACJ7L,AAA6BwE,KAAAA,IAA7BxE,EAAQ,gBAAgB,CACpB,IAAc,CACdA,EAAQ,gBAAgB,CACxB8L,EAAeJ,EAAQ,MAAM,CAG7BxL,EAAOuC,EAAK,OAAO,CAEnB0B,EAAa,CAAC,EAChB4H,EAASD,EASb,GAL2B,SAAvBA,EAAa,KAAK,EAAe5L,AAAS,QAATA,IACnC6L,EAAS,GACTL,EAAQ,MAAM,CAAGK,GAGftJ,EAAK,UAAU,CACjB,IAAKvB,KAAYuB,EAAK,UAAU,CAC1B,GAAI,IAAI,CAACA,EAAK,UAAU,CAAEvB,IAC5B8K,AAsLR,SAAqBrE,CAAK,CAAEsC,CAAI,CAAE9I,CAAK,CAAEuG,CAAG,EAC1C,IAAMwC,EAAO+B,AFjWR,SAAcF,CAAM,CAAE5K,CAAK,EAChC,IAAMyH,EAASI,EAAU7H,GACrB8I,EAAO9I,EACP+K,EAAOjD,EAEX,GAAIL,KAAUmD,EAAO,MAAM,CACzB,OAAOA,EAAO,QAAQ,CAACA,EAAO,MAAM,CAACnD,EAAO,CAAC,CAG/C,GAAIA,EAAO,MAAM,CAAG,GAAKA,AAAuB,SAAvBA,EAAO,KAAK,CAAC,EAAG,IAAiBoC,GAAM,IAAI,CAAC7J,GAAQ,CAE3E,GAAIA,AAAoB,MAApBA,EAAM,MAAM,CAAC,GAAY,CAE3B,IAAMgL,EAAOhL,EAAM,KAAK,CAAC,GAAG,OAAO,CAAC8J,GAAMI,IAC1CpB,EAAO,OAASkC,EAAK,MAAM,CAAC,GAAG,WAAW,GAAKA,EAAK,KAAK,CAAC,EAC5D,KAAO,CAEL,IAAMA,EAAOhL,EAAM,KAAK,CAAC,GAEzB,GAAI,CAAC8J,GAAK,IAAI,CAACkB,GAAO,CACpB,IAAIC,EAASD,EAAK,OAAO,CAACjB,GAAKC,GAEN,OAArBiB,EAAO,MAAM,CAAC,IAChBA,CAAAA,EAAS,IAAMA,CAAK,EAGtBjL,EAAQ,OAASiL,CACnB,CACF,CAEAF,EAAOtC,CACT,CAEA,OAAO,IAAIsC,EAAKjC,EAAM9I,EACxB,EE+ToBuG,EAAI,MAAM,CAAEuC,GAC1BpI,EAASV,EAIb,GAAIU,MAAAA,GAA2CA,GAAWA,EAMtDrC,MAAM,OAAO,CAACqC,IAChBA,CAAAA,EAASqI,EAAK,cAAc,CAAG,SAAOrI,GAAU,SAAOA,EAAM,EAGzC,UAAlBqI,EAAK,QAAQ,EAAgB,AAAkB,UAAlB,OAAOrI,GACtCA,CAAAA,EAASwK,AAkBb,SAAoBlL,CAAK,EAEvB,IAAMU,EAAS,CAAC,EAEhB,GAAI,CACF,ACvZW,GDuZLV,EAWR,SAAkBjB,CAAI,CAAEoM,CAAC,EAEvBzK,CAAM,CAAC0K,AADGrM,CAAAA,AAAqB,SAArBA,EAAK,KAAK,CAAC,EAAG,GAAgB,CAAC,GAAG,EAAEA,EAAK,KAAK,CAAC,GAAG,CAAC,CAAGA,CAAG,EAC1D,OAAO,CAAC,YAAasM,IAAe,CAAGF,CAClD,EAbA,CAAE,KAAM,CAER,CAEA,OAAOzK,CAUT,EAtCwBA,EAAM,EAGxBqI,EAAK,KAAK,EAAIA,EAAK,QAAQ,CAC7BvC,CAAK,CACH,GAAI,IAAI,CAAC2D,GAAapB,EAAK,QAAQ,EAC/BoB,EAAW,CAACpB,EAAK,QAAQ,CAAC,CAC1BA,EAAK,QAAQ,CAClB,CAAGrI,EACKqI,EAAK,SAAS,EACvBvC,CAAAA,CAAK,CAACuC,EAAK,SAAS,CAAC,CAAGrI,CAAK,CAEjC,EAnNoBsC,EAAYjD,EAAUuB,EAAK,UAAU,CAACvB,EAAS,CAAEwK,GAK/DxL,CAAAA,AAAS,OAATA,GAAiBA,AAAS,OAATA,CAAY,GAC/BwL,EAAQ,SAAS,GAGnB,IAAM5G,EAAW2G,GAAgBC,EAASjJ,GAEtCvC,CAAAA,AAAS,OAATA,GAAiBA,AAAS,OAATA,CAAY,GAC/BwL,EAAQ,SAAS,GAInBA,EAAQ,MAAM,CAAGI,EAIjB,IAAMW,EAAWhK,EAAK,QAAQ,EAAI,CAChC,MAAO,CAAC,KAAM,KAAM,OAAQ,KAAM,OAAQ,IAAI,EAC9C,IAAK,CAAC,KAAM,KAAM,OAAQ,KAAM,OAAQ,IAAI,CAC9C,EACMiK,EACJ1M,EAAQ,UAAU,EAAI,GAAI,IAAI,CAACA,EAAQ,UAAU,CAAEE,GAC/CF,EAAQ,UAAU,CAACE,EAAK,CACxBA,EACAyM,EAAQ,AAAqB,UAArB,OAAOD,GAA0BA,IAAc,UAAc,CAE3E,GAAI,CAAC,qBAA0B,CAACA,GAC9B,MAAM,AAAI7B,UACR,CAAC,qBAAqB,EAAE3K,EAAK,mCAAmC,CAAC,EAsDrE,GAlDAiE,EAAW,GAAG,CAAGrC,EAEJ,MAAT5B,GAAgBF,EAAQ,UAAU,EACpCmE,CAAAA,EAAW,MAAM,CACf,AAA8B,YAA9B,OAAOnE,EAAQ,UAAU,CACrBA,EAAQ,UAAU,CAChBmC,OAAOgC,EAAW,IAAI,EAAI,IAC1B1B,EAAK,QAAQ,CACb,AAA4B,UAA5B,OAAO0B,EAAW,KAAK,CAAgBA,EAAW,KAAK,CAAG,MAE5DnE,EAAQ,UAAU,AAAD,EAGZ,MAATE,GAAgB2L,GAClB1H,CAAAA,EAAW,IAAI,CAAG0H,EAChB1J,OAAOgC,EAAW,IAAI,EAAI,IAC1B1B,EAAK,QAAQ,CACb,AAA4B,UAA5B,OAAO0B,EAAW,KAAK,CAAgBA,EAAW,KAAK,CAAG,KAC5D,EAIA,CAACwI,GACDzM,AAAS,SAATA,GACAwE,AAAgB,YAAhBA,EAAO,IAAI,EACXA,AAAmB,QAAnBA,EAAO,OAAO,EAEdP,CAAAA,EAAW,MAAM,CAAG,EAAG,EAIvB,CAACwI,GACAzM,CAAAA,AAAS,OAATA,GACCA,AAAS,OAATA,GACAA,AAAS,OAATA,GACAA,AAAS,OAATA,GACAA,AAAS,OAATA,GACAA,AAAS,OAATA,CAAY,GAEdiE,CAAAA,EAAW,KAAK,CAAGyI,OAAO,QAAQ,CAAC1M,EAAK,MAAM,CAAC,GAAI,GAAE,EAG1C,QAATA,GAAkBF,EAAQ,iBAAiB,EAC7CmE,CAAAA,EAAW,GAAG,CAAGnE,EAAQ,iBAAiB,CACxCmC,OAAOgC,EAAW,GAAG,EAAI,IACzBhC,OAAOgC,EAAW,GAAG,EAAI,IACzB,AAA4B,UAA5B,OAAOA,EAAW,KAAK,CAAgBA,EAAW,KAAK,CAAG,KAC5D,EAGE,CAACwI,GAASzM,AAAS,OAATA,GAAiBwE,AAAgB,YAAhBA,EAAO,IAAI,CAAgB,CACxD,IAAM/C,EAAQkL,AA0DlB,SAAyBpK,CAAI,EAC3B,IAAIX,EAAQ,GAEZ,KAAO,EAAEA,EAAQW,EAAK,QAAQ,CAAC,MAAM,EAAE,CACrC,IAAMuC,EAAQvC,EAAK,QAAQ,CAACX,EAAM,CAElC,GAAIkD,AAAe,YAAfA,EAAM,IAAI,EAAkBA,AAAkB,UAAlBA,EAAM,OAAO,CAC3C,OAAOA,CAEX,CAEA,OAAO,IACT,EAtEkCvC,EAC9B0B,CAAAA,EAAW,OAAO,CAChBxC,GAASA,EAAM,UAAU,CAAGmL,CAAAA,CAAQnL,EAAM,UAAU,CAAC,OAAO,CAAI,KAClEwC,EAAW,KAAK,CAAG4I,GAAuBrI,EAAQjC,GAClD0B,EAAW,OAAO,CAAGO,AAAmB,OAAnBA,EAAO,OAAO,AACrC,CA4CA,MA1CI,CAACiI,GAAUzM,CAAAA,AAAS,OAATA,GAAiBA,AAAS,OAATA,CAAY,IAC1CiE,EAAW,OAAO,CAAGjE,AAAS,OAATA,EACrBiE,EAAW,KAAK,CAAGuH,EAAQ,SAAS,EAGlCxL,CAAAA,AAAS,OAATA,GAAiBA,AAAS,OAATA,CAAY,IAC3BiE,EAAW,KAAK,GACd,CAACA,EAAW,KAAK,EAAEA,CAAAA,EAAW,KAAK,CAAG,CAAC,GAE3CA,EAAW,KAAK,CAAC,SAAS,CAAGA,EAAW,KAAK,CAC7C,OAAOA,EAAW,KAAK,EAGrB,CAACwI,GACHxI,CAAAA,EAAW,QAAQ,CAAGjE,AAAS,OAATA,CAAY,GAIlC,CAACyM,GAASzM,AAAS,OAATA,GAAiBwE,AAAgB,YAAhBA,EAAO,IAAI,EACxCP,CAAAA,EAAW,QAAQ,CAAWO,AAAmB,UAAnBA,EAAO,OAAO,AAAY,EAItD1E,EAAQ,SAAS,EACnBmE,CAAAA,CAAU,CAAC,iBAAiB,CAAG6I,AAqInC,SAAyBC,CAAG,EAC1B,MAAO,CACLA,EAAI,KAAK,CAAC,IAAI,CACd,IACAA,EAAI,KAAK,CAAC,MAAM,CAChB,IACAA,EAAI,GAAG,CAAC,IAAI,CACZ,IACAA,EAAI,GAAG,CAAC,MAAM,CACf,CACE,GAAG,CAAC9K,QACJ,IAAI,CAAC,GACV,EAjJmDsK,EAAQ,EAGrD,CAACE,GAAS3M,EAAQ,YAAY,EAChCmE,CAAAA,EAAW,cAAc,CAAG1B,EAAK,QAAQ,AAAD,EAItC,CAACkK,GAAS3M,EAAQ,mBAAmB,GACvCmE,EAAW,KAAK,CAAG4I,GAAuBrI,EAAQjC,GAClD0B,EAAW,YAAY,CAAG4I,GAAuBrI,IAG/C,CAACiI,GACHxI,CAAAA,EAAW,IAAI,CAAG1B,CAAG,EAIhBqC,EAAS,MAAM,CAAG,EACrB,eAAmB,CAAC4H,EAAWvI,EAAYW,GAC3C,eAAmB,CAAC4H,EAAWvI,EACrC,EAlM4BuH,EAAS1G,EAAO2G,EAAYlJ,IACzCuC,AAAe,SAAfA,EAAM,IAAI,CASjBvC,CAAAA,AAAc,YAAdA,EAAK,IAAI,EACT,CAAC8I,GAAc,GAAG,CAAC9I,EAAK,OAAO,GAC/B,CAACyK,AEpIF,SAAoBC,CAAK,EAE9B,IAAMhM,EAEJgM,GAAS,AAAiB,UAAjB,OAAOA,GAAsBA,AAAe,SAAfA,EAAM,IAAI,CAE5CA,EAAM,KAAK,EAAI,GACfA,EAIN,MAAO,AAAiB,UAAjB,OAAOhM,GAAsBA,AAAsC,KAAtCA,EAAM,OAAO,CAAC,eAAgB,GACpE,EFwHoB6D,EAAK,GAEjBF,EAAS,IAAI,CAACE,EAAM,KAAK,EAEH,QAAfA,EAAM,IAAI,EAAc,CAAC0G,EAAQ,OAAO,CAAC,QAAQ,EAE1D5G,EAAS,IAAI,CAACE,EAAM,KAAK,EAI7B,OAAOF,CACT,CAoMA,SAASiI,GAAuBrI,CAAM,CAAEjC,CAAI,EAC1C,IAAIX,EAAQ,GACRsL,EAAQ,EAEZ,KACE,AADK,EAAEtL,EAAQ4C,EAAO,QAAQ,CAAC,MAAM,EACjCA,EAAO,QAAQ,CAAC5C,EAAM,GAAKW,GADQ,EAEH,YAAhCiC,EAAO,QAAQ,CAAC5C,EAAM,CAAC,IAAI,EAAgBsL,GACjD,CAEA,OAAOA,CACT,CAqEA,SAASZ,GAAcpC,CAAC,CAAEiD,CAAE,EAC1B,OAAOA,EAAG,WAAW,EACvB,CG5YA,IAAM,GAAM,CAAC,EAAE,cAAc,CAKvBC,GAAa,CACjB,QAAS,CAAC,GAAI,gBAAiB,GAAI,iCAAiC,EACpE,UAAW,CAAC,GAAI,aAAc,GAAI,gCAAgC,EAClE,WAAY,CAAC,GAAI,sCAAsC,EACvD,mBAAoB,CAAC,GAAI,sCAAsC,EAC/D,WAAY,CAAC,GAAI,sCAAsC,EACvD,OAAQ,CAAC,GAAI,WAAY,GAAI,2BAA2B,EACxD,UAAW,CACT,GAAI,eACJ,GAAI,oDACN,EACA,aAAc,CACZ,GAAI,kBACJ,GAAI,oDACN,EACA,gBAAiB,CACf,GAAI,qBACJ,GAAI,oDACN,EACA,iBAAkB,CAChB,GAAI,sBACJ,GAAI,gDACN,CACF,EAQO,SAASC,GAAcvN,CAAO,EACnC,IAAK,IAAML,KAAO2N,GAChB,GAAI,GAAI,IAAI,CAACA,GAAY3N,IAAQ,GAAI,IAAI,CAACK,EAASL,GAAM,CACvD,IAAM6N,EAAcF,EAAU,CAAC3N,EAAI,CACnC8N,QAAQ,IAAI,CACV,oCACED,EAAY,EAAE,CAAG,CAAC,MAAM,EAAEA,EAAY,EAAE,CAAC,aAAa,CAAC,CAAG,cACtD7N,+EAA4B6N,EAAY,EAAE,kBAAkB,EAEpE,OAAOF,EAAU,CAAC3N,EAAI,AACxB,CAGF,IAAM+N,EAAY,UACf,GAAG,CAAC,GAAW,EACf,GAAG,CAAC1N,EAAQ,aAAa,EAAI,EAAE,EAC/B,GAAG,CAAC,EAAc,CACjB,GAAGA,EAAQ,mBAAmB,CAC9B,mBAAoB,EACtB,GACC,GAAG,CAACA,EAAQ,aAAa,EAAI,EAAE,EAC/B,GAAG,CAAC4K,GAAc5K,GAEfwI,EAAO,IAAI,GAAK,AAElB,AAA4B,WAA5B,OAAOxI,EAAQ,QAAQ,CACzBwI,EAAK,KAAK,CAAGxI,EAAQ,QAAQ,CACCwE,KAAAA,IAArBxE,EAAQ,QAAQ,EAAkBA,AAAqB,OAArBA,EAAQ,QAAQ,EAC3DyN,QAAQ,IAAI,CACV,CAAC,uEAAuE,EAAEzN,EAAQ,QAAQ,CAAC,GAAG,CAAC,EAInG,IAAM2N,EAAWD,EAAU,OAAO,CAACA,EAAU,KAAK,CAAClF,GAAOA,GAE1D,GAAImF,AAAkB,SAAlBA,EAAS,IAAI,CACf,MAAM,AAAI9C,UAAU,0BAItB,IAAIhJ,EAAS,eAAmB,CAC9B,UAAc,CACd,CAAC,EACD4J,GAAgB,CAACzL,QAAAA,EAAS,OAAQ,GAAM,UAAW,CAAC,EAAG2N,IAOzD,OAJI3N,EAAQ,SAAS,EACnB6B,CAAAA,EAAS,eAAmB,CAAC,MAAO,CAAC,UAAW7B,EAAQ,SAAS,EAAG6B,EAAM,EAGrEA,CACT,CAEA0L,GAAc,SAAS,CAAG,CAExB,SAAU,QAAgB,CAE1B,UAAW,QAAgB,CAE3B,aAAc,MAAc,CAC5B,gBAAiB,SAAiB,CAAC,QAAgB,EACnD,mBAAoB,SAAiB,CAAC,QAAgB,EACtD,iBAAkB,MAAc,CAEhC,cAAe,SAAiB,CAC9B,WAAmB,CAAC,CAClB,QAAgB,CAChB,MAAc,CACd,SAAiB,CACf,WAAmB,CAAC,CAClB,MAAc,CACd,QAAgB,CAChB,QAAgB,CAChB,MAAc,CACd,SAAiB,CAGf,KAAa,EAEhB,GAEJ,GAEH,cAAe,SAAiB,CAC9B,WAAmB,CAAC,CAClB,QAAgB,CAChB,MAAc,CACd,SAAiB,CACf,WAAmB,CAAC,CAClB,MAAc,CACd,QAAgB,CAChB,QAAgB,CAChB,MAAc,CACd,SAAiB,CAGf,KAAa,EAEhB,GAEJ,GAGH,UAAW,MAAc,CACzB,aAAc,MAAc,CAC5B,SAAU,MAAc,CACxB,oBAAqB,MAAc,CACnC,iBAAkB,WAAmB,CAAC,CAAC,MAAc,CAAE,MAAc,CAAC,EACtE,WAAY,WAAmB,CAAC,CAAC,MAAc,CAAE,QAAgB,CAAC,EAClE,kBAAmB,MAAc,CACjC,WAAY,QAAgB,AAC9B,uECvLA,IAAMK,EAAY,CAAC,OAAQ,QAAS,SAAU,MAAM,CAM7C,SAASC,EAAeC,CAAG,EAChC,IAAMC,EAAM,AAACD,CAAAA,GAAO,EAAC,EAAG,IAAI,GACtBE,EAAQD,EAAI,MAAM,CAAC,GAEzB,GAAIC,AAAU,MAAVA,GAAiBA,AAAU,MAAVA,EACnB,OAAOD,EAGT,IAAME,EAAQF,EAAI,OAAO,CAAC,KAC1B,GAAIE,AAAU,KAAVA,EACF,OAAOF,EAGT,IAAIjM,EAAQ,GAEZ,KAAO,EAAEA,EAAQ8L,EAAU,MAAM,EAAE,CACjC,IAAMM,EAAWN,CAAS,CAAC9L,EAAM,CAEjC,GACEmM,IAAUC,EAAS,MAAM,EACzBH,EAAI,KAAK,CAAC,EAAGG,EAAS,MAAM,EAAE,WAAW,KAAOA,EAEhD,OAAOH,CAEX,QAGA,AAAc,KADdjM,CAAAA,EAAQiM,EAAI,OAAO,CAAC,IAAG,GACHE,EAAQnM,GAKxBA,AAAU,KADdA,CAAAA,EAAQiM,EAAI,OAAO,CAAC,IAAG,GACHE,EAAQnM,EAJnBiM,EASF,oBACT,uFCjCe,SAASI,EAAYnO,CAAO,EAkBzCd,OAAO,MAAM,CAAC,IAAI,CAAE,CAAC,OAhBN,AAACkP,IAEd,IAAM1M,EAAmC,IAAI,CAAC,IAAI,CAAC,YAEnD,MAAO,QACL0M,EACAlP,OAAO,MAAM,CAAC,CAAC,EAAGwC,EAAU1B,EAAS,CAInC,WAAY,IAAI,CAAC,IAAI,CAAC,wBAA0B,EAAE,CAClD,gBAAiB,IAAI,CAAC,IAAI,CAAC,2BAA6B,EAAE,AAC5D,GAEJ,CAEmC,EACrC,uCCTO,SAASwB,EAAUC,CAAM,EAC9B,OAAOA,EAAO,IAAI,CAAC,KAAK,IAAI,EAC9B,sEChBO,SAAS4M,EAAK5F,CAAK,EACxB,GAAIA,EACF,MAAMA,CAEV,kDCXe,SAAShJ,EAAc0B,CAAK,EAC1C,GAAI,AAAiB,UAAjB,OAAOA,GAAsBA,AAAU,OAAVA,EAChC,MAAO,GAGR,IAAMmN,EAAYpP,OAAO,cAAc,CAACiC,GACxC,MAAO,AAACmN,CAAAA,AAAc,OAAdA,GAAsBA,IAAcpP,OAAO,SAAS,EAAIA,AAAqC,OAArCA,OAAO,cAAc,CAACoP,EAAkB,GAAM,CAAEC,CAAAA,OAAO,WAAW,IAAIpN,CAAI,GAAM,CAAEoN,CAAAA,OAAO,QAAQ,IAAIpN,CAAI,CAC1K,mBCqBO,IAAMqN,EAAUC,AAQvB,UAASA,QAOHC,EANJ,IAAMC,EAAeC,ACChB,WAEL,IAAMC,EAAM,EAAE,CAERC,EAAW,CAACC,IAKlB,SAAa,GAAGtN,CAAM,EACpB,IAAIuN,EAAkB,GAEhBC,EAAWxN,EAAO,GAAG,GAE3B,GAAI,AAAoB,YAApB,OAAOwN,EACT,MAAM,AAAIpE,UAAU,2CAA6CoE,GAGnE7M,AAQA,UAASA,EAAKqG,CAAK,CAAE,GAAGrH,CAAM,EAC5B,IAAM8N,EAAKL,CAAG,CAAC,EAAEG,EAAgB,CAC7BlN,EAAQ,GAEZ,GAAI2G,EAAO,CACTwG,EAASxG,GACT,MACF,CAGA,KAAO,EAAE3G,EAAQL,EAAO,MAAM,EACxBL,CAAAA,AAAkB,OAAlBA,CAAM,CAACU,EAAM,EAAaV,AAAkBoD,KAAAA,IAAlBpD,CAAM,CAACU,EAAM,AAAa,GACtDV,CAAAA,CAAM,CAACU,EAAM,CAAGL,CAAM,CAACK,EAAM,AAAD,EAKhCL,EAASL,EAGL8N,EACFlI,AAoDD,UAAcmI,CAAU,CAAEF,CAAQ,EAEvC,IAAIG,EAEJ,OAQA,SAAiB,GAAGC,CAAU,MAGxBxN,EAFJ,IAAMyN,EAAoBH,EAAW,MAAM,CAAGE,EAAW,MAAM,CAI3DC,GACFD,EAAW,IAAI,CAACE,GAGlB,GAAI,CACF1N,EAASsN,EAAW,KAAK,CAAC,IAAI,CAAEE,EAClC,CAAE,MAAO5G,EAAO,CAOd,GAAI6G,GAAqBF,EACvB,MAPsC3G,EAUxC,OAAO8G,EAViC9G,EAW1C,CAEI,CAAC6G,IACCzN,GAAUA,EAAO,IAAI,EAAI,AAAuB,YAAvB,OAAOA,EAAO,IAAI,CAC7CA,EAAO,IAAI,CAAC2N,EAAMD,GACT1N,aAAkB+E,MAC3B2I,EAAK1N,GAEL2N,EAAK3N,GAGX,EAOA,SAAS0N,EAAK9G,CAAK,CAAE,GAAGrH,CAAM,EACxB,CAACgO,IACHA,EAAS,GACTH,EAASxG,KAAUrH,GAEvB,CAOA,SAASoO,EAAKrO,CAAK,EACjBoO,EAAK,KAAMpO,EACb,CACF,GAxHa+N,EAAI9M,MAAShB,GAElB6N,EAAS,QAAS7N,EAEtB,GAjCK,QAASK,EAkChB,EAhDuBgO,IAmDvB,SAAaC,CAAU,EACrB,GAAI,AAAsB,YAAtB,OAAOA,EACT,MAAM,AAAI7E,UACR,+CAAiD6E,GAKrD,OADAb,EAAI,IAAI,CAACa,GACFZ,CACT,CA5D0B,EAE1B,OAAOA,CA2DT,IDhEQa,EAAY,EAAE,CAEhBC,EAAY,CAAC,EAGbC,EAAc,GA2BlB,OAvBAnC,EAAU,IAAI,CA6Cd,SAAc/N,CAAG,CAAEwB,CAAK,EACtB,GAAI,AAAe,UAAf,OAAOxB,SAET,AAAIc,AAAqB,GAArBA,UAAU,MAAM,EAClBqP,EAAe,OAAQpB,GACvBkB,CAAS,CAACjQ,EAAI,CAAGwB,EACVuM,GAIF,AAAC/K,EAAI,IAAI,CAACiN,EAAWjQ,IAAQiQ,CAAS,CAACjQ,EAAI,EAAK,YAIzD,AAAIA,GACFmQ,EAAe,OAAQpB,GACvBkB,EAAYjQ,EACL+N,GAIFkC,CACT,EAlEAlC,EAAU,MAAM,CAAGlJ,KAAAA,EACnBkJ,EAAU,QAAQ,CAAGlJ,KAAAA,EAGrBkJ,EAAU,MAAM,CAiEhB,WACE,GAAIgB,EACF,OAAOhB,EAGT,KAAO,EAAEmC,EAAcF,EAAU,MAAM,EAAE,CACvC,GAAM,CAACI,EAAU,GAAG/P,EAAQ,CAAG2P,CAAS,CAACE,EAAY,CAErD,GAAI7P,AAAe,KAAfA,CAAO,CAAC,EAAE,CACZ,QAGiB,MAAfA,CAAO,CAAC,EAAE,EACZA,CAAAA,CAAO,CAAC,EAAE,CAAGwE,KAAAA,CAAQ,EAIvB,IAAMwL,EAAcD,EAAS,IAAI,CAACrC,KAAc1N,EAErB,aAAvB,OAAOgQ,GACTrB,EAAa,GAAG,CAACqB,EAErB,CAKA,OAHAtB,EAAS,GACTmB,EAAcjD,OAAO,iBAAiB,CAE/Bc,CACT,EA1FAA,EAAU,SAAS,CAAGiC,EAEtBjC,EAAU,GAAG,CA+Fb,SAAavM,CAAK,CAAE,GAAGnB,CAAO,EAE5B,IAAI0B,EAIJ,GAFAoO,EAAe,MAAOpB,GAElBvN,MAAAA,QAEG,GAAI,AAAiB,YAAjB,OAAOA,EAChB8O,EAAU9O,KAAUnB,QACf,GAAI,AAAiB,UAAjB,OAAOmB,EACZ3B,MAAM,OAAO,CAAC2B,GAChB+O,EAAQ/O,GAERgP,EAAUhP,QAGZ,MAAM,AAAI0J,UAAU,+BAAiC1J,EAAQ,KAO/D,OAJIO,GACFkO,CAAAA,EAAU,QAAQ,CAAG1Q,OAAO,MAAM,CAAC0Q,EAAU,QAAQ,EAAI,CAAC,EAAGlO,EAAQ,EAGhEgM,EAyBP,SAASyC,EAAUtO,CAAM,EACvBqO,EAAQrO,EAAO,OAAO,EAElBA,EAAO,QAAQ,EACjBH,CAAAA,EAAWxC,OAAO,MAAM,CAACwC,GAAY,CAAC,EAAGG,EAAO,QAAQ,EAE5D,CAMA,SAASqO,EAAQE,CAAO,EACtB,IAAItO,EAAQ,GAEZ,GAAIsO,MAAAA,QAEG,GAAI5Q,MAAM,OAAO,CAAC4Q,GACvB,KAAO,EAAEtO,EAAQsO,EAAQ,MAAM,GAE7BC,AAvCN,SAAalP,CAAK,EAChB,GAAI,AAAiB,YAAjB,OAAOA,EACT8O,EAAU9O,QACL,GAAI,AAAiB,UAAjB,OAAOA,GAChB,GAAI3B,MAAM,OAAO,CAAC2B,GAAQ,CACxB,GAAM,CAACmP,EAAQ,GAAGtQ,EAAQ,CAAGmB,EAC7B8O,EAAUK,KAAWtQ,EACvB,MACEmQ,EAAUhP,QAGZ,MAAM,AAAI0J,UAAU,+BAAiC1J,EAAQ,IAEjE,EAyBoBiP,CAAO,CAACtO,EAAM,OAI9B,MAAM,AAAI+I,UAAU,oCAAsCuF,EAAU,IAExE,CAOA,SAASH,EAAUK,CAAM,CAAEnP,CAAK,EAC9B,IAEIoP,EAFAzO,EAAQ,GAIZ,KAAO,EAAEA,EAAQ6N,EAAU,MAAM,EAC/B,GAAIA,CAAS,CAAC7N,EAAM,CAAC,EAAE,GAAKwO,EAAQ,CAClCC,EAAQZ,CAAS,CAAC7N,EAAM,CACxB,KACF,CAGEyO,GACE,EAAWA,CAAK,CAAC,EAAE,GAAK,EAAWpP,IACrCA,CAAAA,EAAQf,EAAO,GAAMmQ,CAAK,CAAC,EAAE,CAAEpP,EAAK,EAGtCoP,CAAK,CAAC,EAAE,CAAGpP,GAGXwO,EAAU,IAAI,CAAC,IAAIlP,UAAU,CAEjC,CACF,EApMAiN,EAAU,KAAK,CAuMf,SAAeU,CAAG,EAChBV,EAAU,MAAM,GAChB,IAAMlF,EAAOgI,EAAMpC,GACbqC,EAAS/C,EAAU,MAAM,OAG/B,CAFAgD,EAAa,QAASD,GAElBE,EAAQF,EAAQ,UAEX,IAAIA,EAAOtO,OAAOqG,GAAOA,GAAM,KAAK,GAItCiI,EAAOtO,OAAOqG,GAAOA,EAC9B,EAnNAkF,EAAU,SAAS,CAsNnB,SAAmBjL,CAAI,CAAE2L,CAAG,EAC1BV,EAAU,MAAM,GAChB,IAAMlF,EAAOgI,EAAMpC,GACbwC,EAAWlD,EAAU,QAAQ,OAInC,CAHAmD,EAAe,YAAaD,GAC5BE,EAAWrO,GAEPkO,EAAQC,EAAU,YAEb,IAAIA,EAASnO,EAAM+F,GAAM,OAAO,GAIlCoI,EAASnO,EAAM+F,EACxB,EAlOAkF,EAAU,GAAG,CA0Ob,SAAajL,CAAI,CAAE2L,CAAG,CAAEa,CAAQ,EAS9B,GARA6B,EAAWrO,GACXiL,EAAU,MAAM,GAEZ,CAACuB,GAAY,AAAe,YAAf,OAAOb,IACtBa,EAAWb,EACXA,EAAM5J,KAAAA,GAGJ,CAACyK,EACH,OAAO,IAAI8B,QAAQC,GAUrB,SAASA,EAASC,CAAO,CAAEC,CAAM,EAE/BvC,EAAa,GAAG,CAAClM,EAAM+N,EAAMpC,GAQ7B,SAAc3F,CAAK,CAAEvB,CAAI,CAAEsB,CAAI,EAC7BtB,EAAOA,GAAQzE,EACXgG,EACFyI,EAAOzI,GACEwI,EACTA,EAAQ/J,GAGR+H,EAAS,KAAM/H,EAAMsB,EAEzB,EACF,CA5BAwI,EAAS,KAAM/B,EA6BjB,EAnRAvB,EAAU,OAAO,CAsRjB,SAAiBjL,CAAI,CAAE+F,CAAI,EAEzB,IAAI3G,EAEAsP,EAOJ,OALAzD,EAAU,GAAG,CAACjL,EAAM+F,EAYpB,SAAcC,CAAK,CAAEvB,CAAI,EACvBmH,EAAK5F,GACL5G,EAASqF,EACTiK,EAAW,EACb,GAdAC,EAAW,UAAW,MAAOD,GAGtBtP,CAYT,EA3SA6L,EAAU,OAAO,CAkTjB,SAAiBU,CAAG,CAAEa,CAAQ,EAK5B,GAJAvB,EAAU,MAAM,GAChBgD,EAAa,UAAWhD,EAAU,MAAM,EACxCmD,EAAe,UAAWnD,EAAU,QAAQ,EAExC,CAACuB,EACH,OAAO,IAAI8B,QAAQC,GAUrB,SAASA,EAASC,CAAO,CAAEC,CAAM,EAC/B,IAAM1I,EAAOgI,EAAMpC,GA0BnB,SAASmB,EAAK9G,CAAK,CAAED,CAAI,EACnBC,GAAS,CAACD,EACZ0I,EAAOzI,GACEwI,EACTA,EAAQzI,GAGRyG,EAAS,KAAMzG,EAEnB,CAjCAkF,EAAU,GAAG,CAACA,EAAU,KAAK,CAAClF,GAAOA,EAAM,CAACC,EAAOvB,EAAMsB,KACvD,GAAIC,CAAAA,GAAUvB,GAASsB,EAEhB,CAEL,IAAM3G,EAAS6L,EAAU,SAAS,CAACxG,EAAMsB,SAErC3G,IAEOwP,AAmMrB,SAA8BlQ,CAAK,EACjC,MAAO,AAAiB,UAAjB,OAAOA,GAAsB,EAASA,EAC/C,EArM0CU,GAC9B2G,EAAK,KAAK,CAAG3G,EAEb2G,EAAK,MAAM,CAAG3G,GAGhB0N,EAAK9G,EAAOD,EACd,MAdE+G,EAAK9G,EAeT,EAiBF,CA5CAuI,EAAS,KAAM/B,EA6CjB,EAvWAvB,EAAU,WAAW,CA0WrB,SAAqBU,CAAG,MAElB+C,EAEJzD,EAAU,MAAM,GAChBgD,EAAa,cAAehD,EAAU,MAAM,EAC5CmD,EAAe,cAAenD,EAAU,QAAQ,EAEhD,IAAMlF,EAAOgI,EAAMpC,GAMnB,OAJAV,EAAU,OAAO,CAAClF,EAUlB,SAAcC,CAAK,EACjB0I,EAAW,GACX9C,EAAK5F,EACP,GAXA2I,EAAW,cAAe,UAAWD,GAE9B3I,CAUT,EA/XOkF,EAIP,SAASA,IACP,IAAMpF,EAAcmG,IAChB3M,EAAQ,GAEZ,KAAO,EAAEA,EAAQ6N,EAAU,MAAM,EAC/BrH,EAAY,GAAG,IAAIqH,CAAS,CAAC7N,EAAM,EAKrC,OAFAwG,EAAY,IAAI,CAAClI,EAAO,GAAM,CAAC,EAAGwP,IAE3BtH,CACT,CAiXF,KA3a8B,MAAM,GAE9B3F,EAAM,CAAC,EAAE,cAAc,CAkb7B,SAASgO,EAAQxP,CAAK,CAAEjB,CAAI,EAC1B,MACE,AAAiB,YAAjB,OAAOiB,GAGPA,EAAM,SAAS,EAKdmQ,CAAAA,AAUL,SAAcnQ,CAAK,EAEjB,IAAIxB,EAEJ,IAAKA,KAAOwB,EACV,GAAIwB,EAAI,IAAI,CAACxB,EAAOxB,GAClB,MAAO,GAIX,MAAO,EACT,EArBUwB,EAAM,SAAS,GAAKjB,KAAQiB,EAAM,SAAS,AAAD,CAEpD,CA4BA,SAASuP,EAAaxQ,CAAI,CAAEiB,CAAK,EAC/B,GAAI,AAAiB,YAAjB,OAAOA,EACT,MAAM,AAAI0J,UAAU,WAAa3K,EAAO,qBAE5C,CASA,SAAS2Q,EAAe3Q,CAAI,CAAEiB,CAAK,EACjC,GAAI,AAAiB,YAAjB,OAAOA,EACT,MAAM,AAAI0J,UAAU,WAAa3K,EAAO,uBAE5C,CASA,SAAS4P,EAAe5P,CAAI,CAAEwO,CAAM,EAClC,GAAIA,EACF,MAAM,AAAI9H,MACR,gBACE1G,EACA,mHAGR,CAQA,SAAS4Q,EAAWrO,CAAI,EAGtB,GAAI,CAAC,EAAWA,IAAS,AAAqB,UAArB,OAAOA,EAAK,IAAI,CACvC,MAAM,AAAIoI,UAAU,uBAAyBpI,EAAO,IAGxD,CAUA,SAAS2O,EAAWlR,CAAI,CAAEqR,CAAS,CAAEJ,CAAQ,EAC3C,GAAI,CAACA,EACH,MAAM,AAAIvK,MACR,IAAM1G,EAAO,0BAA4BqR,EAAY,YAG3D,CAMA,SAASf,EAAMrP,CAAK,EAClB,OAAOqQ,AAOT,SAAyBrQ,CAAK,EAC5B,MAAO2L,CAAAA,CACL3L,CAAAA,GACE,AAAiB,UAAjB,OAAOA,GACP,YAAaA,GACb,aAAcA,CAAI,CAExB,EAdyBA,GAASA,EAAQ,IAAI,GAAK,CAACA,EACpD,uFErjBO,OAAMsQ,UAAqB7K,MAmBhC,YAAY8K,CAAM,CAAEC,CAAK,CAAEC,CAAM,CAAE,CAEjC,IAAMC,EAAQ,CAAC,KAAM,KAAK,CAEtBpF,EAAW,CAEb,MAAO,CAAC,KAAM,KAAM,OAAQ,IAAI,EAEhC,IAAK,CAAC,KAAM,KAAM,OAAQ,IAAI,CAChC,EASA,GAPA,KAAK,GAEgB,UAAjB,OAAOkF,IACTC,EAASD,EACTA,EAAQnN,KAAAA,GAGN,AAAkB,UAAlB,OAAOoN,EAAqB,CAC9B,IAAM9P,EAAQ8P,EAAO,OAAO,CAAC,IAEzB9P,AAAU,MAAVA,EACF+P,CAAK,CAAC,EAAE,CAAGD,GAEXC,CAAK,CAAC,EAAE,CAAGD,EAAO,KAAK,CAAC,EAAG9P,GAC3B+P,CAAK,CAAC,EAAE,CAAGD,EAAO,KAAK,CAAC9P,EAAQ,GAEpC,CAEI6P,IAEE,SAAUA,GAAS,aAAcA,EAC/BA,EAAM,QAAQ,EAGhBlF,CAAAA,EAAWkF,EAAM,QAAQ,AAAD,EAInB,UAAWA,GAAS,QAASA,EAGpClF,EAAWkF,EAGJ,UAAUA,GAAS,WAAYA,CAAI,GAE1ClF,CAAAA,EAAS,KAAK,CAAGkF,CAAI,GAWzB,IAAI,CAAC,IAAI,CAAG,QAAkBA,IAAU,MAOxC,IAAI,CAAC,OAAO,CAAG,AAAkB,UAAlB,OAAOD,EAAsBA,EAAO,OAAO,CAAGA,EAU7D,IAAI,CAAC,KAAK,CAAG,GAES,UAAlB,OAAOA,GAAuBA,EAAO,KAAK,EAC5C,KAAI,CAAC,KAAK,CAAGA,EAAO,KAAK,AAAD,EAU1B,IAAI,CAAC,MAAM,CAAG,IAAI,CAAC,OAAO,CAY1B,IAAI,CAAC,KAAK,CAOV,IAAI,CAAC,IAAI,CAAGjF,EAAS,KAAK,CAAC,IAAI,CAO/B,IAAI,CAAC,MAAM,CAAGA,EAAS,KAAK,CAAC,MAAM,CAOnC,IAAI,CAAC,QAAQ,CAAGA,EAOhB,IAAI,CAAC,MAAM,CAAGoF,CAAK,CAAC,EAAE,CAOtB,IAAI,CAAC,MAAM,CAAGA,CAAK,CAAC,EAAE,CAOtB,IAAI,CAAC,IAAI,CAYT,IAAI,CAAC,MAAM,CAOX,IAAI,CAAC,QAAQ,CAUb,IAAI,CAAC,GAAG,CAOR,IAAI,CAAC,IAAI,AAEX,CACF,CAEAJ,EAAa,SAAS,CAAC,IAAI,CAAG,GAC9BA,EAAa,SAAS,CAAC,IAAI,CAAG,GAC9BA,EAAa,SAAS,CAAC,MAAM,CAAG,GAChCA,EAAa,SAAS,CAAC,OAAO,CAAG,GACjCA,EAAa,SAAS,CAAC,KAAK,CAAG,GAC/BA,EAAa,SAAS,CAAC,KAAK,CAAG,KAC/BA,EAAa,SAAS,CAAC,MAAM,CAAG,KAChCA,EAAa,SAAS,CAAC,IAAI,CAAG,KAC9BA,EAAa,SAAS,CAAC,MAAM,CAAG,KAChCA,EAAa,SAAS,CAAC,MAAM,CAAG,KAChCA,EAAa,SAAS,CAAC,QAAQ,CAAG,KC7K3B,IAAM,EAAO,CAAC,SAcrB,SAAkBK,CAAI,CAAEC,CAAG,MAUrBC,EATJ,GAAID,AAAQvN,KAAAA,IAARuN,GAAqB,AAAe,UAAf,OAAOA,EAC9B,MAAM,AAAIlH,UAAU,mCAGtBoH,EAAWH,GACX,IAAI/P,EAAQ,EACR+B,EAAM,GACNhC,EAAQgQ,EAAK,MAAM,CAIvB,GAAIC,AAAQvN,KAAAA,IAARuN,GAAqBA,AAAe,IAAfA,EAAI,MAAM,EAAUA,EAAI,MAAM,CAAGD,EAAK,MAAM,CAAE,CACrE,KAAOhQ,KACL,GAAIgQ,AAA2B,KAA3BA,EAAK,UAAU,CAAChQ,GAGlB,IAAIkQ,EAAc,CAChBjQ,EAAQD,EAAQ,EAChB,KACF,OACSgC,EAAM,IAGfkO,EAAe,GACflO,EAAMhC,EAAQ,GAIlB,OAAOgC,EAAM,EAAI,GAAKgO,EAAK,KAAK,CAAC/P,EAAO+B,EAC1C,CAEA,GAAIiO,IAAQD,EACV,MAAO,GAGT,IAAII,EAAmB,GACnBC,EAAWJ,EAAI,MAAM,CAAG,EAE5B,KAAOjQ,KACL,GAAIgQ,AAA2B,KAA3BA,EAAK,UAAU,CAAChQ,GAGlB,IAAIkQ,EAAc,CAChBjQ,EAAQD,EAAQ,EAChB,KACF,OAEIoQ,EAAmB,IAGrBF,EAAe,GACfE,EAAmBpQ,EAAQ,GAGzBqQ,EAAW,KAETL,EAAK,UAAU,CAAChQ,KAAWiQ,EAAI,UAAU,CAACI,KACxCA,EAAW,GAGbrO,CAAAA,EAAMhC,CAAI,GAKZqQ,EAAW,GACXrO,EAAMoO,IAYd,OANInQ,IAAU+B,EACZA,EAAMoO,EACGpO,EAAM,GACfA,CAAAA,EAAMgO,EAAK,MAAM,AAAD,EAGXA,EAAK,KAAK,CAAC/P,EAAO+B,EAC3B,EA9F6B,QAwG7B,SAAiBgO,CAAI,MAUfM,EAPJ,GAFAH,EAAWH,GAEPA,AAAgB,IAAhBA,EAAK,MAAM,CACb,MAAO,IAGT,IAAIhO,EAAM,GACNhC,EAAQgQ,EAAK,MAAM,CAKvB,KAAO,EAAEhQ,GACP,GAAIgQ,AAA2B,KAA3BA,EAAK,UAAU,CAAChQ,GAClB,IAAIsQ,EAAgB,CAClBtO,EAAMhC,EACN,KACF,MACS,CAACsQ,GAEVA,CAAAA,EAAiB,EAAG,EAIxB,OAAOtO,EAAM,EACTgO,AAAuB,KAAvBA,EAAK,UAAU,CAAC,GACd,IACA,IACFhO,AAAQ,IAARA,GAAagO,AAAuB,KAAvBA,EAAK,UAAU,CAAC,GAC7B,KACAA,EAAK,KAAK,CAAC,EAAGhO,EACpB,EAxIsC,QAkJtC,SAAiBgO,CAAI,MAYfM,EAXJH,EAAWH,GAEX,IAAIhQ,EAAQgQ,EAAK,MAAM,CAEnBhO,EAAM,GACNuO,EAAY,EACZC,EAAW,GAGXC,EAAc,EAIlB,KAAOzQ,KAAS,CACd,IAAMG,EAAO6P,EAAK,UAAU,CAAChQ,GAE7B,GAAIG,AAAS,KAATA,EAAuB,CAGzB,GAAImQ,EAAgB,CAClBC,EAAYvQ,EAAQ,EACpB,KACF,CAEA,QACF,CAEIgC,EAAM,IAGRsO,EAAiB,GACjBtO,EAAMhC,EAAQ,GAGZG,AAAS,KAATA,EAEEqQ,EAAW,EACbA,EAAWxQ,EACc,IAAhByQ,GACTA,CAAAA,EAAc,GAEPD,EAAW,IAGpBC,CAAAA,EAAc,EAAC,CAEnB,QAEA,AACED,EAAW,GACXxO,EAAM,GAENyO,AAAgB,IAAhBA,GAECA,AAAgB,IAAhBA,GAAqBD,IAAaxO,EAAM,GAAKwO,IAAaD,EAAY,EAEhE,GAGFP,EAAK,KAAK,CAACQ,EAAUxO,EAC9B,EA/M+C,KAyN/C,SAAc,GAAG0O,CAAQ,EACvB,IAEIC,EAFA3Q,EAAQ,GAIZ,KAAO,EAAEA,EAAQ0Q,EAAS,MAAM,EAC9BP,EAAWO,CAAQ,CAAC1Q,EAAM,EAEtB0Q,CAAQ,CAAC1Q,EAAM,EACjB2Q,CAAAA,EACEA,AAAWjO,KAAAA,IAAXiO,EAAuBD,CAAQ,CAAC1Q,EAAM,CAAG2Q,EAAS,IAAMD,CAAQ,CAAC1Q,EAAM,AAAD,EAI5E,OAAO2Q,AAAWjO,KAAAA,IAAXiO,EAAuB,IAAMzJ,AAatC,SAAmB8I,CAAI,EACrBG,EAAWH,GAEX,IAAMY,EAAWZ,AAAuB,KAAvBA,EAAK,UAAU,CAAC,GAG7B3Q,EAAQwR,AAuBd,SAAyBb,CAAI,CAAEc,CAAc,EAC3C,IAMI3Q,EAEA4Q,EARAhR,EAAS,GACTiR,EAAoB,EACpBC,EAAY,GACZC,EAAO,EACPlR,EAAQ,GAMZ,KAAO,EAAEA,GAASgQ,EAAK,MAAM,EAAE,CAC7B,GAAIhQ,EAAQgQ,EAAK,MAAM,CACrB7P,EAAO6P,EAAK,UAAU,CAAChQ,QAClB,GAAIG,AAAS,KAATA,EACT,WAEAA,EAAO,GAGT,GAAIA,AAAS,KAATA,EAAuB,CACzB,GAAI8Q,IAAcjR,EAAQ,GAAKkR,AAAS,IAATA,QAExB,GAAID,IAAcjR,EAAQ,GAAKkR,AAAS,IAATA,EAAY,CAChD,GACEnR,EAAO,MAAM,CAAG,GAChBiR,AAAsB,IAAtBA,GACAjR,AAAyC,KAAzCA,EAAO,UAAU,CAACA,EAAO,MAAM,CAAG,IAClCA,AAAyC,KAAzCA,EAAO,UAAU,CAACA,EAAO,MAAM,CAAG,IAElC,GAAIA,EAAO,MAAM,CAAG,EAGlB,IAAIgR,AAFJA,CAAAA,EAAiBhR,EAAO,WAAW,CAAC,IAAG,IAEhBA,EAAO,MAAM,CAAG,EAAG,CACpCgR,EAAiB,GACnBhR,EAAS,GACTiR,EAAoB,GAGpBA,EAAoBjR,AADpBA,CAAAA,EAASA,EAAO,KAAK,CAAC,EAAGgR,EAAc,EACZ,MAAM,CAAG,EAAIhR,EAAO,WAAW,CAAC,KAG7DkR,EAAYjR,EACZkR,EAAO,EACP,QACF,OACK,GAAInR,EAAO,MAAM,CAAG,EAAG,CAC5BA,EAAS,GACTiR,EAAoB,EACpBC,EAAYjR,EACZkR,EAAO,EACP,QACF,EAGEJ,IACF/Q,EAASA,EAAO,MAAM,CAAG,EAAIA,EAAS,MAAQ,KAC9CiR,EAAoB,EAExB,MACMjR,EAAO,MAAM,CAAG,EAClBA,GAAU,IAAMiQ,EAAK,KAAK,CAACiB,EAAY,EAAGjR,GAE1CD,EAASiQ,EAAK,KAAK,CAACiB,EAAY,EAAGjR,GAGrCgR,EAAoBhR,EAAQiR,EAAY,EAG1CA,EAAYjR,EACZkR,EAAO,CACT,MAAW/Q,AAAS,KAATA,GAAyB+Q,EAAO,GACzCA,IAEAA,EAAO,EAEX,CAEA,OAAOnR,CACT,EAtG8BiQ,EAAM,CAACY,GAUnC,OARqB,IAAjBvR,EAAM,MAAM,EAAU,CAACuR,GACzBvR,CAAAA,EAAQ,GAAE,EAGRA,EAAM,MAAM,CAAG,GAAK2Q,AAAqC,KAArCA,EAAK,UAAU,CAACA,EAAK,MAAM,CAAG,IACpD3Q,CAAAA,GAAS,GAAE,EAGNuR,EAAW,IAAMvR,EAAQA,CAClC,EA9BgDsR,EAChD,EAxO+C,SA0W/C,SAASR,EAAWH,CAAI,EACtB,GAAI,AAAgB,UAAhB,OAAOA,EACT,MAAM,AAAIjH,UACR,mCAAqCxC,KAAK,SAAS,CAACyJ,GAG1D,CChaO,MAEP,WACE,MAAO,GACT,ECoBO,SAASmB,EAAMC,CAAa,EACjC,OACEA,AAAkB,OAAlBA,GACA,AAAyB,UAAzB,OAAOA,GAEPA,EAAc,IAAI,EAElBA,EAAc,MAAM,AAExB,CC8DA,IAAMC,EAAQ,CAAC,UAAW,OAAQ,WAAY,OAAQ,UAAW,UAAU,AAEpE,OAAMC,EAuBX,YAAYjS,CAAK,CAAE,KAEbnB,EAyGAiK,EApGFjK,EAHGmB,EAEM,AAAiB,UAAjB,OAAOA,GAAsBkS,AAoY5C,SAAgBlS,CAAK,EACnB,OAAO,EAAWA,EACpB,EAtYmDA,GACnC,CAACA,MAAAA,CAAK,EACP8R,EAAM9R,GACL,CAAC,KAAMA,CAAK,EAEZA,EANA,CAAC,EAiBb,IAAI,CAAC,IAAI,CAAG,CAAC,EAOb,IAAI,CAAC,QAAQ,CAAG,EAAE,CASlB,IAAI,CAAC,OAAO,CAAG,EAAE,CAOjB,IAAI,CAAC,GAAG,CAAGmS,IAQX,IAAI,CAAC,KAAK,CAYV,IAAI,CAAC,MAAM,CAUX,IAAI,CAAC,MAAM,CAUX,IAAI,CAAC,GAAG,CAIR,IAAIxR,EAAQ,GAEZ,KAAO,EAAEA,EAAQqR,EAAM,MAAM,EAAE,CAC7B,IAAMlJ,EAAOkJ,CAAK,CAACrR,EAAM,CAKvBmI,KAAQjK,GACRA,AAAkBwE,KAAAA,IAAlBxE,CAAO,CAACiK,EAAK,EACbjK,AAAkB,OAAlBA,CAAO,CAACiK,EAAK,EAGb,KAAI,CAACA,EAAK,CAAGA,AAAS,YAATA,EAAqB,IAAIjK,CAAO,CAACiK,EAAK,CAAC,CAAGjK,CAAO,CAACiK,EAAK,AAAD,CAEvE,CAMA,IAAKA,KAAQjK,EAEP,CAACmT,EAAM,QAAQ,CAAClJ,IAElB,KAAI,CAACA,EAAK,CAAGjK,CAAO,CAACiK,EAAK,AAAD,CAG/B,CAOA,IAAI,MAAO,CACT,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAG,EAAE,AAC9C,CAWA,IAAI,KAAK6H,CAAI,CAAE,CACTmB,EAAMnB,IACRA,CAAAA,EAAOyB,AC1PN,SAAmBzB,CAAI,EAC5B,GAAI,AAAgB,UAAhB,OAAOA,EACTA,EAAO,IAAI0B,IAAI1B,QACV,GAAI,CAACmB,EAAMnB,GAAO,CAEvB,IAAMrJ,EAAQ,AAAIoC,UAChB,+EACEiH,EACA,IAGJ,OADArJ,EAAM,IAAI,CAAG,uBACPA,CACR,CAEA,GAAIqJ,AAAkB,UAAlBA,EAAK,QAAQ,CAAc,CAE7B,IAAMrJ,EAAQ,AAAIoC,UAAU,iCAE5B,OADApC,EAAM,IAAI,CAAG,yBACPA,CACR,CAEA,OAAOgL,AAWT,SAA6B1F,CAAG,EAC9B,GAAIA,AAAiB,KAAjBA,EAAI,QAAQ,CAAS,CAEvB,IAAMtF,EAAQ,AAAIoC,UAChB,uDAGF,OADApC,EAAM,IAAI,CAAG,4BACPA,CACR,CAEA,IAAMiL,EAAW3F,EAAI,QAAQ,CACzBjM,EAAQ,GAEZ,KAAO,EAAEA,EAAQ4R,EAAS,MAAM,EAC9B,GACEA,AAA+B,KAA/BA,EAAS,UAAU,CAAC5R,IACpB4R,AAAmC,KAAnCA,EAAS,UAAU,CAAC5R,EAAQ,GAC5B,CACA,IAAM6R,EAAQD,EAAS,UAAU,CAAC5R,EAAQ,GAC1C,GAAI6R,AAAU,KAAVA,GAA0BA,AAAU,MAAVA,EAAyB,CAErD,IAAMlL,EAAQ,AAAIoC,UAChB,sDAGF,OADApC,EAAM,IAAI,CAAG,4BACPA,CACR,CACF,CAGF,OAAOmL,mBAAmBF,EAC5B,EA1C6B5B,EAC7B,EDoOuBA,EAAI,EAGvB+B,EAAe/B,EAAM,QAEjB,IAAI,CAAC,IAAI,GAAKA,GAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAACA,EAEtB,CAKA,IAAI,SAAU,CACZ,MAAO,AAAqB,UAArB,OAAO,IAAI,CAAC,IAAI,CAAgB,SAAY,CAAC,IAAI,CAAC,IAAI,EAAItN,KAAAA,CACnE,CAOA,IAAI,QAAQsP,CAAO,CAAE,CACnB,EAAW,IAAI,CAAC,QAAQ,CAAE,WAC1B,IAAI,CAAC,IAAI,CAAG,MAAS,CAACA,GAAW,GAAI,IAAI,CAAC,QAAQ,CACpD,CAKA,IAAI,UAAW,CACb,MAAO,AAAqB,UAArB,OAAO,IAAI,CAAC,IAAI,CAAgB,UAAa,CAAC,IAAI,CAAC,IAAI,EAAItP,KAAAA,CACpE,CASA,IAAI,SAASuP,CAAQ,CAAE,CACrBF,EAAeE,EAAU,YACzBC,EAAWD,EAAU,YACrB,IAAI,CAAC,IAAI,CAAG,MAAS,CAAC,IAAI,CAAC,OAAO,EAAI,GAAIA,EAC5C,CAKA,IAAI,SAAU,CACZ,MAAO,AAAqB,UAArB,OAAO,IAAI,CAAC,IAAI,CAAgB,SAAY,CAAC,IAAI,CAAC,IAAI,EAAIvP,KAAAA,CACnE,CASA,IAAI,QAAQyP,CAAO,CAAE,CAInB,GAHAD,EAAWC,EAAS,WACpB,EAAW,IAAI,CAAC,OAAO,CAAE,WAErBA,EAAS,CACX,GAAIA,AAA0B,KAA1BA,EAAQ,UAAU,CAAC,GACrB,MAAM,AAAIrN,MAAM,iCAGlB,GAAIqN,EAAQ,QAAQ,CAAC,IAAK,GACxB,MAAM,AAAIrN,MAAM,yCAEpB,CAEA,IAAI,CAAC,IAAI,CAAG,MAAS,CAAC,IAAI,CAAC,OAAO,CAAE,IAAI,CAAC,IAAI,CAAIqN,CAAAA,GAAW,EAAC,EAC/D,CAKA,IAAI,MAAO,CACT,MAAO,AAAqB,UAArB,OAAO,IAAI,CAAC,IAAI,CACnB,UAAa,CAAC,IAAI,CAAC,IAAI,CAAE,IAAI,CAAC,OAAO,EACrCzP,KAAAA,CACN,CASA,IAAI,KAAK0P,CAAI,CAAE,CACbL,EAAeK,EAAM,QACrBF,EAAWE,EAAM,QACjB,IAAI,CAAC,IAAI,CAAG,MAAS,CAAC,IAAI,CAAC,OAAO,EAAI,GAAIA,EAAQ,KAAI,CAAC,OAAO,EAAI,EAAC,EACrE,CAWA,SAASC,CAAQ,CAAE,CACjB,MAAO,AAAC,KAAI,CAAC,KAAK,EAAI,EAAC,EAAG,QAAQ,CAACA,GAAY3P,KAAAA,EACjD,CAkBA,QAAQkN,CAAM,CAAEC,CAAK,CAAEC,CAAM,CAAE,CAC7B,IAAMwC,EAAU,IAAI3C,EAAaC,EAAQC,EAAOC,GAWhD,OATI,IAAI,CAAC,IAAI,GACXwC,EAAQ,IAAI,CAAG,IAAI,CAAC,IAAI,CAAG,IAAMA,EAAQ,IAAI,CAC7CA,EAAQ,IAAI,CAAG,IAAI,CAAC,IAAI,EAG1BA,EAAQ,KAAK,CAAG,GAEhB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAACA,GAEZA,CACT,CAkBA,KAAK1C,CAAM,CAAEC,CAAK,CAAEC,CAAM,CAAE,CAC1B,IAAMwC,EAAU,IAAI,CAAC,OAAO,CAAC1C,EAAQC,EAAOC,GAI5C,OAFAwC,EAAQ,KAAK,CAAG,KAETA,CACT,CAsBA,KAAK1C,CAAM,CAAEC,CAAK,CAAEC,CAAM,CAAE,CAC1B,IAAMwC,EAAU,IAAI,CAAC,OAAO,CAAC1C,EAAQC,EAAOC,EAI5C,OAFAwC,EAAQ,KAAK,CAAG,GAEVA,CACR,CACF,CAYA,SAASJ,EAAWK,CAAI,CAAEnU,CAAI,EAC5B,GAAImU,GAAQA,EAAK,QAAQ,CAAC,KAAQ,EAChC,MAAM,AAAIzN,MACR,IAAM1G,EAAO,uCAAyC,KAAQ,CAAG,IAGvE,CAYA,SAAS2T,EAAeQ,CAAI,CAAEnU,CAAI,EAChC,GAAI,CAACmU,EACH,MAAM,AAAIzN,MAAM,IAAM1G,EAAO,oBAEjC,CAYA,SAAS,EAAW4R,CAAI,CAAE5R,CAAI,EAC5B,GAAI,CAAC4R,EACH,MAAM,AAAIlL,MAAM,YAAc1G,EAAO,kCAEzC"}