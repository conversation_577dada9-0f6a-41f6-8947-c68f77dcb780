{"version": 3, "file": "static/js/async/6809.1319df31.js", "sources": ["webpack://@coze-studio/app/../../packages/data/memory/variables/src/store/variable-groups/types.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/constants.ts", "webpack://@coze-studio/app/../../packages/data/common/e2e/src/variable-e2e.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/json-import/utils/traverse.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/variables-value.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/utils/traverse.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/store/variable-groups/transform/vo2dto.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/store/variable-groups/transform/dto2vo.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/store/variable-groups/store.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/hooks/use-case/use-leave-waring.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/hooks/life-cycle/use-init.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/hooks/life-cycle/use-destory.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/context/index.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/context/variable-tree-context.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/utils.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/types/view-variable-tree.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/components/readonly-text/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/components/param-type/utils.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/components/param-type/constants.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/components/param-type/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/components/add-operation/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/components/param-operator/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/components/param-name/services/check-rules.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/components/param-name/hooks/use-cache-field.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/components/param-name/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/components/param-description/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/json-editor/utils/format-json.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/json-editor/service/convert-schema-service.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/json-editor/light.module.less?3f65", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/json-editor/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/constants.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/json-import/utils/cut-off.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/json-import/services/use-case-service/export-variable-service.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/json-import/services/life-cycle-service/init-service.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/json-import/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/components/param-default/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/components/param-channel/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/custom-tree-node/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-group/group-header/render.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-group/group-header/hooks/use-get-hide-keys.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-group/group-collapsible-wraper/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-group/index.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/service/use-case-service/submit-service.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/hooks/use-case/use-hidden-session.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/hooks/use-case/use-change-warning.ts", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/variables-config.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/index.tsx", "webpack://@coze-studio/app/../../packages/project-ide/biz-data/src/variables-main.tsx", "webpack://@coze-studio/app/../../packages/data/memory/variables/src/components/variable-tree/components/json-editor/light.module.less"], "sourcesContent": ["/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport enum VariableTypeDTO {\n  Object = 'object',\n  List = 'list',\n  String = 'string',\n  Integer = 'integer',\n  Boolean = 'boolean',\n  Float = 'float',\n}\n\nexport interface VariableSchemaDTO {\n  type: VariableTypeDTO;\n  name: string;\n  enable: boolean;\n  description: string;\n  readonly: boolean;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  schema?: any;\n}\n\n/**\n * Front-end variable type\n */\nexport enum ViewVariableType {\n  String = 1,\n  Integer,\n  Boolean,\n  Number,\n  Object = 6,\n  // The above is the InputType defined in the api. The following is the integrated one. Start from 99 to avoid collisions with the backend definition.\n  ArrayString = 99,\n  ArrayInteger,\n  ArrayBoolean,\n  ArrayNumber,\n  ArrayObject,\n}\n\nexport const BASE_ARRAY_PAIR: [ViewVariableType, ViewVariableType][] = [\n  [ViewVariableType.String, ViewVariableType.ArrayString],\n  [ViewVariableType.Integer, ViewVariableType.ArrayInteger],\n  [ViewVariableType.Boolean, ViewVariableType.ArrayBoolean],\n  [ViewVariableType.Number, ViewVariableType.ArrayNumber],\n  [ViewVariableType.Object, ViewVariableType.ArrayObject],\n];\n\nexport const VARIABLE_TYPE_ALIAS_MAP: Record<ViewVariableType, string> = {\n  [ViewVariableType.String]: 'String',\n  [ViewVariableType.Integer]: 'Integer',\n  [ViewVariableType.Boolean]: 'Boolean',\n  [ViewVariableType.Number]: 'Number',\n  [ViewVariableType.Object]: 'Object',\n  [ViewVariableType.ArrayString]: 'Array<String>',\n  [ViewVariableType.ArrayInteger]: 'Array<Integer>',\n  [ViewVariableType.ArrayBoolean]: 'Array<Boolean>',\n  [ViewVariableType.ArrayNumber]: 'Array<Number>',\n  [ViewVariableType.ArrayObject]: 'Array<Object>',\n};\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace ViewVariableType {\n  /**\n   * Get the complement of all variable types\n   * @param inputTypes\n   */\n  export function getComplement(inputTypes: ViewVariableType[]) {\n    const allTypes: ViewVariableType[] = [\n      ...BASE_ARRAY_PAIR.map(_pair => _pair[0]),\n      ...BASE_ARRAY_PAIR.map(_pair => _pair[1]),\n    ];\n\n    return allTypes.filter(type => !inputTypes.includes(type));\n  }\n\n  export function isArrayType(type: ViewVariableType): boolean {\n    const arrayTypes = BASE_ARRAY_PAIR.map(_pair => _pair[1]);\n    return arrayTypes.includes(type);\n  }\n}\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const ObjectLikeTypes = [\n  ViewVariableType.Object,\n  ViewVariableType.ArrayObject,\n];\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ViewVariableType } from '@/store';\n\nexport enum ChangeMode {\n  Update,\n  Delete,\n  Append,\n  UpdateEnabled,\n  Replace,\n}\n\n// JSON type\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const JSONLikeTypes = [\n  ViewVariableType.Object,\n  ViewVariableType.ArrayObject,\n  ViewVariableType.ArrayBoolean,\n  ViewVariableType.ArrayNumber,\n  ViewVariableType.ArrayString,\n  ViewVariableType.ArrayInteger,\n];\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport enum VariableE2e {\n  VariableTreeDeleteBtn = 'variable.tree.delete.btn',\n}\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable @typescript-eslint/no-namespace */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type TraverseValue = any;\nexport interface TraverseNode {\n  value: TraverseValue;\n  container?: TraverseValue;\n  parent?: TraverseNode;\n  key?: string;\n  index?: number;\n}\n\nexport interface TraverseContext {\n  node: TraverseNode;\n  setValue: (value: TraverseValue) => void;\n  getParents: () => TraverseNode[];\n  getPath: () => Array<string | number>;\n  getStringifyPath: () => string;\n  deleteSelf: () => void;\n}\n\nexport type TraverseHandler = (context: TraverseContext) => void;\n\n/**\n * Traverse the object in depth, processing each value\n * @param value over object\n * @param handling function\n */\nexport const traverse = <T extends TraverseValue = TraverseValue>(\n  value: T,\n  handler: TraverseHandler | TraverseHandler[],\n): T => {\n  const traverseHandler: TraverseHandler = Array.isArray(handler)\n    ? (context: TraverseContext) => {\n        handler.forEach(handlerFn => handlerFn(context));\n      }\n    : handler;\n  TraverseUtils.traverseNodes({ value }, traverseHandler);\n  return value;\n};\n\nnamespace TraverseUtils {\n  /**\n   * Traverse the object in depth, processing each value\n   * @param node traverse node\n   * @param handling function\n   */\n  export const traverseNodes = (\n    node: TraverseNode,\n    handle: TraverseHandler,\n  ): void => {\n    const { value } = node;\n    if (!value) {\n      // exception handling\n      return;\n    }\n    if (Object.prototype.toString.call(value) === '[object Object]') {\n      // Object, iterate through each property of the object\n      Object.entries(value).forEach(([key, item]) =>\n        traverseNodes(\n          {\n            value: item,\n            container: value,\n            key,\n            parent: node,\n          },\n          handle,\n        ),\n      );\n    } else if (Array.isArray(value)) {\n      // Array, iterate through each element of the array\n      // The iteration starts at the end of the array, so that even if an element is removed halfway through, it will not affect the index of the unprocessed element\n      for (let index = value.length - 1; index >= 0; index--) {\n        const item: string = value[index];\n        traverseNodes(\n          {\n            value: item,\n            container: value,\n            index,\n            parent: node,\n          },\n          handle,\n        );\n      }\n    }\n    const context: TraverseContext = createContext({ node });\n    handle(context);\n  };\n\n  const createContext = ({\n    node,\n  }: {\n    node: TraverseNode;\n  }): TraverseContext => ({\n    node,\n    setValue: (value: unknown) => setValue(node, value),\n    getParents: () => getParents(node),\n    getPath: () => getPath(node),\n    getStringifyPath: () => getStringifyPath(node),\n    deleteSelf: () => deleteSelf(node),\n  });\n\n  const setValue = (node: TraverseNode, value: unknown) => {\n    // Set Value Function\n    // Reference type, you need to modify the value with the help of the parent element\n    // Since it is a recursive traversal, it is necessary to determine which property of the object to assign a value to, or which element of the array to assign a value to, according to node\n    if (!value || !node) {\n      return;\n    }\n    node.value = value;\n    // Remove container, key, index from upper scope node\n    const { container, key, index } = node;\n    if (key && container) {\n      container[key] = value;\n    } else if (typeof index === 'number') {\n      container[index] = value;\n    }\n  };\n\n  const getParents = (node: TraverseNode): TraverseNode[] => {\n    const parents: TraverseNode[] = [];\n    let currentNode: TraverseNode | undefined = node;\n    while (currentNode) {\n      parents.unshift(currentNode);\n      currentNode = currentNode.parent;\n    }\n    return parents;\n  };\n\n  const getPath = (node: TraverseNode): Array<string | number> => {\n    const path: Array<string | number> = [];\n    const parents = getParents(node);\n    parents.forEach(parent => {\n      if (parent.key) {\n        path.unshift(parent.key);\n      } else if (parent.index) {\n        path.unshift(parent.index);\n      }\n    });\n    return path;\n  };\n\n  const getStringifyPath = (node: TraverseNode): string => {\n    const path = getPath(node);\n    return path.reduce((stringifyPath: string, pathItem: string | number) => {\n      if (typeof pathItem === 'string') {\n        const re = /\\W/g;\n        if (re.test(pathItem)) {\n          // Contains special characters\n          return `${stringifyPath}[\"${pathItem}\"]`;\n        }\n        return `${stringifyPath}.${pathItem}`;\n      } else {\n        return `${stringifyPath}[${pathItem}]`;\n      }\n    }, '');\n  };\n\n  const deleteSelf = (node: TraverseNode): void => {\n    const { container, key, index } = node;\n    if (key && container) {\n      delete container[key];\n    } else if (typeof index === 'number') {\n      container.splice(index, 1);\n    }\n  };\n}\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport dayjs from 'dayjs';\nimport classNames from 'classnames';\nimport { useRequest } from 'ahooks';\nimport { IllustrationNoContent } from '@douyinfe/semi-illustrations';\nimport { I18n } from '@coze-arch/i18n';\nimport { typeSafeJSONParse } from '@coze-arch/bot-utils';\nimport { EVENT_NAMES, sendTeaEvent } from '@coze-arch/bot-tea';\nimport { type KVItem } from '@coze-arch/bot-api/memory';\nimport { MemoryApi } from '@coze-arch/bot-api';\nimport {\n  IconCozRefresh,\n  IconCozCrossCircleFill,\n} from '@coze-arch/coze-design/icons';\nimport {\n  Table,\n  Select,\n  IconButton,\n  Tooltip,\n  Empty,\n} from '@coze-arch/coze-design';\n\nexport interface VariablesValueProps {\n  projectID: string;\n  version?: string;\n}\n\nexport function VariablesValue({ projectID, version }: VariablesValueProps) {\n  const { loading, data, refresh } = useRequest(async () => {\n    const res = await MemoryApi.GetPlayGroundMemory({\n      project_id: projectID,\n      ...(version ? { version } : {}),\n    });\n    return res.memories ?? [];\n  });\n\n  const handleClear = async (item: KVItem) => {\n    if (!item.keyword) {\n      return;\n    }\n\n    sendTeaEvent(EVENT_NAMES.memory_click_front, {\n      project_id: projectID,\n      resource_type: 'variable',\n      action: 'reset',\n      source: 'app_detail_page',\n      source_detail: 'memory_preview',\n    });\n\n    await MemoryApi.DelProfileMemory({\n      project_id: projectID,\n      keywords: [item.keyword],\n    });\n\n    refresh();\n  };\n\n  const handleReset = async () => {\n    sendTeaEvent(EVENT_NAMES.memory_click_front, {\n      project_id: projectID,\n      resource_type: 'variable',\n      action: 'reset',\n      source: 'app_detail_page',\n      source_detail: 'memory_preview',\n    });\n\n    await MemoryApi.DelProfileMemory({ project_id: projectID });\n\n    refresh();\n  };\n\n  return (\n    <div\n      className={classNames(\n        'h-full p-4',\n        '[&_.semi-table-row]:!bg-transparent',\n        '[&_.semi-table-row-head]:!bg-transparent',\n        '[&_.semi-table-row-cell]:text-[14px]',\n      )}\n    >\n      <Table\n        useHoverStyle={false}\n        empty={\n          <Empty\n            image={<IllustrationNoContent className=\"w-[140px] h-[140px]\" />}\n            title={I18n.t('variables_user_data_empty')}\n          />\n        }\n        tableProps={{\n          loading,\n          dataSource: data,\n          columns: [\n            {\n              title: I18n.t('variable_Table_Title_name'),\n              dataIndex: 'keyword',\n              width: 300,\n            },\n            {\n              title: (\n                <div className={'flex items-center'}>\n                  <span className={'mr-4px'}>\n                    {I18n.t('variable_Table_Title_value')}\n                  </span>\n                  <Tooltip\n                    theme={'dark'}\n                    content={I18n.t('variable_Button_reset_variable')}\n                  >\n                    <IconButton\n                      color={'primary'}\n                      icon={<IconCozRefresh />}\n                      size={'small'}\n                      onClick={handleReset}\n                    />\n                  </Tooltip>\n                </div>\n              ),\n              dataIndex: 'value',\n              render: (value: string, item: KVItem) => {\n                const schema = typeSafeJSONParse(item?.schema) as\n                  | { readonly?: boolean }\n                  | undefined;\n\n                if (schema?.readonly) {\n                  return value;\n                }\n\n                return (\n                  <Select\n                    className=\"w-full truncate\"\n                    value={value}\n                    showArrow={false}\n                    showClear={true}\n                    emptyContent={null}\n                    onClear={() => handleClear(item)}\n                    clearIcon={\n                      <IconButton\n                        theme={'borderless'}\n                        color={'secondary'}\n                        icon={<IconCozCrossCircleFill />}\n                        size={'large'}\n                      />\n                    }\n                  />\n                );\n              },\n            },\n            {\n              title: I18n.t('variable_Table_Title_edit_time'),\n              align: 'left',\n              dataIndex: 'update_time',\n              width: 150,\n              render: (time: number, item: KVItem) =>\n                item.value && dayjs.unix(time).format('YYYY-MM-DD HH:mm'),\n            },\n          ],\n        }}\n      />\n    </div>\n  );\n}\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// eslint-disable-next-line max-params\nexport function traverse<\n  T extends { [key in K]?: T[] },\n  K extends string = 'children',\n>(\n  nodeOrNodes: T | T[],\n  action: (node: T) => void,\n  traverseKey: K = 'children' as K,\n  maxDepth = Infinity,\n  currentDepth = 0,\n) {\n  const nodes = Array.isArray(nodeOrNodes) ? nodeOrNodes : [nodeOrNodes];\n  nodes.forEach(node => {\n    action(node);\n    if (currentDepth < maxDepth) {\n      const children = node[traverseKey] ?? [];\n      if (children?.length > 0) {\n        traverse(children, action, traverseKey, maxDepth, currentDepth + 1);\n      }\n    }\n  });\n}\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  exhaustiveCheckSimple,\n  safeAsyncThrow,\n} from '@coze-common/chat-area-utils';\nimport { type project_memory as ProjectMemory } from '@coze-arch/bot-api/memory';\n\nimport { type VariableSchemaDTO, VariableTypeDTO } from '../types';\nimport { type Variable } from '../store';\n\n/**\n * Front-end variable type\n */\nexport enum ViewVariableType {\n  String = 1,\n  Integer,\n  Boolean,\n  Number,\n  Object = 6,\n  // The above is the InputType defined in the api. The following is the integrated one. Start from 99 to avoid collisions with the backend definition.\n  ArrayString = 99,\n  ArrayInteger,\n  ArrayBoolean,\n  ArrayNumber,\n  ArrayObject,\n}\n\nexport function viewTypeToDTO(type: ViewVariableType): {\n  type: VariableTypeDTO;\n  arrayItemType?: VariableTypeDTO;\n} {\n  switch (type) {\n    case ViewVariableType.Boolean:\n      return { type: VariableTypeDTO.Boolean };\n    case ViewVariableType.Integer:\n      return { type: VariableTypeDTO.Integer };\n    case ViewVariableType.Number:\n      return { type: VariableTypeDTO.Float };\n    case ViewVariableType.String:\n      return { type: VariableTypeDTO.String };\n    case ViewVariableType.Object:\n      return { type: VariableTypeDTO.Object };\n    case ViewVariableType.ArrayBoolean:\n      return {\n        type: VariableTypeDTO.List,\n        arrayItemType: VariableTypeDTO.Boolean,\n      };\n    case ViewVariableType.ArrayInteger:\n      return {\n        type: VariableTypeDTO.List,\n        arrayItemType: VariableTypeDTO.Integer,\n      };\n    case ViewVariableType.ArrayNumber:\n      return {\n        type: VariableTypeDTO.List,\n        arrayItemType: VariableTypeDTO.Float,\n      };\n    case ViewVariableType.ArrayString:\n      return {\n        type: VariableTypeDTO.List,\n        arrayItemType: VariableTypeDTO.String,\n      };\n    case ViewVariableType.ArrayObject:\n      return {\n        type: VariableTypeDTO.List,\n        arrayItemType: VariableTypeDTO.Object,\n      };\n    default:\n      exhaustiveCheckSimple(type);\n      safeAsyncThrow(`Unknown view variable type: ${type}`);\n      return { type: VariableTypeDTO.String };\n  }\n}\n\nexport const getDtoVariable = (\n  viewVariable: Variable,\n): ProjectMemory.Variable => {\n  const { type, arrayItemType } = viewTypeToDTO(viewVariable.type);\n\n  const schema: VariableSchemaDTO = {\n    name: viewVariable.name,\n    enable: viewVariable.enabled,\n    description: viewVariable.description || '',\n    type,\n    readonly: Boolean(viewVariable.readonly),\n    schema: '',\n  };\n\n  // Working with array types\n  if (type === VariableTypeDTO.List && arrayItemType) {\n    if (arrayItemType === VariableTypeDTO.Object) {\n      schema.schema = {\n        type: VariableTypeDTO.Object,\n        schema: viewVariable.children?.map(child => {\n          const childDTO = getDtoVariable(child);\n          return JSON.parse(childDTO.Schema || '{}');\n        }),\n      };\n    } else {\n      schema.schema = {\n        type: arrayItemType,\n      };\n    }\n  }\n\n  // Handling object types\n  if (type === VariableTypeDTO.Object) {\n    schema.schema = viewVariable.children?.map(child => {\n      const childDTO = getDtoVariable(child);\n      return JSON.parse(childDTO.Schema || '{}');\n    });\n  }\n\n  return {\n    Keyword: viewVariable.name,\n    Channel: viewVariable.channel,\n    VariableType: viewVariable.variableType ?? 1,\n    DefaultValue: viewVariable.defaultValue,\n    Description: viewVariable.description,\n    EffectiveChannelList: viewVariable.effectiveChannelList,\n    Enable: Boolean(viewVariable.enabled),\n    IsReadOnly: Boolean(viewVariable.readonly),\n    Schema: JSON.stringify(schema, null, 0),\n  };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { nanoid } from 'nanoid';\nimport {\n  exhaustiveCheckSimple,\n  safeAsyncThrow,\n} from '@coze-common/chat-area-utils';\nimport { typeSafeJSONParse } from '@coze-arch/bot-utils';\nimport {\n  type project_memory as ProjectMemory,\n  VariableChannel,\n  VariableType,\n} from '@coze-arch/bot-api/memory';\n\nimport {\n  VariableTypeDTO,\n  type VariableSchemaDTO,\n  ViewVariableType,\n} from '../types';\nimport { type VariableGroup, type Variable } from '../store';\n\nexport const getGroupListByDto = (\n  dtoGroups: ProjectMemory.GroupVariableInfo[],\n): VariableGroup[] => {\n  const groups = dtoGroups?.map(group => {\n    const baseGroupInfo = getBaseGroupInfoByDto(group);\n    const { groupId } = baseGroupInfo;\n    const varInfoList = getGroupVariableListByDto({\n      group,\n      groupId,\n    });\n    return {\n      ...baseGroupInfo,\n      varInfoList,\n      subGroupList: getSubGroupListByDto({\n        group,\n        groupId,\n      }),\n    };\n  });\n  return groups || [];\n};\n\nconst getBaseGroupInfoByDto = (\n  group: Partial<ProjectMemory.GroupVariableInfo>,\n): Omit<VariableGroup, 'subGroupList' | 'varInfoList'> => {\n  const {\n    GroupName: groupName = '',\n    GroupDesc: groupDesc = '',\n    GroupExtDesc: groupExtDesc = '',\n    IsReadOnly: isReadOnly = false,\n    DefaultChannel: channel = VariableChannel.Custom,\n  } = group;\n  const groupId = nanoid();\n  return {\n    groupId,\n    groupName,\n    groupDesc,\n    groupExtDesc,\n    channel,\n    isReadOnly,\n    raw: group,\n  };\n};\n\nconst getGroupVariableListByDto = ({\n  group,\n  groupId,\n}: {\n  group: Partial<ProjectMemory.GroupVariableInfo>;\n  groupId: string;\n}): Variable[] => {\n  const { VarInfoList: varInfoList = [] } = group;\n  return (\n    varInfoList?.map(dtoVariable =>\n      getViewVariableByDto(dtoVariable, groupId),\n    ) ?? []\n  );\n};\n\nconst getSubGroupListByDto = ({\n  group,\n  groupId,\n}: {\n  group: Partial<ProjectMemory.GroupVariableInfo>;\n  groupId: string;\n}): VariableGroup[] => {\n  const { SubGroupList: subGroupList = [] } = group;\n  return (\n    subGroupList?.map(subGroup => ({\n      ...getBaseGroupInfoByDto({\n        ...subGroup,\n        DefaultChannel: group.DefaultChannel, // The subGroup returned by the server level has no DefaultChannel and needs to be set manually\n      }),\n      groupId,\n      varInfoList: getGroupVariableListByDto({\n        group: subGroup,\n        groupId,\n      }),\n      subGroupList: [],\n    })) ?? []\n  );\n};\n\nexport function getViewVariableByDto(\n  dtoVariable: ProjectMemory.Variable,\n  groupId: string,\n): Variable {\n  const variableSchema = typeSafeJSONParse(\n    dtoVariable.Schema || '{}',\n  ) as VariableSchemaDTO;\n\n  const { type } = variableSchema;\n\n  const baseVariable = createBaseVariable({\n    dtoVariable,\n    groupId,\n  });\n\n  if (type === VariableTypeDTO.List) {\n    return convertListVariable(baseVariable, variableSchema);\n  }\n\n  if (type === VariableTypeDTO.Object) {\n    return convertObjectVariable(baseVariable, variableSchema);\n  }\n\n  return {\n    ...baseVariable,\n    type: dTOTypeToViewType(variableSchema.type),\n    children: [],\n  };\n}\n\nexport function dTOTypeToViewType(\n  type: VariableTypeDTO,\n  {\n    arrayItemType,\n  }: {\n    arrayItemType?: VariableTypeDTO;\n  } = {},\n): ViewVariableType {\n  switch (type) {\n    case VariableTypeDTO.Boolean:\n      return ViewVariableType.Boolean;\n    case VariableTypeDTO.Integer:\n      return ViewVariableType.Integer;\n    case VariableTypeDTO.Float:\n      return ViewVariableType.Number;\n    case VariableTypeDTO.String:\n      return ViewVariableType.String;\n    case VariableTypeDTO.Object:\n      return ViewVariableType.Object;\n    case VariableTypeDTO.List:\n      if (!arrayItemType) {\n        throw new Error(\n          `Unkown variable DTO list need sub type but get ${arrayItemType}`,\n        );\n      }\n\n      switch (arrayItemType) {\n        case VariableTypeDTO.Boolean:\n          return ViewVariableType.ArrayBoolean;\n        case VariableTypeDTO.Integer:\n          return ViewVariableType.ArrayInteger;\n        case VariableTypeDTO.Float:\n          return ViewVariableType.ArrayNumber;\n        case VariableTypeDTO.String:\n          return ViewVariableType.ArrayString;\n        case VariableTypeDTO.Object:\n          return ViewVariableType.ArrayObject;\n        case VariableTypeDTO.List:\n          safeAsyncThrow(\n            `List type variable can't have sub list type: ${type}:${arrayItemType}`,\n          );\n          return ViewVariableType.String;\n        default:\n          exhaustiveCheckSimple(arrayItemType);\n          safeAsyncThrow(`Unknown variable DTO Type: ${type}:${arrayItemType}`);\n          return ViewVariableType.String;\n      }\n    default:\n      exhaustiveCheckSimple(type);\n      safeAsyncThrow(`Unknown variable DTO Type: ${type}:${arrayItemType}`);\n      return ViewVariableType.String;\n  }\n}\n\nfunction createBaseVariable({\n  dtoVariable,\n  groupId,\n}: {\n  dtoVariable: ProjectMemory.Variable;\n  groupId: string;\n}): Omit<Variable, 'type' | 'children'> {\n  return {\n    variableId: nanoid(),\n    name: dtoVariable.Keyword ?? '',\n    description: dtoVariable.Description ?? '',\n    enabled: dtoVariable.Enable ?? true,\n    defaultValue: dtoVariable.DefaultValue ?? '',\n    channel: dtoVariable.Channel ?? VariableChannel.Custom,\n    effectiveChannelList: dtoVariable.EffectiveChannelList ?? [],\n    variableType: dtoVariable.VariableType ?? VariableType.KVVariable,\n    readonly: dtoVariable.IsReadOnly ?? false,\n    groupId,\n    parentId: '',\n    meta: {\n      isHistory: true,\n    },\n  };\n}\n\nfunction convertListVariable(\n  baseVariable: Omit<Variable, 'type' | 'children'>,\n  variableSchema: VariableSchemaDTO,\n): Variable {\n  const subVariableSchema = variableSchema.schema as VariableSchemaDTO;\n\n  const { type: subVariableType } = subVariableSchema;\n\n  if (subVariableType === VariableTypeDTO.Object) {\n    return convertListObjectVariable(baseVariable, variableSchema);\n  }\n\n  return {\n    ...baseVariable,\n    type: dTOTypeToViewType(variableSchema.type, {\n      arrayItemType: subVariableType,\n    }),\n    children: [],\n  } as unknown as Variable;\n}\n\n/**\n *@example schema: array<object>\n{\n    \"type\": \"list\",\n    \"name\": \"arr_obj\",\n    \"schema\": {\n        \"type\": \"object\",\n        \"schema\": [{\n            \"type\": \"string\",\n            \"name\": \"name\",\n            \"required\": false\n        }, {\n            \"type\": \"integer\",\n            \"name\": \"age\",\n            \"required\": false\n        }]\n    },\n}\n*/\nfunction convertListObjectVariable(\n  baseVariable: Omit<Variable, 'type' | 'children'>,\n  variableSchema: VariableSchemaDTO,\n): Variable {\n  const subVariableSchema = variableSchema.schema;\n\n  if (!subVariableSchema) {\n    throw new Error('List object variable schema is invalid');\n  }\n\n  const { type: subVariableType } = subVariableSchema;\n\n  return {\n    ...baseVariable,\n    type: dTOTypeToViewType(VariableTypeDTO.List, {\n      arrayItemType: subVariableType,\n    }),\n    children: Array.isArray(subVariableSchema.schema)\n      ? subVariableSchema.schema.map(schema =>\n          createVariableBySchema(schema, {\n            groupId: baseVariable.groupId,\n            parentId: baseVariable.variableId,\n          }),\n        )\n      : [],\n  };\n}\n\n/**\n * @example schema: object\n * object\n{\n    \"type\": \"object\",\n    \"name\": \"obj\",\n    \"schema\": [{\n        \"type\": \"string\",\n        \"name\": \"name\",\n        \"required\": false\n    }, {\n        \"type\": \"integer\",\n        \"name\": \"age\",\n        \"required\": false\n    }],\n}\n * @returns\n */\nfunction convertObjectVariable(\n  baseVariable: Omit<Variable, 'type' | 'children'>,\n  variableSchema: VariableSchemaDTO,\n): Variable {\n  const schema = variableSchema.schema || [];\n\n  return {\n    ...baseVariable,\n    type: dTOTypeToViewType(variableSchema.type),\n    children: Array.isArray(schema)\n      ? schema.map(subMeta =>\n          createVariableBySchema(subMeta, {\n            groupId: baseVariable.groupId,\n            parentId: baseVariable.variableId,\n          }),\n        )\n      : [],\n  };\n}\nfunction createVariableBySchema(\n  subMeta: VariableSchemaDTO,\n  {\n    groupId,\n    parentId,\n  }: {\n    groupId: string;\n    parentId: string;\n  },\n): Variable {\n  return getViewVariableByDto(\n    {\n      Keyword: subMeta.name,\n      Description: subMeta.description,\n      Schema: JSON.stringify(subMeta),\n      Enable: true,\n      IsReadOnly: subMeta.readonly,\n    },\n    groupId,\n  );\n}\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable @coze-arch/max-line-per-function */\nimport { devtools, subscribeWithSelector } from 'zustand/middleware';\nimport { create } from 'zustand';\nimport { nanoid } from 'nanoid';\nimport { cloneDeep } from 'lodash-es';\nimport { produce } from 'immer';\nimport {\n  type project_memory as ProjectMemory,\n  type VariableChannel,\n  VariableType,\n} from '@coze-arch/bot-api/memory';\n\nimport { traverse } from '../../utils/traverse';\nimport { type ViewVariableType, ObjectLikeTypes } from './types';\nimport { getDtoVariable } from './transform/vo2dto';\nimport { getGroupListByDto } from './transform/dto2vo';\n\nexport interface Variable {\n  variableId: string;\n  type: ViewVariableType;\n  name: string;\n  children: Variable[];\n  defaultValue: string;\n  description: string;\n  enabled: boolean;\n  channel: VariableChannel;\n  effectiveChannelList: string[];\n  variableType: VariableType;\n  readonly: boolean;\n  groupId: string;\n  parentId: string;\n  meta: VariableMeta;\n}\n\nexport interface VariableMeta {\n  isHistory: boolean;\n  level?: number;\n  hasObjectLike?: boolean;\n  field?: string;\n}\n\nexport interface VariableGroup {\n  groupId: string;\n  groupName: string;\n  groupDesc: string;\n  groupExtDesc: string;\n  isReadOnly: boolean;\n  channel: VariableChannel;\n  subGroupList: VariableGroup[];\n  varInfoList: Variable[];\n  raw: ProjectMemory.GroupVariableInfo;\n}\n\nexport interface VariableGroupsStore {\n  variableGroups: VariableGroup[];\n  canEdit: boolean;\n}\n\nexport const getDefaultVariableGroupStore = (): VariableGroupsStore => ({\n  canEdit: false,\n  variableGroups: [],\n});\n\nexport interface VariableGroupsAction {\n  setVariableGroups: (variableGroups: VariableGroup[]) => void;\n  createVariable: (variableInfo: {\n    variableType: ViewVariableType;\n    groupId: string;\n    parentId: string;\n    channel: VariableChannel;\n  }) => Variable;\n  // Update variables, according to groupId and variableId\n  updateVariable: (newVariable: Variable) => void;\n  // Update the meta information of the variable\n  updateMeta: (params: {\n    variables: Variable[];\n    level?: number;\n    parentId?: string;\n  }) => void;\n  // Add root node variable\n  addRootVariable: (variable: Omit<Variable, 'channel'>) => void;\n  // Add sub-node variable\n  addChildVariable: (variable: Variable) => void;\n  // Delete variable\n  deleteVariable: (variable: Variable) => void;\n  // After being preserved, it is treated as a historical variable\n  saveHistory: () => void;\n  // Get DTO variable\n  getDtoVariable: (variable: Variable) => ProjectMemory.Variable;\n  // Get all the variables under groups\n  getAllRootVariables: () => Variable[];\n  // Get all the variables under groups\n  getAllVariables: () => Variable[];\n  transformDto2Vo: (data: ProjectMemory.GroupVariableInfo[]) => VariableGroup[];\n  initStore: (data: {\n    variableGroups: ProjectMemory.GroupVariableInfo[];\n    canEdit: boolean;\n  }) => void;\n  clear: () => void;\n  // Locate variables in the variable tree and optionally modify or delete them\n  findAndModifyVariable: (\n    groupId: string,\n    predicate: (variable: Variable) => boolean,\n    options?: {\n      modifyVariable?: (variable: Variable) => void;\n      removeVariable?: boolean;\n      mark?: string;\n    },\n  ) => Variable | null;\n}\n\nexport const useVariableGroupsStore = create<\n  VariableGroupsStore & VariableGroupsAction\n>()(\n  devtools(\n    subscribeWithSelector((set, get) => ({\n      ...getDefaultVariableGroupStore(),\n      setVariableGroups: variableGroups =>\n        set({ variableGroups }, false, 'setVariableGroups'),\n      createVariable: baseInfo => ({\n        variableId: nanoid(),\n        type: baseInfo.variableType,\n        name: '',\n        enabled: true,\n        children: [],\n        defaultValue: '',\n        description: '',\n        channel: baseInfo.channel,\n        effectiveChannelList: [],\n        variableType: VariableType.KVVariable,\n        readonly: false,\n        groupId: baseInfo.groupId,\n        parentId: baseInfo.parentId,\n        meta: {\n          isHistory: false,\n        },\n      }),\n      addRootVariable: variable => {\n        set(\n          produce<VariableGroupsStore>(state => {\n            const findGroup = state.variableGroups.find(\n              item => item.groupId === variable.groupId,\n            );\n            if (!findGroup) {\n              return;\n            }\n            findGroup.varInfoList.push({\n              ...variable,\n              channel: findGroup.channel,\n            });\n            get().updateMeta({\n              variables: findGroup.varInfoList,\n              level: 0,\n              parentId: '',\n            });\n          }),\n          false,\n          'addRootVariable',\n        );\n      },\n      addChildVariable: variable => {\n        get().findAndModifyVariable(\n          variable.groupId,\n          item => item.variableId === variable.parentId,\n          {\n            modifyVariable: parentNode => {\n              parentNode.children.push(variable);\n              get().updateMeta({\n                variables: parentNode.children,\n                level: (parentNode.meta.level || 0) + 1,\n                parentId: parentNode.variableId,\n              });\n            },\n            mark: 'addChildVariable',\n          },\n        );\n      },\n      deleteVariable: variable => {\n        get().findAndModifyVariable(\n          variable.groupId,\n          item => item.variableId === variable.variableId,\n          { removeVariable: true, mark: 'deleteVariable' },\n        );\n      },\n      findAndModifyVariable: (groupId, predicate, options) => {\n        let foundVariable: Variable | null = null;\n\n        set(\n          produce<VariableGroupsStore>(state => {\n            const findInGroups = (groups: VariableGroup[]): boolean => {\n              for (const group of groups) {\n                if (group.groupId === groupId) {\n                  if (findInTree(group.varInfoList, predicate, options)) {\n                    return true;\n                  }\n                }\n                if (group.subGroupList?.length) {\n                  if (findInGroups(group.subGroupList)) {\n                    return true;\n                  }\n                }\n              }\n              return false;\n            };\n\n            const findInTree = (\n              variables: Variable[],\n              predicateIn: (variable: Variable) => boolean,\n              optionsIn?: {\n                modifyVariable?: (variable: Variable) => void;\n                removeVariable?: boolean;\n              },\n            ): boolean => {\n              for (let i = 0; i < variables.length; i++) {\n                if (predicateIn(variables[i])) {\n                  foundVariable = cloneDeep(variables[i]);\n                  if (optionsIn?.removeVariable) {\n                    variables.splice(i, 1);\n                  }\n                  if (optionsIn?.modifyVariable) {\n                    optionsIn.modifyVariable(variables[i]);\n                  }\n                  return true;\n                }\n                if (variables[i].children?.length) {\n                  if (\n                    findInTree(variables[i].children, predicateIn, optionsIn)\n                  ) {\n                    return true;\n                  }\n                }\n              }\n              return false;\n            };\n\n            findInGroups(state.variableGroups);\n          }),\n          false,\n          options?.mark || 'findVariableInTree',\n        );\n\n        return foundVariable;\n      },\n      updateVariable: newVariable => {\n        get().findAndModifyVariable(\n          newVariable.groupId,\n          variable => variable.variableId === newVariable.variableId,\n          {\n            mark: 'updateVariable',\n            modifyVariable: variable => {\n              Object.assign(variable, newVariable);\n              get().updateMeta({\n                variables: [variable],\n                level: variable.meta.level,\n                parentId: variable.parentId,\n              });\n            },\n          },\n        );\n      },\n      updateMeta: ({ variables, level = 0, parentId = '' }) => {\n        variables.forEach(item => {\n          item.meta.level = level;\n          item.meta.hasObjectLike = ObjectLikeTypes.includes(item.type);\n          item.parentId = parentId;\n          if (item.children?.length) {\n            get().updateMeta({\n              variables: item.children,\n              level: level + 1,\n              parentId: item.variableId,\n            });\n          }\n        });\n      },\n      saveHistory: () => {\n        set(\n          produce<VariableGroupsStore>(state => {\n            state.variableGroups.forEach(item => {\n              traverse(item.varInfoList, itemIn => {\n                itemIn.meta.isHistory = true;\n              });\n            });\n          }),\n          false,\n          'saveHistory',\n        );\n      },\n      getAllRootVariables: () => {\n        const { variableGroups } = get();\n        const res: Variable[] = [];\n        traverse(\n          variableGroups,\n          item => {\n            res.push(...item.varInfoList);\n          },\n          'subGroupList',\n        );\n        return res;\n      },\n      getAllVariables: () => {\n        const { variableGroups } = get();\n        const variables = variableGroups.map(item => item.varInfoList).flat();\n        const res: Variable[] = [];\n        traverse(\n          variables,\n          item => {\n            res.push(item);\n          },\n          'children',\n        );\n        return res;\n      },\n      transformDto2Vo: data => {\n        const transformedData = getGroupListByDto(data);\n        // After the data conversion is completed, update the meta information immediately\n        transformedData.forEach(group => {\n          get().updateMeta({ variables: group.varInfoList });\n        });\n        return transformedData;\n      },\n      getDtoVariable: (variable: Variable) => getDtoVariable(variable),\n      initStore: data => {\n        const { transformDto2Vo } = get();\n        const transformedData = transformDto2Vo(data.variableGroups);\n        set(\n          {\n            variableGroups: transformedData,\n            canEdit: data.canEdit,\n          },\n          false,\n          'initStore',\n        );\n      },\n      clear: () => {\n        set({ ...getDefaultVariableGroupStore() }, false, 'clear');\n      },\n    })),\n    {\n      enabled: IS_DEV_MODE,\n      name: 'knowledge.variableGroups',\n    },\n  ),\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useLocation } from 'react-router-dom';\nimport { useEffect, useRef, useState } from 'react';\n\nimport { useDataNavigate } from '@coze-data/knowledge-stores';\nimport { I18n } from '@coze-arch/i18n';\nimport { Button, Toast } from '@coze-arch/coze-design';\n\nexport const useLeaveWarning = () => {\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const location = useLocation();\n  const prevPathRef = useRef(location.pathname);\n  const resourceNavigate = useDataNavigate();\n\n  useEffect(() => {\n    const currentPath = location.pathname;\n    const wasInVariablePage = prevPathRef.current.includes('/variables');\n\n    const handleBeforeUnload = (e: BeforeUnloadEvent) => {\n      if (hasUnsavedChanges) {\n        e.preventDefault();\n      }\n    };\n\n    if (\n      wasInVariablePage &&\n      !currentPath.includes('/variables') &&\n      hasUnsavedChanges\n    ) {\n      Toast.warning({\n        content: (\n          <div>\n            <span className=\"text-sm font-medium coz-fg-plus mr-2\">\n              {I18n.t('variable_config_toast_savetips')}\n            </span>\n            <Button\n              color=\"primary\"\n              onClick={() => {\n                resourceNavigate.navigateTo?.('/variables');\n              }}\n            >\n              {I18n.t('variable_config_toast_return_button')}\n            </Button>\n          </div>\n        ),\n      });\n    }\n\n    if (currentPath.includes('/variables') && hasUnsavedChanges) {\n      window.addEventListener('beforeunload', handleBeforeUnload);\n    }\n\n    prevPathRef.current = currentPath;\n\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, [location, hasUnsavedChanges]);\n\n  return {\n    hasUnsavedChanges,\n    setHasUnsavedChanges,\n  };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useEffect } from 'react';\n\nimport { useRequest } from 'ahooks';\nimport { I18n } from '@coze-arch/i18n';\nimport { CustomError } from '@coze-arch/bot-error';\nimport { type project_memory as ProjectMemory } from '@coze-arch/bot-api/memory';\nimport { MemoryApi } from '@coze-arch/bot-api';\nimport { Toast } from '@coze-arch/coze-design';\n\nimport { useVariableGroupsStore } from '../../store';\n\nexport const useInit = (projectID?: string, version?: string) => {\n  const { data: reqData, loading } = useGetVariableList(projectID, version);\n  const { initStore } = useVariableGroupsStore();\n\n  useEffect(() => {\n    if (loading) {\n      return;\n    }\n\n    const { variableGroups, canEdit } = reqData;\n\n    initStore({\n      variableGroups,\n      canEdit: canEdit && !version,\n    });\n  }, [loading]);\n\n  return {\n    loading,\n  };\n};\n\nconst useGetVariableList = (\n  projectID?: string,\n  version?: string,\n): {\n  data: {\n    variableGroups: ProjectMemory.GroupVariableInfo[];\n    canEdit: boolean;\n  };\n  loading: boolean;\n  error: string;\n} => {\n  const {\n    data: reqData,\n    loading,\n    error,\n  } = useRequest(\n    async () => {\n      if (!projectID) {\n        throw new CustomError(\n          'useListDataSetReq_error',\n          'projectID cannot be empty',\n        );\n      }\n      const res = await MemoryApi.GetProjectVariableList({\n        ProjectID: projectID,\n        version: version || undefined,\n      });\n\n      const { GroupConf, code, CanEdit: canEdit, msg } = res;\n\n      if (code !== 0) {\n        return {\n          error: msg,\n          data: {\n            variableGroups: [],\n            canEdit: false,\n          },\n          loading: false,\n        };\n      }\n\n      if (!GroupConf) {\n        return {\n          data: {\n            variableGroups: [],\n            canEdit,\n          },\n          loading: false,\n        };\n      }\n\n      return {\n        variableGroups: GroupConf,\n        canEdit,\n      };\n    },\n    {\n      manual: false,\n      onError: () => {\n        Toast.error({\n          content: I18n.t('Network_error'),\n          showClose: false,\n        });\n      },\n    },\n  );\n  return {\n    data: {\n      variableGroups: reqData?.variableGroups ?? [],\n      canEdit: reqData?.canEdit ?? false,\n    },\n    loading,\n    error: error?.message ?? '',\n  };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useEffect } from 'react';\n\nimport { useVariableGroupsStore } from '../../store';\n\nexport const useDestory = () => {\n  const { clear } = useVariableGroupsStore();\n  useEffect(\n    () => () => {\n      clear();\n    },\n    [clear],\n  );\n  return {\n    clear,\n  };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createContext, useContext } from 'react';\n\nimport { type VariableGroup } from '../store';\n\ninterface VariableContextType {\n  variablePageCanEdit?: boolean;\n  groups: VariableGroup[];\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const VariableContext = createContext<VariableContextType>({\n  groups: [],\n});\n\nexport const useVariableContext = () => useContext(VariableContext);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createContext, useContext } from 'react';\n\nimport { type Variable } from '../store';\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const VariableTreeContext = createContext<{\n  groupId: string;\n  variables: Variable[];\n}>({\n  groupId: '',\n  variables: [],\n});\n\nexport const useVariableTreeContext = () => useContext(VariableTreeContext);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { traverse } from '@/utils/traverse';\nimport { type Variable, type VariableGroup } from '@/store';\n\nimport { type TreeNodeCustomData } from './type';\n\ninterface RootFindResult {\n  isRoot: true;\n  data: TreeNodeCustomData;\n  parentData: null;\n}\ninterface ChildrenFindResult {\n  isRoot: false;\n  parentData: TreeNodeCustomData;\n  data: TreeNodeCustomData;\n}\n\nexport type FindDataResult = RootFindResult | ChildrenFindResult | null;\n/**\n * According to the target array, find the value and position of the key in the item, mainly to obtain the position, which is convenient for operating the children of the parent.\n */\nexport function findCustomTreeNodeDataResult(\n  target: Array<TreeNodeCustomData>,\n  variableId: string,\n): FindDataResult {\n  const dataInRoot = target.find(item => item.variableId === variableId);\n  if (dataInRoot) {\n    // If it is the root node\n    return {\n      isRoot: true,\n      parentData: null,\n      data: dataInRoot,\n    };\n  }\n  function findDataInChildrenLoop(\n    customChildren: Array<TreeNodeCustomData>,\n    parentData?: TreeNodeCustomData,\n  ): FindDataResult {\n    function findDataLoop(\n      customData: TreeNodeCustomData,\n      _parentData: TreeNodeCustomData,\n    ): FindDataResult {\n      if (customData.variableId === variableId) {\n        return {\n          isRoot: false,\n          parentData: _parentData,\n          data: customData,\n        };\n      }\n      if (customData.children && customData.children.length > 0) {\n        return findDataInChildrenLoop(\n          customData.children as Array<TreeNodeCustomData>,\n          customData,\n        );\n      }\n      return null;\n    }\n    for (const child of customChildren) {\n      const childResult = findDataLoop(child, parentData || child);\n      if (childResult) {\n        return childResult;\n      }\n    }\n    return null;\n  }\n  return findDataInChildrenLoop(target);\n}\n\n// Flatten groupVariableMeta to viewVariableTreeNode []\nexport function flatGroupVariableMeta(\n  groupVariableMeta: VariableGroup[],\n  maxDepth = Infinity,\n) {\n  const res: Variable[] = [];\n  traverse(\n    groupVariableMeta,\n    item => {\n      res.push(...item.varInfoList);\n    },\n    'subGroupList',\n    maxDepth,\n  );\n  return res;\n}\nexport const flatVariableTreeData = (treeData: Variable[]) => {\n  const res: Variable[] = [];\n  traverse(\n    treeData,\n    item => {\n      res.push(item);\n    },\n    'children',\n  );\n  return res;\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type CSSProperties } from 'react';\n\nimport { ViewVariableType } from '../store';\n\nexport type WithCustomStyle<T = object> = {\n  className?: string;\n  style?: CSSProperties;\n} & T;\n\nexport const VARIABLE_TYPE_ALIAS_MAP: Record<ViewVariableType, string> = {\n  [ViewVariableType.String]: 'String',\n  [ViewVariableType.Integer]: 'Integer',\n  [ViewVariableType.Boolean]: 'Boolean',\n  [ViewVariableType.Number]: 'Number',\n  [ViewVariableType.Object]: 'Object',\n  [ViewVariableType.ArrayString]: 'Array<String>',\n  [ViewVariableType.ArrayInteger]: 'Array<Integer>',\n  [ViewVariableType.ArrayBoolean]: 'Array<Boolean>',\n  [ViewVariableType.ArrayNumber]: 'Array<Number>',\n  [ViewVariableType.ArrayObject]: 'Array<Object>',\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport classNames from 'classnames';\nimport { Typography } from '@coze-arch/coze-design';\n\nconst { Text } = Typography;\n\nexport const ReadonlyText = (props: { value: string; className?: string }) => {\n  const { value, className } = props;\n  return (\n    <Text\n      className={classNames(\n        'w-full coz-fg-primary text-sm !font-medium',\n        className,\n      )}\n      ellipsis\n    >\n      {value}\n    </Text>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type ReactNode } from 'react';\n\nimport { VARIABLE_TYPE_ALIAS_MAP } from '@/types/view-variable-tree';\nimport { ObjectLikeTypes } from '@/store/variable-groups/types';\nimport { ViewVariableType } from '@/store';\n\nconst LEVEL_LIMIT = 3;\n\nexport const generateVariableOption = (\n  type: ViewVariableType,\n  label?: string,\n  display?: string,\n) => ({\n  value: Number(type),\n  label: label || VARIABLE_TYPE_ALIAS_MAP[type],\n  display: display || label || VARIABLE_TYPE_ALIAS_MAP[type],\n});\n\nexport interface VariableTypeOption {\n  // Value of type, possibly empty when not a leaf node\n  value: number | string;\n  // The display name of the option\n  label: ReactNode;\n  // Echoed display name\n  display?: string;\n  // Is the type disabled?\n  disabled?: boolean;\n  // subtype\n  children?: VariableTypeOption[];\n}\n\nexport const allVariableTypeList: Array<VariableTypeOption> = [\n  generateVariableOption(ViewVariableType.String),\n  generateVariableOption(ViewVariableType.Integer),\n  generateVariableOption(ViewVariableType.Boolean),\n  generateVariableOption(ViewVariableType.Number),\n  generateVariableOption(ViewVariableType.Object),\n  generateVariableOption(ViewVariableType.ArrayString),\n  generateVariableOption(ViewVariableType.ArrayInteger),\n  generateVariableOption(ViewVariableType.ArrayBoolean),\n  generateVariableOption(ViewVariableType.ArrayNumber),\n  generateVariableOption(ViewVariableType.ArrayObject),\n];\n\nconst filterTypes = (\n  list: Array<VariableTypeOption>,\n  options?: VariableListOptions,\n): Array<VariableTypeOption> => {\n  const { level } = options || {};\n\n  return list.reduce((pre, cur) => {\n    const newOption = { ...cur };\n\n    if (newOption.children) {\n      newOption.children = filterTypes(newOption.children, options);\n    }\n\n    /**\n     * 1. Disable the ObjectLike type when reaching the level limit to avoid too deep nesting\n     */\n    const disabled = Boolean(\n      level &&\n        level >= LEVEL_LIMIT &&\n        ObjectLikeTypes.includes(Number(newOption.value)),\n    );\n\n    return [\n      ...pre,\n      {\n        ...newOption,\n        disabled,\n      },\n    ];\n  }, [] as Array<VariableTypeOption>);\n};\n\ninterface VariableListOptions {\n  level?: number;\n}\n\nexport const getVariableTypeList = options =>\n  filterTypes(allVariableTypeList, options);\n\n/**\n * Get the path of the type in the options list as the cascader value\n */\nexport const getCascaderVal = (\n  originalVal: ViewVariableType,\n  list: Array<VariableTypeOption>,\n  path: Array<string | number> = [],\n) => {\n  let valuePath = [...path];\n  list.forEach(item => {\n    if (item.children) {\n      const childPath = getCascaderVal(originalVal, item.children, [\n        ...valuePath,\n        item.value,\n      ]);\n      if (childPath[childPath.length - 1] === originalVal) {\n        valuePath = childPath;\n        return;\n      }\n    } else if (item.value === originalVal) {\n      valuePath.push(originalVal);\n      return;\n    }\n  });\n\n  return valuePath;\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type ReactNode } from 'react';\n\nimport {\n  IconCozNumber,\n  IconCozNumberBracket,\n  IconCozString,\n  IconCozStringBracket,\n  IconCozBoolean,\n  IconCozBooleanBracket,\n  IconCozBrace,\n  IconCozBraceBracket,\n} from '@coze-arch/coze-design/icons';\n\nimport { ViewVariableType } from '@/store';\n\nexport const VARIABLE_TYPE_ICONS_MAP: Record<ViewVariableType, ReactNode> = {\n  [ViewVariableType.String]: <IconCozString />,\n  [ViewVariableType.Integer]: <IconCozNumber />,\n  [ViewVariableType.Boolean]: <IconCozBoolean />,\n  [ViewVariableType.Number]: <IconCozNumber />,\n  [ViewVariableType.Object]: <IconCozBrace />,\n  [ViewVariableType.ArrayString]: <IconCozStringBracket />,\n  [ViewVariableType.ArrayInteger]: <IconCozNumberBracket />,\n  [ViewVariableType.ArrayBoolean]: <IconCozBooleanBracket />,\n  [ViewVariableType.ArrayNumber]: <IconCozNumberBracket />,\n  [ViewVariableType.ArrayObject]: <IconCozBraceBracket />,\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable @coze-arch/no-deep-relative-import */\nimport React, { useMemo } from 'react';\n\nimport { I18n } from '@coze-arch/i18n';\nimport { type SelectProps } from '@coze-arch/bot-semi/Select';\nimport { Cascader } from '@coze-arch/coze-design';\n\nimport { VARIABLE_TYPE_ALIAS_MAP } from '@/types/view-variable-tree';\n\nimport { ReadonlyText } from '../readonly-text';\nimport { type TreeNodeCustomData } from '../../../../type';\nimport {\n  getVariableTypeList,\n  getCascaderVal,\n  allVariableTypeList,\n} from './utils';\nimport { VARIABLE_TYPE_ICONS_MAP } from './constants';\n\ninterface ParamTypeProps {\n  data: TreeNodeCustomData;\n  level: number;\n  onSelectChange?: SelectProps['onChange'];\n  readonly?: boolean;\n}\n\nexport default function ParamType({\n  data,\n  onSelectChange,\n  level,\n  readonly,\n}: ParamTypeProps) {\n  const optionList = useMemo(() => getVariableTypeList({ level }), [level]);\n\n  const cascaderVal = useMemo(\n    () => getCascaderVal(data.type, allVariableTypeList),\n    [data.type],\n  );\n\n  return readonly ? (\n    <ReadonlyText\n      className=\"w-full\"\n      value={VARIABLE_TYPE_ALIAS_MAP[data.type]}\n    />\n  ) : (\n    <Cascader\n      placeholder={I18n.t('workflow_detail_start_variable_type')}\n      disabled={readonly}\n      onChange={val => {\n        let newVal = val;\n        if (Array.isArray(val)) {\n          newVal = val[val.length - 1];\n        }\n        onSelectChange?.(newVal);\n      }}\n      className=\"w-full coz-stroke-plus\"\n      displayProp=\"value\"\n      displayRender={selected => {\n        if (!Array.isArray(selected)) {\n          return null;\n        }\n\n        return (\n          <div className=\"flex items-center gap-1 text-xs\">\n            {VARIABLE_TYPE_ICONS_MAP[selected[selected.length - 1]]}\n            <div className=\"truncate\">\n              {VARIABLE_TYPE_ALIAS_MAP[selected[selected.length - 1]]}\n            </div>\n          </div>\n        );\n      }}\n      treeData={optionList}\n      value={cascaderVal}\n    />\n  );\n}\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React from 'react';\n\nimport { IconAdd } from '@coze-arch/bot-icons';\nimport { IconCozAddNode } from '@coze-arch/coze-design/icons';\nimport { IconButton, type ButtonProps } from '@coze-arch/coze-design';\n\ntype AddOperationProps = React.PropsWithChildren<{\n  readonly?: boolean;\n  onClick: React.MouseEventHandler<HTMLButtonElement>;\n  className?: string;\n  style?: React.CSSProperties;\n  disabled?: boolean;\n  subitem?: boolean;\n  size?: ButtonProps['size'];\n  color?: ButtonProps['color'];\n}>;\n\nexport default function AddOperation({\n  readonly,\n  onClick,\n  className,\n  style,\n  disabled,\n  subitem = false,\n  size,\n  color,\n  ...restProps\n}: AddOperationProps) {\n  if (readonly) {\n    return null;\n  }\n\n  return (\n    <IconButton\n      data-testid={restProps['data-testid']}\n      onClick={onClick}\n      className={`${\n        disabled ? 'disabled:text-[rgb(28,31,35,0.35)]' : 'text-[#4d53e8]'\n      } ${className}`}\n      style={style}\n      icon={\n        subitem ? (\n          <IconCozAddNode />\n        ) : (\n          <IconAdd className=\"text-[#4d53e8] disabled:text-[rgb(28,31,35,0.35)]\" />\n        )\n      }\n      disabled={disabled}\n      size={size}\n      color={color}\n    />\n  );\n}\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable @coze-arch/no-deep-relative-import */\nimport { VariableE2e } from '@coze-data/e2e';\nimport { I18n } from '@coze-arch/i18n';\nimport { IconCozTrashCan } from '@coze-arch/coze-design/icons';\nimport { Tooltip, IconButton, Switch } from '@coze-arch/coze-design';\n\nimport { ObjectLikeTypes } from '@/store/variable-groups/types';\nimport { useVariableContext } from '@/context';\n\nimport AddOperation from '../add-operation';\nimport { type TreeNodeCustomData } from '../../../../type';\n\ninterface ParamOperatorProps {\n  data: TreeNodeCustomData;\n  level: number;\n  onAppend: () => void;\n  onDelete: () => void;\n  onEnabledChange: (enabled: boolean) => void;\n  hasObjectLike?: boolean;\n  needRenderAppendChild?: boolean;\n  readonly?: boolean;\n}\n\nexport default function ParamOperator({\n  level,\n  data,\n  hasObjectLike,\n  readonly,\n  needRenderAppendChild = true,\n  onEnabledChange,\n  onDelete,\n  onAppend,\n}: ParamOperatorProps) {\n  const isLimited = level >= 3;\n\n  // Is it possible to add children?\n  const canAddChild = !readonly && ObjectLikeTypes.includes(data.type);\n  // Is the child button available?\n  const enableAddChildButton =\n    !readonly && hasObjectLike && canAddChild && needRenderAppendChild;\n  // Whether to display the delete button\n  const showDeleteButton = !readonly;\n  // Whether to display the on/off button\n  const enabledSwitch = level === 0;\n\n  const { variablePageCanEdit } = useVariableContext();\n\n  return (\n    <div className=\"flex items-center h-[24px] flex-shrink-0 justify-start gap-x-2 w-[130px]\">\n      {/* Open/close */}\n      <Switch\n        size=\"small\"\n        disabled={!variablePageCanEdit || !enabledSwitch}\n        checked={data.enabled}\n        onChange={onEnabledChange}\n      />\n      {/* Add child item */}\n      {needRenderAppendChild ? (\n        <div className=\"flex items-center justify-center\">\n          <Tooltip\n            content={I18n.t('workflow_detail_node_output_add_subitem')}\n            theme=\"dark\"\n          >\n            <div>\n              <AddOperation\n                color=\"secondary\"\n                disabled={isLimited || !enableAddChildButton}\n                className=\"cursor-pointer\"\n                onClick={onAppend}\n                subitem={true}\n              />\n            </div>\n          </Tooltip>\n        </div>\n      ) : null}\n      {/* delete */}\n      <IconButton\n        data-testid={VariableE2e.VariableTreeDeleteBtn}\n        color=\"secondary\"\n        onClick={onDelete}\n        disabled={!showDeleteButton}\n        icon={<IconCozTrashCan />}\n      />\n    </div>\n  );\n}\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { I18n } from '@coze-arch/i18n';\nimport { VariableChannel } from '@coze-arch/bot-api/memory';\n\nimport { type Variable, type VariableGroup } from '@/store';\n\nexport const requiredRules = {\n  validate: (value: Variable) => !!value.name,\n  message: I18n.t('bot_edit_variable_field_required_error'),\n};\n\n/**\n * Check if variable names are duplicate\n * 1. Check whether the variable names are duplicated in the same group & level\n * 2. Check whether the variable names are duplicated in the root node names of different groups\n */\nexport const duplicateRules = {\n  validate: (value: Variable, groups: VariableGroup[]): boolean => {\n    if (!value.name) {\n      return true;\n    } // Skip check if name is empty\n\n    // 1. Check whether the same group and level are duplicated\n    const currentGroup = groups.find(group => group.groupId === value.groupId);\n\n    if (!currentGroup) {\n      return true;\n    }\n\n    // Get all nodes at the same level as the current node (including those nested in other node children)\n    const findSiblings = (\n      variables: Variable[],\n      targetParentId: string | null,\n    ): Variable[] => {\n      let result: Variable[] = [];\n\n      for (const variable of variables) {\n        // If the parentId of the current variable is the same as the target parentId and is not itself, it is added to the result\n        if (\n          variable.parentId === targetParentId &&\n          variable.variableId !== value.variableId\n        ) {\n          result.push(variable);\n        }\n        // Check children recursively\n        if (variable.children?.length) {\n          result = result.concat(\n            findSiblings(variable.children, targetParentId),\n          );\n        }\n      }\n\n      return result;\n    };\n\n    const siblings = findSiblings(currentGroup.varInfoList, value.parentId);\n\n    if (siblings.some(sibling => sibling.name === value.name)) {\n      return false;\n    }\n\n    // 2. Check if it has the same name as the root node of other groups\n    // Check only if the current node is the root node\n    if (!value.parentId) {\n      const otherGroupsRootNodes = groups\n        .filter(group => group.groupId !== value.groupId)\n        .flatMap(group => {\n          const rootVariableList = group.varInfoList;\n          const subGroupVarInfoList = group.subGroupList.flatMap(\n            subGroup => subGroup.varInfoList,\n          );\n          return rootVariableList.concat(subGroupVarInfoList);\n        });\n\n      if (otherGroupsRootNodes.some(node => node.name === value.name)) {\n        return false;\n      }\n    }\n\n    return true;\n  },\n  message: I18n.t('workflow_detail_node_error_variablename_duplicated'),\n};\n\nexport const existKeywordRules = {\n  validate: (value: Variable) =>\n    /^(?!.*\\b(true|false|and|AND|or|OR|not|NOT|null|nil|If|Switch)\\b)[a-zA-Z_][a-zA-Z_$0-9]*$/.test(\n      value.name,\n    ),\n  message: I18n.t('variables_app_name_limit'),\n};\n\nexport const checkParamNameRules = (\n  value: Variable,\n  groups: VariableGroup[],\n  validateExistKeyword: boolean,\n):\n  | {\n      valid: boolean;\n      message: string;\n    }\n  | undefined => {\n  if (!requiredRules.validate(value)) {\n    return {\n      valid: false,\n      message: requiredRules.message,\n    };\n  }\n  if (!duplicateRules.validate(value, groups)) {\n    return {\n      valid: false,\n      message: duplicateRules.message,\n    };\n  }\n  if (\n    validateExistKeyword &&\n    !existKeywordRules.validate(value) &&\n    value.channel === VariableChannel.APP\n  ) {\n    return {\n      valid: false,\n      message: existKeywordRules.message,\n    };\n  }\n  return {\n    valid: true,\n    message: '',\n  };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useEffect, useRef } from 'react';\n\nimport { useFormApi } from '@coze-arch/coze-design';\n\nimport { type Variable } from '@/store';\n\nexport const useCacheField = (data: Variable) => {\n  const formApi = useFormApi();\n\n  const lastValidValueRef = useRef(data.name);\n\n  useEffect(() => {\n    const currentValue = formApi.getValue(`${data.variableId}.name`);\n    if (currentValue) {\n      lastValidValueRef.current = currentValue;\n    } else if (lastValidValueRef.current) {\n      formApi.setValue(`${data.variableId}.name`, lastValidValueRef.current);\n    }\n  }, [data.variableId]);\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport cls from 'classnames';\nimport { I18n } from '@coze-arch/i18n';\nimport { FormInput, useFormApi } from '@coze-arch/coze-design';\n\nimport { type Variable } from '@/store';\nimport { useVariableContext } from '@/context';\n\nimport { ReadonlyText } from '../readonly-text';\nimport {\n  requiredRules,\n  duplicateRules,\n  existKeywordRules,\n} from './services/check-rules';\nimport { useCacheField } from './hooks/use-cache-field';\n\nexport const ParamName = (props: {\n  data: Variable;\n  readonly: boolean;\n  onChange: (value: string) => void;\n  validateExistKeyword?: boolean;\n}) => {\n  const { data, onChange, readonly, validateExistKeyword = false } = props;\n  const { groups } = useVariableContext();\n  const formApi = useFormApi();\n\n  // Use ref to cache the last valid value. When the Tree component is hidden, the component will be destroyed, and the Field field of the Form will be deleted, so it needs to be cached.\n  useCacheField(data);\n\n  return (\n    <div\n      className={cls(\n        'w-full overflow-hidden',\n        '[&_.semi-form-field-error-message]:absolute',\n        '[&_.semi-form-field-error-message]:text-[12px]',\n        '[&_.semi-form-field-error-message]:font-[400]',\n        '[&_.semi-form-field-error-message]:leading-[16px]',\n      )}\n    >\n      {!readonly ? (\n        <>\n          <FormInput\n            field={`${data.variableId}.name`}\n            placeholder={I18n.t('variable_name_placeholder')}\n            maxLength={50}\n            autoFocus={!data.name}\n            noLabel\n            rules={[\n              {\n                validator: (_, value) =>\n                  requiredRules.validate({\n                    ...data,\n                    name: value,\n                  }),\n                message: requiredRules.message,\n              },\n              {\n                validator: (_, value) =>\n                  validateExistKeyword\n                    ? existKeywordRules.validate({\n                        ...data,\n                        name: value,\n                      })\n                    : true,\n                message: existKeywordRules.message,\n              },\n              {\n                validator: (_, value) =>\n                  duplicateRules.validate(\n                    {\n                      ...data,\n                      name: value,\n                    },\n                    groups,\n                  ),\n                message: duplicateRules.message,\n              },\n            ]}\n            onChange={value => {\n              onChange(value);\n              formApi.setValue(`${data.variableId}.name`, value);\n            }}\n            className=\"w-full truncate\"\n          />\n        </>\n      ) : (\n        <ReadonlyText value={data.name} />\n      )}\n    </div>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { I18n } from '@coze-arch/i18n';\nimport { Input } from '@coze-arch/coze-design';\n\nimport { type Variable } from '@/store';\n\nimport { ReadonlyText } from '../readonly-text';\n\nexport const ParamDescription = (props: {\n  data: Variable;\n  onChange: (value: string) => void;\n  readonly: boolean;\n}) => {\n  const { data, onChange, readonly } = props;\n  return !readonly ? (\n    <div className=\"flex flex-col w-full relative overflow-hidden\">\n      <Input\n        value={data.description}\n        placeholder={I18n.t('workflow_detail_llm_output_decription')}\n        maxLength={200}\n        onChange={value => {\n          onChange(value);\n        }}\n        className=\"w-full\"\n      />\n    </div>\n  ) : (\n    <ReadonlyText value={data.description ?? ''} />\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst INDENT = 4;\nexport const formatJson = (json: string) => {\n  try {\n    return JSON.stringify(JSON.parse(json), null, INDENT);\n  } catch (e) {\n    return json;\n  }\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface SchemaNode {\n  name: string;\n  type: number;\n  children?: SchemaNode[];\n  defaultValue: string;\n}\n\n// modify from @byted/biz-ide-component\nexport const convertSchemaService = (\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  object: any,\n  maxDepth = 20,\n  currentDepth = 1,\n): SchemaNode[] => {\n  if (currentDepth > maxDepth) {\n    return [];\n  }\n  const paramSchema: SchemaNode[] = [];\n  Object.keys(object).forEach(key => {\n    const value = object[key];\n    switch (typeof value) {\n      case 'string':\n        paramSchema.push({\n          name: key,\n          defaultValue: JSON.stringify(value),\n          type: 1 /* String */,\n        });\n        break;\n      case 'number':\n        if (Number.isInteger(value)) {\n          paramSchema.push({\n            name: key,\n            defaultValue: JSON.stringify(value),\n            type: 2 /* Integer */,\n          });\n        } else {\n          paramSchema.push({\n            name: key,\n            defaultValue: JSON.stringify(value),\n            type: 4 /* Number */,\n          });\n        }\n        break;\n      case 'boolean':\n        paramSchema.push({\n          name: key,\n          defaultValue: JSON.stringify(value),\n          type: 3 /* Boolean */,\n        });\n        break;\n      case 'object':\n        if (value === null) {\n          break;\n        }\n        if (Array.isArray(value)) {\n          if (value.length > 0) {\n            switch (typeof value[0]) {\n              case 'string':\n                paramSchema.push({\n                  name: key,\n                  defaultValue: JSON.stringify(value),\n                  type: 99 /* ArrayString */,\n                });\n                break;\n              case 'number':\n                if (Number.isInteger(value[0])) {\n                  paramSchema.push({\n                    name: key,\n                    defaultValue: JSON.stringify(value),\n                    type: 100 /* ArrayInteger */,\n                  });\n                } else {\n                  paramSchema.push({\n                    name: key,\n                    defaultValue: JSON.stringify(value),\n                    type: 102 /* ArrayNumber */,\n                  });\n                }\n                break;\n              case 'boolean':\n                paramSchema.push({\n                  name: key,\n                  defaultValue: JSON.stringify(value),\n                  type: 101 /* ArrayBoolean */,\n                });\n                break;\n              case 'object':\n                paramSchema.push({\n                  name: key,\n                  defaultValue: JSON.stringify(value),\n                  type: 103 /* ArrayObject */,\n                  children: convertSchemaService(\n                    value[0],\n                    maxDepth,\n                    currentDepth + 1,\n                  ),\n                });\n                break;\n              default:\n                paramSchema.push({\n                  name: key,\n                  defaultValue: JSON.stringify(value),\n                  type: 99 /* ArrayString */,\n                });\n            }\n          } else {\n            paramSchema.push({\n              name: key,\n              defaultValue: JSON.stringify(value),\n              type: 99 /* ArrayString */,\n            });\n          }\n        } else {\n          paramSchema.push({\n            name: key,\n            defaultValue: JSON.stringify(value),\n            type: 6 /* Object */,\n            children: convertSchemaService(value, maxDepth, currentDepth + 1),\n          });\n        }\n        break;\n      default:\n        throw new Error('ContainsInvalidValue');\n    }\n  });\n  return paramSchema;\n};\n", "\n      import API from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/style-loader/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./light.module.less\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/index.js??ruleSet[1].rules[10].use[1]!builtin:lightningcss-loader??ruleSet[1].rules[10].use[2]!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/postcss-loader/index.js??ruleSet[1].rules[10].use[3]!../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+plugin-less@1.1.1_@rsbuild+core@1.1.13/node_modules/@rsbuild/plugin-less/compiled/less-loader/index.js??ruleSet[1].rules[10].use[4]!./light.module.less\";\n       export default content && content.locals ? content.locals : undefined;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  useCallback,\n  useEffect,\n  useState,\n  Suspense,\n  lazy,\n  type FC,\n  useMemo,\n} from 'react';\n\nimport { I18n } from '@coze-arch/i18n';\nimport { IconCozBroom } from '@coze-arch/coze-design/icons';\nimport { IconButton, Tooltip, Modal } from '@coze-arch/coze-design';\n\nimport { MAX_JSON_LENGTH } from '../../constants';\nimport { formatJson } from './utils/format-json';\nimport {\n  convertSchemaService,\n  type SchemaNode,\n} from './service/convert-schema-service';\n\nimport lightStyles from './light.module.less';\n\nconst LazyBizIDEMonacoEditor = lazy(async () => {\n  const { Editor } = await import('@coze-arch/bot-monaco-editor');\n  return { default: Editor };\n});\n\nconst BizIDEMonacoEditor = props => (\n  <Suspense>\n    <LazyBizIDEMonacoEditor {...props} />\n  </Suspense>\n);\ninterface JSONEditorProps {\n  id: string;\n  value: string;\n  groupId: string;\n  setValue: (value: string) => void;\n  visible: boolean;\n  readonly?: boolean;\n  onCancel: () => void;\n  onOk: (value: SchemaNode[]) => void;\n}\n\nconst ValidateRules = {\n  jsonValid: {\n    message: I18n.t('variables_json_input_error'),\n    validator: (value: string) => {\n      try {\n        const rs = JSON.parse(value);\n        const isJson = typeof rs === 'object';\n        return isJson;\n        // eslint-disable-next-line @coze-arch/use-error-in-catch\n      } catch (error) {\n        return false;\n      }\n    },\n  },\n  jsonLength: {\n    message: I18n.t('variables_json_input_limit'),\n    validator: (value: string) => {\n      if (value.length > MAX_JSON_LENGTH) {\n        return false;\n      }\n      return true;\n    },\n  },\n};\n\nexport const JSONEditor: FC<JSONEditorProps> = props => {\n  const { id, value, setValue, visible, onCancel, onOk, readonly } = props;\n  const [schema, setSchema] = useState<SchemaNode[] | undefined>();\n  const [error, setError] = useState<string | undefined>();\n  const change = useCallback(async () => {\n    if (!schema) {\n      return;\n    }\n    setError(undefined);\n    return new Promise(resolve => {\n      Modal.warning({\n        title: I18n.t('workflow_json_node_update_tips_title'),\n        content: I18n.t('workflow_json_node_update_tips_content'),\n        okType: 'warning',\n        okText: I18n.t('Confirm'),\n        cancelText: I18n.t('Cancel'),\n        onOk: () => {\n          const outputValue = convert(value) || [];\n          onOk(outputValue);\n          resolve(true);\n        },\n        onCancel: () => resolve(false),\n      });\n    });\n  }, [schema]);\n\n  const convert = (jsonString: string): SchemaNode[] | undefined => {\n    if (!jsonString) {\n      return;\n    }\n    try {\n      const json = JSON.parse(jsonString);\n      const outputValue = convertSchemaService(json);\n      if (\n        !outputValue ||\n        !Array.isArray(outputValue) ||\n        outputValue.length === 0\n      ) {\n        return;\n      }\n      return outputValue;\n    } catch (e) {\n      return;\n    }\n  };\n\n  const validate = (newValue: string) => {\n    const rules = Object.values(ValidateRules);\n    for (const rule of rules) {\n      if (!rule.validator(newValue)) {\n        setError(rule.message);\n        return false;\n      }\n    }\n    setError(undefined);\n    return true;\n  };\n\n  const isValid = useMemo(() => validate(value), [value]);\n\n  // Synchronizing values and schemas\n  useEffect(() => {\n    const _schema = convert(value);\n    setSchema(_schema);\n  }, [value]);\n\n  return (\n    <Modal\n      visible={visible}\n      title={\n        readonly\n          ? I18n.t('variables_json_input_readonly_title')\n          : I18n.t('workflow_json_windows_title')\n      }\n      okText={I18n.t('Confirm')}\n      cancelText={I18n.t('Cancel')}\n      onOk={change}\n      onCancel={onCancel}\n      height={530}\n      okButtonProps={{\n        disabled: !isValid || readonly,\n      }}\n    >\n      <div key={id} className=\"w-full relative\">\n        <div className=\"w-full h-[48px] coz-bg-primary rounded-t-lg coz-fg-primary font-medium text-sm flex items-center justify-between px-4\">\n          <div className=\"coz-fg-primary\">JSON</div>\n          <Tooltip content={I18n.t('workflow_exception_ignore_format')}>\n            <IconButton\n              className=\"bg-transparent\"\n              disabled={readonly}\n              icon={<IconCozBroom />}\n              onClick={() => {\n                setValue(formatJson(value));\n              }}\n            />\n          </Tooltip>\n        </div>\n        <div className=\"w-full h-[320px]\">\n          <BizIDEMonacoEditor\n            key={id}\n            value={value}\n            defaultLanguage=\"json\"\n            /** Override icube-dark theme with css style */\n            className={lightStyles.light}\n            options={{\n              fontSize: 13,\n              minimap: {\n                enabled: false,\n              },\n              contextmenu: false,\n              scrollbar: {\n                verticalScrollbarSize: 10,\n                alwaysConsumeMouseWheel: false,\n              },\n              lineNumbers: 'on',\n              lineNumbersMinChars: 3,\n              folding: false,\n              lineDecorationsWidth: 2,\n              renderLineHighlight: 'none',\n              glyphMargin: false,\n              scrollBeyondLastLine: false,\n              overviewRulerBorder: false,\n              wordWrap: 'on',\n              fixedOverflowWidgets: true,\n              readOnly: readonly,\n            }}\n            onChange={stringValue => {\n              setValue(stringValue || '');\n            }}\n          />\n        </div>\n        {error ? (\n          <div className=\"absolute top-full\">\n            <span className=\"coz-fg-hglt-red text-[12px] font-[400] leading-[16px] whitespace-nowrap\">\n              {error}\n            </span>\n          </div>\n        ) : null}\n      </div>\n    </Modal>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\n/** Indent width of each level of tree */\nexport const TreeIndentWidth = 30;\n/** Tree Node Expand Collapse Button Width */\nexport const TreeCollapseWidth = 24;\n\n// Name Maximum 50 characters\nexport const MAX_NAME_LENGTH = 50;\n\n// Maximum depth limit\nexport const MAX_LEVEL = 3;\n// Maximum number of variables limit\nexport const MAX_JSON_VARIABLE_COUNT = 1;\n// Maximum JSON length limit 30kb\nexport const MAX_JSON_LENGTH = 30 * 1024;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type Variable, ViewVariableType } from '@/store';\n\nimport {\n  traverse,\n  type TraverseContext,\n  type TraverseHandler,\n} from './traverse';\n\nconst isOutputValueContext = (context: TraverseContext): boolean => {\n  if (\n    typeof context.node.value !== 'object' ||\n    typeof context.node.value.type === 'undefined'\n  ) {\n    return false;\n  } else {\n    return true;\n  }\n};\n\nconst cutOffNameLength =\n  (length: number): TraverseHandler =>\n  (context: TraverseContext): void => {\n    if (!isOutputValueContext(context)) {\n      return;\n    }\n    if (context.node.value.name.length > length) {\n      context.node.value.name = context.node.value.name.slice(0, length);\n    }\n  };\n\nconst cutOffDepth =\n  (depth: number): TraverseHandler =>\n  (context: TraverseContext): void => {\n    if (\n      !isOutputValueContext(context) ||\n      context.node.value.level !== depth ||\n      ![ViewVariableType.Object, ViewVariableType.ArrayObject].includes(\n        context.node.value.type,\n      )\n    ) {\n      return;\n    }\n    context.deleteSelf();\n  };\n\nexport const cutOffInvalidData = (params: {\n  data: Variable[];\n  allowDepth: number;\n  allowNameLength: number;\n  maxVariableCount: number;\n}): Variable[] => {\n  const { data, allowDepth, allowNameLength, maxVariableCount } = params;\n  const cutOffVariableCountData = data.slice(0, maxVariableCount);\n  return traverse<Variable[]>(cutOffVariableCountData, [\n    cutOffNameLength(allowNameLength),\n    cutOffDepth(allowDepth),\n  ]);\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type VariableChannel } from '@coze-arch/bot-api/memory';\n\nimport { type ViewVariableType } from '@/store/variable-groups/types';\nimport { useVariableGroupsStore } from '@/store/variable-groups/store';\nimport { type Variable } from '@/store';\n\nimport { type SchemaNode } from '../../../json-editor/service/convert-schema-service';\n\n/**\n * Converting Converted Data to Variables\n * @param data converted data\n * @param baseInfo\n * @param originalVariable to hold variableId\n * @returns Variable[]\n */\nexport const exportVariableService = (\n  data: SchemaNode[],\n  baseInfo: {\n    groupId: string;\n    channel: VariableChannel;\n  },\n  originalVariable?: Variable,\n): Variable[] => {\n  const store = useVariableGroupsStore.getState();\n\n  const convertNode = (\n    node: SchemaNode,\n    parentId = '',\n    originalNode?: Variable,\n  ): Variable => {\n    // Create the underlying variable using the createVariable method in the store\n    const baseVariable = store.createVariable({\n      variableType: node.type as ViewVariableType,\n      groupId: baseInfo.groupId,\n      parentId,\n      channel: baseInfo.channel,\n    });\n\n    // If the original node exists, keep its variableId.\n    if (originalNode) {\n      baseVariable.variableId = originalNode.variableId;\n      baseVariable.description = originalNode.description;\n    }\n\n    // Update basic information about variables\n    baseVariable.name = node.name;\n    baseVariable.defaultValue = node.defaultValue;\n\n    // Recursively process the sub-node and try to match the original sub-node.\n    if (node.children?.length) {\n      baseVariable.children = node.children.map((child, index) => {\n        const originalChild = originalNode?.children?.[index];\n        return convertNode(child, baseVariable.variableId, originalChild);\n      });\n    }\n\n    return baseVariable;\n  };\n\n  const variables = data.map(node => convertNode(node, '', originalVariable));\n\n  // Update meta information using the updateMeta method in the store\n  store.updateMeta({ variables });\n\n  return variables;\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ViewVariableType } from '@/store/variable-groups/types';\nimport { type TreeNodeCustomData } from '@/components/variable-tree/type';\nimport { formatJson } from '@/components/variable-tree/components/json-editor/utils/format-json';\n\nconst getDefaultValueByType = (type: ViewVariableType) => {\n  switch (type) {\n    case ViewVariableType.String:\n      return '';\n    case ViewVariableType.Integer:\n    case ViewVariableType.Number:\n      return 0;\n    case ViewVariableType.Boolean:\n      return false;\n    case ViewVariableType.Object:\n      return {};\n    case ViewVariableType.ArrayString:\n      return [''];\n    case ViewVariableType.ArrayInteger:\n      return [0];\n    case ViewVariableType.ArrayBoolean:\n      return [true];\n    case ViewVariableType.ArrayNumber:\n      return [0];\n    case ViewVariableType.ArrayObject:\n      return [{}];\n    default:\n      return {};\n  }\n};\n\nconst isArrayType = (type: ViewVariableType) =>\n  [\n    ViewVariableType.ArrayString,\n    ViewVariableType.ArrayInteger,\n    ViewVariableType.ArrayBoolean,\n    ViewVariableType.ArrayNumber,\n    ViewVariableType.ArrayObject,\n  ].includes(type);\n\nexport const getEditorViewVariableJson = (treeData: TreeNodeCustomData) => {\n  const { defaultValue, type, name, children } = treeData;\n\n  if (defaultValue) {\n    const json = JSON.parse(defaultValue);\n    return formatJson(\n      JSON.stringify({\n        [name]: json,\n      }),\n    );\n  }\n\n  // If there is no name, return an empty object\n  if (!name) {\n    return '{}';\n  }\n\n  const isArray = isArrayType(type);\n\n  // Recursive processing of children\n  const processChildren = (\n    nodes?: TreeNodeCustomData[],\n    parentType?: ViewVariableType,\n  ) => {\n    if (!nodes || nodes.length === 0) {\n      return getDefaultValueByType(parentType || type);\n    }\n\n    if (isArray && !parentType) {\n      const firstChild = nodes[0];\n      if (!firstChild) {\n        return [];\n      }\n\n      // If it is an array type, generate a default value based on the type of the first child element\n      const result = {};\n      if (firstChild.children && firstChild.children.length > 0) {\n        result[firstChild.name] = processChildren(\n          firstChild.children,\n          firstChild.type,\n        );\n      } else {\n        result[firstChild.name] = getDefaultValueByType(firstChild.type);\n      }\n      return [result];\n    }\n\n    return nodes.reduce((acc, node) => {\n      if (!node.name) {\n        return acc;\n      }\n      if (node.children && node.children.length > 0) {\n        const value = processChildren(node.children, node.type);\n        acc[node.name] = isArrayType(node.type) ? [value] : value;\n      } else {\n        acc[node.name] = getDefaultValueByType(node.type);\n      }\n      return acc;\n    }, {} satisfies Record<string, unknown>);\n  };\n\n  // Generate the final JSON structure\n  const result = {\n    [name]: processChildren(children),\n  };\n\n  return formatJson(JSON.stringify(result));\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useEffect, useState, type FC } from 'react';\n\nimport { merge, cloneDeep } from 'lodash-es';\n\nimport { type SchemaNode } from '../json-editor/service/convert-schema-service';\nimport { JSONEditor } from '../json-editor';\nimport type { TreeNodeCustomData } from '../../type';\nimport {\n  MAX_LEVEL,\n  MAX_NAME_LENGTH,\n  MAX_JSON_VARIABLE_COUNT,\n} from '../../constants';\nimport { cutOffInvalidData } from './utils/cut-off';\nimport { exportVariableService } from './services/use-case-service/export-variable-service';\nimport { getEditorViewVariableJson } from './services/life-cycle-service/init-service';\n\ninterface JSONImportProps {\n  visible: boolean;\n  onCancel: () => void;\n  treeData: TreeNodeCustomData;\n  rules: {\n    jsonImport: boolean;\n    readonly: boolean;\n  };\n  onOk: (value: TreeNodeCustomData) => void;\n}\n\nexport const JSONImport: FC<JSONImportProps> = props => {\n  const { treeData, rules, visible, onCancel, onOk } = props;\n  const { jsonImport, readonly } = rules;\n  const [jsonString, setJsonString] = useState('');\n\n  const handleImport = (data: SchemaNode[]) => {\n    const allowDepth = MAX_LEVEL; // Maximum depth limit\n    const allowNameLength = MAX_NAME_LENGTH; // Name length limit\n    const maxVariableCount = MAX_JSON_VARIABLE_COUNT; // Maximum number of variables limit\n    const variables = exportVariableService(\n      data,\n      {\n        groupId: treeData.groupId,\n        channel: treeData.channel,\n      },\n      treeData, // Pass in the original variable to maintain the variableId.\n    );\n\n    // Crop illegal data\n    const dataCutoff = cutOffInvalidData({\n      data: variables,\n      allowDepth,\n      allowNameLength,\n      maxVariableCount,\n    });\n\n    // First deep copy the original data source\n    const clonedTreeData = cloneDeep(treeData);\n    // Merge old and new data\n    const mergedData = merge(clonedTreeData, dataCutoff[0]);\n\n    // update data\n    return onOk(mergedData);\n  };\n\n  useEffect(() => {\n    setJsonString(getEditorViewVariableJson(treeData));\n  }, [treeData]);\n\n  if (!jsonImport) {\n    return <></>;\n  }\n\n  return (\n    <JSONEditor\n      id={treeData.variableId}\n      groupId={treeData.groupId}\n      value={jsonString}\n      readonly={readonly}\n      setValue={(value: string) => {\n        setJsonString(value);\n      }}\n      visible={visible}\n      onOk={handleImport}\n      onCancel={onCancel}\n    />\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useState } from 'react';\n\nimport cls from 'classnames';\nimport { I18n } from '@coze-arch/i18n';\nimport { IconCozEdit } from '@coze-arch/coze-design/icons';\nimport { CozInputNumber, Switch, Input } from '@coze-arch/coze-design';\n\nimport { ViewVariableType } from '@/store';\nimport { type TreeNodeCustomData } from '@/components/variable-tree/type';\n\nimport { ReadonlyText } from '../readonly-text';\nimport { JSONLikeTypes } from '../../constants';\nimport { JSONImport } from '../../../json-import';\n\ninterface ParamDefaultProps {\n  data: TreeNodeCustomData;\n  onDefaultChange: (value: string | number | boolean) => void;\n  onImportChange: (value: TreeNodeCustomData) => void;\n  readonly?: boolean;\n}\n\nexport const ParamDefault = (props: ParamDefaultProps) => {\n  const { data, onDefaultChange, onImportChange, readonly } = props;\n  const [jsonModalVisible, setJsonModalVisible] = useState(false);\n\n  const isRoot = data.meta.level === 0;\n  const isString = isRoot && data.type === ViewVariableType.String;\n  const isNumber =\n    isRoot &&\n    (data.type === ViewVariableType.Number ||\n      data.type === ViewVariableType.Integer);\n  const isBoolean = isRoot && data.type === ViewVariableType.Boolean;\n  const isShowJsonImport = JSONLikeTypes.includes(data.type) && isRoot;\n\n  return (\n    <div className=\"w-[144px] h-full flex items-center [&_.semi-input-number-suffix-btns]:!h-auto\">\n      <div className=\"flex flex-col w-full relative\">\n        {readonly && !isShowJsonImport ? (\n          <ReadonlyText\n            className=\"w-[144px]\"\n            value={data.defaultValue || '-'}\n          />\n        ) : (\n          <>\n            {isString ? (\n              <Input\n                value={data.defaultValue}\n                onChange={value => onDefaultChange(value)}\n                placeholder={I18n.t(\n                  'workflow_detail_title_testrun_error_input',\n                  {\n                    a: data.name,\n                  },\n                )}\n                maxLength={1000}\n                disabled={readonly}\n              />\n            ) : null}\n\n            {isNumber ? (\n              <CozInputNumber\n                className={cls('h-full', {\n                  '[&_.semi-input-wrapper]:!coz-stroke-plus': true,\n                })}\n                value={data.defaultValue}\n                onChange={value => onDefaultChange(value)}\n                placeholder={I18n.t(\n                  'workflow_detail_title_testrun_error_input',\n                  {\n                    a: data.name,\n                  },\n                )}\n                disabled={readonly}\n              />\n            ) : null}\n\n            {isBoolean ? (\n              <Switch\n                checked={Boolean(data.defaultValue === 'true')}\n                size=\"small\"\n                onChange={value => onDefaultChange(value)}\n                disabled={readonly}\n              />\n            ) : null}\n\n            {isShowJsonImport ? (\n              <>\n                <div\n                  onClick={() => setJsonModalVisible(true)}\n                  className={cls(\n                    'coz-mg-primary rounded cursor-pointer flex items-center justify-center h-[32px] gap-x-1',\n                    {\n                      'coz-fg-primary': !readonly,\n                      'coz-fg-dim': readonly,\n                    },\n                  )}\n                >\n                  <IconCozEdit />\n                  <span className=\"text-sm font-medium\">\n                    {readonly\n                      ? I18n.t('variables_json_input_readonly_button')\n                      : I18n.t('variable_button_input_json')}\n                  </span>\n                </div>\n                <JSONImport\n                  visible={jsonModalVisible}\n                  treeData={data}\n                  rules={{\n                    jsonImport: true,\n                    readonly: Boolean(readonly),\n                  }}\n                  onOk={value => {\n                    onImportChange(value);\n                    setJsonModalVisible(false);\n                  }}\n                  onCancel={() => setJsonModalVisible(false)}\n                />\n              </>\n            ) : null}\n          </>\n        )}\n      </div>\n    </div>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type TreeNodeCustomData } from '@/components/variable-tree/type';\n\nexport const ParamChannel = (props: { value: TreeNodeCustomData }) => {\n  const { value } = props;\n  return value.effectiveChannelList?.length ? (\n    <div className=\"coz-stroke-primary text-[14px] font-[500] leading-[20px]\">\n      {value.effectiveChannelList?.join(',') ?? '--'}\n    </div>\n  ) : null;\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable @coze-arch/max-line-per-function */\nimport React, { useCallback, useRef } from 'react';\n\nimport isNumber from 'lodash-es/isNumber';\nimport { cloneDeep } from 'lodash-es';\nimport classNames from 'classnames';\nimport { type RenderFullLabelProps } from '@coze-arch/bot-semi/Tree';\nimport { IconCozArrowRight } from '@coze-arch/coze-design/icons';\n\nimport { type Variable, type ViewVariableType } from '@/store';\n\nimport { type TreeNodeCustomData } from '../../type';\nimport { TreeIndentWidth } from '../../constants';\nimport { ChangeMode } from './constants';\nimport ParamType from './components/param-type';\nimport ParamOperator from './components/param-operator';\nimport { ParamName } from './components/param-name';\nimport { ParamDescription } from './components/param-description';\nimport { ParamDefault } from './components/param-default';\nimport { ParamChannel } from './components/param-channel';\nexport interface CustomTreeNodeProps extends RenderFullLabelProps {\n  level: number;\n  readonly?: boolean;\n  variablePageCanEdit?: boolean;\n  needRenderAppendChild?: boolean;\n  onChange: (mode: ChangeMode, param: TreeNodeCustomData) => void;\n  hasObjectLike?: boolean;\n  disableDelete?: boolean;\n  couldCollapse?: boolean;\n  hideHeaderKeys?: string[];\n  collapsed?: boolean;\n  validateExistKeyword?: boolean;\n  onCollapse?: (collapsed: boolean) => void;\n}\n\nexport default function CustomTreeNode(props: CustomTreeNodeProps) {\n  const {\n    data,\n    className,\n    level,\n    readonly = false,\n    onChange,\n    hasObjectLike,\n    couldCollapse = true,\n    hideHeaderKeys,\n    collapsed = false,\n    onCollapse,\n    validateExistKeyword = false,\n  } = props;\n  // current value\n  const value = cloneDeep(data) as Variable;\n  const treeNodeRef = useRef<HTMLDivElement>(null);\n\n  // When deleting\n  const onDelete = () => {\n    onChange(ChangeMode.Delete, value);\n  };\n\n  // When adding a child\n  const onAppend = () => {\n    onChange(ChangeMode.Append, value);\n  };\n  // When switching types\n  const onSelectChange = (\n    val?: string | number | Array<unknown> | Record<string, unknown>,\n  ) => {\n    if (val === undefined) {\n      return;\n    }\n    if (!isNumber(val)) {\n      return;\n    }\n    // Clear default\n    value.defaultValue = '';\n    value.children = [];\n    onChange(ChangeMode.Update, { ...value, type: val as ViewVariableType });\n  };\n  const onDefaultChange = (\n    val: string | number | boolean | TreeNodeCustomData,\n  ) => {\n    onChange(ChangeMode.Update, { ...value, defaultValue: val.toString() });\n  };\n\n  const onImportChange = (val: TreeNodeCustomData) => {\n    onChange(ChangeMode.Replace, val);\n  };\n\n  const onNameChange = (name: string) => {\n    if (value.name === name) {\n      return;\n    }\n    onChange(ChangeMode.Update, { ...value, name });\n  };\n\n  const onDescriptionChange = useCallback(\n    (description: string) => {\n      if (value.description === description) {\n        return;\n      }\n      onChange(ChangeMode.Update, { ...value, description });\n    },\n    [onChange, value],\n  );\n\n  const onEnabledChange = useCallback(\n    (enabled: boolean) => {\n      onChange(ChangeMode.UpdateEnabled, { ...value, enabled });\n    },\n    [onChange, value],\n  );\n  return (\n    <div\n      className={classNames('flex items-center', {\n        [className]: Boolean(className),\n      })}\n      ref={treeNodeRef}\n    >\n      <div className=\"flex flex-1 my-3 gap-x-4 items-center w-full relative h-[32px]\">\n        <div className=\"flex flex-1 items-center flex-nowrap overflow-x-hidden overflow-y-visible\">\n          <div\n            className=\"flex items-center justify-end\"\n            style={{ width: level * TreeIndentWidth }}\n          ></div>\n          <IconCozArrowRight\n            className={classNames(\n              'flex-none mr-2 w-[16px] h-[16px]',\n              collapsed ? 'rotate-90' : '',\n              couldCollapse ? '' : 'invisible',\n              'cursor-pointer',\n              level === 0 && !couldCollapse ? 'hidden' : '',\n            )}\n            onClick={() => {\n              onCollapse?.(!collapsed);\n            }}\n          />\n          <ParamName\n            readonly={readonly}\n            data={value}\n            onChange={onNameChange}\n            validateExistKeyword={validateExistKeyword}\n          />\n        </div>\n        <div className=\"flex-1 overflow-hidden\">\n          <ParamDescription\n            data={value}\n            onChange={onDescriptionChange}\n            readonly={readonly}\n          />\n        </div>\n        {!hideHeaderKeys?.includes('type') ? (\n          <div className=\"flex-none w-[166px] basis-[166px]\">\n            <ParamType\n              level={level}\n              readonly={readonly}\n              data={value}\n              onSelectChange={onSelectChange}\n            />\n          </div>\n        ) : null}\n        <div className=\"flex-none w-[164px] basis-[164px]\">\n          <ParamDefault\n            readonly={readonly}\n            data={value}\n            onDefaultChange={onDefaultChange}\n            onImportChange={onImportChange}\n          />\n        </div>\n        <div className=\"flex-none w-[164px] basis-[164px] empty:hidden\">\n          <ParamChannel value={value} />\n        </div>\n        <div className=\"flex-none w-[130px] basis-[130px]\">\n          <ParamOperator\n            data={value}\n            readonly={readonly}\n            level={level}\n            onDelete={onDelete}\n            onAppend={onAppend}\n            hasObjectLike={hasObjectLike}\n            needRenderAppendChild={!hideHeaderKeys?.includes('type')}\n            onEnabledChange={onEnabledChange}\n          />\n        </div>\n      </div>\n    </div>\n  );\n}\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable @coze-arch/max-line-per-function */\nimport { useParams } from 'react-router-dom';\nimport React, {\n  useCallback,\n  useImperativeHandle,\n  useMemo,\n  useState,\n} from 'react';\n\nimport { useShallow } from 'zustand/react/shallow';\nimport { nanoid } from 'nanoid';\nimport classNames from 'classnames';\nimport { I18n } from '@coze-arch/i18n';\nimport { type DynamicParams } from '@coze-arch/bot-typings/teamspace';\nimport { EVENT_NAMES, sendTeaEvent } from '@coze-arch/bot-tea';\nimport { type TreeProps } from '@coze-arch/bot-semi/Tree';\nimport { type VariableChannel } from '@coze-arch/bot-api/memory';\nimport { IconCozPlus } from '@coze-arch/coze-design/icons';\nimport { IconButton, Toast, Tree, useFormApi } from '@coze-arch/coze-design';\n\nimport { traverse } from '@/utils/traverse';\nimport { useVariableGroupsStore, ViewVariableType } from '@/store';\nimport { VariableTreeContext } from '@/context/variable-tree-context';\n\nimport { flatVariableTreeData } from './utils';\nimport { type TreeNodeCustomData } from './type';\nimport { ChangeMode } from './components/custom-tree-node/constants';\nimport CustomTreeNode from './components/custom-tree-node';\n\nexport interface VariableTreeProps {\n  groupId: string;\n  value: Array<TreeNodeCustomData>;\n  treeProps?: TreeProps;\n  readonly?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n  showAddButton?: boolean;\n  /** Default variable type */\n  defaultVariableType?: ViewVariableType;\n  defaultCollapse?: boolean;\n  children?: React.ReactNode;\n  maxLimit?: number;\n  hideHeaderKeys?: string[];\n  channel: VariableChannel;\n  validateExistKeyword?: boolean;\n  onChange?: (changeValue: TreeNodeCustomData) => void;\n}\n\nexport interface VariableTreeRef {\n  validate: () => void;\n}\n\nfunction useExpandedKeys(keys: string[], defaultCollapse: boolean) {\n  const [expandedKeys, setExpandedKeys] = useState(defaultCollapse ? [] : keys);\n\n  const expandTreeNode = useCallback((key: string) => {\n    setExpandedKeys(prev => [...new Set([...prev, key])]);\n  }, []);\n\n  const collapseTreeNode = useCallback((key: string) => {\n    setExpandedKeys(prev => prev.filter(expandedKey => expandedKey !== key));\n  }, []);\n\n  return { expandedKeys, expandTreeNode, collapseTreeNode };\n}\n\nexport function Index(\n  props: VariableTreeProps,\n  ref: React.Ref<VariableTreeRef>,\n) {\n  const {\n    readonly = false,\n    treeProps,\n    className,\n    style,\n    value,\n    defaultVariableType = ViewVariableType.String,\n    defaultCollapse = false,\n    maxLimit,\n    groupId,\n    channel,\n    hideHeaderKeys,\n    validateExistKeyword = false,\n    onChange,\n  } = props;\n\n  const {\n    createVariable,\n    addRootVariable,\n    addChildVariable,\n    updateVariable,\n    deleteVariable,\n    findAndModifyVariable,\n  } = useVariableGroupsStore(\n    useShallow(state => ({\n      createVariable: state.createVariable,\n      addRootVariable: state.addRootVariable,\n      addChildVariable: state.addChildVariable,\n      updateVariable: state.updateVariable,\n      deleteVariable: state.deleteVariable,\n      findAndModifyVariable: state.findAndModifyVariable,\n    })),\n  );\n\n  const formApi = useFormApi();\n\n  const isValueEmpty = !value || value.length === 0;\n\n  const itemKeysWithChildren = useMemo(() => {\n    const keys: string[] = [];\n    traverse(value, item => {\n      if (item.children?.length > 0) {\n        keys.push(item.variableId);\n      }\n    });\n    return keys;\n  }, [value]);\n\n  const flatTreeData = useMemo(() => flatVariableTreeData(value), [value]);\n\n  const { expandedKeys, expandTreeNode, collapseTreeNode } = useExpandedKeys(\n    itemKeysWithChildren,\n    defaultCollapse,\n  );\n  const params = useParams<DynamicParams>();\n\n  useImperativeHandle(ref, () => ({\n    validate: () => formApi.validate(),\n  }));\n\n  const disableAdd = useMemo(() => {\n    if (maxLimit === undefined) {\n      return false;\n    }\n    return (value?.length ?? 0) >= maxLimit;\n  }, [value, maxLimit]);\n\n  const showAddButton = !readonly && !disableAdd;\n\n  const onAdd = () => {\n    const newVariable = createVariable({\n      groupId,\n      parentId: '',\n      variableType: defaultVariableType,\n      channel,\n    });\n\n    addRootVariable(newVariable);\n    onChange?.(newVariable);\n    sendTeaEvent(EVENT_NAMES.memory_click_front, {\n      project_id: params?.project_id || '',\n      resource_type: 'variable',\n      action: 'add',\n      source: 'app_detail_page',\n      source_detail: 'memory_manage',\n    });\n  };\n\n  // Tree node change method\n  const onTreeNodeChange = (mode: ChangeMode, param: TreeNodeCustomData) => {\n    const findResult = findAndModifyVariable(\n      groupId,\n      item => item.variableId === param.variableId,\n    );\n    if (!findResult) {\n      Toast.error(I18n.t('workflow_detail_node_output_parsingfailed'));\n      return;\n    }\n\n    switch (mode) {\n      case ChangeMode.Append: {\n        const { variableId: parentId, channel: parentChannel } = findResult;\n        const childVariable = createVariable({\n          groupId,\n          parentId,\n          variableType: defaultVariableType,\n          channel: parentChannel,\n        });\n        addChildVariable(childVariable);\n\n        // Add a new node under the current node and expand the current node\n        if (findResult?.variableId) {\n          expandTreeNode(findResult.variableId);\n        }\n        sendTeaEvent(EVENT_NAMES.memory_click_front, {\n          project_id: params?.project_id || '',\n          resource_type: 'variable',\n          action: 'add',\n          source: 'app_detail_page',\n          source_detail: 'memory_manage',\n        });\n        break;\n      }\n      case ChangeMode.Update: {\n        updateVariable(param);\n        sendTeaEvent(EVENT_NAMES.memory_click_front, {\n          project_id: params?.project_id || '',\n          resource_type: 'variable',\n          action: 'edit',\n          source: 'app_detail_page',\n          source_detail: 'memory_manage',\n        });\n        break;\n      }\n      case ChangeMode.Delete: {\n        deleteVariable(param);\n        sendTeaEvent(EVENT_NAMES.memory_click_front, {\n          project_id: params?.project_id || '',\n          resource_type: 'variable',\n          action: 'delete',\n          source: 'app_detail_page',\n          source_detail: 'memory_manage',\n        });\n        break;\n      }\n      case ChangeMode.UpdateEnabled: {\n        findResult.enabled = param.enabled;\n        // Close all sub-nodes with one click\n        traverse<TreeNodeCustomData>(findResult, node => {\n          if (!param.enabled) {\n            node.enabled = param.enabled;\n          }\n        });\n        // The child point is turned on, and the parent node is also turned on.\n        if (findResult.parentId && findResult.enabled) {\n          const parentData = findAndModifyVariable(\n            groupId,\n            item => item.variableId === findResult.parentId,\n          );\n          if (parentData) {\n            parentData.enabled = findResult.enabled;\n            updateVariable(parentData);\n          }\n        }\n        updateVariable(findResult);\n        sendTeaEvent(EVENT_NAMES.memory_click_front, {\n          project_id: params?.project_id || '',\n          resource_type: 'variable',\n          action: param.enabled ? 'turn_on' : 'turn_off',\n          source: 'app_detail_page',\n          source_detail: 'memory_manage',\n        });\n        break;\n      }\n      case ChangeMode.Replace: {\n        updateVariable(param);\n        expandTreeNode(findResult.variableId);\n        sendTeaEvent(EVENT_NAMES.memory_click_front, {\n          project_id: params?.project_id || '',\n          resource_type: 'variable',\n          action: 'edit',\n          source: 'app_detail_page',\n          source_detail: 'memory_manage',\n        });\n        break;\n      }\n      default:\n    }\n\n    onChange?.(param);\n  };\n\n  if (readonly && isValueEmpty) {\n    return null;\n  }\n\n  return (\n    <VariableTreeContext.Provider value={{ groupId, variables: flatTreeData }}>\n      <div\n        className={classNames(\n          // basic container style\n          'relative h-full',\n          // interaction state\n          !readonly && 'cursor-default',\n          // custom class name\n          className,\n        )}\n        style={style}\n      >\n        <Tree\n          style={readonly ? {} : { overflow: 'inherit' }}\n          motion={false}\n          keyMaps={{\n            key: 'variableId',\n          }}\n          disabled={readonly}\n          className={classNames(\n            // basic scrolling behavior\n            'overflow-x-auto',\n\n            // Tree list base style\n            [\n              // list container style\n              '[&_.semi-tree-option-list]:overflow-visible',\n              '[&_.semi-tree-option-list]:p-0',\n              '[&_.semi-tree-option-list>div:first-child]:mt-0',\n              // Option style\n              '[&_.semi-tree-option]:!pl-2',\n            ].join(' '),\n\n            // interaction state style\n            readonly\n              ? '[&_.semi-tree-option-list-block_.semi-tree-option:hover]:bg-inherit'\n              : [\n                  '[&_.semi-tree-option-list-block_.semi-tree-option:hover]:bg-transparent',\n                  '[&_.semi-tree-option-list-block_.semi-tree-option:active]:bg-transparent',\n                ].join(' '),\n          )}\n          renderFullLabel={renderFullLabelProps => {\n            const { data } = renderFullLabelProps;\n            const currentLevelReadOnly = readonly || data.IsReadOnly;\n\n            const onCollapse = (collapsed: boolean) => {\n              const { variableId } = renderFullLabelProps.data;\n\n              if (!variableId) {\n                return;\n              }\n\n              if (collapsed) {\n                expandTreeNode(variableId);\n              } else {\n                collapseTreeNode(variableId);\n              }\n            };\n\n            return (\n              <CustomTreeNode\n                {...renderFullLabelProps}\n                hideHeaderKeys={hideHeaderKeys}\n                validateExistKeyword={validateExistKeyword}\n                onChange={onTreeNodeChange}\n                hasObjectLike={data.meta.hasObjectLike}\n                readonly={currentLevelReadOnly}\n                couldCollapse={(data.children?.length ?? 0) > 0}\n                collapsed={renderFullLabelProps.expandStatus.expanded}\n                onCollapse={onCollapse}\n              />\n            );\n          }}\n          emptyContent={<></>}\n          expandedKeys={[...expandedKeys, nanoid()]}\n          treeData={value}\n          {...treeProps}\n        />\n        {showAddButton ? (\n          <div className=\"flex items-center my-3\">\n            <IconButton icon={<IconCozPlus />} onClick={onAdd}>\n              {I18n.t('workflow_detail_node_output_add_subitem')}\n            </IconButton>\n          </div>\n        ) : null}\n      </div>\n    </VariableTreeContext.Provider>\n  );\n}\n\n// Export components that can call the ref method\nexport const VariableTree = React.forwardRef(Index);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport cls from 'classnames';\nimport { I18n } from '@coze-arch/i18n';\nexport const VariableGroupParamHeader = ({\n  hideHeaderKeys,\n}: {\n  hideHeaderKeys?: string[];\n}) => (\n  <div\n    className={cls(\n      'flex w-full h-[28px] py-[6px] pl-8 items-center gap-x-4 justify-start',\n      'border border-solid coz-stroke-primary border-t-0 border-x-0',\n    )}\n  >\n    <div className=\"flex-1 h-full flex items-center\">\n      <div className=\"coz-fg-secondary text-[12px] font-[500] leading-[16px]\">\n        {I18n.t('bot_edit_memory_title_filed')}\n        <span className=\"coz-fg-hglt-red\">*</span>\n      </div>\n    </div>\n    <div className=\"flex-1 h-full flex items-center\">\n      <div className=\"coz-fg-secondary text-[12px] font-[500] leading-[16px]\">\n        {I18n.t('bot_edit_memory_title_description')}\n      </div>\n    </div>\n    {!hideHeaderKeys?.includes('type') ? (\n      <div className=\"flex-none w-[166px] basis-[166px] h-full flex items-center box-content\">\n        <div className=\"coz-fg-secondary text-[12px] font-[500] leading-[16px]\">\n          {I18n.t('variable_Table_Title_type')}\n        </div>\n      </div>\n    ) : null}\n    <div className=\"flex-none w-[164px] basis-[164px] h-full flex items-center box-content\">\n      <div className=\"coz-fg-secondary text-[12px] font-[500] leading-[16px]\">\n        {I18n.t('bot_edit_memory_title_default')}\n      </div>\n    </div>\n    {!hideHeaderKeys?.includes('channel') ? (\n      <div className=\"flex-none w-[164px] basis-[164px] h-full flex items-center box-content\">\n        <div className=\"coz-fg-secondary text-[12px] font-[500] leading-[16px]\">\n          {I18n.t('variable_Table_Title_support_channels')}\n        </div>\n      </div>\n    ) : null}\n\n    <div className=\"flex-none w-[130px] basis-[130px] h-full flex items-center box-content\">\n      <div className=\"coz-fg-secondary text-[12px] font-[500] leading-[16px]\">\n        {I18n.t('bot_edit_memory_title_action')}\n      </div>\n    </div>\n  </div>\n);\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { VariableChannel } from '@coze-arch/bot-api/memory';\n\nimport { type VariableGroup } from '@/store';\n\nimport { flatGroupVariableMeta } from '../../../variable-tree/utils';\n\nexport const useGetHideKeys = (variableGroup: VariableGroup) => {\n  const hideKeys: string[] = [];\n\n  const hideChannel =\n    flatGroupVariableMeta([variableGroup]).filter(\n      item => (item?.effectiveChannelList?.length ?? 0) > 0,\n    ).length <= 0;\n\n  const hideTypeChange = variableGroup.channel === VariableChannel.Custom;\n\n  if (hideChannel) {\n    hideKeys.push('channel');\n  }\n\n  if (hideTypeChange) {\n    hideKeys.push('type');\n  }\n  return hideKeys;\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type FC, type PropsWithChildren, useState } from 'react';\n\nimport cls from 'classnames';\nimport { IconCozArrowRight } from '@coze-arch/coze-design/icons';\nimport { Collapsible } from '@coze-arch/coze-design';\n\nimport { type VariableGroup } from '@/store';\n\nexport const GroupCollapsibleWrapper: FC<\n  PropsWithChildren<{\n    groupInfo: VariableGroup;\n    level?: number;\n  }>\n> = props => {\n  const { groupInfo, children, level = 0 } = props;\n  const [isOpen, setIsOpen] = useState(true);\n  const isTopLevel = level === 0;\n  return (\n    <>\n      <div\n        className={cls(\n          'flex w-full flex-col cursor-pointer px-1 py-2',\n          isTopLevel ? 'hover:coz-mg-secondary-hovered hover:rounded-lg' : '',\n        )}\n        onClick={() => setIsOpen(!isOpen)}\n      >\n        <div className=\"flex items-center\">\n          <div className=\"w-[22px] h-full flex items-center\">\n            <IconCozArrowRight\n              className={cls('w-[14px] h-[14px]', isOpen ? 'rotate-90' : '')}\n            />\n          </div>\n          <div className=\"w-[370px] h-full flex items-center\">\n            <div\n              className={cls(\n                'coz-stroke-primary text-xxl font-medium',\n                !isTopLevel ? '!text-sm my-[10px]' : '',\n              )}\n            >\n              {groupInfo.groupName}\n            </div>\n          </div>\n        </div>\n        {isTopLevel ? (\n          <div className=\"text-sm coz-fg-secondary pl-[22px]\">\n            {groupInfo.groupDesc}\n          </div>\n        ) : null}\n      </div>\n      <Collapsible keepDOM isOpen={isOpen}>\n        <div className={cls('w-full h-full', !isTopLevel ? 'pl-[18px]' : '')}>\n          {children}\n        </div>\n      </Collapsible>\n    </>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { type VariableGroup as VariableGroupType } from '@/store';\n\nimport { type TreeNodeCustomData } from '../variable-tree/type';\nimport { VariableTree } from '../variable-tree';\nimport { VariableGroupParamHeader, useGetHideKeys } from './group-header';\nimport { GroupCollapsibleWrapper } from './group-collapsible-wraper';\n\ninterface IVariableGroupProps {\n  groupInfo: VariableGroupType;\n  readonly?: boolean;\n  validateExistKeyword?: boolean;\n  onVariableChange: (changeValue: TreeNodeCustomData) => void;\n}\n\nexport const VariableGroup = (props: IVariableGroupProps) => {\n  const {\n    groupInfo,\n    readonly = true,\n    validateExistKeyword = false,\n    onVariableChange,\n  } = props;\n  const hideHeaderKeys = useGetHideKeys(groupInfo);\n  return (\n    <>\n      <GroupCollapsibleWrapper groupInfo={groupInfo}>\n        <VariableGroupParamHeader hideHeaderKeys={hideHeaderKeys} />\n        <div className=\"pl-6\">\n          {groupInfo.subGroupList?.map(subGroup => (\n            <GroupCollapsibleWrapper groupInfo={subGroup} level={1}>\n              <VariableTree\n                hideHeaderKeys={hideHeaderKeys}\n                groupId={groupInfo.groupId}\n                value={subGroup.varInfoList ?? []}\n                readonly={readonly}\n                channel={subGroup.channel}\n                validateExistKeyword={validateExistKeyword}\n                onChange={onVariableChange}\n              />\n            </GroupCollapsibleWrapper>\n          ))}\n        </div>\n        <div className=\"flex flex-col pl-6\">\n          <VariableTree\n            hideHeaderKeys={hideHeaderKeys}\n            groupId={groupInfo.groupId}\n            value={groupInfo.varInfoList ?? []}\n            readonly={readonly}\n            channel={groupInfo.channel}\n            validateExistKeyword={validateExistKeyword}\n            onChange={onVariableChange}\n          />\n        </div>\n      </GroupCollapsibleWrapper>\n    </>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { I18n } from '@coze-arch/i18n';\nimport { MemoryApi } from '@coze-arch/bot-api';\nimport { Toast } from '@coze-arch/coze-design';\n\nimport { useVariableGroupsStore } from '../../store';\n/**\n * commit variable\n * @param projectID\n * @returns\n */\nexport async function submit(projectID: string) {\n  const { getAllRootVariables, getDtoVariable } =\n    useVariableGroupsStore.getState();\n  const res = await <PERSON><PERSON>pi.UpdateProjectVariable({\n    ProjectID: projectID,\n    VariableList: getAllRootVariables().map(item => getDtoVariable(item)),\n  });\n\n  if (res.code === 0) {\n    Toast.success(I18n.t('Update_success'));\n  }\n}\n\n/**\n * Check and make sure projectID is a non-empty string\n * @param projectID possibly empty project ID\n * @Returns whether projectID is a non-empty string\n */\nexport const checkProjectID = (projectID: unknown): projectID is string =>\n  typeof projectID === 'string' && projectID.length > 0;\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useState } from 'react';\n\nimport { localStorageService } from '@coze-foundation/local-storage';\n\nconst SESSION_HIDDEN_KEY = 'coze-home-session-area-hidden-key';\n\nexport const useHiddenSession = (key: string) => {\n  const [isSessionHidden, setIsSessionHidden] = useState(isKeyExist(key));\n  return {\n    isSessionHidden,\n    hideSession: () => {\n      if (isKeyExist(key)) {\n        return;\n      }\n      const oldValue = localStorageService.getValue(SESSION_HIDDEN_KEY) || '';\n      localStorageService.setValue(\n        SESSION_HIDDEN_KEY,\n        oldValue ? `${oldValue},${key}` : key,\n      );\n      setIsSessionHidden(true);\n    },\n  };\n};\n\nconst isKeyExist = (key: string) => {\n  const oldValue = localStorageService.getValue(SESSION_HIDDEN_KEY);\n  return oldValue?.includes(key);\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { useState } from 'react';\n\nimport { useHiddenSession } from '@/hooks/use-case/use-hidden-session';\n\nexport const useChangeWarning = () => {\n  const [isShowBanner, setIsShowBanner] = useState(false);\n  const { isSessionHidden, hideSession } = useHiddenSession(\n    'variable_config_change_banner_remind',\n  );\n\n  const showBanner = () => {\n    setIsShowBanner(true);\n  };\n\n  const hideBanner = () => {\n    setIsShowBanner(false);\n  };\n\n  const hideBannerForever = () => {\n    hideSession();\n    setIsShowBanner(false);\n  };\n\n  return {\n    isShowBanner: isShowBanner && !isSessionHidden,\n    showBanner,\n    hideBanner,\n    hideBannerForever,\n  };\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React, { useEffect, useRef } from 'react';\n\nimport { useShallow } from 'zustand/react/shallow';\nimport cls from 'classnames';\nimport { IllustrationNoContent } from '@douyinfe/semi-illustrations';\nimport { I18n } from '@coze-arch/i18n';\nimport { VariableChannel } from '@coze-arch/bot-api/memory';\nimport { IconCozCross } from '@coze-arch/coze-design/icons';\nimport {\n  Button,\n  Empty,\n  Form,\n  type FormApi,\n  IconButton,\n  Spin,\n} from '@coze-arch/coze-design';\n\nimport { useVariableGroupsStore, type Variable } from '@/store';\nimport { useLeaveWarning } from '@/hooks/use-case/use-leave-waring';\nimport { useInit } from '@/hooks/life-cycle/use-init';\nimport { useDestory } from '@/hooks/life-cycle/use-destory';\nimport { VariableContext } from '@/context';\nimport { VariableGroup as VariableGroupComponent } from '@/components/variable-group';\n\nimport {\n  checkProjectID,\n  submit,\n} from './service/use-case-service/submit-service';\nimport { useChangeWarning } from './hooks/use-case/use-change-warning';\n\nexport interface VariableConfigProps {\n  projectID: string;\n  version?: string;\n}\n\nexport const VariablesConfig = ({\n  projectID,\n  version,\n}: VariableConfigProps) => {\n  const formApiRef = useRef<FormApi | null>(null);\n\n  const { loading } = useInit(projectID, version);\n  useDestory();\n  const { setHasUnsavedChanges, hasUnsavedChanges } = useLeaveWarning();\n  const { variableGroups, canEdit, saveHistory, getAllVariables } =\n    useVariableGroupsStore(\n      useShallow(state => ({\n        variableGroups: state.variableGroups,\n        canEdit: state.canEdit,\n        saveHistory: state.saveHistory,\n        getAllVariables: state.getAllVariables,\n      })),\n    );\n\n  const { isShowBanner, showBanner, hideBanner, hideBannerForever } =\n    useChangeWarning();\n\n  const isEmpty = !variableGroups.length;\n\n  const onVariableChange = (changeValue: Variable) => {\n    setHasUnsavedChanges(true);\n    if (changeValue.meta?.isHistory) {\n      showBanner();\n    }\n  };\n\n  const handleSubmit = async () => {\n    if (!checkProjectID(projectID)) {\n      return;\n    }\n    const formApi = formApiRef.current;\n    if (!formApi) {\n      return;\n    }\n    const isValid = await formApi.validate();\n    if (!isValid) {\n      return;\n    }\n    saveHistory();\n    await submit(projectID);\n    setHasUnsavedChanges(false);\n  };\n\n  const initValues = getAllVariables().reduce((acc, curr) => {\n    acc[curr.variableId] = { name: curr.name };\n    return acc;\n  }, {});\n\n  useEffect(() => {\n    if (loading) {\n      return;\n    }\n    formApiRef.current?.setValues(initValues);\n  }, [loading, initValues]);\n\n  return (\n    <VariableContext.Provider\n      value={{\n        variablePageCanEdit: canEdit,\n        groups: variableGroups,\n      }}\n    >\n      <div className=\"p-4 pb-[72px]\">\n        {loading ? (\n          <div className=\"w-full h-full flex justify-center items-center\">\n            <Spin />\n          </div>\n        ) : isEmpty ? (\n          <div className=\"w-full h-full flex items-center justify-center\">\n            <Empty\n              image={<IllustrationNoContent className=\"w-[140px] h-[140px]\" />}\n              title={I18n.t('card_builder_varpanel_var_empty')}\n            />\n          </div>\n        ) : (\n          <>\n            {isShowBanner ? (\n              <div className=\"h-[36px] flex items-center justify-center coz-mg-hglt coz-fg-primary text-sm mb-4 mt-[-16px] mx-[-16px]\">\n                <div className=\"flex items-center ml-auto\">\n                  {I18n.t('variable_config_change_banner')}\n                </div>\n                <div className=\"flex items-center ml-auto cursor-pointer\">\n                  <div\n                    className=\"coz-fg-secondary text-xs\"\n                    onClick={hideBannerForever}\n                  >\n                    {I18n.t('do_not_remind_again')}\n                  </div>\n                  <IconButton\n                    className=\"ml-2 !bg-transparent\"\n                    onClick={hideBanner}\n                    icon={<IconCozCross />}\n                  />\n                </div>\n              </div>\n            ) : null}\n            <Form<typeof initValues>\n              getFormApi={formApi => {\n                formApiRef.current = formApi;\n              }}\n              showValidateIcon={false}\n              autoScrollToError\n              initValues={initValues}\n            >\n              <div className=\"flex flex-col gap-2\">\n                {variableGroups.map(item => (\n                  <VariableGroupComponent\n                    readonly={!canEdit || item.isReadOnly}\n                    groupInfo={item}\n                    onVariableChange={onVariableChange}\n                    validateExistKeyword={item.channel === VariableChannel.APP}\n                  />\n                ))}\n              </div>\n            </Form>\n            <div\n              className={cls(\n                'flex items-center justify-end',\n                'fixed bottom-[1px] right-[1px] left-[1px] pb-4 pt-6',\n                'bg-white mr-4 px-4',\n              )}\n            >\n              <Button\n                onClick={handleSubmit}\n                disabled={!canEdit || !hasUnsavedChanges}\n              >\n                {I18n.t('edit_variables_modal_ok_text')}\n              </Button>\n            </div>\n          </>\n        )}\n      </div>\n    </VariableContext.Provider>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport classNames from 'classnames';\nimport { useKnowledgeParams } from '@coze-data/knowledge-stores';\nimport { I18n } from '@coze-arch/i18n';\nimport { TabBar, TabPane } from '@coze-arch/coze-design';\n\nimport { VariablesValue } from './variables-value';\nimport { VariablesConfig } from './variables-config';\n\nexport const VariablesPage = () => {\n  const params = useKnowledgeParams();\n  const { projectID = '', version } = params;\n  return (\n    <div\n      className={classNames(\n        'h-full w-full overflow-hidden',\n        'border border-solid coz-stroke-primary coz-bg-max',\n      )}\n    >\n      <TabBar\n        lazyRender\n        type=\"text\"\n        className={classNames(\n          'h-full flex flex-col',\n          // Scroll bar position is adjusted to tab content\n          '[&_.semi-tabs-content]:p-0 [&_.semi-tabs-content]:grow [&_.semi-tabs-content]:overflow-hidden',\n          '[&_.semi-tabs-pane-active]:h-full',\n          '[&_.semi-tabs-pane-motion-overlay]:h-full [&_.semi-tabs-pane-motion-overlay]:overflow-auto',\n        )}\n        tabBarClassName=\"flex items-center h-[56px] mx-[16px]\"\n      >\n        <TabPane tab={I18n.t('db_optimize_033')} itemKey=\"config\">\n          <VariablesConfig projectID={projectID} version={version} />\n        </TabPane>\n        <TabPane tab={I18n.t('variable_Tabname_test_data')} itemKey=\"values\">\n          <VariablesValue projectID={projectID} version={version} />\n        </TabPane>\n      </TabBar>\n    </div>\n  );\n};\n", "/*\n * Copyright 2025 coze-dev Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport React, { useEffect } from 'react';\n\nimport qs from 'qs';\nimport {\n  useCurrentWidgetContext,\n  useIDENavigate,\n  useProjectId,\n  useCommitVersion,\n} from '@coze-project-ide/framework';\nimport { VariablesPage } from '@coze-data/variable';\nimport { KnowledgeParamsStoreProvider } from '@coze-data/knowledge-stores';\nimport { I18n } from '@coze-arch/i18n';\n\nconst Main = () => {\n  const IDENav = useIDENavigate();\n  const { widget } = useCurrentWidgetContext();\n  const projectID = useProjectId();\n\n  const { version } = useCommitVersion();\n\n  const { uri } = useCurrentWidgetContext();\n\n  const datasetID = uri?.path.name ?? '';\n\n  useEffect(() => {\n    widget.setTitle(I18n.t('dataide002'));\n    widget.setUIState('normal');\n  }, []);\n\n  return (\n    <KnowledgeParamsStoreProvider\n      params={{\n        version,\n        projectID,\n        datasetID,\n        biz: 'project',\n      }}\n      resourceNavigate={{\n        // eslint-disable-next-line max-params\n        toResource: (resource, resourceID, query, opts) =>\n          IDENav(`/${resource}/${resourceID}?${qs.stringify(query)}`, opts),\n        upload: (query, opts) =>\n          IDENav(\n            `/knowledge/${datasetID}?module=upload&${qs.stringify(query)}`,\n            opts,\n          ),\n        navigateTo: IDENav,\n      }}\n    >\n      <VariablesPage />\n    </KnowledgeParamsStoreProvider>\n  );\n};\n\nexport default Main;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../../../../../common/temp/default/node_modules/.pnpm/@rsbuild+core@1.1.13/node_modules/@rsbuild/core/compiled/css-loader/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.light-X4nr3q .monaco-editor{--vscode-foreground:#616161;--vscode-disabledForeground:rgba(97,97,97,.5);--vscode-errorForeground:#a1260d;--vscode-descriptionForeground:#717171;--vscode-icon-foreground:#424242;--vscode-focusBorder:#0090f1;--vscode-textSeparator-foreground:rgba(0,0,0,.18);--vscode-textLink-foreground:#006ab1;--vscode-textLink-activeForeground:#006ab1;--vscode-textPreformat-foreground:#a31515;--vscode-textBlockQuote-background:rgba(127,127,127,.1);--vscode-textBlockQuote-border:rgba(0,122,204,.5);--vscode-textCodeBlock-background:rgba(220,220,220,.4);--vscode-widget-shadow:rgba(0,0,0,.16);--vscode-input-background:#fff;--vscode-input-foreground:#616161;--vscode-inputOption-activeBorder:#007acc;--vscode-inputOption-hoverBackground:rgba(184,184,184,.31);--vscode-inputOption-activeBackground:rgba(0,144,241,.2);--vscode-inputOption-activeForeground:#000;--vscode-input-placeholderForeground:rgba(97,97,97,.5);--vscode-inputValidation-infoBackground:#d6ecf2;--vscode-inputValidation-infoBorder:#007acc;--vscode-inputValidation-warningBackground:#f6f5d2;--vscode-inputValidation-warningBorder:#b89500;--vscode-inputValidation-errorBackground:#f2dede;--vscode-inputValidation-errorBorder:#be1100;--vscode-dropdown-background:#fff;--vscode-dropdown-foreground:#616161;--vscode-dropdown-border:#cecece;--vscode-button-foreground:#fff;--vscode-button-separator:rgba(255,255,255,.4);--vscode-button-background:#007acc;--vscode-button-hoverBackground:#0062a3;--vscode-button-secondaryForeground:#fff;--vscode-button-secondaryBackground:#5f6a79;--vscode-button-secondaryHoverBackground:#4c5561;--vscode-badge-background:#c4c4c4;--vscode-badge-foreground:#333;--vscode-scrollbar-shadow:#ddd;--vscode-scrollbarSlider-background:rgba(100,100,100,.4);--vscode-scrollbarSlider-hoverBackground:rgba(100,100,100,.7);--vscode-scrollbarSlider-activeBackground:rgba(0,0,0,.6);--vscode-progressBar-background:#0e70c0;--vscode-editorError-foreground:#e51400;--vscode-editorWarning-foreground:#bf8803;--vscode-editorInfo-foreground:#1a85ff;--vscode-editorHint-foreground:#6c6c6c;--vscode-sash-hoverBorder:#0090f1;--vscode-editor-background:#fffffe;--vscode-editor-foreground:#000;--vscode-editorStickyScroll-background:#fffffe;--vscode-editorStickyScrollHover-background:#f0f0f0;--vscode-editorWidget-background:#f3f3f3;--vscode-editorWidget-foreground:#616161;--vscode-editorWidget-border:#c8c8c8;--vscode-quickInput-background:#f3f3f3;--vscode-quickInput-foreground:#616161;--vscode-quickInputTitle-background:rgba(0,0,0,.06);--vscode-pickerGroup-foreground:#0066bf;--vscode-pickerGroup-border:#cccedb;--vscode-keybindingLabel-background:rgba(221,221,221,.4);--vscode-keybindingLabel-foreground:#555;--vscode-keybindingLabel-border:rgba(204,204,204,.4);--vscode-keybindingLabel-bottomBorder:rgba(187,187,187,.4);--vscode-editor-selectionBackground:#add6ff;--vscode-editor-inactiveSelectionBackground:#e5ebf1;--vscode-editor-selectionHighlightBackground:rgba(173,214,255,.3);--vscode-editor-findMatchBackground:#a8ac94;--vscode-editor-findMatchHighlightBackground:rgba(234,92,0,.33);--vscode-editor-findRangeHighlightBackground:rgba(180,180,180,.3);--vscode-searchEditor-findMatchBackground:rgba(234,92,0,.22);--vscode-search-resultsInfoForeground:#616161;--vscode-editor-hoverHighlightBackground:rgba(173,214,255,.15);--vscode-editorHoverWidget-background:#f3f3f3;--vscode-editorHoverWidget-foreground:#616161;--vscode-editorHoverWidget-border:#c8c8c8;--vscode-editorHoverWidget-statusBarBackground:#e7e7e7;--vscode-editorLink-activeForeground:#00f;--vscode-editorInlayHint-foreground:#969696;--vscode-editorInlayHint-background:rgba(196,196,196,.1);--vscode-editorInlayHint-typeForeground:#969696;--vscode-editorInlayHint-typeBackground:rgba(196,196,196,.1);--vscode-editorInlayHint-parameterForeground:#969696;--vscode-editorInlayHint-parameterBackground:rgba(196,196,196,.1);--vscode-editorLightBulb-foreground:#ddb100;--vscode-editorLightBulbAutoFix-foreground:#007acc;--vscode-diffEditor-insertedTextBackground:rgba(156,204,44,.25);--vscode-diffEditor-removedTextBackground:rgba(255,0,0,.2);--vscode-diffEditor-insertedLineBackground:rgba(155,185,85,.2);--vscode-diffEditor-removedLineBackground:rgba(255,0,0,.2);--vscode-diffEditor-diagonalFill:rgba(34,34,34,.2);--vscode-diffEditor-unchangedRegionBackground:#e4e4e4;--vscode-diffEditor-unchangedRegionForeground:#4d4c4c;--vscode-diffEditor-unchangedCodeBackground:rgba(184,184,184,.16);--vscode-list-focusOutline:#0090f1;--vscode-list-activeSelectionBackground:#0060c0;--vscode-list-activeSelectionForeground:#fff;--vscode-list-inactiveSelectionBackground:#e4e6f1;--vscode-list-hoverBackground:#f0f0f0;--vscode-list-dropBackground:#d6ebff;--vscode-list-highlightForeground:#0066bf;--vscode-list-focusHighlightForeground:#bbe7ff;--vscode-list-invalidItemForeground:#b89500;--vscode-list-errorForeground:#b01011;--vscode-list-warningForeground:#855f00;--vscode-listFilterWidget-background:#f3f3f3;--vscode-listFilterWidget-outline:transparent;--vscode-listFilterWidget-noMatchesOutline:#be1100;--vscode-listFilterWidget-shadow:rgba(0,0,0,.16);--vscode-list-filterMatchBackground:rgba(234,92,0,.33);--vscode-tree-indentGuidesStroke:#a9a9a9;--vscode-tree-inactiveIndentGuidesStroke:rgba(169,169,169,.4);--vscode-tree-tableColumnsBorder:rgba(97,97,97,.13);--vscode-tree-tableOddRowsBackground:rgba(97,97,97,.04);--vscode-list-deemphasizedForeground:#8e8e90;--vscode-checkbox-background:#fff;--vscode-checkbox-selectBackground:#f3f3f3;--vscode-checkbox-foreground:#616161;--vscode-checkbox-border:#cecece;--vscode-checkbox-selectBorder:#424242;--vscode-quickInputList-focusForeground:#fff;--vscode-quickInputList-focusBackground:#0060c0;--vscode-menu-foreground:#616161;--vscode-menu-background:#fff;--vscode-menu-selectionForeground:#fff;--vscode-menu-selectionBackground:#0060c0;--vscode-menu-separatorBackground:#d4d4d4;--vscode-toolbar-hoverBackground:rgba(184,184,184,.31);--vscode-toolbar-activeBackground:rgba(166,166,166,.31);--vscode-editor-snippetTabstopHighlightBackground:rgba(10,50,100,.2);--vscode-editor-snippetFinalTabstopHighlightBorder:rgba(10,50,100,.5);--vscode-breadcrumb-foreground:rgba(97,97,97,.8);--vscode-breadcrumb-background:#fffffe;--vscode-breadcrumb-focusForeground:#4e4e4e;--vscode-breadcrumb-activeSelectionForeground:#4e4e4e;--vscode-breadcrumbPicker-background:#f3f3f3;--vscode-merge-currentHeaderBackground:rgba(64,200,174,.5);--vscode-merge-currentContentBackground:rgba(64,200,174,.2);--vscode-merge-incomingHeaderBackground:rgba(64,166,255,.5);--vscode-merge-incomingContentBackground:rgba(64,166,255,.2);--vscode-merge-commonHeaderBackground:rgba(96,96,96,.4);--vscode-merge-commonContentBackground:rgba(96,96,96,.16);--vscode-editorOverviewRuler-currentContentForeground:rgba(64,200,174,.5);--vscode-editorOverviewRuler-incomingContentForeground:rgba(64,166,255,.5);--vscode-editorOverviewRuler-commonContentForeground:rgba(96,96,96,.4);--vscode-editorOverviewRuler-findMatchForeground:rgba(209,134,22,.49);--vscode-editorOverviewRuler-selectionHighlightForeground:rgba(160,160,160,.8);--vscode-minimap-findMatchHighlight:#d18616;--vscode-minimap-selectionOccurrenceHighlight:#c9c9c9;--vscode-minimap-selectionHighlight:#add6ff;--vscode-minimap-errorHighlight:rgba(255,18,18,.7);--vscode-minimap-warningHighlight:#bf8803;--vscode-minimap-foregroundOpacity:#000;--vscode-minimapSlider-background:rgba(100,100,100,.2);--vscode-minimapSlider-hoverBackground:rgba(100,100,100,.35);--vscode-minimapSlider-activeBackground:rgba(0,0,0,.3);--vscode-problemsErrorIcon-foreground:#e51400;--vscode-problemsWarningIcon-foreground:#bf8803;--vscode-problemsInfoIcon-foreground:#1a85ff;--vscode-charts-foreground:#616161;--vscode-charts-lines:rgba(97,97,97,.5);--vscode-charts-red:#e51400;--vscode-charts-blue:#1a85ff;--vscode-charts-yellow:#bf8803;--vscode-charts-orange:#d18616;--vscode-charts-green:#388a34;--vscode-charts-purple:#652d90;--vscode-diffEditor-move-border:rgba(139,139,139,.61);--vscode-diffEditor-moveActive-border:orange;--vscode-symbolIcon-arrayForeground:#616161;--vscode-symbolIcon-booleanForeground:#616161;--vscode-symbolIcon-classForeground:#d67e00;--vscode-symbolIcon-colorForeground:#616161;--vscode-symbolIcon-constantForeground:#616161;--vscode-symbolIcon-constructorForeground:#652d90;--vscode-symbolIcon-enumeratorForeground:#d67e00;--vscode-symbolIcon-enumeratorMemberForeground:#007acc;--vscode-symbolIcon-eventForeground:#d67e00;--vscode-symbolIcon-fieldForeground:#007acc;--vscode-symbolIcon-fileForeground:#616161;--vscode-symbolIcon-folderForeground:#616161;--vscode-symbolIcon-functionForeground:#652d90;--vscode-symbolIcon-interfaceForeground:#007acc;--vscode-symbolIcon-keyForeground:#616161;--vscode-symbolIcon-keywordForeground:#616161;--vscode-symbolIcon-methodForeground:#652d90;--vscode-symbolIcon-moduleForeground:#616161;--vscode-symbolIcon-namespaceForeground:#616161;--vscode-symbolIcon-nullForeground:#616161;--vscode-symbolIcon-numberForeground:#616161;--vscode-symbolIcon-objectForeground:#616161;--vscode-symbolIcon-operatorForeground:#616161;--vscode-symbolIcon-packageForeground:#616161;--vscode-symbolIcon-propertyForeground:#616161;--vscode-symbolIcon-referenceForeground:#616161;--vscode-symbolIcon-snippetForeground:#616161;--vscode-symbolIcon-stringForeground:#616161;--vscode-symbolIcon-structForeground:#616161;--vscode-symbolIcon-textForeground:#616161;--vscode-symbolIcon-typeParameterForeground:#616161;--vscode-symbolIcon-unitForeground:#616161;--vscode-symbolIcon-variableForeground:#007acc;--vscode-actionBar-toggledBackground:rgba(0,144,241,.2);--vscode-editor-lineHighlightBorder:#eee;--vscode-editor-rangeHighlightBackground:rgba(253,255,0,.2);--vscode-editor-symbolHighlightBackground:rgba(234,92,0,.33);--vscode-editorCursor-foreground:#000;--vscode-editorWhitespace-foreground:rgba(51,51,51,.2);--vscode-editorLineNumber-foreground:#237893;--vscode-editorIndentGuide-background:rgba(51,51,51,.2);--vscode-editorIndentGuide-activeBackground:rgba(51,51,51,.2);--vscode-editorIndentGuide-background1:#d3d3d3;--vscode-editorIndentGuide-background2:transparent;--vscode-editorIndentGuide-background3:transparent;--vscode-editorIndentGuide-background4:transparent;--vscode-editorIndentGuide-background5:transparent;--vscode-editorIndentGuide-background6:transparent;--vscode-editorIndentGuide-activeBackground1:#939393;--vscode-editorIndentGuide-activeBackground2:transparent;--vscode-editorIndentGuide-activeBackground3:transparent;--vscode-editorIndentGuide-activeBackground4:transparent;--vscode-editorIndentGuide-activeBackground5:transparent;--vscode-editorIndentGuide-activeBackground6:transparent;--vscode-editorActiveLineNumber-foreground:#0b216f;--vscode-editorLineNumber-activeForeground:#0b216f;--vscode-editorRuler-foreground:#d3d3d3;--vscode-editorCodeLens-foreground:#919191;--vscode-editorBracketMatch-background:rgba(0,100,0,.1);--vscode-editorBracketMatch-border:#b9b9b9;--vscode-editorOverviewRuler-border:rgba(127,127,127,.3);--vscode-editorGutter-background:#fffffe;--vscode-editorUnnecessaryCode-opacity:rgba(0,0,0,.47);--vscode-editorGhostText-foreground:rgba(0,0,0,.47);--vscode-editorOverviewRuler-rangeHighlightForeground:rgba(0,122,204,.6);--vscode-editorOverviewRuler-errorForeground:rgba(255,18,18,.7);--vscode-editorOverviewRuler-warningForeground:#bf8803;--vscode-editorOverviewRuler-infoForeground:#1a85ff;--vscode-editorBracketHighlight-foreground1:#0431fa;--vscode-editorBracketHighlight-foreground2:#319331;--vscode-editorBracketHighlight-foreground3:#7b3814;--vscode-editorBracketHighlight-foreground4:transparent;--vscode-editorBracketHighlight-foreground5:transparent;--vscode-editorBracketHighlight-foreground6:transparent;--vscode-editorBracketHighlight-unexpectedBracket-foreground:rgba(255,18,18,.8);--vscode-editorBracketPairGuide-background1:transparent;--vscode-editorBracketPairGuide-background2:transparent;--vscode-editorBracketPairGuide-background3:transparent;--vscode-editorBracketPairGuide-background4:transparent;--vscode-editorBracketPairGuide-background5:transparent;--vscode-editorBracketPairGuide-background6:transparent;--vscode-editorBracketPairGuide-activeBackground1:transparent;--vscode-editorBracketPairGuide-activeBackground2:transparent;--vscode-editorBracketPairGuide-activeBackground3:transparent;--vscode-editorBracketPairGuide-activeBackground4:transparent;--vscode-editorBracketPairGuide-activeBackground5:transparent;--vscode-editorBracketPairGuide-activeBackground6:transparent;--vscode-editorUnicodeHighlight-border:#cea33d;--vscode-editorUnicodeHighlight-background:rgba(206,163,61,.08);--vscode-editorOverviewRuler-bracketMatchForeground:#a0a0a0;--vscode-editor-linkedEditingBackground:rgba(255,0,0,.3);--vscode-editor-wordHighlightBackground:rgba(87,87,87,.25);--vscode-editor-wordHighlightStrongBackground:rgba(14,99,156,.25);--vscode-editor-wordHighlightTextBackground:rgba(87,87,87,.25);--vscode-editorOverviewRuler-wordHighlightForeground:rgba(160,160,160,.8);--vscode-editorOverviewRuler-wordHighlightStrongForeground:rgba(192,160,192,.8);--vscode-editorOverviewRuler-wordHighlightTextForeground:rgba(160,160,160,.8);--vscode-peekViewTitle-background:#f3f3f3;--vscode-peekViewTitleLabel-foreground:#000;--vscode-peekViewTitleDescription-foreground:#616161;--vscode-peekView-border:#1a85ff;--vscode-peekViewResult-background:#f3f3f3;--vscode-peekViewResult-lineForeground:#646465;--vscode-peekViewResult-fileForeground:#1e1e1e;--vscode-peekViewResult-selectionBackground:rgba(51,153,255,.2);--vscode-peekViewResult-selectionForeground:#6c6c6c;--vscode-peekViewEditor-background:#f2f8fc;--vscode-peekViewEditorGutter-background:#f2f8fc;--vscode-peekViewEditorStickyScroll-background:#f2f8fc;--vscode-peekViewResult-matchHighlightBackground:rgba(234,92,0,.3);--vscode-peekViewEditor-matchHighlightBackground:rgba(245,216,2,.87);--vscode-editorMarkerNavigationError-background:#e51400;--vscode-editorMarkerNavigationError-headerBackground:rgba(229,20,0,.1);--vscode-editorMarkerNavigationWarning-background:#bf8803;--vscode-editorMarkerNavigationWarning-headerBackground:rgba(191,136,3,.1);--vscode-editorMarkerNavigationInfo-background:#1a85ff;--vscode-editorMarkerNavigationInfo-headerBackground:rgba(26,133,255,.1);--vscode-editorMarkerNavigation-background:#fffffe;--vscode-editorHoverWidget-highlightForeground:#0066bf;--vscode-editorSuggestWidget-background:#f3f3f3;--vscode-editorSuggestWidget-border:#c8c8c8;--vscode-editorSuggestWidget-foreground:#000;--vscode-editorSuggestWidget-selectedForeground:#fff;--vscode-editorSuggestWidget-selectedBackground:#0060c0;--vscode-editorSuggestWidget-highlightForeground:#0066bf;--vscode-editorSuggestWidget-focusHighlightForeground:#bbe7ff;--vscode-editorSuggestWidgetStatus-foreground:rgba(0,0,0,.5);--vscode-editor-foldBackground:rgba(173,214,255,.3);--vscode-editorGutter-foldingControlForeground:#424242;background-color:#fff}.light-X4nr3q .monaco-editor .bracket-highlighting-0{color:#d5a00d}.light-X4nr3q .monaco-editor .bracket-highlighting-1{color:#8140e3}.light-X4nr3q .monaco-editor .mtk1{color:#000}.light-X4nr3q .monaco-editor .lines-content .core-guide-indent{box-shadow:1px 0 transparent!important}.light-X4nr3q .monaco-editor .lines-content .core-guide-indent.indent-active{box-shadow:1px 0 transparent!important}.light-X4nr3q .monaco-editor .line-numbers{color:#a7a7b0}.light-X4nr3q .monaco-editor .marker-widget{background-color:#fff!important}.light-X4nr3q .monaco-editor .find-widget{color:#000;background-color:#fff}.light-X4nr3q .monaco-editor .find-widget .monaco-sash{background-color:#fff}.light-X4nr3q .monaco-editor .find-widget .button:not(.disabled):hover{background-color:#f8f8f8!important}.light-X4nr3q .monaco-editor .find-widget .monaco-editor .find-widget .codicon-find-selection:hover{background-color:#f8f8f8!important}.light-X4nr3q .monaco-editor .find-widget .monaco-inputbox{border:1px solid #e4e3e4!important}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"light\": `light-X4nr3q`\n};\nexport default ___CSS_LOADER_EXPORT___;\n"], "names": ["VariableTypeDTO", "ViewVariableType", "ChangeMode", "VariableE2e", "TraverseUtils", "createContext", "setValue", "getParents", "<PERSON><PERSON><PERSON>", "getStringifyPath", "deleteSelf", "VariablesValue", "param", "projectID", "version", "loading", "data", "refresh", "useRequest", "_res_memories", "MemoryApi", "handleClear", "item", "sendTeaEvent", "EVENT_NAMES", "handleReset", "classNames", "Table", "Empty", "IllustrationNoContent", "I18n", "<PERSON><PERSON><PERSON>", "IconButton", "IconCozRefresh", "value", "schema", "typeSafeJSONParse", "Select", "IconCozCrossCircleFill", "time", "dayjs", "traverse", "nodeOrNodes", "action", "traverseKey", "max<PERSON><PERSON><PERSON>", "Infinity", "<PERSON><PERSON><PERSON><PERSON>", "nodes", "Array", "node", "_node_traverseKey", "children", "BASE_ARRAY_PAIR", "getComplement", "inputTypes", "allTypes", "_pair", "type", "isArrayType", "arrayTypes", "ObjectLikeTypes", "getDtoVariable", "viewVariable", "_viewVariable_children", "_viewVariable_children1", "_viewVariable_variableType", "arrayItemType", "viewTypeToDTO", "exhaustiveCheckSimple", "safeAsyncThrow", "Boolean", "child", "JSON", "childDTO", "getGroupListByDto", "dtoGroups", "groups", "group", "baseGroupInfo", "getBaseGroupInfoByDto", "groupId", "varInfoList", "getGroupVariableListByDto", "getSubGroupListByDto", "groupName", "groupDesc", "groupExtDesc", "isReadOnly", "channel", "VariableChannel", "nanoid", "_varInfoList_map", "dtoVariable", "getViewVariableByDto", "_subGroupList_map", "subGroupList", "subGroup", "variableSchema", "baseVariable", "createBaseVariable", "_dtoVariable_Keyword", "_dtoVariable_Description", "_dtoVariable_Enable", "_dtoVariable_DefaultValue", "_dtoVariable_Channel", "_dtoVariable_EffectiveChannelList", "_dtoVariable_VariableType", "_dtoVariable_IsReadOnly", "VariableType", "convertListVariable", "subVariableType", "convertListObjectVariable", "subVariableSchema", "Error", "dTOTypeToViewType", "createVariableBySchema", "convertObjectVariable", "subMeta", "parentId", "getDefaultVariableGroupStore", "useVariableGroupsStore", "create", "devtools", "subscribeWithSelector", "set", "get", "variableGroups", "baseInfo", "variable", "produce", "state", "findGroup", "parentNode", "predicate", "options", "foundVariable", "findInGroups", "_group_subGroupList", "findInTree", "variables", "predicateIn", "optionsIn", "_variables_i_children", "i", "cloneDeep", "newVariable", "Object", "level", "_item_children", "itemIn", "res", "transformedData", "transformDto2Vo", "IS_DEV_MODE", "useLeaveWarning", "hasUnsavedChanges", "setHasUnsavedChanges", "useState", "location", "useLocation", "prevPathRef", "useRef", "resourceNavigate", "useDataNavigate", "useEffect", "currentPath", "wasInVariablePage", "handleBeforeUnload", "e", "Toast", "<PERSON><PERSON>", "window", "useInit", "reqData", "useGetVariableList", "initStore", "canEdit", "_reqData_variableGroups", "_reqData_canEdit", "_error_message", "error", "CustomError", "GroupConf", "code", "msg", "undefined", "useDestory", "clear", "VariableContext", "useVariableContext", "useContext", "VariableTreeContext", "flatVariableTreeData", "treeData", "JSONLikeTypes", "VARIABLE_TYPE_ALIAS_MAP", "Text", "Typography", "ReadonlyText", "props", "className", "generateVariableOption", "label", "display", "Number", "allVariableTypeList", "filterTypes", "list", "pre", "cur", "newOption", "disabled", "getVariableTypeList", "getCascaderVal", "originalVal", "path", "valuePath", "child<PERSON><PERSON>", "VARIABLE_TYPE_ICONS_MAP", "IconCozString", "IconCozNumber", "IconCozBoolean", "IconCozBrace", "IconCozStringBracket", "IconCozNumberBracket", "IconCozBooleanBracket", "IconCozBraceBracket", "ParamType", "onSelectChange", "readonly", "optionList", "useMemo", "cascaderVal", "<PERSON>r", "val", "newVal", "selected", "AddOperation", "_param", "onClick", "style", "subitem", "size", "color", "restProps", "IconCozAddNode", "IconAdd", "ParamOperator", "hasObjectLike", "needRenderAppendChild", "onEnabledChange", "onDelete", "onAppend", "can<PERSON>dd<PERSON><PERSON><PERSON>", "variablePageCanEdit", "Switch", "isLimited", "IconCozTrashCan", "requiredRules", "duplicateRules", "currentGroup", "findSiblings", "targetParentId", "_variable_children", "result", "siblings", "sibling", "otherGroupsRootNodes", "rootVariableList", "subGroupVarInfoList", "existKeywordRules", "useCacheField", "formApi", "useFormApi", "lastValidValueRef", "currentValue", "ParamName", "onChange", "validateExistKeyword", "cls", "FormInput", "_", "ParamDescription", "_data_description", "Input", "formatJson", "json", "convertSchemaService", "object", "paramSchema", "key", "LazyBizIDEMonacoEditor", "lazy", "Editor", "BizIDEMonacoEditor", "Suspense", "ValidateRules", "rs", "JSONEditor", "id", "visible", "onCancel", "onOk", "setSchema", "setError", "change", "useCallback", "Promise", "resolve", "Modal", "convert", "jsonString", "outputValue", "validate", "newValue", "rule", "<PERSON><PERSON><PERSON><PERSON>", "IconCozBroom", "lightStyles", "stringValue", "handler", "traverseH<PERSON><PERSON>", "context", "handlerFn", "handle", "index", "container", "parents", "currentNode", "parent", "stringifyPath", "pathItem", "re", "isOutputValueContext", "cut<PERSON>ff<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "depth", "cutOffInvalidData", "params", "<PERSON><PERSON><PERSON><PERSON>", "allowNameLength", "maxVariableCount", "exportVariableService", "originalVariable", "store", "convertNode", "_node_children", "originalNode", "_originalNode_children", "<PERSON><PERSON><PERSON><PERSON>", "getDefaultValueByType", "getEditorViewVariableJson", "defaultValue", "name", "isArray", "processChildren", "parentType", "<PERSON><PERSON><PERSON><PERSON>", "acc", "JSONImport", "rules", "jsonImport", "setJsonString", "<PERSON><PERSON><PERSON><PERSON>", "clonedTreeData", "merge", "ParamDefault", "onDefaultChange", "onImportChange", "jsonModalVisible", "setJsonModalVisible", "isRoot", "isString", "isNumber", "isBoolean", "isShowJsonImport", "CozInputNumber", "IconCozEdit", "ParamChannel", "_value_effectiveChannelList", "_value_effectiveChannelList_join", "CustomTreeNode", "couldCollapse", "hide<PERSON><PERSON>er<PERSON>eys", "collapsed", "onCollapse", "treeNodeRef", "onDescriptionChange", "description", "enabled", "IconCozArrowRight", "VariableTree", "React", "ref", "treeProps", "defaultVariableType", "defaultCollapse", "maxLimit", "createVariable", "addRootVariable", "addChildVariable", "updateVariable", "deleteVariable", "findAndModifyVariable", "useShallow", "isValueEmpty", "itemKeysWithChildren", "keys", "flatTreeData", "expandedKeys", "expandTreeNode", "collapseTreeNode", "useExpandedKeys", "setExpandedKeys", "prev", "Set", "expandedKey", "useParams", "useImperativeHandle", "disableAdd", "_value_length", "showAddButton", "onTreeNodeChange", "mode", "findResult", "parentChannel", "parentData", "Tree", "renderFullLabelProps", "_data_children_length", "currentLevelReadOnly", "variableId", "IconCozPlus", "VariableGroupParamHeader", "useGetHideKeys", "variableGroup", "hideKeys", "hideChannel", "flatGroupVariableMeta", "groupVariableMeta", "_item_effectiveChannelList_length", "hideTypeChange", "GroupCollapsibleWrapper", "groupInfo", "isOpen", "setIsOpen", "isTopLevel", "Collapsible", "VariableGroup", "_groupInfo_subGroupList", "_groupInfo_varInfoList", "onVariableChange", "_subGroup_varInfoList", "_submit", "getAllRootVariables", "checkProjectID", "SESSION_HIDDEN_KEY", "useHiddenSession", "isSessionHidden", "setIsSessionHidden", "isKeyExist", "oldValue", "localStorageService", "useChangeWarning", "isShowBanner", "setIsShowBanner", "hideSession", "showBanner", "hideBanner", "hideBannerF<PERSON>ver", "VariablesConfig", "formApiRef", "saveHistory", "getAllVariables", "isEmpty", "changeValue", "_changeValue_meta", "handleSubmit", "submit", "initValues", "curr", "_formApiRef_current", "Spin", "IconCozCross", "Form", "VariableGroupComponent", "VariablesPage", "useKnowledgeParams", "TabBar", "TabPane", "_uri_path_name", "IDENav", "useIDENavigate", "widget", "useCurrentWidgetContext", "useProjectId", "useCommitVersion", "uri", "datasetID", "KnowledgeParamsStoreProvider", "resource", "resourceID", "query", "opts", "qs", "___CSS_LOADER_EXPORT___", "module"], "mappings": "iKAgBYA,EAsBAC,EAmCKA,ECvDLC,ECFAC,ECwCFC,EAgDFC,EAaAC,EAiBAC,EAUAC,EAaAC,EAgBAC,E,0QCnID,SAASC,EAAeC,CAA2C,E,QAA3CA,CAAEC,UAAAA,CAAS,CAAEC,QAAAA,CAAO,CAAuB,CAA3CF,EACvB,CAAEG,QAAAA,CAAO,CAAEC,KAAAA,CAAI,CAAEC,QAAAA,CAAO,CAAE,CAAGC,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUA,AAAC,GAADA,EAAAA,CAAAA,AAAAA,EAAC,YAC5C,IAIOC,EAAP,OAAOA,AAAY,OAAZA,CAAAA,EAAAA,AAJK,OAAMC,EAAAA,EAAAA,CAAAA,mBAA6B,CAAC,SAC9C,WAAYP,C,EACRC,EAAU,CAAEA,QAAAA,CAAQ,EAAI,CAAC,GAAE,EAEtB,QAAQ,AAAD,GAAXK,AAAAA,KAAAA,IAAAA,EAAAA,EAAgB,EAAE,AAC3B,IAEA,IAAME,G,EAAc,kBAAOC,CAAI,EAC7B,IAAI,CAACA,EAAK,OAAO,CAIjBC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,kBAA8B,CAAE,CAC3C,WAAYX,EACZ,cAAe,WACf,OAAQ,QACR,OAAQ,kBACR,cAAe,gBACjB,GAEA,MAAMO,EAAAA,EAAAA,CAAAA,gBAA0B,CAAC,CAC/B,WAAYP,EACZ,SAAU,CAACS,EAAK,OAAO,CAAC,AAC1B,GAEAL,GACF,G,SAnB2BK,CAAI,E,iCAqB/B,IAAMG,G,EAAc,oBAClBF,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,kBAA8B,CAAE,CAC3C,WAAYX,EACZ,cAAe,WACf,OAAQ,QACR,OAAQ,kBACR,cAAe,gBACjB,GAEA,MAAMO,EAAAA,EAAAA,CAAAA,gBAA0B,CAAC,CAAE,WAAYP,CAAU,GAEzDI,GACF,G,4CAEA,MACE,UAAC,OACC,UAAWS,IACT,aACA,sCACA,2CACA,wC,SAGF,UAACC,EAAAA,EAAKA,CAAAA,CACJ,cAAe,GACf,MACE,UAACC,EAAAA,EAAKA,CAAAA,CACJ,MAAO,UAACC,EAAAA,EAAqBA,CAAAA,CAAC,UAAU,qB,GACxC,MAAOC,EAAAA,CAAAA,CAAAA,CAAM,CAAC,4B,GAGlB,WAAY,CACVf,QAAAA,EACA,WAAYC,EACZ,QAAS,CACP,CACE,MAAOc,EAAAA,CAAAA,CAAAA,CAAM,CAAC,6BACd,UAAW,UACX,MAAO,GACT,EACA,CACE,MACE,WAAC,OAAI,UAAW,oB,UACd,UAAC,QAAK,UAAW,S,SACdA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,6B,GAEV,UAACC,EAAAA,CAAOA,CAAAA,CACN,MAAO,OACP,QAASD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,kC,SAEhB,UAACE,EAAAA,EAAUA,CAAAA,CACT,MAAO,UACP,KAAM,UAACC,EAAAA,GAAcA,CAAAA,CAAAA,GACrB,KAAM,QACN,QAASR,C,QAKjB,UAAW,QACX,OAAQ,CAACS,EAAeZ,KACtB,IAAMa,EAASC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAkBd,MAAAA,EAAAA,KAAAA,EAAAA,EAAM,MAAM,QAI7C,AAAIa,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,QAAQ,AAAD,EACVD,EAIP,UAACG,EAAAA,EAAMA,CAAAA,CACL,UAAU,kBACV,MAAOH,EACP,UAAW,GACX,UAAW,GACX,aAAc,KACd,QAAS,IAAMb,EAAYC,GAC3B,UACE,UAACU,EAAAA,EAAUA,CAAAA,CACT,MAAO,aACP,MAAO,YACP,KAAM,UAACM,EAAAA,GAAsBA,CAAAA,CAAAA,GAC7B,KAAM,O,IAKhB,CACF,EACA,CACE,MAAOR,EAAAA,CAAAA,CAAAA,CAAM,CAAC,kCACd,MAAO,OACP,UAAW,cACX,MAAO,IACP,OAAQ,CAACS,EAAcjB,IACrBA,EAAK,KAAK,EAAIkB,IAAAA,IAAU,CAACD,GAAM,MAAM,CAAC,mBAC1C,EACD,AACH,C,IAIR,C,mHC7JO,SAASE,EAIdC,CAAoB,CACpBC,CAAyB,E,IACzBC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,AAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAiB,WACjBC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,AAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAWC,IACXC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,AAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAe,EAGfC,AADcC,CAAAA,MAAM,OAAO,CAACP,GAAeA,EAAc,CAACA,EAAY,A,EAChE,OAAO,CAACQ,AAAAA,IAEZ,GADAP,EAAOO,GACHH,EAAeF,EAAU,CAC3B,IAAiBM,EAAXC,EAAWD,AAAiB,OAAjBA,CAAAA,EAAAA,CAAI,CAACP,EAAY,AAAD,GAAhBO,AAAAA,KAAAA,IAAAA,EAAAA,EAAqB,EAAE,CACpCC,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,MAAM,AAAD,EAAI,GACrBX,EAASW,EAAUT,EAAQC,EAAaC,EAAUE,EAAe,EAErE,CACF,EACF,CLrBO,IAAK/C,G,CAAAA,E,4GAAAA,GAsBL,IAAKC,G,CAAAA,E,2SAAAA,GAcCoD,EAA0D,CACrE,C,KAAuD,CACvD,C,MAAyD,CACzD,C,MAAyD,CACzD,C,MAAuD,CACvD,C,MAAuD,CACxD,AAmBE,EAJcpD,EAAAA,GAAAA,CAAAA,EAAgBA,CAAAA,IAKfqD,aAAa,CAAtB,SAAuBC,CAA8B,EAM1D,MAAOC,AAL8B,IAChCH,EAAgB,GAAG,CAACI,AAAAA,GAASA,CAAK,CAAC,EAAE,KACrCJ,EAAgB,GAAG,CAACI,AAAAA,GAASA,CAAK,CAAC,EAAE,EACzC,CAEe,MAAM,CAACC,AAAAA,GAAQ,CAACH,EAAW,QAAQ,CAACG,GACtD,E,EAEgBC,WAAW,CAApB,SAAqBD,CAAsB,EAEhD,OAAOE,AADYP,EAAgB,GAAG,CAACI,AAAAA,GAASA,CAAK,CAAC,EAAE,EACtC,QAAQ,CAACC,EAC7B,EAGK,IAAMG,EAAkB,C,MAG9B,C,cMPYC,EAAiB,AAC5BC,IAEA,IAgBcC,EAcIC,EASFC,EAvCV,CAAER,KAAAA,CAAI,CAAES,cAAAA,CAAa,CAAE,CAAGC,AAlD3B,SAAuBV,CAAsB,EAIlD,OAAQA,GACN,KAAK,EACH,MAAO,CAAE,KAAM1D,EAAAA,OAAuB,AAAC,CACzC,MAAK,EACH,MAAO,CAAE,KAAMA,EAAAA,OAAuB,AAAC,CACzC,MAAK,EACH,MAAO,CAAE,KAAMA,EAAAA,KAAqB,AAAC,CACvC,MAAK,EACH,MAAO,CAAE,KAAMA,EAAAA,MAAsB,AAAC,CACxC,MAAK,EACH,MAAO,CAAE,KAAMA,EAAAA,MAAsB,AAAC,CACxC,MAAK,IACH,MAAO,CACL,KAAMA,EAAAA,IAAoB,CAC1B,cAAeA,EAAAA,OAAuB,AACxC,CACF,MAAK,IACH,MAAO,CACL,KAAMA,EAAAA,IAAoB,CAC1B,cAAeA,EAAAA,OAAuB,AACxC,CACF,MAAK,IACH,MAAO,CACL,KAAMA,EAAAA,IAAoB,CAC1B,cAAeA,EAAAA,KAAqB,AACtC,CACF,MAAK,GACH,MAAO,CACL,KAAMA,EAAAA,IAAoB,CAC1B,cAAeA,EAAAA,MAAsB,AACvC,CACF,MAAK,IACH,MAAO,CACL,KAAMA,EAAAA,IAAoB,CAC1B,cAAeA,EAAAA,MAAsB,AACvC,CACF,SAGE,MAFAqE,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAsBX,GACtBY,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAgB,+BAAmC,OAALZ,IACvC,CAAE,KAAM1D,EAAAA,MAAsB,AAAC,CAC1C,CACF,EAKgD+D,EAAa,IAAI,EAEzD5B,EAA4B,CAChC,KAAM4B,EAAa,IAAI,CACvB,OAAQA,EAAa,OAAO,CAC5B,YAAaA,EAAa,WAAW,EAAI,GACzCL,KAAAA,EACA,SAAUa,CAAAA,CAAQR,EAAa,QAAQ,CACvC,OAAQ,EACV,EA2BA,OAxBIL,IAAS1D,EAAAA,IAAoB,EAAImE,IAC/BA,IAAkBnE,EAAAA,MAAsB,CAC1CmC,EAAO,MAAM,CAAG,CACd,KAAMnC,EAAAA,MAAsB,CAC5B,OAAQ,AAAqB,OAArBgE,CAAAA,EAAAA,EAAa,QAAQ,AAAD,GAApBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAuB,GAAG,CAACQ,AAAAA,GAE1BC,KAAK,KAAK,CAACC,AADDZ,EAAeU,GACL,MAAM,EAAI,MAEzC,EAEArC,EAAO,MAAM,CAAG,CACd,KAAMgC,CACR,GAKAT,IAAS1D,EAAAA,MAAsB,EACjCmC,CAAAA,EAAO,MAAM,CAAG,AAAqB,OAArB8B,CAAAA,EAAAA,EAAa,QAAQ,AAAD,GAApBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAuB,GAAG,CAACO,AAAAA,GAElCC,KAAK,KAAK,CAACC,AADDZ,EAAeU,GACL,MAAM,EAAI,MACtC,EAGI,CACL,QAAST,EAAa,IAAI,CAC1B,QAASA,EAAa,OAAO,CAC7B,aAAcG,AAAyB,OAAzBA,CAAAA,EAAAA,EAAa,YAAY,AAAD,GAAxBA,AAAAA,KAAAA,IAAAA,EAAAA,EAA6B,EAC3C,aAAcH,EAAa,YAAY,CACvC,YAAaA,EAAa,WAAW,CACrC,qBAAsBA,EAAa,oBAAoB,CACvD,OAAQQ,CAAAA,CAAQR,EAAa,OAAO,CACpC,WAAYQ,CAAAA,CAAQR,EAAa,QAAQ,CACzC,OAAQU,KAAK,SAAS,CAACtC,EAAQ,KAAM,EACvC,CACF,ECxGawC,EAAoB,AAC/BC,GAkBOC,AAhBQD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAW,GAAG,CAACE,AAAAA,IAC5B,IAAMC,EAAgBC,EAAsBF,GACtC,CAAEG,QAAAA,CAAO,CAAE,CAAGF,EACdG,EAAcC,GAA0B,CAC5CL,MAAAA,EACAG,QAAAA,CACF,GACA,MAAO,mBACFF,GAAAA,CACHG,YAAAA,EACA,aAAcE,GAAqB,CACjCN,MAAAA,EACAG,QAAAA,CACF,E,EAEJ,EAAC,GACgB,EAAE,CAGfD,EAAwB,AAC5BF,IAEA,GAAM,CACJ,UAAWO,EAAY,EAAE,CACzB,UAAWC,EAAY,EAAE,CACzB,aAAcC,EAAe,EAAE,CAC/B,WAAYC,EAAa,EAAK,CAC9B,eAAgBC,EAAUC,EAAAA,EAAAA,CAAAA,MAAsB,CACjD,CAAGZ,EAEJ,MAAO,CACLG,QAFcU,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAGdN,UAAAA,EACAC,UAAAA,EACAC,aAAAA,EACAE,QAAAA,EACAD,WAAAA,EACA,IAAKV,CACP,CACF,EAEMK,GAA4B,AAAC,I,IAS/BS,EAT+B,CACjCd,MAAAA,CAAK,CACLG,QAAAA,CAAO,CAIR,GACO,CAAE,YAAaC,EAAc,EAAE,CAAE,CAAGJ,EAC1C,OACEc,AACoCX,OADpCW,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAa,GAAG,CAACC,AAAAA,GACfC,GAAqBD,EAAaZ,GAAQ,GAD5CW,AAAAA,KAAAA,IAAAA,EAAAA,EAEK,EAAE,AAEX,EAEMR,GAAuB,AAAC,I,IAS1BW,EAT0B,CAC5BjB,MAAAA,CAAK,CACLG,QAAAA,CAAO,CAIR,GACO,CAAE,aAAce,EAAe,EAAE,CAAE,CAAGlB,EAC5C,OACEiB,A,OAAAA,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAc,GAAG,CAACE,AAAAA,GAAa,mBAC1BjB,EAAsB,mBACpBiB,GAAAA,CACH,eAAgBnB,EAAM,cAAc,A,MAEtCG,QAAAA,EACA,YAAaE,GAA0B,CACrC,MAAOc,EACPhB,QAAAA,CACF,GACA,aAAc,EAAE,A,MAVlBc,AAAAA,KAAAA,IAAAA,EAAAA,EAWO,EAAE,AAEb,EAEO,SAASD,GACdD,CAAmC,CACnCZ,CAAe,EAEf,IAAMiB,EAAiB9D,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EACrByD,EAAY,MAAM,EAAI,MAGlB,CAAEnC,KAAAA,CAAI,CAAE,CAAGwC,EAEXC,EAAeC,AA0EvB,SAA4BxF,CAM3B,E,IAGSyF,EACOC,EACJC,EACKC,EACLC,EACaC,EACRC,EACJC,EAhBchG,CAC1BiF,YAAAA,CAAW,CACXZ,QAAAA,CAAO,CAIR,CAN2BrE,EAO1B,MAAO,CACL,WAAY+E,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IACZ,KAAMU,AAAmB,OAAnBA,CAAAA,EAAAA,EAAY,OAAO,AAAD,GAAlBA,AAAAA,KAAAA,IAAAA,EAAAA,EAAuB,GAC7B,YAAaC,AAAuB,OAAvBA,CAAAA,EAAAA,EAAY,WAAW,AAAD,GAAtBA,AAAAA,KAAAA,IAAAA,EAAAA,EAA2B,GACxC,QAASC,AAAkB,OAAlBA,CAAAA,EAAAA,EAAY,MAAM,AAAD,GAAjBA,AAAAA,KAAAA,IAAAA,GAAAA,EACT,aAAcC,AAAwB,OAAxBA,CAAAA,EAAAA,EAAY,YAAY,AAAD,GAAvBA,AAAAA,KAAAA,IAAAA,EAAAA,EAA4B,GAC1C,QAASC,AAAmB,OAAnBA,CAAAA,EAAAA,EAAY,OAAO,AAAD,GAAlBA,AAAAA,KAAAA,IAAAA,EAAAA,EAAuBf,EAAAA,EAAAA,CAAAA,MAAsB,CACtD,qBAAsBgB,AAAgC,OAAhCA,CAAAA,EAAAA,EAAY,oBAAoB,AAAD,GAA/BA,AAAAA,KAAAA,IAAAA,EAAAA,EAAoC,EAAE,CAC5D,aAAcC,AAAwB,OAAxBA,CAAAA,EAAAA,EAAY,YAAY,AAAD,GAAvBA,AAAAA,KAAAA,IAAAA,EAAAA,EAA4BE,EAAAA,EAAAA,CAAAA,UAAuB,CACjE,SAAUD,AAAsB,OAAtBA,CAAAA,EAAAA,EAAY,UAAU,AAAD,GAArBA,AAAAA,KAAAA,IAAAA,GAAAA,EACV3B,QAAAA,EACA,SAAU,GACV,KAAM,CACJ,UAAW,EACb,CACF,CACF,EAjG0C,CACtCY,YAAAA,EACAZ,QAAAA,CACF,UAEA,AAAIvB,IAAS1D,EAAAA,IAAoB,CACxB8G,AA6FX,SACEX,CAAiD,CACjDD,CAAiC,EAIjC,GAAM,CAAE,KAAMa,CAAe,CAAE,CAFLb,EAAe,MAAM,QAI/C,AAAIa,IAAoB/G,EAAAA,MAAsB,CACrCgH,AA+BX,SACEb,CAAiD,CACjDD,CAAiC,EAEjC,IAAMe,EAAoBf,EAAe,MAAM,CAE/C,GAAI,CAACe,EACH,MAAM,AAAIC,MAAM,0CAGlB,GAAM,CAAE,KAAMH,CAAe,CAAE,CAAGE,EAElC,MAAO,mBACFd,GAAAA,CACH,KAAMgB,GAAkBnH,EAAAA,IAAoB,CAAE,CAC5C,cAAe+G,CACjB,GACA,SAAU9D,MAAM,OAAO,CAACgE,EAAkB,MAAM,EAC5CA,EAAkB,MAAM,CAAC,GAAG,CAAC9E,AAAAA,GAC3BiF,GAAuBjF,EAAQ,CAC7B,QAASgE,EAAa,OAAO,CAC7B,SAAUA,EAAa,UAAU,AACnC,IAEF,EAAE,A,EAEV,EAzDqCA,EAAcD,GAG1C,mBACFC,GAAAA,CACH,KAAMgB,GAAkBjB,EAAe,IAAI,CAAE,CAC3C,cAAea,CACjB,GACA,SAAU,EAAE,A,EAEhB,EAhH+BZ,EAAcD,GAGvCxC,IAAS1D,EAAAA,MAAsB,CAC1BqH,AA+KX,SACElB,CAAiD,CACjDD,CAAiC,EAEjC,IAAM/D,EAAS+D,EAAe,MAAM,EAAI,EAAE,CAE1C,MAAO,mBACFC,GAAAA,CACH,KAAMgB,GAAkBjB,EAAe,IAAI,EAC3C,SAAUjD,MAAM,OAAO,CAACd,GACpBA,EAAO,GAAG,CAACmF,AAAAA,GACTF,GAAuBE,EAAS,CAC9B,QAASnB,EAAa,OAAO,CAC7B,SAAUA,EAAa,UAAU,AACnC,IAEF,EAAE,A,EAEV,EAjMiCA,EAAcD,GAGtC,mBACFC,GAAAA,CACH,KAAMgB,GAAkBjB,EAAe,IAAI,EAC3C,SAAU,EAAE,A,EAEhB,CAEO,SAASiB,GACdzD,CAAqB,E,GACrB,CACES,cAAAA,CAAa,CAGd,CAJD,uDAII,CAAC,EAEL,OAAQT,GACN,KAAK1D,EAAAA,OAAuB,CAC1B,OAAOC,EAAAA,OAAwB,AACjC,MAAKD,EAAAA,OAAuB,CAC1B,OAAOC,EAAAA,OAAwB,AACjC,MAAKD,EAAAA,KAAqB,CACxB,OAAOC,EAAAA,MAAuB,AAChC,MAAKD,EAAAA,MAAsB,CACzB,OAAOC,EAAAA,MAAuB,AAChC,MAAKD,EAAAA,MAAsB,CACzB,OAAOC,EAAAA,MAAuB,AAChC,MAAKD,EAAAA,IAAoB,CACvB,GAAI,CAACmE,EACH,MAAM,AAAI+C,MACP,kDAA+D,OAAd/C,IAItD,OAAQA,GACN,KAAKnE,EAAAA,OAAuB,CAC1B,OAAOC,EAAAA,YAA6B,AACtC,MAAKD,EAAAA,OAAuB,CAC1B,OAAOC,EAAAA,YAA6B,AACtC,MAAKD,EAAAA,KAAqB,CACxB,OAAOC,EAAAA,WAA4B,AACrC,MAAKD,EAAAA,MAAsB,CACzB,OAAOC,EAAAA,WAA4B,AACrC,MAAKD,EAAAA,MAAsB,CACzB,OAAOC,EAAAA,WAA4B,AACrC,MAAKD,EAAAA,IAAoB,CAIvB,MAHAsE,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EACG,gDAAuDH,MAAAA,CAART,EAAK,KAAiB,OAAdS,IAEnDlE,EAAAA,MAAuB,AAChC,SAGE,MAFAoE,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAsBF,GACtBG,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAgB,8BAAqCH,MAAAA,CAART,EAAK,KAAiB,OAAdS,IAC9ClE,EAAAA,MAAuB,AAClC,CACF,QAGE,MAFAoE,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAsBX,GACtBY,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAgB,8BAAqCH,MAAAA,CAART,EAAK,KAAiB,OAAdS,IAC9ClE,EAAAA,MAAuB,AAClC,CACF,CAoIA,SAASmH,GACPE,CAA0B,CAC1B1G,CAMC,E,GANDA,CACEqE,QAAAA,CAAO,CACPsC,SAAAA,CAAQ,CAIT,CAND3G,EAQA,OAAOkF,GACL,CACE,QAASwB,EAAQ,IAAI,CACrB,YAAaA,EAAQ,WAAW,CAChC,OAAQ7C,KAAK,SAAS,CAAC6C,GACvB,OAAQ,GACR,WAAYA,EAAQ,QAAQ,AAC9B,EACArC,EAEJ,CCtRO,IAAMuC,GAA+B,IAA4B,EACtE,QAAS,GACT,eAAgB,EAAE,AACpB,GAkDaC,GAAyBC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAGpCC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EACEC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAsB,CAACC,EAAKC,IAAS,mBAChCN,MAAAA,CACH,kBAAmBO,AAAAA,GACjBF,EAAI,CAAEE,eAAAA,CAAe,EAAG,GAAO,qBACjC,eAAgBC,AAAAA,GAAa,EAC3B,WAAYrC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IACZ,KAAMqC,EAAS,YAAY,CAC3B,KAAM,GACN,QAAS,GACT,SAAU,EAAE,CACZ,aAAc,GACd,YAAa,GACb,QAASA,EAAS,OAAO,CACzB,qBAAsB,EAAE,CACxB,aAAcnB,EAAAA,EAAAA,CAAAA,UAAuB,CACrC,SAAU,GACV,QAASmB,EAAS,OAAO,CACzB,SAAUA,EAAS,QAAQ,CAC3B,KAAM,CACJ,UAAW,EACb,CACF,GACA,gBAAiBC,AAAAA,IACfJ,EACEK,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAA6BC,AAAAA,IAC3B,IAAMC,EAAYD,EAAM,cAAc,CAAC,IAAI,CACzC7G,AAAAA,GAAQA,EAAK,OAAO,GAAK2G,EAAS,OAAO,EAE3C,IAAI,CAACG,EAGLA,EAAU,WAAW,CAAC,IAAI,CAAC,mBACtBH,GAAAA,CACH,QAASG,EAAU,OAAO,A,IAE5BN,IAAM,UAAU,CAAC,CACf,UAAWM,EAAU,WAAW,CAChC,MAAO,EACP,SAAU,EACZ,EACF,GACA,GACA,kBAEJ,EACA,iBAAkBH,AAAAA,IAChBH,IAAM,qBAAqB,CACzBG,EAAS,OAAO,CAChB3G,AAAAA,GAAQA,EAAK,UAAU,GAAK2G,EAAS,QAAQ,CAC7C,CACE,eAAgBI,AAAAA,IACdA,EAAW,QAAQ,CAAC,IAAI,CAACJ,GACzBH,IAAM,UAAU,CAAC,CACf,UAAWO,EAAW,QAAQ,CAC9B,MAAQA,AAAAA,CAAAA,EAAW,IAAI,CAAC,KAAK,EAAI,GAAK,EACtC,SAAUA,EAAW,UAAU,AACjC,EACF,EACA,KAAM,kBACR,EAEJ,EACA,eAAgBJ,AAAAA,IACdH,IAAM,qBAAqB,CACzBG,EAAS,OAAO,CAChB3G,AAAAA,GAAQA,EAAK,UAAU,GAAK2G,EAAS,UAAU,CAC/C,CAAE,eAAgB,GAAM,KAAM,gBAAiB,EAEnD,EACA,sBAAuB,CAAChD,EAASqD,EAAWC,KAC1C,IAAIC,EAAiC,KAwDrC,OAtDAX,EACEK,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAA6BC,AAAAA,IAC3B,IAAMM,EAAe,AAAC5D,IACpB,IAAK,IAAMC,KAASD,EAAQ,C,IAMtB6D,EALJ,GAAI5D,EAAM,OAAO,GAAKG,GAChB0D,EAAW7D,EAAM,WAAW,CAAEwD,EAAWC,IAI3C,CAAkB,OAAlBG,CAAAA,EAAAA,EAAM,YAAY,AAAD,GAAjBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAoB,MAAM,AAAD,GACvBD,EAAa3D,EAAM,YAAY,EAJjC,MAAO,EAQb,CACA,MAAO,EACT,EAEM6D,EAAa,CACjBC,EACAC,EACAC,KAKA,IAAK,IAWCC,EAXGC,EAAI,EAAGA,EAAIJ,EAAU,MAAM,CAAEI,IAAK,CACzC,GAAIH,EAAYD,CAAS,CAACI,EAAE,EAQ1B,OAPAR,EAAgBS,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUL,CAAS,CAACI,EAAE,EAClCF,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAW,cAAc,AAAD,GAC1BF,EAAU,MAAM,CAACI,EAAG,GAElBF,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAW,cAAc,AAAD,GAC1BA,EAAU,cAAc,CAACF,CAAS,CAACI,EAAE,EAEhC,GAET,GAAI,CAAqB,OAArBD,CAAAA,EAAAA,CAAS,CAACC,EAAE,CAAC,QAAQ,AAAD,GAApBD,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAuB,MAAM,AAAD,GAE5BJ,EAAWC,CAAS,CAACI,EAAE,CAAC,QAAQ,CAAEH,EAAaC,GAE/C,MAAO,EAGb,CACA,MAAO,EACT,EAEAL,EAAaN,EAAM,cAAc,CACnC,GACA,GACAI,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAS,IAAI,AAAD,GAAK,sBAGZC,CACT,EACA,eAAgBU,AAAAA,IACdpB,IAAM,qBAAqB,CACzBoB,EAAY,OAAO,CACnBjB,AAAAA,GAAYA,EAAS,UAAU,GAAKiB,EAAY,UAAU,CAC1D,CACE,KAAM,iBACN,eAAgBjB,AAAAA,IACdkB,OAAO,MAAM,CAAClB,EAAUiB,GACxBpB,IAAM,UAAU,CAAC,CACf,UAAW,CAACG,EAAS,CACrB,MAAOA,EAAS,IAAI,CAAC,KAAK,CAC1B,SAAUA,EAAS,QAAQ,AAC7B,EACF,CACF,EAEJ,EACA,WAAY,AAAC,I,GAAA,CAAEW,UAAAA,CAAS,CAAEQ,MAAAA,EAAQ,CAAC,CAAE7B,SAAAA,EAAW,EAAE,CAAE,GAClDqB,EAAU,OAAO,CAACtH,AAAAA,I,IAIZ+H,CAHJ/H,CAAAA,EAAK,IAAI,CAAC,KAAK,CAAG8H,EAClB9H,EAAK,IAAI,CAAC,aAAa,CAAGuC,EAAgB,QAAQ,CAACvC,EAAK,IAAI,EAC5DA,EAAK,QAAQ,CAAGiG,EACZ,CAAa,OAAb8B,CAAAA,EAAAA,EAAK,QAAQ,AAAD,GAAZA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAe,MAAM,AAAD,GACtBvB,IAAM,UAAU,CAAC,CACf,UAAWxG,EAAK,QAAQ,CACxB,MAAO8H,EAAQ,EACf,SAAU9H,EAAK,UAAU,AAC3B,EAEJ,EACF,EACA,YAAa,KACXuG,EACEK,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAA6BC,AAAAA,IAC3BA,EAAM,cAAc,CAAC,OAAO,CAAC7G,AAAAA,IAC3BmB,EAASnB,EAAK,WAAW,CAAEgI,AAAAA,IACzBA,EAAO,IAAI,CAAC,SAAS,CAAG,EAC1B,EACF,EACF,GACA,GACA,cAEJ,EACA,oBAAqB,KACnB,GAAM,CAAEvB,eAAAA,CAAc,CAAE,CAAGD,IACrByB,EAAkB,EAAE,CAQ1B,OAPA9G,EACEsF,EACAzG,AAAAA,IACEiI,EAAI,IAAI,IAAIjI,EAAK,WAAW,CAC9B,EACA,gBAEKiI,CACT,EACA,gBAAiB,KACf,GAAM,CAAExB,eAAAA,CAAc,CAAE,CAAGD,IACrBc,EAAYb,EAAe,GAAG,CAACzG,AAAAA,GAAQA,EAAK,WAAW,EAAE,IAAI,GAC7DiI,EAAkB,EAAE,CAQ1B,OAPA9G,EACEmG,EACAtH,AAAAA,IACEiI,EAAI,IAAI,CAACjI,EACX,EACA,YAEKiI,CACT,EACA,gBAAiBvI,AAAAA,IACf,IAAMwI,EAAkB7E,EAAkB3D,GAK1C,OAHAwI,EAAgB,OAAO,CAAC1E,AAAAA,IACtBgD,IAAM,UAAU,CAAC,CAAE,UAAWhD,EAAM,WAAW,AAAC,EAClD,GACO0E,CACT,EACA,eAAgB,AAACvB,GAAuBnE,EAAemE,GACvD,UAAWjH,AAAAA,IACT,GAAM,CAAEyI,gBAAAA,CAAe,CAAE,CAAG3B,IAE5BD,EACE,CACE,eAHoB4B,EAAgBzI,EAAK,cAAc,EAIvD,QAASA,EAAK,OAAO,AACvB,EACA,GACA,YAEJ,EACA,MAAO,KACL6G,EAAI,WAAKL,MAAkC,GAAO,QACpD,C,IAEF,CACE,QAASkC,GACT,KAAM,0BACR,I,eC7USC,GAAkB,KAC7B,GAAM,CAACC,EAAmBC,EAAqB,CAAGC,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IACrDC,EAAWC,AAAAA,GAAAA,GAAAA,WAAAA,AAAAA,IACXC,EAAcC,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,EAAOH,EAAS,QAAQ,EACtCI,EAAmBC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IA+CzB,MA7CAC,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACR,IAAMC,EAAcP,EAAS,QAAQ,CAC/BQ,EAAoBN,EAAY,OAAO,CAAC,QAAQ,CAAC,cAEjDO,EAAqB,AAACC,IACtBb,GACFa,EAAE,cAAc,EAEpB,EAgCA,OA7BEF,GACA,CAACD,EAAY,QAAQ,CAAC,eACtBV,GAEAc,EAAAA,EAAAA,CAAAA,OAAa,CAAC,CACZ,QACE,WAAC,O,UACC,UAAC,QAAK,UAAU,uC,SACb5I,EAAAA,CAAAA,CAAAA,CAAM,CAAC,iC,GAEV,UAAC6I,EAAAA,EAAMA,CAAAA,CACL,MAAM,UACN,QAAS,K,IACPR,C,AAA2B,QAA3BA,CAAAA,EAAAA,EAAiB,UAAU,AAAD,GAA1BA,AAAAA,KAAAA,IAAAA,GAAAA,EAAAA,IAAAA,CAAAA,EAA8B,aAChC,E,SAECrI,EAAAA,CAAAA,CAAAA,CAAM,CAAC,sC,KAIhB,GAGEwI,EAAY,QAAQ,CAAC,eAAiBV,GACxCgB,OAAO,gBAAgB,CAAC,eAAgBJ,GAG1CP,EAAY,OAAO,CAAGK,EAEf,KACLM,OAAO,mBAAmB,CAAC,eAAgBJ,EAC7C,CACF,EAAG,CAACT,EAAUH,EAAkB,EAEzB,CACLA,kBAAAA,EACAC,qBAAAA,CACF,CACF,E,eCnDagB,GAAU,CAAChK,EAAoBC,KAC1C,GAAM,CAAE,KAAMgK,CAAO,CAAE/J,QAAAA,CAAO,CAAE,CAAGgK,GAAmBlK,EAAWC,GAC3D,CAAEkK,UAAAA,CAAS,CAAE,CAAGvD,KAetB,MAbA4C,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACR,IAAItJ,GAIJ,GAAM,CAAEgH,eAAAA,CAAc,CAAEkD,QAAAA,CAAO,CAAE,CAAGH,EAEpCE,EAAU,CACRjD,eAAAA,EACA,QAASkD,GAAW,CAACnK,CACvB,GACF,EAAG,CAACC,EAAQ,EAEL,CACLA,QAAAA,CACF,CACF,EAEMgK,GAAqB,CACzBlK,EACAC,KASA,IAyDoBoK,EACPC,EAGJC,EA7DH,CACJ,KAAMN,CAAO,CACb/J,QAAAA,CAAO,CACPsK,MAAAA,CAAK,CACN,CAAGnK,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUA,AAAC,GAADA,EAAAA,CAAAA,AAAAA,EACZ,YACE,GAAI,CAACL,EACH,MAAM,IAAIyK,GAAAA,EAAWA,CACnB,0BACA,6BAQJ,GAAM,CAAEC,UAAAA,CAAS,CAAEC,KAAAA,CAAI,CAAE,QAASP,CAAO,CAAEQ,IAAAA,CAAG,CAAE,CALpC,MAAMrK,EAAAA,EAAAA,CAAAA,sBAAgC,CAAC,CACjD,UAAWP,EACX,QAASC,GAAW4K,KAAAA,CACtB,UAIA,AAAIF,AAAS,IAATA,EACK,CACL,MAAOC,EACP,KAAM,CACJ,eAAgB,EAAE,CAClB,QAAS,EACX,EACA,QAAS,EACX,EAGGF,EAUE,CACL,eAAgBA,EAChBN,QAAAA,CACF,EAZS,CACL,KAAM,CACJ,eAAgB,EAAE,CAClBA,QAAAA,CACF,EACA,QAAS,EACX,CAOJ,GACA,CACE,OAAQ,GACR,QAAS,KACPP,EAAAA,EAAAA,CAAAA,KAAW,CAAC,CACV,QAAS5I,EAAAA,CAAAA,CAAAA,CAAM,CAAC,iBAChB,UAAW,EACb,EACF,CACF,GAEF,MAAO,CACL,KAAM,CACJ,eAAgBoJ,AAAuB,OAAvBA,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAS,cAAc,AAAD,GAAtBA,AAAAA,KAAAA,IAAAA,EAAAA,EAA2B,EAAE,CAC7C,QAASC,AAAgB,OAAhBA,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAS,OAAO,AAAD,GAAfA,AAAAA,KAAAA,IAAAA,GAAAA,CACX,EACApK,QAAAA,EACA,MAAOqK,AAAc,OAAdA,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAO,OAAO,AAAD,GAAbA,AAAAA,KAAAA,IAAAA,EAAAA,EAAkB,EAC3B,CACF,ECvGaO,GAAa,KACxB,GAAM,CAAEC,MAAAA,CAAK,CAAE,CAAGnE,KAOlB,MANA4C,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EACE,IAAM,KACJuB,GACF,EACA,CAACA,EAAM,EAEF,CACLA,MAAAA,CACF,CACF,ECLaC,GAAkBxL,AAAAA,GAAAA,EAAAA,aAAAA,AAAAA,EAAmC,CAChE,OAAQ,EAAE,AACZ,GAEayL,GAAqB,IAAMC,AAAAA,GAAAA,EAAAA,UAAAA,AAAAA,EAAWF,ICTtCG,GAAsB3L,AAAAA,GAAAA,EAAAA,aAAAA,AAAAA,EAGhC,CACD,QAAS,GACT,UAAW,EAAE,AACf,GCwEa4L,GAAuB,AAACC,IACnC,IAAM3C,EAAkB,EAAE,CAQ1B,OAPA9G,EACEyJ,EACA5K,AAAAA,IACEiI,EAAI,IAAI,CAACjI,EACX,EACA,YAEKiI,CACT,Eb3FO,IAAKrJ,I,CAAAA,E,qIAAAA,GAUCiM,GAAgB,CAC3BlM,EAAAA,MAAuB,CACvBA,EAAAA,WAA4B,CAC5BA,EAAAA,YAA6B,CAC7BA,EAAAA,WAA4B,CAC5BA,EAAAA,WAA4B,CAC5BA,EAAAA,YAA6B,CAC9B,C,ecVYmM,GAA4D,CACvE,CAACnM,EAAAA,MAAuB,CAAC,CAAE,SAC3B,CAACA,EAAAA,OAAwB,CAAC,CAAE,UAC5B,CAACA,EAAAA,OAAwB,CAAC,CAAE,UAC5B,CAACA,EAAAA,MAAuB,CAAC,CAAE,SAC3B,CAACA,EAAAA,MAAuB,CAAC,CAAE,SAC3B,CAACA,EAAAA,WAA4B,CAAC,CAAE,gBAChC,CAACA,EAAAA,YAA6B,CAAC,CAAE,iBACjC,CAACA,EAAAA,YAA6B,CAAC,CAAE,iBACjC,CAACA,EAAAA,WAA4B,CAAC,CAAE,gBAChC,CAACA,EAAAA,WAA4B,CAAC,CAAE,eAClC,ECjBM,CAAEoM,KAAAA,EAAI,CAAE,CAAGC,EAAAA,EAAUA,CAEdC,GAAe,AAACC,IAC3B,GAAM,CAAEtK,MAAAA,CAAK,CAAEuK,UAAAA,CAAS,CAAE,CAAGD,EAC7B,MACE,UAACH,GAAAA,CACC,UAAW3K,IACT,6CACA+K,GAEF,SAAQ,G,SAEPvK,C,EAGP,ECVawK,GAAyB,CACpChJ,EACAiJ,EACAC,IACI,EACJ,MAAOC,OAAOnJ,GACd,MAAOiJ,GAASP,EAAuB,CAAC1I,EAAK,CAC7C,QAASkJ,GAAWD,GAASP,EAAuB,CAAC1I,EAAK,AAC5D,GAeaoJ,GAAiD,CAC5DJ,GAAuBzM,EAAAA,MAAuB,EAC9CyM,GAAuBzM,EAAAA,OAAwB,EAC/CyM,GAAuBzM,EAAAA,OAAwB,EAC/CyM,GAAuBzM,EAAAA,MAAuB,EAC9CyM,GAAuBzM,EAAAA,MAAuB,EAC9CyM,GAAuBzM,EAAAA,WAA4B,EACnDyM,GAAuBzM,EAAAA,YAA6B,EACpDyM,GAAuBzM,EAAAA,YAA6B,EACpDyM,GAAuBzM,EAAAA,WAA4B,EACnDyM,GAAuBzM,EAAAA,WAA4B,EACpD,CAEK8M,GAAc,CAClBC,EACAzE,KAEA,GAAM,CAAEa,MAAAA,CAAK,CAAE,CAAGb,GAAW,CAAC,EAE9B,OAAOyE,EAAK,MAAM,CAAC,CAACC,EAAKC,KACvB,IAAMC,EAAY,WAAKD,EAEnBC,CAAAA,EAAU,QAAQ,EACpBA,CAAAA,EAAU,QAAQ,CAAGJ,GAAYI,EAAU,QAAQ,CAAE5E,EAAO,EAM9D,IAAM6E,EAAW7I,CAAAA,CACf6E,CAAAA,GACEA,GAxDY,GAyDZvF,EAAgB,QAAQ,CAACgJ,OAAOM,EAAU,KAAK,EAAC,EAGpD,MAAO,IACFF,EACH,mBACKE,GAAAA,CACHC,SAAAA,C,GAEH,AACH,EAAG,EAAE,CACP,EAMaC,GAAsB9E,AAAAA,GACjCwE,GAAYD,GAAqBvE,GAKtB+E,GAAiB,SAC5BC,CAAW,CACXP,CAAI,E,IACJQ,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,AAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAA+B,EAAE,CAE7BC,EAAY,IAAID,EAAK,CAiBzB,OAhBAR,EAAK,OAAO,CAAC1L,AAAAA,IACX,GAAIA,EAAK,QAAQ,CAAE,CACjB,IAAMoM,EAAYJ,GAAeC,EAAajM,EAAK,QAAQ,CAAE,IACxDmM,EACHnM,EAAK,KAAK,CACX,EACD,GAAIoM,CAAS,CAACA,EAAU,MAAM,CAAG,EAAE,GAAKH,EAAa,CACnDE,EAAYC,EACZ,MACF,CACF,MAAO,GAAIpM,EAAK,KAAK,GAAKiM,EAAa,CACrCE,EAAU,IAAI,CAACF,GACf,MACF,CACF,GAEOE,CACT,EC9FaE,GAA+D,CAC1E,CAAC1N,EAAAA,MAAuB,CAAC,CAAE,UAAC2N,EAAAA,GAAaA,CAAAA,CAAAA,GACzC,CAAC3N,EAAAA,OAAwB,CAAC,CAAE,UAAC4N,EAAAA,GAAaA,CAAAA,CAAAA,GAC1C,CAAC5N,EAAAA,OAAwB,CAAC,CAAE,UAAC6N,EAAAA,GAAcA,CAAAA,CAAAA,GAC3C,CAAC7N,EAAAA,MAAuB,CAAC,CAAE,UAAC4N,EAAAA,GAAaA,CAAAA,CAAAA,GACzC,CAAC5N,EAAAA,MAAuB,CAAC,CAAE,UAAC8N,EAAAA,GAAYA,CAAAA,CAAAA,GACxC,CAAC9N,EAAAA,WAA4B,CAAC,CAAE,UAAC+N,EAAAA,GAAoBA,CAAAA,CAAAA,GACrD,CAAC/N,EAAAA,YAA6B,CAAC,CAAE,UAACgO,EAAAA,GAAoBA,CAAAA,CAAAA,GACtD,CAAChO,EAAAA,YAA6B,CAAC,CAAE,UAACiO,EAAAA,GAAqBA,CAAAA,CAAAA,GACvD,CAACjO,EAAAA,WAA4B,CAAC,CAAE,UAACgO,EAAAA,GAAoBA,CAAAA,CAAAA,GACrD,CAAChO,EAAAA,WAA4B,CAAC,CAAE,UAACkO,EAAAA,GAAmBA,CAAAA,CAAAA,EACtD,ECDe,SAASC,GAAUxN,CAKjB,E,GALiBA,CAChCI,KAAAA,CAAI,CACJqN,eAAAA,CAAc,CACdjF,MAAAA,CAAK,CACLkF,SAAAA,CAAQ,CACO,CALiB1N,EAM1B2N,EAAaC,AAAAA,GAAAA,EAAAA,OAAAA,AAAAA,EAAQ,IAAMnB,GAAoB,CAAEjE,MAAAA,CAAM,GAAI,CAACA,EAAM,EAElEqF,EAAcD,AAAAA,GAAAA,EAAAA,OAAAA,AAAAA,EAClB,IAAMlB,GAAetM,EAAK,IAAI,CAAE8L,IAChC,CAAC9L,EAAK,IAAI,CAAC,EAGb,OAAOsN,EACL,UAAC/B,GAAYA,CACX,UAAU,SACV,MAAOH,EAAuB,CAACpL,EAAK,IAAI,CAAC,A,GAG3C,UAAC0N,EAAAA,EAAQA,CAAAA,CACP,YAAa5M,EAAAA,CAAAA,CAAAA,CAAM,CAAC,uCACpB,SAAUwM,EACV,SAAUK,AAAAA,IACR,IAAIC,EAASD,EACT1L,MAAM,OAAO,CAAC0L,IAChBC,CAAAA,EAASD,CAAG,CAACA,EAAI,MAAM,CAAG,EAAE,AAAD,EAE7BN,MAAAA,GAAAA,EAAiBO,EACnB,EACA,UAAU,yBACV,YAAY,QACZ,cAAeC,AAAAA,GACb,AAAK5L,MAAM,OAAO,CAAC4L,GAKjB,WAAC,OAAI,UAAU,kC,UACZlB,EAAuB,CAACkB,CAAQ,CAACA,EAAS,MAAM,CAAG,EAAE,CAAC,CACvD,UAAC,OAAI,UAAU,W,SACZzC,EAAuB,CAACyC,CAAQ,CAACA,EAAS,MAAM,CAAG,EAAE,CAAC,A,MAPpD,KAYX,SAAUN,EACV,MAAOE,C,EAGb,CjB1EO,IAAKtO,I,CAAAA,E,qDAAAA,G,6BkBiBG,SAAS2O,GAAaC,CAAAA,E,GAAAA,CACnCT,SAAAA,CAAQ,CACRU,QAAAA,CAAO,CACPvC,UAAAA,CAAS,CACTwC,MAAAA,CAAK,CACL7B,SAAAA,CAAQ,CACR8B,QAAAA,EAAU,EAAK,CACfC,KAAAA,CAAI,CACJC,MAAAA,CAAK,CAEa,CAViBL,EAShCM,EAAAA,AAAAA,GAAAA,GAAAA,CAAAA,AAAAA,EATgCN,EAAAA,CACnCT,WACAU,UACAvC,YACAwC,QACA7B,WACA8B,UACAC,OACAC,Q,SAGA,AAAId,EACK,KAIP,UAACtM,EAAAA,EAAUA,CAAAA,CACT,cAAaqN,CAAS,CAAC,cAAc,CACrC,QAASL,EACT,UAAY,GAERvC,MAAAA,CADFW,EAAW,qCAAuC,iBACnD,KAAa,OAAVX,GACJ,MAAOwC,EACP,KACEC,EACE,UAACI,EAAAA,GAAcA,CAAAA,CAAAA,GAEf,UAACC,GAAAA,GAAOA,CAAAA,CAAC,UAAU,mD,GAGvB,SAAUnC,EACV,KAAM+B,EACN,MAAOC,C,EAGb,CC7Be,SAASI,GAAc5O,CASjB,E,GATiBA,CACpCwI,MAAAA,CAAK,CACLpI,KAAAA,CAAI,CACJyO,cAAAA,CAAa,CACbnB,SAAAA,CAAQ,CACRoB,sBAAAA,EAAwB,EAAI,CAC5BC,gBAAAA,CAAe,CACfC,SAAAA,CAAQ,CACRC,SAAAA,CAAQ,CACW,CATiBjP,EAa9BkP,EAAc,CAACxB,GAAYzK,EAAgB,QAAQ,CAAC7C,EAAK,IAAI,EAS7D,CAAE+O,oBAAAA,CAAmB,CAAE,CAAGjE,KAEhC,MACE,WAAC,OAAI,UAAU,2E,UAEb,UAACkE,EAAAA,EAAMA,CAAAA,CACL,KAAK,QACL,SAAU,CAACD,GAAuB,AATR,IAAV3G,EAUhB,QAASpI,EAAK,OAAO,CACrB,SAAU2O,C,GAGXD,EACC,UAAC,OAAI,UAAU,mC,SACb,UAAC3N,EAAAA,CAAOA,CAAAA,CACN,QAASD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,2CAChB,MAAM,O,SAEN,UAAC,O,SACC,UAACgN,GAAYA,CACX,MAAM,YACN,SAAUmB,AAjCN7G,GAAS,GAiCU,CA3BnC,EAACkF,GAAYmB,GAAiBK,GAAeJ,CAAoB,EA4BrD,UAAU,iBACV,QAASG,EACT,QAAS,E,SAKf,KAEJ,UAAC7N,EAAAA,EAAUA,CAAAA,CACT,cAAa7B,GAAAA,qBAAiC,CAC9C,MAAM,YACN,QAASyP,EACT,SAAU,CAvCS,CAACtB,EAwCpB,KAAM,UAAC4B,EAAAA,GAAeA,CAAAA,CAAAA,E,KAI9B,CChFO,IAAMC,GAAgB,CAC3B,SAAU,AAACjO,GAAoB,CAAC,CAACA,EAAM,IAAI,CAC3C,QAASJ,EAAAA,CAAAA,CAAAA,CAAM,CAAC,yCAClB,EAOasO,GAAiB,CAC5B,SAAU,CAAClO,EAAiB2C,KAC1B,GAAI,CAAC3C,EAAM,IAAI,CACb,MAAO,GAIT,IAAMmO,EAAexL,EAAO,IAAI,CAACC,AAAAA,GAASA,EAAM,OAAO,GAAK5C,EAAM,OAAO,EAEzE,GAAI,CAACmO,EACH,MAAO,GAIT,IAAMC,EAAe,CACnB1H,EACA2H,KAEA,IAWMC,EAXFC,EAAqB,EAAE,CAE3B,IAAK,IAAMxI,KAAYW,EAGnBX,EAAS,QAAQ,GAAKsI,GACtBtI,EAAS,UAAU,GAAK/F,EAAM,UAAU,EAExCuO,EAAO,IAAI,CAACxI,GAGV,CAAiB,OAAjBuI,CAAAA,EAAAA,EAAS,QAAQ,AAAD,GAAhBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAmB,MAAM,AAAD,GAC1BC,CAAAA,EAASA,EAAO,MAAM,CACpBH,EAAarI,EAAS,QAAQ,CAAEsI,GAAe,EAKrD,OAAOE,CACT,QAIA,EAAIC,AAFaJ,EAAaD,EAAa,WAAW,CAAEnO,EAAM,QAAQ,EAEzD,IAAI,CAACyO,AAAAA,GAAWA,EAAQ,IAAI,GAAKzO,EAAM,IAAI,GAMpD,CAACA,EAAM,QAAQ,EAWb0O,AAVyB/L,EAC1B,MAAM,CAACC,AAAAA,GAASA,EAAM,OAAO,GAAK5C,EAAM,OAAO,EAC/C,OAAO,CAAC4C,AAAAA,IACP,IAAM+L,EAAmB/L,EAAM,WAAW,CACpCgM,EAAsBhM,EAAM,YAAY,CAAC,OAAO,CACpDmB,AAAAA,GAAYA,EAAS,WAAW,EAElC,OAAO4K,EAAiB,MAAM,CAACC,EACjC,GAEuB,IAAI,CAAC5N,AAAAA,GAAQA,EAAK,IAAI,GAAKhB,EAAM,IAAI,IAKzD,EACT,EACA,QAASJ,EAAAA,CAAAA,CAAAA,CAAM,CAAC,qDAClB,EAEaiP,GAAoB,CAC/B,SAAU,AAAC7O,GACT,2FAA2F,IAAI,CAC7FA,EAAM,IAAI,EAEd,QAASJ,EAAAA,CAAAA,CAAAA,CAAM,CAAC,2BAClB,ECnFakP,GAAgB,AAAChQ,IAC5B,IAAMiQ,EAAUC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAEVC,EAAoBjH,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,EAAOlJ,EAAK,IAAI,EAE1CqJ,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACR,IAAM+G,EAAeH,EAAQ,QAAQ,CAAE,GAAkB,OAAhBjQ,EAAK,UAAU,CAAC,UACrDoQ,EACFD,EAAkB,OAAO,CAAGC,EACnBD,EAAkB,OAAO,EAClCF,EAAQ,QAAQ,CAAE,GAAkB,OAAhBjQ,EAAK,UAAU,CAAC,SAAQmQ,EAAkB,OAAO,CAEzE,EAAG,CAACnQ,EAAK,UAAU,CAAC,CACtB,ECJaqQ,GAAY,AAAC7E,IAMxB,GAAM,CAAExL,KAAAA,CAAI,CAAEsQ,SAAAA,CAAQ,CAAEhD,SAAAA,CAAQ,CAAEiD,qBAAAA,EAAuB,EAAK,CAAE,CAAG/E,EAC7D,CAAE3H,OAAAA,CAAM,CAAE,CAAGiH,KACbmF,EAAUC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAKhB,OAFAF,GAAchQ,GAGZ,UAAC,OACC,UAAWwQ,IACT,yBACA,8CACA,iDACA,gDACA,qD,SAGD,AAAClD,EA+CA,UAAC/B,GAAYA,CAAC,MAAOvL,EAAK,IAAI,A,GA9C9B,sB,SACE,UAACyQ,EAAAA,EAASA,CAAAA,CACR,MAAQ,GAAkB,OAAhBzQ,EAAK,UAAU,CAAC,SAC1B,YAAac,EAAAA,CAAAA,CAAAA,CAAM,CAAC,6BACpB,UAAW,GACX,UAAW,CAACd,EAAK,IAAI,CACrB,QAAO,GACP,MAAO,CACL,CACE,UAAW,CAAC0Q,EAAGxP,IACbiO,GAAc,QAAQ,CAAC,mBAClBnP,GAAAA,CACH,KAAMkB,C,IAEV,QAASiO,GAAc,OAAO,AAChC,EACA,CACE,UAAW,CAACuB,EAAGxP,IACbqP,CAAAA,GACIR,GAAkB,QAAQ,CAAC,mBACtB/P,GAAAA,CACH,KAAMkB,C,IAGd,QAAS6O,GAAkB,OAAO,AACpC,EACA,CACE,UAAW,CAACW,EAAGxP,IACbkO,GAAe,QAAQ,CACrB,mBACKpP,GAAAA,CACH,KAAMkB,C,GAER2C,GAEJ,QAASuL,GAAe,OAAO,AACjC,EACD,CACD,SAAUlO,AAAAA,IACRoP,EAASpP,GACT+O,EAAQ,QAAQ,CAAE,GAAkB,OAAhBjQ,EAAK,UAAU,CAAC,SAAQkB,EAC9C,EACA,UAAU,iB,MAQtB,EClFayP,GAAmB,AAACnF,IAK/B,IAcuBoF,EAdjB,CAAE5Q,KAAAA,CAAI,CAAEsQ,SAAAA,CAAQ,CAAEhD,SAAAA,CAAQ,CAAE,CAAG9B,EACrC,OAAO,AAAC8B,EAaN,UAAC/B,GAAYA,CAAC,MAAOqF,AAAgB,OAAhBA,CAAAA,EAAAA,EAAK,WAAW,AAAD,GAAfA,AAAAA,KAAAA,IAAAA,EAAAA,EAAoB,E,GAZzC,UAAC,OAAI,UAAU,gD,SACb,UAACC,EAAAA,EAAKA,CAAAA,CACJ,MAAO7Q,EAAK,WAAW,CACvB,YAAac,EAAAA,CAAAA,CAAAA,CAAM,CAAC,yCACpB,UAAW,IACX,SAAUI,AAAAA,IACRoP,EAASpP,EACX,EACA,UAAU,Q,IAMlB,E,eC3Ba4P,GAAa,AAACC,IACzB,GAAI,CACF,OAAOtN,KAAK,SAAS,CAACA,KAAK,KAAK,CAACsN,GAAO,KAH7B,EAIb,CAAE,MAAOtH,EAAG,CACV,OAAOsH,CACT,CACF,ECCaC,GAAuB,SAElCC,CAAM,E,IACNpP,EAAW,UAAXA,MAAAA,CAAAA,GAAAA,AAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAW,GACXE,EAAe,UAAfA,MAAAA,CAAAA,GAAAA,AAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAe,EAEf,GAAIA,EAAeF,EACjB,MAAO,EAAE,CAEX,IAAMqP,EAA4B,EAAE,CA4GpC,OA3GA/I,OAAO,IAAI,CAAC8I,GAAQ,OAAO,CAACE,AAAAA,IAC1B,IAAMjQ,EAAQ+P,CAAM,CAACE,EAAI,CACzB,OAAQ,OAAOjQ,GACb,IAAK,SACHgQ,EAAY,IAAI,CAAC,CACf,KAAMC,EACN,aAAc1N,KAAK,SAAS,CAACvC,GAC7B,KAAM,CACR,GACA,KACF,KAAK,SACC2K,OAAO,SAAS,CAAC3K,GACnBgQ,EAAY,IAAI,CAAC,CACf,KAAMC,EACN,aAAc1N,KAAK,SAAS,CAACvC,GAC7B,KAAM,CACR,GAEAgQ,EAAY,IAAI,CAAC,CACf,KAAMC,EACN,aAAc1N,KAAK,SAAS,CAACvC,GAC7B,KAAM,CACR,GAEF,KACF,KAAK,UACHgQ,EAAY,IAAI,CAAC,CACf,KAAMC,EACN,aAAc1N,KAAK,SAAS,CAACvC,GAC7B,KAAM,CACR,GACA,KACF,KAAK,SACH,GAAIA,AAAU,OAAVA,EACF,MAEF,GAAIe,MAAM,OAAO,CAACf,IAChB,GAAIA,EAAM,MAAM,CAAG,EACjB,OAAQ,OAAOA,CAAK,CAAC,EAAE,EACrB,IAAK,SAyCL,QAxCEgQ,EAAY,IAAI,CAAC,CACf,KAAMC,EACN,aAAc1N,KAAK,SAAS,CAACvC,GAC7B,KAAM,EACR,GACA,KACF,KAAK,SACC2K,OAAO,SAAS,CAAC3K,CAAK,CAAC,EAAE,EAC3BgQ,EAAY,IAAI,CAAC,CACf,KAAMC,EACN,aAAc1N,KAAK,SAAS,CAACvC,GAC7B,KAAM,GACR,GAEAgQ,EAAY,IAAI,CAAC,CACf,KAAMC,EACN,aAAc1N,KAAK,SAAS,CAACvC,GAC7B,KAAM,GACR,GAEF,KACF,KAAK,UACHgQ,EAAY,IAAI,CAAC,CACf,KAAMC,EACN,aAAc1N,KAAK,SAAS,CAACvC,GAC7B,KAAM,GACR,GACA,KACF,KAAK,SACHgQ,EAAY,IAAI,CAAC,CACf,KAAMC,EACN,aAAc1N,KAAK,SAAS,CAACvC,GAC7B,KAAM,IACN,SAAU8P,GACR9P,CAAK,CAAC,EAAE,CACRW,EACAE,EAAe,EAEnB,EAQJ,MAEAmP,EAAY,IAAI,CAAC,CACf,KAAMC,EACN,aAAc1N,KAAK,SAAS,CAACvC,GAC7B,KAAM,EACR,QAGFgQ,EAAY,IAAI,CAAC,CACf,KAAMC,EACN,aAAc1N,KAAK,SAAS,CAACvC,GAC7B,KAAM,EACN,SAAU8P,GAAqB9P,EAAOW,EAAUE,EAAe,EACjE,GAEF,KACF,SACE,MAAM,AAAImE,MAAM,uBACpB,CACF,GACOgL,CACT,E,2KCnII,GAAU,CAAC,CAEf,IAAQ,iBAAiB,CAAG,KAC5B,GAAQ,aAAa,CAAG,KAElB,GAAQ,MAAM,CAAG,SAAa,CAAC,KAAM,QAE3C,GAAQ,MAAM,CAAG,KACjB,GAAQ,kBAAkB,CAAG,KAEhB,KAAI,IAAO,CAAE,IAKnB,OAAe,IAAO,EAAI,WAAc,CAAG,WAAc,CAAGxG,KAAAA,ECa7D0G,GAAyBC,AAAAA,GAAAA,EAAAA,IAAAA,AAAAA,EAAIA,AAAC,GAADA,EAAAA,CAAAA,AAAAA,EAAC,YAClC,GAAM,CAAEC,OAAAA,CAAM,CAAE,CAAG,MAAM,yCACzB,MAAO,CAAE,QAASA,CAAO,CAC3B,IAEMC,GAAqB/F,AAAAA,GACzB,UAACgG,EAAAA,QAAQA,CAAAA,C,SACP,UAACJ,GAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,CAAAA,EAA2B5F,G,GAc1BiG,GAAgB,CACpB,UAAW,CACT,QAAS3Q,EAAAA,CAAAA,CAAAA,CAAM,CAAC,8BAChB,UAAW,AAACI,IACV,GAAI,CACF,IAAMwQ,EAAKjO,KAAK,KAAK,CAACvC,GAEtB,MADe,AAAc,UAAd,OAAOwQ,CAGxB,CAAE,MAAOrH,EAAO,CACd,MAAO,EACT,CACF,CACF,EACA,WAAY,CACV,QAASvJ,EAAAA,CAAAA,CAAAA,CAAM,CAAC,8BAChB,UAAW,AAACI,GACV,CAAIA,CAAAA,EAAM,MAAM,CC9CS,KD8CQ,GAG1B,EAEX,CACF,EAEayQ,GAAkCnG,AAAAA,IAC7C,GAAM,CAAEoG,GAAAA,CAAE,CAAE1Q,MAAAA,CAAK,CAAE5B,SAAAA,CAAQ,CAAEuS,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAEzE,SAAAA,CAAQ,CAAE,CAAG9B,EAC7D,CAACrK,EAAQ6Q,EAAU,CAAGlJ,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,IACtB,CAACuB,EAAO4H,EAAS,CAAGnJ,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,IACpBoJ,EAASC,AAAAA,GAAAA,EAAAA,WAAAA,AAAAA,EAAWA,AAAC,GAADA,EAAAA,CAAAA,AAAAA,EAAC,YACzB,IAAI,CAAChR,EAIL,OADA8Q,EAASvH,KAAAA,GACF,IAAI0H,QAAQC,AAAAA,IACjBC,EAAAA,EAAAA,CAAAA,OAAa,CAAC,CACZ,MAAOxR,EAAAA,CAAAA,CAAAA,CAAM,CAAC,wCACd,QAASA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,0CAChB,OAAQ,UACR,OAAQA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,WACf,WAAYA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,UACnB,KAAM,KAEJiR,EADoBQ,EAAQrR,IAAU,EAAE,EAExCmR,EAAQ,GACV,EACA,SAAU,IAAMA,EAAQ,GAC1B,EACF,EACF,GAAG,CAAClR,EAAO,EAELoR,EAAU,AAACC,IACf,IAAI,CAACA,EAGL,GAAI,CACF,IAAMzB,EAAOtN,KAAK,KAAK,CAAC+O,GAClBC,EAAczB,GAAqBD,GACzC,GACE,CAAC0B,GACD,CAACxQ,MAAM,OAAO,CAACwQ,IACfA,AAAuB,IAAvBA,EAAY,MAAM,CAElB,OAEF,OAAOA,CACT,CAAE,MAAOhJ,EAAG,CACV,MACF,CACF,EAEMiJ,EAAW,AAACC,IAEhB,IAAK,IAAMC,KADGzK,OAAO,MAAM,CAACsJ,IAE1B,GAAI,CAACmB,EAAK,SAAS,CAACD,GAElB,OADAV,EAASW,EAAK,OAAO,EACd,GAIX,OADAX,EAASvH,KAAAA,GACF,EACT,EAEMmI,EAAUrF,AAAAA,GAAAA,EAAAA,OAAAA,AAAAA,EAAQ,IAAMkF,EAASxR,GAAQ,CAACA,EAAM,EAQtD,MALAmI,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KAER2I,EADgBO,EAAQrR,GAE1B,EAAG,CAACA,EAAM,EAGR,UAACoR,EAAAA,EAAKA,CAAAA,CACJ,QAAST,EACT,MACEvE,EACIxM,EAAAA,CAAAA,CAAAA,CAAM,CAAC,uCACPA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,+BAEb,OAAQA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,WACf,WAAYA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,UACnB,KAAMoR,EACN,SAAUJ,EACV,OAAQ,IACR,cAAe,CACb,SAAU,CAACe,GAAWvF,CACxB,E,SAEA,WAAC,OAAa,UAAU,kB,UACtB,WAAC,OAAI,UAAU,wH,UACb,UAAC,OAAI,UAAU,iB,SAAiB,M,GAChC,UAACvM,EAAAA,CAAOA,CAAAA,CAAC,QAASD,EAAAA,CAAAA,CAAAA,CAAM,CAAC,oC,SACvB,UAACE,EAAAA,EAAUA,CAAAA,CACT,UAAU,iBACV,SAAUsM,EACV,KAAM,UAACwF,EAAAA,GAAYA,CAAAA,CAAAA,GACnB,QAAS,KACPxT,EAASwR,GAAW5P,GACtB,C,QAIN,UAAC,OAAI,UAAU,mB,SACb,UAACqQ,GAAAA,CAEC,MAAOrQ,EACP,gBAAgB,OAEhB,UAAW6R,GAAAA,KAAiB,CAC5B,QAAS,CACP,SAAU,GACV,QAAS,CACP,QAAS,EACX,EACA,YAAa,GACb,UAAW,CACT,sBAAuB,GACvB,wBAAyB,EAC3B,EACA,YAAa,KACb,oBAAqB,EACrB,QAAS,GACT,qBAAsB,EACtB,oBAAqB,OACrB,YAAa,GACb,qBAAsB,GACtB,oBAAqB,GACrB,SAAU,KACV,qBAAsB,GACtB,SAAUzF,CACZ,EACA,SAAU0F,AAAAA,IACR1T,EAAS0T,GAAe,GAC1B,C,EA7BKpB,E,GAgCRvH,EACC,UAAC,OAAI,UAAU,oB,SACb,UAAC,QAAK,UAAU,0E,SACbA,C,KAGH,K,EAtDIuH,E,EA0DhB,E1BvLanQ,GAAW,CACtBP,EACA+R,KAEA,IAAMC,EAAmCjR,MAAM,OAAO,CAACgR,GACnD,AAACE,IACCF,EAAQ,OAAO,CAACG,AAAAA,GAAaA,EAAUD,GACzC,EACAF,EAEJ,OADA7T,EAAc,aAAa,CAAC,CAAE8B,MAAAA,CAAM,EAAGgS,GAChChS,CACT,C,EAEU9B,EAAAA,GAAAA,CAAAA,EAAaA,CAAAA,IAMR,aAAa,CAAG,CAC3B8C,EACAmR,KAEA,GAAM,CAAEnS,MAAAA,CAAK,CAAE,CAAGgB,EAClB,IAAI,CAAChB,GAIL,GAAIiH,AAA0C,oBAA1CA,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAACjH,GAEjCiH,OAAO,OAAO,CAACjH,GAAO,OAAO,CAAC,AAAC,I,GAAA,CAACiQ,EAAK7Q,EAAK,G,OACxC,gBACE,CACE,MAAOA,EACP,UAAWY,EACXiQ,IAAAA,EACA,OAAQjP,CACV,EACAmR,E,QAGC,GAAIpR,MAAM,OAAO,CAACf,GAGvB,IAAK,IAAIoS,EAAQpS,EAAM,MAAM,CAAG,EAAGoS,GAAS,EAAGA,IAAS,CACtD,IAAMhT,EAAeY,CAAK,CAACoS,EAAM,CACjC,gBACE,CACE,MAAOhT,EACP,UAAWY,EACXoS,MAAAA,EACA,OAAQpR,CACV,EACAmR,EAEJ,CAGFA,EADiChU,EAAc,CAAE6C,KAAAA,CAAK,IAExD,EAEM7C,EAAgB,AAAC,I,GAAA,CACrB6C,KAAAA,CAAI,CAGL,G,MAAuB,CACtBA,KAAAA,EACA,SAAU,AAAChB,GAAmB5B,EAAS4C,EAAMhB,GAC7C,WAAY,IAAM3B,EAAW2C,GAC7B,QAAS,IAAM1C,EAAQ0C,GACvB,iBAAkB,IAAMzC,EAAiByC,GACzC,WAAY,IAAMxC,EAAWwC,EAC/B,C,EAEM5C,EAAW,CAAC4C,EAAoBhB,KAIpC,GAAI,EAACA,IAAS,CAACgB,GAGfA,EAAK,KAAK,CAAGhB,EAEb,GAAM,CAAEqS,UAAAA,CAAS,CAAEpC,IAAAA,CAAG,CAAEmC,MAAAA,CAAK,CAAE,CAAGpR,CAC9BiP,CAAAA,GAAOoC,EACTA,CAAS,CAACpC,EAAI,CAAGjQ,EACS,UAAjB,OAAOoS,GAChBC,CAAAA,CAAS,CAACD,EAAM,CAAGpS,CAAI,EAE3B,EAEM3B,EAAa,AAAC2C,IAGlB,IAFA,IAAMsR,EAA0B,EAAE,CAC9BC,EAAwCvR,EACrCuR,GACLD,EAAQ,OAAO,CAACC,GAChBA,EAAcA,EAAY,MAAM,CAElC,OAAOD,CACT,EAEMhU,EAAU,AAAC0C,IACf,IAAMsK,EAA+B,EAAE,CASvC,OAPAgH,AADgBjU,EAAW2C,GACnB,OAAO,CAACwR,AAAAA,IACVA,EAAO,GAAG,CACZlH,EAAK,OAAO,CAACkH,EAAO,GAAG,EACdA,EAAO,KAAK,EACrBlH,EAAK,OAAO,CAACkH,EAAO,KAAK,CAE7B,GACOlH,CACT,EAEM/M,EAAmB,AAACyC,GAEjBsK,AADMhN,EAAQ0C,GACT,MAAM,CAAC,CAACyR,EAAuBC,IACzC,AAAI,AAAoB,UAApB,OAAOA,EAQD,GAAmBA,MAAAA,CAAjBD,EAAc,KAAY,OAATC,EAAS,KANpC,AAAIC,AADO,MACJ,IAAI,CAACD,GAEF,GAAoBA,MAAAA,CAAlBD,EAAc,MAAa,OAATC,EAAS,MAE/B,GAAmBA,MAAAA,CAAjBD,EAAc,KAAY,OAATC,GAI5B,IAGClU,EAAa,AAACwC,IAClB,GAAM,CAAEqR,UAAAA,CAAS,CAAEpC,IAAAA,CAAG,CAAEmC,MAAAA,CAAK,CAAE,CAAGpR,CAC9BiP,CAAAA,GAAOoC,EACT,OAAOA,CAAS,CAACpC,EAAI,CACK,UAAjB,OAAOmC,GAChBC,EAAU,MAAM,CAACD,EAAO,EAE5B,E4B5JF,IAAMQ,GAAuB,AAACX,GAC5B,AACgC,UAA9B,OAAOA,EAAQ,IAAI,CAAC,KAAK,EACzB,AAAmC,SAA5BA,EAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,EAIvB,GAILY,GACJ,AAACC,GACD,AAACb,IACC,IAAI,CAACW,GAAqBX,GAGtBA,EAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAGa,GACnCb,CAAAA,EAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAGA,EAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAGa,EAAM,CAErE,EAEIC,GACJ,AAACC,GACD,AAACf,IACC,GACE,EAACW,GAAqBX,IACtBA,EAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,GAAKe,IAC7B,CAAC,CAACjV,EAAAA,MAAuB,CAAEA,EAAAA,WAA4B,CAAC,CAAC,QAAQ,CAC/DkU,EAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,EAK3BA,EAAQ,UAAU,EACpB,EAEWgB,GAAoB,AAACC,IAMhC,GAAM,CAAEpU,KAAAA,CAAI,CAAEqU,WAAAA,CAAU,CAAEC,gBAAAA,CAAe,CAAEC,iBAAAA,CAAgB,CAAE,CAAGH,EAEhE,OAAO3S,GADyBzB,EAAK,KAAK,CAAC,EAAGuU,GACO,CACnDR,GAAiBO,GACjBL,GAAYI,GACb,CACH,EC1CaG,GAAwB,CACnCxU,EACAgH,EAIAyN,KAEA,IAAMC,EAAQjO,GAAuB,QAAQ,GAEvCkO,EAAc,SAClBzS,CAAI,E,IAuBA0S,EAtBJrO,EAAW,UAAXA,MAAAA,CAAAA,GAAAA,AAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAW,GACXsO,EAAAA,UAAAA,MAAAA,CAAAA,EAAAA,SAAAA,CAAAA,EAAAA,CAAAA,KAAAA,EAGM1P,EAAeuP,EAAM,cAAc,CAAC,CACxC,aAAcxS,EAAK,IAAI,CACvB,QAAS8E,EAAS,OAAO,CACzBT,SAAAA,EACA,QAASS,EAAS,OAAO,AAC3B,GAoBA,OAjBI6N,IACF1P,EAAa,UAAU,CAAG0P,EAAa,UAAU,CACjD1P,EAAa,WAAW,CAAG0P,EAAa,WAAW,EAIrD1P,EAAa,IAAI,CAAGjD,EAAK,IAAI,CAC7BiD,EAAa,YAAY,CAAGjD,EAAK,YAAY,CAGzC,CAAa,OAAb0S,CAAAA,EAAAA,EAAK,QAAQ,AAAD,GAAZA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAe,MAAM,AAAD,GACtBzP,CAAAA,EAAa,QAAQ,CAAGjD,EAAK,QAAQ,CAAC,GAAG,CAAC,CAACsB,EAAO8P,KAChD,IAAsBwB,EAAhBC,EAAgBD,MAAAA,EAAAA,KAAAA,EAAAA,AAAsB,OAAtBA,CAAAA,EAAAA,EAAc,QAAQ,AAAD,GAArBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,CAAwB,CAACxB,EAAM,CACrD,OAAOqB,EAAYnR,EAAO2B,EAAa,UAAU,CAAE4P,EACrD,EAAC,EAGI5P,CACT,EAEMyC,EAAY5H,EAAK,GAAG,CAACkC,AAAAA,GAAQyS,EAAYzS,EAAM,GAAIuS,IAKzD,OAFAC,EAAM,UAAU,CAAC,CAAE9M,UAAAA,CAAU,GAEtBA,CACT,EC7DMoN,GAAwB,AAACtS,IAC7B,OAAQA,GACN,KAAKzD,EAAAA,MAAuB,CAC1B,MAAO,EACT,MAAKA,EAAAA,OAAwB,CAC7B,KAAKA,EAAAA,MAAuB,CAC1B,OAAO,CACT,MAAKA,EAAAA,OAAwB,CAC3B,MAAO,EACT,MAAKA,EAAAA,MAAuB,CAC1B,MAAO,CAAC,CACV,MAAKA,EAAAA,WAA4B,CAC/B,MAAO,CAAC,GAAG,AACb,MAAKA,EAAAA,YAA6B,CAChC,MAAO,CAAC,EAAE,AACZ,MAAKA,EAAAA,YAA6B,CAChC,MAAO,CAAC,GAAK,AACf,MAAKA,EAAAA,WAA4B,CAC/B,MAAO,CAAC,EAAE,AACZ,MAAKA,EAAAA,WAA4B,CAC/B,MAAO,CAAC,CAAC,EAAE,AACb,SACE,MAAO,CAAC,CACZ,CACF,EAEM0D,GAAc,AAACD,GACnB,CACEzD,EAAAA,WAA4B,CAC5BA,EAAAA,YAA6B,CAC7BA,EAAAA,YAA6B,CAC7BA,EAAAA,WAA4B,CAC5BA,EAAAA,WAA4B,CAC7B,CAAC,QAAQ,CAACyD,GAEAuS,GAA4B,AAAC/J,IACxC,GAAM,CAAEgK,aAAAA,CAAY,CAAExS,KAAAA,CAAI,CAAEyS,KAAAA,CAAI,CAAE/S,SAAAA,CAAQ,CAAE,CAAG8I,EAE/C,GAAIgK,EAEF,OAAOpE,GACLrN,KAAK,SAAS,CAAC,CACb,CAAC0R,EAAK,CAHG1R,KAAK,KAAK,CAACyR,EAItB,IAKJ,GAAI,CAACC,EACH,MAAO,KAGT,IAAMC,EAAUzS,GAAYD,GAGtB2S,EAAkB,CACtBrT,EACAsT,KAEA,GAAI,CAACtT,GAASA,AAAiB,IAAjBA,EAAM,MAAM,CACxB,OAAOgT,GAAsBM,GAAc5S,GAG7C,GAAI0S,GAAW,CAACE,EAAY,CAC1B,IAAMC,EAAavT,CAAK,CAAC,EAAE,CAC3B,GAAI,CAACuT,EACH,MAAO,EAAE,CAIX,IAAM9F,EAAS,CAAC,EAShB,OARI8F,EAAW,QAAQ,EAAIA,EAAW,QAAQ,CAAC,MAAM,CAAG,EACtD9F,CAAM,CAAC8F,EAAW,IAAI,CAAC,CAAGF,EACxBE,EAAW,QAAQ,CACnBA,EAAW,IAAI,EAGjB9F,CAAM,CAAC8F,EAAW,IAAI,CAAC,CAAGP,GAAsBO,EAAW,IAAI,EAE1D,CAAC9F,EAAO,AACjB,CAEA,OAAOzN,EAAM,MAAM,CAAC,CAACwT,EAAKtT,KACxB,GAAI,CAACA,EAAK,IAAI,CACZ,OAAOsT,EAET,GAAItT,EAAK,QAAQ,EAAIA,EAAK,QAAQ,CAAC,MAAM,CAAG,EAAG,CAC7C,IAAMhB,EAAQmU,EAAgBnT,EAAK,QAAQ,CAAEA,EAAK,IAAI,CACtDsT,CAAAA,CAAG,CAACtT,EAAK,IAAI,CAAC,CAAGS,GAAYT,EAAK,IAAI,EAAI,CAAChB,EAAM,CAAGA,CACtD,MACEsU,CAAG,CAACtT,EAAK,IAAI,CAAC,CAAG8S,GAAsB9S,EAAK,IAAI,EAElD,OAAOsT,CACT,EAAG,CAAC,EACN,EAOA,OAAO1E,GAAWrN,KAAK,SAAS,CAJjB,CACb,CAAC0R,EAAK,CAAEE,EAAgBjT,EAC1B,GAGF,EC/EaqT,GAAkCjK,AAAAA,IAC7C,GAAM,CAAEN,SAAAA,CAAQ,CAAEwK,MAAAA,CAAK,CAAE7D,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAE,CAAGvG,EAC/C,CAAEmK,WAAAA,CAAU,CAAErI,SAAAA,CAAQ,CAAE,CAAGoI,EAC3B,CAAClD,EAAYoD,EAAc,CAAG9M,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,UAoC7C,CAJAO,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACRuM,EAAcX,GAA0B/J,GAC1C,EAAG,CAACA,EAAS,EAERyK,GAKH,UAAChE,GAAUA,CACT,GAAIzG,EAAS,UAAU,CACvB,QAASA,EAAS,OAAO,CACzB,MAAOsH,EACP,SAAUlF,EACV,SAAU,AAACpM,IACT0U,EAAc1U,EAChB,EACA,QAAS2Q,EACT,KAhDiB,AAAC7R,IAcpB,IAAM6V,EAAa1B,GAAkB,CACnC,KAXgBK,GAChBxU,EACA,CACE,QAASkL,EAAS,OAAO,CACzB,QAASA,EAAS,OAAO,AAC3B,EACAA,GAMAmJ,WJrCmB,EIsCnBC,gBJzCyB,GI0CzBC,iBJrCiC,CIsCnC,GAGMuB,EAAiB7N,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUiD,GAKjC,OAAO6G,EAHYgE,AAAAA,GAAAA,GAAAA,CAAAA,AAAAA,EAAMD,EAAgBD,CAAU,CAAC,EAAE,EAIxD,EAqBI,SAAU/D,C,GAdL,wBAiBX,EC/DakE,GAAe,AAACxK,IAC3B,GAAM,CAAExL,KAAAA,CAAI,CAAEiW,gBAAAA,CAAe,CAAEC,eAAAA,CAAc,CAAE5I,SAAAA,CAAQ,CAAE,CAAG9B,EACtD,CAAC2K,EAAkBC,EAAoB,CAAGtN,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IAEnDuN,EAASrW,AAAoB,IAApBA,EAAK,IAAI,CAAC,KAAK,CACxBsW,EAAWD,GAAUrW,EAAK,IAAI,GAAKf,EAAAA,MAAuB,CAC1DsX,EACJF,GACCrW,CAAAA,EAAK,IAAI,GAAKf,EAAAA,MAAuB,EACpCe,EAAK,IAAI,GAAKf,EAAAA,OAAuB,AAAvBA,EACZuX,EAAYH,GAAUrW,EAAK,IAAI,GAAKf,EAAAA,OAAwB,CAC5DwX,EAAmBtL,GAAc,QAAQ,CAACnL,EAAK,IAAI,GAAKqW,EAE9D,MACE,UAAC,OAAI,UAAU,gF,SACb,UAAC,OAAI,UAAU,gC,SACZ/I,GAAY,CAACmJ,EACZ,UAAClL,GAAYA,CACX,UAAU,YACV,MAAOvL,EAAK,YAAY,EAAI,G,GAG9B,uB,UACGsW,EACC,UAACzF,EAAAA,EAAKA,CAAAA,CACJ,MAAO7Q,EAAK,YAAY,CACxB,SAAUkB,AAAAA,GAAS+U,EAAgB/U,GACnC,YAAaJ,EAAAA,CAAAA,CAAAA,CAAM,CACjB,4CACA,CACE,EAAGd,EAAK,IAAI,AACd,GAEF,UAAW,IACX,SAAUsN,C,GAEV,KAEHiJ,EACC,UAACG,EAAAA,EAAcA,CAAAA,CACb,UAAWlG,IAAI,SAAU,CACvB,2CAA4C,EAC9C,GACA,MAAOxQ,EAAK,YAAY,CACxB,SAAUkB,AAAAA,GAAS+U,EAAgB/U,GACnC,YAAaJ,EAAAA,CAAAA,CAAAA,CAAM,CACjB,4CACA,CACE,EAAGd,EAAK,IAAI,AACd,GAEF,SAAUsN,C,GAEV,KAEHkJ,EACC,UAACxH,EAAAA,EAAMA,CAAAA,CACL,QAAiBhP,AAAsB,SAAtBA,EAAK,YAAY,CAClC,KAAK,QACL,SAAUkB,AAAAA,GAAS+U,EAAgB/U,GACnC,SAAUoM,C,GAEV,KAEHmJ,EACC,uB,UACE,WAAC,OACC,QAAS,IAAML,EAAoB,IACnC,UAAW5F,IACT,0FACA,CACE,iBAAkB,CAAClD,EACnB,aAAcA,CAChB,G,UAGF,UAACqJ,EAAAA,GAAWA,CAAAA,CAAAA,GACZ,UAAC,QAAK,UAAU,sB,SACbrJ,EACGxM,EAAAA,CAAAA,CAAAA,CAAM,CAAC,wCACPA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,6B,MAGf,UAAC2U,GAAUA,CACT,QAASU,EACT,SAAUnW,EACV,MAAO,CACL,WAAY,GACZ,SAAUuD,CAAAA,CAAQ+J,CACpB,EACA,KAAMpM,AAAAA,IACJgV,EAAehV,GACfkV,EAAoB,GACtB,EACA,SAAU,IAAMA,EAAoB,G,MAGtC,K,MAMhB,EC1HaQ,GAAe,AAACpL,IAC3B,IACOqL,EAEFC,EAAAA,EAHC,CAAE5V,MAAAA,CAAK,CAAE,CAAGsK,EAClB,MAAOqL,AAAAA,CAA0B,OAA1BA,CAAAA,EAAAA,EAAM,oBAAoB,AAAD,GAAzBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA4B,MAAM,AAAD,EACtC,UAAC,OAAI,UAAU,2D,SACZC,AAAiC,OAAjCA,CAAAA,EAAAA,AAA0B,OAA1BA,CAAAA,EAAAA,EAAM,oBAAoB,AAAD,GAAzBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA4B,IAAI,CAAC,IAAG,GAApCA,AAAAA,KAAAA,IAAAA,EAAAA,EAAyC,I,GAE1C,IACN,EC0Be,SAASC,GAAevL,CAA0B,EAC/D,GAAM,CACJxL,KAAAA,CAAI,CACJyL,UAAAA,CAAS,CACTrD,MAAAA,CAAK,CACLkF,SAAAA,EAAW,EAAK,CAChBgD,SAAAA,CAAQ,CACR7B,cAAAA,CAAa,CACbuI,cAAAA,EAAgB,EAAI,CACpBC,eAAAA,CAAc,CACdC,UAAAA,EAAY,EAAK,CACjBC,WAAAA,CAAU,CACV5G,qBAAAA,EAAuB,EAAK,CAC7B,CAAG/E,EAEEtK,EAAQ+G,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAUjI,GAClBoX,EAAclO,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,EAAuB,MA2CrCmO,EAAsBlF,AAAAA,GAAAA,EAAAA,WAAAA,AAAAA,EAC1B,AAACmF,IACC,GAAIpW,EAAM,WAAW,GAAKoW,EAG1BhH,EAASpR,GAAAA,MAAiB,CAAE,mBAAKgC,GAAAA,CAAOoW,YAAAA,C,GAC1C,EACA,CAAChH,EAAUpP,EAAM,EAGbyN,EAAkBwD,AAAAA,GAAAA,EAAAA,WAAAA,AAAAA,EACtB,AAACoF,IACCjH,EAASpR,GAAAA,aAAwB,CAAE,mBAAKgC,GAAAA,CAAOqW,QAAAA,C,GACjD,EACA,CAACjH,EAAUpP,EAAM,EAEnB,MACE,UAAC,OACC,UAAWR,IAAW,oBAAqB,CACzC,CAAC+K,EAAU,CAAElI,CAAAA,CAAQkI,CACvB,GACA,IAAK2L,E,SAEL,WAAC,OAAI,UAAU,iE,UACb,WAAC,OAAI,UAAU,4E,UACb,UAAC,OACC,UAAU,gCACV,MAAO,CAAE,MAAOhP,APtHG,GOsHHA,CAAwB,C,GAE1C,UAACoP,EAAAA,GAAiBA,CAAAA,CAChB,UAAW9W,IACT,mCACAwW,EAAY,YAAc,GAC1BF,EAAgB,GAAK,YACrB,iBACA5O,AAAU,IAAVA,GAAgB4O,EAA2B,GAAX,UAElC,QAAS,KACPG,MAAAA,GAAAA,EAAa,CAACD,EAChB,C,GAEF,UAAC7G,GAASA,CACR,SAAU/C,EACV,KAAMpM,EACN,SAnDW,AAACiU,IACpB,GAAIjU,EAAM,IAAI,GAAKiU,EAGnB7E,EAASpR,GAAAA,MAAiB,CAAE,mBAAKgC,GAAAA,CAAOiU,KAAAA,C,GAC1C,EA+CU,qBAAsB5E,C,MAG1B,UAAC,OAAI,UAAU,yB,SACb,UAACI,GAAgBA,CACf,KAAMzP,EACN,SAAUmW,EACV,SAAU/J,C,KAGb,AAAC2J,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAgB,QAAQ,CAAC,OAAM,EAS7B,KARF,UAAC,OAAI,UAAU,oC,SACb,UAAC7J,GAASA,CACR,MAAOhF,EACP,SAAUkF,EACV,KAAMpM,EACN,eA5FW,AACrByM,IAEA,GAAYjD,KAAAA,IAARiD,IAGA,CAAC4I,AAAAA,GAAAA,GAAAA,CAAAA,AAAAA,EAAS5I,GAIdzM,EAAM,YAAY,CAAG,GACrBA,EAAM,QAAQ,CAAG,EAAE,CACnBoP,EAASpR,GAAAA,MAAiB,CAAE,mBAAKgC,GAAAA,CAAO,KAAMyM,C,GAChD,C,KAmFM,UAAC,OAAI,UAAU,oC,SACb,UAACqI,GAAYA,CACX,SAAU1I,EACV,KAAMpM,EACN,gBAtFc,AACtByM,IAEA2C,EAASpR,GAAAA,MAAiB,CAAE,mBAAKgC,GAAAA,CAAO,aAAcyM,EAAI,QAAQ,E,GACpE,EAmFU,eAjFa,AAACA,IACtB2C,EAASpR,GAAAA,OAAkB,CAAEyO,EAC/B,C,KAkFM,UAAC,OAAI,UAAU,iD,SACb,UAACiJ,GAAYA,CAAC,MAAO1V,C,KAEvB,UAAC,OAAI,UAAU,oC,SACb,UAACsN,GAAaA,CACZ,KAAMtN,EACN,SAAUoM,EACV,MAAOlF,EACP,SAzHO,KACfkI,EAASpR,GAAAA,MAAiB,CAAEgC,EAC9B,EAwHU,SArHO,KACfoP,EAASpR,GAAAA,MAAiB,CAAEgC,EAC9B,EAoHU,cAAeuN,EACf,sBAAuB,CAACwI,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAgB,QAAQ,CAAC,OAAM,EACvD,gBAAiBtI,C,SAM7B,CC6KO,IAAM8I,GAAeC,EAAAA,UAAgB,CApSrC,SACLlM,CAAwB,CACxBmM,CAA+B,EAE/B,GAAM,CACJrK,SAAAA,EAAW,EAAK,CAChBsK,UAAAA,CAAS,CACTnM,UAAAA,CAAS,CACTwC,MAAAA,CAAK,CACL/M,MAAAA,CAAK,CACL2W,oBAAAA,EAAsB5Y,EAAAA,MAAuB,CAC7C6Y,gBAAAA,EAAkB,EAAK,CACvBC,SAAAA,CAAQ,CACR9T,QAAAA,CAAO,CACPQ,QAAAA,CAAO,CACPwS,eAAAA,CAAc,CACd1G,qBAAAA,EAAuB,EAAK,CAC5BD,SAAAA,CAAQ,CACT,CAAG9E,EAEE,CACJwM,eAAAA,CAAc,CACdC,gBAAAA,CAAe,CACfC,iBAAAA,CAAgB,CAChBC,eAAAA,CAAc,CACdC,eAAAA,CAAc,CACdC,sBAAAA,CAAqB,CACtB,CAAG5R,GACF6R,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAWnR,AAAAA,GAAU,EACnB,eAAgBA,EAAM,cAAc,CACpC,gBAAiBA,EAAM,eAAe,CACtC,iBAAkBA,EAAM,gBAAgB,CACxC,eAAgBA,EAAM,cAAc,CACpC,eAAgBA,EAAM,cAAc,CACpC,sBAAuBA,EAAM,qBAAqB,AACpD,KAGI8I,EAAUC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAEVqI,EAAe,CAACrX,GAASA,AAAiB,IAAjBA,EAAM,MAAM,CAErCsX,EAAuBhL,AAAAA,GAAAA,EAAAA,OAAAA,AAAAA,EAAQ,KACnC,IAAMiL,EAAiB,EAAE,CAMzB,OALAhX,EAASP,EAAOZ,AAAAA,I,IACV+H,EAAAA,CAAa,OAAbA,CAAAA,EAAAA,EAAK,QAAQ,AAAD,GAAZA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAe,MAAM,AAAD,EAAI,GAC1BoQ,EAAK,IAAI,CAACnY,EAAK,UAAU,CAE7B,GACOmY,CACT,EAAG,CAACvX,EAAM,EAEJwX,EAAelL,AAAAA,GAAAA,EAAAA,OAAAA,AAAAA,EAAQ,IAAMvC,GAAqB/J,GAAQ,CAACA,EAAM,EAEjE,CAAEyX,aAAAA,CAAY,CAAEC,eAAAA,CAAc,CAAEC,iBAAAA,CAAgB,CAAE,CAAGC,AApE7D,SAAyBL,CAAc,CAAEX,CAAwB,EAC/D,GAAM,CAACa,EAAcI,EAAgB,CAAGjQ,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAASgP,EAAkB,EAAE,CAAGW,GAElEG,EAAiBzG,AAAAA,GAAAA,EAAAA,WAAAA,AAAAA,EAAY,AAAChB,IAClC4H,EAAgBC,AAAAA,GAAQ,IAAI,IAAIC,IAAI,IAAID,EAAM7H,EAAI,EAAE,CACtD,EAAG,EAAE,EAML,MAAO,CAAEwH,aAAAA,EAAcC,eAAAA,EAAgBC,iBAJd1G,AAAAA,GAAAA,EAAAA,WAAAA,AAAAA,EAAY,AAAChB,IACpC4H,EAAgBC,AAAAA,GAAQA,EAAK,MAAM,CAACE,AAAAA,GAAeA,IAAgB/H,GACrE,EAAG,EAAE,CAEmD,CAC1D,EAyDIqH,EACAV,GAEI1D,EAAS+E,AAAAA,GAAAA,GAAAA,SAAAA,AAAAA,IAEfC,AAAAA,GAAAA,EAAAA,mBAAAA,AAAAA,EAAoBzB,EAAK,IAAO,EAC9B,SAAU,IAAM1H,EAAQ,QAAQ,EAClC,IAEA,IAAMoJ,EAAa7L,AAAAA,GAAAA,EAAAA,OAAAA,AAAAA,EAAQ,SAIjB8L,SAHR,AAAiB5O,KAAAA,IAAbqN,GAGIuB,AAAAA,CAAa,OAAbA,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAO,MAAM,AAAD,GAAZA,AAAAA,KAAAA,IAAAA,EAAAA,EAAiB,IAAMvB,CACjC,EAAG,CAAC7W,EAAO6W,EAAS,EAEdwB,EAAgB,CAACjM,GAAY,CAAC+L,EAsB9BG,EAAmB,CAACC,EAAkB7Z,KAC1C,IAAM8Z,EAAarB,EACjBpU,EACA3D,AAAAA,GAAQA,EAAK,UAAU,GAAKV,EAAM,UAAU,EAE9C,GAAI,CAAC8Z,EAAY,CACfhQ,EAAAA,EAAAA,CAAAA,KAAW,CAAC5I,EAAAA,CAAAA,CAAAA,CAAM,CAAC,8CACnB,MACF,CAEA,OAAQ2Y,GACN,KAAKva,GAAAA,MAAiB,CACpB,GAAM,CAAE,WAAYqH,CAAQ,CAAE,QAASoT,CAAa,CAAE,CAAGD,EAOzDxB,EANsBF,EAAe,CACnC/T,QAAAA,EACAsC,SAAAA,EACA,aAAcsR,EACd,QAAS8B,CACX,IAIID,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAY,UAAU,AAAD,GACvBd,EAAec,EAAW,UAAU,EAEtCnZ,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,kBAA8B,CAAE,CAC3C,WAAY4T,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,UAAU,AAAD,GAAK,GAClC,cAAe,WACf,OAAQ,MACR,OAAQ,kBACR,cAAe,eACjB,GACA,KAEF,MAAKlV,GAAAA,MAAiB,CACpBiZ,EAAevY,GACfW,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,kBAA8B,CAAE,CAC3C,WAAY4T,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,UAAU,AAAD,GAAK,GAClC,cAAe,WACf,OAAQ,OACR,OAAQ,kBACR,cAAe,eACjB,GACA,KAEF,MAAKlV,GAAAA,MAAiB,CACpBkZ,EAAexY,GACfW,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,kBAA8B,CAAE,CAC3C,WAAY4T,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,UAAU,AAAD,GAAK,GAClC,cAAe,WACf,OAAQ,SACR,OAAQ,kBACR,cAAe,eACjB,GACA,KAEF,MAAKlV,GAAAA,aAAwB,CAS3B,GARAwa,EAAW,OAAO,CAAG9Z,EAAM,OAAO,CAElC6B,EAA6BiY,EAAYxX,AAAAA,IACnC,CAACtC,EAAM,OAAO,EAChBsC,CAAAA,EAAK,OAAO,CAAGtC,EAAM,OAAO,AAAD,CAE/B,GAEI8Z,EAAW,QAAQ,EAAIA,EAAW,OAAO,CAAE,CAC7C,IAAME,EAAavB,EACjBpU,EACA3D,AAAAA,GAAQA,EAAK,UAAU,GAAKoZ,EAAW,QAAQ,EAE7CE,IACFA,EAAW,OAAO,CAAGF,EAAW,OAAO,CACvCvB,EAAeyB,GAEnB,CACAzB,EAAeuB,GACfnZ,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,kBAA8B,CAAE,CAC3C,WAAY4T,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,UAAU,AAAD,GAAK,GAClC,cAAe,WACf,OAAQxU,EAAM,OAAO,CAAG,UAAY,WACpC,OAAQ,kBACR,cAAe,eACjB,GACA,KAEF,MAAKV,GAAAA,OAAkB,CACrBiZ,EAAevY,GACfgZ,EAAec,EAAW,UAAU,EACpCnZ,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,kBAA8B,CAAE,CAC3C,WAAY4T,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,UAAU,AAAD,GAAK,GAClC,cAAe,WACf,OAAQ,OACR,OAAQ,kBACR,cAAe,eACjB,EAIJ,CAEA9D,MAAAA,GAAAA,EAAW1Q,EACb,SAEA,AAAI0N,GAAYiL,EACP,KAIP,UAACvN,GAAoB,QAAQ,EAAC,MAAO,CAAE/G,QAAAA,EAAS,UAAWyU,CAAa,E,SACtE,WAAC,OACC,UAAWhY,IAET,kBAEA,CAAC4M,GAAY,iBAEb7B,GAEF,MAAOwC,E,UAEP,UAAC4L,EAAAA,EAAIA,CAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,CACH,MAAOvM,EAAW,CAAC,EAAI,CAAE,SAAU,SAAU,EAC7C,OAAQ,GACR,QAAS,CACP,IAAK,YACP,EACA,SAAUA,EACV,UAAW5M,IAET,kBAGA,yJAUA4M,EACI,sEACA,oJAKN,gBAAiBwM,AAAAA,IACf,IAyBoBC,EAAAA,EAzBd,CAAE/Z,KAAAA,CAAI,CAAE,CAAG8Z,EACXE,EAAuB1M,GAAYtN,EAAK,UAAU,CAgBxD,MACE,UAAC+W,GAAcA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAAA,CAAAA,EACT+C,GAAAA,CACJ,eAAgB7C,EAChB,qBAAsB1G,EACtB,SAAUiJ,EACV,cAAexZ,EAAK,IAAI,CAAC,aAAa,CACtC,SAAUga,EACV,cAAgBD,AAAAA,CAAqB,OAArBA,CAAAA,EAAAA,AAAa,OAAbA,CAAAA,EAAAA,EAAK,QAAQ,AAAD,GAAZA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAe,MAAM,AAAD,GAApBA,AAAAA,KAAAA,IAAAA,EAAAA,EAAyB,GAAK,EAC9C,UAAWD,EAAqB,YAAY,CAAC,QAAQ,CACrD,WAxBe,AAAC5C,IAClB,GAAM,CAAE+C,WAAAA,CAAU,CAAE,CAAGH,EAAqB,IAAI,CAEhD,IAAI,CAACG,EAID/C,EACF0B,EAAeqB,GAEfpB,EAAiBoB,EAErB,C,GAeF,EACA,aAAc,yBACd,aAAc,IAAItB,EAAchU,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAAS,CACzC,SAAUzD,C,EACN0W,IAEL2B,EACC,UAAC,OAAI,UAAU,yB,SACb,UAACvY,EAAAA,EAAUA,CAAAA,CAAC,KAAM,UAACkZ,EAAAA,GAAWA,CAAAA,CAAAA,GAAK,QAhN/B,KACZ,IAAMhS,EAAc8P,EAAe,CACjC/T,QAAAA,EACA,SAAU,GACV,aAAc4T,EACdpT,QAAAA,CACF,GAEAwT,EAAgB/P,GAChBoI,MAAAA,GAAAA,EAAWpI,GACX3H,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,EAAaC,EAAAA,EAAAA,CAAAA,kBAA8B,CAAE,CAC3C,WAAY4T,AAAAA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQ,UAAU,AAAD,GAAK,GAClC,cAAe,WACf,OAAQ,MACR,OAAQ,kBACR,cAAe,eACjB,EACF,E,SAgMatT,EAAAA,CAAAA,CAAAA,CAAM,CAAC,0C,KAGV,K,IAIZ,GCjWaqZ,GAA2B,AAAC,I,GAAA,CACvClD,eAAAA,CAAc,CAGf,G,MACC,WAAC,OACC,UAAWzG,IACT,wEACA,gE,UAGF,UAAC,OAAI,UAAU,kC,SACb,WAAC,OAAI,UAAU,yD,UACZ1P,EAAAA,CAAAA,CAAAA,CAAM,CAAC,+BACR,UAAC,QAAK,UAAU,kB,SAAkB,G,QAGtC,UAAC,OAAI,UAAU,kC,SACb,UAAC,OAAI,UAAU,yD,SACZA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,oC,KAGX,AAACmW,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAgB,QAAQ,CAAC,OAAM,EAM7B,KALF,UAAC,OAAI,UAAU,yE,SACb,UAAC,OAAI,UAAU,yD,SACZnW,EAAAA,CAAAA,CAAAA,CAAM,CAAC,4B,KAId,UAAC,OAAI,UAAU,yE,SACb,UAAC,OAAI,UAAU,yD,SACZA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,gC,KAGX,AAACmW,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAgB,QAAQ,CAAC,UAAS,EAMhC,KALF,UAAC,OAAI,UAAU,yE,SACb,UAAC,OAAI,UAAU,yD,SACZnW,EAAAA,CAAAA,CAAAA,CAAM,CAAC,wC,KAKd,UAAC,OAAI,UAAU,yE,SACb,UAAC,OAAI,UAAU,yD,SACZA,EAAAA,CAAAA,CAAAA,CAAM,CAAC,+B,SCxCHsZ,GAAiB,AAACC,IAC7B,IAAMC,EAAqB,EAAE,CAEvBC,EACJC,A1B0DG,UACLC,CAAkC,E,IAClC5Y,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,AAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAWC,IAELyG,EAAkB,EAAE,CAS1B,OARA9G,EACEgZ,EACAna,AAAAA,IACEiI,EAAI,IAAI,IAAIjI,EAAK,WAAW,CAC9B,EACA,eACAuB,GAEK0G,CACT,G0BxE0B,CAAC8R,EAAc,EAAE,MAAM,CAC3C/Z,AAAAA,QAASoa,EAAAA,E,MAAAA,AAAAA,CAAkC,OAAlCA,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,AAA0B,OAA1BA,CAAAA,EAAAA,EAAM,oBAAoB,AAAD,GAAzBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA4B,MAAM,AAAD,GAAjCA,AAAAA,KAAAA,IAAAA,EAAAA,EAAsC,GAAK,C,GACpD,MAAM,EAAI,EAERC,EAAiBN,EAAc,OAAO,GAAK3V,EAAAA,EAAAA,CAAAA,MAAsB,CASvE,OAPI6V,GACFD,EAAS,IAAI,CAAC,WAGZK,GACFL,EAAS,IAAI,CAAC,QAETA,CACT,EChBaM,GAKTpP,AAAAA,IACF,GAAM,CAAEqP,UAAAA,CAAS,CAAEzY,SAAAA,CAAQ,CAAEgG,MAAAA,EAAQ,CAAC,CAAE,CAAGoD,EACrC,CAACsP,EAAQC,EAAU,CAAGjS,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IAC/BkS,EAAa5S,AAAU,IAAVA,EACnB,MACE,uB,UACE,WAAC,OACC,UAAWoI,IACT,gDACAwK,EAAa,kDAAoD,IAEnE,QAAS,IAAMD,EAAU,CAACD,G,UAE1B,WAAC,OAAI,UAAU,oB,UACb,UAAC,OAAI,UAAU,oC,SACb,UAACtD,EAAAA,GAAiBA,CAAAA,CAChB,UAAWhH,IAAI,oBAAqBsK,EAAS,YAAc,G,KAG/D,UAAC,OAAI,UAAU,qC,SACb,UAAC,OACC,UAAWtK,IACT,0CACA,AAACwK,EAAoC,GAAvB,sB,SAGfH,EAAU,SAAS,A,QAIzBG,EACC,UAAC,OAAI,UAAU,qC,SACZH,EAAU,SAAS,A,GAEpB,K,GAEN,UAACI,EAAAA,EAAWA,CAAAA,CAAC,QAAO,GAAC,OAAQH,E,SAC3B,UAAC,OAAI,UAAWtK,IAAI,gBAAiB,AAACwK,EAA2B,GAAd,a,SAChD5Y,C,OAKX,EC1Ca8Y,GAAgB,AAAC1P,IAC5B,IAYS2P,EAkBQC,EA9BX,CACJP,UAAAA,CAAS,CACTvN,SAAAA,EAAW,EAAI,CACfiD,qBAAAA,EAAuB,EAAK,CAC5B8K,iBAAAA,CAAgB,CACjB,CAAG7P,EACEyL,EAAiBmD,GAAeS,GACtC,MACE,sB,SACE,WAACD,GAAuBA,CAAC,UAAWC,E,UAClC,UAACV,GAAwBA,CAAC,eAAgBlD,C,GAC1C,UAAC,OAAI,UAAU,O,SACU,OAAtBkE,CAAAA,EAAAA,EAAU,YAAY,AAAD,GAArBA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAwB,GAAG,CAAClW,AAAAA,I,IAKhBqW,E,MAJX,UAACV,GAAuBA,CAAC,UAAW3V,EAAU,MAAO,E,SACnD,UAACwS,GAAYA,CACX,eAAgBR,EAChB,QAAS4D,EAAU,OAAO,CAC1B,MAAOS,AAAoB,OAApBA,CAAAA,EAAAA,EAAS,WAAW,AAAD,GAAnBA,AAAAA,KAAAA,IAAAA,EAAAA,EAAwB,EAAE,CACjC,SAAUhO,EACV,QAASrI,EAAS,OAAO,CACzB,qBAAsBsL,EACtB,SAAU8K,C,SAKlB,UAAC,OAAI,UAAU,qB,SACb,UAAC5D,GAAYA,CACX,eAAgBR,EAChB,QAAS4D,EAAU,OAAO,CAC1B,MAAOO,AAAqB,OAArBA,CAAAA,EAAAA,EAAU,WAAW,AAAD,GAApBA,AAAAA,KAAAA,IAAAA,EAAAA,EAAyB,EAAE,CAClC,SAAU9N,EACV,QAASuN,EAAU,OAAO,CAC1B,qBAAsBtK,EACtB,SAAU8K,C,SAMtB,E,SC7CsBE,K,MAAAA,AAAAA,CAAAA,GAAf,kBAAsB1b,CAAiB,EAC5C,GAAM,CAAE2b,oBAAAA,CAAmB,CAAE1Y,eAAAA,CAAc,CAAE,CAC3C2D,GAAuB,QAAQ,EAMhB,KAAb8B,AALQ,OAAMnI,EAAAA,EAAAA,CAAAA,qBAA+B,CAAC,CAChD,UAAWP,EACX,aAAc2b,IAAsB,GAAG,CAAClb,AAAAA,GAAQwC,EAAexC,GACjE,EAAC,EAEO,IAAI,EACVoJ,EAAAA,EAAAA,CAAAA,OAAa,CAAC5I,EAAAA,CAAAA,CAAAA,CAAM,CAAC,kBAEzB,EAAC,EAXqBya,KAAAA,CAAAA,IAAAA,CAAAA,U,CAkBf,IAAME,GAAiB,AAAC5b,GAC7B,AAAqB,UAArB,OAAOA,GAA0BA,EAAU,MAAM,CAAG,E,eCzBhD6b,GAAqB,oCAEdC,GAAmB,AAACxK,IAC/B,GAAM,CAACyK,EAAiBC,EAAmB,CAAG/S,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAASgT,GAAW3K,IAClE,MAAO,CACLyK,gBAAAA,EACA,YAAa,KACX,IAAIE,GAAW3K,IAGf,IAAM4K,EAAWC,GAAAA,CAAAA,CAAAA,QAA4B,CAACN,KAAuB,GACrEM,GAAAA,CAAAA,CAAAA,QAA4B,CAC1BN,GACAK,EAAY,GAAc5K,MAAAA,CAAZ4K,EAAS,KAAO,OAAJ5K,GAAQA,GAEpC0K,EAAmB,IACrB,CACF,CACF,EAEMC,GAAa,AAAC3K,IAClB,IAAM4K,EAAWC,GAAAA,CAAAA,CAAAA,QAA4B,CAACN,IAC9C,OAAOK,MAAAA,EAAAA,KAAAA,EAAAA,EAAU,QAAQ,CAAC5K,EAC5B,ECvBa8K,GAAmB,KAC9B,GAAM,CAACC,EAAcC,EAAgB,CAAGrT,AAAAA,GAAAA,EAAAA,QAAAA,AAAAA,EAAS,IAC3C,CAAE8S,gBAAAA,CAAe,CAAEQ,YAAAA,CAAW,CAAE,CAAGT,GACvC,wCAgBF,MAAO,CACL,aAAcO,GAAgB,CAACN,EAC/BS,WAfiB,KACjBF,EAAgB,GAClB,EAcEG,WAZiB,KACjBH,EAAgB,GAClB,EAWEI,kBATwB,KACxBH,IACAD,EAAgB,GAClB,CAOA,CACF,ECMaK,GAAkB,AAAC,I,MAAA,CAC9B3c,UAAAA,CAAS,CACTC,QAAAA,CAAO,CACa,GACd2c,EAAavT,AAAAA,GAAAA,EAAAA,MAAAA,AAAAA,EAAuB,MAEpC,CAAEnJ,QAAAA,CAAO,CAAE,CAAG8J,GAAQhK,EAAWC,GACvC6K,KACA,GAAM,CAAE9B,qBAAAA,CAAoB,CAAED,kBAAAA,CAAiB,CAAE,CAAGD,KAC9C,CAAE5B,eAAAA,CAAc,CAAEkD,QAAAA,CAAO,CAAEyS,YAAAA,CAAW,CAAEC,gBAAAA,CAAe,CAAE,CAC7DlW,GACE6R,AAAAA,GAAAA,EAAAA,CAAAA,AAAAA,EAAWnR,AAAAA,GAAU,EACnB,eAAgBA,EAAM,cAAc,CACpC,QAASA,EAAM,OAAO,CACtB,YAAaA,EAAM,WAAW,CAC9B,gBAAiBA,EAAM,eAAe,AACxC,KAGE,CAAE+U,aAAAA,CAAY,CAAEG,WAAAA,CAAU,CAAEC,WAAAA,CAAU,CAAEC,kBAAAA,CAAiB,CAAE,CAC/DN,KAEIW,EAAU,CAAC7V,EAAe,MAAM,CAEhCsU,EAAmB,AAACwB,I,IAEpBC,EADJjU,EAAqB,IACjB,CAAgB,OAAhBiU,CAAAA,EAAAA,EAAY,IAAI,AAAD,GAAfA,AAAAA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAkB,SAAS,AAAD,GAC5BT,GAEJ,EAEA,IAAMU,G,EAAe,oBACnB,GAAI,CAACtB,GAAe5b,GAClB,OAEF,IAAMoQ,EAAUwM,EAAW,OAAO,CAClC,IAAI,CAACxM,IAID,CADY,OAAMA,EAAQ,QAAQ,EAAC,EAIvCyM,IACA,MAAMM,AHrEH,SAAsBnd,CAAiB,E,OAAxB0b,GAAAA,KAAAA,CAAAA,IAAAA,CAAAA,U,EGqEL1b,GACbgJ,EAAqB,GACvB,G,4CAEMoU,EAAaN,IAAkB,MAAM,CAAC,CAACnH,EAAK0H,KAChD1H,CAAG,CAAC0H,EAAK,UAAU,CAAC,CAAG,CAAE,KAAMA,EAAK,IAAI,AAAC,EAClC1H,GACN,CAAC,GASJ,MAPAnM,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,K,IAIR8T,EAHA,IAAIpd,E,AAGc,OAAlBod,CAAAA,EAAAA,EAAW,OAAO,AAAD,GAAjBA,AAAAA,KAAAA,IAAAA,GAAAA,EAAoB,SAAS,CAACF,EAChC,EAAG,CAACld,EAASkd,EAAW,EAGtB,UAACpS,GAAgB,QAAQ,EACvB,MAAO,CACL,oBAAqBZ,EACrB,OAAQlD,CACV,E,SAEA,UAAC,OAAI,UAAU,gB,SACZhH,EACC,UAAC,OAAI,UAAU,iD,SACb,UAACqd,EAAAA,EAAIA,CAAAA,CAAAA,E,GAELR,EACF,UAAC,OAAI,UAAU,iD,SACb,UAAChc,EAAAA,EAAKA,CAAAA,CACJ,MAAO,UAACC,EAAAA,EAAqBA,CAAAA,CAAC,UAAU,qB,GACxC,MAAOC,EAAAA,CAAAA,CAAAA,CAAM,CAAC,kC,KAIlB,uB,UACGob,EACC,WAAC,OAAI,UAAU,0G,UACb,UAAC,OAAI,UAAU,4B,SACZpb,EAAAA,CAAAA,CAAAA,CAAM,CAAC,gC,GAEV,WAAC,OAAI,UAAU,2C,UACb,UAAC,OACC,UAAU,2BACV,QAASyb,E,SAERzb,EAAAA,CAAAA,CAAAA,CAAM,CAAC,sB,GAEV,UAACE,EAAAA,EAAUA,CAAAA,CACT,UAAU,uBACV,QAASsb,EACT,KAAM,UAACe,EAAAA,GAAYA,CAAAA,CAAAA,E,SAIvB,KACJ,UAACC,EAAAA,EAAIA,CAAAA,CACH,WAAYrN,AAAAA,IACVwM,EAAW,OAAO,CAAGxM,CACvB,EACA,iBAAkB,GAClB,kBAAiB,GACjB,WAAYgN,E,SAEZ,UAAC,OAAI,UAAU,sB,SACZlW,EAAe,GAAG,CAACzG,AAAAA,GAClB,UAACid,GAAsBA,CACrB,SAAU,CAACtT,GAAW3J,EAAK,UAAU,CACrC,UAAWA,EACX,iBAAkB+a,EAClB,qBAAsB/a,EAAK,OAAO,GAAKoE,EAAAA,EAAAA,CAAAA,GAAmB,A,QAKlE,UAAC,OACC,UAAW8L,IACT,gCACA,sDACA,sB,SAGF,UAAC7G,EAAAA,EAAMA,CAAAA,CACL,QAASoT,EACT,SAAU,CAAC9S,GAAW,CAACrB,E,SAEtB9H,EAAAA,CAAAA,CAAAA,CAAM,CAAC,+B,WAQxB,ECtKa0c,GAAgB,KAE3B,GAAM,CAAE3d,UAAAA,EAAY,EAAE,CAAEC,QAAAA,CAAO,CAAE,CADlB2d,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAEf,MACE,UAAC,OACC,UAAW/c,IACT,gCACA,qD,SAGF,WAACgd,EAAAA,EAAMA,CAAAA,CACL,WAAU,GACV,KAAK,OACL,UAAWhd,IACT,uBAEA,gGACA,oCACA,8FAEF,gBAAgB,uC,UAEhB,UAACid,EAAAA,EAAOA,CAAAA,CAAC,IAAK7c,EAAAA,CAAAA,CAAAA,CAAM,CAAC,mBAAoB,QAAQ,S,SAC/C,UAAC0b,GAAeA,CAAC,UAAW3c,EAAW,QAASC,C,KAElD,UAAC6d,EAAAA,EAAOA,CAAAA,CAAC,IAAK7c,EAAAA,CAAAA,CAAAA,CAAM,CAAC,8BAA+B,QAAQ,S,SAC1D,UAACnB,EAAcA,CAAC,UAAWE,EAAW,QAASC,C,SAKzD,ECeA,GAzCa,KACX,IAQkB8d,EARZC,EAASC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IACT,CAAEC,OAAAA,CAAM,CAAE,CAAGC,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IACbne,EAAYoe,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAEZ,CAAEne,QAAAA,CAAO,CAAE,CAAGoe,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAEd,CAAEC,IAAAA,CAAG,CAAE,CAAGH,AAAAA,GAAAA,EAAAA,EAAAA,AAAAA,IAEVI,EAAYR,AAAc,OAAdA,CAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAK,IAAI,CAAC,IAAI,AAAD,GAAbA,AAAAA,KAAAA,IAAAA,EAAAA,EAAkB,GAOpC,MALAvU,AAAAA,GAAAA,EAAAA,SAAAA,AAAAA,EAAU,KACR0U,EAAO,QAAQ,CAACjd,EAAAA,CAAAA,CAAAA,CAAM,CAAC,eACvBid,EAAO,UAAU,CAAC,SACpB,EAAG,EAAE,EAGH,UAACM,EAAAA,EAA4BA,CAAAA,CAC3B,OAAQ,CACNve,QAAAA,EACAD,UAAAA,EACAue,UAAAA,EACA,IAAK,SACP,EACA,iBAAkB,CAEhB,WAAY,CAACE,EAAUC,EAAYC,EAAOC,IACxCZ,EAAQ,IAAeU,MAAAA,CAAZD,EAAS,KAAiBI,MAAAA,CAAdH,EAAW,KAAuB,OAApBG,IAAAA,SAAY,CAACF,IAAUC,GAC9D,OAAQ,CAACD,EAAOC,IACdZ,EACG,cAAwCa,MAAAA,CAA3BN,EAAU,mBAAqC,OAApBM,IAAAA,SAAY,CAACF,IACtDC,GAEJ,WAAYZ,CACd,E,SAEA,UAACL,GAAaA,CAAAA,E,EAGpB,C,8DCjEImB,EAA0B,A,SAA4B,KAE1DA,EAAwB,IAAI,CAAC,CAACC,EAAO,EAAE,CAAE,kjfAAmjf,GAAG,EAE/lfD,EAAwB,MAAM,CAAG,CAChC,MAAS,cACV,EACA,IAAeA,C"}