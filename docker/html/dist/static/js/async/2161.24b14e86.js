"use strict";(self.webpackChunk_coze_studio_app=self.webpackChunk_coze_studio_app||[]).push([["2161"],{198031:function(t,e,i){var r=i(130001),s=i(455069),n=i(998600);e.Z=function(t,e){var i=(0,r.CR)((0,s.useState)(t),2),o=i[0],a=i[1],l=(0,n.Z)(function(){a(t)},e).run;return(0,s.useEffect)(function(){l()},[t]),o}},117354:function(t,e,i){let r,s,n;i.d(e,{Ue:()=>cr,HL:()=>ci});var o,a,l,h,c,d,u,g,f,p,v,m,x,y,_,b,w,C,S,T,O,k,j,M,E=i("808549"),D=i("120454"),A=i("151064"),L=i("455069"),P=i("801815"),F=i("997516"),R=i("252237"),I=i.n(R),B=i("416290"),N=i("444479"),V=i("198031"),X=i("775834"),z=i("868398"),W=i("873183"),Y=i("659596"),H=i("803901"),G=i("614904"),U=i("569502"),Z=i("336205"),q=i("407821"),K=i.n(q),J=i("472772"),Q=i.n(J),$=i("395245"),tt=i.n($),te=i("297998"),ti=i.n(te),tr=i("646576"),ts=i.n(tr),tn=i("606121"),to=i.n(tn),ta=i("410391"),tl={};tl.styleTagTransform=to(),tl.setAttributes=ti(),tl.insert=tt().bind(null,"head"),tl.domAPI=Q(),tl.insertStyleElement=ts(),K()(ta.Z,tl);var th=ta.Z&&ta.Z.locals?ta.Z.locals:void 0,tc=["#000000","#ffffff","#C6C6CD","#FF441E","#3EC254","#4D53E8","#00B2B2","#FF9600"],td=t=>{var{color:e,size:i=24,onClick:r,selected:s,className:n}=t;return(0,A.jsx)("div",{onClick:r,className:"".concat(n," rounded-[4px]"),style:{backgroundColor:e,width:i,height:i},children:(0,A.jsx)("div",{className:I()(["relative top-0 left-0","flex items-center justify-center","rounded-[4px] border border-solid border-stroke"]),style:{width:i,height:i,color:"#ffffff"!==e?"#fff":"#000"},children:s?(0,A.jsx)(W.P03,{}):void 0})})},tu=t=>/^#[0-9A-Fa-f]{8}$/.test(t),tg=t=>/^#[0-9A-Fa-f]{6}$/.test(t),tf=t=>parseInt(t,16)/2.55,tp=t=>Math.floor(2.55*t).toString(16).padStart(2,"0"),tv=t=>{var{value:e="#ffffffff",onChange:i,showOpacity:r=!0,showColor:s=!0,readonly:n=!1}=t,{color:o,opacity:a}=(0,L.useMemo)(()=>s?{color:e.substring(0,7),opacity:tf(e.substring(7,9))}:{opacity:100*e},[e,s]),l=(0,L.useCallback)(t=>{tu(t)&&i(t)},[i]);return(0,A.jsxs)("div",{className:"flex flex-col w-full gap-[12px] text-[14px]",children:[s?(0,A.jsxs)("div",{className:"flex items-center w-full gap-[16px]",children:[(0,A.jsx)("div",{className:"flex items-center flex-1 gap-[12px]",children:tc.map(t=>{var e=t.toUpperCase()===o.toUpperCase();return(0,A.jsx)(td,{className:"".concat(n?"":"cursor-pointer"),selected:e,onClick:()=>{if(!n)l("".concat(t).concat(tp(a)))},color:t},"rect-".concat(t))})}),(0,A.jsx)(Y.II,{disabled:n,prefix:(0,A.jsx)(td,{color:o,size:16}),type:"text",className:"w-[110px]",defaultValue:o,onChange:t=>{tg(t)&&l("".concat(t).concat(tp(a)))}},"input-".concat(o))]}):void 0,r?(0,A.jsxs)("div",{className:"w-full flex items-center gap-[8px]",children:[(0,A.jsx)("div",{className:"min-w-[80px]",children:Z.o.t("imageflow_canvas_transparency")}),(0,A.jsx)("div",{className:I()("flex-1 min-w-[320px]",th["color-picker-slider"]),children:(0,A.jsx)(Y.iR,{min:0,showArrow:!1,max:100,step:1,value:a,disabled:n,onChange:t=>{s?l("".concat(o).concat(tp(t))):i(t/100)}})})]}):void 0]})},tm=i("473980"),tx=i("220193"),ty={};ty.styleTagTransform=to(),ty.setAttributes=ti(),ty.insert=tt().bind(null,"head"),ty.domAPI=Q(),ty.insertStyleElement=ts(),K()(tx.Z,ty);var t_=tx.Z&&tx.Z.locals?tx.Z.locals:void 0,tb=(0,L.forwardRef)((t,e)=>{var{className:i="",inForm:r=!1,color:s="secondary"}=t,n=(0,tm._)(t,["className","inForm","color"]);return(0,A.jsx)(Y.hU,(0,E._)({ref:e,className:I()([t_["icon-button"]],{"!p-[4px]":!r,"!p-[8px] !w-[32px] !h-[32px]":r,[t_["coz-fg-secondary"]]:"secondary"===s},i),size:"small",color:s},n))}),tw="variable";var tC=((o={}).INLINE_TEXT="inline_text",o.BLOCK_TEXT="block_text",o.RECT="rect",o.TRIANGLE="triangle",o.CIRCLE="ellipse",o.STRAIGHT_LINE="straight_line",o.PENCIL="pencil",o.IMAGE="img",o.GROUP="group",o);var tS=((a={}).FILL="fill",a.STROKE="stroke",a);var tT=((l={}).LEFT="left",l.CENTER="center",l.RIGHT="right",l.JUSTIFY="justify",l);var tO=((h={}).AUTO="auto",h.FILL="fill",h.FULL="full",h),tk="__unknown__";var tj=((c={}).CtrlCV="CtrlCV",c.CtrlD="CtrlD",c.DragCV="DragCV",c);var tM=((d={}).Left="left",d.Center="center",d.Right="right",d.Top="top",d.Middle="middle",d.Bottom="bottom",d.HorizontalAverage="horizontalAverage",d.VerticalAverage="verticalAverage",d);(g=(u=x||(x={})).ControlType||(u.ControlType={})).TopLeft="topLeft",g.TopRight="topRight",g.BottomLeft="bottomLeft",g.BottomRight="bottomRight",g.Top="top",g.Bottom="bottom",g.Left="left",g.Right="right",g.Center="center";var tE=i("665412"),tD={};tD.styleTagTransform=to(),tD.setAttributes=ti(),tD.insert=tt().bind(null,"head"),tD.domAPI=Q(),tD.insertStyleElement=ts(),K()(tE.Z,tD);var tA=tE.Z&&tE.Z.locals?tE.Z.locals:void 0,tL=(0,L.createContext)({}),tP=()=>(0,L.useContext)(tL),tF=i("825955"),tR=i("913552"),tI={};tI.styleTagTransform=to(),tI.setAttributes=ti(),tI.insert=tt().bind(null,"head"),tI.domAPI=Q(),tI.insertStyleElement=ts(),K()(tR.Z,tI);var tB=tR.Z&&tR.Z.locals?tR.Z.locals:void 0,tN={suffix:["jpg","jpeg","png","webp"],maxSize:5242880},tV=t=>{var e,i,{onChange:r,children:s,className:n,tooltip:o,key:a,disabledTooltip:l}=t,{uploadImg:h,clearImg:c,loading:d}=(0,G.wj)({rules:tN});var u=(e=(0,tF._)(function*(t){var{fileInstance:e}=t;c();var i=yield h(e);(null==i?void 0:i.isSuccess)&&(null==r||r(i.url))}),function(t){return e.apply(this,arguments)}),g=(0,A.jsx)("div",{className:I()([tB["loading-container"],n]),children:(0,A.jsx)(Y.gq,{action:"",disabled:d,customRequest:u,draggable:!0,accept:null===(i=tN.suffix)||void 0===i?void 0:i.map(t=>".".concat(t)).join(","),showUploadList:!1,onAcceptInvalid:()=>{var t;Y.FN.error(Z.o.t("imageflow_upload_error_type",{type:null===(t=tN.suffix)||void 0===t?void 0:t.join("/")}))},children:"function"==typeof s?s({loading:d,cancel:c}):d?(0,A.jsxs)("div",{children:[(0,A.jsx)(W.j2n,{className:"loading coz-fg-dim ".concat(tB["hover-hidden"])}),(0,A.jsx)(W.AYy,{onClick:t=>{t.stopPropagation(),c()},className:"coz-fg-dim hover-visible ".concat(tB["hover-visible"])})]}):s})});return l?g:(0,A.jsx)(Y.u,{content:d?Z.o.t("Cancel"):o,mouseEnterDelay:300,mouseLeaveDelay:300,getPopupContainer:()=>document.body,children:g},null!=a?a:"image")},tX=t=>{var{readonly:e,onChange:i,popRefAlignRight:r}=t,s=t=>{var{name:e,value:i,icon:r,suffix:s}=t;return(0,A.jsx)(Y.Ph.Option,{value:i,children:(0,A.jsxs)("div",{className:"w-[172px] px-[8px] flex gap-[4px] align-center coz-fg-primary",children:[(0,A.jsx)("div",{className:"text-[16px] flex items-center",children:r}),(0,A.jsx)("div",{className:"flex-1 text-[14px]",children:e}),(0,A.jsx)("div",{className:"coz-fg-secondary text-[12px]",children:s})]})})};return(0,A.jsx)("div",{onClick:t=>{t.stopPropagation()},children:(0,A.jsxs)(Y.Ph,{disabled:e,className:"hide-selected-label hide-border",dropdownClassName:tA["select-hidden-group-label"],showTick:!1,size:"small",getPopupContainer:()=>{var t;return null!==(t=null==r?void 0:r.current)&&void 0!==t?t:document.body},onSelect:t=>{i(t)},maxHeight:300,restTagsPopoverProps:{trigger:"hover"},children:[(0,A.jsx)(Y.Ph.OptGroup,{label:"a",children:[{name:Z.o.t("imageflow_canvas_align1",{},"左对齐"),value:tM.Left,icon:(0,A.jsx)(W.AjC,{}),suffix:"⌥ + A"},{name:Z.o.t("imageflow_canvas_align2",{},"水平居中"),value:tM.Center,icon:(0,A.jsx)(W.ZAo,{}),suffix:"⌥ + H"},{name:Z.o.t("imageflow_canvas_align3",{},"右对齐"),value:tM.Right,icon:(0,A.jsx)(W.qwv,{}),suffix:"⌥ + D"}].map(s)}),(0,A.jsx)(Y.Ph.OptGroup,{label:"b",children:[{name:Z.o.t("imageflow_canvas_align4",{},"顶部对齐"),value:tM.Top,icon:(0,A.jsx)(W.ur0,{}),suffix:"⌥ + W"},{name:Z.o.t("imageflow_canvas_align5",{},"垂直居中"),value:tM.Middle,icon:(0,A.jsx)(W.QQI,{}),suffix:"⌥ + V"},{name:Z.o.t("imageflow_canvas_align6",{},"底部对齐"),value:tM.Bottom,icon:(0,A.jsx)(W.opx,{}),suffix:"⌥ + S"}].map(s)}),(0,A.jsx)(Y.Ph.OptGroup,{label:"c",children:[{name:Z.o.t("imageflow_canvas_align7",{},"水平均分"),value:tM.VerticalAverage,icon:(0,A.jsx)(W.ukK,{}),suffix:"^ + ⌥ + H"},{name:Z.o.t("imageflow_canvas_align8",{},"垂直均分"),value:tM.HorizontalAverage,icon:(0,A.jsx)(W.xUr,{}),suffix:"^ + ⌥ + V"}].map(s)})]})})},tz=()=>(0,A.jsx)("div",{className:"h-[24px] w-[1px] coz-mg-primary-pressed"}),tW={[tC.INLINE_TEXT]:{icon:W.$pq,text:Z.o.t("imageflow_canvas_text1")},[tC.BLOCK_TEXT]:{icon:W.jLu,text:Z.o.t("imageflow_canvas_text2")}},tY={[tC.RECT]:{icon:W.JwK,text:Z.o.t("imageflow_canvas_rect")},[tC.CIRCLE]:{icon:W.$Ex,text:Z.o.t("imageflow_canvas_circle")},[tC.TRIANGLE]:{icon:W.vRq,text:Z.o.t("imageflow_canvas_trian")},[tC.STRAIGHT_LINE]:{icon:W.r9F,text:Z.o.t("imageflow_canvas_line")}},tH={[tM.Bottom]:W.opx,[tM.Center]:W.QQI,[tM.Middle]:W.ZAo,[tM.Left]:W.AjC,[tM.Right]:W.qwv,[tM.Top]:W.ur0,[tM.HorizontalAverage]:W.ukK,[tM.VerticalAverage]:W.xUr},tG=Z.o.t("add"),tU={mouseEnterDelay:300,mouseLeaveDelay:300,getPopupContainer:()=>document.body},tZ=t=>{var e,i,{popRefAlignRight:r,readonly:s,maxLimit:n,mode:o,onModeChange:a,onMoveToTop:l,onMoveToBackend:h,onAddImg:c,zoomSettings:d,canvasSettings:u,redo:g,undo:f,disabledUndo:p,disabledRedo:v,redoUndoing:m,isActiveObjectsInBack:x,isActiveObjectsInFront:y,aligns:_}=t,b=(0,L.useCallback)(t=>{t===o?a(void 0,o):a(t,o)},[a,o]),w=(0,L.useRef)(null),[C,S]=(0,L.useState)(tC.INLINE_TEXT),T=null===(e=tW[C])||void 0===e?void 0:e.icon,[O,k]=(0,L.useState)(tC.RECT),j=null===(i=tY[O])||void 0===i?void 0:i.icon,[M,P]=(0,L.useState)(tM.Left),F=tH[M];(0,H.Z)("esc",()=>{b(void 0)},{events:["keyup"]});var{variables:R,addRefObjectByVariable:B,customVariableRefs:N}=tP();return(0,A.jsxs)("div",{className:I()([tA["top-bar"],"flex justify-center items-center gap-[12px]"]),children:[(0,A.jsx)(Y.u,(0,D._)((0,E._)({content:Z.o.t("workflow_detail_condition_reference")},tU),{children:(0,A.jsx)("div",{children:(0,A.jsx)(Y.v2,{trigger:"click",position:"bottomLeft",className:"max-h-[300px] overflow-y-auto",render:(0,A.jsx)(Y.v2.SubMenu,{mode:"menu",children:(null!=R?R:[]).length>0?(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("div",{className:"p-[8px] pt-[4px] coz-fg-secondary text-[12px]",children:Z.o.t("imageflow_canvas_var_add",{},"添加变量")}),null==R?void 0:R.map(t=>{var e=null==N?void 0:N.filter(e=>e.variableId===t.id).length;return(0,A.jsx)(Y.v2.Item,{itemKey:t.name,disabled:!t.type,onClick:(e,i)=>{i.stopPropagation(),null==B||B(t)},children:(0,A.jsxs)("div",{className:"flex flex-row gap-[4px] items-center w-[220px] h-[32px]",children:[(0,A.jsxs)("div",{className:"flex flex-row gap-[4px] items-center flex-1 overflow-hidden w-full",children:[t.type?(0,A.jsx)(A.Fragment,{children:t.type===U.ow.String?(0,A.jsx)(W.ls7,{className:"coz-fg-dim"}):(0,A.jsx)(W.Q2$,{className:"coz-fg-dim"})}):(0,A.jsx)(A.Fragment,{}),(0,A.jsx)("div",{className:"flex-1 overflow-hidden flex flex-row gap-[4px] items-center",children:(0,A.jsx)(G.xv,{text:t.name})})]}),e&&e>0?(0,A.jsx)(Y.Vp,{size:"mini",color:"primary",children:Z.o.t("imageflow_canvas_var_reference",{n:e},"引用 ".concat(e," 次"))}):null]})},t.name)})]}):(0,A.jsx)(Y.ub,{className:"py-[16px] w-[200px]",size:"default",icon:(0,A.jsx)(W.nL$,{}),darkModeIcon:(0,A.jsx)(W.nL$,{}),title:Z.o.t("imageflow_canvas_var_no",{},"暂无变量")})}),children:(0,A.jsx)(tb,{icon:(0,A.jsx)(W.pFl,{className:"text-[16px]"})})})})}),"ref-variable"),(0,A.jsx)(tz,{}),(0,A.jsx)(Y.u,{position:"bottom",trigger:"click",getPopupContainer:()=>document.body,className:"!max-w-[600px]",content:(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("div",{ref:w}),(0,A.jsx)("div",{className:"flex flex-col gap-[12px] px-[4px] py-[8px] w-[410px] rounded-[12px] relative",children:(0,A.jsxs)(Y.iV,{getPopupContainer:()=>{var t;return null!==(t=w.current)&&void 0!==t?t:document.body},children:[(0,A.jsx)("div",{className:"text-[16px] font-semibold",children:Z.o.t("imageflow_canvas_setting")}),(0,A.jsx)("div",{children:Z.o.t("imageflow_canvas_frame")}),(0,A.jsx)(G.CA,{selectClassName:"w-[120px]",readonly:s,value:{width:u.width,height:u.height},minHeight:u.minHeight,minWidth:u.minWidth,maxHeight:u.maxHeight,maxWidth:u.maxWidth,onChange:t=>{u.onChange(t)},options:[{label:"16:9",value:{width:1920,height:1080}},{label:"9:16",value:{width:1080,height:1920}},{label:"1:1",value:{width:1024,height:1024}},{label:Z.o.t("imageflow_canvas_a41"),value:{width:1485,height:1050}},{label:Z.o.t("imageflow_canvas_a42"),value:{width:1050,height:1485}}]}),(0,A.jsx)("div",{children:Z.o.t("imageflow_canvas_color")}),(0,A.jsx)(tv,{readonly:s,value:u.background,onChange:t=>{u.onChange({background:t})}})]})})]}),children:(0,A.jsx)(tb,{icon:(0,A.jsx)(W.We2,{className:"text-[16px]"})})},"canvas-setting"),(0,A.jsx)(Y.u,(0,D._)((0,E._)({content:Z.o.t("imageflow_canvas_restart")},tU),{children:(0,A.jsx)(tb,{onClick:()=>{d.reset()},icon:(0,A.jsx)(W.ibo,{className:"text-[16px]"})})}),"reset-view"),(0,A.jsx)(tb,{disabled:s,onClick:()=>{d.onChange(Math.max(d.zoom-.1,d.min))},icon:(0,A.jsx)(W.ozt,{className:"text-[16px]"})}),(0,A.jsx)(Y.Rn,{disabled:s,className:"w-[60px]",suffix:"%",min:100*d.min,max:100*d.max,hideButtons:!0,precision:0,onNumberChange:t=>{d.onChange(t/100)},value:100*d.zoom}),(0,A.jsx)(tb,{disabled:s,onClick:()=>{d.onChange(Math.min(d.zoom+.1,d.max))},icon:(0,A.jsx)(W.PwN,{className:"text-[16px]"})}),(0,A.jsx)(tz,{}),(0,A.jsx)(Y.u,(0,D._)((0,E._)({content:Z.o.t("card_builder_redoUndo_undo")},tU),{children:(0,A.jsx)(tb,{loading:m,disabled:s||p,onClick:()=>{f()},icon:(0,A.jsx)(W.GES,{className:"text-[16px]"})})}),"undo"),(0,A.jsx)(Y.u,(0,D._)((0,E._)({content:Z.o.t("card_builder_redoUndo_redo")},tU),{children:(0,A.jsx)(tb,{loading:m,disabled:s||v,onClick:()=>{g()},icon:(0,A.jsx)(W.fwo,{className:"text-[16px]"})})}),"redo"),(0,A.jsx)(tz,{}),(0,A.jsx)(Y.u,(0,D._)((0,E._)({content:Z.o.t("card_builder_move_to_bottom")},tU),{children:(0,A.jsx)(tb,{disabled:s||x,onClick:h,icon:(0,A.jsx)(W.Eqh,{className:"text-[16px]"})})}),"move-to-bottom"),(0,A.jsx)(Y.u,(0,D._)((0,E._)({content:Z.o.t("card_builder_move_to_top")},tU),{children:(0,A.jsx)(tb,{disabled:s||y,onClick:l,icon:(0,A.jsx)(W.rIA,{className:"text-[16px]"})})}),"move-to-top"),(0,A.jsxs)("div",{className:"flex",children:[(0,A.jsx)(tb,{disabled:s,onClick:t=>{t.stopPropagation(),_[M]()},icon:(0,A.jsx)(F,{className:"text-[16px]"})}),(0,A.jsx)(tX,{readonly:s,onChange:t=>{P(t),_[t]()},popRefAlignRight:r})]}),(0,A.jsxs)("div",{className:"flex",children:[(0,A.jsx)(Y.u,(0,D._)((0,E._)({content:C===tC.INLINE_TEXT?"".concat(tG).concat(Z.o.t("imageflow_canvas_text1")):"".concat(tG).concat(Z.o.t("imageflow_canvas_text2"))},tU),{children:(0,A.jsx)(tb,{disabled:s||n,onClick:()=>{b(C)},className:I()({"!coz-mg-secondary-pressed":o&&[tC.INLINE_TEXT,tC.BLOCK_TEXT].includes(o)}),icon:(0,A.jsx)(T,{className:"text-[16px]"})})}),"text"),(0,A.jsx)(Y.Ph,{disabled:s||n,className:"hide-selected-label hide-border",value:C,size:"small",getPopupContainer:()=>{var t;return null!==(t=null==r?void 0:r.current)&&void 0!==t?t:document.body},optionList:Object.entries(tW).map(t=>{var[e,i]=t,r=i.icon,{text:s}=i;return{value:e,label:(0,A.jsxs)("div",{className:"px-[8px] flex gap-[8px] items-center",children:[(0,A.jsx)(r,{className:"text-[16px]"}),(0,A.jsx)("span",{children:s})]})}}),onSelect:t=>{S(t),b(t)}})]}),(0,A.jsx)(tV,{onChange:c,tooltip:"".concat(tG).concat(Z.o.t("card_builder_image")),children:(0,A.jsx)(tb,{disabled:s||n,icon:(0,A.jsx)(W.Q2$,{className:"text-[16px]"})})}),(0,A.jsxs)("div",{className:"flex",children:[(0,A.jsx)(Y.u,(0,D._)((0,E._)({content:(()=>{if(O===tC.CIRCLE)return"".concat(tG).concat(Z.o.t("imageflow_canvas_circle"));if(O===tC.TRIANGLE)return"".concat(tG).concat(Z.o.t("imageflow_canvas_trian"));if(O===tC.STRAIGHT_LINE)return"".concat(tG).concat(Z.o.t("imageflow_canvas_line"));return"".concat(tG).concat(Z.o.t("imageflow_canvas_rect"))})()},tU),{children:(0,A.jsx)(tb,{disabled:s||n,onClick:()=>{b(O)},className:I()({"!coz-mg-secondary-pressed":o&&[tC.RECT,tC.CIRCLE,tC.TRIANGLE,tC.STRAIGHT_LINE].includes(o)}),icon:(0,A.jsx)(j,{className:"text-[16px]"})})}),"shape"),(0,A.jsx)(Y.Ph,{disabled:s||n,className:"hide-selected-label hide-border",value:O,size:"small",getPopupContainer:()=>{var t;return null!==(t=null==r?void 0:r.current)&&void 0!==t?t:document.body},optionList:Object.entries(tY).map(t=>{var[e,i]=t,r=i.icon,{text:s}=i;return{value:e,label:(0,A.jsxs)("div",{className:"px-[8px] flex gap-[8px] items-center",children:[(0,A.jsx)(r,{className:"text-[16px]"}),(0,A.jsx)("span",{children:s})]})}}),onSelect:t=>{k(t),b(t)}})]}),(0,A.jsx)(Y.u,(0,D._)((0,E._)({content:Z.o.t("imageflow_canvas_draw")},tU),{children:(0,A.jsx)(tb,{disabled:s||n,onClick:()=>{b(tC.PENCIL)},className:I()({"!coz-mg-secondary-pressed":o&&[tC.PENCIL].includes(o)}),icon:(0,A.jsx)(W.cUz,{className:"text-[16px]"})})}),"pencil")]})},tq=t=>{var e,i,r,{visible:s,offsetX:n,offsetY:o}=t,{allObjectsPositionInScreen:a,customVariableRefs:l,variables:h,activeObjects:c}=tP(),d=null!==(i=null==a?void 0:null===(e=a.filter(t=>null==l?void 0:l.map(t=>t.objectId).includes(t.id)))||void 0===e?void 0:e.map((t,e)=>{var i=null==l?void 0:l.find(e=>e.objectId===t.id),r=null==h?void 0:h.find(t=>t.id===(null==i?void 0:i.variableId));return(0,D._)((0,E._)({},t),{active:!!(null==c?void 0:c.find(e=>e.customId===t.id)),unused:!r,zIndex:e+1,variable:r})}))&&void 0!==i?i:[];return(0,A.jsx)("div",{className:"relative w-full ".concat(s?"":"hidden"),style:{top:null!=o?o:0,left:null!=n?n:0},children:null!==(r=null==d?void 0:d.map(t=>{var e,i;return(0,A.jsx)("div",{style:{zIndex:t.active?999:t.zIndex,position:"absolute",width:"fit-content",top:"".concat(t.top-4,"px"),left:"".concat(t.left,"px"),transform:"translateY(-100%) rotate(".concat(t.angle,"deg) scale(1)"),transformOrigin:"0 calc(100% + ".concat(4,"px)"),opacity:t.active?1:.3,maxWidth:"200px",overflow:"hidden",whiteSpace:"nowrap"},className:"flex items-center gap-[3px]",children:t.unused?(0,A.jsx)(Y.Vp,{className:"w-full",color:"yellow",children:(0,A.jsx)("div",{className:"truncate w-full overflow-hidden",children:Z.o.t("imageflow_canvas_var_delete",{},"变量被删除")})}):(0,A.jsx)(Y.Vp,{className:"w-full",color:"primary",prefixIcon:(null===(e=t.variable)||void 0===e?void 0:e.type)===U.ow.Image?(0,A.jsx)(W.Q2$,{className:"coz-fg-dim"}):(0,A.jsx)(W.ls7,{className:"coz-fg-dim"}),children:(0,A.jsx)("div",{className:"truncate w-full overflow-hidden",children:null===(i=t.variable)||void 0===i?void 0:i.name})})},t.id)}))&&void 0!==r?r:void 0})},tK=i("228594"),tJ={};tJ.styleTagTransform=to(),tJ.setAttributes=ti(),tJ.insert=tt().bind(null,"head"),tJ.domAPI=Q(),tJ.insertStyleElement=ts(),K()(tK.Z,tJ);var tQ=tK.Z&&tK.Z.locals?tK.Z.locals:void 0,t$=(0,L.forwardRef)(t=>{var{onChange:e}=t,i=(0,tm._)(t,["onChange"]);return(0,A.jsx)(Y.q4,(0,D._)((0,E._)({},i),{onChange:t=>{null==e||e(t.target.value)}}))}),t0=i("691045"),t1={};t1.styleTagTransform=to(),t1.setAttributes=ti(),t1.insert=tt().bind(null,"head"),t1.domAPI=Q(),t1.insertStyleElement=ts(),K()(t0.Z,t1);var t2=t0.Z&&t0.Z.locals?t0.Z.locals:void 0,t3=i("412656"),t6=function(t,e,i){var r,s,n;return void 0===i&&(i=e,e=void 0),void 0!==i&&(i=(i=(0,t3.Z)(i))==i?i:0),void 0!==e&&(e=(e=(0,t3.Z)(e))==e?e:0),r=(0,t3.Z)(t),s=e,n=i,r==r&&(void 0!==n&&(r=r<=n?r:n),void 0!==s&&(r=r>=s?r:s)),r},t5=(0,L.forwardRef)(t=>{var{onChange:e,min:i,max:r,value:s}=t,n=(0,tm._)(t,["onChange","min","max","value"]);return(0,A.jsx)(Y.Rn,(0,D._)((0,E._)({},n),{min:i,max:r,value:s,pressInterval:864e5,onNumberChange:t=>{Number.isFinite(t)&&("number"==typeof i&&t<i?null==e||e(i):"number"==typeof r&&t>r?null==e||e(r):Number(t.toFixed(1))!==s&&(null==e||e(Number(t.toFixed(1)))))}}))}),t8=i("416013"),t4={};t4.styleTagTransform=to(),t4.setAttributes=ti(),t4.insert=tt().bind(null,"head"),t4.domAPI=Q(),t4.insertStyleElement=ts(),K()(t8.Z,t4);var t9=t8.Z&&t8.Z.locals?t8.Z.locals:void 0,t7={ColorPicker:tv,TextAlign:t=>{var{value:e,onChange:i}=t;return(0,A.jsx)(Y.Ph,{className:"border-0 hover:border-0 focus:border-0",value:e,onChange:t=>{i(t)},optionList:[{icon:(0,A.jsx)(W.D3A,{className:"text-[16px]"}),label:Z.o.t("card_builder_hover_align_left"),value:tT.LEFT},{icon:(0,A.jsx)(W.HVd,{className:"text-[16px]"}),label:Z.o.t("card_builder_hover_align_horizontal"),value:tT.CENTER},{icon:(0,A.jsx)(W.nGL,{className:"text-[16px]"}),label:Z.o.t("card_builder_hover_align_right"),value:tT.RIGHT}].map(t=>(0,D._)((0,E._)({},t),{label:(0,A.jsxs)("div",{className:"flex flex-row items-center gap-[4px]",children:[t.icon,t.label]})})),renderSelectedItem:t=>{var{icon:e}=t;return(0,A.jsx)("div",{className:"flex flex-row items-center",children:e})}})},InputNumber:t5,TextType:t=>{var{value:e,onChange:i}=t;return(0,A.jsxs)("div",{className:"flex gap-[12px]",children:[(0,A.jsx)(Y.u,{mouseEnterDelay:300,mouseLeaveDelay:300,content:Z.o.t("imageflow_canvas_text1"),children:(0,A.jsx)(tb,{inForm:!0,color:e===tC.INLINE_TEXT?"highlight":"secondary",onClick:()=>{i(tC.INLINE_TEXT)},icon:(0,A.jsx)(W.Ii3,{className:"text-[16px]"})})}),(0,A.jsx)(Y.u,{mouseEnterDelay:300,mouseLeaveDelay:300,content:Z.o.t("imageflow_canvas_text2"),children:(0,A.jsx)(tb,{inForm:!0,color:e===tC.BLOCK_TEXT?"highlight":"secondary",onClick:()=>{i(tC.BLOCK_TEXT)},icon:(0,A.jsx)(W.jLu,{className:"text-[16px]"})})})]})},SingleSelect:t$,BorderWidth:t=>{var{value:e,onChange:i,min:r,max:s}=t;return(0,A.jsx)("div",{className:I()("flex gap-[12px] text-[14px]",t9["imageflow-canvas-border-width"]),children:(0,A.jsxs)("div",{className:"w-full flex items-center gap-[8px]",children:[(0,A.jsx)("div",{className:"min-w-[42px]",children:Z.o.t("imageflow_canvas_stroke_width")}),(0,A.jsx)("div",{className:"flex-1 min-w-[320px]",children:(0,A.jsx)(Y.iR,{min:r,max:s,step:1,showArrow:!1,value:e,onChange:t=>{i(t)}})})]})})},Select:Y.Ph,TextFamily:t=>{var e,{onChange:i,value:r}=t,s=(0,tm._)(t,["onChange","value"]);return(0,A.jsx)(Y.vT,(0,D._)((0,E._)({},s),{value:null==r?void 0:null===(e=r.split("-"))||void 0===e?void 0:e.reverse(),onChange:t=>{var e;null==i||i(null==t?void 0:null===(e=t.reverse())||void 0===e?void 0:e.join("-"))},dropdownClassName:tQ["imageflow-canvas-font-family-cascader"]}))},FontSize:t=>{var{onChange:e,min:i,max:r,optionList:s,value:n}=t,o=(0,tm._)(t,["onChange","min","max","optionList","value"]),a=(0,L.useCallback)(t=>{isFinite(t)&&(null==e||e(t6(t,i,r)))},[e,i,r]),l=(0,L.useMemo)(()=>{var t=[...null!=s?s:[]];return!t.map(t=>t.value).includes(n)&&t.unshift({label:"".concat(n),value:n}),t},[s,n]);return(0,A.jsx)("div",{className:"flex gap-[8px] items-center",children:(0,A.jsx)(Y.u,{content:Z.o.t("imageflow_canvas_text_tooltip1"),mouseEnterDelay:300,mouseLeaveDelay:300,children:(0,L.createElement)(Y.Ph,(0,D._)((0,E._)({},o),{prefix:(0,A.jsx)(W.yW6,{className:"text-[16px] coz-fg-secondary m-[8px]"}),key:l.map(t=>t.label).join(),value:n,optionList:l,filter:!0,allowCreate:!0,onChange:t=>{a(t)},style:{width:"98px"}}))})})},LineHeight:t=>{var{onChange:e,min:i,max:r,value:s,optionList:n}=t,o=(0,tm._)(t,["onChange","min","max","value","optionList"]),a=(0,L.useCallback)(t=>{var s=Number("".concat(t).replace("%",""));isFinite(s)&&(null==e||e(Number((t6(s,i,r)/100).toFixed(2))))},[e,i,r]),l=(0,L.useMemo)(()=>{var t=[...null!=n?n:[]];return!t.map(t=>t.value).includes(Number((100*Number("".concat(s).replace("%",""))).toFixed(0)))&&t.unshift({label:"".concat(Number((100*s).toFixed(0)),"%"),value:Number((100*s).toFixed(0))}),t},[n,s]);return(0,A.jsx)("div",{className:"flex gap-[8px] items-center",children:(0,A.jsx)(Y.u,{content:Z.o.t("imageflow_canvas_text_tooltip2"),mouseEnterDelay:300,mouseLeaveDelay:300,children:(0,L.createElement)(Y.Ph,(0,D._)((0,E._)({prefix:(0,A.jsx)(W.kw4,{className:"text-[16px] coz-fg-secondary m-[8px]"})},o),{key:l.map(t=>t.label).join(),filter:!0,value:Number((100*s).toFixed(0)),allowCreate:!0,onChange:t=>{a(t)},optionList:l,style:{width:"104px"}}))})})},LabelSelect:t=>{var{label:e}=t,i=(0,tm._)(t,["label"]);return(0,A.jsxs)("div",{className:"w-full flex gap-[8px] justify-between items-center text-[14px]",children:[(0,A.jsx)("div",{className:"min-w-[80px]",children:e}),(0,A.jsx)(Y.Ph,(0,E._)({},i))]})},Uploader:t=>{var{getLabel:e,onChange:i,isRefElement:r}=t;return(0,A.jsxs)("div",{className:"w-full flex gap-[8px] justify-between items-center text-[14px]",children:[(0,A.jsx)("div",{className:"min-w-[80px]",children:e(r)}),(0,A.jsx)(tV,{disabledTooltip:!0,onChange:i,tooltip:Z.o.t("card_builder_image"),className:"flex-1",children:t=>{var{loading:e,cancel:i}=t;return(0,A.jsx)(Y.zx,{className:"w-full",color:"primary",onClick:()=>{e&&i()},icon:e?(0,A.jsx)(W.j2n,{className:"loading coz-fg-dim"}):(0,A.jsx)(W.Isu,{}),children:e?Z.o.t("imageflow_canvas_cancel_change",{},"取消上传"):Z.o.t("imageflow_canvas_change_img",{},"更换图片")})}})]})},RefSelect:t=>{var{value:e,label:i,labelInside:r,className:s}=t,{customVariableRefs:n,variables:o,updateRefByObjectId:a}=tP(),l=null==n?void 0:n.find(t=>t.objectId===e),h=null==l?void 0:l.variableId,c=null==o?void 0:o.find(t=>t.id===h);return(0,A.jsxs)("div",{className:"w-full text-[14px] flex flex-row gap-[8px] items-center",children:[!r&&i?(0,A.jsx)("div",{className:"text-[14px] min-w-[80px]",children:i}):(0,A.jsx)(A.Fragment,{}),(0,A.jsxs)(Y.Ph,{prefix:r&&i?(0,A.jsx)("div",{className:"text-[14px] pl-[8px] pr-[4px] py-[2px] min-w-[40px]",children:i}):void 0,showClear:!0,showTick:!1,placeholder:Z.o.t("imageflow_canvas_select_var",{},"选择变量"),value:null==l?void 0:l.variableId,className:I()(s,t2["ref-select"]),onChange:t=>{null==a||a({objectId:e,variable:t?null==o?void 0:o.find(e=>e.id===t):void 0})},renderSelectedItem:t=>{var{value:e,label:i}=t,r=null==o?void 0:o.find(t=>t.id===e);return r?(0,A.jsx)(Y.Vp,{color:"primary",className:"w-full",children:(0,A.jsxs)("div",{className:"flex flex-row items-center gap-[4px] w-full",children:[r.type===U.ow.String?(0,A.jsx)(W.ls7,{className:"coz-fg-dim"}):(0,A.jsx)(W.Q2$,{className:"coz-fg-dim"}),(0,A.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,A.jsx)("div",{className:"truncate w-full overflow-hidden",children:r.name})})]})}):i},children:[h&&!c?(0,A.jsx)(Y.Ph.Option,{value:h,children:(0,A.jsx)(Y.Vp,{className:"max-w-full m-[8px]",color:"yellow",children:(0,A.jsx)("div",{className:"truncate overflow-hidden",children:Z.o.t("imageflow_canvas_var_delete",{},"变量被删除")})})}):(0,A.jsx)(A.Fragment,{}),null==o?void 0:o.map(t=>(0,A.jsx)(Y.Ph.Option,{value:t.id,children:(0,A.jsxs)("div",{className:"flex flex-row items-center gap-[4px] w-full p-[8px] max-w-[400px]",children:[t.type===U.ow.String?(0,A.jsx)(W.ls7,{className:"coz-fg-dim"}):(0,A.jsx)(W.Q2$,{className:"coz-fg-dim"}),(0,A.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,A.jsx)("div",{className:"truncate w-full overflow-hidden",children:t.name})}),t.id===h?(0,A.jsx)(Y.Vp,{color:"primary",children:Z.o.t("eval_status_referenced",{},"已引用")}):null]})}))]})]})}};function et(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)}return i}function ee(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?et(Object(i),!0).forEach(function(e){ei(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):et(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function ei(t,e,i){return(e=en(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function er(t,e){if(null==t)return{};var i,r,s=function(t,e){if(null==t)return{};var i,r,s={},n=Object.keys(t);for(r=0;r<n.length;r++)i=n[r],e.indexOf(i)>=0||(s[i]=t[i]);return s}(t,e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);for(r=0;r<n.length;r++)i=n[r],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(s[i]=t[i])}return s}function es(t,e){return e||(e=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function en(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}class eo{constructor(){ei(this,"browserShadowBlurConstant",1),ei(this,"DPI",96),ei(this,"devicePixelRatio","undefined"!=typeof window?window.devicePixelRatio:1),ei(this,"perfLimitSizeTotal",2097152),ei(this,"maxCacheSideLimit",4096),ei(this,"minCacheSideLimit",256),ei(this,"disableStyleCopyPaste",!1),ei(this,"enableGLFiltering",!0),ei(this,"textureSize",4096),ei(this,"forceGLPutImageData",!1),ei(this,"cachesBoundsOfCurve",!0),ei(this,"fontPaths",{}),ei(this,"NUM_FRACTION_DIGITS",4)}}let ea=new class extends eo{constructor(t){super(),this.configure(t)}configure(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Object.assign(this,t)}addFonts(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fontPaths=ee(ee({},this.fontPaths),t)}removeFonts(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach(t=>{delete this.fontPaths[t]})}clearFonts(){this.fontPaths={}}restoreDefaults(t){let e=new eo,i=(null==t?void 0:t.reduce((t,i)=>(t[i]=e[i],t),{}))||e;this.configure(i)}},el=function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return console[t]("fabric",...i)};class eh extends Error{constructor(t,e){super("fabric: ".concat(t),e)}}class ec extends eh{constructor(t){super("".concat(t," 'options.signal' is in 'aborted' state"))}}class ed{}class eu extends ed{testPrecision(t,e){let i=t.createShader(t.FRAGMENT_SHADER);return!!i&&(t.shaderSource(i,"precision ".concat(e," float;\nvoid main(){}")),t.compileShader(i),!!t.getShaderParameter(i,t.COMPILE_STATUS))}queryWebGL(t){let e=t.getContext("webgl");e&&(this.maxTextureSize=e.getParameter(e.MAX_TEXTURE_SIZE),this.GLPrecision=["highp","mediump","lowp"].find(t=>this.testPrecision(e,t)),e.getExtension("WEBGL_lose_context").loseContext(),el("log","WebGL: max texture size ".concat(this.maxTextureSize)))}isSupported(t){return!!this.maxTextureSize&&this.maxTextureSize>=t}}let eg={},ef=()=>r||(r={document:document,window:window,isTouchSupported:"ontouchstart"in window||"ontouchstart"in document||window&&window.navigator&&window.navigator.maxTouchPoints>0,WebGLProbe:new eu,dispose(){},copyPasteData:eg}),ep=()=>ef().document,ev=()=>ef().window,em=()=>{var t;return Math.max(null!==(t=ea.devicePixelRatio)&&void 0!==t?t:ev().devicePixelRatio,1)},ex=new class{constructor(){ei(this,"charWidthsCache",{}),ei(this,"boundsOfCurveCache",{})}getFontCache(t){let{fontFamily:e,fontStyle:i,fontWeight:r}=t;e=e.toLowerCase(),this.charWidthsCache[e]||(this.charWidthsCache[e]={});let s=this.charWidthsCache[e],n="".concat(i.toLowerCase(),"_").concat((r+"").toLowerCase());return s[n]||(s[n]={}),s[n]}clearFontCache(t){(t=(t||"").toLowerCase())?this.charWidthsCache[t]&&delete this.charWidthsCache[t]:this.charWidthsCache={}}limitDimsByArea(t){let{perfLimitSizeTotal:e}=ea,i=Math.sqrt(e*t);return[Math.floor(i),Math.floor(e/i)]}},ey="6.0.0-rc2";function e_(){}let eb=Math.PI/2,ew=2*Math.PI,eC=Math.PI/180,eS=Object.freeze([1,0,0,1,0,0]),eT="center",eO="left",ek="bottom",ej="right",eM="none",eE=/\r?\n/,eD="json",eA=new class{constructor(){this[eD]=new Map,this.svg=new Map}getClass(t){let e=this[eD].get(t);if(!e)throw new eh("No class registered for ".concat(t));return e}setClass(t,e){e?this[eD].set(e,t):(this[eD].set(t.type,t),this[eD].set(t.type.toLowerCase(),t))}getSVGClass(t){return this.svg.get(t)}setSVGClass(t,e){this.svg.set(null!=e?e:t.type.toLowerCase(),t)}},eL=new class extends Array{remove(t){let e=this.indexOf(t);e>-1&&this.splice(e,1)}cancelAll(){let t=this.splice(0);return t.forEach(t=>t.abort()),t}cancelByCanvas(t){if(!t)return[];let e=this.filter(e=>{var i;return e.target===t||"object"==typeof e.target&&(null===(i=e.target)||void 0===i?void 0:i.canvas)===t});return e.forEach(t=>t.abort()),e}cancelByTarget(t){if(!t)return[];let e=this.filter(e=>e.target===t);return e.forEach(t=>t.abort()),e}};class eP{constructor(){ei(this,"__eventListeners",{})}on(t,e){return(this.__eventListeners||(this.__eventListeners={}),"object"==typeof t)?(Object.entries(t).forEach(t=>{let[e,i]=t;this.on(e,i)}),()=>this.off(t)):e?(this.__eventListeners[t]||(this.__eventListeners[t]=[]),this.__eventListeners[t].push(e),()=>this.off(t,e)):()=>!1}once(t,e){if("object"==typeof t){let e=[];return Object.entries(t).forEach(t=>{let[i,r]=t;e.push(this.once(i,r))}),()=>e.forEach(t=>t())}if(e){let i=this.on(t,function(){for(var t=arguments.length,r=Array(t),s=0;s<t;s++)r[s]=arguments[s];e.call(this,...r),i()});return i}return()=>!1}_removeEventListener(t,e){if(this.__eventListeners[t]){if(e){let i=this.__eventListeners[t],r=i.indexOf(e);r>-1&&i.splice(r,1)}else this.__eventListeners[t]=[]}}off(t,e){if(this.__eventListeners){if(void 0===t)for(let t in this.__eventListeners)this._removeEventListener(t);else"object"==typeof t?Object.entries(t).forEach(t=>{let[e,i]=t;this._removeEventListener(e,i)}):this._removeEventListener(t,e)}}fire(t,e){var i;if(!this.__eventListeners)return;let r=null===(i=this.__eventListeners[t])||void 0===i?void 0:i.concat();if(r)for(let t=0;t<r.length;t++)r[t].call(this,e||{})}}let eF=(t,e)=>isNaN(t)&&"number"==typeof e?e:t,eR=(t,e)=>{let i=t.indexOf(e);return -1!==i&&t.splice(i,1),t},eI=t=>{if(0===t)return 1;switch(Math.abs(t)/eb){case 1:case 3:return 0;case 2:return -1}return Math.cos(t)},eB=t=>{if(0===t)return 0;let e=Math.sign(t);switch(t/eb){case 1:return e;case 2:return 0;case 3:return-e}return Math.sin(t)};class eN{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;"object"==typeof t?(this.x=t.x,this.y=t.y):(this.x=t,this.y=e)}add(t){return new eN(this.x+t.x,this.y+t.y)}addEquals(t){return this.x+=t.x,this.y+=t.y,this}scalarAdd(t){return new eN(this.x+t,this.y+t)}scalarAddEquals(t){return this.x+=t,this.y+=t,this}subtract(t){return new eN(this.x-t.x,this.y-t.y)}subtractEquals(t){return this.x-=t.x,this.y-=t.y,this}scalarSubtract(t){return new eN(this.x-t,this.y-t)}scalarSubtractEquals(t){return this.x-=t,this.y-=t,this}multiply(t){return new eN(this.x*t.x,this.y*t.y)}scalarMultiply(t){return new eN(this.x*t,this.y*t)}scalarMultiplyEquals(t){return this.x*=t,this.y*=t,this}divide(t){return new eN(this.x/t.x,this.y/t.y)}scalarDivide(t){return new eN(this.x/t,this.y/t)}scalarDivideEquals(t){return this.x/=t,this.y/=t,this}eq(t){return this.x===t.x&&this.y===t.y}lt(t){return this.x<t.x&&this.y<t.y}lte(t){return this.x<=t.x&&this.y<=t.y}gt(t){return this.x>t.x&&this.y>t.y}gte(t){return this.x>=t.x&&this.y>=t.y}lerp(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5;return e=Math.max(Math.min(1,e),0),new eN(this.x+(t.x-this.x)*e,this.y+(t.y-this.y)*e)}distanceFrom(t){let e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)}midPointFrom(t){return this.lerp(t)}min(t){return new eN(Math.min(this.x,t.x),Math.min(this.y,t.y))}max(t){return new eN(Math.max(this.x,t.x),Math.max(this.y,t.y))}toString(){return"".concat(this.x,",").concat(this.y)}setXY(t,e){return this.x=t,this.y=e,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setFromPoint(t){return this.x=t.x,this.y=t.y,this}swap(t){let e=this.x,i=this.y;this.x=t.x,this.y=t.y,t.x=e,t.y=i}clone(){return new eN(this.x,this.y)}rotate(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eV,i=eB(t),r=eI(t),s=this.subtract(e);return new eN(s.x*r-s.y*i,s.x*i+s.y*r).add(e)}transform(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new eN(t[0]*this.x+t[2]*this.y+(e?0:t[4]),t[1]*this.x+t[3]*this.y+(e?0:t[5]))}}let eV=new eN(0,0),eX=t=>!!t&&Array.isArray(t._objects);function ez(t){class e extends t{constructor(){super(...arguments),ei(this,"_objects",[])}_onObjectAdded(t){}_onObjectRemoved(t){}_onStackOrderChanged(t){}add(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];let r=this._objects.push(...e);return e.forEach(t=>this._onObjectAdded(t)),r}insertAt(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return this._objects.splice(t,0,...i),i.forEach(t=>this._onObjectAdded(t)),this._objects.length}remove(){let t=this._objects,e=[];for(var i=arguments.length,r=Array(i),s=0;s<i;s++)r[s]=arguments[s];return r.forEach(i=>{let r=t.indexOf(i);-1!==r&&(t.splice(r,1),e.push(i),this._onObjectRemoved(i))}),e}forEachObject(t){this.getObjects().forEach((e,i,r)=>t(e,i,r))}getObjects(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return 0===e.length?[...this._objects]:this._objects.filter(t=>t.isType(...e))}item(t){return this._objects[t]}isEmpty(){return 0===this._objects.length}size(){return this._objects.length}contains(t,i){return!!this._objects.includes(t)||!!i&&this._objects.some(i=>i instanceof e&&i.contains(t,!0))}complexity(){return this._objects.reduce((t,e)=>t+=e.complexity?e.complexity():0,0)}sendObjectToBack(t){return!(!t||t===this._objects[0])&&(eR(this._objects,t),this._objects.unshift(t),this._onStackOrderChanged(t),!0)}bringObjectToFront(t){return!(!t||t===this._objects[this._objects.length-1])&&(eR(this._objects,t),this._objects.push(t),this._onStackOrderChanged(t),!0)}sendObjectBackwards(t,e){if(!t)return!1;let i=this._objects.indexOf(t);if(0!==i){let r=this.findNewLowerIndex(t,i,e);return eR(this._objects,t),this._objects.splice(r,0,t),this._onStackOrderChanged(t),!0}return!1}bringObjectForward(t,e){if(!t)return!1;let i=this._objects.indexOf(t);if(i!==this._objects.length-1){let r=this.findNewUpperIndex(t,i,e);return eR(this._objects,t),this._objects.splice(r,0,t),this._onStackOrderChanged(t),!0}return!1}moveObjectTo(t,e){return t!==this._objects[e]&&(eR(this._objects,t),this._objects.splice(e,0,t),this._onStackOrderChanged(t),!0)}findNewLowerIndex(t,e,i){let r;if(i){r=e;for(let i=e-1;i>=0;--i)if(t.isOverlapping(this._objects[i])){r=i;break}}else r=e-1;return r}findNewUpperIndex(t,e,i){let r;if(i){r=e;for(let i=e+1;i<this._objects.length;++i)if(t.isOverlapping(this._objects[i])){r=i;break}}else r=e+1;return r}collectObjects(t){let{left:e,top:i,width:r,height:s}=t,{includeIntersecting:n=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=[],a=new eN(e,i),l=a.add(new eN(r,s));for(let t=this._objects.length-1;t>=0;t--){let e=this._objects[t];e.selectable&&e.visible&&(n&&e.intersectsWithRect(a,l)||e.isContainedWithinRect(a,l)||n&&e.containsPoint(a)||n&&e.containsPoint(l))&&o.push(e)}return o}}return e}class eW extends eP{_setOptions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};for(let e in t)this.set(e,t[e])}_setObject(t){for(let e in t)this._set(e,t[e])}set(t,e){return"object"==typeof t?this._setObject(t):this._set(t,e),this}_set(t,e){this[t]=e}toggle(t){let e=this.get(t);return"boolean"==typeof e&&this.set(t,!e),this}get(t){return this[t]}}function eY(t){return ev().requestAnimationFrame(t)}function eH(t){return ev().cancelAnimationFrame(t)}let eG=0,eU=()=>eG++,eZ=()=>{let t=ep().createElement("canvas");if(!t||void 0===t.getContext)throw new eh("Failed to create `canvas` element");return t},eq=()=>ep().createElement("img"),eK=(t,e,i)=>t.toDataURL("image/".concat(e),i),eJ=t=>t*eC,eQ=t=>t/eC,e$=t=>t.every((t,e)=>t===eS[e]),e0=(t,e,i)=>new eN(t).transform(e,i),e1=t=>{let e=1/(t[0]*t[3]-t[1]*t[2]),i=[e*t[3],-e*t[1],-e*t[2],e*t[0],0,0],{x:r,y:s}=new eN(t[4],t[5]).transform(i,!0);return i[4]=-r,i[5]=-s,i},e2=(t,e,i)=>[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],i?0:t[0]*e[4]+t[2]*e[5]+t[4],i?0:t[1]*e[4]+t[3]*e[5]+t[5]],e3=(t,e)=>t.reduceRight((t,i)=>i&&t?e2(i,t,e):i||t,void 0)||eS.concat(),e6=t=>{let[e,i]=t;return Math.atan2(i,e)},e5=t=>{let e=e6(t),i=Math.pow(t[0],2)+Math.pow(t[1],2),r=Math.sqrt(i),s=(t[0]*t[3]-t[2]*t[1])/r,n=Math.atan2(t[0]*t[2]+t[1]*t[3],i);return{angle:eQ(e),scaleX:r,scaleY:s,skewX:eQ(n),skewY:0,translateX:t[4]||0,translateY:t[5]||0}},e8=function(t){return[1,0,0,1,t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0]};function e4(){let{angle:t=0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{x:e=0,y:i=0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=eJ(t),s=eI(r),n=eB(r);return[s,n,-n,s,e?e-(s*e-n*i):0,i?i-(n*e+s*i):0]}let e9=function(t){return[t,0,0,arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,0,0]},e7=t=>Math.tan(eJ(t)),it=t=>[1,0,e7(t),1,0,0],ie=t=>[1,e7(t),0,1,0,0],ii=t=>{let{scaleX:e=1,scaleY:i=1,flipX:r=!1,flipY:s=!1,skewX:n=0,skewY:o=0}=t,a=e9(r?-e:e,s?-i:i);return n&&(a=e2(a,it(n),!0)),o&&(a=e2(a,ie(o),!0)),a},ir=t=>{let{translateX:e=0,translateY:i=0,angle:r=0}=t,s=e8(e,i);r&&(s=e2(s,e4({angle:r})));let n=ii(t);return e$(n)||(s=e2(s,n)),s},is=function(t){let{signal:e,crossOrigin:i=null}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise(function(r,s){let n;if(e&&e.aborted)return s(new ec("loadImage"));let o=eq();e&&(n=function(t){o.src="",s(t)},e.addEventListener("abort",n,{once:!0}));let a=function(){o.onload=o.onerror=null,n&&(null==e||e.removeEventListener("abort",n)),r(o)};t?(o.onload=a,o.onerror=function(){n&&(null==e||e.removeEventListener("abort",n)),s(new eh("Error loading ".concat(o.src)))},i&&(o.crossOrigin=i),o.src=t):a()})},io=function(t){let{signal:e,reviver:i=e_}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((r,s)=>{let n=[];e&&e.addEventListener("abort",s,{once:!0}),Promise.all(t.map(t=>eA.getClass(t.type).fromObject(t,{signal:e}).then(e=>(i(t,e),n.push(e),e)))).then(r).catch(t=>{n.forEach(t=>{t.dispose&&t.dispose()}),s(t)}).finally(()=>{e&&e.removeEventListener("abort",s)})})},ia=function(t){let{signal:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((i,r)=>{let s=[];e&&e.addEventListener("abort",r,{once:!0});let n=Object.values(t).map(t=>t?t.type?io([t],{signal:e}).then(t=>{let[e]=t;return s.push(e),e}):t.source?eA.getClass("pattern").fromObject(t,{signal:e}).then(t=>(s.push(t),t)):t:t),o=Object.keys(t);Promise.all(n).then(t=>t.reduce((t,e,i)=>(t[o[i]]=e,t),{})).then(i).catch(t=>{s.forEach(t=>{t.dispose&&t.dispose()}),r(t)}).finally(()=>{e&&e.removeEventListener("abort",r)})})},il=function(t){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[]).reduce((e,i)=>(i in t&&(e[i]=t[i]),e),{})},ih=(t,e)=>Object.keys(t).reduce((i,r)=>(e(t[r],r,t)&&(i[r]=t[r]),i),{}),ic={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aqua:"#0FF",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000",blanchedalmond:"#FFEBCD",blue:"#00F",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#0FF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgrey:"#A9A9A9",darkgreen:"#006400",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",fuchsia:"#F0F",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgray:"#D3D3D3",lightgrey:"#D3D3D3",lightgreen:"#90EE90",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#789",lightslategrey:"#789",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",lime:"#0F0",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#F0F",maroon:"#800000",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",navy:"#000080",oldlace:"#FDF5E6",olive:"#808000",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",purple:"#800080",rebeccapurple:"#639",red:"#F00",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",silver:"#C0C0C0",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",teal:"#008080",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",white:"#FFF",whitesmoke:"#F5F5F5",yellow:"#FF0",yellowgreen:"#9ACD32"},id=(t,e,i)=>(i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t),iu=(t,e,i,r)=>{let s,n;t/=255,e/=255;let o=Math.max(t,e,i/=255),a=Math.min(t,e,i),l=(o+a)/2;if(o===a)s=n=0;else{let r=o-a;switch(n=l>.5?r/(2-o-a):r/(o+a),o){case t:s=(e-i)/r+(e<i?6:0);break;case e:s=(i-t)/r+2;break;case i:s=(t-e)/r+4}s/=6}return[Math.round(360*s),Math.round(100*n),Math.round(100*l),r]},ig=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"1";return parseFloat(t)/(t.endsWith("%")?100:1)},ip=t=>Math.min(Math.round(t),255).toString(16).toUpperCase().padStart(2,"0"),iv=t=>{let[e,i,r,s=1]=t,n=Math.round(.3*e+.59*i+.11*r);return[n,n,n,s]};class im{constructor(t){if(t){if(t instanceof im)this.setSource([...t._source]);else if(Array.isArray(t)){let[e,i,r,s=1]=t;this.setSource([e,i,r,s])}else this.setSource(this._tryParsingColor(t))}else this.setSource([0,0,0,1])}_tryParsingColor(t){return t in ic&&(t=ic[t]),"transparent"===t?[255,255,255,0]:im.sourceFromHex(t)||im.sourceFromRgb(t)||im.sourceFromHsl(t)||[0,0,0,1]}getSource(){return this._source}setSource(t){this._source=t}toRgb(){let[t,e,i]=this.getSource();return"rgb(".concat(t,",").concat(e,",").concat(i,")")}toRgba(){return"rgba(".concat(this.getSource().join(","),")")}toHsl(){let[t,e,i]=iu(...this.getSource());return"hsl(".concat(t,",").concat(e,"%,").concat(i,"%)")}toHsla(){let[t,e,i,r]=iu(...this.getSource());return"hsla(".concat(t,",").concat(e,"%,").concat(i,"%,").concat(r,")")}toHex(){return this.toHexa().slice(0,6)}toHexa(){let[t,e,i,r]=this.getSource();return"".concat(ip(t)).concat(ip(e)).concat(ip(i)).concat(ip(Math.round(255*r)))}getAlpha(){return this.getSource()[3]}setAlpha(t){return this._source[3]=t,this}toGrayscale(){return this.setSource(iv(this.getSource())),this}toBlackWhite(t){let[e,,,i]=iv(this.getSource()),r=e<(t||127)?0:255;return this.setSource([r,r,r,i]),this}overlayWith(t){t instanceof im||(t=new im(t));let e=this.getSource(),i=t.getSource(),[r,s,n]=e.map((t,e)=>Math.round(.5*t+.5*i[e]));return this.setSource([r,s,n,e[3]]),this}static fromRgb(t){return im.fromRgba(t)}static fromRgba(t){return new im(im.sourceFromRgb(t))}static sourceFromRgb(t){let e=t.match(/^rgba?\(\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*(?:\s*[,/]\s*(\d{0,3}(?:\.\d+)?%?)\s*)?\)$/i);if(e){let[t,i,r]=e.slice(1,4).map(t=>{let e=parseFloat(t);return t.endsWith("%")?Math.round(2.55*e):e});return[t,i,r,ig(e[4])]}}static fromHsl(t){return im.fromHsla(t)}static fromHsla(t){return new im(im.sourceFromHsl(t))}static sourceFromHsl(t){let e,i,r;let s=t.match(/^hsla?\(\s*([+-]?\d{1,3})\s*[\s|,]\s*(\d{1,3}%)\s*[\s|,]\s*(\d{1,3}%)\s*(?:\s*[,/]\s*(\d*(?:\.\d+)?%?)\s*)?\)$/i);if(!s)return;let n=(parseFloat(s[1])%360+360)%360/360,o=parseFloat(s[2])/100,a=parseFloat(s[3])/100;if(0===o)e=i=r=a;else{let t=a<=.5?a*(o+1):a+o-a*o,s=2*a-t;e=id(s,t,n+1/3),i=id(s,t,n),r=id(s,t,n-1/3)}return[Math.round(255*e),Math.round(255*i),Math.round(255*r),ig(s[4])]}static fromHex(t){return new im(im.sourceFromHex(t))}static sourceFromHex(t){if(t.match(/^#?(([0-9a-f]){3,4}|([0-9a-f]{2}){3,4})$/i)){let e=t.slice(t.indexOf("#")+1),[i,r,s,n=255]=(e.length<=4?e.split("").map(t=>t+t):e.match(/.{2}/g)).map(t=>parseInt(t,16));return[i,r,s,n/255]}}}let ix=(t,e)=>parseFloat(Number(t).toFixed(e)),iy=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:16,i=/\D{0,2}$/.exec(t),r=parseFloat(t),s=ea.DPI;switch(null==i?void 0:i[0]){case"mm":return r*s/25.4;case"cm":return r*s/2.54;case"in":return r*s;case"pt":return r*s/72;case"pc":return r*s/72*12;case"em":return r*e;default:return r}},i_=t=>{let[e,i]=t.trim().split(" "),[r,s]=e&&e!==eM?[e.slice(1,4),e.slice(5,8)]:e===eM?[e,e]:["Mid","Mid"];return{meetOrSlice:i||"meet",alignX:r,alignY:s}},ib=t=>"matrix("+t.map(t=>ix(t,ea.NUM_FRACTION_DIGITS)).join(" ")+")",iw=function(t,e){let i,r,s=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(e){if(e.toLive)i="url(#SVGID_".concat(e.id,")");else{let t=new im(e),s=t.getAlpha();i=t.toRgb(),1!==s&&(r=s.toString())}}else i="none";return s?"".concat(t,": ").concat(i,"; ").concat(r?"".concat(t,"-opacity: ").concat(r,"; "):""):"".concat(t,'="').concat(i,'" ').concat(r?"".concat(t,'-opacity="').concat(r,'" '):"")},iC=t=>!!t&&void 0!==t.toLive,iS=t=>!!t&&"function"==typeof t.toObject,iT=t=>!!t&&void 0!==t.offsetX&&"source"in t,iO=t=>!!t&&"function"==typeof t._renderText,ik=t=>!!t&&"multiSelectionStacking"in t;function ij(t){let e=t&&iM(t),i=0,r=0;if(!t||!e)return{left:i,top:r};let s=e.documentElement,n=e.body||{scrollLeft:0,scrollTop:0};for(;t&&(t.parentNode||t.host)&&((t=t.parentNode||t.host)===e?(i=n.scrollLeft||s.scrollLeft||0,r=n.scrollTop||s.scrollTop||0):(i+=t.scrollLeft||0,r+=t.scrollTop||0),1!==t.nodeType||"fixed"!==t.style.position););return{left:i,top:r}}let iM=t=>t.ownerDocument||null,iE=t=>{var e;return(null===(e=t.ownerDocument)||void 0===e?void 0:e.defaultView)||null};function iD(t,e){let i=t.style;i&&("string"==typeof e?t.style.cssText+=";"+e:Object.entries(e).forEach(t=>{let[e,r]=t;return i.setProperty(e,r)}))}let iA=function(t,e,i){let{width:r,height:s}=i,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;t.width=r,t.height=s,n>1&&(t.setAttribute("width",(r*n).toString()),t.setAttribute("height",(s*n).toString()),e.scale(n,n))},iL=(t,e)=>{let{width:i,height:r}=e;i&&(t.style.width="number"==typeof i?"".concat(i,"px"):i),r&&(t.style.height="number"==typeof r?"".concat(r,"px"):r)};function iP(t){return void 0!==t.onselectstart&&(t.onselectstart=()=>!1),t.style.userSelect=eM,t}class iF{constructor(t){ei(this,"_originalCanvasStyle",void 0),ei(this,"lower",void 0);let e=this.createLowerCanvas(t);this.lower={el:e,ctx:e.getContext("2d")}}createLowerCanvas(t){let e=t&&void 0!==t.getContext?t:t&&ep().getElementById(t)||eZ();if(e.hasAttribute("data-fabric"))throw new eh("Trying to initialize a canvas that has already been initialized. Did you forget to dispose the canvas?");return this._originalCanvasStyle=e.style.cssText,e.setAttribute("data-fabric","main"),e.classList.add("lower-canvas"),e}cleanupDOM(t){let{width:e,height:i}=t,{el:r}=this.lower;r.classList.remove("lower-canvas"),r.removeAttribute("data-fabric"),r.setAttribute("width","".concat(e)),r.setAttribute("height","".concat(i)),r.style.cssText=this._originalCanvasStyle||"",this._originalCanvasStyle=void 0}setDimensions(t,e){let{el:i,ctx:r}=this.lower;iA(i,r,t,e)}setCSSDimensions(t){iL(this.lower.el,t)}calcOffset(){return function(t){var e;let i={left:0,top:0},r=t&&iM(t),s={left:0,top:0},n={borderLeftWidth:eO,borderTopWidth:"top",paddingLeft:eO,paddingTop:"top"};if(!r)return s;let o=(null===(e=iE(t))||void 0===e?void 0:e.getComputedStyle(t,null))||{};for(let t in n)s[n[t]]+=parseInt(o[t],10)||0;let a=r.documentElement;void 0!==t.getBoundingClientRect&&(i=t.getBoundingClientRect());let l=ij(t);return{left:i.left+l.left-(a.clientLeft||0)+s.left,top:i.top+l.top-(a.clientTop||0)+s.top}}(this.lower.el)}dispose(){ef().dispose(this.lower.el),delete this.lower}}let iR={backgroundVpt:!0,backgroundColor:"",overlayVpt:!0,overlayColor:"",includeDefaultValues:!0,svgViewportTransformation:!0,renderOnAddRemove:!0,skipOffscreen:!0,enableRetinaScaling:!0,imageSmoothingEnabled:!0,controlsAboveOverlay:!1,allowTouchScrolling:!1,viewportTransform:[...eS]};class iI extends ez(eW){get lowerCanvasEl(){var t;return null===(t=this.elements.lower)||void 0===t?void 0:t.el}get contextContainer(){var t;return null===(t=this.elements.lower)||void 0===t?void 0:t.ctx}static getDefaults(){return iI.ownDefaults}constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),Object.assign(this,this.constructor.getDefaults()),this.set(e),this.initElements(t),this._setDimensionsImpl({width:this.width||this.elements.lower.el.width||0,height:this.height||this.elements.lower.el.height||0}),this.skipControlsDrawing=!1,this.viewportTransform=[...this.viewportTransform],this.calcViewportBoundaries()}initElements(t){this.elements=new iF(t)}add(){let t=super.add(...arguments);return arguments.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),t}insertAt(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];let s=super.insertAt(t,...i);return i.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),s}remove(){let t=super.remove(...arguments);return t.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),t}_onObjectAdded(t){t.canvas&&t.canvas!==this&&(el("warn","Canvas is trying to add an object that belongs to a different canvas.\nResulting to default behavior: removing object from previous canvas and adding to new canvas"),t.canvas.remove(t)),t._set("canvas",this),t.setCoords(),this.fire("object:added",{target:t}),t.fire("added",{target:this})}_onObjectRemoved(t){t._set("canvas",void 0),this.fire("object:removed",{target:t}),t.fire("removed",{target:this})}_onStackOrderChanged(){this.renderOnAddRemove&&this.requestRenderAll()}getRetinaScaling(){return this.enableRetinaScaling?em():1}calcOffset(){return this._offset=this.elements.calcOffset()}getWidth(){return this.width}getHeight(){return this.height}setWidth(t,e){return this.setDimensions({width:t},e)}setHeight(t,e){return this.setDimensions({height:t},e)}_setDimensionsImpl(t){let{cssOnly:e=!1,backstoreOnly:i=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e){let e=ee({width:this.width,height:this.height},t);this.elements.setDimensions(e,this.getRetinaScaling()),this.hasLostContext=!0,this.width=e.width,this.height=e.height}i||this.elements.setCSSDimensions(t),this.calcOffset()}setDimensions(t,e){this._setDimensionsImpl(t,e),e&&e.cssOnly||this.requestRenderAll()}getZoom(){return this.viewportTransform[0]}setViewportTransform(t){let e=this.backgroundImage,i=this.overlayImage,r=this._objects.length;this.viewportTransform=t;for(let t=0;t<r;t++){let e=this._objects[t];e.group||e.setCoords()}e&&e.setCoords(),i&&i.setCoords(),this.calcViewportBoundaries(),this.renderOnAddRemove&&this.requestRenderAll()}zoomToPoint(t,e){let i=[...this.viewportTransform],r=e0(t,e1(i));i[0]=e,i[3]=e;let s=e0(r,i);i[4]+=t.x-s.x,i[5]+=t.y-s.y,this.setViewportTransform(i)}setZoom(t){this.zoomToPoint(new eN(0,0),t)}absolutePan(t){let e=[...this.viewportTransform];return e[4]=-t.x,e[5]=-t.y,this.setViewportTransform(e)}relativePan(t){return this.absolutePan(new eN(-t.x-this.viewportTransform[4],-t.y-this.viewportTransform[5]))}getElement(){return this.elements.lower.el}clearContext(t){t.clearRect(0,0,this.width,this.height)}getContext(){return this.elements.lower.ctx}clear(){this.remove(...this.getObjects()),this.backgroundImage=void 0,this.overlayImage=void 0,this.backgroundColor="",this.overlayColor="",this.clearContext(this.getContext()),this.fire("canvas:cleared"),this.renderOnAddRemove&&this.requestRenderAll()}renderAll(){this.cancelRequestedRender(),this.destroyed||this.renderCanvas(this.getContext(),this._objects)}renderAndReset(){this.nextRenderHandle=0,this.renderAll()}requestRenderAll(){this.nextRenderHandle||this.disposed||this.destroyed||(this.nextRenderHandle=eY(()=>this.renderAndReset()))}calcViewportBoundaries(){let t=this.width,e=this.height,i=e1(this.viewportTransform),r=e0({x:0,y:0},i),s=e0({x:t,y:e},i),n=r.min(s),o=r.max(s);return this.vptCoords={tl:n,tr:new eN(o.x,n.y),bl:new eN(n.x,o.y),br:o}}cancelRequestedRender(){this.nextRenderHandle&&(eH(this.nextRenderHandle),this.nextRenderHandle=0)}drawControls(t){}renderCanvas(t,e){if(this.destroyed)return;let i=this.viewportTransform,r=this.clipPath;this.calcViewportBoundaries(),this.clearContext(t),t.imageSmoothingEnabled=this.imageSmoothingEnabled,t.patternQuality="best",this.fire("before:render",{ctx:t}),this._renderBackground(t),t.save(),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),this._renderObjects(t,e),t.restore(),this.controlsAboveOverlay||this.skipControlsDrawing||this.drawControls(t),r&&(r._set("canvas",this),r.shouldCache(),r._transformDone=!0,r.renderCache({forClipping:!0}),this.drawClipPathOnCanvas(t,r)),this._renderOverlay(t),this.controlsAboveOverlay&&!this.skipControlsDrawing&&this.drawControls(t),this.fire("after:render",{ctx:t}),this.__cleanupTask&&(this.__cleanupTask(),this.__cleanupTask=void 0)}drawClipPathOnCanvas(t,e){let i=this.viewportTransform;t.save(),t.transform(...i),t.globalCompositeOperation="destination-in",e.transform(t),t.scale(1/e.zoomX,1/e.zoomY),t.drawImage(e._cacheCanvas,-e.cacheTranslationX,-e.cacheTranslationY),t.restore()}_renderObjects(t,e){for(let i=0,r=e.length;i<r;++i)e[i]&&e[i].render(t)}_renderBackgroundOrOverlay(t,e){let i=this["".concat(e,"Color")],r=this["".concat(e,"Image")],s=this.viewportTransform,n=this["".concat(e,"Vpt")];if(!i&&!r)return;let o=iC(i);if(i){if(t.save(),t.beginPath(),t.moveTo(0,0),t.lineTo(this.width,0),t.lineTo(this.width,this.height),t.lineTo(0,this.height),t.closePath(),t.fillStyle=o?i.toLive(t):i,n&&t.transform(...s),o){t.transform(1,0,0,1,i.offsetX||0,i.offsetY||0);let e=i.gradientTransform||i.patternTransform;e&&t.transform(...e)}t.fill(),t.restore()}if(r){t.save();let{skipOffscreen:e}=this;this.skipOffscreen=n,n&&t.transform(...s),r.render(t),this.skipOffscreen=e,t.restore()}}_renderBackground(t){this._renderBackgroundOrOverlay(t,"background")}_renderOverlay(t){this._renderBackgroundOrOverlay(t,"overlay")}getCenter(){return{top:this.height/2,left:this.width/2}}getCenterPoint(){return new eN(this.width/2,this.height/2)}centerObjectH(t){return this._centerObject(t,new eN(this.getCenterPoint().x,t.getCenterPoint().y))}centerObjectV(t){return this._centerObject(t,new eN(t.getCenterPoint().x,this.getCenterPoint().y))}centerObject(t){return this._centerObject(t,this.getCenterPoint())}viewportCenterObject(t){return this._centerObject(t,this.getVpCenter())}viewportCenterObjectH(t){return this._centerObject(t,new eN(this.getVpCenter().x,t.getCenterPoint().y))}viewportCenterObjectV(t){return this._centerObject(t,new eN(t.getCenterPoint().x,this.getVpCenter().y))}getVpCenter(){return e0(this.getCenterPoint(),e1(this.viewportTransform))}_centerObject(t,e){t.setXY(e,eT,eT),t.setCoords(),this.renderOnAddRemove&&this.requestRenderAll()}toDatalessJSON(t){return this.toDatalessObject(t)}toObject(t){return this._toObjectMethod("toObject",t)}toJSON(){return this.toObject()}toDatalessObject(t){return this._toObjectMethod("toDatalessObject",t)}_toObjectMethod(t,e){let i=this.clipPath,r=i&&!i.excludeFromExport?this._toObject(i,t,e):null;return ee(ee(ee({version:ey},il(this,e)),{},{objects:this._objects.filter(t=>!t.excludeFromExport).map(i=>this._toObject(i,t,e))},this.__serializeBgOverlay(t,e)),r?{clipPath:r}:null)}_toObject(t,e,i){let r;this.includeDefaultValues||(r=t.includeDefaultValues,t.includeDefaultValues=!1);let s=t[e](i);return this.includeDefaultValues||(t.includeDefaultValues=!!r),s}__serializeBgOverlay(t,e){let i={},r=this.backgroundImage,s=this.overlayImage,n=this.backgroundColor,o=this.overlayColor;return iC(n)?n.excludeFromExport||(i.background=n.toObject(e)):n&&(i.background=n),iC(o)?o.excludeFromExport||(i.overlay=o.toObject(e)):o&&(i.overlay=o),r&&!r.excludeFromExport&&(i.backgroundImage=this._toObject(r,t,e)),s&&!s.excludeFromExport&&(i.overlayImage=this._toObject(s,t,e)),i}toSVG(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;t.reviver=e;let i=[];return this._setSVGPreamble(i,t),this._setSVGHeader(i,t),this.clipPath&&i.push('<g clip-path="url(#'.concat(this.clipPath.clipPathId,')" >\n')),this._setSVGBgOverlayColor(i,"background"),this._setSVGBgOverlayImage(i,"backgroundImage",e),this._setSVGObjects(i,e),this.clipPath&&i.push("</g>\n"),this._setSVGBgOverlayColor(i,"overlay"),this._setSVGBgOverlayImage(i,"overlayImage",e),i.push("</svg>"),i.join("")}_setSVGPreamble(t,e){e.suppressPreamble||t.push('<?xml version="1.0" encoding="',e.encoding||"UTF-8",'" standalone="no" ?>\n','<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" ','"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n')}_setSVGHeader(t,e){let i;let r=e.width||"".concat(this.width),s=e.height||"".concat(this.height),n=ea.NUM_FRACTION_DIGITS,o=e.viewBox;if(o)i='viewBox="'.concat(o.x," ").concat(o.y," ").concat(o.width," ").concat(o.height,'" ');else if(this.svgViewportTransformation){let t=this.viewportTransform;i='viewBox="'.concat(ix(-t[4]/t[0],n)," ").concat(ix(-t[5]/t[3],n)," ").concat(ix(this.width/t[0],n)," ").concat(ix(this.height/t[3],n),'" ')}else i='viewBox="0 0 '.concat(this.width," ").concat(this.height,'" ');t.push("<svg ",'xmlns="http://www.w3.org/2000/svg" ','xmlns:xlink="http://www.w3.org/1999/xlink" ','version="1.1" ','width="',r,'" ','height="',s,'" ',i,'xml:space="preserve">\n',"<desc>Created with Fabric.js ",ey,"</desc>\n","<defs>\n",this.createSVGFontFacesMarkup(),this.createSVGRefElementsMarkup(),this.createSVGClipPathMarkup(e),"</defs>\n")}createSVGClipPathMarkup(t){let e=this.clipPath;return e?(e.clipPathId="CLIPPATH_".concat(eU()),'<clipPath id="'.concat(e.clipPathId,'" >\n').concat(e.toClipPathSVG(t.reviver),"</clipPath>\n")):""}createSVGRefElementsMarkup(){return["background","overlay"].map(t=>{let e=this["".concat(t,"Color")];if(iC(e)){let i=this["".concat(t,"Vpt")],r=this.viewportTransform,s={isType:()=>!1,width:this.width/(i?r[0]:1),height:this.height/(i?r[3]:1)};return e.toSVG(s,{additionalTransform:i?ib(r):""})}}).join("")}createSVGFontFacesMarkup(){let t=[],e={},i=ea.fontPaths;this._objects.forEach(function e(i){t.push(i),eX(i)&&i._objects.forEach(e)}),t.forEach(t=>{if(!iO(t))return;let{styles:r,fontFamily:s}=t;!e[s]&&i[s]&&(e[s]=!0,r&&Object.values(r).forEach(t=>{Object.values(t).forEach(t=>{let{fontFamily:r=""}=t;!e[r]&&i[r]&&(e[r]=!0)})}))});let r=Object.keys(e).map(t=>"		@font-face {\n			font-family: '".concat(t,"';\n			src: url('").concat(i[t],"');\n		}\n")).join("");return r?'	<style type="text/css"><![CDATA[\n'.concat(r,"]]></style>\n"):""}_setSVGObjects(t,e){this.forEachObject(i=>{i.excludeFromExport||this._setSVGObject(t,i,e)})}_setSVGObject(t,e,i){t.push(e.toSVG(i))}_setSVGBgOverlayImage(t,e,i){let r=this[e];r&&!r.excludeFromExport&&r.toSVG&&t.push(r.toSVG(i))}_setSVGBgOverlayColor(t,e){let i=this["".concat(e,"Color")];if(i){if(iC(i)){let r=i.repeat||"",s=this.width,n=this.height,o=this["".concat(e,"Vpt")]?ib(e1(this.viewportTransform)):"";t.push('<rect transform="'.concat(o," translate(").concat(s/2,",").concat(n/2,')" x="').concat(i.offsetX-s/2,'" y="').concat(i.offsetY-n/2,'" width="').concat(("repeat-y"===r||"no-repeat"===r)&&iT(i)?i.source.width:s,'" height="').concat(("repeat-x"===r||"no-repeat"===r)&&iT(i)?i.source.height:n,'" fill="url(#SVGID_').concat(i.id,')"></rect>\n'))}else t.push('<rect x="0" y="0" width="100%" height="100%" ','fill="',i,'"',"></rect>\n")}}loadFromJSON(t,e){let{signal:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!t)return Promise.reject(new eh("`json` is undefined"));let r="string"==typeof t?JSON.parse(t):t,{objects:s=[],backgroundImage:n,background:o,overlayImage:a,overlay:l,clipPath:h}=r,c=this.renderOnAddRemove;return this.renderOnAddRemove=!1,Promise.all([io(s,{reviver:e,signal:i}),ia({backgroundImage:n,backgroundColor:o,overlayImage:a,overlayColor:l,clipPath:h},{signal:i})]).then(t=>{let[e,i]=t;return this.clear(),this.add(...e),this.set(r),this.set(i),this.renderOnAddRemove=c,this})}clone(t){let e=this.toObject(t);return this.cloneWithoutData().loadFromJSON(e)}cloneWithoutData(){let t=eZ();return t.width=this.width,t.height=this.height,new this.constructor(t)}toDataURL(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{format:e="png",quality:i=1,multiplier:r=1,enableRetinaScaling:s=!1}=t,n=r*(s?this.getRetinaScaling():1);return eK(this.toCanvasElement(n,t),e,i)}toCanvasElement(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,{width:e,height:i,left:r,top:s,filter:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=(e||this.width)*t,a=(i||this.height)*t,l=this.getZoom(),h=this.width,c=this.height,d=this.skipControlsDrawing,u=l*t,g=this.viewportTransform,f=[u,0,0,u,(g[4]-(r||0))*t,(g[5]-(s||0))*t],p=this.enableRetinaScaling,v=eZ(),m=n?this._objects.filter(t=>n(t)):this._objects;return v.width=o,v.height=a,this.enableRetinaScaling=!1,this.viewportTransform=f,this.width=o,this.height=a,this.skipControlsDrawing=!0,this.calcViewportBoundaries(),this.renderCanvas(v.getContext("2d"),m),this.viewportTransform=g,this.width=h,this.height=c,this.calcViewportBoundaries(),this.enableRetinaScaling=p,this.skipControlsDrawing=d,v}dispose(){return this.disposed||this.elements.cleanupDOM({width:this.width,height:this.height}),eL.cancelByCanvas(this),this.disposed=!0,new Promise((t,e)=>{let i=()=>{this.destroy(),t(!0)};i.kill=e,this.__cleanupTask&&this.__cleanupTask.kill("aborted"),this.destroyed?t(!1):this.nextRenderHandle?this.__cleanupTask=i:i()})}destroy(){this.destroyed=!0,this.cancelRequestedRender(),this.forEachObject(t=>t.dispose()),this._objects=[],this.backgroundImage&&this.backgroundImage.dispose(),this.backgroundImage=void 0,this.overlayImage&&this.overlayImage.dispose(),this.overlayImage=void 0,this.elements.dispose()}toString(){return"#<Canvas (".concat(this.complexity(),"): { objects: ").concat(this._objects.length," }>")}}ei(iI,"ownDefaults",iR);let iB=["touchstart","touchmove","touchend"],iN=t=>{let e=ij(t.target),i=function(t){let e=t.changedTouches;return e&&e[0]?e[0]:t}(t);return new eN(i.clientX+e.left,i.clientY+e.top)},iV=t=>iB.includes(t.type)||"touch"===t.pointerType,iX=t=>{t.preventDefault(),t.stopPropagation()},iz=t=>{if(0===t.length)return{left:0,top:0,width:0,height:0};let{min:e,max:i}=t.reduce((t,e)=>{let{min:i,max:r}=t;return{min:i.min(e),max:r.max(e)}},{min:new eN(t[0]),max:new eN(t[0])}),r=i.subtract(e);return{left:e.x,top:e.y,width:r.x,height:r.y}},iW=["translateX","translateY","scaleX","scaleY"],iY=(t,e)=>iH(t,e2(e,t.calcOwnMatrix())),iH=(t,e)=>{let i=e5(e),{translateX:r,translateY:s,scaleX:n,scaleY:o}=i,a=er(i,iW),l=new eN(r,s);t.flipX=!1,t.flipY=!1,Object.assign(t,a),t.set({scaleX:n,scaleY:o}),t.setPositionByOrigin(l,eT,eT)},iG=t=>{t.scaleX=1,t.scaleY=1,t.skewX=0,t.skewY=0,t.flipX=!1,t.flipY=!1,t.rotate(0)},iU=t=>({scaleX:t.scaleX,scaleY:t.scaleY,skewX:t.skewX,skewY:t.skewY,angle:t.angle,left:t.left,flipX:t.flipX,flipY:t.flipY,top:t.top}),iZ=(t,e,i)=>{let r=t/2,s=e/2,n=iz([new eN(-r,-s),new eN(r,-s),new eN(-r,s),new eN(r,s)].map(t=>t.transform(i)));return new eN(n.width,n.height)},iq=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:eS;return e2(e1(arguments.length>1&&void 0!==arguments[1]?arguments[1]:eS),t)},iK=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eS,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:eS;return t.transform(iq(e,i))},iJ=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eS,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:eS;return t.transform(iq(e,i),!0)},iQ=(t,e,i)=>{let r=iq(e,i);return iH(t,e2(r,t.calcOwnMatrix())),r},i$=(t,e)=>{var i;let{transform:{target:r}}=e;null===(i=r.canvas)||void 0===i||i.fire("object:".concat(t),ee(ee({},e),{},{target:r})),r.fire(t,e)},i0={left:-.5,top:-.5,center:0,bottom:.5,right:.5},i1=t=>"string"==typeof t?i0[t]:t-.5,i2="not-allowed";function i3(t){return t.originX===eT&&t.originY===eT}function i6(t){return .5-i1(t)}let i5=(t,e)=>t[e],i8=(t,e,i,r)=>({e:t,transform:e,pointer:new eN(i,r)});function i4(t,e){return Math.round((t.getTotalAngle()+eQ(Math.atan2(e.y,e.x))+360)%360/45)}function i9(t,e,i,r,s){var n;let{target:o,corner:a}=t,l=o.controls[a],h=(null===(n=o.canvas)||void 0===n?void 0:n.getZoom())||1,c=o.padding/h,d=function(t,e,i,r){let s=t.getRelativeCenterPoint(),n=void 0!==i&&void 0!==r?t.translateToGivenOrigin(s,eT,eT,i,r):new eN(t.left,t.top);return(t.angle?e.rotate(-eJ(t.angle),s):e).subtract(n)}(o,new eN(r,s),e,i);return d.x>=c&&(d.x-=c),d.x<=-c&&(d.x+=c),d.y>=c&&(d.y-=c),d.y<=c&&(d.y+=c),d.x-=l.offsetX,d.y-=l.offsetY,d}let i7=(t,e,i,r)=>{let{target:s,offsetX:n,offsetY:o}=e,a=i-n,l=r-o,h=!i5(s,"lockMovementX")&&s.left!==a,c=!i5(s,"lockMovementY")&&s.top!==l;return h&&s.set(eO,a),c&&s.set("top",l),(h||c)&&i$("moving",i8(t,e,i,r)),h||c};class rt{getSvgStyles(t){let e=this.fillRule?this.fillRule:"nonzero",i=this.strokeWidth?this.strokeWidth:"0",r=this.strokeDashArray?this.strokeDashArray.join(" "):eM,s=this.strokeDashOffset?this.strokeDashOffset:"0",n=this.strokeLineCap?this.strokeLineCap:"butt",o=this.strokeLineJoin?this.strokeLineJoin:"miter",a=this.strokeMiterLimit?this.strokeMiterLimit:"4",l=void 0!==this.opacity?this.opacity:"1",h=this.visible?"":" visibility: hidden;",c=t?"":this.getSvgFilter(),d=iw("fill",this.fill);return[iw("stroke",this.stroke),"stroke-width: ",i,"; ","stroke-dasharray: ",r,"; ","stroke-linecap: ",n,"; ","stroke-dashoffset: ",s,"; ","stroke-linejoin: ",o,"; ","stroke-miterlimit: ",a,"; ",d,"fill-rule: ",e,"; ","opacity: ",l,";",c,h].join("")}getSvgFilter(){return this.shadow?"filter: url(#SVGID_".concat(this.shadow.id,");"):""}getSvgCommons(){return[this.id?'id="'.concat(this.id,'" '):"",this.clipPath?'clip-path="url(#'.concat(this.clipPath.clipPathId,')" '):""].join("")}getSvgTransform(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=t?this.calcTransformMatrix():this.calcOwnMatrix(),r='transform="'.concat(ib(i));return"".concat(r).concat(e,'" ')}_toSVG(t){return[""]}toSVG(t){return this._createBaseSVGMarkup(this._toSVG(t),{reviver:t})}toClipPathSVG(t){return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(t),{reviver:t})}_createBaseClipPathSVGMarkup(t){let{reviver:e,additionalTransform:i=""}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=[this.getSvgTransform(!0,i),this.getSvgCommons()].join(""),s=t.indexOf("COMMON_PARTS");return t[s]=r,e?e(t.join("")):t.join("")}_createBaseSVGMarkup(t){let e,{noStyle:i,reviver:r,withShadow:s,additionalTransform:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=i?"":'style="'.concat(this.getSvgStyles(),'" '),a=s?'style="'.concat(this.getSvgFilter(),'" '):"",l=this.clipPath,h=this.strokeUniform?'vector-effect="non-scaling-stroke" ':"",c=l&&l.absolutePositioned,d=this.stroke,u=this.fill,g=this.shadow,f=[],p=t.indexOf("COMMON_PARTS");l&&(l.clipPathId="CLIPPATH_".concat(eU()),e='<clipPath id="'.concat(l.clipPathId,'" >\n').concat(l.toClipPathSVG(r),"</clipPath>\n")),c&&f.push("<g ",a,this.getSvgCommons()," >\n"),f.push("<g ",this.getSvgTransform(!1),c?"":a+this.getSvgCommons()," >\n");let v=[o,h,i?"":this.addPaintOrder()," ",n?'transform="'.concat(n,'" '):""].join("");return t[p]=v,iC(u)&&f.push(u.toSVG(this)),iC(d)&&f.push(d.toSVG(this)),g&&f.push(g.toSVG(this)),l&&f.push(e),f.push(t.join("")),f.push("</g>\n"),c&&f.push("</g>\n"),r?r(f.join("")):f.join("")}addPaintOrder(){return"fill"!==this.paintFirst?' paint-order="'.concat(this.paintFirst,'" '):""}}let re=(t,e,i,r)=>(t<Math.abs(e)?(t=e,r=i/4):r=0===e&&0===t?i/ew*Math.asin(1):i/ew*Math.asin(e/t),{a:t,c:e,p:i,s:r}),ri=(t,e,i,r,s)=>t*Math.pow(2,10*(r-=1))*Math.sin((r*s-e)*ew/i),rr=(t,e,i,r)=>-i*Math.cos(t/r*eb)+i+e,rs=(t,e,i,r)=>(t/=r)<1/2.75?7.5625*t*t*i+e:t<2/2.75?i*(7.5625*(t-=1.5/2.75)*t+.75)+e:t<2.5/2.75?i*(7.5625*(t-=2.25/2.75)*t+.9375)+e:i*(7.5625*(t-=2.625/2.75)*t+.984375)+e,rn=(t,e,i,r)=>i-rs(r-t,0,i,r)+e;var ro=Object.freeze({__proto__:null,defaultEasing:rr,easeInBack:function(t,e,i,r){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1.70158;return i*(t/=r)*t*((s+1)*t-s)+e},easeInBounce:rn,easeInCirc:(t,e,i,r)=>-i*(Math.sqrt(1-(t/=r)*t)-1)+e,easeInCubic:(t,e,i,r)=>i*(t/r)**3+e,easeInElastic:(t,e,i,r)=>{let s=0;if(0===t)return e;if(1==(t/=r))return e+i;s||(s=.3*r);let{a:n,s:o,p:a}=re(i,i,s,1.70158);return-ri(n,o,a,t,r)+e},easeInExpo:(t,e,i,r)=>0===t?e:i*2**(10*(t/r-1))+e,easeInOutBack:function(t,e,i,r){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1.70158;return(t/=r/2)<1?i/2*(t*t*((1+(s*=1.525))*t-s))+e:i/2*((t-=2)*t*((1+(s*=1.525))*t+s)+2)+e},easeInOutBounce:(t,e,i,r)=>t<r/2?.5*rn(2*t,0,i,r)+e:.5*rs(2*t-r,0,i,r)+.5*i+e,easeInOutCirc:(t,e,i,r)=>(t/=r/2)<1?-i/2*(Math.sqrt(1-t**2)-1)+e:i/2*(Math.sqrt(1-(t-=2)*t)+1)+e,easeInOutCubic:(t,e,i,r)=>(t/=r/2)<1?i/2*t**3+e:i/2*((t-2)**3+2)+e,easeInOutElastic:(t,e,i,r)=>{let s=0;if(0===t)return e;if(2==(t/=r/2))return e+i;s||(s=.3*1.5*r);let{a:n,s:o,p:a,c:l}=re(i,i,s,1.70158);return t<1?-.5*ri(n,o,a,t,r)+e:n*Math.pow(2,-10*(t-=1))*Math.sin((t*r-o)*ew/a)*.5+l+e},easeInOutExpo:(t,e,i,r)=>0===t?e:t===r?e+i:(t/=r/2)<1?i/2*2**(10*(t-1))+e:-(i/2*(2**(-10*--t)+2))+e,easeInOutQuad:(t,e,i,r)=>(t/=r/2)<1?i/2*t**2+e:-i/2*(--t*(t-2)-1)+e,easeInOutQuart:(t,e,i,r)=>(t/=r/2)<1?i/2*t**4+e:-i/2*((t-=2)*t**3-2)+e,easeInOutQuint:(t,e,i,r)=>(t/=r/2)<1?i/2*t**5+e:i/2*((t-2)**5+2)+e,easeInOutSine:(t,e,i,r)=>-i/2*(Math.cos(Math.PI*t/r)-1)+e,easeInQuad:(t,e,i,r)=>i*(t/=r)*t+e,easeInQuart:(t,e,i,r)=>i*(t/=r)*t**3+e,easeInQuint:(t,e,i,r)=>i*(t/r)**5+e,easeInSine:(t,e,i,r)=>-i*Math.cos(t/r*eb)+i+e,easeOutBack:function(t,e,i,r){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1.70158;return i*((t=t/r-1)*t*((s+1)*t+s)+1)+e},easeOutBounce:rs,easeOutCirc:(t,e,i,r)=>i*Math.sqrt(1-(t=t/r-1)*t)+e,easeOutCubic:(t,e,i,r)=>i*((t/r-1)**3+1)+e,easeOutElastic:(t,e,i,r)=>{let s=0;if(0===t)return e;if(1==(t/=r))return e+i;s||(s=.3*r);let{a:n,s:o,p:a,c:l}=re(i,i,s,1.70158);return n*2**(-10*t)*Math.sin((t*r-o)*ew/a)+l+e},easeOutExpo:(t,e,i,r)=>t===r?e+i:-(i*(2**(-10*t/r)+1))+e,easeOutQuad:(t,e,i,r)=>-i*(t/=r)*(t-2)+e,easeOutQuart:(t,e,i,r)=>-i*((t=t/r-1)*t**3-1)+e,easeOutQuint:(t,e,i,r)=>i*((t/r-1)**5+1)+e,easeOutSine:(t,e,i,r)=>i*Math.sin(t/r*eb)+e});let ra=()=>!1;class rl{constructor(t){let{startValue:e,byValue:i,duration:r=500,delay:s=0,easing:n=rr,onStart:o=e_,onChange:a=e_,onComplete:l=e_,abort:h=ra,target:c}=t;ei(this,"_state","pending"),ei(this,"durationProgress",0),ei(this,"valueProgress",0),this.tick=this.tick.bind(this),this.duration=r,this.delay=s,this.easing=n,this._onStart=o,this._onChange=a,this._onComplete=l,this._abort=h,this.target=c,this.startValue=e,this.byValue=i,this.value=this.startValue,this.endValue=Object.freeze(this.calculate(this.duration).value)}get state(){return this._state}isDone(){return"aborted"===this._state||"completed"===this._state}start(){let t=t=>{"pending"===this._state&&(this.startTime=t||+new Date,this._state="running",this._onStart(),this.tick(this.startTime))};this.register(),this.delay>0?setTimeout(()=>eY(t),this.delay):eY(t)}tick(t){let e=(t||+new Date)-this.startTime,i=Math.min(e,this.duration);this.durationProgress=i/this.duration;let{value:r,valueProgress:s}=this.calculate(i);this.value=Object.freeze(r),this.valueProgress=s,"aborted"!==this._state&&(this._abort(this.value,this.valueProgress,this.durationProgress)?(this._state="aborted",this.unregister()):e>=this.duration?(this.durationProgress=this.valueProgress=1,this._onChange(this.endValue,this.valueProgress,this.durationProgress),this._state="completed",this._onComplete(this.endValue,this.valueProgress,this.durationProgress),this.unregister()):(this._onChange(this.value,this.valueProgress,this.durationProgress),eY(this.tick)))}register(){eL.push(this)}unregister(){eL.remove(this)}abort(){this._state="aborted",this.unregister()}}let rh=["startValue","endValue"];class rc extends rl{constructor(t){let{startValue:e=0,endValue:i=100}=t;super(ee(ee({},er(t,rh)),{},{startValue:e,byValue:i-e}))}calculate(t){let e=this.easing(t,this.startValue,this.byValue,this.duration);return{value:e,valueProgress:Math.abs((e-this.startValue)/this.byValue)}}}let rd=["startValue","endValue"];class ru extends rl{constructor(t){let{startValue:e=[0],endValue:i=[100]}=t;super(ee(ee({},er(t,rd)),{},{startValue:e,byValue:i.map((t,i)=>t-e[i])}))}calculate(t){let e=this.startValue.map((e,i)=>this.easing(t,e,this.byValue[i],this.duration,i));return{value:e,valueProgress:Math.abs((e[0]-this.startValue[0])/this.byValue[0])}}}let rg=(t,e,i)=>Math.max(t,Math.min(e,i)),rf=["startValue","endValue","easing","onChange","onComplete","abort"],rp=(t,e,i,r)=>e+i*(1-Math.cos(t/r*eb)),rv=t=>t&&((e,i,r)=>t(new im(e).toRgba(),i,r));class rm extends rl{constructor(t){let{startValue:e,endValue:i,easing:r=rp,onChange:s,onComplete:n,abort:o}=t,a=er(t,rf),l=new im(e).getSource();super(ee(ee({},a),{},{startValue:l,byValue:new im(i).getSource().map((t,e)=>t-l[e]),easing:r,onChange:rv(s),onComplete:rv(n),abort:rv(o)}))}calculate(t){let[e,i,r,s]=this.startValue.map((e,i)=>this.easing(t,e,this.byValue[i],this.duration,i)),n=[...[e,i,r].map(Math.round),rg(0,s,1)];return{value:n,valueProgress:n.map((t,e)=>0!==this.byValue[e]?Math.abs((t-this.startValue[e])/this.byValue[e]):0).find(t=>0!==t)||0}}}function rx(t){let e;let i=Array.isArray((e=t).startValue)||Array.isArray(e.endValue)?new ru(t):new rc(t);return i.start(),i}function ry(t){let e=new rm(t);return e.start(),e}let r_=new eN(1,0),rb=new eN,rw=(t,e)=>t.rotate(e),rC=(t,e)=>new eN(e).subtract(t),rS=t=>t.distanceFrom(rb),rT=(t,e)=>Math.atan2(rM(t,e),rE(t,e)),rO=t=>rT(r_,t),rk=t=>t.eq(rb)?t:t.scalarDivide(rS(t)),rj=function(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return rk(new eN(-t.y,t.x).scalarMultiply(e?1:-1))},rM=(t,e)=>t.x*e.y-t.y*e.x,rE=(t,e)=>t.x*e.x+t.y*e.y,rD=(t,e,i)=>{if(t.eq(e)||t.eq(i))return!0;let r=rM(e,i),s=rM(e,t),n=rM(i,t);return r>=0?s>=0&&n<=0:!(s<=0&&n>=0)};class rA{constructor(t){this.status=t,this.points=[]}includes(t){return this.points.some(e=>e.eq(t))}append(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return this.points=this.points.concat(e.filter(t=>!this.includes(t))),this}static isPointContained(t,e,i){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(e.eq(i))return t.eq(e);if(e.x===i.x)return t.x===e.x&&(r||t.y>=Math.min(e.y,i.y)&&t.y<=Math.max(e.y,i.y));if(e.y===i.y)return t.y===e.y&&(r||t.x>=Math.min(e.x,i.x)&&t.x<=Math.max(e.x,i.x));{let s=rC(e,i),n=rC(e,t).divide(s);return r?Math.abs(n.x)===Math.abs(n.y):n.x===n.y&&n.x>=0&&n.x<=1}}static isPointInPolygon(t,e){let i=new eN(t).setX(Math.min(t.x-1,...e.map(t=>t.x))),r=0;for(let s=0;s<e.length;s++){let n=this.intersectSegmentSegment(e[s],e[(s+1)%e.length],t,i);if(n.includes(t))return!0;r+=Number("Intersection"===n.status)}return r%2==1}static intersectLineLine(t,e,i,r){let s=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],n=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],o=e.x-t.x,a=e.y-t.y,l=r.x-i.x,h=r.y-i.y,c=t.x-i.x,d=t.y-i.y,u=l*d-h*c,g=o*d-a*c,f=h*o-l*a;if(0!==f){let e=u/f,i=g/f;return(s||0<=e&&e<=1)&&(n||0<=i&&i<=1)?new rA("Intersection").append(new eN(t.x+e*o,t.y+e*a)):new rA}if(0===u||0===g){let o=s||n||rA.isPointContained(t,i,r)||rA.isPointContained(e,i,r)||rA.isPointContained(i,t,e)||rA.isPointContained(r,t,e);return new rA(o?"Coincident":void 0)}return new rA("Parallel")}static intersectSegmentLine(t,e,i,r){return rA.intersectLineLine(t,e,i,r,!1,!0)}static intersectSegmentSegment(t,e,i,r){return rA.intersectLineLine(t,e,i,r,!1,!1)}static intersectLinePolygon(t,e,i){let r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],s=new rA,n=i.length;for(let o,a,l,h=0;h<n;h++){if(o=i[h],a=i[(h+1)%n],"Coincident"===(l=rA.intersectLineLine(t,e,o,a,r,!1)).status)return l;s.append(...l.points)}return s.points.length>0&&(s.status="Intersection"),s}static intersectSegmentPolygon(t,e,i){return rA.intersectLinePolygon(t,e,i,!1)}static intersectPolygonPolygon(t,e){let i=new rA,r=t.length,s=[];for(let n=0;n<r;n++){let o=t[n],a=t[(n+1)%r],l=rA.intersectSegmentPolygon(o,a,e);"Coincident"===l.status?(s.push(l),i.append(o,a)):i.append(...l.points)}return s.length>0&&s.length===t.length?new rA("Coincident"):(i.points.length>0&&(i.status="Intersection"),i)}static intersectPolygonRectangle(t,e,i){let r=e.min(i),s=e.max(i),n=new eN(s.x,r.y),o=new eN(r.x,s.y);return rA.intersectPolygonPolygon(t,[r,n,s,o])}}class rL extends eW{_getTransformedDimensions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=ee({scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,width:this.width,height:this.height,strokeWidth:this.strokeWidth},t),i=e.strokeWidth,r=i,s=0;this.strokeUniform&&(r=0,s=i);let n=e.width+r,o=e.height+r;return(0===e.skewX&&0===e.skewY?new eN(n*e.scaleX,o*e.scaleY):iZ(n,o,ii(e))).scalarAdd(s)}translateToGivenOrigin(t,e,i,r,s){let n=t.x,o=t.y,a=i1(r)-i1(e),l=i1(s)-i1(i);if(a||l){let t=this._getTransformedDimensions();n+=a*t.x,o+=l*t.y}return new eN(n,o)}translateToCenterPoint(t,e,i){let r=this.translateToGivenOrigin(t,e,i,eT,eT);return this.angle?r.rotate(eJ(this.angle),t):r}translateToOriginPoint(t,e,i){let r=this.translateToGivenOrigin(t,eT,eT,e,i);return this.angle?r.rotate(eJ(this.angle),t):r}getCenterPoint(){let t=this.getRelativeCenterPoint();return this.group?e0(t,this.group.calcTransformMatrix()):t}getRelativeCenterPoint(){return this.translateToCenterPoint(new eN(this.left,this.top),this.originX,this.originY)}getPointByOrigin(t,e){return this.translateToOriginPoint(this.getRelativeCenterPoint(),t,e)}setPositionByOrigin(t,e,i){let r=this.translateToCenterPoint(t,e,i),s=this.translateToOriginPoint(r,this.originX,this.originY);this.set({left:s.x,top:s.y})}_getLeftTopCoords(){return this.translateToOriginPoint(this.getRelativeCenterPoint(),eO,"top")}}class rP extends rL{getX(){return this.getXY().x}setX(t){this.setXY(this.getXY().setX(t))}getY(){return this.getXY().y}setY(t){this.setXY(this.getXY().setY(t))}getRelativeX(){return this.left}setRelativeX(t){this.left=t}getRelativeY(){return this.top}setRelativeY(t){this.top=t}getXY(){let t=this.getRelativeXY();return this.group?e0(t,this.group.calcTransformMatrix()):t}setXY(t,e,i){this.group&&(t=e0(t,e1(this.group.calcTransformMatrix()))),this.setRelativeXY(t,e,i)}getRelativeXY(){return new eN(this.left,this.top)}setRelativeXY(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.originX,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.originY;this.setPositionByOrigin(t,e,i)}isStrokeAccountedForInDimensions(){return!1}getCoords(){let{tl:t,tr:e,br:i,bl:r}=this.aCoords||(this.aCoords=this.calcACoords()),s=[t,e,i,r];if(this.group){let t=this.group.calcTransformMatrix();return s.map(e=>e0(e,t))}return s}intersectsWithRect(t,e){return"Intersection"===rA.intersectPolygonRectangle(this.getCoords(),t,e).status}intersectsWithObject(t){let e=rA.intersectPolygonPolygon(this.getCoords(),t.getCoords());return"Intersection"===e.status||"Coincident"===e.status||t.isContainedWithinObject(this)||this.isContainedWithinObject(t)}isContainedWithinObject(t){return this.getCoords().every(e=>t.containsPoint(e))}isContainedWithinRect(t,e){let{left:i,top:r,width:s,height:n}=this.getBoundingRect();return i>=t.x&&i+s<=e.x&&r>=t.y&&r+n<=e.y}isOverlapping(t){return this.intersectsWithObject(t)||this.isContainedWithinObject(t)||t.isContainedWithinObject(this)}containsPoint(t){return rA.isPointInPolygon(t,this.getCoords())}isOnScreen(){if(!this.canvas)return!1;let{tl:t,br:e}=this.canvas.vptCoords;return!!this.getCoords().some(i=>i.x<=e.x&&i.x>=t.x&&i.y<=e.y&&i.y>=t.y)||!!this.intersectsWithRect(t,e)||this.containsPoint(t.midPointFrom(e))}isPartiallyOnScreen(){if(!this.canvas)return!1;let{tl:t,br:e}=this.canvas.vptCoords;return!!this.intersectsWithRect(t,e)||this.getCoords().every(i=>(i.x>=e.x||i.x<=t.x)&&(i.y>=e.y||i.y<=t.y))&&this.containsPoint(t.midPointFrom(e))}getBoundingRect(){return iz(this.getCoords())}getScaledWidth(){return this._getTransformedDimensions().x}getScaledHeight(){return this._getTransformedDimensions().y}scale(t){this._set("scaleX",t),this._set("scaleY",t),this.setCoords()}scaleToWidth(t){let e=this.getBoundingRect().width/this.getScaledWidth();return this.scale(t/this.width/e)}scaleToHeight(t){let e=this.getBoundingRect().height/this.getScaledHeight();return this.scale(t/this.height/e)}getCanvasRetinaScaling(){var t;return(null===(t=this.canvas)||void 0===t?void 0:t.getRetinaScaling())||1}getTotalAngle(){return this.group?eQ(e6(this.calcTransformMatrix())):this.angle}getViewportTransform(){var t;return(null===(t=this.canvas)||void 0===t?void 0:t.viewportTransform)||eS.concat()}calcACoords(){let t=e4({angle:this.angle}),{x:e,y:i}=this.getRelativeCenterPoint(),r=e2(e8(e,i),t),s=this._getTransformedDimensions(),n=s.x/2,o=s.y/2;return{tl:e0({x:-n,y:-o},r),tr:e0({x:n,y:-o},r),bl:e0({x:-n,y:o},r),br:e0({x:n,y:o},r)}}setCoords(){this.aCoords=this.calcACoords()}transformMatrixKey(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=[];return!t&&this.group&&(e=this.group.transformMatrixKey(t)),e.push(this.top,this.left,this.width,this.height,this.scaleX,this.scaleY,this.angle,this.strokeWidth,this.skewX,this.skewY,+this.flipX,+this.flipY,i1(this.originX),i1(this.originY)),e}calcTransformMatrix(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.calcOwnMatrix();if(t||!this.group)return e;let i=this.transformMatrixKey(t),r=this.matrixCache;return r&&r.key.every((t,e)=>t===i[e])?r.value:(this.group&&(e=e2(this.group.calcTransformMatrix(!1),e)),this.matrixCache={key:i,value:e},e)}calcOwnMatrix(){let t=this.transformMatrixKey(!0),e=this.ownMatrixCache;if(e&&e.key===t)return e.value;let i=this.getRelativeCenterPoint(),r=ir({angle:this.angle,translateX:i.x,translateY:i.y,scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,flipX:this.flipX,flipY:this.flipY});return this.ownMatrixCache={key:t,value:r},r}_getNonTransformedDimensions(){return new eN(this.width,this.height).scalarAdd(this.strokeWidth)}_calculateCurrentDimensions(t){return this._getTransformedDimensions(t).transform(this.getViewportTransform(),!0).scalarAdd(2*this.padding)}}class rF extends rP{isDescendantOf(t){let{parent:e,group:i}=this;return e===t||i===t||this.canvas===t||!!e&&e.isDescendantOf(t)||!!i&&i!==e&&i.isDescendantOf(t)}getAncestors(t){let e=[],i=this;do{var r;(i=i instanceof rF?null!==(r=i.parent)&&void 0!==r?r:t?void 0:i.canvas:void 0)&&e.push(i)}while(i);return e}findCommonAncestors(t,e){if(this===t)return{fork:[],otherFork:[],common:[this,...this.getAncestors(e)]};let i=this.getAncestors(e),r=t.getAncestors(e);if(0===i.length&&r.length>0&&this===r[r.length-1])return{fork:[],otherFork:[t,...r.slice(0,r.length-1)],common:[this]};for(let e,s=0;s<i.length;s++){if((e=i[s])===t)return{fork:[this,...i.slice(0,s)],otherFork:[],common:i.slice(s)};for(let n=0;n<r.length;n++){if(this===r[n])return{fork:[],otherFork:[t,...r.slice(0,n)],common:[this,...i]};if(e===r[n])return{fork:[this,...i.slice(0,s)],otherFork:[t,...r.slice(0,n)],common:i.slice(s)}}}return{fork:[this,...i],otherFork:[t,...r],common:[]}}hasCommonAncestors(t,e){let i=this.findCommonAncestors(t,e);return i&&!!i.common.length}isInFrontOf(t){if(this===t)return;let e=this.findCommonAncestors(t);if(!e)return;if(e.fork.includes(t))return!0;if(e.otherFork.includes(this))return!1;let i=e.common[0];if(!i)return;let r=e.fork.pop(),s=e.otherFork.pop(),n=i._objects.indexOf(r),o=i._objects.indexOf(s);return n>-1&&n>o}}class rR extends rF{animate(t,e){return Object.entries(t).reduce((t,i)=>{let[r,s]=i;return t[r]=this._animate(r,s,e),t},{})}_animate(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t.split("."),s=this.constructor.colorProperties.includes(r[r.length-1]),{abort:n,startValue:o,onChange:a,onComplete:l}=i,h=ee(ee({},i),{},{target:this,startValue:null!=o?o:r.reduce((t,e)=>t[e],this),endValue:e,abort:null==n?void 0:n.bind(this),onChange:(t,e,i)=>{r.reduce((e,i,s)=>(s===r.length-1&&(e[i]=t),e[i]),this),a&&a(t,e,i)},onComplete:(t,e,i)=>{this.setCoords(),l&&l(t,e,i)}});return s?ry(h):rx(h)}}function rI(t){return RegExp("^("+t.join("|")+")\\b","i")}ei(rR,"colorProperties",["fill","stroke","backgroundColor"]);let rB=String.raw(y||(y=es(["(?:[-+]?(?:d*.d+|d+.?)(?:[eE][-+]?d+)?)"],["(?:[-+]?(?:\\d*\\.\\d+|\\d+\\.?)(?:[eE][-+]?\\d+)?)"])));String.raw(_||(_=es(["(?:s+,?s*|,s*|$)"],["(?:\\s+,?\\s*|,\\s*|$)"])));let rN=RegExp("(normal|italic)?\\s*(normal|small-caps)?\\s*(normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900)?\\s*("+rB+"(?:px|cm|mm|em|pt|pc|in)*)(?:\\/(normal|"+rB+"))?\\s+(.*)"),rV={cx:eO,x:eO,r:"radius",cy:"top",y:"top",display:"visible",visibility:"visible",transform:"transformMatrix","fill-opacity":"fillOpacity","fill-rule":"fillRule","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","letter-spacing":"charSpacing","paint-order":"paintFirst","stroke-dasharray":"strokeDashArray","stroke-dashoffset":"strokeDashOffset","stroke-linecap":"strokeLineCap","stroke-linejoin":"strokeLineJoin","stroke-miterlimit":"strokeMiterLimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth","text-decoration":"textDecoration","text-anchor":"textAnchor",opacity:"opacity","clip-path":"clipPath","clip-rule":"clipRule","vector-effect":"strokeUniform","image-rendering":"imageSmoothing"},rX="font-size",rz="clip-path",rW=(rI(["path","circle","polygon","polyline","ellipse","rect","line","image","text"]),rI(["symbol","image","marker","pattern","view","svg"]),rI(["symbol","g","a","svg","clipPath","defs"])),rY=(RegExp("^\\s*("+rB+"+)\\s*,?\\s*("+rB+"+)\\s*,?\\s*("+rB+"+)\\s*,?\\s*("+rB+"+)\\s*$"),"(-?\\d+(?:\\.\\d*)?(?:px)?(?:\\s?|$))?"),rH=RegExp("(?:\\s|^)"+rY+rY+"("+rB+"?(?:px)?)?(?:\\s?|$)(?:$|\\s)");class rG{constructor(t){let e="string"==typeof t?rG.parseShadow(t):t;for(let t in Object.assign(this,rG.ownDefaults),e)this[t]=e[t];this.id=eU()}static parseShadow(t){let e=t.trim(),[,i=0,r=0,s=0]=(rH.exec(e)||[]).map(t=>parseFloat(t)||0);return{color:(e.replace(rH,"")||"rgb(0,0,0)").trim(),offsetX:i,offsetY:r,blur:s}}toString(){return[this.offsetX,this.offsetY,this.blur,this.color].join("px ")}toSVG(t){let e=rw(new eN(this.offsetX,this.offsetY),eJ(-t.angle)),i=new im(this.color),r=40,s=40;return t.width&&t.height&&(r=100*ix((Math.abs(e.x)+this.blur)/t.width,ea.NUM_FRACTION_DIGITS)+20,s=100*ix((Math.abs(e.y)+this.blur)/t.height,ea.NUM_FRACTION_DIGITS)+20),t.flipX&&(e.x*=-1),t.flipY&&(e.y*=-1),'<filter id="SVGID_'.concat(this.id,'" y="-').concat(s,'%" height="').concat(100+2*s,'%" x="-').concat(r,'%" width="').concat(100+2*r,'%" >\n	<feGaussianBlur in="SourceAlpha" stdDeviation="').concat(ix(this.blur?this.blur/2:0,ea.NUM_FRACTION_DIGITS),'"></feGaussianBlur>\n	<feOffset dx="').concat(ix(e.x,ea.NUM_FRACTION_DIGITS),'" dy="').concat(ix(e.y,ea.NUM_FRACTION_DIGITS),'" result="oBlur" ></feOffset>\n	<feFlood flood-color="').concat(i.toRgb(),'" flood-opacity="').concat(i.getAlpha(),'"/>\n	<feComposite in2="oBlur" operator="in" />\n	<feMerge>\n		<feMergeNode></feMergeNode>\n		<feMergeNode in="SourceGraphic"></feMergeNode>\n	</feMerge>\n</filter>\n')}toObject(){let t={color:this.color,blur:this.blur,offsetX:this.offsetX,offsetY:this.offsetY,affectStroke:this.affectStroke,nonScaling:this.nonScaling,type:this.constructor.type},e=rG.ownDefaults;return this.includeDefaultValues?t:ih(t,(t,i)=>t!==e[i])}static async fromObject(t){return new this(t)}}ei(rG,"ownDefaults",{color:"rgb(0,0,0)",blur:0,offsetX:0,offsetY:0,affectStroke:!1,includeDefaultValues:!0,nonScaling:!1}),ei(rG,"type","shadow"),eA.setClass(rG,"shadow");let rU=t=>JSON.parse(JSON.stringify(t)),rZ=["top",eO,"scaleX","scaleY","flipX","flipY","originX","originY","angle","opacity","globalCompositeOperation","shadow","visible","skewX","skewY"],rq=["fill","stroke","strokeWidth","strokeDashArray","width","height","paintFirst","strokeUniform","strokeLineCap","strokeDashOffset","strokeLineJoin","strokeMiterLimit","backgroundColor","clipPath"],rK=["type"],rJ=["extraParam"],rQ=class t extends rR{static getDefaults(){return t.ownDefaults}get type(){let t=this.constructor.type;return"FabricObject"===t?"object":t.toLowerCase()}set type(t){el("warn","Setting type has no effect",t)}constructor(e){super(),ei(this,"_cacheContext",null),Object.assign(this,t.ownDefaults),this.setOptions(e)}_createCacheCanvas(){this._cacheCanvas=eZ(),this._cacheContext=this._cacheCanvas.getContext("2d"),this._updateCacheCanvas(),this.dirty=!0}_limitCacheSize(t){let e=t.width,i=t.height,r=ea.maxCacheSideLimit,s=ea.minCacheSideLimit;if(e<=r&&i<=r&&e*i<=ea.perfLimitSizeTotal)return e<s&&(t.width=s),i<s&&(t.height=s),t;let[n,o]=ex.limitDimsByArea(e/i),a=rg(s,n,r),l=rg(s,o,r);return e>a&&(t.zoomX/=e/a,t.width=a,t.capped=!0),i>l&&(t.zoomY/=i/l,t.height=l,t.capped=!0),t}_getCacheCanvasDimensions(){let t=this.getTotalObjectScaling(),e=this._getTransformedDimensions({skewX:0,skewY:0}),i=e.x*t.x/this.scaleX,r=e.y*t.y/this.scaleY;return{width:i+2,height:r+2,zoomX:t.x,zoomY:t.y,x:i,y:r}}_updateCacheCanvas(){let t=this._cacheCanvas,e=this._cacheContext,i=this._limitCacheSize(this._getCacheCanvasDimensions()),r=ea.minCacheSideLimit,s=i.width,n=i.height,o=i.zoomX,a=i.zoomY,l=s!==this.cacheWidth||n!==this.cacheHeight,h=this.zoomX!==o||this.zoomY!==a;if(!t||!e)return!1;let c,d,u=l||h,g=0,f=0,p=!1;if(l){let t=this._cacheCanvas.width,e=this._cacheCanvas.height,o=s>t||n>e;p=o||(s<.9*t||n<.9*e)&&t>r&&e>r,o&&!i.capped&&(s>r||n>r)&&(g=.1*s,f=.1*n)}return iO(this)&&this.path&&(u=!0,p=!0,g+=this.getHeightOfLine(0)*this.zoomX,f+=this.getHeightOfLine(0)*this.zoomY),!!u&&(p?(t.width=Math.ceil(s+g),t.height=Math.ceil(n+f)):(e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t.width,t.height)),c=i.x/2,d=i.y/2,this.cacheTranslationX=Math.round(t.width/2-c)+c,this.cacheTranslationY=Math.round(t.height/2-d)+d,this.cacheWidth=s,this.cacheHeight=n,e.translate(this.cacheTranslationX,this.cacheTranslationY),e.scale(o,a),this.zoomX=o,this.zoomY=a,!0)}setOptions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._setOptions(t)}transform(t){let e=this.group&&!this.group._transformDone||this.group&&this.canvas&&t===this.canvas.contextTop,i=this.calcTransformMatrix(!e);t.transform(i[0],i[1],i[2],i[3],i[4],i[5])}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=ea.NUM_FRACTION_DIGITS,i=this.clipPath&&!this.clipPath.excludeFromExport?ee(ee({},this.clipPath.toObject(t)),{},{inverted:this.clipPath.inverted,absolutePositioned:this.clipPath.absolutePositioned}):null,r=ee(ee({},il(this,t)),{},{type:this.constructor.type,version:ey,originX:this.originX,originY:this.originY,left:ix(this.left,e),top:ix(this.top,e),width:ix(this.width,e),height:ix(this.height,e),fill:iS(this.fill)?this.fill.toObject():this.fill,stroke:iS(this.stroke)?this.stroke.toObject():this.stroke,strokeWidth:ix(this.strokeWidth,e),strokeDashArray:this.strokeDashArray?this.strokeDashArray.concat():this.strokeDashArray,strokeLineCap:this.strokeLineCap,strokeDashOffset:this.strokeDashOffset,strokeLineJoin:this.strokeLineJoin,strokeUniform:this.strokeUniform,strokeMiterLimit:ix(this.strokeMiterLimit,e),scaleX:ix(this.scaleX,e),scaleY:ix(this.scaleY,e),angle:ix(this.angle,e),flipX:this.flipX,flipY:this.flipY,opacity:ix(this.opacity,e),shadow:this.shadow&&this.shadow.toObject?this.shadow.toObject():this.shadow,visible:this.visible,backgroundColor:this.backgroundColor,fillRule:this.fillRule,paintFirst:this.paintFirst,globalCompositeOperation:this.globalCompositeOperation,skewX:ix(this.skewX,e),skewY:ix(this.skewY,e)},i?{clipPath:i}:null);return this.includeDefaultValues?r:this._removeDefaultValues(r)}toDatalessObject(t){return this.toObject(t)}_removeDefaultValues(t){let e=this.constructor.getDefaults(),i=Object.keys(e).length>0?e:Object.getPrototypeOf(this);return ih(t,(t,e)=>{if(e===eO||"top"===e||"type"===e)return!0;let r=i[e];return t!==r&&!(Array.isArray(t)&&Array.isArray(r)&&0===t.length&&0===r.length)})}toString(){return"#<".concat(this.constructor.type,">")}getObjectScaling(){if(!this.group)return new eN(Math.abs(this.scaleX),Math.abs(this.scaleY));let t=e5(this.calcTransformMatrix());return new eN(Math.abs(t.scaleX),Math.abs(t.scaleY))}getTotalObjectScaling(){let t=this.getObjectScaling();if(this.canvas){let e=this.canvas.getZoom(),i=this.getCanvasRetinaScaling();return t.scalarMultiply(e*i)}return t}getObjectOpacity(){let t=this.opacity;return this.group&&(t*=this.group.getObjectOpacity()),t}_constrainScale(t){return Math.abs(t)<this.minScaleLimit?t<0?-this.minScaleLimit:this.minScaleLimit:0===t?1e-4:t}_set(t,e){"scaleX"!==t&&"scaleY"!==t||(e=this._constrainScale(e)),"scaleX"===t&&e<0?(this.flipX=!this.flipX,e*=-1):"scaleY"===t&&e<0?(this.flipY=!this.flipY,e*=-1):"shadow"!==t||!e||e instanceof rG||(e=new rG(e));let i=this[t]!==e;return this[t]=e,i&&this.constructor.cacheProperties.includes(t)&&(this.dirty=!0),this.parent&&(this.dirty||i&&this.constructor.stateProperties.includes(t))&&this.parent._set("dirty",!0),this}isNotVisible(){return 0===this.opacity||!this.width&&!this.height&&0===this.strokeWidth||!this.visible}render(t){this.isNotVisible()||this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(t.save(),this._setupCompositeOperation(t),this.drawSelectionBackground(t),this.transform(t),this._setOpacity(t),this._setShadow(t),this.shouldCache()?(this.renderCache(),this.drawCacheOnCanvas(t)):(this._removeCacheCanvas(),this.drawObject(t),this.dirty=!1),t.restore())}drawSelectionBackground(t){}renderCache(t){t=t||{},this._cacheCanvas&&this._cacheContext||this._createCacheCanvas(),this.isCacheDirty()&&this._cacheContext&&(this.drawObject(this._cacheContext,t.forClipping),this.dirty=!1)}_removeCacheCanvas(){this._cacheCanvas=void 0,this._cacheContext=null,this.cacheWidth=0,this.cacheHeight=0}hasStroke(){return this.stroke&&"transparent"!==this.stroke&&0!==this.strokeWidth}hasFill(){return this.fill&&"transparent"!==this.fill}needsItsOwnCache(){return!!("stroke"===this.paintFirst&&this.hasFill()&&this.hasStroke()&&this.shadow)||!!this.clipPath}shouldCache(){return this.ownCaching=this.needsItsOwnCache()||this.objectCaching&&(!this.parent||!this.parent.isOnACache()),this.ownCaching}willDrawShadow(){return!!this.shadow&&(0!==this.shadow.offsetX||0!==this.shadow.offsetY)}drawClipPathOnCache(t,e){if(t.save(),e.inverted?t.globalCompositeOperation="destination-out":t.globalCompositeOperation="destination-in",e.absolutePositioned){let e=e1(this.calcTransformMatrix());t.transform(e[0],e[1],e[2],e[3],e[4],e[5])}e.transform(t),t.scale(1/e.zoomX,1/e.zoomY),t.drawImage(e._cacheCanvas,-e.cacheTranslationX,-e.cacheTranslationY),t.restore()}drawObject(t,e){let i=this.fill,r=this.stroke;e?(this.fill="black",this.stroke="",this._setClippingProperties(t)):this._renderBackground(t),this._render(t),this._drawClipPath(t,this.clipPath),this.fill=i,this.stroke=r}_drawClipPath(t,e){e&&(e._set("canvas",this.canvas),e.shouldCache(),e._transformDone=!0,e.renderCache({forClipping:!0}),this.drawClipPathOnCache(t,e))}drawCacheOnCanvas(t){t.scale(1/this.zoomX,1/this.zoomY),t.drawImage(this._cacheCanvas,-this.cacheTranslationX,-this.cacheTranslationY)}isCacheDirty(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.isNotVisible())return!1;if(this._cacheCanvas&&this._cacheContext&&!t&&this._updateCacheCanvas())return!0;if(this.dirty||this.clipPath&&this.clipPath.absolutePositioned){if(this._cacheCanvas&&this._cacheContext&&!t){let t=this.cacheWidth/this.zoomX,e=this.cacheHeight/this.zoomY;this._cacheContext.clearRect(-t/2,-e/2,t,e)}return!0}return!1}_renderBackground(t){if(!this.backgroundColor)return;let e=this._getNonTransformedDimensions();t.fillStyle=this.backgroundColor,t.fillRect(-e.x/2,-e.y/2,e.x,e.y),this._removeShadow(t)}_setOpacity(t){this.group&&!this.group._transformDone?t.globalAlpha=this.getObjectOpacity():t.globalAlpha*=this.opacity}_setStrokeStyles(t,e){let i=e.stroke;i&&(t.lineWidth=e.strokeWidth,t.lineCap=e.strokeLineCap,t.lineDashOffset=e.strokeDashOffset,t.lineJoin=e.strokeLineJoin,t.miterLimit=e.strokeMiterLimit,iC(i)?"percentage"===i.gradientUnits||i.gradientTransform||i.patternTransform?this._applyPatternForTransformedGradient(t,i):(t.strokeStyle=i.toLive(t),this._applyPatternGradientTransform(t,i)):t.strokeStyle=e.stroke)}_setFillStyles(t,e){let{fill:i}=e;i&&(iC(i)?(t.fillStyle=i.toLive(t),this._applyPatternGradientTransform(t,i)):t.fillStyle=i)}_setClippingProperties(t){t.globalAlpha=1,t.strokeStyle="transparent",t.fillStyle="#000000"}_setLineDash(t,e){e&&0!==e.length&&(1&e.length&&e.push(...e),t.setLineDash(e))}_setShadow(t){if(!this.shadow)return;let e=this.shadow,i=this.canvas,r=this.getCanvasRetinaScaling(),[s,,,n]=(null==i?void 0:i.viewportTransform)||eS,o=s*r,a=n*r,l=e.nonScaling?new eN(1,1):this.getObjectScaling();t.shadowColor=e.color,t.shadowBlur=e.blur*ea.browserShadowBlurConstant*(o+a)*(l.x+l.y)/4,t.shadowOffsetX=e.offsetX*o*l.x,t.shadowOffsetY=e.offsetY*a*l.y}_removeShadow(t){this.shadow&&(t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0)}_applyPatternGradientTransform(t,e){if(!iC(e))return{offsetX:0,offsetY:0};let i=e.gradientTransform||e.patternTransform,r=-this.width/2+e.offsetX||0,s=-this.height/2+e.offsetY||0;return"percentage"===e.gradientUnits?t.transform(this.width,0,0,this.height,r,s):t.transform(1,0,0,1,r,s),i&&t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),{offsetX:r,offsetY:s}}_renderPaintInOrder(t){"stroke"===this.paintFirst?(this._renderStroke(t),this._renderFill(t)):(this._renderFill(t),this._renderStroke(t))}_render(t){}_renderFill(t){this.fill&&(t.save(),this._setFillStyles(t,this),"evenodd"===this.fillRule?t.fill("evenodd"):t.fill(),t.restore())}_renderStroke(t){if(this.stroke&&0!==this.strokeWidth){if(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this.strokeUniform){let e=this.getObjectScaling();t.scale(1/e.x,1/e.y)}this._setLineDash(t,this.strokeDashArray),this._setStrokeStyles(t,this),t.stroke(),t.restore()}}_applyPatternForTransformedGradient(t,e){var i;let r=this._limitCacheSize(this._getCacheCanvasDimensions()),s=eZ(),n=this.getCanvasRetinaScaling(),o=r.x/this.scaleX/n,a=r.y/this.scaleY/n;s.width=Math.ceil(o),s.height=Math.ceil(a);let l=s.getContext("2d");l&&(l.beginPath(),l.moveTo(0,0),l.lineTo(o,0),l.lineTo(o,a),l.lineTo(0,a),l.closePath(),l.translate(o/2,a/2),l.scale(r.zoomX/this.scaleX/n,r.zoomY/this.scaleY/n),this._applyPatternGradientTransform(l,e),l.fillStyle=e.toLive(t),l.fill(),t.translate(-this.width/2-this.strokeWidth/2,-this.height/2-this.strokeWidth/2),t.scale(n*this.scaleX/r.zoomX,n*this.scaleY/r.zoomY),t.strokeStyle=null!==(i=l.createPattern(s,"no-repeat"))&&void 0!==i?i:"")}_findCenterFromElement(){return new eN(this.left+this.width/2,this.top+this.height/2)}clone(t){let e=this.toObject(t);return this.constructor.fromObject(e)}cloneAsImage(t){let e=this.toCanvasElement(t);return new(eA.getClass("image"))(e)}toCanvasElement(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=iU(this),i=this.group,r=this.shadow,s=Math.abs,n=t.enableRetinaScaling?em():1,o=(t.multiplier||1)*n,a=t.canvasProvider||(t=>new iI(t,{enableRetinaScaling:!1,renderOnAddRemove:!1,skipOffscreen:!1}));delete this.group,t.withoutTransform&&iG(this),t.withoutShadow&&(this.shadow=null),t.viewportTransform&&iQ(this,this.getViewportTransform()),this.setCoords();let l=eZ(),h=this.getBoundingRect(),c=this.shadow,d=new eN;if(c){let t=c.blur,e=c.nonScaling?new eN(1,1):this.getObjectScaling();d.x=2*Math.round(s(c.offsetX)+t)*s(e.x),d.y=2*Math.round(s(c.offsetY)+t)*s(e.y)}let u=h.width+d.x,g=h.height+d.y;l.width=Math.ceil(u),l.height=Math.ceil(g);let f=a(l);"jpeg"===t.format&&(f.backgroundColor="#fff"),this.setPositionByOrigin(new eN(f.width/2,f.height/2),eT,eT);let p=this.canvas;f._objects=[this],this.set("canvas",f),this.setCoords();let v=f.toCanvasElement(o||1,t);return this.set("canvas",p),this.shadow=r,i&&(this.group=i),this.set(e),this.setCoords(),f._objects=[],f.destroy(),v}toDataURL(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return eK(this.toCanvasElement(t),t.format||"png",t.quality||1)}isType(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.includes(this.constructor.type)||e.includes(this.type)}complexity(){return 1}toJSON(){return this.toObject()}rotate(t){let{centeredRotation:e,originX:i,originY:r}=this;if(e){let{x:t,y:e}=this.getRelativeCenterPoint();this.originX=eT,this.originY=eT,this.left=t,this.top=e}if(this.set("angle",t),e){let{x:t,y:e}=this.translateToOriginPoint(this.getRelativeCenterPoint(),i,r);this.left=t,this.top=e,this.originX=i,this.originY=r}}setOnGroup(){}_setupCompositeOperation(t){this.globalCompositeOperation&&(t.globalCompositeOperation=this.globalCompositeOperation)}dispose(){eL.cancelByTarget(this),this.off(),this._set("canvas",void 0),this._cacheCanvas&&ef().dispose(this._cacheCanvas),this._cacheCanvas=void 0,this._cacheContext=null}static _fromObject(t){let e=er(t,rK),i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{extraParam:r}=i,s=er(i,rJ);return ia(rU(e),s).then(t=>{let e=ee(ee({},s),t);if(r){let{[r]:t}=e;return new this(t,er(e,[r].map(en)))}return new this(e)})}static fromObject(t,e){return this._fromObject(t,e)}};ei(rQ,"stateProperties",rZ),ei(rQ,"cacheProperties",rq),ei(rQ,"ownDefaults",{top:0,left:0,width:0,height:0,angle:0,flipX:!1,flipY:!1,scaleX:1,scaleY:1,minScaleLimit:0,skewX:0,skewY:0,originX:eO,originY:"top",strokeWidth:1,strokeUniform:!1,padding:0,opacity:1,paintFirst:"fill",fill:"rgb(0,0,0)",fillRule:"nonzero",stroke:null,strokeDashArray:null,strokeDashOffset:0,strokeLineCap:"butt",strokeLineJoin:"miter",strokeMiterLimit:4,globalCompositeOperation:"source-over",backgroundColor:"",shadow:null,visible:!0,includeDefaultValues:!0,excludeFromExport:!1,objectCaching:!0,clipPath:void 0,inverted:!1,absolutePositioned:!1,centeredRotation:!0,centeredScaling:!1,dirty:!0}),ei(rQ,"type","FabricObject"),eA.setClass(rQ),eA.setClass(rQ,"object");let r$=(t,e)=>(i,r,s,n)=>{let o=e(i,r,s,n);return o&&i$(t,i8(i,r,s,n)),o};function r0(t){return(e,i,r,s)=>{let{target:n,originX:o,originY:a}=i,l=n.getRelativeCenterPoint(),h=n.translateToOriginPoint(l,o,a),c=t(e,i,r,s);return n.setPositionByOrigin(h,i.originX,i.originY),c}}let r1=r$("resizing",r0((t,e,i,r)=>{let s=i9(e,e.originX,e.originY,i,r);if(e.originX===eT||e.originX===ej&&s.x<0||e.originX===eO&&s.x>0){let{target:t}=e,i=t.strokeWidth/(t.strokeUniform?t.scaleX:1),r=i3(e)?2:1,n=t.width,o=Math.ceil(Math.abs(s.x*r/t.scaleX)-i);return t.set("width",Math.max(o,0)),n!==t.width}return!1}));function r2(t,e,i,r,s){r=r||{};let n=this.sizeX||r.cornerSize||s.cornerSize,o=this.sizeY||r.cornerSize||s.cornerSize,a=void 0!==r.transparentCorners?r.transparentCorners:s.transparentCorners,l=!a&&(r.cornerStrokeColor||s.cornerStrokeColor),h,c=e,d=i;t.save(),t.fillStyle=r.cornerColor||s.cornerColor||"",t.strokeStyle=r.cornerStrokeColor||s.cornerStrokeColor||"",n>o?(h=n,t.scale(1,o/n),d=i*n/o):o>n?(h=o,t.scale(n/o,1),c=e*o/n):h=n,t.lineWidth=1,t.beginPath(),t.arc(c,d,h/2,0,ew,!1),t[a?"stroke":"fill"](),l&&t.stroke(),t.restore()}function r3(t,e,i,r,s){r=r||{};let n=this.sizeX||r.cornerSize||s.cornerSize,o=this.sizeY||r.cornerSize||s.cornerSize,a=void 0!==r.transparentCorners?r.transparentCorners:s.transparentCorners,l=!a&&(r.cornerStrokeColor||s.cornerStrokeColor),h=n/2,c=o/2;t.save(),t.fillStyle=r.cornerColor||s.cornerColor||"",t.strokeStyle=r.cornerStrokeColor||s.cornerStrokeColor||"",t.lineWidth=1,t.translate(e,i);let d=s.getTotalAngle();t.rotate(eJ(d)),t["".concat(a?"stroke":"fill","Rect")](-h,-c,n,o),l&&t.strokeRect(-h,-c,n,o),t.restore()}class r6{constructor(t){ei(this,"visible",!0),ei(this,"actionName","scale"),ei(this,"angle",0),ei(this,"x",0),ei(this,"y",0),ei(this,"offsetX",0),ei(this,"offsetY",0),ei(this,"sizeX",0),ei(this,"sizeY",0),ei(this,"touchSizeX",0),ei(this,"touchSizeY",0),ei(this,"cursorStyle","crosshair"),ei(this,"withConnection",!1),Object.assign(this,t)}shouldActivate(t,e,i,r){var s;let{tl:n,tr:o,br:a,bl:l}=r;return(null===(s=e.canvas)||void 0===s?void 0:s.getActiveObject())===e&&e.isControlVisible(t)&&rA.isPointInPolygon(i,[n,o,a,l])}getActionHandler(t,e,i){return this.actionHandler}getMouseDownHandler(t,e,i){return this.mouseDownHandler}getMouseUpHandler(t,e,i){return this.mouseUpHandler}cursorStyleHandler(t,e,i){return e.cursorStyle}getActionName(t,e,i){return e.actionName}getVisibility(t,e){var i,r;return null!==(i=null===(r=t._controlsVisibility)||void 0===r?void 0:r[e])&&void 0!==i?i:this.visible}setVisibility(t,e,i){this.visible=t}positionHandler(t,e,i,r){return new eN(this.x*t.x+this.offsetX,this.y*t.y+this.offsetY).transform(e)}calcCornerCoords(t,e,i,r,s,n){let o=e3([e8(i,r),e4({angle:t}),e9((s?this.touchSizeX:this.sizeX)||e,(s?this.touchSizeY:this.sizeY)||e)]);return{tl:new eN(-.5,-.5).transform(o),tr:new eN(.5,-.5).transform(o),br:new eN(.5,.5).transform(o),bl:new eN(-.5,.5).transform(o)}}render(t,e,i,r,s){"circle"===((r=r||{}).cornerStyle||s.cornerStyle)?r2.call(this,t,e,i,r,s):r3.call(this,t,e,i,r,s)}}let r5=(t,e,i)=>i.lockRotation?i2:e.cursorStyle,r8=r$("rotating",r0((t,e,i,r)=>{let{target:s,ex:n,ey:o,theta:a,originX:l,originY:h}=e,c=s.translateToOriginPoint(s.getRelativeCenterPoint(),l,h);if(i5(s,"lockRotation"))return!1;let d=Math.atan2(o-c.y,n-c.x),u=eQ(Math.atan2(r-c.y,i-c.x)-d+a);if(s.snapAngle&&s.snapAngle>0){let t=s.snapAngle,e=s.snapThreshold||t,i=Math.ceil(u/t)*t,r=Math.floor(u/t)*t;Math.abs(u-r)<e?u=r:Math.abs(u-i)<e&&(u=i)}u<0&&(u=360+u),u%=360;let g=s.angle!==u;return s.angle=u,g}));function r4(t,e){let i=e.canvas,r=t[i.uniScaleKey];return i.uniformScaling&&!r||!i.uniformScaling&&r}function r9(t,e,i){let r=i5(t,"lockScalingX"),s=i5(t,"lockScalingY");if(r&&s||!e&&(r||s)&&i||r&&"x"===e||s&&"y"===e)return!0;let{width:n,height:o,strokeWidth:a}=t;return 0===n&&0===a&&"y"!==e||0===o&&0===a&&"x"!==e}let r7=["e","se","s","sw","w","nw","n","ne","e"],st=(t,e,i)=>{let r=r4(t,i);if(r9(i,0!==e.x&&0===e.y?"x":0===e.x&&0!==e.y?"y":"",r))return i2;let s=i4(i,e);return"".concat(r7[s],"-resize")};function se(t,e,i,r){let s,n,o,a,l,h,c=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},d=e.target,u=c.by,g=r4(t,d);if(r9(d,u,g))return!1;if(e.gestureScale)n=e.scaleX*e.gestureScale,o=e.scaleY*e.gestureScale;else{if(s=i9(e,e.originX,e.originY,i,r),l="y"!==u?Math.sign(s.x||e.signX||1):1,h="x"!==u?Math.sign(s.y||e.signY||1):1,e.signX||(e.signX=l),e.signY||(e.signY=h),i5(d,"lockScalingFlip")&&(e.signX!==l||e.signY!==h))return!1;if(a=d._getTransformedDimensions(),g&&!u){let t=Math.abs(s.x)+Math.abs(s.y),{original:i}=e,r=t/(Math.abs(a.x*i.scaleX/d.scaleX)+Math.abs(a.y*i.scaleY/d.scaleY));n=i.scaleX*r,o=i.scaleY*r}else n=Math.abs(s.x*d.scaleX/a.x),o=Math.abs(s.y*d.scaleY/a.y);i3(e)&&(n*=2,o*=2),e.signX!==l&&"y"!==u&&(e.originX=.5-i1(e.originX),n*=-1,e.signX=l),e.signY!==h&&"x"!==u&&(e.originY=.5-i1(e.originY),o*=-1,e.signY=h)}let f=d.scaleX,p=d.scaleY;return u?("x"===u&&d.set("scaleX",n),"y"===u&&d.set("scaleY",o)):(i5(d,"lockScalingX")||d.set("scaleX",n),i5(d,"lockScalingY")||d.set("scaleY",o)),f!==d.scaleX||p!==d.scaleY}let si=r$("scaling",r0((t,e,i,r)=>se(t,e,i,r))),sr=r$("scaling",r0((t,e,i,r)=>se(t,e,i,r,{by:"x"}))),ss=r$("scaling",r0((t,e,i,r)=>se(t,e,i,r,{by:"y"}))),sn=["target","ex","ey","skewingSide"],so={x:{counterAxis:"y",scale:"scaleX",skew:"skewX",lockSkewing:"lockSkewingX",origin:"originX",flip:"flipX"},y:{counterAxis:"x",scale:"scaleY",skew:"skewY",lockSkewing:"lockSkewingY",origin:"originY",flip:"flipY"}},sa=["ns","nesw","ew","nwse"],sl=(t,e,i)=>{if(0!==e.x&&i5(i,"lockSkewingY")||0!==e.y&&i5(i,"lockSkewingX"))return i2;let r=i4(i,e)%4;return"".concat(sa[r],"-resize")};function sh(t,e,i,r,s){let{target:n}=i,{counterAxis:o,origin:a,lockSkewing:l,skew:h,flip:c}=so[t];if(i5(n,l))return!1;let{origin:d,flip:u}=so[o],g=-Math.sign(i1(i[d])*(n[u]?-1:1))*(n[c]?-1:1),f=-(.5*((0===n[h]&&i9(i,eT,eT,r,s)[t]>0||n[h]>0?1:-1)*g))+.5;return r$("skewing",r0((e,i,r,s)=>(function(t,e,i){let{target:r,ex:s,ey:n,skewingSide:o}=e,a=er(e,sn),{skew:l}=so[t],h=i.subtract(new eN(s,n)).divide(new eN(r.scaleX,r.scaleY))[t],c=r[l],d=Math.tan(eJ(a[l])),u=eQ(Math.atan(2*h*o/Math.max("y"===t?r._getTransformedDimensions({scaleX:1,scaleY:1,skewX:0}).x:r._getTransformedDimensions({scaleX:1,scaleY:1}).y,1)+d));r.set(l,u);let g=c!==r[l];if(g&&"y"===t){let{skewX:t,scaleX:e}=r,i=r._getTransformedDimensions({skewY:c}),s=r._getTransformedDimensions(),n=0!==t?i.x/s.x:1;1!==n&&r.set("scaleX",n*e)}return g})(t,i,new eN(r,s))))(e,ee(ee({},i),{},{[a]:f,skewingSide:g}),r,s)}let sc=(t,e,i,r)=>sh("x",t,e,i,r),sd=(t,e,i,r)=>sh("y",t,e,i,r);function su(t,e){return t[e.canvas.altActionKey]}let sg=(t,e,i)=>{let r=su(t,i);return 0===e.x?r?"skewX":"scaleY":0===e.y?r?"skewY":"scaleX":""},sf=(t,e,i)=>su(t,i)?sl(0,e,i):st(t,e,i),sp=(t,e,i,r)=>su(t,e.target)?sd(t,e,i,r):sr(t,e,i,r),sv=(t,e,i,r)=>su(t,e.target)?sc(t,e,i,r):ss(t,e,i,r),sm=()=>({ml:new r6({x:-.5,y:0,cursorStyleHandler:sf,actionHandler:sp,getActionName:sg}),mr:new r6({x:.5,y:0,cursorStyleHandler:sf,actionHandler:sp,getActionName:sg}),mb:new r6({x:0,y:.5,cursorStyleHandler:sf,actionHandler:sv,getActionName:sg}),mt:new r6({x:0,y:-.5,cursorStyleHandler:sf,actionHandler:sv,getActionName:sg}),tl:new r6({x:-.5,y:-.5,cursorStyleHandler:st,actionHandler:si}),tr:new r6({x:.5,y:-.5,cursorStyleHandler:st,actionHandler:si}),bl:new r6({x:-.5,y:.5,cursorStyleHandler:st,actionHandler:si}),br:new r6({x:.5,y:.5,cursorStyleHandler:st,actionHandler:si}),mtr:new r6({x:0,y:-.5,actionHandler:r8,cursorStyleHandler:r5,offsetY:-40,withConnection:!0,actionName:"rotate"})}),sx=()=>({mr:new r6({x:.5,y:0,actionHandler:r1,cursorStyleHandler:sf,actionName:"resizing"}),ml:new r6({x:-.5,y:0,actionHandler:r1,cursorStyleHandler:sf,actionName:"resizing"})}),sy=()=>ee(ee({},sm()),sx());class s_ extends rQ{static getDefaults(){return ee(ee({},super.getDefaults()),s_.ownDefaults)}constructor(t){super(),Object.assign(this,this.constructor.createControls(),s_.ownDefaults),this.setOptions(t)}static createControls(){return{controls:sm()}}_updateCacheCanvas(){let t=this.canvas;if(this.noScaleCache&&t&&t._currentTransform){let e=t._currentTransform,i=e.target,r=e.action;if(this===i&&r&&r.startsWith("scale"))return!1}return super._updateCacheCanvas()}getActiveControl(){let t=this.__corner;return t?{key:t,control:this.controls[t],coord:this.oCoords[t]}:void 0}findControl(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasControls||!this.canvas)return;this.__corner=void 0;let i=Object.entries(this.oCoords);for(let r=i.length-1;r>=0;r--){let[s,n]=i[r],o=this.controls[s];if(o.shouldActivate(s,this,t,e?n.touchCorner:n.corner))return this.__corner=s,{key:s,control:o,coord:this.oCoords[s]}}}calcOCoords(){let t=this.getViewportTransform(),e=this.getCenterPoint(),i=e8(e.x,e.y),r=e2(i,e4({angle:this.getTotalAngle()-(this.group&&this.flipX?180:0)})),s=e2(t,r),n=e2(s,[1/t[0],0,0,1/t[3],0,0]),o=this.group?e5(this.calcTransformMatrix()):void 0;o&&(o.scaleX=Math.abs(o.scaleX),o.scaleY=Math.abs(o.scaleY));let a=this._calculateCurrentDimensions(o),l={};return this.forEachControl((t,e)=>{let i=t.positionHandler(a,n,this,t);l[e]=Object.assign(i,this._calcCornerCoords(t,i))}),l}_calcCornerCoords(t,e){let i=this.getTotalAngle();return{corner:t.calcCornerCoords(i,this.cornerSize,e.x,e.y,!1,this),touchCorner:t.calcCornerCoords(i,this.touchCornerSize,e.x,e.y,!0,this)}}setCoords(){super.setCoords(),this.canvas&&(this.oCoords=this.calcOCoords())}forEachControl(t){for(let e in this.controls)t(this.controls[e],e,this)}drawSelectionBackground(t){if(!this.selectionBackgroundColor||this.canvas&&this.canvas._activeObject!==this)return;t.save();let e=this.getRelativeCenterPoint(),i=this._calculateCurrentDimensions(),r=this.getViewportTransform();t.translate(e.x,e.y),t.scale(1/r[0],1/r[3]),t.rotate(eJ(this.angle)),t.fillStyle=this.selectionBackgroundColor,t.fillRect(-i.x/2,-i.y/2,i.x,i.y),t.restore()}strokeBorders(t,e){t.strokeRect(-e.x/2,-e.y/2,e.x,e.y)}_drawBorders(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=ee({hasControls:this.hasControls,borderColor:this.borderColor,borderDashArray:this.borderDashArray},i);t.save(),t.strokeStyle=r.borderColor,this._setLineDash(t,r.borderDashArray),this.strokeBorders(t,e),r.hasControls&&this.drawControlsConnectingLines(t,e),t.restore()}_renderControls(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{hasBorders:i,hasControls:r}=this,s=ee({hasBorders:i,hasControls:r},e),n=this.getViewportTransform(),o=s.hasBorders,a=s.hasControls,l=e5(e2(n,this.calcTransformMatrix()));t.save(),t.translate(l.translateX,l.translateY),t.lineWidth=1*this.borderScaleFactor,this.group===this.parent&&(t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1),this.flipX&&(l.angle-=180),t.rotate(eJ(this.group?l.angle:this.angle)),o&&this.drawBorders(t,l,e),a&&this.drawControls(t,e),t.restore()}drawBorders(t,e,i){let r;if(i&&i.forActiveSelection||this.group){let t=iZ(this.width,this.height,ii(e)),i=this.isStrokeAccountedForInDimensions()?eV:(this.strokeUniform?(new eN).scalarAdd(this.canvas?this.canvas.getZoom():1):new eN(e.scaleX,e.scaleY)).scalarMultiply(this.strokeWidth);r=t.add(i).scalarAdd(this.borderScaleFactor).scalarAdd(2*this.padding)}else r=this._calculateCurrentDimensions().scalarAdd(this.borderScaleFactor);this._drawBorders(t,r,i)}drawControlsConnectingLines(t,e){let i=!1;t.beginPath(),this.forEachControl((r,s)=>{r.withConnection&&r.getVisibility(this,s)&&(i=!0,t.moveTo(r.x*e.x,r.y*e.y),t.lineTo(r.x*e.x+r.offsetX,r.y*e.y+r.offsetY))}),i&&t.stroke()}drawControls(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t.save();let i=this.getCanvasRetinaScaling(),{cornerStrokeColor:r,cornerDashArray:s,cornerColor:n}=this,o=ee({cornerStrokeColor:r,cornerDashArray:s,cornerColor:n},e);t.setTransform(i,0,0,i,0,0),t.strokeStyle=t.fillStyle=o.cornerColor,this.transparentCorners||(t.strokeStyle=o.cornerStrokeColor),this._setLineDash(t,o.cornerDashArray),this.setCoords(),this.forEachControl((e,i)=>{if(e.getVisibility(this,i)){let r=this.oCoords[i];e.render(t,r.x,r.y,o,this)}}),t.restore()}isControlVisible(t){return this.controls[t]&&this.controls[t].getVisibility(this,t)}setControlVisible(t,e){this._controlsVisibility||(this._controlsVisibility={}),this._controlsVisibility[t]=e}setControlsVisibility(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Object.entries(t).forEach(t=>{let[e,i]=t;return this.setControlVisible(e,i)})}clearContextTop(t){if(!this.canvas)return;let e=this.canvas.contextTop;if(!e)return;let i=this.canvas.viewportTransform;e.save(),e.transform(i[0],i[1],i[2],i[3],i[4],i[5]),this.transform(e);let r=this.width+4,s=this.height+4;return e.clearRect(-r/2,-s/2,r,s),t||e.restore(),e}onDeselect(t){return!1}onSelect(t){return!1}shouldStartDragging(t){return!1}onDragStart(t){return!1}canDrop(t){return!1}renderDragSourceEffect(t){}renderDropTargetEffect(t){}}function sb(t,e){return e.forEach(e=>{Object.getOwnPropertyNames(e.prototype).forEach(i=>{"constructor"!==i&&Object.defineProperty(t.prototype,i,Object.getOwnPropertyDescriptor(e.prototype,i)||Object.create(null))})}),t}ei(s_,"ownDefaults",{noScaleCache:!0,lockMovementX:!1,lockMovementY:!1,lockRotation:!1,lockScalingX:!1,lockScalingY:!1,lockSkewingX:!1,lockSkewingY:!1,lockScalingFlip:!1,cornerSize:13,touchCornerSize:24,transparentCorners:!0,cornerColor:"rgb(178,204,255)",cornerStrokeColor:"",cornerStyle:"rect",cornerDashArray:null,hasControls:!0,borderColor:"rgb(178,204,255)",borderDashArray:null,borderOpacityWhenMoving:.4,borderScaleFactor:1,hasBorders:!0,selectionBackgroundColor:"",selectable:!0,evented:!0,perPixelTargetFind:!1,activeOn:"down",hoverCursor:null,moveCursor:null});class sw extends s_{}sb(sw,[rt]),eA.setClass(sw),eA.setClass(sw,"object");let sC=(t,e,i,r)=>{let s=2*(r=Math.round(r))+1,{data:n}=t.getImageData(e-r,i-r,s,s);for(let t=3;t<n.length;t+=4)if(n[t]>0)return!1;return!0};class sS{constructor(t){this.options=t,this.strokeProjectionMagnitude=this.options.strokeWidth/2,this.scale=new eN(this.options.scaleX,this.options.scaleY),this.strokeUniformScalar=this.options.strokeUniform?new eN(1/this.options.scaleX,1/this.options.scaleY):new eN(1,1)}createSideVector(t,e){let i=rC(t,e);return this.options.strokeUniform?i.multiply(this.scale):i}projectOrthogonally(t,e,i){return this.applySkew(t.add(this.calcOrthogonalProjection(t,e,i)))}isSkewed(){return 0!==this.options.skewX||0!==this.options.skewY}applySkew(t){let e=new eN(t);return e.y+=e.x*Math.tan(eJ(this.options.skewY)),e.x+=e.y*Math.tan(eJ(this.options.skewX)),e}scaleUnitVector(t,e){return t.multiply(this.strokeUniformScalar).scalarMultiply(e)}}let sT=new eN;class sO extends sS{static getOrthogonalRotationFactor(t,e){return Math.abs(e?rT(t,e):rO(t))<eb?-1:1}constructor(t,e,i,r){super(r),ei(this,"AB",void 0),ei(this,"AC",void 0),ei(this,"alpha",void 0),ei(this,"bisector",void 0),this.A=new eN(t),this.B=new eN(e),this.C=new eN(i),this.AB=this.createSideVector(this.A,this.B),this.AC=this.createSideVector(this.A,this.C),this.alpha=rT(this.AB,this.AC),this.bisector=rk(rw(this.AB.eq(sT)?this.AC:this.AB,this.alpha/2))}calcOrthogonalProjection(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.strokeProjectionMagnitude,r=rj(this.createSideVector(t,e)),s=sO.getOrthogonalRotationFactor(r,this.bisector);return this.scaleUnitVector(r,i*s)}projectBevel(){let t=[];return(this.alpha%ew==0?[this.B]:[this.B,this.C]).forEach(e=>{t.push(this.projectOrthogonally(this.A,e)),t.push(this.projectOrthogonally(this.A,e,-this.strokeProjectionMagnitude))}),t}projectMiter(){let t=[],e=1/Math.sin(Math.abs(this.alpha)/2),i=this.scaleUnitVector(this.bisector,-this.strokeProjectionMagnitude*e),r=this.options.strokeUniform?rS(this.scaleUnitVector(this.bisector,this.options.strokeMiterLimit)):this.options.strokeMiterLimit;return rS(i)/this.strokeProjectionMagnitude<=r&&t.push(this.applySkew(this.A.add(i))),t.push(...this.projectBevel()),t}projectRoundNoSkew(t,e){let i=[],r=new eN(sO.getOrthogonalRotationFactor(this.bisector),sO.getOrthogonalRotationFactor(new eN(this.bisector.y,this.bisector.x)));return[new eN(1,0).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar).multiply(r),new eN(0,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar).multiply(r)].forEach(r=>{rD(r,t,e)&&i.push(this.A.add(r))}),i}projectRoundWithSkew(t,e){let i=[],{skewX:r,skewY:s,scaleX:n,scaleY:o,strokeUniform:a}=this.options,l=new eN(Math.tan(eJ(r)),Math.tan(eJ(s))),h=this.strokeProjectionMagnitude,c=a?h/o/Math.sqrt(1/o**2+1/n**2*l.y**2):h/Math.sqrt(1+l.y**2),d=new eN(Math.sqrt(Math.max(h**2-c**2,0)),c),u=a?h/Math.sqrt(1+l.x**2*(1/o)**2/(1/n+1/n*l.x*l.y)**2):h/Math.sqrt(1+l.x**2/(1+l.x*l.y)**2),g=new eN(u,Math.sqrt(Math.max(h**2-u**2,0)));return[g,g.scalarMultiply(-1),d,d.scalarMultiply(-1)].map(t=>this.applySkew(a?t.multiply(this.strokeUniformScalar):t)).forEach(r=>{rD(r,t,e)&&i.push(this.applySkew(this.A).add(r))}),i}projectRound(){let t=[];t.push(...this.projectBevel());let e=this.alpha%ew==0,i=this.applySkew(this.A),r=t[e?0:2].subtract(i),s=t[e?1:0].subtract(i),n=rM(r,e?this.applySkew(this.AB.scalarMultiply(-1)):this.applySkew(this.bisector.multiply(this.strokeUniformScalar).scalarMultiply(-1)))>0,o=n?r:s,a=n?s:r;return this.isSkewed()?t.push(...this.projectRoundWithSkew(o,a)):t.push(...this.projectRoundNoSkew(o,a)),t}projectPoints(){switch(this.options.strokeLineJoin){case"miter":return this.projectMiter();case"round":return this.projectRound();default:return this.projectBevel()}}project(){return this.projectPoints().map(t=>({originPoint:this.A,projectedPoint:t,angle:this.alpha,bisector:this.bisector}))}}class sk extends sS{constructor(t,e,i){super(i),this.A=new eN(t),this.T=new eN(e)}calcOrthogonalProjection(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.strokeProjectionMagnitude,r=this.createSideVector(t,e);return this.scaleUnitVector(rj(r),i)}projectButt(){return[this.projectOrthogonally(this.A,this.T,this.strokeProjectionMagnitude),this.projectOrthogonally(this.A,this.T,-this.strokeProjectionMagnitude)]}projectRound(){let t=[];if(!this.isSkewed()&&this.A.eq(this.T)){let e=new eN(1,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar);t.push(this.applySkew(this.A.add(e)),this.applySkew(this.A.subtract(e)))}else t.push(...new sO(this.A,this.T,this.T,this.options).projectRound());return t}projectSquare(){let t=[];if(this.A.eq(this.T)){let e=new eN(1,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar);t.push(this.A.add(e),this.A.subtract(e))}else{let e=this.calcOrthogonalProjection(this.A,this.T,this.strokeProjectionMagnitude),i=this.scaleUnitVector(rk(this.createSideVector(this.A,this.T)),-this.strokeProjectionMagnitude),r=this.A.add(i);t.push(r.add(e),r.subtract(e))}return t.map(t=>this.applySkew(t))}projectPoints(){switch(this.options.strokeLineCap){case"round":return this.projectRound();case"square":return this.projectSquare();default:return this.projectButt()}}project(){return this.projectPoints().map(t=>({originPoint:this.A,projectedPoint:t}))}}let sj=function(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=[];if(0===t.length)return r;let s=t.reduce((t,e)=>(t[t.length-1].eq(e)||t.push(new eN(e)),t),[new eN(t[0])]);if(1===s.length)i=!0;else if(!i){let t=s[0],e=((t,e)=>{for(let i=t.length-1;i>=0;i--)if(e(t[i],i,t))return i;return -1})(s,e=>!e.eq(t));s.splice(e+1)}return s.forEach((t,s,n)=>{let o,a;0===s?(a=n[1],o=i?t:n[n.length-1]):s===n.length-1?(o=n[s-1],a=i?t:n[0]):(o=n[s-1],a=n[s+1]),i&&1===n.length?r.push(...new sk(t,t,e).project()):i&&(0===s||s===n.length-1)?r.push(...new sk(t,0===s?a:o,e).project()):r.push(...new sO(t,o,a,e).project())}),r},sM=t=>t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),sE=t=>{let e=[];for(let i,r=0;r<t.length;r++)!1!==(i=sD(t,r))&&e.push(i);return e},sD=(t,e)=>{let i=t.charCodeAt(e);if(isNaN(i))return"";if(i<55296||i>57343)return t.charAt(e);if(55296<=i&&i<=56319){if(t.length<=e+1)throw"High surrogate without following low surrogate";let i=t.charCodeAt(e+1);if(56320>i||i>57343)throw"High surrogate without following low surrogate";return t.charAt(e)+t.charAt(e+1)}if(0===e)throw"Low surrogate without preceding high surrogate";let r=t.charCodeAt(e-1);if(55296>r||r>56319)throw"Low surrogate without preceding high surrogate";return!1};var sA=Object.freeze({__proto__:null,capitalize:function(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"".concat(t.charAt(0).toUpperCase()).concat(e?t.slice(1):t.slice(1).toLowerCase())},escapeXml:sM,graphemeSplit:sE});let sL=function(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t.fill!==e.fill||t.stroke!==e.stroke||t.strokeWidth!==e.strokeWidth||t.fontSize!==e.fontSize||t.fontFamily!==e.fontFamily||t.fontWeight!==e.fontWeight||t.fontStyle!==e.fontStyle||t.textBackgroundColor!==e.textBackgroundColor||t.deltaY!==e.deltaY||i&&(t.overline!==e.overline||t.underline!==e.underline||t.linethrough!==e.linethrough)},sP=(t,e)=>{let i=e.split("\n"),r=[],s=-1,n={};t=rU(t);for(let e=0;e<i.length;e++){let o=sE(i[e]);if(t[e])for(let i=0;i<o.length;i++){s++;let o=t[e][i];o&&Object.keys(o).length>0&&(sL(n,o,!0)?r.push({start:s,end:s+1,style:o}):r[r.length-1].end++),n=o||{}}else s+=o.length,n={}}return r},sF=(t,e)=>{if(!Array.isArray(t))return rU(t);let i=e.split(eE),r={},s=-1,n=0;for(let e=0;e<i.length;e++){let o=sE(i[e]);for(let i=0;i<o.length;i++)s++,t[n]&&t[n].start<=s&&s<t[n].end&&(r[e]=r[e]||{},r[e][i]=ee({},t[n].style),s===t[n].end-1&&n++)}return r},sR=["display","transform","fill","fill-opacity","fill-rule","opacity","stroke","stroke-dasharray","stroke-linecap","stroke-dashoffset","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","id","paint-order","vector-effect","instantiated_by_use","clip-path"];function sI(t,e){let i;let r=t.nodeName,s=t.getAttribute("class"),n=t.getAttribute("id"),o="(?![a-zA-Z\\-]+)";if(i=RegExp("^"+r,"i"),e=e.replace(i,""),n&&e.length&&(i=RegExp("#"+n+o,"i"),e=e.replace(i,"")),s&&e.length){let t=s.split(" ");for(let r=t.length;r--;)i=RegExp("\\."+t[r]+o,"i"),e=e.replace(i,"")}return 0===e.length}let sB=t=>{var e;return null!==(e=rV[t])&&void 0!==e?e:t},sN=RegExp("(".concat(rB,")"),"gi"),sV=t=>t.replace(sN," $1 ").replace(/,/gi," ").replace(/\s+/gi," "),sX="(".concat(rB,")"),sz=String.raw(b||(b=es(["(skewX)(",")"],["(skewX)\\(","\\)"])),sX),sW=String.raw(w||(w=es(["(skewY)(",")"],["(skewY)\\(","\\)"])),sX),sY=String.raw(C||(C=es(["(rotate)(","(?: "," ",")?)"],["(rotate)\\(","(?: "," ",")?\\)"])),sX,sX,sX),sH=String.raw(S||(S=es(["(scale)(","(?: ",")?)"],["(scale)\\(","(?: ",")?\\)"])),sX,sX),sG=String.raw(T||(T=es(["(translate)(","(?: ",")?)"],["(translate)\\(","(?: ",")?\\)"])),sX,sX),sU=String.raw(O||(O=es(["(matrix)("," "," "," "," "," ",")"],["(matrix)\\("," "," "," "," "," ","\\)"])),sX,sX,sX,sX,sX,sX),sZ="(?:".concat(sU,"|").concat(sG,"|").concat(sY,"|").concat(sH,"|").concat(sz,"|").concat(sW,")"),sq=new RegExp(String.raw(k||(k=es(["^s*(?:","?)s*$"],["^\\s*(?:","?)\\s*$"])),"(?:".concat(sZ,"*)"))),sK=new RegExp(sZ),sJ=RegExp(sZ,"g");function sQ(t){let e=[];if(!(t=sV(t).replace(/\s*([()])\s*/gi,"$1"))||t&&!sq.test(t))return[...eS];for(let i of t.matchAll(sJ)){let t=sK.exec(i[0]);if(!t)continue;let r=eS,[,s,...n]=t.filter(t=>!!t),[o,a,l,h,c,d]=n.map(t=>parseFloat(t));switch(s){case"translate":r=e8(o,a);break;case"rotate":r=e4({angle:o},{x:a,y:l});break;case"scale":r=e9(o,a);break;case"skewX":r=it(o);break;case"skewY":r=ie(o);break;case"matrix":r=[o,a,l,h,c,d]}e.push(r)}return e3(e)}let s$={stroke:"strokeOpacity",fill:"fillOpacity"};function s0(t,e,i){if(!t)return{};let r,s={},n=16;t.parentNode&&rW.test(t.parentNode.nodeName)&&(s=s0(t.parentElement,e,i)).fontSize&&(r=n=iy(s.fontSize));let o=ee(ee(ee({},e.reduce((e,i)=>{let r=t.getAttribute(i);return r&&(e[i]=r),e},{})),function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i={};for(let r in e)(function(t,e){let i=!0,r=sI(t,e.pop());return r&&e.length&&(i=function(t,e){let i,r=!0;for(;t.parentElement&&1===t.parentElement.nodeType&&e.length;)r&&(i=e.pop()),r=sI(t=t.parentElement,i);return 0===e.length}(t,e)),r&&i&&0===e.length})(t,r.split(" "))&&(i=ee(ee({},i),e[r]));return i}(t,i)),function(t){let e={},i=t.getAttribute("style");return i&&("string"==typeof i?function(t,e){t.replace(/;\s*$/,"").split(";").forEach(t=>{let[i,r]=t.split(":");e[i.trim().toLowerCase()]=r.trim()})}(i,e):function(t,e){Object.entries(t).forEach(t=>{let[i,r]=t;void 0!==r&&(e[i.toLowerCase()]=r)})}(i,e)),e}(t));o[rz]&&t.setAttribute(rz,o[rz]),o[rX]&&(r=iy(o[rX],n),o[rX]="".concat(r));let a={};for(let t in o){let e=sB(t),i=function(t,e,i,r){let s=Array.isArray(e),n,o=e;if("fill"!==t&&"stroke"!==t||e!==eM){if("strokeUniform"===t)return"non-scaling-stroke"===e;if("strokeDashArray"===t)o=e===eM?null:e.replace(/,/g," ").split(/\s+/).map(parseFloat);else if("transformMatrix"===t)o=i&&i.transformMatrix?e2(i.transformMatrix,sQ(e)):sQ(e);else if("visible"===t)o=e!==eM&&"hidden"!==e,i&&!1===i.visible&&(o=!1);else if("opacity"===t)o=parseFloat(e),i&&void 0!==i.opacity&&(o*=i.opacity);else if("textAnchor"===t)o="start"===e?eO:"end"===e?ej:eT;else if("charSpacing"===t)n=iy(e,r)/r*1e3;else if("paintFirst"===t){let t=e.indexOf("fill"),i=e.indexOf("stroke");o="fill",(t>-1&&i>-1&&i<t||-1===t&&i>-1)&&(o="stroke")}else{if("href"===t||"xlink:href"===t||"font"===t)return e;if("imageSmoothing"===t)return"optimizeQuality"===e;n=s?e.map(iy):iy(e,r)}}else o="";return!s&&isNaN(n)?o:n}(e,o[t],s,r);a[e]=i}a&&a.font&&function(t,e){let i=t.match(rN);if(!i)return;let r=i[1],s=i[3],n=i[4],o=i[5],a=i[6];r&&(e.fontStyle=r),s&&(e.fontWeight=isNaN(parseFloat(s))?s:parseFloat(s)),n&&(e.fontSize=iy(n)),a&&(e.fontFamily=a),o&&(e.lineHeight="normal"===o?1:o)}(a.font,a);let l=ee(ee({},s),a);return rW.test(t.nodeName)?l:function(t){let e=sw.getDefaults();return Object.entries(s$).forEach(i=>{let[r,s]=i;if(void 0===t[s]||""===t[r])return;if(void 0===t[r]){if(!e[r])return;t[r]=e[r]}if(0===t[r].indexOf("url("))return;let n=new im(t[r]);t[r]=n.setAlpha(ix(n.getAlpha()*t[s],2)).toRgba()}),t}(l)}let s1=["left","top","width","height","visible"],s2=["rx","ry"];class s3 extends sw{static getDefaults(){return ee(ee({},super.getDefaults()),s3.ownDefaults)}constructor(t){super(),Object.assign(this,s3.ownDefaults),this.setOptions(t),this._initRxRy()}_initRxRy(){let{rx:t,ry:e}=this;t&&!e?this.ry=t:e&&!t&&(this.rx=e)}_render(t){let{width:e,height:i}=this,r=-e/2,s=-i/2,n=this.rx?Math.min(this.rx,e/2):0,o=this.ry?Math.min(this.ry,i/2):0,a=0!==n||0!==o;t.beginPath(),t.moveTo(r+n,s),t.lineTo(r+e-n,s),a&&t.bezierCurveTo(r+e-.4477152502*n,s,r+e,s+.4477152502*o,r+e,s+o),t.lineTo(r+e,s+i-o),a&&t.bezierCurveTo(r+e,s+i-.4477152502*o,r+e-.4477152502*n,s+i,r+e-n,s+i),t.lineTo(r+n,s+i),a&&t.bezierCurveTo(r+.4477152502*n,s+i,r,s+i-.4477152502*o,r,s+i-o),t.lineTo(r,s+o),a&&t.bezierCurveTo(r,s+.4477152502*o,r+.4477152502*n,s,r+n,s),t.closePath(),this._renderPaintInOrder(t)}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return super.toObject([...s2,...t])}_toSVG(){let{width:t,height:e,rx:i,ry:r}=this;return["<rect ","COMMON_PARTS",'x="'.concat(-t/2,'" y="').concat(-e/2,'" rx="').concat(i,'" ry="').concat(r,'" width="').concat(t,'" height="').concat(e,'" />\n')]}static async fromElement(t,e,i){let r=s0(t,this.ATTRIBUTE_NAMES,i),{left:s=0,top:n=0,width:o=0,height:a=0,visible:l=!0}=r,h=er(r,s1);return new this(ee(ee(ee({},e),h),{},{left:s,top:n,width:o,height:a,visible:!!(l&&o&&a)}))}}ei(s3,"type","Rect"),ei(s3,"cacheProperties",[...rq,...s2]),ei(s3,"ownDefaults",{rx:0,ry:0}),ei(s3,"ATTRIBUTE_NAMES",[...sR,"x","y","rx","ry","width","height"]),eA.setClass(s3),eA.setSVGClass(s3);let s6="initialization",s5="added",s8="removed",s4="imperative",s9=(t,e)=>{let{strokeUniform:i,strokeWidth:r,width:s,height:n,group:o}=e,a=o&&o!==t?iq(o.calcTransformMatrix(),t.calcTransformMatrix()):null,l=a?e.getRelativeCenterPoint().transform(a):e.getRelativeCenterPoint(),h=!e.isStrokeAccountedForInDimensions(),c=i&&h?iJ(new eN(r,r),void 0,t.calcTransformMatrix()):eV,d=!i&&h?r:0,u=iZ(s+d,n+d,e3([a,e.calcOwnMatrix()],!0)).add(c).scalarDivide(2);return[l.subtract(u),l.add(u)]};class s7{calcLayoutResult(t,e){if(this.shouldPerformLayout(t))return this.calcBoundingBox(e,t)}shouldPerformLayout(t){return t.type===s6||t.type===s4||!!t.prevStrategy&&t.strategy!==t.prevStrategy}shouldLayoutClipPath(t){let{type:e,target:{clipPath:i}}=t;return e!==s6&&i&&!i.absolutePositioned}getInitialSize(t,e){return e.size}calcBoundingBox(t,e){if(e.type===s4&&e.overrides)return e.overrides;if(0===t.length)return;let{target:i}=e,{left:r,top:s,width:n,height:o}=iz(t.map(t=>s9(i,t)).reduce((t,e)=>t.concat(e),[])),a=new eN(n,o),l=new eN(r,s).add(a.scalarDivide(2));if(e.type===s6){let t=this.getInitialSize(e,{size:a,center:l});return{center:l,relativeCorrection:new eN(0,0),size:t}}return{center:l.transform(i.calcOwnMatrix()),size:a}}}ei(s7,"type","strategy");class nt extends s7{shouldPerformLayout(t){return!0}}ei(nt,"type","fit-content"),eA.setClass(nt);let ne=["strategy"],ni=["target","strategy","bubbles","prevStrategy"],nr="layoutManager";class ns{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new nt;ei(this,"strategy",void 0),this.strategy=t,this._subscriptions=new Map}performLayout(t){let e=ee(ee({bubbles:!0,strategy:this.strategy},t),{},{prevStrategy:this._prevLayoutStrategy,stopPropagation(){this.bubbles=!1}});this.onBeforeLayout(e);let i=this.getLayoutResult(e);i&&this.commitLayout(e,i),this.onAfterLayout(e,i),this._prevLayoutStrategy=e.strategy}attachHandlers(t,e){let{target:i}=e;return["modified","moving","resizing","rotating","scaling","skewing","changed","modifyPoly"].map(e=>t.on(e,t=>this.performLayout("modified"===e?{type:"object_modified",trigger:e,e:t,target:i}:{type:"object_modifying",trigger:e,e:t,target:i})))}subscribe(t,e){this.unsubscribe(t,e);let i=this.attachHandlers(t,e);this._subscriptions.set(t,i)}unsubscribe(t,e){(this._subscriptions.get(t)||[]).forEach(t=>t()),this._subscriptions.delete(t)}unsubscribeTargets(t){t.targets.forEach(e=>this.unsubscribe(e,t))}subscribeTargets(t){t.targets.forEach(e=>this.subscribe(e,t))}onBeforeLayout(t){let{target:e,type:i}=t,{canvas:r}=e;if(i===s6||i===s5?this.subscribeTargets(t):i===s8&&this.unsubscribeTargets(t),e.fire("layout:before",{context:t}),r&&r.fire("object:layout:before",{target:e,context:t}),i===s4&&t.deep){let i=er(t,ne);e.forEachObject(t=>t.layoutManager&&t.layoutManager.performLayout(ee(ee({},i),{},{bubbles:!1,target:t})))}}getLayoutResult(t){let{target:e}=t,i=t.strategy.calcLayoutResult(t,e.getObjects());if(!i)return;let r=t.type===s6?new eN:e.getRelativeCenterPoint(),{center:s,correction:n=new eN,relativeCorrection:o=new eN}=i,a=r.subtract(s).add(n).transform(t.type===s6?eS:e1(e.calcOwnMatrix()),!0).add(o);return{result:i,prevCenter:r,nextCenter:s,offset:a}}commitLayout(t,e){var i,r;let{target:s}=t,{result:{size:n},nextCenter:o}=e;(s.set({width:n.x,height:n.y}),this.layoutObjects(t,e),t.type===s6)?s.set({left:null!==(i=t.x)&&void 0!==i?i:o.x+n.x*i1(s.originX),top:null!==(r=t.y)&&void 0!==r?r:o.y+n.y*i1(s.originY)}):(s.setPositionByOrigin(o,eT,eT),s.setCoords(),s.set("dirty",!0))}layoutObjects(t,e){let{target:i}=t;i.forEachObject(r=>{r.group===i&&this.layoutObject(t,e,r)}),t.strategy.shouldLayoutClipPath(t)&&this.layoutObject(t,e,i.clipPath)}layoutObject(t,e,i){let{offset:r}=e;i.set({left:i.left+r.x,top:i.top+r.y})}onAfterLayout(t,e){let{target:i,strategy:r,bubbles:s,prevStrategy:n}=t,o=er(t,ni),{canvas:a}=i;i.fire("layout:after",{context:t,result:e}),a&&a.fire("object:layout:after",{context:t,result:e,target:i});let l=i.parent;s&&null!=l&&l.layoutManager&&((o.path||(o.path=[])).push(i),l.layoutManager.performLayout(ee(ee({},o),{},{target:l}))),i.set("dirty",!0)}dispose(){this._subscriptions.forEach(t=>t.forEach(t=>t())),this._subscriptions.clear()}toObject(){return{type:nr,strategy:this.strategy.constructor.type}}toJSON(){return this.toObject()}}eA.setClass(ns,nr);let nn=["type","objects","layoutManager"];class no extends ns{performLayout(){}}class na extends ez(sw){static getDefaults(){return ee(ee({},super.getDefaults()),na.ownDefaults)}constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),ei(this,"_activeObjects",[]),ei(this,"__objectSelectionTracker",void 0),ei(this,"__objectSelectionDisposer",void 0),Object.assign(this,na.ownDefaults),this.setOptions(e),this._objects=[...t],this.__objectSelectionTracker=this.__objectSelectionMonitor.bind(this,!0),this.__objectSelectionDisposer=this.__objectSelectionMonitor.bind(this,!1),this.forEachObject(t=>{this.enterGroup(t,!1)}),this.layoutManager=e.layoutManager||new ns,this.layoutManager.performLayout({type:s6,target:this,targets:[...t],x:e.left,y:e.top})}canEnterGroup(t){return t===this||this.isDescendantOf(t)?(el("error","Group: circular object trees are not supported, this call has no effect"),!1):-1===this._objects.indexOf(t)||(el("error","Group: duplicate objects are not supported inside group, this call has no effect"),!1)}_filterObjectsBeforeEnteringGroup(t){return t.filter((t,e,i)=>this.canEnterGroup(t)&&i.indexOf(t)===e)}add(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];let r=this._filterObjectsBeforeEnteringGroup(e),s=super.add(...r);return this._onAfterObjectsChange(s5,r),s}insertAt(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];let s=this._filterObjectsBeforeEnteringGroup(i),n=super.insertAt(t,...s);return this._onAfterObjectsChange(s5,s),n}remove(){let t=super.remove(...arguments);return this._onAfterObjectsChange(s8,t),t}_onObjectAdded(t){this.enterGroup(t,!0),this.fire("object:added",{target:t}),t.fire("added",{target:this})}_onObjectRemoved(t,e){this.exitGroup(t,e),this.fire("object:removed",{target:t}),t.fire("removed",{target:this})}_onAfterObjectsChange(t,e){this.layoutManager.performLayout({type:t,targets:e,target:this})}_onStackOrderChanged(){this._set("dirty",!0)}_set(t,e){let i=this[t];return super._set(t,e),"canvas"===t&&i!==e&&(this._objects||[]).forEach(i=>{i._set(t,e)}),this}_shouldSetNestedCoords(){return this.subTargetCheck}removeAll(){return this._activeObjects=[],this.remove(...this._objects)}__objectSelectionMonitor(t,e){let{target:i}=e,r=this._activeObjects;if(t)r.push(i),this._set("dirty",!0);else if(r.length>0){let t=r.indexOf(i);t>-1&&(r.splice(t,1),this._set("dirty",!0))}}_watchObject(t,e){t&&this._watchObject(!1,e),t?(e.on("selected",this.__objectSelectionTracker),e.on("deselected",this.__objectSelectionDisposer)):(e.off("selected",this.__objectSelectionTracker),e.off("deselected",this.__objectSelectionDisposer))}enterGroup(t,e){t.group&&t.group.remove(t),t._set("parent",this),this._enterGroup(t,e)}_enterGroup(t,e){e&&iH(t,e2(e1(this.calcTransformMatrix()),t.calcTransformMatrix())),this._shouldSetNestedCoords()&&t.setCoords(),t._set("group",this),t._set("canvas",this.canvas),this._watchObject(!0,t);let i=this.canvas&&this.canvas.getActiveObject&&this.canvas.getActiveObject();i&&(i===t||t.isDescendantOf(i))&&this._activeObjects.push(t)}exitGroup(t,e){this._exitGroup(t,e),t._set("parent",void 0),t._set("canvas",void 0)}_exitGroup(t,e){t._set("group",void 0),e||(iH(t,e2(this.calcTransformMatrix(),t.calcTransformMatrix())),t.setCoords()),this._watchObject(!1,t);let i=this._activeObjects.length>0?this._activeObjects.indexOf(t):-1;i>-1&&this._activeObjects.splice(i,1)}shouldCache(){let t=sw.prototype.shouldCache.call(this);if(t){for(let t=0;t<this._objects.length;t++)if(this._objects[t].willDrawShadow())return this.ownCaching=!1,!1}return t}willDrawShadow(){if(super.willDrawShadow())return!0;for(let t=0;t<this._objects.length;t++)if(this._objects[t].willDrawShadow())return!0;return!1}isOnACache(){return this.ownCaching||!!this.parent&&this.parent.isOnACache()}drawObject(t){this._renderBackground(t);for(let i=0;i<this._objects.length;i++){var e;null!==(e=this.canvas)&&void 0!==e&&e.preserveObjectStacking&&this._objects[i].group!==this?(t.save(),t.transform(...e1(this.calcTransformMatrix())),this._objects[i].render(t),t.restore()):this._objects[i].group===this&&this._objects[i].render(t)}this._drawClipPath(t,this.clipPath)}setCoords(){super.setCoords(),this._shouldSetNestedCoords()&&this.forEachObject(t=>t.setCoords())}triggerLayout(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.layoutManager.performLayout(ee({target:this,type:s4},t))}render(t){this._transformDone=!0,super.render(t),this._transformDone=!1}__serializeObjects(t,e){let i=this.includeDefaultValues;return this._objects.filter(function(t){return!t.excludeFromExport}).map(function(r){let s=r.includeDefaultValues;r.includeDefaultValues=i;let n=r[t||"toObject"](e);return r.includeDefaultValues=s,n})}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=this.layoutManager.toObject();return ee(ee(ee({},super.toObject(["subTargetCheck","interactive",...t])),"fit-content"!==e.strategy||this.includeDefaultValues?{layoutManager:e}:{}),{},{objects:this.__serializeObjects("toObject",t)})}toString(){return"#<Group: (".concat(this.complexity(),")>")}dispose(){this.layoutManager.unsubscribeTargets({targets:this.getObjects(),target:this}),this._activeObjects=[],this.forEachObject(t=>{this._watchObject(!1,t),t.dispose()}),super.dispose()}_createSVGBgRect(t){if(!this.backgroundColor)return"";let e=s3.prototype._toSVG.call(this),i=e.indexOf("COMMON_PARTS");e[i]='for="group" ';let r=e.join("");return t?t(r):r}_toSVG(t){let e=["<g ","COMMON_PARTS"," >\n"],i=this._createSVGBgRect(t);i&&e.push("		",i);for(let i=0;i<this._objects.length;i++)e.push("		",this._objects[i].toSVG(t));return e.push("</g>\n"),e}getSvgStyles(){let t=void 0!==this.opacity&&1!==this.opacity?"opacity: ".concat(this.opacity,";"):"",e=this.visible?"":" visibility: hidden;";return[t,this.getSvgFilter(),e].join("")}toClipPathSVG(t){let e=[],i=this._createSVGBgRect(t);i&&e.push("	",i);for(let i=0;i<this._objects.length;i++)e.push("	",this._objects[i].toClipPathSVG(t));return this._createBaseClipPathSVGMarkup(e,{reviver:t})}static fromObject(t,e){let{type:i,objects:r=[],layoutManager:s}=t,n=er(t,nn);return Promise.all([io(r,e),ia(n,e)]).then(t=>{let[e,i]=t,r=new this(e,ee(ee(ee({},n),i),{},{layoutManager:new no}));if(s){let t=eA.getClass(s.type),e=eA.getClass(s.strategy);r.layoutManager=new t(new e)}else r.layoutManager=new ns;return r.layoutManager.subscribeTargets({type:s6,target:r,targets:r.getObjects()}),r.setCoords(),r})}}ei(na,"type","Group"),ei(na,"ownDefaults",{strokeWidth:0,subTargetCheck:!1,interactive:!1}),eA.setClass(na);let nl=(t,e)=>Math.min(e.width/t.width,e.height/t.height),nh=(t,e)=>Math.max(e.width/t.width,e.height/t.height),nc="(".concat(rB,")"),nd="(M) (?:".concat(nc," ").concat(nc," ?)+"),nu="(L) (?:".concat(nc," ").concat(nc," ?)+"),ng="(H) (?:".concat(nc," ?)+"),nf="(V) (?:".concat(nc," ?)+"),np=String.raw(j||(j=es(["(Z)s*"],["(Z)\\s*"]))),nv="(C) (?:".concat(nc," ").concat(nc," ").concat(nc," ").concat(nc," ").concat(nc," ").concat(nc," ?)+"),nm="(S) (?:".concat(nc," ").concat(nc," ").concat(nc," ").concat(nc," ?)+"),nx="(Q) (?:".concat(nc," ").concat(nc," ").concat(nc," ").concat(nc," ?)+"),ny="(T) (?:".concat(nc," ").concat(nc," ?)+"),n_="(A) (?:".concat(nc," ").concat(nc," ").concat(nc," ([01]) ?([01]) ").concat(nc," ").concat(nc," ?)+"),nb="(?:(?:".concat(nd,")")+"|(?:".concat(nu,")")+"|(?:".concat(ng,")")+"|(?:".concat(nf,")")+"|(?:".concat(np,")")+"|(?:".concat(nv,")")+"|(?:".concat(nm,")")+"|(?:".concat(nx,")")+"|(?:".concat(ny,")")+"|(?:".concat(n_,"))"),nw={m:"l",M:"L"},nC=(t,e,i,r,s,n,o,a,l,h,c)=>{let d=eI(t),u=eB(t),g=eI(e),f=eB(e),p=i*s*g-r*n*f+o,v=r*s*g+i*n*f+a;return["C",h+l*(-i*s*u-r*n*d),c+l*(-r*s*u+i*n*d),p+l*(i*s*f+r*n*g),v+l*(r*s*f-i*n*g),p,v]},nS=(t,e,i,r)=>{let s=Math.atan2(e,t),n=Math.atan2(r,i);return n>=s?n-s:2*Math.PI-(s-n)};function nT(t,e,i,r,s,n,o,a){let l;if(ea.cachesBoundsOfCurve&&(l=[...arguments].join(),ex.boundsOfCurveCache[l]))return ex.boundsOfCurveCache[l];let h=Math.sqrt,c=Math.abs,d=[],u=[[0,0],[0,0]],g=6*t-12*i+6*s,f=-3*t+9*i-9*s+3*o,p=3*i-3*t;for(let t=0;t<2;++t){if(t>0&&(g=6*e-12*r+6*n,f=-3*e+9*r-9*n+3*a,p=3*r-3*e),1e-12>c(f)){if(1e-12>c(g))continue;let t=-p/g;0<t&&t<1&&d.push(t);continue}let i=g*g-4*p*f;if(i<0)continue;let s=h(i),o=(-g+s)/(2*f);0<o&&o<1&&d.push(o);let l=(-g-s)/(2*f);0<l&&l<1&&d.push(l)}let v=d.length,m=v,x=nM(t,e,i,r,s,n,o,a);for(;v--;){let{x:t,y:e}=x(d[v]);u[0][v]=t,u[1][v]=e}u[0][m]=t,u[1][m]=e,u[0][m+1]=o,u[1][m+1]=a;let y=[new eN(Math.min(...u[0]),Math.min(...u[1])),new eN(Math.max(...u[0]),Math.max(...u[1]))];return ea.cachesBoundsOfCurve&&(ex.boundsOfCurveCache[l]=y),y}let nO=(t,e,i)=>{let[r,s,n,o,a,l,h,c]=i,d=((t,e,i,r,s,n,o)=>{if(0===i||0===r)return[];let a=0,l=0,h=0,c=Math.PI,d=o*eC,u=eB(d),g=eI(d),f=.5*(-g*t-u*e),p=.5*(-g*e+u*t),v=i**2,m=r**2,x=p**2,y=f**2,_=v*m-v*x-m*y,b=Math.abs(i),w=Math.abs(r);if(_<0){let t=Math.sqrt(1-_/(v*m));b*=t,w*=t}else h=(s===n?-1:1)*Math.sqrt(_/(v*x+m*y));let C=h*b*p/w,S=-h*w*f/b,T=g*C-u*S+.5*t,O=u*C+g*S+.5*e,k=nS(1,0,(f-C)/b,(p-S)/w),j=nS((f-C)/b,(p-S)/w,(-f-C)/b,(-p-S)/w);0===n&&j>0?j-=2*c:1===n&&j<0&&(j+=2*c);let M=Math.ceil(Math.abs(j/c*2)),E=Array(M),D=j/M,A=8/3*Math.sin(D/4)*Math.sin(D/4)/Math.sin(D/2),L=k+D;for(let t=0;t<M;t++)E[t]=nC(k,L,g,u,b,w,T,O,A,a,l),a=E[t][5],l=E[t][6],k=L,L+=D;return E})(h-t,c-e,s,n,a,l,o);for(let i=0,r=d.length;i<r;i++)d[i][1]+=t,d[i][2]+=e,d[i][3]+=t,d[i][4]+=e,d[i][5]+=t,d[i][6]+=e;return d},nk=t=>{let e=0,i=0,r=0,s=0,n=[],o,a=0,l=0;for(let h of t){let t;let c=[...h];switch(c[0]){case"l":c[1]+=e,c[2]+=i;case"L":e=c[1],t=["L",e,i=c[2]];break;case"h":c[1]+=e;case"H":t=["L",e=c[1],i];break;case"v":c[1]+=i;case"V":t=["L",e,i=c[1]];break;case"m":c[1]+=e,c[2]+=i;case"M":e=c[1],i=c[2],r=c[1],s=c[2],t=["M",e,i];break;case"c":c[1]+=e,c[2]+=i,c[3]+=e,c[4]+=i,c[5]+=e,c[6]+=i;case"C":a=c[3],l=c[4],e=c[5],i=c[6],t=["C",c[1],c[2],a,l,e,i];break;case"s":c[1]+=e,c[2]+=i,c[3]+=e,c[4]+=i;case"S":"C"===o?(a=2*e-a,l=2*i-l):(a=e,l=i),e=c[3],i=c[4],a=(t=["C",a,l,c[1],c[2],e,i])[3],l=t[4];break;case"q":c[1]+=e,c[2]+=i,c[3]+=e,c[4]+=i;case"Q":a=c[1],l=c[2],e=c[3],t=["Q",a,l,e,i=c[4]];break;case"t":c[1]+=e,c[2]+=i;case"T":"Q"===o?(a=2*e-a,l=2*i-l):(a=e,l=i),e=c[1],t=["Q",a,l,e,i=c[2]];break;case"a":c[6]+=e,c[7]+=i;case"A":nO(e,i,c).forEach(t=>n.push(t)),e=c[6],i=c[7];break;case"z":case"Z":e=r,i=s,t=["Z"]}t?(n.push(t),o=t[0]):o=""}return n},nj=(t,e,i,r)=>Math.sqrt((i-t)**2+(r-e)**2),nM=(t,e,i,r,s,n,o,a)=>l=>{let h,c;let d=l**3,u=3*(h=l)**2*(1-h),g=3*(c=l)*(1-c)**2,f=(1-l)**3;return new eN(o*d+s*u+i*g+t*f,a*d+n*u+r*g+e*f)},nE=t=>t**2,nD=t=>2*t*(1-t),nA=t=>(1-t)**2,nL=(t,e,i,r,s,n,o,a)=>l=>{let h=nE(l),c=nD(l),d=nA(l);return Math.atan2(3*(d*(r-e)+c*(n-r)+h*(a-n)),3*(d*(i-t)+c*(s-i)+h*(o-s)))},nP=(t,e,i,r,s,n)=>o=>{let a=nE(o),l=nD(o),h=nA(o);return new eN(s*a+i*l+t*h,n*a+r*l+e*h)},nF=(t,e,i,r,s,n)=>o=>{let a=1-o;return Math.atan2(2*(a*(r-e)+o*(n-r)),2*(a*(i-t)+o*(s-i)))},nR=(t,e,i)=>{let r=new eN(e,i),s=0;for(let e=1;e<=100;e+=1){let i=t(e/100);s+=nj(r.x,r.y,i.x,i.y),r=i}return s},nI=(t,e)=>{let i,r=0,s=0,n={x:t.x,y:t.y},o=ee({},n),a=.01,l=0,h=t.iterator,c=t.angleFinder;for(;s<e&&a>1e-4;)o=h(r),l=r,(i=nj(n.x,n.y,o.x,o.y))+s>e?(r-=a,a/=2):(n=o,r+=a,s+=i);return ee(ee({},o),{},{angle:c(l)})},nB=t=>{let e,i,r=0,s=0,n=0,o=0,a=0,l=[];for(let h of t){let t={x:s,y:n,command:h[0],length:0};switch(h[0]){case"M":(i=t).x=o=s=h[1],i.y=a=n=h[2];break;case"L":(i=t).length=nj(s,n,h[1],h[2]),s=h[1],n=h[2];break;case"C":e=nM(s,n,h[1],h[2],h[3],h[4],h[5],h[6]),(i=t).iterator=e,i.angleFinder=nL(s,n,h[1],h[2],h[3],h[4],h[5],h[6]),i.length=nR(e,s,n),s=h[5],n=h[6];break;case"Q":e=nP(s,n,h[1],h[2],h[3],h[4]),(i=t).iterator=e,i.angleFinder=nF(s,n,h[1],h[2],h[3],h[4]),i.length=nR(e,s,n),s=h[3],n=h[4];break;case"Z":(i=t).destX=o,i.destY=a,i.length=nj(s,n,o,a),s=o,n=a}r+=i.length,l.push(i)}return l.push({length:r,x:s,y:n}),l},nN=function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:nB(t),r=0;for(;e-i[r].length>0&&r<i.length-2;)e-=i[r].length,r++;let s=i[r],n=e/s.length,o=t[r];switch(s.command){case"M":return{x:s.x,y:s.y,angle:0};case"Z":return ee(ee({},new eN(s.x,s.y).lerp(new eN(s.destX,s.destY),n)),{},{angle:Math.atan2(s.destY-s.y,s.destX-s.x)});case"L":return ee(ee({},new eN(s.x,s.y).lerp(new eN(o[1],o[2]),n)),{},{angle:Math.atan2(o[2]-s.y,o[1]-s.x)});case"C":case"Q":return nI(s,e)}},nV=RegExp(nb,"gi"),nX=RegExp(nb,"i"),nz=t=>{t=sV(t);let e=[];for(let[i]of t.matchAll(nV)){let t;let r=[];do{if(!(t=nX.exec(i)))break;let e=t.filter(t=>t);e.shift();let s=e.map(t=>{let e=Number.parseFloat(t);return Number.isNaN(e)?t:e});if(r.push(s),e.length<=1)break;e.shift(),i=i.replace(new RegExp("".concat(e.join(" ?")," ?$")),"")}while(t);r.reverse().forEach((t,i)=>{let r=nw[t[0]];i>0&&("l"==r||"L"==r)&&(t[0]=r),e.push(t)})}return e},nW=function(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=new eN(t[0]),s=new eN(t[1]),n=1,o=0,a=[],l=t.length,h=l>2;for(h&&(n=t[2].x<s.x?-1:t[2].x===s.x?0:1,o=t[2].y<s.y?-1:t[2].y===s.y?0:1),a.push(["M",r.x-n*i,r.y-o*i]),e=1;e<l;e++){if(!r.eq(s)){let t=r.midPointFrom(s);a.push(["Q",r.x,r.y,t.x,t.y])}r=t[e],e+1<t.length&&(s=t[e+1])}return h&&(n=r.x>t[e-2].x?1:r.x===t[e-2].x?0:-1,o=r.y>t[e-2].y?1:r.y===t[e-2].y?0:-1),a.push(["L",r.x+n*i,r.y+o*i]),a},nY=(t,e)=>t.map(t=>t.map((t,i)=>0===i||void 0===e?t:ix(t,e)).join(" ")).join(" ");Object.freeze({__proto__:null,addTransformToObject:iY,animate:rx,animateColor:ry,applyTransformToObject:iH,calcAngleBetweenVectors:rT,calcDimensionsMatrix:ii,calcPlaneChangeMatrix:iq,calcVectorRotation:rO,cancelAnimFrame:eH,capValue:rg,composeMatrix:ir,copyCanvasElement:t=>{var e;let i=eZ();return i.width=t.width,i.height=t.height,null===(e=i.getContext("2d"))||void 0===e||e.drawImage(t,0,0),i},cos:eI,createCanvasElement:eZ,createImage:eq,createRotateMatrix:e4,createScaleMatrix:e9,createSkewXMatrix:it,createSkewYMatrix:ie,createTranslateMatrix:e8,createVector:rC,crossProduct:rM,degreesToRadians:eJ,dotProduct:rE,ease:ro,enlivenObjectEnlivables:ia,enlivenObjects:io,findScaleToCover:nh,findScaleToFit:nl,getBoundsOfCurve:nT,getOrthonormalVector:rj,getPathSegmentsInfo:nB,getPointOnPath:nN,getPointer:iN,getRandomInt:(t,e)=>Math.floor(Math.random()*(e-t+1))+t,getRegularPolygonPath:(t,e)=>{let i=2*Math.PI/t,r=-eb;t%2==0&&(r+=i/2);let s=Array(t+1);for(let n=0;n<t;n++){let t=n*i+r,{x:o,y:a}=new eN(eI(t),eB(t)).scalarMultiply(e);s[n]=[0===n?"M":"L",o,a]}return s[t]=["Z"],s},getSmoothPathFromPoints:nW,getSvgAttributes:t=>{let e=["instantiated_by_use","style","id","class"];switch(t){case"linearGradient":return e.concat(["x1","y1","x2","y2","gradientUnits","gradientTransform"]);case"radialGradient":return e.concat(["gradientUnits","gradientTransform","cx","cy","r","fx","fy","fr"]);case"stop":return e.concat(["offset","stop-color","stop-opacity"])}return e},getUnitVector:rk,groupSVGElements:(t,e)=>t&&1===t.length?t[0]:new na(t,e),hasStyleChanged:sL,invertTransform:e1,isBetweenVectors:rD,isIdentityMatrix:e$,isTouchEvent:iV,isTransparent:sC,joinPath:nY,loadImage:is,magnitude:rS,makeBoundingBoxFromPoints:iz,makePathSimpler:nk,matrixToSVG:ib,mergeClipPaths:(t,e)=>{var i;let r=t,s=e;r.inverted&&!s.inverted&&(r=e,s=t),iQ(s,null===(i=s.group)||void 0===i?void 0:i.calcTransformMatrix(),r.calcTransformMatrix());let n=r.inverted&&s.inverted;return n&&(r.inverted=s.inverted=!1),new na([r],{clipPath:s,inverted:n})},multiplyTransformMatrices:e2,multiplyTransformMatrixArray:e3,parsePath:nz,parsePreserveAspectRatioAttribute:i_,parseUnit:iy,pick:il,projectStrokeOnPoints:sj,qrDecompose:e5,radiansToDegrees:eQ,removeFromArray:eR,removeTransformFromObject:(t,e)=>{let i=e2(e1(e),t.calcOwnMatrix());iH(t,i)},removeTransformMatrixForSvgParsing:(t,e)=>{let i=t._findCenterFromElement();t.transformMatrix&&((t=>{if(t.transformMatrix){let{scaleX:e,scaleY:i,angle:r,skewX:s}=e5(t.transformMatrix);t.flipX=!1,t.flipY=!1,t.set("scaleX",e),t.set("scaleY",i),t.angle=r,t.skewX=s,t.skewY=0}})(t),i=i.transform(t.transformMatrix)),delete t.transformMatrix,e&&(t.scaleX*=e.scaleX,t.scaleY*=e.scaleY,t.cropX=e.cropX,t.cropY=e.cropY,i.x+=e.offsetLeft,i.y+=e.offsetTop,t.width=e.width,t.height=e.height),t.setPositionByOrigin(i,eT,eT)},request:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.onComplete||e_,r=new(ev()).XMLHttpRequest,s=e.signal,n=function(){r.abort()},o=function(){s&&s.removeEventListener("abort",n),r.onerror=r.ontimeout=e_};if(s&&s.aborted)throw new ec("request");return s&&s.addEventListener("abort",n,{once:!0}),r.onreadystatechange=function(){4===r.readyState&&(o(),i(r),r.onreadystatechange=e_)},r.onerror=r.ontimeout=o,r.open("get",t,!0),r.send(),r},requestAnimFrame:eY,resetObjectTransform:iG,rotatePoint:(t,e,i)=>t.rotate(i,e),rotateVector:rw,saveObjectTransform:iU,sendObjectToPlane:iQ,sendPointToPlane:iK,sendVectorToPlane:iJ,setStyle:iD,sin:eB,sizeAfterTransform:iZ,string:sA,stylesFromArray:sF,stylesToArray:sP,toDataURL:eK,toFixed:ix,transformPath:(t,e,i)=>(i&&(e=e2(e,[1,0,0,1,-i.x,-i.y])),t.map(t=>{let i=[...t];for(let r=1;r<t.length-1;r+=2){let{x:s,y:n}=e0({x:t[r],y:t[r+1]},e);i[r]=s,i[r+1]=n}return i})),transformPoint:e0});class nH extends iF{constructor(t){let{allowTouchScrolling:e=!1,containerClass:i=""}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(t),ei(this,"upper",void 0),ei(this,"container",void 0);let{el:r}=this.lower,s=this.createUpperCanvas();this.upper={el:s,ctx:s.getContext("2d")},this.applyCanvasStyle(r,{allowTouchScrolling:e}),this.applyCanvasStyle(s,{allowTouchScrolling:e});let n=this.createContainerElement();n.classList.add(i),r.parentNode&&r.parentNode.replaceChild(n,r),n.append(r,s),this.container=n}createUpperCanvas(){let{el:t}=this.lower,e=eZ();return e.className=t.className,e.classList.remove("lower-canvas"),e.classList.add("upper-canvas"),e.setAttribute("data-fabric","top"),e.style.cssText=t.style.cssText,e.setAttribute("draggable","true"),e}createContainerElement(){let t=ep().createElement("div");return t.setAttribute("data-fabric","wrapper"),iD(t,{position:"relative"}),iP(t),t}applyCanvasStyle(t,e){let{allowTouchScrolling:i}=e;iD(t,{position:"absolute",left:"0",top:"0"}),function(t,e){let i=e?"manipulation":eM;iD(t,{"touch-action":i,"-ms-touch-action":i})}(t,i),iP(t)}setDimensions(t,e){super.setDimensions(t,e);let{el:i,ctx:r}=this.upper;iA(i,r,t,e)}setCSSDimensions(t){super.setCSSDimensions(t),iL(this.upper.el,t),iL(this.container,t)}cleanupDOM(t){let e=this.container,{el:i}=this.lower,{el:r}=this.upper;super.cleanupDOM(t),e.removeChild(r),e.removeChild(i),e.parentNode&&e.parentNode.replaceChild(i,e)}dispose(){super.dispose(),ef().dispose(this.upper.el),delete this.upper,delete this.container}}class nG extends iI{constructor(){super(...arguments),ei(this,"targets",[]),ei(this,"_hoveredTargets",[]),ei(this,"_objectsToRender",void 0),ei(this,"_currentTransform",null),ei(this,"_groupSelector",null),ei(this,"contextTopDirty",!1)}static getDefaults(){return ee(ee({},super.getDefaults()),nG.ownDefaults)}get upperCanvasEl(){var t;return null===(t=this.elements.upper)||void 0===t?void 0:t.el}get contextTop(){var t;return null===(t=this.elements.upper)||void 0===t?void 0:t.ctx}get wrapperEl(){return this.elements.container}initElements(t){this.elements=new nH(t,{allowTouchScrolling:this.allowTouchScrolling,containerClass:this.containerClass}),this._createCacheCanvas()}_onObjectAdded(t){this._objectsToRender=void 0,super._onObjectAdded(t)}_onObjectRemoved(t){this._objectsToRender=void 0,t===this._activeObject&&(this.fire("before:selection:cleared",{deselected:[t]}),this._discardActiveObject(),this.fire("selection:cleared",{deselected:[t]}),t.fire("deselected",{target:t})),t===this._hoveredTarget&&(this._hoveredTarget=void 0,this._hoveredTargets=[]),super._onObjectRemoved(t)}_onStackOrderChanged(){this._objectsToRender=void 0,super._onStackOrderChanged()}_chooseObjectsToRender(){let t=this._activeObject;return!this.preserveObjectStacking&&t?this._objects.filter(e=>!e.group&&e!==t).concat(t):this._objects}renderAll(){this.cancelRequestedRender(),this.destroyed||(!this.contextTopDirty||this._groupSelector||this.isDrawingMode||(this.clearContext(this.contextTop),this.contextTopDirty=!1),this.hasLostContext&&(this.renderTopLayer(this.contextTop),this.hasLostContext=!1),this._objectsToRender||(this._objectsToRender=this._chooseObjectsToRender()),this.renderCanvas(this.getContext(),this._objectsToRender))}renderTopLayer(t){t.save(),this.isDrawingMode&&this._isCurrentlyDrawing&&(this.freeDrawingBrush&&this.freeDrawingBrush._render(),this.contextTopDirty=!0),this.selection&&this._groupSelector&&(this._drawSelection(t),this.contextTopDirty=!0),t.restore()}renderTop(){let t=this.contextTop;this.clearContext(t),this.renderTopLayer(t),this.fire("after:render",{ctx:t})}setTargetFindTolerance(t){t=Math.round(t),this.targetFindTolerance=t;let e=this.getRetinaScaling(),i=Math.ceil((2*t+1)*e);this.pixelFindCanvasEl.width=this.pixelFindCanvasEl.height=i,this.pixelFindContext.scale(e,e)}isTargetTransparent(t,e,i){let r=this.targetFindTolerance,s=this.pixelFindContext;this.clearContext(s),s.save(),s.translate(-e+r,-i+r),s.transform(...this.viewportTransform);let n=t.selectionBackgroundColor;t.selectionBackgroundColor="",t.render(s),t.selectionBackgroundColor=n,s.restore();let o=Math.round(r*this.getRetinaScaling());return sC(s,o,o,o)}_isSelectionKeyPressed(t){let e=this.selectionKey;return!!e&&(Array.isArray(e)?!!e.find(e=>!!e&&!0===t[e]):t[e])}_shouldClearSelection(t,e){let i=this.getActiveObjects(),r=this._activeObject;return!!(!e||e&&r&&i.length>1&&-1===i.indexOf(e)&&r!==e&&!this._isSelectionKeyPressed(t)||e&&!e.evented||e&&!e.selectable&&r&&r!==e)}_shouldCenterTransform(t,e,i){let r;if(t)return"scale"===e||"scaleX"===e||"scaleY"===e||"resizing"===e?r=this.centeredScaling||t.centeredScaling:"rotate"===e&&(r=this.centeredRotation||t.centeredRotation),r?!i:i}_getOriginFromCorner(t,e){let i={x:t.originX,y:t.originY};return e&&(["ml","tl","bl"].includes(e)?i.x=ej:["mr","tr","br"].includes(e)&&(i.x=eO),["tl","mt","tr"].includes(e)?i.y=ek:["bl","mb","br"].includes(e)&&(i.y="top")),i}_setupCurrentTransform(t,e,i){var r;let s=e.group?iK(this.getScenePoint(t),void 0,e.group.calcTransformMatrix()):this.getScenePoint(t),{key:n="",control:o}=e.getActiveControl()||{},a=i&&o?null===(r=o.getActionHandler(t,e,o))||void 0===r?void 0:r.bind(o):i7,l=((t,e,i,r)=>{if(!e||!t)return"drag";let s=r.controls[e];return s.getActionName(i,s,r)})(i,n,t,e),h=t[this.centeredKey],c=this._shouldCenterTransform(e,l,h)?{x:eT,y:eT}:this._getOriginFromCorner(e,n),d={target:e,action:l,actionHandler:a,actionPerformed:!1,corner:n,scaleX:e.scaleX,scaleY:e.scaleY,skewX:e.skewX,skewY:e.skewY,offsetX:s.x-e.left,offsetY:s.y-e.top,originX:c.x,originY:c.y,ex:s.x,ey:s.y,lastX:s.x,lastY:s.y,theta:eJ(e.angle),width:e.width,height:e.height,shiftKey:t.shiftKey,altKey:h,original:ee(ee({},iU(e)),{},{originX:c.x,originY:c.y})};this._currentTransform=d,this.fire("before:transform",{e:t,transform:d})}setCursor(t){this.upperCanvasEl.style.cursor=t}_drawSelection(t){let{x:e,y:i,deltaX:r,deltaY:s}=this._groupSelector,n=new eN(e,i).transform(this.viewportTransform),o=new eN(e+r,i+s).transform(this.viewportTransform),a=this.selectionLineWidth/2,l=Math.min(n.x,o.x),h=Math.min(n.y,o.y),c=Math.max(n.x,o.x),d=Math.max(n.y,o.y);this.selectionColor&&(t.fillStyle=this.selectionColor,t.fillRect(l,h,c-l,d-h)),this.selectionLineWidth&&this.selectionBorderColor&&(t.lineWidth=this.selectionLineWidth,t.strokeStyle=this.selectionBorderColor,l+=a,h+=a,c-=a,d-=a,sw.prototype._setLineDash.call(this,t,this.selectionDashArray),t.strokeRect(l,h,c-l,d-h))}findTarget(t){if(this.skipTargetFind)return;let e=this.getViewportPoint(t),i=this._activeObject,r=this.getActiveObjects();if(this.targets=[],i&&r.length>=1){if(i.findControl(e,iV(t))||r.length>1&&this.searchPossibleTargets([i],e))return i;if(i===this.searchPossibleTargets([i],e)){if(this.preserveObjectStacking){let r=this.targets;this.targets=[];let s=this.searchPossibleTargets(this._objects,e);return t[this.altSelectionKey]&&s&&s!==i?(this.targets=r,i):s}return i}}return this.searchPossibleTargets(this._objects,e)}_pointIsInObjectSelectionArea(t,e){let i=t.getCoords(),r=this.getZoom(),s=t.padding/r;if(s){let[t,e,r,n]=i,o=Math.atan2(e.y-t.y,e.x-t.x),a=eI(o)*s,l=eB(o)*s,h=a+l,c=a-l;i=[new eN(t.x-c,t.y-h),new eN(e.x+h,e.y-c),new eN(r.x+c,r.y+h),new eN(n.x-h,n.y+c)]}return rA.isPointInPolygon(e,i)}_checkTarget(t,e){return!!(t&&t.visible&&t.evented&&this._pointIsInObjectSelectionArea(t,iK(e,void 0,this.viewportTransform)))&&(!this.perPixelTargetFind&&!t.perPixelTargetFind||!!t.isEditing||!this.isTargetTransparent(t,e.x,e.y))||!1}_searchPossibleTargets(t,e){let i=t.length;for(;i--;){let r=t[i];if(this._checkTarget(r,e)){if(eX(r)&&r.subTargetCheck){let t=this._searchPossibleTargets(r._objects,e);t&&this.targets.push(t)}return r}}}searchPossibleTargets(t,e){let i=this._searchPossibleTargets(t,e);if(i&&eX(i)&&i.interactive&&this.targets[0]){let t=this.targets;for(let e=t.length-1;e>0;e--){let i=t[e];if(!eX(i)||!i.interactive)return i}return t[0]}return i}getViewportPoint(t){return this._pointer?this._pointer:this.getPointer(t,!0)}getScenePoint(t){return this._absolutePointer?this._absolutePointer:this.getPointer(t)}getPointer(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.upperCanvasEl,r=i.getBoundingClientRect(),s=iN(t),n=r.width||0,o=r.height||0;n&&o||("top"in r&&ek in r&&(o=Math.abs(r.top-r.bottom)),ej in r&&eO in r&&(n=Math.abs(r.right-r.left))),this.calcOffset(),s.x=s.x-this._offset.left,s.y=s.y-this._offset.top,e||(s=iK(s,void 0,this.viewportTransform));let a=this.getRetinaScaling();1!==a&&(s.x/=a,s.y/=a);let l=0===n||0===o?new eN(1,1):new eN(i.width/n,i.height/o);return s.multiply(l)}_setDimensionsImpl(t,e){this._resetTransformEventData(),super._setDimensionsImpl(t,e),this._isCurrentlyDrawing&&this.freeDrawingBrush&&this.freeDrawingBrush._setBrushStyles(this.contextTop)}_createCacheCanvas(){this.pixelFindCanvasEl=eZ(),this.pixelFindContext=this.pixelFindCanvasEl.getContext("2d",{willReadFrequently:!0}),this.setTargetFindTolerance(this.targetFindTolerance)}getTopContext(){return this.elements.upper.ctx}getSelectionContext(){return this.elements.upper.ctx}getSelectionElement(){return this.elements.upper.el}getActiveObject(){return this._activeObject}getActiveObjects(){let t=this._activeObject;return ik(t)?t.getObjects():t?[t]:[]}_fireSelectionEvents(t,e){let i=!1,r=!1,s=this.getActiveObjects(),n=[],o=[];t.forEach(t=>{s.includes(t)||(i=!0,t.fire("deselected",{e:e,target:t}),o.push(t))}),s.forEach(r=>{t.includes(r)||(i=!0,r.fire("selected",{e:e,target:r}),n.push(r))}),t.length>0&&s.length>0?(r=!0,i&&this.fire("selection:updated",{e:e,selected:n,deselected:o})):s.length>0?(r=!0,this.fire("selection:created",{e:e,selected:n})):t.length>0&&(r=!0,this.fire("selection:cleared",{e:e,deselected:o})),r&&(this._objectsToRender=void 0)}setActiveObject(t,e){let i=this.getActiveObjects(),r=this._setActiveObject(t,e);return this._fireSelectionEvents(i,e),r}_setActiveObject(t,e){let i=this._activeObject;return i!==t&&!(!this._discardActiveObject(e,t)&&this._activeObject)&&!t.onSelect({e:e})&&(this._activeObject=t,ik(t)&&i!==t&&(t.set("canvas",this),t.setCoords()),!0)}_discardActiveObject(t,e){let i=this._activeObject;return!!i&&!i.onDeselect({e:t,object:e})&&(this._currentTransform&&this._currentTransform.target===i&&this.endCurrentTransform(t),this._activeObject=void 0,!0)}discardActiveObject(t){let e=this.getActiveObjects(),i=this.getActiveObject();e.length&&this.fire("before:selection:cleared",{e:t,deselected:[i]});let r=this._discardActiveObject(t);return this._fireSelectionEvents(e,t),r}endCurrentTransform(t){let e=this._currentTransform;this._finalizeCurrentTransform(t),e&&e.target&&(e.target.isMoving=!1),this._currentTransform=null}_finalizeCurrentTransform(t){let e=this._currentTransform,i=e.target,r={e:t,target:i,transform:e,action:e.action};i._scaling&&(i._scaling=!1),i.setCoords(),e.actionPerformed&&(this.fire("object:modified",r),i.fire("modified",r))}setViewportTransform(t){super.setViewportTransform(t);let e=this._activeObject;e&&e.setCoords()}destroy(){let t=this._activeObject;ik(t)&&(t.removeAll(),t.dispose()),delete this._activeObject,super.destroy(),this.pixelFindContext=null,this.pixelFindCanvasEl=void 0}clear(){this.discardActiveObject(),this._activeObject=void 0,this.clearContext(this.contextTop),super.clear()}drawControls(t){let e=this._activeObject;e&&e._renderControls(t)}_toObject(t,e,i){let r=this._realizeGroupTransformOnObject(t),s=super._toObject(t,e,i);return t.set(r),s}_realizeGroupTransformOnObject(t){let{group:e}=t;if(e&&ik(e)&&this._activeObject===e){let i=il(t,["angle","flipX","flipY",eO,"scaleX","scaleY","skewX","skewY","top"]);return iY(t,e.calcOwnMatrix()),i}return{}}_setSVGObject(t,e,i){let r=this._realizeGroupTransformOnObject(e);super._setSVGObject(t,e,i),e.set(r)}}ei(nG,"ownDefaults",{uniformScaling:!0,uniScaleKey:"shiftKey",centeredScaling:!1,centeredRotation:!1,centeredKey:"altKey",altActionKey:"shiftKey",selection:!0,selectionKey:"shiftKey",selectionColor:"rgba(100, 100, 255, 0.3)",selectionDashArray:[],selectionBorderColor:"rgba(255, 255, 255, 0.3)",selectionLineWidth:1,selectionFullyContained:!1,hoverCursor:"move",moveCursor:"move",defaultCursor:"default",freeDrawingCursor:"crosshair",notAllowedCursor:"not-allowed",perPixelTargetFind:!1,targetFindTolerance:0,skipTargetFind:!1,stopContextMenu:!1,fireRightClick:!1,fireMiddleClick:!1,enablePointerEvents:!1,containerClass:"canvas-container",preserveObjectStacking:!1});class nU{constructor(t){ei(this,"targets",[]),ei(this,"__disposer",void 0);let e=()=>{let{hiddenTextarea:e}=t.getActiveObject()||{};e&&e.focus()},i=t.upperCanvasEl;i.addEventListener("click",e),this.__disposer=()=>i.removeEventListener("click",e)}exitTextEditing(){this.target=void 0,this.targets.forEach(t=>{t.isEditing&&t.exitEditing()})}add(t){this.targets.push(t)}remove(t){this.unregister(t),eR(this.targets,t)}register(t){this.target=t}unregister(t){t===this.target&&(this.target=void 0)}onMouseMove(t){var e;(null===(e=this.target)||void 0===e?void 0:e.isEditing)&&this.target.updateSelectionOnMouseMove(t)}clear(){this.targets=[],this.target=void 0}dispose(){this.clear(),this.__disposer(),delete this.__disposer}}let nZ=["target","oldTarget","fireCanvas","e"],nq={passive:!1},nK=(t,e)=>{let i=t.getViewportPoint(e),r=t.getScenePoint(e);return{viewportPoint:i,scenePoint:r,pointer:i,absolutePointer:r}},nJ=function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return t.addEventListener(...i)},nQ=function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return t.removeEventListener(...i)},n$={mouse:{in:"over",out:"out",targetIn:"mouseover",targetOut:"mouseout",canvasIn:"mouse:over",canvasOut:"mouse:out"},drag:{in:"enter",out:"leave",targetIn:"dragenter",targetOut:"dragleave",canvasIn:"drag:enter",canvasOut:"drag:leave"}};class n0 extends nG{constructor(t){super(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}),ei(this,"_isClick",void 0),ei(this,"textEditingManager",new nU(this)),["_onMouseDown","_onTouchStart","_onMouseMove","_onMouseUp","_onTouchEnd","_onResize","_onMouseWheel","_onMouseOut","_onMouseEnter","_onContextMenu","_onDoubleClick","_onDragStart","_onDragEnd","_onDragProgress","_onDragOver","_onDragEnter","_onDragLeave","_onDrop"].forEach(t=>{this[t]=this[t].bind(this)}),this.addOrRemove(nJ,"add")}_getEventPrefix(){return this.enablePointerEvents?"pointer":"mouse"}addOrRemove(t,e){let i=this.upperCanvasEl,r=this._getEventPrefix();t(iE(i),"resize",this._onResize),t(i,r+"down",this._onMouseDown),t(i,"".concat(r,"move"),this._onMouseMove,nq),t(i,"".concat(r,"out"),this._onMouseOut),t(i,"".concat(r,"enter"),this._onMouseEnter),t(i,"wheel",this._onMouseWheel),t(i,"contextmenu",this._onContextMenu),t(i,"dblclick",this._onDoubleClick),t(i,"dragstart",this._onDragStart),t(i,"dragend",this._onDragEnd),t(i,"dragover",this._onDragOver),t(i,"dragenter",this._onDragEnter),t(i,"dragleave",this._onDragLeave),t(i,"drop",this._onDrop),this.enablePointerEvents||t(i,"touchstart",this._onTouchStart,nq)}removeListeners(){this.addOrRemove(nQ,"remove");let t=this._getEventPrefix(),e=iM(this.upperCanvasEl);nQ(e,"".concat(t,"up"),this._onMouseUp),nQ(e,"touchend",this._onTouchEnd,nq),nQ(e,"".concat(t,"move"),this._onMouseMove,nq),nQ(e,"touchmove",this._onMouseMove,nq)}_onMouseWheel(t){this.__onMouseWheel(t)}_onMouseOut(t){let e=this._hoveredTarget,i=ee({e:t},nK(this,t));this.fire("mouse:out",ee(ee({},i),{},{target:e})),this._hoveredTarget=void 0,e&&e.fire("mouseout",ee({},i)),this._hoveredTargets.forEach(t=>{this.fire("mouse:out",ee(ee({},i),{},{target:t})),t&&t.fire("mouseout",ee({},i))}),this._hoveredTargets=[]}_onMouseEnter(t){this._currentTransform||this.findTarget(t)||(this.fire("mouse:over",ee({e:t},nK(this,t))),this._hoveredTarget=void 0,this._hoveredTargets=[])}_onDragStart(t){this._isClick=!1;let e=this.getActiveObject();if(e&&e.onDragStart(t)){this._dragSource=e;let i={e:t,target:e};return this.fire("dragstart",i),e.fire("dragstart",i),void nJ(this.upperCanvasEl,"drag",this._onDragProgress)}iX(t)}_renderDragEffects(t,e,i){let r=!1,s=this._dropTarget;s&&s!==e&&s!==i&&(s.clearContextTop(),r=!0),null==e||e.clearContextTop(),i!==e&&(null==i||i.clearContextTop());let n=this.contextTop;n.save(),n.transform(...this.viewportTransform),e&&(n.save(),e.transform(n),e.renderDragSourceEffect(t),n.restore(),r=!0),i&&(n.save(),i.transform(n),i.renderDropTargetEffect(t),n.restore(),r=!0),n.restore(),r&&(this.contextTopDirty=!0)}_onDragEnd(t){let e=!!t.dataTransfer&&t.dataTransfer.dropEffect!==eM,i=e?this._activeObject:void 0,r={e:t,target:this._dragSource,subTargets:this.targets,dragSource:this._dragSource,didDrop:e,dropTarget:i};nQ(this.upperCanvasEl,"drag",this._onDragProgress),this.fire("dragend",r),this._dragSource&&this._dragSource.fire("dragend",r),delete this._dragSource,this._onMouseUp(t)}_onDragProgress(t){let e={e:t,target:this._dragSource,dragSource:this._dragSource,dropTarget:this._draggedoverTarget};this.fire("drag",e),this._dragSource&&this._dragSource.fire("drag",e)}findDragTargets(t){return this.targets=[],{target:this._searchPossibleTargets(this._objects,this.getViewportPoint(t)),targets:[...this.targets]}}_onDragOver(t){let e;let i="dragover",{target:r,targets:s}=this.findDragTargets(t),n=this._dragSource,o={e:t,target:r,subTargets:s,dragSource:n,canDrop:!1,dropTarget:void 0};this.fire(i,o),this._fireEnterLeaveEvents(r,o),r&&(r.canDrop(t)&&(e=r),r.fire(i,o));for(let r=0;r<s.length;r++){let n=s[r];n.canDrop(t)&&(e=n),n.fire(i,o)}this._renderDragEffects(t,n,e),this._dropTarget=e}_onDragEnter(t){let{target:e,targets:i}=this.findDragTargets(t),r={e:t,target:e,subTargets:i,dragSource:this._dragSource};this.fire("dragenter",r),this._fireEnterLeaveEvents(e,r)}_onDragLeave(t){let e={e:t,target:this._draggedoverTarget,subTargets:this.targets,dragSource:this._dragSource};this.fire("dragleave",e),this._fireEnterLeaveEvents(void 0,e),this._renderDragEffects(t,this._dragSource),this._dropTarget=void 0,this.targets=[],this._hoveredTargets=[]}_onDrop(t){let{target:e,targets:i}=this.findDragTargets(t),r=this._basicEventHandler("drop:before",ee({e:t,target:e,subTargets:i,dragSource:this._dragSource},nK(this,t)));r.didDrop=!1,r.dropTarget=void 0,this._basicEventHandler("drop",r),this.fire("drop:after",r)}_onContextMenu(t){let e=this.findTarget(t),i=this.targets||[],r=this._basicEventHandler("contextmenu:before",{e:t,target:e,subTargets:i});return this.stopContextMenu&&iX(t),this._basicEventHandler("contextmenu",r),!1}_onDoubleClick(t){this._cacheTransformEventData(t),this._handleEvent(t,"dblclick"),this._resetTransformEventData()}getPointerId(t){let e=t.changedTouches;return e?e[0]&&e[0].identifier:this.enablePointerEvents?t.pointerId:-1}_isMainEvent(t){return!0===t.isPrimary||!1!==t.isPrimary&&("touchend"===t.type&&0===t.touches.length||!t.changedTouches||t.changedTouches[0].identifier===this.mainTouchId)}_onTouchStart(t){t.preventDefault(),void 0===this.mainTouchId&&(this.mainTouchId=this.getPointerId(t)),this.__onMouseDown(t),this._resetTransformEventData();let e=this.upperCanvasEl,i=this._getEventPrefix(),r=iM(e);nJ(r,"touchend",this._onTouchEnd,nq),nJ(r,"touchmove",this._onMouseMove,nq),nQ(e,"".concat(i,"down"),this._onMouseDown)}_onMouseDown(t){this.__onMouseDown(t),this._resetTransformEventData();let e=this.upperCanvasEl,i=this._getEventPrefix();nQ(e,"".concat(i,"move"),this._onMouseMove,nq);let r=iM(e);nJ(r,"".concat(i,"up"),this._onMouseUp),nJ(r,"".concat(i,"move"),this._onMouseMove,nq)}_onTouchEnd(t){if(t.touches.length>0)return;this.__onMouseUp(t),this._resetTransformEventData(),delete this.mainTouchId;let e=this._getEventPrefix(),i=iM(this.upperCanvasEl);nQ(i,"touchend",this._onTouchEnd,nq),nQ(i,"touchmove",this._onMouseMove,nq),this._willAddMouseDown&&clearTimeout(this._willAddMouseDown),this._willAddMouseDown=setTimeout(()=>{nJ(this.upperCanvasEl,"".concat(e,"down"),this._onMouseDown),this._willAddMouseDown=0},400)}_onMouseUp(t){this.__onMouseUp(t),this._resetTransformEventData();let e=this.upperCanvasEl,i=this._getEventPrefix();if(this._isMainEvent(t)){let t=iM(this.upperCanvasEl);nQ(t,"".concat(i,"up"),this._onMouseUp),nQ(t,"".concat(i,"move"),this._onMouseMove,nq),nJ(e,"".concat(i,"move"),this._onMouseMove,nq)}}_onMouseMove(t){let e=this.getActiveObject();this.allowTouchScrolling||e&&e.shouldStartDragging(t)||!t.preventDefault||t.preventDefault(),this.__onMouseMove(t)}_onResize(){this.calcOffset(),this._resetTransformEventData()}_shouldRender(t){let e=this.getActiveObject();return!!e!=!!t||e&&t&&e!==t}__onMouseUp(t){var e;this._cacheTransformEventData(t),this._handleEvent(t,"up:before");let i=this._currentTransform,r=this._isClick,s=this._target,{button:n}=t;if(n)return(this.fireMiddleClick&&1===n||this.fireRightClick&&2===n)&&this._handleEvent(t,"up"),void this._resetTransformEventData();if(this.isDrawingMode&&this._isCurrentlyDrawing)return void this._onMouseUpInDrawingMode(t);if(!this._isMainEvent(t))return;let o,a,l=!1;if(i&&(this._finalizeCurrentTransform(t),l=i.actionPerformed),!r){let e=s===this._activeObject;this.handleSelection(t),l||(l=this._shouldRender(s)||!e&&s===this._activeObject)}if(s){let{key:e,control:r}=s.findControl(this.getViewportPoint(t),iV(t))||{};if(a=e,s.selectable&&s!==this._activeObject&&"up"===s.activeOn)this.setActiveObject(s,t),l=!0;else if(r){let e=r.getMouseUpHandler(t,s,r);e&&(o=this.getScenePoint(t),e.call(r,t,i,o.x,o.y))}s.isMoving=!1}if(i&&(i.target!==s||i.corner!==a)){let e=i.target&&i.target.controls[i.corner],r=e&&e.getMouseUpHandler(t,i.target,e);o=o||this.getScenePoint(t),r&&r.call(e,t,i,o.x,o.y)}this._setCursorFromEvent(t,s),this._handleEvent(t,"up"),this._groupSelector=null,this._currentTransform=null,s&&(s.__corner=void 0),l?this.requestRenderAll():r||null!==(e=this._activeObject)&&void 0!==e&&e.isEditing||this.renderTop()}_basicEventHandler(t,e){let{target:i,subTargets:r=[]}=e;this.fire(t,e),i&&i.fire(t,e);for(let s=0;s<r.length;s++)r[s]!==i&&r[s].fire(t,e);return e}_handleEvent(t,e){let i=this._target,r=this.targets||[],s=ee(ee({e:t,target:i,subTargets:r},nK(this,t)),{},{transform:this._currentTransform},"up:before"===e||"up"===e?{isClick:this._isClick,currentTarget:this.findTarget(t),currentSubTargets:this.targets}:{});this.fire("mouse:".concat(e),s),i&&i.fire("mouse".concat(e),s);for(let t=0;t<r.length;t++)r[t]!==i&&r[t].fire("mouse".concat(e),s)}_onMouseDownInDrawingMode(t){this._isCurrentlyDrawing=!0,this.getActiveObject()&&(this.discardActiveObject(t),this.requestRenderAll());let e=this.getScenePoint(t);this.freeDrawingBrush&&this.freeDrawingBrush.onMouseDown(e,{e:t,pointer:e}),this._handleEvent(t,"down")}_onMouseMoveInDrawingMode(t){if(this._isCurrentlyDrawing){let e=this.getScenePoint(t);this.freeDrawingBrush&&this.freeDrawingBrush.onMouseMove(e,{e:t,pointer:e})}this.setCursor(this.freeDrawingCursor),this._handleEvent(t,"move")}_onMouseUpInDrawingMode(t){let e=this.getScenePoint(t);this.freeDrawingBrush?this._isCurrentlyDrawing=!!this.freeDrawingBrush.onMouseUp({e:t,pointer:e}):this._isCurrentlyDrawing=!1,this._handleEvent(t,"up")}__onMouseDown(t){this._isClick=!0,this._cacheTransformEventData(t),this._handleEvent(t,"down:before");let e=this._target,{button:i}=t;if(i)return(this.fireMiddleClick&&1===i||this.fireRightClick&&2===i)&&this._handleEvent(t,"down"),void this._resetTransformEventData();if(this.isDrawingMode)return void this._onMouseDownInDrawingMode(t);if(!this._isMainEvent(t)||this._currentTransform)return;let r=this._shouldRender(e),s=!1;if(this.handleMultiSelection(t,e)?(e=this._activeObject,s=!0,r=!0):this._shouldClearSelection(t,e)&&this.discardActiveObject(t),this.selection&&(!e||!e.selectable&&!e.isEditing&&e!==this._activeObject)){let e=this.getScenePoint(t);this._groupSelector={x:e.x,y:e.y,deltaY:0,deltaX:0}}if(e){let i=e===this._activeObject;e.selectable&&"down"===e.activeOn&&this.setActiveObject(e,t);let r=e.findControl(this.getViewportPoint(t),iV(t));if(e===this._activeObject&&(r||!s)){this._setupCurrentTransform(t,e,i);let s=r?r.control:void 0,n=this.getScenePoint(t),o=s&&s.getMouseDownHandler(t,e,s);o&&o.call(s,t,this._currentTransform,n.x,n.y)}}r&&(this._objectsToRender=void 0),this._handleEvent(t,"down"),r&&this.requestRenderAll()}_resetTransformEventData(){this._target=void 0,this._pointer=void 0,this._absolutePointer=void 0}_cacheTransformEventData(t){this._resetTransformEventData(),this._pointer=this.getViewportPoint(t),this._absolutePointer=iK(this._pointer,void 0,this.viewportTransform),this._target=this._currentTransform?this._currentTransform.target:this.findTarget(t)}__onMouseMove(t){if(this._isClick=!1,this._cacheTransformEventData(t),this._handleEvent(t,"move:before"),this.isDrawingMode)return void this._onMouseMoveInDrawingMode(t);if(!this._isMainEvent(t))return;let e=this._groupSelector;if(e){let i=this.getScenePoint(t);e.deltaX=i.x-e.x,e.deltaY=i.y-e.y,this.renderTop()}else if(this._currentTransform)this._transformObject(t);else{let e=this.findTarget(t);this._setCursorFromEvent(t,e),this._fireOverOutEvents(t,e)}this.textEditingManager.onMouseMove(t),this._handleEvent(t,"move"),this._resetTransformEventData()}_fireOverOutEvents(t,e){let i=this._hoveredTarget,r=this._hoveredTargets,s=this.targets,n=Math.max(r.length,s.length);this.fireSyntheticInOutEvents("mouse",{e:t,target:e,oldTarget:i,fireCanvas:!0});for(let e=0;e<n;e++)this.fireSyntheticInOutEvents("mouse",{e:t,target:s[e],oldTarget:r[e]});this._hoveredTarget=e,this._hoveredTargets=this.targets.concat()}_fireEnterLeaveEvents(t,e){let i=this._draggedoverTarget,r=this._hoveredTargets,s=this.targets,n=Math.max(r.length,s.length);this.fireSyntheticInOutEvents("drag",ee(ee({},e),{},{target:t,oldTarget:i,fireCanvas:!0}));for(let t=0;t<n;t++)this.fireSyntheticInOutEvents("drag",ee(ee({},e),{},{target:s[t],oldTarget:r[t]}));this._draggedoverTarget=t}fireSyntheticInOutEvents(t,e){let{target:i,oldTarget:r,fireCanvas:s,e:n}=e,o=er(e,nZ),{targetIn:a,targetOut:l,canvasIn:h,canvasOut:c}=n$[t],d=r!==i;if(r&&d){let t=ee(ee({},o),{},{e:n,target:r,nextTarget:i},nK(this,n));s&&this.fire(c,t),r.fire(l,t)}if(i&&d){let t=ee(ee({},o),{},{e:n,target:i,previousTarget:r},nK(this,n));s&&this.fire(h,t),i.fire(a,t)}}__onMouseWheel(t){this._cacheTransformEventData(t),this._handleEvent(t,"wheel"),this._resetTransformEventData()}_transformObject(t){let e=this.getScenePoint(t),i=this._currentTransform,r=i.target,s=r.group?iK(e,void 0,r.group.calcTransformMatrix()):e;i.shiftKey=t.shiftKey,i.altKey=!!this.centeredKey&&t[this.centeredKey],this._performTransformAction(t,i,s),i.actionPerformed&&this.requestRenderAll()}_performTransformAction(t,e,i){let r=i.x,s=i.y,n=e.action,o=e.actionHandler,a=!1;o&&(a=o(t,e,r,s)),"drag"===n&&a&&(e.target.isMoving=!0,this.setCursor(e.target.moveCursor||this.moveCursor)),e.actionPerformed=e.actionPerformed||a}_setCursorFromEvent(t,e){if(!e)return void this.setCursor(this.defaultCursor);let i=e.hoverCursor||this.hoverCursor,r=ik(this._activeObject)?this._activeObject:null,s=(!r||e.group!==r)&&e.findControl(this.getViewportPoint(t));if(s){let i=s.control;this.setCursor(i.cursorStyleHandler(t,i,e))}else e.subTargetCheck&&this.targets.concat().reverse().map(t=>{i=t.hoverCursor||i}),this.setCursor(i)}handleMultiSelection(t,e){let i=this._activeObject,r=ik(i);if(i&&this._isSelectionKeyPressed(t)&&this.selection&&e&&e.selectable&&(i!==e||r)&&(r||!e.isDescendantOf(i)&&!i.isDescendantOf(e))&&!e.onSelect({e:t})&&!i.getActiveControl()){if(r){let r=i.getObjects();if(e===i){let i=this.getViewportPoint(t);if(!(e=this.searchPossibleTargets(r,i)||this.searchPossibleTargets(this._objects,i))||!e.selectable)return!1}e.group===i?(i.remove(e),this._hoveredTarget=e,this._hoveredTargets=[...this.targets],1===i.size()&&this._setActiveObject(i.item(0),t)):(i.multiSelectAdd(e),this._hoveredTarget=i,this._hoveredTargets=[...this.targets]),this._fireSelectionEvents(r,t)}else{i.exitEditing&&i.exitEditing();let r=new(eA.getClass("ActiveSelection"))([],{canvas:this});r.multiSelectAdd(i,e),this._hoveredTarget=r,this._setActiveObject(r,t),this._fireSelectionEvents([i],t)}return!0}return!1}handleSelection(t){if(!this.selection||!this._groupSelector)return!1;let{x:e,y:i,deltaX:r,deltaY:s}=this._groupSelector,n=new eN(e,i),o=n.add(new eN(r,s)),a=n.min(o),l=n.max(o).subtract(a),h=this.collectObjects({left:a.x,top:a.y,width:l.x,height:l.y},{includeIntersecting:!this.selectionFullyContained}),c=n.eq(o)?h[0]?[h[0]]:[]:h.length>1?h.filter(e=>!e.onSelect({e:t})).reverse():h;if(1===c.length)this.setActiveObject(c[0],t);else if(c.length>1){let e=eA.getClass("ActiveSelection");this.setActiveObject(new e(c,{canvas:this}),t)}return this._groupSelector=null,!0}clear(){this.textEditingManager.clear(),super.clear()}destroy(){this.removeListeners(),this.textEditingManager.dispose(),super.destroy()}}let n1={x1:0,y1:0,x2:0,y2:0},n2=ee(ee({},n1),{},{r1:0,r2:0}),n3=/^(\d+\.\d+)%|(\d+)%$/;function n6(t){return t&&n3.test(t)}function n5(t,e){return rg(0,eF("number"==typeof t?t:"string"==typeof t?parseFloat(t)/(n6(t)?100:1):NaN,e),1)}let n8=/\s*;\s*/,n4=/\s*:\s*/;function n9(t){return"linearGradient"===t.nodeName||"LINEARGRADIENT"===t.nodeName?"linear":"radial"}function n7(t){return"userSpaceOnUse"===t.getAttribute("gradientUnits")?"pixels":"percentage"}function ot(t,e){return t.getAttribute(e)}class oe{constructor(t){let{type:e="linear",gradientUnits:i="pixels",coords:r={},colorStops:s=[],offsetX:n=0,offsetY:o=0,gradientTransform:a,id:l}=t;this.id=l?"".concat(l,"_").concat(eU()):eU(),this.type=e,this.gradientUnits=i,this.gradientTransform=a,this.offsetX=n,this.offsetY=o,this.coords=ee(ee({},"radial"===this.type?n2:n1),r),this.colorStops=s.slice()}addColorStop(t){for(let e in t){let i=new im(t[e]);this.colorStops.push({offset:parseFloat(e),color:i.toRgb(),opacity:i.getAlpha()})}return this}toObject(t){return ee(ee({},il(this,t)),{},{type:this.type,coords:this.coords,colorStops:this.colorStops,offsetX:this.offsetX,offsetY:this.offsetY,gradientUnits:this.gradientUnits,gradientTransform:this.gradientTransform?[...this.gradientTransform]:void 0})}toSVG(t){let{additionalTransform:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=[],r=this.gradientTransform?this.gradientTransform.concat():eS.concat(),s="pixels"===this.gradientUnits?"userSpaceOnUse":"objectBoundingBox",n=this.colorStops.map(t=>ee({},t)).sort((t,e)=>t.offset-e.offset),o=-this.offsetX,a=-this.offsetY;"objectBoundingBox"===s?(o/=t.width,a/=t.height):(o+=t.width/2,a+=t.height/2),t&&"function"==typeof t._renderPathCommands&&"percentage"!==this.gradientUnits&&(o-=t.pathOffset.x,a-=t.pathOffset.y),r[4]-=o,r[5]-=a;let l=['id="SVGID_'.concat(this.id,'"'),'gradientUnits="'.concat(s,'"'),'gradientTransform="'.concat(e?e+" ":"").concat(ib(r),'"'),""].join(" ");if("linear"===this.type){let{x1:t,y1:e,x2:r,y2:s}=this.coords;i.push("<linearGradient ",l,' x1="',t,'" y1="',e,'" x2="',r,'" y2="',s,'">\n')}else if("radial"===this.type){let{x1:t,y1:e,x2:r,y2:s,r1:o,r2:a}=this.coords,h=o>a;i.push("<radialGradient ",l,' cx="',h?t:r,'" cy="',h?e:s,'" r="',h?o:a,'" fx="',h?r:t,'" fy="',h?s:e,'">\n'),h&&(n.reverse(),n.forEach(t=>{t.offset=1-t.offset}));let c=Math.min(o,a);if(c>0){let t=c/Math.max(o,a);n.forEach(e=>{e.offset+=t*(1-e.offset)})}}return n.forEach(t=>{let{color:e,offset:r,opacity:s}=t;i.push("<stop ",'offset="',100*r+"%",'" style="stop-color:',e,void 0!==s?";stop-opacity: "+s:";",'"/>\n')}),i.push("linear"===this.type?"</linearGradient>":"</radialGradient>","\n"),i.join("")}toLive(t){let e=this.coords,i="linear"===this.type?t.createLinearGradient(e.x1,e.y1,e.x2,e.y2):t.createRadialGradient(e.x1,e.y1,e.r1,e.x2,e.y2,e.r2);return this.colorStops.forEach(t=>{let{color:e,opacity:r,offset:s}=t;i.addColorStop(s,void 0!==r?new im(e).setAlpha(r).toRgba():e)}),i}static async fromObject(t){return new this(t)}static fromElement(t,e,i){var r,s,n,o;let a=n7(t),l=e._findCenterFromElement();return new this(ee({id:t.getAttribute("id")||void 0,type:n9(t),coords:(r=t,s={width:i.viewBoxWidth||i.width,height:i.viewBoxHeight||i.height},function(t,e){let i,{width:r,height:s,gradientUnits:n}=e;return Object.keys(t).reduce((e,o)=>{let a=t[o];return"Infinity"===a?i=1:"-Infinity"===a?i=0:(i="string"==typeof a?parseFloat(a):a,"string"==typeof a&&n6(a)&&(i*=.01,"pixels"===n&&("x1"!==o&&"x2"!==o&&"r2"!==o||(i*=r),"y1"!==o&&"y2"!==o||(i*=s)))),e[o]=i,e},{})}("linear"===n9(r)?{x1:ot(n=r,"x1")||0,y1:ot(n,"y1")||0,x2:ot(n,"x2")||"100%",y2:ot(n,"y2")||0}:{x1:ot(o=r,"fx")||ot(o,"cx")||"50%",y1:ot(o,"fy")||ot(o,"cy")||"50%",r1:0,x2:ot(o,"cx")||"50%",y2:ot(o,"cy")||"50%",r2:ot(o,"r")||"50%"},ee(ee({},s),{},{gradientUnits:n7(r)}))),colorStops:function(t,e){let i=[],r=t.getElementsByTagName("stop"),s=n5(e,1);for(let t=r.length;t--;)i.push(function(t,e){let i,r;let s=t.getAttribute("style");if(s){let t=s.split(n8);""===t[t.length-1]&&t.pop();for(let e=t.length;e--;){let[s,n]=t[e].split(n4).map(t=>t.trim());"stop-color"===s?i=n:"stop-opacity"===s&&(r=n)}}let n=new im(i||t.getAttribute("stop-color")||"rgb(0,0,0)");return{offset:n5(t.getAttribute("offset"),0),color:n.toRgb(),opacity:eF(parseFloat(r||t.getAttribute("stop-opacity")||""),1)*n.getAlpha()*e}}(r[t],s));return i}(t,i.opacity),gradientUnits:a,gradientTransform:sQ(t.getAttribute("gradientTransform")||"")},"pixels"===a?{offsetX:e.width/2-l.x,offsetY:e.height/2-l.y}:{offsetX:0,offsetY:0}))}}ei(oe,"type","Gradient"),eA.setClass(oe,"gradient"),eA.setClass(oe,"linear"),eA.setClass(oe,"radial");let oi=["type","source"];class or{get type(){return"pattern"}set type(t){el("warn","Setting type has no effect",t)}constructor(t){ei(this,"repeat","repeat"),ei(this,"offsetX",0),ei(this,"offsetY",0),ei(this,"crossOrigin",""),ei(this,"patternTransform",null),this.id=eU(),Object.assign(this,t)}isImageSource(){return!!this.source&&"string"==typeof this.source.src}isCanvasSource(){return!!this.source&&!!this.source.toDataURL}sourceToString(){return this.isImageSource()?this.source.src:this.isCanvasSource()?this.source.toDataURL():""}toLive(t){return this.source&&(!this.isImageSource()||this.source.complete&&0!==this.source.naturalWidth&&0!==this.source.naturalHeight)?t.createPattern(this.source,this.repeat):null}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],{repeat:e,crossOrigin:i}=this;return ee(ee({},il(this,t)),{},{type:"pattern",source:this.sourceToString(),repeat:e,crossOrigin:i,offsetX:ix(this.offsetX,ea.NUM_FRACTION_DIGITS),offsetY:ix(this.offsetY,ea.NUM_FRACTION_DIGITS),patternTransform:this.patternTransform?[...this.patternTransform]:null})}toSVG(t){let{width:e,height:i}=t,{source:r,repeat:s,id:n}=this,o=eF(this.offsetX/e,0),a=eF(this.offsetY/i,0),l="repeat-y"===s||"no-repeat"===s?1+Math.abs(o||0):eF(r.width/e,0),h="repeat-x"===s||"no-repeat"===s?1+Math.abs(a||0):eF(r.height/i,0);return['<pattern id="SVGID_'.concat(n,'" x="').concat(o,'" y="').concat(a,'" width="').concat(l,'" height="').concat(h,'">'),'<image x="0" y="0" width="'.concat(r.width,'" height="').concat(r.height,'" xlink:href="').concat(this.sourceToString(),'"></image>'),"</pattern>",""].join("\n")}static async fromObject(t,e){let{type:i,source:r}=t,s=er(t,oi),n=await is(r,ee(ee({},e),{},{crossOrigin:s.crossOrigin}));return new this(ee(ee({},s),{},{source:n}))}}ei(or,"type","Pattern"),eA.setClass(or),eA.setClass(or,"pattern");class os{constructor(t){ei(this,"color","rgb(0, 0, 0)"),ei(this,"width",1),ei(this,"shadow",null),ei(this,"strokeLineCap","round"),ei(this,"strokeLineJoin","round"),ei(this,"strokeMiterLimit",10),ei(this,"strokeDashArray",null),ei(this,"limitedToCanvasSize",!1),this.canvas=t}_setBrushStyles(t){t.strokeStyle=this.color,t.lineWidth=this.width,t.lineCap=this.strokeLineCap,t.miterLimit=this.strokeMiterLimit,t.lineJoin=this.strokeLineJoin,t.setLineDash(this.strokeDashArray||[])}_saveAndTransform(t){let e=this.canvas.viewportTransform;t.save(),t.transform(e[0],e[1],e[2],e[3],e[4],e[5])}needsFullRender(){return 1>new im(this.color).getAlpha()||!!this.shadow}_setShadow(){if(!this.shadow||!this.canvas)return;let t=this.canvas,e=this.shadow,i=t.contextTop,r=t.getZoom()*t.getRetinaScaling();i.shadowColor=e.color,i.shadowBlur=e.blur*r,i.shadowOffsetX=e.offsetX*r,i.shadowOffsetY=e.offsetY*r}_resetShadow(){let t=this.canvas.contextTop;t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0}_isOutSideCanvas(t){return t.x<0||t.x>this.canvas.getWidth()||t.y<0||t.y>this.canvas.getHeight()}}let on=["path","left","top"],oo=["d"];class oa extends sw{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{path:i,left:r,top:s}=e,n=er(e,on);super(),Object.assign(this,oa.ownDefaults),this.setOptions(n),this._setPath(t||[],!0),"number"==typeof r&&this.set(eO,r),"number"==typeof s&&this.set("top",s)}_setPath(t,e){this.path=nk(Array.isArray(t)?t:nz(t)),this.setBoundingBox(e)}_findCenterFromElement(){let t=this._calcBoundsFromPath();return new eN(t.left+t.width/2,t.top+t.height/2)}_renderPathCommands(t){let e=-this.pathOffset.x,i=-this.pathOffset.y;for(let r of(t.beginPath(),this.path))switch(r[0]){case"L":t.lineTo(r[1]+e,r[2]+i);break;case"M":t.moveTo(r[1]+e,r[2]+i);break;case"C":t.bezierCurveTo(r[1]+e,r[2]+i,r[3]+e,r[4]+i,r[5]+e,r[6]+i);break;case"Q":t.quadraticCurveTo(r[1]+e,r[2]+i,r[3]+e,r[4]+i);break;case"Z":t.closePath()}}_render(t){this._renderPathCommands(t),this._renderPaintInOrder(t)}toString(){return"#<Path (".concat(this.complexity(),'): { "top": ').concat(this.top,', "left": ').concat(this.left," }>")}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return ee(ee({},super.toObject(t)),{},{path:this.path.map(t=>t.slice())})}toDatalessObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=this.toObject(t);return this.sourcePath&&(delete e.path,e.sourcePath=this.sourcePath),e}_toSVG(){let t=nY(this.path,ea.NUM_FRACTION_DIGITS);return["<path ","COMMON_PARTS",'d="'.concat(t,'" stroke-linecap="round" />\n')]}_getOffsetTransform(){let t=ea.NUM_FRACTION_DIGITS;return" translate(".concat(ix(-this.pathOffset.x,t),", ").concat(ix(-this.pathOffset.y,t),")")}toClipPathSVG(t){let e=this._getOffsetTransform();return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})}toSVG(t){let e=this._getOffsetTransform();return this._createBaseSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})}complexity(){return this.path.length}setDimensions(){this.setBoundingBox()}setBoundingBox(t){let{width:e,height:i,pathOffset:r}=this._calcDimensions();this.set({width:e,height:i,pathOffset:r}),t&&this.setPositionByOrigin(r,eT,eT)}_calcBoundsFromPath(){let t=[],e=0,i=0,r=0,s=0;for(let n of this.path)switch(n[0]){case"L":r=n[1],s=n[2],t.push(new eN(e,i),new eN(r,s));break;case"M":r=n[1],s=n[2],e=r,i=s;break;case"C":t.push(...nT(r,s,n[1],n[2],n[3],n[4],n[5],n[6])),r=n[5],s=n[6];break;case"Q":t.push(...nT(r,s,n[1],n[2],n[1],n[2],n[3],n[4])),r=n[3],s=n[4];break;case"Z":r=e,s=i}return iz(t)}_calcDimensions(){let t=this._calcBoundsFromPath();return ee(ee({},t),{},{pathOffset:new eN(t.left+t.width/2,t.top+t.height/2)})}static fromObject(t){return this._fromObject(t,{extraParam:"path"})}static async fromElement(t,e,i){let r=s0(t,this.ATTRIBUTE_NAMES,i),{d:s}=r;return new this(s,ee(ee(ee({},er(r,oo)),e),{},{left:void 0,top:void 0}))}}ei(oa,"type","Path"),ei(oa,"cacheProperties",[...rq,"path","fillRule"]),ei(oa,"ATTRIBUTE_NAMES",[...sR,"d"]),eA.setClass(oa),eA.setSVGClass(oa);class ol extends os{constructor(t){super(t),ei(this,"decimate",.4),ei(this,"drawStraightLine",!1),ei(this,"straightLineKey","shiftKey"),this._points=[],this._hasStraightLine=!1}needsFullRender(){return super.needsFullRender()||this._hasStraightLine}static drawSegment(t,e,i){let r=e.midPointFrom(i);return t.quadraticCurveTo(e.x,e.y,r.x,r.y),r}onMouseDown(t,e){let{e:i}=e;this.canvas._isMainEvent(i)&&(this.drawStraightLine=!!this.straightLineKey&&i[this.straightLineKey],this._prepareForDrawing(t),this._addPoint(t),this._render())}onMouseMove(t,e){let{e:i}=e;if(this.canvas._isMainEvent(i)&&(this.drawStraightLine=!!this.straightLineKey&&i[this.straightLineKey],(!0!==this.limitedToCanvasSize||!this._isOutSideCanvas(t))&&this._addPoint(t)&&this._points.length>1)){if(this.needsFullRender())this.canvas.clearContext(this.canvas.contextTop),this._render();else{let t=this._points,e=t.length,i=this.canvas.contextTop;this._saveAndTransform(i),this.oldEnd&&(i.beginPath(),i.moveTo(this.oldEnd.x,this.oldEnd.y)),this.oldEnd=ol.drawSegment(i,t[e-2],t[e-1]),i.stroke(),i.restore()}}}onMouseUp(t){let{e:e}=t;return!this.canvas._isMainEvent(e)||(this.drawStraightLine=!1,this.oldEnd=void 0,this._finalizeAndAddPath(),!1)}_prepareForDrawing(t){this._reset(),this._addPoint(t),this.canvas.contextTop.moveTo(t.x,t.y)}_addPoint(t){return!(this._points.length>1&&t.eq(this._points[this._points.length-1]))&&(this.drawStraightLine&&this._points.length>1&&(this._hasStraightLine=!0,this._points.pop()),this._points.push(t),!0)}_reset(){this._points=[],this._setBrushStyles(this.canvas.contextTop),this._setShadow(),this._hasStraightLine=!1}_render(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.canvas.contextTop,e=this._points[0],i=this._points[1];if(this._saveAndTransform(t),t.beginPath(),2===this._points.length&&e.x===i.x&&e.y===i.y){let t=this.width/1e3;e.x-=t,i.x+=t}t.moveTo(e.x,e.y);for(let r=1;r<this._points.length;r++)ol.drawSegment(t,e,i),e=this._points[r],i=this._points[r+1];t.lineTo(e.x,e.y),t.stroke(),t.restore()}convertPointsToSVGPath(t){return nW(t,this.width/1e3)}createPath(t){let e=new oa(t,{fill:null,stroke:this.color,strokeWidth:this.width,strokeLineCap:this.strokeLineCap,strokeMiterLimit:this.strokeMiterLimit,strokeLineJoin:this.strokeLineJoin,strokeDashArray:this.strokeDashArray});return this.shadow&&(this.shadow.affectStroke=!0,e.shadow=new rG(this.shadow)),e}decimatePoints(t,e){if(t.length<=2)return t;let i=t[0],r=Math.pow(e/this.canvas.getZoom(),2),s=t.length-1,n=[i];for(let e=1;e<s-1;e++)Math.pow(i.x-t[e].x,2)+Math.pow(i.y-t[e].y,2)>=r&&n.push(i=t[e]);return n.push(t[s]),n}_finalizeAndAddPath(){this.canvas.contextTop.closePath(),this.decimate&&(this._points=this.decimatePoints(this._points,this.decimate));let t=this.convertPointsToSVGPath(this._points);if("M 0 0 Q 0 0 0 0 L 0 0"===nY(t))return void this.canvas.requestRenderAll();let e=this.createPath(t);this.canvas.clearContext(this.canvas.contextTop),this.canvas.fire("before:path:created",{path:e}),this.canvas.add(e),this.canvas.requestRenderAll(),e.setCoords(),this._resetShadow(),this.canvas.fire("path:created",{path:e})}}let oh=["left","top","radius"],oc=["radius","startAngle","endAngle","counterClockwise"];class od extends sw{static getDefaults(){return ee(ee({},super.getDefaults()),od.ownDefaults)}constructor(t){super(),Object.assign(this,od.ownDefaults),this.setOptions(t)}_set(t,e){return super._set(t,e),"radius"===t&&this.setRadius(e),this}_render(t){t.beginPath(),t.arc(0,0,this.radius,eJ(this.startAngle),eJ(this.endAngle),this.counterClockwise),this._renderPaintInOrder(t)}getRadiusX(){return this.get("radius")*this.get("scaleX")}getRadiusY(){return this.get("radius")*this.get("scaleY")}setRadius(t){this.radius=t,this.set({width:2*t,height:2*t})}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return super.toObject([...oc,...t])}_toSVG(){let t=(this.endAngle-this.startAngle)%360;if(0===t)return["<circle ","COMMON_PARTS",'cx="0" cy="0" ','r="',"".concat(this.radius),'" />\n'];{let{radius:e}=this,i=eJ(this.startAngle),r=eJ(this.endAngle),s=eI(i)*e,n=eB(i)*e,o=eI(r)*e,a=eB(r)*e,l=this.counterClockwise?0:1;return['<path d="M '.concat(s," ").concat(n," A ").concat(e," ").concat(e," 0 ").concat(t>180?1:0," ").concat(l," ").concat(o," ").concat(a,'" '),"COMMON_PARTS"," />\n"]}}static async fromElement(t,e,i){let r=s0(t,this.ATTRIBUTE_NAMES,i),{left:s=0,top:n=0,radius:o=0}=r;return new this(ee(ee({},er(r,oh)),{},{radius:o,left:s-o,top:n-o}))}static fromObject(t){return super._fromObject(t)}}ei(od,"type","Circle"),ei(od,"cacheProperties",[...rq,...oc]),ei(od,"ownDefaults",{radius:0,startAngle:0,endAngle:360,counterClockwise:!1}),ei(od,"ATTRIBUTE_NAMES",["cx","cy","r",...sR]),eA.setClass(od),eA.setSVGClass(od);let ou=["x1","y1","x2","y2"],og=["x1","y1","x2","y2"],of=["x1","x2","y1","y2"];class op extends sw{constructor(){let[t,e,i,r]=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[0,0,0,0],s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),Object.assign(this,op.ownDefaults),this.setOptions(s),this.x1=t,this.x2=i,this.y1=e,this.y2=r,this._setWidthHeight();let{left:n,top:o}=s;"number"==typeof n&&this.set(eO,n),"number"==typeof o&&this.set("top",o)}_setWidthHeight(){let{x1:t,y1:e,x2:i,y2:r}=this;this.width=Math.abs(i-t),this.height=Math.abs(r-e);let{left:s,top:n,width:o,height:a}=iz([{x:t,y:e},{x:i,y:r}]),l=new eN(s+o/2,n+a/2);this.setPositionByOrigin(l,eT,eT)}_set(t,e){return super._set(t,e),of.includes(t)&&this._setWidthHeight(),this}_render(t){var e;t.beginPath();let i=this.calcLinePoints();t.moveTo(i.x1,i.y1),t.lineTo(i.x2,i.y2),t.lineWidth=this.strokeWidth;let r=t.strokeStyle;iC(this.stroke)?t.strokeStyle=this.stroke.toLive(t):t.strokeStyle=null!==(e=this.stroke)&&void 0!==e?e:t.fillStyle,this.stroke&&this._renderStroke(t),t.strokeStyle=r}_findCenterFromElement(){return new eN((this.x1+this.x2)/2,(this.y1+this.y2)/2)}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return ee(ee({},super.toObject(t)),this.calcLinePoints())}_getNonTransformedDimensions(){let t=super._getNonTransformedDimensions();return"butt"===this.strokeLineCap&&(0===this.width&&(t.y-=this.strokeWidth),0===this.height&&(t.x-=this.strokeWidth)),t}calcLinePoints(){let{x1:t,x2:e,y1:i,y2:r,width:s,height:n}=this,o=t<=e?-1:1,a=i<=r?-1:1;return{x1:o*s/2,x2:-(o*s)/2,y1:a*n/2,y2:-(a*n)/2}}_toSVG(){let{x1:t,x2:e,y1:i,y2:r}=this.calcLinePoints();return["<line ","COMMON_PARTS",'x1="'.concat(t,'" y1="').concat(i,'" x2="').concat(e,'" y2="').concat(r,'" />\n')]}static async fromElement(t,e,i){let r=s0(t,this.ATTRIBUTE_NAMES,i),{x1:s=0,y1:n=0,x2:o=0,y2:a=0}=r;return new this([s,n,o,a],er(r,ou))}static fromObject(t){let{x1:e,y1:i,x2:r,y2:s}=t,n=er(t,og);return this._fromObject(ee(ee({},n),{},{points:[e,i,r,s]}),{extraParam:"points"})}}ei(op,"type","Line"),ei(op,"cacheProperties",[...rq,...of]),ei(op,"ATTRIBUTE_NAMES",sR.concat(of)),eA.setClass(op),eA.setSVGClass(op);class ov extends sw{static getDefaults(){return ee(ee({},super.getDefaults()),ov.ownDefaults)}constructor(t){super(),Object.assign(this,ov.ownDefaults),this.setOptions(t)}_render(t){let e=this.width/2,i=this.height/2;t.beginPath(),t.moveTo(-e,i),t.lineTo(0,-i),t.lineTo(e,i),t.closePath(),this._renderPaintInOrder(t)}_toSVG(){let t=this.width/2,e=this.height/2;return["<polygon ","COMMON_PARTS",'points="',"".concat(-t," ").concat(e,",0 ").concat(-e,",").concat(t," ").concat(e),'" />']}}ei(ov,"type","Triangle"),ei(ov,"ownDefaults",{width:100,height:100}),eA.setClass(ov),eA.setSVGClass(ov);let om=["rx","ry"];class ox extends sw{static getDefaults(){return ee(ee({},super.getDefaults()),ox.ownDefaults)}constructor(t){super(),Object.assign(this,ox.ownDefaults),this.setOptions(t)}_set(t,e){switch(super._set(t,e),t){case"rx":this.rx=e,this.set("width",2*e);break;case"ry":this.ry=e,this.set("height",2*e)}return this}getRx(){return this.get("rx")*this.get("scaleX")}getRy(){return this.get("ry")*this.get("scaleY")}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return super.toObject([...om,...t])}_toSVG(){return["<ellipse ","COMMON_PARTS",'cx="0" cy="0" rx="'.concat(this.rx,'" ry="').concat(this.ry,'" />\n')]}_render(t){t.beginPath(),t.save(),t.transform(1,0,0,this.ry/this.rx,0,0),t.arc(0,0,this.rx,0,ew,!1),t.restore(),this._renderPaintInOrder(t)}static async fromElement(t,e,i){let r=s0(t,this.ATTRIBUTE_NAMES,i);return r.left=(r.left||0)-r.rx,r.top=(r.top||0)-r.ry,new this(r)}}ei(ox,"type","Ellipse"),ei(ox,"cacheProperties",[...rq,...om]),ei(ox,"ownDefaults",{rx:0,ry:0}),ei(ox,"ATTRIBUTE_NAMES",[...sR,"cx","cy","rx","ry"]),eA.setClass(ox),eA.setSVGClass(ox);let oy=["left","top"],o_={exactBoundingBox:!1};class ob extends sw{static getDefaults(){return ee(ee({},super.getDefaults()),ob.ownDefaults)}constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),ei(this,"strokeDiff",void 0),Object.assign(this,ob.ownDefaults),this.setOptions(e),this.points=t;let{left:i,top:r}=e;this.initialized=!0,this.setBoundingBox(!0),"number"==typeof i&&this.set(eO,i),"number"==typeof r&&this.set("top",r)}isOpen(){return!0}_projectStrokeOnPoints(t){return sj(this.points,t,this.isOpen())}_calcDimensions(t){t=ee({scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,strokeLineCap:this.strokeLineCap,strokeLineJoin:this.strokeLineJoin,strokeMiterLimit:this.strokeMiterLimit,strokeUniform:this.strokeUniform,strokeWidth:this.strokeWidth},t||{});let e=this.exactBoundingBox?this._projectStrokeOnPoints(t).map(t=>t.projectedPoint):this.points;if(0===e.length)return{left:0,top:0,width:0,height:0,pathOffset:new eN,strokeOffset:new eN,strokeDiff:new eN};let i=iz(e),r=ii(ee(ee({},t),{},{scaleX:1,scaleY:1})),s=iz(this.points.map(t=>e0(t,r,!0))),n=new eN(this.scaleX,this.scaleY),o=i.left+i.width/2,a=i.top+i.height/2;return this.exactBoundingBox&&(o-=a*Math.tan(eJ(this.skewX)),a-=o*Math.tan(eJ(this.skewY))),ee(ee({},i),{},{pathOffset:new eN(o,a),strokeOffset:new eN(s.left,s.top).subtract(new eN(i.left,i.top)).multiply(n),strokeDiff:new eN(i.width,i.height).subtract(new eN(s.width,s.height)).multiply(n)})}_findCenterFromElement(){let t=iz(this.points);return new eN(t.left+t.width/2,t.top+t.height/2)}setDimensions(){this.setBoundingBox()}setBoundingBox(t){let{left:e,top:i,width:r,height:s,pathOffset:n,strokeOffset:o,strokeDiff:a}=this._calcDimensions();this.set({width:r,height:s,pathOffset:n,strokeOffset:o,strokeDiff:a}),t&&this.setPositionByOrigin(new eN(e+r/2,i+s/2),eT,eT)}isStrokeAccountedForInDimensions(){return this.exactBoundingBox}_getNonTransformedDimensions(){return this.exactBoundingBox?new eN(this.width,this.height):super._getNonTransformedDimensions()}_getTransformedDimensions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.exactBoundingBox){var e,i,r,s;let n;if(Object.keys(t).some(t=>this.strokeUniform||this.constructor.layoutProperties.includes(t))){let{width:r,height:s}=this._calcDimensions(t);n=new eN(null!==(e=t.width)&&void 0!==e?e:r,null!==(i=t.height)&&void 0!==i?i:s)}else n=new eN(null!==(r=t.width)&&void 0!==r?r:this.width,null!==(s=t.height)&&void 0!==s?s:this.height);return n.multiply(new eN(t.scaleX||this.scaleX,t.scaleY||this.scaleY))}return super._getTransformedDimensions(t)}_set(t,e){let i=this.initialized&&this[t]!==e,r=super._set(t,e);return this.exactBoundingBox&&i&&(("scaleX"===t||"scaleY"===t)&&this.strokeUniform&&this.constructor.layoutProperties.includes("strokeUniform")||this.constructor.layoutProperties.includes(t))&&this.setDimensions(),r}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return ee(ee({},super.toObject(t)),{},{points:rU(this.points)})}_toSVG(){let t=[],e=this.pathOffset.x,i=this.pathOffset.y,r=ea.NUM_FRACTION_DIGITS;for(let s=0,n=this.points.length;s<n;s++)t.push(ix(this.points[s].x-e,r),",",ix(this.points[s].y-i,r)," ");return["<".concat(this.constructor.type.toLowerCase()," "),"COMMON_PARTS",'points="'.concat(t.join(""),'" />\n')]}_render(t){let e=this.points.length,i=this.pathOffset.x,r=this.pathOffset.y;if(e&&!isNaN(this.points[e-1].y)){t.beginPath(),t.moveTo(this.points[0].x-i,this.points[0].y-r);for(let s=0;s<e;s++){let e=this.points[s];t.lineTo(e.x-i,e.y-r)}this.isOpen()||t.closePath(),this._renderPaintInOrder(t)}}complexity(){return this.points.length}static async fromElement(t,e,i){return new this(function(t){if(!t)return[];let e=t.replace(/,/g," ").trim().split(/\s+/),i=[];for(let t=0;t<e.length;t+=2)i.push({x:parseFloat(e[t]),y:parseFloat(e[t+1])});return i}(t.getAttribute("points")),ee(ee({},er(s0(t,this.ATTRIBUTE_NAMES,i),oy)),e))}static fromObject(t){return this._fromObject(t,{extraParam:"points"})}}ei(ob,"ownDefaults",o_),ei(ob,"type","Polyline"),ei(ob,"layoutProperties",["skewX","skewY","strokeLineCap","strokeLineJoin","strokeMiterLimit","strokeWidth","strokeUniform","points"]),ei(ob,"cacheProperties",[...rq,"points"]),ei(ob,"ATTRIBUTE_NAMES",[...sR]),eA.setClass(ob),eA.setSVGClass(ob);class ow extends ob{isOpen(){return!1}}ei(ow,"ownDefaults",o_),ei(ow,"type","Polygon"),eA.setClass(ow),eA.setSVGClass(ow);let oC=["fontSize","fontWeight","fontFamily","fontStyle"],oS=["underline","overline","linethrough"],oT=[...oC,"lineHeight","text","charSpacing","textAlign","styles","path","pathStartOffset","pathSide","pathAlign"],oO=[...oT,...oS,"textBackgroundColor","direction"],ok=[...oC,...oS,"stroke","strokeWidth","fill","deltaY","textBackgroundColor"],oj="justify",oM="justify-left",oE="justify-right",oD="justify-center";class oA extends sw{isEmptyStyles(t){if(!this.styles||void 0!==t&&!this.styles[t])return!0;let e=void 0===t?this.styles:{line:this.styles[t]};for(let t in e)for(let i in e[t])for(let r in e[t][i])return!1;return!0}styleHas(t,e){if(!this.styles||void 0!==e&&!this.styles[e])return!1;let i=void 0===e?this.styles:{0:this.styles[e]};for(let e in i)for(let r in i[e])if(void 0!==i[e][r][t])return!0;return!1}cleanStyle(t){if(!this.styles)return!1;let e=this.styles,i,r,s=0,n=!0,o=0;for(let o in e){for(let a in i=0,e[o]){let l=e[o][a]||{};s++,void 0!==l[t]?(r?l[t]!==r&&(n=!1):r=l[t],l[t]===this[t]&&delete l[t]):n=!1,0!==Object.keys(l).length?i++:delete e[o][a]}0===i&&delete e[o]}for(let t=0;t<this._textLines.length;t++)o+=this._textLines[t].length;n&&s===o&&(this[t]=r,this.removeStyle(t))}removeStyle(t){let e,i,r;if(!this.styles)return;let s=this.styles;for(i in s){for(r in e=s[i])delete e[r][t],0===Object.keys(e[r]).length&&delete e[r];0===Object.keys(e).length&&delete s[i]}}_extendStyles(t,e){let{lineIndex:i,charIndex:r}=this.get2DCursorLocation(t);this._getLineStyle(i)||this._setLineStyle(i);let s=ih(ee(ee({},this._getStyleDeclaration(i,r)),e),t=>void 0!==t);this._setStyleDeclaration(i,r,s)}getSelectionStyles(t,e,i){let r=[];for(let s=t;s<(e||t);s++)r.push(this.getStyleAtPosition(s,i));return r}getStyleAtPosition(t,e){let{lineIndex:i,charIndex:r}=this.get2DCursorLocation(t);return e?this.getCompleteStyleDeclaration(i,r):this._getStyleDeclaration(i,r)}setSelectionStyles(t,e,i){for(let r=e;r<(i||e);r++)this._extendStyles(r,t);this._forceClearCache=!0}_getStyleDeclaration(t,e){var i;let r=this.styles&&this.styles[t];return r&&null!==(i=r[e])&&void 0!==i?i:{}}getCompleteStyleDeclaration(t,e){return ee(ee({},il(this,this.constructor._styleProperties)),this._getStyleDeclaration(t,e))}_setStyleDeclaration(t,e,i){this.styles[t][e]=i}_deleteStyleDeclaration(t,e){delete this.styles[t][e]}_getLineStyle(t){return!!this.styles[t]}_setLineStyle(t){this.styles[t]={}}_deleteLineStyle(t){delete this.styles[t]}}ei(oA,"_styleProperties",ok);let oL=/  +/g,oP=/"/g;function oF(t,e,i,r,s){return"		".concat(function(t,e){let{left:i,top:r,width:s,height:n}=e,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ea.NUM_FRACTION_DIGITS,a=iw("fill",t,!1),[l,h,c,d]=[i,r,s,n].map(t=>ix(t,o));return"<rect ".concat(a,' x="').concat(l,'" y="').concat(h,'" width="').concat(c,'" height="').concat(d,'"></rect>')}(t,{left:e,top:i,width:r,height:s}),"\n")}let oR=["textAnchor","textDecoration","dx","dy","top","left","fontSize","strokeWidth"];class oI extends oA{static getDefaults(){return ee(ee({},super.getDefaults()),oI.ownDefaults)}constructor(t,e){super(),ei(this,"__charBounds",[]),Object.assign(this,oI.ownDefaults),this.setOptions(e),this.styles||(this.styles={}),this.text=t,this.initialized=!0,this.path&&this.setPathInfo(),this.initDimensions(),this.setCoords()}setPathInfo(){let t=this.path;t&&(t.segmentsInfo=nB(t.path))}_splitText(){let t=this._splitTextIntoLines(this.text);return this.textLines=t.lines,this._textLines=t.graphemeLines,this._unwrappedTextLines=t._unwrappedLines,this._text=t.graphemeText,t}initDimensions(){this._splitText(),this._clearCache(),this.dirty=!0,this.path?(this.width=this.path.width,this.height=this.path.height):(this.width=this.calcTextWidth()||this.cursorWidth||this.MIN_TEXT_WIDTH,this.height=this.calcTextHeight()),this.textAlign.includes(oj)&&this.enlargeSpaces()}enlargeSpaces(){let t,e,i,r,s,n,o;for(let a=0,l=this._textLines.length;a<l;a++)if((this.textAlign===oj||a!==l-1&&!this.isEndOfWrapping(a))&&(r=0,s=this._textLines[a],(e=this.getLineWidth(a))<this.width&&(o=this.textLines[a].match(this._reSpacesAndTabs)))){i=o.length,t=(this.width-e)/i;for(let e=0;e<=s.length;e++)n=this.__charBounds[a][e],this._reSpaceAndTab.test(s[e])?(n.width+=t,n.kernedWidth+=t,n.left+=r,r+=t):n.left+=r}}isEndOfWrapping(t){return t===this._textLines.length-1}missingNewlineOffset(t){return 1}get2DCursorLocation(t,e){let i;let r=e?this._unwrappedTextLines:this._textLines;for(i=0;i<r.length;i++){if(t<=r[i].length)return{lineIndex:i,charIndex:t};t-=r[i].length+this.missingNewlineOffset(i,e)}return{lineIndex:i-1,charIndex:r[i-1].length<t?r[i-1].length:t}}toString(){return"#<Text (".concat(this.complexity(),'): { "text": "').concat(this.text,'", "fontFamily": "').concat(this.fontFamily,'" }>')}_getCacheCanvasDimensions(){let t=super._getCacheCanvasDimensions(),e=this.fontSize;return t.width+=e*t.zoomX,t.height+=e*t.zoomY,t}_render(t){let e=this.path;e&&!e.isNotVisible()&&e._render(t),this._setTextStyles(t),this._renderTextLinesBackground(t),this._renderTextDecoration(t,"underline"),this._renderText(t),this._renderTextDecoration(t,"overline"),this._renderTextDecoration(t,"linethrough")}_renderText(t){"stroke"===this.paintFirst?(this._renderTextStroke(t),this._renderTextFill(t)):(this._renderTextFill(t),this._renderTextStroke(t))}_setTextStyles(t,e,i){if(t.textBaseline="alphabetic",this.path)switch(this.pathAlign){case eT:t.textBaseline="middle";break;case"ascender":t.textBaseline="top";break;case"descender":t.textBaseline=ek}t.font=this._getFontDeclaration(e,i)}calcTextWidth(){let t=this.getLineWidth(0);for(let e=1,i=this._textLines.length;e<i;e++){let i=this.getLineWidth(e);i>t&&(t=i)}return t}_renderTextLine(t,e,i,r,s,n){this._renderChars(t,e,i,r,s,n)}_renderTextLinesBackground(t){if(!this.textBackgroundColor&&!this.styleHas("textBackgroundColor"))return;let e=t.fillStyle,i=this._getLeftOffset(),r=this._getTopOffset();for(let e=0,s=this._textLines.length;e<s;e++){let s=this.getHeightOfLine(e);if(!this.textBackgroundColor&&!this.styleHas("textBackgroundColor",e)){r+=s;continue}let n=this._textLines[e].length,o=this._getLineLeftOffset(e),a,l,h=0,c=0,d=this.getValueOfPropertyAt(e,0,"textBackgroundColor");for(let u=0;u<n;u++){let n=this.__charBounds[e][u];l=this.getValueOfPropertyAt(e,u,"textBackgroundColor"),this.path?(t.save(),t.translate(n.renderLeft,n.renderTop),t.rotate(n.angle),t.fillStyle=l,l&&t.fillRect(-n.width/2,-s/this.lineHeight*(1-this._fontSizeFraction),n.width,s/this.lineHeight),t.restore()):l!==d?(a=i+o+c,"rtl"===this.direction&&(a=this.width-a-h),t.fillStyle=d,d&&t.fillRect(a,r,h,s/this.lineHeight),c=n.left,h=n.width,d=l):h+=n.kernedWidth}l&&!this.path&&(a=i+o+c,"rtl"===this.direction&&(a=this.width-a-h),t.fillStyle=l,t.fillRect(a,r,h,s/this.lineHeight)),r+=s}t.fillStyle=e,this._removeShadow(t)}_measureChar(t,e,i,r){let n,o,a,l;let h=ex.getFontCache(e),c=this._getFontDeclaration(e),d=i+t,u=i&&c===this._getFontDeclaration(r),g=e.fontSize/this.CACHE_FONT_SIZE;if(i&&void 0!==h[i]&&(a=h[i]),void 0!==h[t]&&(l=n=h[t]),u&&void 0!==h[d]&&(l=(o=h[d])-a),void 0===n||void 0===a||void 0===o){let r=function(){if(!s){let t=eZ();t.width=t.height=0,s=t.getContext("2d")}return s}();this._setTextStyles(r,e,!0),void 0===n&&(l=n=r.measureText(t).width,h[t]=n),void 0===a&&u&&i&&(a=r.measureText(i).width,h[i]=a),u&&void 0===o&&(o=r.measureText(d).width,h[d]=o,l=o-a)}return{width:n*g,kernedWidth:l*g}}getHeightOfChar(t,e){return this.getValueOfPropertyAt(t,e,"fontSize")}measureLine(t){let e=this._measureLine(t);return 0!==this.charSpacing&&(e.width-=this._getWidthOfCharSpacing()),e.width<0&&(e.width=0),e}_measureLine(t){let e,i,r=0,s=this.pathSide===ej,n=this.path,o=this._textLines[t],a=o.length,l=Array(a);this.__charBounds[t]=l;for(let s=0;s<a;s++){let n=o[s];i=this._getGraphemeBox(n,t,s,e),l[s]=i,r+=i.kernedWidth,e=n}if(l[a]={left:i?i.left+i.width:0,width:0,kernedWidth:0,height:this.fontSize,deltaY:0},n&&n.segmentsInfo){let t=0,e=n.segmentsInfo[n.segmentsInfo.length-1].length;switch(this.textAlign){case eO:t=s?e-r:0;break;case eT:t=(e-r)/2;break;case ej:t=s?0:e-r}t+=this.pathStartOffset*(s?-1:1);for(let r=s?a-1:0;s?r>=0:r<a;s?r--:r++)i=l[r],t>e?t%=e:t<0&&(t+=e),this._setGraphemeOnPath(t,i),t+=i.kernedWidth}return{width:r,numOfSpaces:0}}_setGraphemeOnPath(t,e){let i=t+e.kernedWidth/2,r=this.path,s=nN(r.path,i,r.segmentsInfo);e.renderLeft=s.x-r.pathOffset.x,e.renderTop=s.y-r.pathOffset.y,e.angle=s.angle+(this.pathSide===ej?Math.PI:0)}_getGraphemeBox(t,e,i,r,s){let n=this.getCompleteStyleDeclaration(e,i),o=r?this.getCompleteStyleDeclaration(e,i-1):{},a=this._measureChar(t,n,r,o),l,h=a.kernedWidth,c=a.width;0!==this.charSpacing&&(c+=l=this._getWidthOfCharSpacing(),h+=l);let d={width:c,left:0,height:n.fontSize,kernedWidth:h,deltaY:n.deltaY};if(i>0&&!s){let t=this.__charBounds[e][i-1];d.left=t.left+t.width+a.kernedWidth-a.width}return d}getHeightOfLine(t){if(this.__lineHeights[t])return this.__lineHeights[t];let e=this.getHeightOfChar(t,0);for(let i=1,r=this._textLines[t].length;i<r;i++)e=Math.max(this.getHeightOfChar(t,i),e);return this.__lineHeights[t]=e*this.lineHeight*this._fontSizeMult}calcTextHeight(){let t,e=0;for(let i=0,r=this._textLines.length;i<r;i++)t=this.getHeightOfLine(i),e+=i===r-1?t/this.lineHeight:t;return e}_getLeftOffset(){return"ltr"===this.direction?-this.width/2:this.width/2}_getTopOffset(){return-this.height/2}_renderTextCommon(t,e){t.save();let i=0,r=this._getLeftOffset(),s=this._getTopOffset();for(let n=0,o=this._textLines.length;n<o;n++){let o=this.getHeightOfLine(n),a=o/this.lineHeight,l=this._getLineLeftOffset(n);this._renderTextLine(e,t,this._textLines[n],r+l,s+i+a,n),i+=o}t.restore()}_renderTextFill(t){(this.fill||this.styleHas("fill"))&&this._renderTextCommon(t,"fillText")}_renderTextStroke(t){(this.stroke&&0!==this.strokeWidth||!this.isEmptyStyles())&&(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this._setLineDash(t,this.strokeDashArray),t.beginPath(),this._renderTextCommon(t,"strokeText"),t.closePath(),t.restore())}_renderChars(t,e,i,r,s,n){let o=this.getHeightOfLine(n),a=this.textAlign.includes(oj),l=this.path,h=!a&&0===this.charSpacing&&this.isEmptyStyles(n)&&!l,c="ltr"===this.direction,d="ltr"===this.direction?1:-1,u=e.direction,g,f,p,v,m,x="",y=0;if(e.save(),u!==this.direction&&(e.canvas.setAttribute("dir",c?"ltr":"rtl"),e.direction=c?"ltr":"rtl",e.textAlign=c?eO:ej),s-=o*this._fontSizeFraction/this.lineHeight,h)return this._renderChar(t,e,n,0,i.join(""),r,s),void e.restore();for(let o=0,h=i.length-1;o<=h;o++)v=o===h||this.charSpacing||l,x+=i[o],p=this.__charBounds[n][o],0===y?(r+=d*(p.kernedWidth-p.width),y+=p.width):y+=p.kernedWidth,a&&!v&&this._reSpaceAndTab.test(i[o])&&(v=!0),v||(g=g||this.getCompleteStyleDeclaration(n,o),v=sL(g,f=this.getCompleteStyleDeclaration(n,o+1),!1)),v&&(l?(e.save(),e.translate(p.renderLeft,p.renderTop),e.rotate(p.angle),this._renderChar(t,e,n,o,x,-y/2,0),e.restore()):(m=r,this._renderChar(t,e,n,o,x,m,s)),x="",g=f,r+=d*y,y=0);e.restore()}_applyPatternGradientTransformText(t){let e=eZ(),i=this.width+this.strokeWidth,r=this.height+this.strokeWidth,s=e.getContext("2d");return e.width=i,e.height=r,s.beginPath(),s.moveTo(0,0),s.lineTo(i,0),s.lineTo(i,r),s.lineTo(0,r),s.closePath(),s.translate(i/2,r/2),s.fillStyle=t.toLive(s),this._applyPatternGradientTransform(s,t),s.fill(),s.createPattern(e,"no-repeat")}handleFiller(t,e,i){let r,s;return iC(i)?"percentage"===i.gradientUnits||i.gradientTransform||i.patternTransform?(r=-this.width/2,s=-this.height/2,t.translate(r,s),t[e]=this._applyPatternGradientTransformText(i),{offsetX:r,offsetY:s}):(t[e]=i.toLive(t),this._applyPatternGradientTransform(t,i)):(t[e]=i,{offsetX:0,offsetY:0})}_setStrokeStyles(t,e){let{stroke:i,strokeWidth:r}=e;return t.lineWidth=r,t.lineCap=this.strokeLineCap,t.lineDashOffset=this.strokeDashOffset,t.lineJoin=this.strokeLineJoin,t.miterLimit=this.strokeMiterLimit,this.handleFiller(t,"strokeStyle",i)}_setFillStyles(t,e){let{fill:i}=e;return this.handleFiller(t,"fillStyle",i)}_renderChar(t,e,i,r,s,n,o){let a=this._getStyleDeclaration(i,r),l=this.getCompleteStyleDeclaration(i,r),h="fillText"===t&&l.fill,c="strokeText"===t&&l.stroke&&l.strokeWidth;if(c||h){if(e.save(),e.font=this._getFontDeclaration(l),a.textBackgroundColor&&this._removeShadow(e),a.deltaY&&(o+=a.deltaY),h){let t=this._setFillStyles(e,l);e.fillText(s,n-t.offsetX,o-t.offsetY)}if(c){let t=this._setStrokeStyles(e,l);e.strokeText(s,n-t.offsetX,o-t.offsetY)}e.restore()}}setSuperscript(t,e){this._setScript(t,e,this.superscript)}setSubscript(t,e){this._setScript(t,e,this.subscript)}_setScript(t,e,i){let r=this.get2DCursorLocation(t,!0),s=this.getValueOfPropertyAt(r.lineIndex,r.charIndex,"fontSize"),n=this.getValueOfPropertyAt(r.lineIndex,r.charIndex,"deltaY"),o={fontSize:s*i.size,deltaY:n+s*i.baseline};this.setSelectionStyles(o,t,e)}_getLineLeftOffset(t){let e=this.getLineWidth(t),i=this.width-e,r=this.textAlign,s=this.direction,n=this.isEndOfWrapping(t),o=0;return r!==oj&&(r!==oD||n)&&(r!==oE||n)&&(r!==oM||n)?(r===eT&&(o=i/2),r===ej&&(o=i),r===oD&&(o=i/2),r===oE&&(o=i),"rtl"===s&&(r===ej||r===oj||r===oE?o=0:r===eO||r===oM?o=-i:r!==eT&&r!==oD||(o=-i/2)),o):0}_clearCache(){this._forceClearCache=!1,this.__lineWidths=[],this.__lineHeights=[],this.__charBounds=[]}getLineWidth(t){if(void 0!==this.__lineWidths[t])return this.__lineWidths[t];let{width:e}=this.measureLine(t);return this.__lineWidths[t]=e,e}_getWidthOfCharSpacing(){return 0!==this.charSpacing?this.fontSize*this.charSpacing/1e3:0}getValueOfPropertyAt(t,e,i){var r;return null!==(r=this._getStyleDeclaration(t,e)[i])&&void 0!==r?r:this[i]}_renderTextDecoration(t,e){if(!this[e]&&!this.styleHas(e))return;let i=this._getTopOffset(),r=this._getLeftOffset(),s=this.path,n=this._getWidthOfCharSpacing(),o=this.offsets[e];for(let a=0,l=this._textLines.length;a<l;a++){let l=this.getHeightOfLine(a);if(!this[e]&&!this.styleHas(e,a)){i+=l;continue}let h=this._textLines[a],c=l/this.lineHeight,d=this._getLineLeftOffset(a),u,g,f=0,p=0,v=this.getValueOfPropertyAt(a,0,e),m=this.getValueOfPropertyAt(a,0,"fill"),x=i+c*(1-this._fontSizeFraction),y=this.getHeightOfChar(a,0),_=this.getValueOfPropertyAt(a,0,"deltaY");for(let i=0,n=h.length;i<n;i++){let n=this.__charBounds[a][i];u=this.getValueOfPropertyAt(a,i,e),g=this.getValueOfPropertyAt(a,i,"fill");let l=this.getHeightOfChar(a,i),h=this.getValueOfPropertyAt(a,i,"deltaY");if(s&&u&&g)t.save(),t.fillStyle=m,t.translate(n.renderLeft,n.renderTop),t.rotate(n.angle),t.fillRect(-n.kernedWidth/2,o*l+h,n.kernedWidth,this.fontSize/15),t.restore();else if((u!==v||g!==m||l!==y||h!==_)&&p>0){let e=r+d+f;"rtl"===this.direction&&(e=this.width-e-p),v&&m&&(t.fillStyle=m,t.fillRect(e,x+o*y+_,p,this.fontSize/15)),f=n.left,p=n.width,v=u,m=g,y=l,_=h}else p+=n.kernedWidth}let b=r+d+f;"rtl"===this.direction&&(b=this.width-b-p),t.fillStyle=g,u&&g&&t.fillRect(b,x+o*y+_,p-n,this.fontSize/15),i+=l}this._removeShadow(t)}_getFontDeclaration(){let{fontFamily:t=this.fontFamily,fontStyle:e=this.fontStyle,fontWeight:i=this.fontWeight,fontSize:r=this.fontSize}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=arguments.length>1?arguments[1]:void 0,n=t.includes("'")||t.includes('"')||t.includes(",")||oI.genericFonts.includes(t.toLowerCase())?t:'"'.concat(t,'"');return[e,i,"".concat(s?this.CACHE_FONT_SIZE:r,"px"),n].join(" ")}render(t){this.visible&&(this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(this._forceClearCache&&this.initDimensions(),super.render(t)))}graphemeSplit(t){return sE(t)}_splitTextIntoLines(t){let e=t.split(this._reNewline),i=Array(e.length),r=["\n"],s=[];for(let t=0;t<e.length;t++)i[t]=this.graphemeSplit(e[t]),s=s.concat(i[t],r);return s.pop(),{_unwrappedLines:i,lines:e,graphemeText:s,graphemeLines:i}}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return ee(ee({},super.toObject([...oO,...t])),{},{styles:sP(this.styles,this.text)},this.path?{path:this.path.toObject()}:{})}set(t,e){let{textLayoutProperties:i}=this.constructor;super.set(t,e);let r=!1,s=!1;if("object"==typeof t)for(let e in t)"path"===e&&this.setPathInfo(),r=r||i.includes(e),s=s||"path"===e;else r=i.includes(t),s="path"===t;return s&&this.setPathInfo(),r&&this.initialized&&(this.initDimensions(),this.setCoords()),this}complexity(){return 1}static async fromElement(t,e,i){let r=s0(t,oI.ATTRIBUTE_NAMES,i),s=ee(ee({},e),r),{textAnchor:n=eO,textDecoration:o="",dx:a=0,dy:l=0,top:h=0,left:c=0,fontSize:d=16,strokeWidth:u=1}=s,g=er(s,oR),f=new this((t.textContent||"").replace(/^\s+|\s+$|\n+/g,"").replace(/\s+/g," "),ee({left:c+a,top:h+l,underline:o.includes("underline"),overline:o.includes("overline"),linethrough:o.includes("line-through"),strokeWidth:0,fontSize:d},g)),p=f.getScaledHeight()/f.height,v=((f.height+f.strokeWidth)*f.lineHeight-f.height)*p,m=f.getScaledHeight()+v,x=0;return n===eT&&(x=f.getScaledWidth()/2),n===ej&&(x=f.getScaledWidth()),f.set({left:f.left-x,top:f.top-(m-f.fontSize*(.07+f._fontSizeFraction))/f.lineHeight,strokeWidth:u}),f}static fromObject(t){return this._fromObject(ee(ee({},t),{},{styles:sF(t.styles||{},t.text)}),{extraParam:"text"})}}ei(oI,"textLayoutProperties",oT),ei(oI,"cacheProperties",[...rq,...oO]),ei(oI,"ownDefaults",{_reNewline:eE,_reSpacesAndTabs:/[ \t\r]/g,_reSpaceAndTab:/[ \t\r]/,_reWords:/\S+/g,fontSize:40,fontWeight:"normal",fontFamily:"Times New Roman",underline:!1,overline:!1,linethrough:!1,textAlign:eO,fontStyle:"normal",lineHeight:1.16,superscript:{size:.6,baseline:-.35},subscript:{size:.6,baseline:.11},textBackgroundColor:"",stroke:null,shadow:null,path:void 0,pathStartOffset:0,pathSide:eO,pathAlign:"baseline",_fontSizeFraction:.222,offsets:{underline:.1,linethrough:-.315,overline:-.88},_fontSizeMult:1.13,charSpacing:0,deltaY:0,direction:"ltr",CACHE_FONT_SIZE:400,MIN_TEXT_WIDTH:2}),ei(oI,"type","Text"),ei(oI,"genericFonts",["sans-serif","serif","cursive","fantasy","monospace"]),ei(oI,"ATTRIBUTE_NAMES",sR.concat("x","y","dx","dy","font-family","font-style","font-weight","font-size","letter-spacing","text-decoration","text-anchor")),sb(oI,[class extends rt{_toSVG(){let t=this._getSVGLeftTopOffsets(),e=this._getSVGTextAndBg(t.textTop,t.textLeft);return this._wrapSVGTextAndBg(e)}toSVG(t){return this._createBaseSVGMarkup(this._toSVG(),{reviver:t,noStyle:!0,withShadow:!0})}_getSVGLeftTopOffsets(){return{textLeft:-this.width/2,textTop:-this.height/2,lineTop:this.getHeightOfLine(0)}}_wrapSVGTextAndBg(t){let{textBgRects:e,textSpans:i}=t,r=this.getSvgTextDecoration(this);return[e.join(""),'		<text xml:space="preserve" ',this.fontFamily?'font-family="'.concat(this.fontFamily.replace(oP,"'"),'" '):"",this.fontSize?'font-size="'.concat(this.fontSize,'" '):"",this.fontStyle?'font-style="'.concat(this.fontStyle,'" '):"",this.fontWeight?'font-weight="'.concat(this.fontWeight,'" '):"",r?'text-decoration="'.concat(r,'" '):"","rtl"===this.direction?'direction="'.concat(this.direction,'" '):"",'style="',this.getSvgStyles(!0),'"',this.addPaintOrder()," >",i.join(""),"</text>\n"]}_getSVGTextAndBg(t,e){let i=[],r=[],s,n=t;this.backgroundColor&&r.push(...oF(this.backgroundColor,-this.width/2,-this.height/2,this.width,this.height));for(let t=0,o=this._textLines.length;t<o;t++)s=this._getLineLeftOffset(t),"rtl"===this.direction&&(s+=this.width),(this.textBackgroundColor||this.styleHas("textBackgroundColor",t))&&this._setSVGTextLineBg(r,t,e+s,n),this._setSVGTextLineText(i,t,e+s,n),n+=this.getHeightOfLine(t);return{textSpans:i,textBgRects:r}}_createTextCharSpan(t,e,i,r){let s=this.getSvgSpanStyles(e,t!==t.trim()||!!t.match(oL)),n=e.deltaY,o=n?' dy="'.concat(ix(n,ea.NUM_FRACTION_DIGITS),'" '):"";return'<tspan x="'.concat(ix(i,ea.NUM_FRACTION_DIGITS),'" y="').concat(ix(r,ea.NUM_FRACTION_DIGITS),'" ').concat(o).concat(s?'style="'.concat(s,'"'):"",">").concat(sM(t),"</tspan>")}_setSVGTextLineText(t,e,i,r){let s=this.getHeightOfLine(e),n=this.textAlign.includes(oj),o=this._textLines[e],a,l,h,c,d,u="",g=0;r+=s*(1-this._fontSizeFraction)/this.lineHeight;for(let s=0,f=o.length-1;s<=f;s++)d=s===f||this.charSpacing,u+=o[s],h=this.__charBounds[e][s],0===g?(i+=h.kernedWidth-h.width,g+=h.width):g+=h.kernedWidth,n&&!d&&this._reSpaceAndTab.test(o[s])&&(d=!0),d||(a=a||this.getCompleteStyleDeclaration(e,s),d=sL(a,l=this.getCompleteStyleDeclaration(e,s+1),!0)),d&&(c=this._getStyleDeclaration(e,s),t.push(this._createTextCharSpan(u,c,i,r)),u="",a=l,"rtl"===this.direction?i-=g:i+=g,g=0)}_setSVGTextLineBg(t,e,i,r){let s=this._textLines[e],n=this.getHeightOfLine(e)/this.lineHeight,o,a=0,l=0,h=this.getValueOfPropertyAt(e,0,"textBackgroundColor");for(let c=0;c<s.length;c++){let{left:s,width:d,kernedWidth:u}=this.__charBounds[e][c];(o=this.getValueOfPropertyAt(e,c,"textBackgroundColor"))!==h?(h&&t.push(...oF(h,i+l,r,a,n)),l=s,a=d,h=o):a+=u}o&&t.push(...oF(h,i+l,r,a,n))}_getSVGLineTopOffset(t){let e,i=0;for(e=0;e<t;e++)i+=this.getHeightOfLine(e);let r=this.getHeightOfLine(e);return{lineTop:i,offset:(this._fontSizeMult-this._fontSizeFraction)*r/(this.lineHeight*this._fontSizeMult)}}getSvgStyles(t){return"".concat(super.getSvgStyles(t)," white-space: pre;")}getSvgSpanStyles(t,e){let{fontFamily:i,strokeWidth:r,stroke:s,fill:n,fontSize:o,fontStyle:a,fontWeight:l,deltaY:h}=t,c=this.getSvgTextDecoration(t);return[s?iw("stroke",s):"",r?"stroke-width: ".concat(r,"; "):"",i?"font-family: ".concat(i.includes("'")||i.includes('"')?i:"'".concat(i,"'"),"; "):"",o?"font-size: ".concat(o,"px; "):"",a?"font-style: ".concat(a,"; "):"",l?"font-weight: ".concat(l,"; "):"",c?"text-decoration: ".concat(c,"; "):c,n?iw("fill",n):"",h?"baseline-shift: ".concat(-h,"; "):"",e?"white-space: pre; ":""].join("")}getSvgTextDecoration(t){return["overline","underline","line-through"].filter(e=>t[e.replace("-","")]).join(" ")}}]),eA.setClass(oI),eA.setSVGClass(oI);class oB{constructor(t){ei(this,"target",void 0),ei(this,"__mouseDownInPlace",!1),ei(this,"__dragStartFired",!1),ei(this,"__isDraggingOver",!1),ei(this,"__dragStartSelection",void 0),ei(this,"__dragImageDisposer",void 0),ei(this,"_dispose",void 0),this.target=t;let e=[this.target.on("dragenter",this.dragEnterHandler.bind(this)),this.target.on("dragover",this.dragOverHandler.bind(this)),this.target.on("dragleave",this.dragLeaveHandler.bind(this)),this.target.on("dragend",this.dragEndHandler.bind(this)),this.target.on("drop",this.dropHandler.bind(this))];this._dispose=()=>{e.forEach(t=>t()),this._dispose=void 0}}isPointerOverSelection(t){let e=this.target,i=e.getSelectionStartFromPointer(t);return e.isEditing&&i>=e.selectionStart&&i<=e.selectionEnd&&e.selectionStart<e.selectionEnd}start(t){return this.__mouseDownInPlace=this.isPointerOverSelection(t)}isActive(){return this.__mouseDownInPlace}end(t){let e=this.isActive();return e&&!this.__dragStartFired&&(this.target.setCursorByClick(t),this.target.initDelayedCursor(!0)),this.__mouseDownInPlace=!1,this.__dragStartFired=!1,this.__isDraggingOver=!1,e}getDragStartSelection(){return this.__dragStartSelection}setDragImage(t,e){var i;let{selectionStart:r,selectionEnd:s}=e,n=this.target,o=n.canvas,a=new eN(n.flipX?-1:1,n.flipY?-1:1),l=n._getCursorBoundaries(r),h=new eN(l.left+l.leftOffset,l.top+l.topOffset).multiply(a).transform(n.calcTransformMatrix()),c=o.getScenePoint(t).subtract(h),d=n.getCanvasRetinaScaling(),u=n.getBoundingRect(),g=h.subtract(new eN(u.left,u.top)),f=o.viewportTransform,p=g.add(c).transform(f,!0),v=n.backgroundColor,m=rU(n.styles);n.backgroundColor="";let x={stroke:"transparent",fill:"transparent",textBackgroundColor:"transparent"};n.setSelectionStyles(x,0,r),n.setSelectionStyles(x,s,n.text.length),n.dirty=!0;let y=n.toCanvasElement({enableRetinaScaling:o.enableRetinaScaling,viewportTransform:!0});n.backgroundColor=v,n.styles=m,n.dirty=!0,iD(y,{position:"fixed",left:"".concat(-y.width,"px"),border:eM,width:"".concat(y.width/d,"px"),height:"".concat(y.height/d,"px")}),this.__dragImageDisposer&&this.__dragImageDisposer(),this.__dragImageDisposer=()=>{y.remove()},iM(t.target||this.target.hiddenTextarea).body.appendChild(y),null===(i=t.dataTransfer)||void 0===i||i.setDragImage(y,p.x,p.y)}onDragStart(t){this.__dragStartFired=!0;let e=this.target,i=this.isActive();if(i&&t.dataTransfer){let i=this.__dragStartSelection={selectionStart:e.selectionStart,selectionEnd:e.selectionEnd},r=e._text.slice(i.selectionStart,i.selectionEnd).join(""),s=ee({text:e.text,value:r},i);t.dataTransfer.setData("text/plain",r),t.dataTransfer.setData("application/fabric",JSON.stringify({value:r,styles:e.getSelectionStyles(i.selectionStart,i.selectionEnd,!0)})),t.dataTransfer.effectAllowed="copyMove",this.setDragImage(t,s)}return e.abortCursorAnimation(),i}canDrop(t){if(this.target.editable&&!this.target.getActiveControl()&&!t.defaultPrevented){if(this.isActive()&&this.__dragStartSelection){let e=this.target.getSelectionStartFromPointer(t),i=this.__dragStartSelection;return e<i.selectionStart||e>i.selectionEnd}return!0}return!1}targetCanDrop(t){return this.target.canDrop(t)}dragEnterHandler(t){let{e:e}=t,i=this.targetCanDrop(e);!this.__isDraggingOver&&i&&(this.__isDraggingOver=!0)}dragOverHandler(t){let{e:e}=t,i=this.targetCanDrop(e);!this.__isDraggingOver&&i?this.__isDraggingOver=!0:this.__isDraggingOver&&!i&&(this.__isDraggingOver=!1),this.__isDraggingOver&&(e.preventDefault(),t.canDrop=!0,t.dropTarget=this.target)}dragLeaveHandler(){(this.__isDraggingOver||this.isActive())&&(this.__isDraggingOver=!1)}dropHandler(t){var e;let{e:i}=t,r=i.defaultPrevented;this.__isDraggingOver=!1,i.preventDefault();let s=null===(e=i.dataTransfer)||void 0===e?void 0:e.getData("text/plain");if(s&&!r){let e=this.target,r=e.canvas,n=e.getSelectionStartFromPointer(i),{styles:o}=i.dataTransfer.types.includes("application/fabric")?JSON.parse(i.dataTransfer.getData("application/fabric")):{},a=s[Math.max(0,s.length-1)];if(this.__dragStartSelection){let t=this.__dragStartSelection.selectionStart,i=this.__dragStartSelection.selectionEnd;n>t&&n<=i?n=t:n>i&&(n-=i-t),e.removeChars(t,i),delete this.__dragStartSelection}e._reNewline.test(a)&&(e._reNewline.test(e._text[n])||n===e._text.length)&&(s=s.trimEnd()),t.didDrop=!0,t.dropTarget=e,e.insertChars(s,o,n),r.setActiveObject(e),e.enterEditing(i),e.selectionStart=Math.min(n+0,e._text.length),e.selectionEnd=Math.min(e.selectionStart+s.length,e._text.length),e.hiddenTextarea.value=e.text,e._updateTextarea(),e.hiddenTextarea.focus(),e.fire("changed",{index:n+0,action:"drop"}),r.fire("text:changed",{target:e}),r.contextTopDirty=!0,r.requestRenderAll()}}dragEndHandler(t){let{e:e}=t;if(this.isActive()&&this.__dragStartFired&&this.__dragStartSelection){var i;let t=this.target,r=this.target.canvas,{selectionStart:s,selectionEnd:n}=this.__dragStartSelection,o=(null===(i=e.dataTransfer)||void 0===i?void 0:i.dropEffect)||eM;o===eM?(t.selectionStart=s,t.selectionEnd=n,t._updateTextarea(),t.hiddenTextarea.focus()):(t.clearContextTop(),"move"===o&&(t.removeChars(s,n),t.selectionStart=t.selectionEnd=s,t.hiddenTextarea&&(t.hiddenTextarea.value=t.text),t._updateTextarea(),t.fire("changed",{index:s,action:"dragend"}),r.fire("text:changed",{target:t}),r.requestRenderAll()),t.exitEditing())}this.__dragImageDisposer&&this.__dragImageDisposer(),delete this.__dragImageDisposer,delete this.__dragStartSelection,this.__isDraggingOver=!1}dispose(){this._dispose&&this._dispose()}}let oN=/[ \n\.,;!\?\-]/;class oV extends oI{constructor(){super(...arguments),ei(this,"_currentCursorOpacity",1)}initBehavior(){this._tick=this._tick.bind(this),this._onTickComplete=this._onTickComplete.bind(this),this.updateSelectionOnMouseMove=this.updateSelectionOnMouseMove.bind(this)}onDeselect(t){return this.isEditing&&this.exitEditing(),this.selected=!1,super.onDeselect(t)}_animateCursor(t){let{toValue:e,duration:i,delay:r,onComplete:s}=t;return rx({startValue:this._currentCursorOpacity,endValue:e,duration:i,delay:r,onComplete:s,abort:()=>!this.canvas||this.selectionStart!==this.selectionEnd,onChange:t=>{this._currentCursorOpacity=t,this.renderCursorOrSelection()}})}_tick(t){this._currentTickState=this._animateCursor({toValue:0,duration:this.cursorDuration/2,delay:Math.max(t||0,100),onComplete:this._onTickComplete})}_onTickComplete(){var t;null===(t=this._currentTickCompleteState)||void 0===t||t.abort(),this._currentTickCompleteState=this._animateCursor({toValue:1,duration:this.cursorDuration,onComplete:this._tick})}initDelayedCursor(t){this.abortCursorAnimation(),this._tick(t?0:this.cursorDelay)}abortCursorAnimation(){let t=!1;[this._currentTickState,this._currentTickCompleteState].forEach(e=>{e&&!e.isDone()&&(t=!0,e.abort())}),this._currentCursorOpacity=1,t&&this.clearContextTop()}restartCursorIfNeeded(){[this._currentTickState,this._currentTickCompleteState].some(t=>!t||t.isDone())&&this.initDelayedCursor()}selectAll(){return this.selectionStart=0,this.selectionEnd=this._text.length,this._fireSelectionChanged(),this._updateTextarea(),this}getSelectedText(){return this._text.slice(this.selectionStart,this.selectionEnd).join("")}findWordBoundaryLeft(t){let e=0,i=t-1;if(this._reSpace.test(this._text[i]))for(;this._reSpace.test(this._text[i]);)e++,i--;for(;/\S/.test(this._text[i])&&i>-1;)e++,i--;return t-e}findWordBoundaryRight(t){let e=0,i=t;if(this._reSpace.test(this._text[i]))for(;this._reSpace.test(this._text[i]);)e++,i++;for(;/\S/.test(this._text[i])&&i<this._text.length;)e++,i++;return t+e}findLineBoundaryLeft(t){let e=0,i=t-1;for(;!/\n/.test(this._text[i])&&i>-1;)e++,i--;return t-e}findLineBoundaryRight(t){let e=0,i=t;for(;!/\n/.test(this._text[i])&&i<this._text.length;)e++,i++;return t+e}searchWordBoundary(t,e){let i=this._text,r=t>0&&this._reSpace.test(i[t])&&(-1===e||!eE.test(i[t-1]))?t-1:t,s=i[r];for(;r>0&&r<i.length&&!oN.test(s);)r+=e,s=i[r];return -1===e&&oN.test(s)&&r++,r}selectWord(t){t=t||this.selectionStart;let e=this.searchWordBoundary(t,-1),i=Math.max(e,this.searchWordBoundary(t,1));this.selectionStart=e,this.selectionEnd=i,this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()}selectLine(t){t=t||this.selectionStart;let e=this.findLineBoundaryLeft(t),i=this.findLineBoundaryRight(t);return this.selectionStart=e,this.selectionEnd=i,this._fireSelectionChanged(),this._updateTextarea(),this}enterEditing(t){!this.isEditing&&this.editable&&(this.canvas&&(this.canvas.calcOffset(),this.canvas.textEditingManager.exitTextEditing()),this.isEditing=!0,this.initHiddenTextarea(),this.hiddenTextarea.focus(),this.hiddenTextarea.value=this.text,this._updateTextarea(),this._saveEditingProps(),this._setEditingProps(),this._textBeforeEdit=this.text,this._tick(),this.fire("editing:entered",t?{e:t}:void 0),this._fireSelectionChanged(),this.canvas&&(this.canvas.fire("text:editing:entered",{target:this,e:t}),this.canvas.requestRenderAll()))}updateSelectionOnMouseMove(t){if(this.getActiveControl())return;let e=this.hiddenTextarea;iM(e).activeElement!==e&&e.focus();let i=this.getSelectionStartFromPointer(t),r=this.selectionStart,s=this.selectionEnd;(i===this.__selectionStartOnMouseDown&&r!==s||r!==i&&s!==i)&&(i>this.__selectionStartOnMouseDown?(this.selectionStart=this.__selectionStartOnMouseDown,this.selectionEnd=i):(this.selectionStart=i,this.selectionEnd=this.__selectionStartOnMouseDown),this.selectionStart===r&&this.selectionEnd===s||(this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()))}_setEditingProps(){this.hoverCursor="text",this.canvas&&(this.canvas.defaultCursor=this.canvas.moveCursor="text"),this.borderColor=this.editingBorderColor,this.hasControls=this.selectable=!1,this.lockMovementX=this.lockMovementY=!0}fromStringToGraphemeSelection(t,e,i){let r=i.slice(0,t),s=this.graphemeSplit(r).length;if(t===e)return{selectionStart:s,selectionEnd:s};let n=i.slice(t,e);return{selectionStart:s,selectionEnd:s+this.graphemeSplit(n).length}}fromGraphemeToStringSelection(t,e,i){let r=i.slice(0,t).join("").length;return t===e?{selectionStart:r,selectionEnd:r}:{selectionStart:r,selectionEnd:r+i.slice(t,e).join("").length}}_updateTextarea(){if(this.cursorOffsetCache={},this.hiddenTextarea){if(!this.inCompositionMode){let t=this.fromGraphemeToStringSelection(this.selectionStart,this.selectionEnd,this._text);this.hiddenTextarea.selectionStart=t.selectionStart,this.hiddenTextarea.selectionEnd=t.selectionEnd}this.updateTextareaPosition()}}updateFromTextArea(){if(!this.hiddenTextarea)return;this.cursorOffsetCache={};let t=this.hiddenTextarea;this.text=t.value,this.set("dirty",!0),this.initDimensions(),this.setCoords();let e=this.fromStringToGraphemeSelection(t.selectionStart,t.selectionEnd,t.value);this.selectionEnd=this.selectionStart=e.selectionEnd,this.inCompositionMode||(this.selectionStart=e.selectionStart),this.updateTextareaPosition()}updateTextareaPosition(){if(this.selectionStart===this.selectionEnd){let t=this._calcTextareaPosition();this.hiddenTextarea.style.left=t.left,this.hiddenTextarea.style.top=t.top}}_calcTextareaPosition(){if(!this.canvas)return{left:"1px",top:"1px"};let t=this.inCompositionMode?this.compositionStart:this.selectionStart,e=this._getCursorBoundaries(t),i=this.get2DCursorLocation(t),r=i.lineIndex,s=i.charIndex,n=this.getValueOfPropertyAt(r,s,"fontSize")*this.lineHeight,o=e.leftOffset,a=this.getCanvasRetinaScaling(),l=this.canvas.upperCanvasEl,h=l.width/a,c=l.height/a,d=h-n,u=c-n,g=new eN(e.left+o,e.top+e.topOffset+n).transform(this.calcTransformMatrix()).transform(this.canvas.viewportTransform).multiply(new eN(l.clientWidth/h,l.clientHeight/c));return g.x<0&&(g.x=0),g.x>d&&(g.x=d),g.y<0&&(g.y=0),g.y>u&&(g.y=u),g.x+=this.canvas._offset.left,g.y+=this.canvas._offset.top,{left:"".concat(g.x,"px"),top:"".concat(g.y,"px"),fontSize:"".concat(n,"px"),charHeight:n}}_saveEditingProps(){this._savedProps={hasControls:this.hasControls,borderColor:this.borderColor,lockMovementX:this.lockMovementX,lockMovementY:this.lockMovementY,hoverCursor:this.hoverCursor,selectable:this.selectable,defaultCursor:this.canvas&&this.canvas.defaultCursor,moveCursor:this.canvas&&this.canvas.moveCursor}}_restoreEditingProps(){this._savedProps&&(this.hoverCursor=this._savedProps.hoverCursor,this.hasControls=this._savedProps.hasControls,this.borderColor=this._savedProps.borderColor,this.selectable=this._savedProps.selectable,this.lockMovementX=this._savedProps.lockMovementX,this.lockMovementY=this._savedProps.lockMovementY,this.canvas&&(this.canvas.defaultCursor=this._savedProps.defaultCursor||this.canvas.defaultCursor,this.canvas.moveCursor=this._savedProps.moveCursor||this.canvas.moveCursor),delete this._savedProps)}_exitEditing(){let t=this.hiddenTextarea;this.selected=!1,this.isEditing=!1,t&&(t.blur&&t.blur(),t.parentNode&&t.parentNode.removeChild(t)),this.hiddenTextarea=null,this.abortCursorAnimation(),this.selectionStart!==this.selectionEnd&&this.clearContextTop()}exitEditing(){let t=this._textBeforeEdit!==this.text;return this._exitEditing(),this.selectionEnd=this.selectionStart,this._restoreEditingProps(),this._forceClearCache&&(this.initDimensions(),this.setCoords()),this.fire("editing:exited"),t&&this.fire("modified"),this.canvas&&(this.canvas.fire("text:editing:exited",{target:this}),t&&this.canvas.fire("object:modified",{target:this})),this}_removeExtraneousStyles(){for(let t in this.styles)this._textLines[t]||delete this.styles[t]}removeStyleFromTo(t,e){let{lineIndex:i,charIndex:r}=this.get2DCursorLocation(t,!0),{lineIndex:s,charIndex:n}=this.get2DCursorLocation(e,!0);if(i!==s){if(this.styles[i])for(let t=r;t<this._unwrappedTextLines[i].length;t++)delete this.styles[i][t];if(this.styles[s])for(let t=n;t<this._unwrappedTextLines[s].length;t++){let e=this.styles[s][t];e&&(this.styles[i]||(this.styles[i]={}),this.styles[i][r+t-n]=e)}for(let t=i+1;t<=s;t++)delete this.styles[t];this.shiftLineStyles(s,i-s)}else if(this.styles[i]){let t=this.styles[i],e=n-r;for(let e=r;e<n;e++)delete t[e];for(let r in this.styles[i]){let i=parseInt(r,10);i>=n&&(t[i-e]=t[r],delete t[r])}}}shiftLineStyles(t,e){let i=Object.assign({},this.styles);for(let r in this.styles){let s=parseInt(r,10);s>t&&(this.styles[s+e]=i[s],i[s-e]||delete this.styles[s])}}insertNewlineStyleObject(t,e,i,r){let s={},n=this._unwrappedTextLines[t].length,o=n===e,a=!1;i||(i=1),this.shiftLineStyles(t,i);let l=this.styles[t]?this.styles[t][0===e?e:e-1]:void 0;for(let i in this.styles[t]){let r=parseInt(i,10);r>=e&&(a=!0,s[r-e]=this.styles[t][i],o&&0===e||delete this.styles[t][i])}let h=!1;for(a&&!o&&(this.styles[t+i]=s,h=!0),(h||n>e)&&i--;i>0;)r&&r[i-1]?this.styles[t+i]={0:ee({},r[i-1])}:l?this.styles[t+i]={0:ee({},l)}:delete this.styles[t+i],i--;this._forceClearCache=!0}insertCharStyleObject(t,e,i,r){this.styles||(this.styles={});let s=this.styles[t],n=s?ee({},s):{};for(let t in i||(i=1),n){let r=parseInt(t,10);r>=e&&(s[r+i]=n[r],n[r-i]||delete s[r])}if(this._forceClearCache=!0,r){for(;i--;)Object.keys(r[i]).length&&(this.styles[t]||(this.styles[t]={}),this.styles[t][e+i]=ee({},r[i]));return}if(!s)return;let o=s[e?e-1:1];for(;o&&i--;)this.styles[t][e+i]=ee({},o)}insertNewStyleBlock(t,e,i){let r=this.get2DCursorLocation(e,!0),s=[0],n,o=0;for(let e=0;e<t.length;e++)"\n"===t[e]?s[++o]=0:s[o]++;for(s[0]>0&&(this.insertCharStyleObject(r.lineIndex,r.charIndex,s[0],i),i=i&&i.slice(s[0]+1)),o&&this.insertNewlineStyleObject(r.lineIndex,r.charIndex+s[0],o),n=1;n<o;n++)s[n]>0?this.insertCharStyleObject(r.lineIndex+n,0,s[n],i):i&&this.styles[r.lineIndex+n]&&i[0]&&(this.styles[r.lineIndex+n][0]=i[0]),i=i&&i.slice(s[n]+1);s[n]>0&&this.insertCharStyleObject(r.lineIndex+n,0,s[n],i)}removeChars(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t+1;this.removeStyleFromTo(t,e),this._text.splice(t,e-t),this.text=this._text.join(""),this.set("dirty",!0),this.initDimensions(),this.setCoords(),this._removeExtraneousStyles()}insertChars(t,e,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:i;r>i&&this.removeStyleFromTo(i,r);let s=this.graphemeSplit(t);this.insertNewStyleBlock(s,i,e),this._text=[...this._text.slice(0,i),...s,...this._text.slice(r)],this.text=this._text.join(""),this.set("dirty",!0),this.initDimensions(),this.setCoords(),this._removeExtraneousStyles()}setSelectionStartEndWithShift(t,e,i){i<=t?(e===t?this._selectionDirection=eO:this._selectionDirection===ej&&(this._selectionDirection=eO,this.selectionEnd=t),this.selectionStart=i):i>t&&i<e?this._selectionDirection===ej?this.selectionEnd=i:this.selectionStart=i:(e===t?this._selectionDirection=ej:this._selectionDirection===eO&&(this._selectionDirection=ej,this.selectionStart=e),this.selectionEnd=i)}}class oX extends oV{initHiddenTextarea(){let t=this.canvas&&iM(this.canvas.getElement())||ep(),e=t.createElement("textarea");Object.entries({autocapitalize:"off",autocorrect:"off",autocomplete:"off",spellcheck:"false","data-fabric":"textarea",wrap:"off"}).map(t=>{let[i,r]=t;return e.setAttribute(i,r)});let{top:i,left:r,fontSize:s}=this._calcTextareaPosition();e.style.cssText="position: absolute; top: ".concat(i,"; left: ").concat(r,"; z-index: -999; opacity: 0; width: 1px; height: 1px; font-size: 1px; padding-top: ").concat(s,";"),(this.hiddenTextareaContainer||t.body).appendChild(e),Object.entries({blur:"blur",keydown:"onKeyDown",keyup:"onKeyUp",input:"onInput",copy:"copy",cut:"copy",paste:"paste",compositionstart:"onCompositionStart",compositionupdate:"onCompositionUpdate",compositionend:"onCompositionEnd"}).map(t=>{let[i,r]=t;return e.addEventListener(i,this[r].bind(this))}),this.hiddenTextarea=e}blur(){this.abortCursorAnimation()}onKeyDown(t){if(!this.isEditing)return;let e="rtl"===this.direction?this.keysMapRtl:this.keysMap;if(t.keyCode in e)this[e[t.keyCode]](t);else{if(!(t.keyCode in this.ctrlKeysMapDown)||!t.ctrlKey&&!t.metaKey)return;this[this.ctrlKeysMapDown[t.keyCode]](t)}t.stopImmediatePropagation(),t.preventDefault(),t.keyCode>=33&&t.keyCode<=40?(this.inCompositionMode=!1,this.clearContextTop(),this.renderCursorOrSelection()):this.canvas&&this.canvas.requestRenderAll()}onKeyUp(t){!this.isEditing||this._copyDone||this.inCompositionMode?this._copyDone=!1:t.keyCode in this.ctrlKeysMapUp&&(t.ctrlKey||t.metaKey)&&(this[this.ctrlKeysMapUp[t.keyCode]](t),t.stopImmediatePropagation(),t.preventDefault(),this.canvas&&this.canvas.requestRenderAll())}onInput(t){let e=this.fromPaste;if(this.fromPaste=!1,t&&t.stopPropagation(),!this.isEditing)return;let i=()=>{this.updateFromTextArea(),this.fire("changed"),this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll())};if(""===this.hiddenTextarea.value)return this.styles={},void i();let r=this._splitTextIntoLines(this.hiddenTextarea.value).graphemeText,s=this._text.length,n=r.length,o=this.selectionStart,a=this.selectionEnd,l=o!==a,h,c,d,u,g=n-s,f=this.fromStringToGraphemeSelection(this.hiddenTextarea.selectionStart,this.hiddenTextarea.selectionEnd,this.hiddenTextarea.value),p=o>f.selectionStart;l?(c=this._text.slice(o,a),g+=a-o):n<s&&(c=p?this._text.slice(a+g,a):this._text.slice(o,o-g));let v=r.slice(f.selectionEnd-g,f.selectionEnd);if(c&&c.length&&(v.length&&(h=this.getSelectionStyles(o,o+1,!1),h=v.map(()=>h[0])),l?(d=o,u=a):p?(d=a-c.length,u=a):(d=a,u=a+c.length),this.removeStyleFromTo(d,u)),v.length){let{copyPasteData:t}=ef();e&&v.join("")===t.copiedText&&!ea.disableStyleCopyPaste&&(h=t.copiedTextStyle),this.insertNewStyleBlock(v,o,h)}i()}onCompositionStart(){this.inCompositionMode=!0}onCompositionEnd(){this.inCompositionMode=!1}onCompositionUpdate(t){let{target:e}=t,{selectionStart:i,selectionEnd:r}=e;this.compositionStart=i,this.compositionEnd=r,this.updateTextareaPosition()}copy(){if(this.selectionStart===this.selectionEnd)return;let{copyPasteData:t}=ef();t.copiedText=this.getSelectedText(),ea.disableStyleCopyPaste?t.copiedTextStyle=void 0:t.copiedTextStyle=this.getSelectionStyles(this.selectionStart,this.selectionEnd,!0),this._copyDone=!0}paste(){this.fromPaste=!0}_getWidthBeforeCursor(t,e){let i,r=this._getLineLeftOffset(t);return e>0&&(r+=(i=this.__charBounds[t][e-1]).left+i.width),r}getDownCursorOffset(t,e){let i=this._getSelectionForOffset(t,e),r=this.get2DCursorLocation(i),s=r.lineIndex;if(s===this._textLines.length-1||t.metaKey||34===t.keyCode)return this._text.length-i;let n=r.charIndex,o=this._getWidthBeforeCursor(s,n),a=this._getIndexOnLine(s+1,o);return this._textLines[s].slice(n).length+a+1+this.missingNewlineOffset(s)}_getSelectionForOffset(t,e){return t.shiftKey&&this.selectionStart!==this.selectionEnd&&e?this.selectionEnd:this.selectionStart}getUpCursorOffset(t,e){let i=this._getSelectionForOffset(t,e),r=this.get2DCursorLocation(i),s=r.lineIndex;if(0===s||t.metaKey||33===t.keyCode)return-i;let n=r.charIndex,o=this._getWidthBeforeCursor(s,n),a=this._getIndexOnLine(s-1,o),l=this._textLines[s].slice(0,n),h=this.missingNewlineOffset(s-1);return-this._textLines[s-1].length+a-l.length+(1-h)}_getIndexOnLine(t,e){let i=this._textLines[t],r,s,n=this._getLineLeftOffset(t),o=0;for(let a=0,l=i.length;a<l;a++)if((n+=r=this.__charBounds[t][a].width)>e){s=!0;let t=Math.abs(n-r-e);o=Math.abs(n-e)<t?a:a-1;break}return s||(o=i.length-1),o}moveCursorDown(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorUpOrDown("Down",t)}moveCursorUp(t){0===this.selectionStart&&0===this.selectionEnd||this._moveCursorUpOrDown("Up",t)}_moveCursorUpOrDown(t,e){let i=this["get".concat(t,"CursorOffset")](e,this._selectionDirection===ej);if(e.shiftKey?this.moveCursorWithShift(i):this.moveCursorWithoutShift(i),0!==i){let t=this.text.length;this.selectionStart=rg(0,this.selectionStart,t),this.selectionEnd=rg(0,this.selectionEnd,t),this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea()}}moveCursorWithShift(t){let e=this._selectionDirection===eO?this.selectionStart+t:this.selectionEnd+t;return this.setSelectionStartEndWithShift(this.selectionStart,this.selectionEnd,e),0!==t}moveCursorWithoutShift(t){return t<0?(this.selectionStart+=t,this.selectionEnd=this.selectionStart):(this.selectionEnd+=t,this.selectionStart=this.selectionEnd),0!==t}moveCursorLeft(t){0===this.selectionStart&&0===this.selectionEnd||this._moveCursorLeftOrRight("Left",t)}_move(t,e,i){let r;if(t.altKey)r=this["findWordBoundary".concat(i)](this[e]);else{if(!t.metaKey&&35!==t.keyCode&&36!==t.keyCode)return this[e]+="Left"===i?-1:1,!0;r=this["findLineBoundary".concat(i)](this[e])}return void 0!==r&&this[e]!==r&&(this[e]=r,!0)}_moveLeft(t,e){return this._move(t,e,"Left")}_moveRight(t,e){return this._move(t,e,"Right")}moveCursorLeftWithoutShift(t){let e=!0;return this._selectionDirection=eO,this.selectionEnd===this.selectionStart&&0!==this.selectionStart&&(e=this._moveLeft(t,"selectionStart")),this.selectionEnd=this.selectionStart,e}moveCursorLeftWithShift(t){return this._selectionDirection===ej&&this.selectionStart!==this.selectionEnd?this._moveLeft(t,"selectionEnd"):0!==this.selectionStart?(this._selectionDirection=eO,this._moveLeft(t,"selectionStart")):void 0}moveCursorRight(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorLeftOrRight("Right",t)}_moveCursorLeftOrRight(t,e){let i="moveCursor".concat(t).concat(e.shiftKey?"WithShift":"WithoutShift");this._currentCursorOpacity=1,this[i](e)&&(this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())}moveCursorRightWithShift(t){return this._selectionDirection===eO&&this.selectionStart!==this.selectionEnd?this._moveRight(t,"selectionStart"):this.selectionEnd!==this._text.length?(this._selectionDirection=ej,this._moveRight(t,"selectionEnd")):void 0}moveCursorRightWithoutShift(t){let e=!0;return this._selectionDirection=ej,this.selectionStart===this.selectionEnd?(e=this._moveRight(t,"selectionStart"),this.selectionEnd=this.selectionStart):this.selectionStart=this.selectionEnd,e}}let oz=t=>!!t.button;class oW extends oX{constructor(){super(...arguments),ei(this,"draggableTextDelegate",void 0)}initBehavior(){this.on("mousedown",this._mouseDownHandler),this.on("mousedown:before",this._mouseDownHandlerBefore),this.on("mouseup",this.mouseUpHandler),this.on("mousedblclick",this.doubleClickHandler),this.on("tripleclick",this.tripleClickHandler),this.__lastClickTime=+new Date,this.__lastLastClickTime=+new Date,this.__lastPointer={},this.on("mousedown",this.onMouseDown),this.draggableTextDelegate=new oB(this),super.initBehavior()}shouldStartDragging(){return this.draggableTextDelegate.isActive()}onDragStart(t){return this.draggableTextDelegate.onDragStart(t)}canDrop(t){return this.draggableTextDelegate.canDrop(t)}onMouseDown(t){if(!this.canvas)return;this.__newClickTime=+new Date;let e=t.pointer;this.isTripleClick(e)&&(this.fire("tripleclick",t),iX(t.e)),this.__lastLastClickTime=this.__lastClickTime,this.__lastClickTime=this.__newClickTime,this.__lastPointer=e,this.__lastSelected=this.selected&&!this.getActiveControl()}isTripleClick(t){return this.__newClickTime-this.__lastClickTime<500&&this.__lastClickTime-this.__lastLastClickTime<500&&this.__lastPointer.x===t.x&&this.__lastPointer.y===t.y}doubleClickHandler(t){this.isEditing&&this.selectWord(this.getSelectionStartFromPointer(t.e))}tripleClickHandler(t){this.isEditing&&this.selectLine(this.getSelectionStartFromPointer(t.e))}_mouseDownHandler(t){let{e:e}=t;this.canvas&&this.editable&&!oz(e)&&!this.getActiveControl()&&(this.draggableTextDelegate.start(e)||(this.canvas.textEditingManager.register(this),this.selected&&(this.inCompositionMode=!1,this.setCursorByClick(e)),this.isEditing&&(this.__selectionStartOnMouseDown=this.selectionStart,this.selectionStart===this.selectionEnd&&this.abortCursorAnimation(),this.renderCursorOrSelection())))}_mouseDownHandlerBefore(t){let{e:e}=t;this.canvas&&this.editable&&!oz(e)&&(this.selected=this===this.canvas._activeObject)}mouseUpHandler(t){let{e:e,transform:i}=t,r=this.draggableTextDelegate.end(e);if(this.canvas){this.canvas.textEditingManager.unregister(this);let t=this.canvas._activeObject;if(t&&t!==this)return}!this.editable||this.group&&!this.group.interactive||i&&i.actionPerformed||oz(e)||r||(this.__lastSelected&&!this.getActiveControl()?(this.selected=!1,this.__lastSelected=!1,this.enterEditing(e),this.selectionStart===this.selectionEnd?this.initDelayedCursor(!0):this.renderCursorOrSelection()):this.selected=!0)}setCursorByClick(t){let e=this.getSelectionStartFromPointer(t),i=this.selectionStart,r=this.selectionEnd;t.shiftKey?this.setSelectionStartEndWithShift(i,r,e):(this.selectionStart=e,this.selectionEnd=e),this.isEditing&&(this._fireSelectionChanged(),this._updateTextarea())}getSelectionStartFromPointer(t){let e=this.canvas.getScenePoint(t).transform(e1(this.calcTransformMatrix())).add(new eN(-this._getLeftOffset(),-this._getTopOffset())),i=0,r=0,s=0;for(let t=0;t<this._textLines.length&&i<=e.y;t++)i+=this.getHeightOfLine(t),s=t,t>0&&(r+=this._textLines[t-1].length+this.missingNewlineOffset(t-1));let n=Math.abs(this._getLineLeftOffset(s)),o=this._textLines[s].length,a=this.__charBounds[s];for(let t=0;t<o;t++){let i=n+a[t].kernedWidth;if(e.x<=i){Math.abs(e.x-i)<=Math.abs(e.x-n)&&r++;break}n=i,r++}return Math.min(this.flipX?o-r:r,this._text.length)}}let oY="moveCursorUp",oH="moveCursorDown",oG="moveCursorLeft",oU="moveCursorRight",oZ="exitEditing",oq=ee({selectionStart:0,selectionEnd:0,selectionColor:"rgba(17,119,255,0.3)",isEditing:!1,editable:!0,editingBorderColor:"rgba(102,153,255,0.25)",cursorWidth:2,cursorColor:"",cursorDelay:1e3,cursorDuration:600,caching:!0,hiddenTextareaContainer:null,keysMap:{9:oZ,27:oZ,33:oY,34:oH,35:oU,36:oG,37:oG,38:oY,39:oU,40:oH},keysMapRtl:{9:oZ,27:oZ,33:oY,34:oH,35:oG,36:oU,37:oU,38:oY,39:oG,40:oH},ctrlKeysMapDown:{65:"selectAll"},ctrlKeysMapUp:{67:"copy",88:"cut"}},{_selectionDirection:null,_reSpace:/\s|\r?\n/,inCompositionMode:!1});class oK extends oW{static getDefaults(){return ee(ee({},super.getDefaults()),oK.ownDefaults)}get type(){let t=super.type;return"itext"===t?"i-text":t}constructor(t,e){super(t,ee(ee({},oK.ownDefaults),e)),this.initBehavior()}_set(t,e){return this.isEditing&&this._savedProps&&t in this._savedProps?(this._savedProps[t]=e,this):("canvas"===t&&(this.canvas instanceof n0&&this.canvas.textEditingManager.remove(this),e instanceof n0&&e.textEditingManager.add(this)),super._set(t,e))}setSelectionStart(t){t=Math.max(t,0),this._updateAndFire("selectionStart",t)}setSelectionEnd(t){t=Math.min(t,this.text.length),this._updateAndFire("selectionEnd",t)}_updateAndFire(t,e){this[t]!==e&&(this._fireSelectionChanged(),this[t]=e),this._updateTextarea()}_fireSelectionChanged(){this.fire("selection:changed"),this.canvas&&this.canvas.fire("text:selection:changed",{target:this})}initDimensions(){this.isEditing&&this.initDelayedCursor(),super.initDimensions()}getSelectionStyles(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.selectionStart||0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selectionEnd,i=arguments.length>2?arguments[2]:void 0;return super.getSelectionStyles(t,e,i)}setSelectionStyles(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selectionStart||0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.selectionEnd;return super.setSelectionStyles(t,e,i)}get2DCursorLocation(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.selectionStart,e=arguments.length>1?arguments[1]:void 0;return super.get2DCursorLocation(t,e)}render(t){super.render(t),this.cursorOffsetCache={},this.renderCursorOrSelection()}toCanvasElement(t){let e=this.isEditing;this.isEditing=!1;let i=super.toCanvasElement(t);return this.isEditing=e,i}renderCursorOrSelection(){if(!this.isEditing)return;let t=this.clearContextTop(!0);if(!t)return;let e=this._getCursorBoundaries();this.selectionStart===this.selectionEnd?this.renderCursor(t,e):this.renderSelection(t,e),this.canvas.contextTopDirty=!0,t.restore()}_getCursorBoundaries(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.selectionStart,e=arguments.length>1?arguments[1]:void 0,i=this._getLeftOffset(),r=this._getTopOffset(),s=this._getCursorBoundariesOffsets(t,e);return{left:i,top:r,leftOffset:s.left,topOffset:s.top}}_getCursorBoundariesOffsets(t,e){return e?this.__getCursorBoundariesOffsets(t):this.cursorOffsetCache&&"top"in this.cursorOffsetCache?this.cursorOffsetCache:this.cursorOffsetCache=this.__getCursorBoundariesOffsets(t)}__getCursorBoundariesOffsets(t){let e=0,i=0,{charIndex:r,lineIndex:s}=this.get2DCursorLocation(t);for(let t=0;t<s;t++)e+=this.getHeightOfLine(t);let n=this._getLineLeftOffset(s),o=this.__charBounds[s][r];o&&(i=o.left),0!==this.charSpacing&&r===this._textLines[s].length&&(i-=this._getWidthOfCharSpacing());let a={top:e,left:n+(i>0?i:0)};return"rtl"===this.direction&&(this.textAlign===ej||this.textAlign===oj||this.textAlign===oE?a.left*=-1:this.textAlign===eO||this.textAlign===oM?a.left=n-(i>0?i:0):this.textAlign!==eT&&this.textAlign!==oD||(a.left=n-(i>0?i:0))),a}renderCursorAt(t){let e=this._getCursorBoundaries(t,!0);this._renderCursor(this.canvas.contextTop,e,t)}renderCursor(t,e){this._renderCursor(t,e,this.selectionStart)}_renderCursor(t,e,i){let r=this.get2DCursorLocation(i),s=r.lineIndex,n=r.charIndex>0?r.charIndex-1:0,o=this.getValueOfPropertyAt(s,n,"fontSize"),a=this.getObjectScaling().x*this.canvas.getZoom(),l=this.cursorWidth/a,h=this.getValueOfPropertyAt(s,n,"deltaY"),c=e.topOffset+(1-this._fontSizeFraction)*this.getHeightOfLine(s)/this.lineHeight-o*(1-this._fontSizeFraction);this.inCompositionMode&&this.renderSelection(t,e),t.fillStyle=this.cursorColor||this.getValueOfPropertyAt(s,n,"fill"),t.globalAlpha=this._currentCursorOpacity,t.fillRect(e.left+e.leftOffset-l/2,c+e.top+h,l,o)}renderSelection(t,e){let i={selectionStart:this.inCompositionMode?this.hiddenTextarea.selectionStart:this.selectionStart,selectionEnd:this.inCompositionMode?this.hiddenTextarea.selectionEnd:this.selectionEnd};this._renderSelection(t,i,e)}renderDragSourceEffect(){let t=this.draggableTextDelegate.getDragStartSelection();this._renderSelection(this.canvas.contextTop,t,this._getCursorBoundaries(t.selectionStart,!0))}renderDropTargetEffect(t){let e=this.getSelectionStartFromPointer(t);this.renderCursorAt(e)}_renderSelection(t,e,i){let r=e.selectionStart,s=e.selectionEnd,n=this.textAlign.includes(oj),o=this.get2DCursorLocation(r),a=this.get2DCursorLocation(s),l=o.lineIndex,h=a.lineIndex,c=o.charIndex<0?0:o.charIndex,d=a.charIndex<0?0:a.charIndex;for(let e=l;e<=h;e++){let r=this._getLineLeftOffset(e)||0,s=this.getHeightOfLine(e),o=0,a=0,u=0;if(e===l&&(a=this.__charBounds[l][c].left),e>=l&&e<h)u=n&&!this.isEndOfWrapping(e)?this.width:this.getLineWidth(e)||5;else if(e===h){if(0===d)u=this.__charBounds[h][d].left;else{let t=this._getWidthOfCharSpacing();u=this.__charBounds[h][d-1].left+this.__charBounds[h][d-1].width-t}}o=s,(this.lineHeight<1||e===h&&this.lineHeight>1)&&(s/=this.lineHeight);let g=i.left+r+a,f=s,p=0,v=u-a;this.inCompositionMode?(t.fillStyle=this.compositionColor||"black",f=1,p=s):t.fillStyle=this.selectionColor,"rtl"===this.direction&&(this.textAlign===ej||this.textAlign===oj||this.textAlign===oE?g=this.width-g-v:this.textAlign===eO||this.textAlign===oM?g=i.left+r-u:this.textAlign!==eT&&this.textAlign!==oD||(g=i.left+r-u)),t.fillRect(g,i.top+i.topOffset+p,v,f),i.topOffset+=o}}getCurrentCharFontSize(){let t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,"fontSize")}getCurrentCharColor(){let t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,"fill")}_getCurrentCharIndex(){let t=this.get2DCursorLocation(this.selectionStart,!0),e=t.charIndex>0?t.charIndex-1:0;return{l:t.lineIndex,c:e}}dispose(){this._exitEditing(),this.draggableTextDelegate.dispose(),super.dispose()}}ei(oK,"ownDefaults",oq),ei(oK,"type","IText"),eA.setClass(oK),eA.setClass(oK,"i-text");class oJ extends oK{static getDefaults(){return ee(ee({},super.getDefaults()),oJ.ownDefaults)}constructor(t,e){super(t,ee(ee({},oJ.ownDefaults),e))}static createControls(){return{controls:sy()}}initDimensions(){this.initialized&&(this.isEditing&&this.initDelayedCursor(),this._clearCache(),this.dynamicMinWidth=0,this._styleMap=this._generateStyleMap(this._splitText()),this.dynamicMinWidth>this.width&&this._set("width",this.dynamicMinWidth),this.textAlign.includes(oj)&&this.enlargeSpaces(),this.height=this.calcTextHeight())}_generateStyleMap(t){let e=0,i=0,r=0,s={};for(let n=0;n<t.graphemeLines.length;n++)"\n"===t.graphemeText[r]&&n>0?(i=0,r++,e++):!this.splitByGrapheme&&this._reSpaceAndTab.test(t.graphemeText[r])&&n>0&&(i++,r++),s[n]={line:e,offset:i},r+=t.graphemeLines[n].length,i+=t.graphemeLines[n].length;return s}styleHas(t,e){if(this._styleMap&&!this.isWrapping){let t=this._styleMap[e];t&&(e=t.line)}return super.styleHas(t,e)}isEmptyStyles(t){if(!this.styles)return!0;let e,i=0,r=t+1,s=!1,n=this._styleMap[t],o=this._styleMap[t+1];n&&(t=n.line,i=n.offset),o&&(s=(r=o.line)===t,e=o.offset);let a=void 0===t?this.styles:{line:this.styles[t]};for(let t in a)for(let r in a[t]){let n=parseInt(r,10);if(n>=i&&(!s||n<e))for(let e in a[t][r])return!1}return!0}_getStyleDeclaration(t,e){if(this._styleMap&&!this.isWrapping){let i=this._styleMap[t];if(!i)return{};t=i.line,e=i.offset+e}return super._getStyleDeclaration(t,e)}_setStyleDeclaration(t,e,i){let r=this._styleMap[t];super._setStyleDeclaration(r.line,r.offset+e,i)}_deleteStyleDeclaration(t,e){let i=this._styleMap[t];super._deleteStyleDeclaration(i.line,i.offset+e)}_getLineStyle(t){let e=this._styleMap[t];return!!this.styles[e.line]}_setLineStyle(t){let e=this._styleMap[t];super._setLineStyle(e.line)}_wrapText(t,e){this.isWrapping=!0;let i=this.getGraphemeDataForRender(t),r=[];for(let t=0;t<i.wordsData.length;t++)r.push(...this._wrapLine(t,e,i));return this.isWrapping=!1,r}getGraphemeDataForRender(t){let e=this.splitByGrapheme,i=e?"":" ",r=0;return{wordsData:t.map((t,s)=>{let n=0,o=e?this.graphemeSplit(t):this.wordSplit(t);return 0===o.length?[{word:[],width:0}]:o.map(t=>{let o=e?[t]:this.graphemeSplit(t),a=this._measureWord(o,s,n);return r=Math.max(a,r),n+=o.length+i.length,{word:o,width:a}})}),largestWordWidth:r}}_measureWord(t,e){let i,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=0;for(let n=0,o=t.length;n<o;n++)s+=this._getGraphemeBox(t[n],e,n+r,i,!0).kernedWidth,i=t[n];return s}wordSplit(t){return t.split(this._wordJoiners)}_wrapLine(t,e,i){let r,{largestWordWidth:s,wordsData:n}=i,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=this._getWidthOfCharSpacing(),l=this.splitByGrapheme,h=[],c=l?"":" ",d=0,u=[],g=0,f=0,p=!0,v=Math.max(e-=o,s,this.dynamicMinWidth),m=n[t];for(g=0,r=0;r<m.length;r++){let{word:e,width:i}=m[r];g+=e.length,(d+=f+i-a)>v&&!p?(h.push(u),u=[],d=i,p=!0):d+=a,p||l||u.push(c),u=u.concat(e),f=l?0:this._measureWord([c],t,g),g++,p=!1}return r&&h.push(u),s+o>this.dynamicMinWidth&&(this.dynamicMinWidth=s-a+o),h}isEndOfWrapping(t){return!this._styleMap[t+1]||this._styleMap[t+1].line!==this._styleMap[t].line}missingNewlineOffset(t,e){return this.splitByGrapheme&&!e?this.isEndOfWrapping(t)?1:0:1}_splitTextIntoLines(t){let e=super._splitTextIntoLines(t),i=this._wrapText(e.lines,this.width),r=Array(i.length);for(let t=0;t<i.length;t++)r[t]=i[t].join("");return e.lines=r,e.graphemeLines=i,e}getMinWidth(){return Math.max(this.minWidth,this.dynamicMinWidth)}_removeExtraneousStyles(){let t=new Map;for(let e in this._styleMap){let i=parseInt(e,10);if(this._textLines[i]){let i=this._styleMap[e].line;t.set("".concat(i),!0)}}for(let e in this.styles)t.has(e)||delete this.styles[e]}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return super.toObject(["minWidth","splitByGrapheme",...t])}}ei(oJ,"type","Textbox"),ei(oJ,"textLayoutProperties",[...oK.textLayoutProperties,"width"]),ei(oJ,"ownDefaults",{minWidth:20,dynamicMinWidth:2,lockScalingFlip:!0,noScaleCache:!1,_wordJoiners:/[ \t\r]/,splitByGrapheme:!1}),eA.setClass(oJ);class oQ extends s7{shouldPerformLayout(t){return!!t.target.clipPath&&super.shouldPerformLayout(t)}shouldLayoutClipPath(){return!1}calcLayoutResult(t,e){let{target:i}=t,{clipPath:r}=i;if(!r||!this.shouldPerformLayout(t))return;let{width:s,height:n}=iz(s9(i,r)),o=new eN(s,n);if(r.absolutePositioned){var a;return{center:iK(r.getRelativeCenterPoint(),void 0,null===(a=i.group)||void 0===a?void 0:a.calcTransformMatrix()),size:o}}{let s=r.getRelativeCenterPoint().transform(i.calcOwnMatrix(),!0);if(this.shouldPerformLayout(t)){let{center:i=new eN,correction:r=new eN}=this.calcBoundingBox(e,t)||{};return{center:i.add(s),correction:r.subtract(s),size:o}}return{center:i.getRelativeCenterPoint().add(s),size:o}}}}ei(oQ,"type","clip-path"),eA.setClass(oQ);class o$ extends s7{getInitialSize(t,e){let{target:i}=t,{size:r}=e;return new eN(i.width||r.x,i.height||r.y)}}ei(o$,"type","fixed"),eA.setClass(o$);class o0 extends ns{subscribeTargets(t){let e=t.target;t.targets.reduce((t,e)=>(e.parent&&t.add(e.parent),t),new Set).forEach(t=>{t.layoutManager.subscribeTargets({target:t,targets:[e]})})}unsubscribeTargets(t){let e=t.target,i=e.getObjects();t.targets.reduce((t,e)=>(e.parent&&t.add(e.parent),t),new Set).forEach(t=>{i.some(e=>e.parent===t)||t.layoutManager.unsubscribeTargets({target:t,targets:[e]})})}}let o1=["layoutManager"];class o2 extends na{static getDefaults(){return ee(ee({},super.getDefaults()),o2.ownDefaults)}constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{layoutManager:i}=e,r=er(e,o1);super(t,{layoutManager:null!=i?i:new o0}),Object.assign(this,o2.ownDefaults),this.setOptions(r)}_shouldSetNestedCoords(){return!0}__objectSelectionMonitor(){}multiSelectAdd(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];"selection-order"===this.multiSelectionStacking?this.add(...e):e.forEach(t=>{let e=this._objects.findIndex(e=>e.isInFrontOf(t)),i=-1===e?this.size():e;this.insertAt(i,t)})}canEnterGroup(t){return this.getObjects().some(e=>e.isDescendantOf(t)||t.isDescendantOf(e))?(el("error","ActiveSelection: circular object trees are not supported, this call has no effect"),!1):super.canEnterGroup(t)}enterGroup(t,e){t.parent&&t.parent===t.group?t.parent._exitGroup(t):t.group&&t.parent!==t.group&&t.group.remove(t),this._enterGroup(t,e)}exitGroup(t,e){this._exitGroup(t,e),t.parent&&t.parent._enterGroup(t,!0)}_onAfterObjectsChange(t,e){super._onAfterObjectsChange(t,e);let i=new Set;e.forEach(t=>{let{parent:e}=t;e&&i.add(e)}),t===s8?i.forEach(t=>{t._onAfterObjectsChange(s5,e)}):i.forEach(t=>{t._set("dirty",!0)})}onDeselect(){return this.removeAll(),!1}toString(){return"#<ActiveSelection: (".concat(this.complexity(),")>")}shouldCache(){return!1}isOnACache(){return!1}_renderControls(t,e,i){t.save(),t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1,super._renderControls(t,e);let r=ee(ee({hasControls:!1},i),{},{forActiveSelection:!0});for(let e=0;e<this._objects.length;e++)this._objects[e]._renderControls(t,r);t.restore()}}ei(o2,"type","ActiveSelection"),ei(o2,"ownDefaults",{multiSelectionStacking:"canvas-stacking"}),eA.setClass(o2),eA.setClass(o2,"activeSelection");class o3{constructor(){ei(this,"resources",{})}applyFilters(t,e,i,r,s){let n=s.getContext("2d");if(!n)return;n.drawImage(e,0,0,i,r);let o={sourceWidth:i,sourceHeight:r,imageData:n.getImageData(0,0,i,r),originalEl:e,originalImageData:n.getImageData(0,0,i,r),canvasEl:s,ctx:n,filterBackend:this};t.forEach(t=>{t.applyTo(o)});let{imageData:a}=o;return a.width===i&&a.height===r||(s.width=a.width,s.height=a.height),n.putImageData(a,0,0),o}}class o6{constructor(){let{tileSize:t=ea.textureSize}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};ei(this,"aPosition",new Float32Array([0,0,0,1,1,0,1,1])),ei(this,"resources",{}),this.tileSize=t,this.setupGLContext(t,t),this.captureGPUInfo()}setupGLContext(t,e){this.dispose(),this.createWebGLCanvas(t,e)}createWebGLCanvas(t,e){let i=eZ();i.width=t,i.height=e;let r=i.getContext("webgl",{alpha:!0,premultipliedAlpha:!1,depth:!1,stencil:!1,antialias:!1});r&&(r.clearColor(0,0,0,0),this.canvas=i,this.gl=r)}applyFilters(t,e,i,r,s,n){let o;let a=this.gl,l=s.getContext("2d");if(!a||!l)return;n&&(o=this.getCachedTexture(n,e));let h={originalWidth:e.width||e.originalWidth||0,originalHeight:e.height||e.originalHeight||0,sourceWidth:i,sourceHeight:r,destinationWidth:i,destinationHeight:r,context:a,sourceTexture:this.createTexture(a,i,r,o?void 0:e),targetTexture:this.createTexture(a,i,r),originalTexture:o||this.createTexture(a,i,r,o?void 0:e),passes:t.length,webgl:!0,aPosition:this.aPosition,programCache:this.programCache,pass:0,filterBackend:this,targetCanvas:s},c=a.createFramebuffer();return a.bindFramebuffer(a.FRAMEBUFFER,c),t.forEach(t=>{t&&t.applyTo(h)}),function(t){let e=t.targetCanvas,i=e.width,r=e.height,s=t.destinationWidth,n=t.destinationHeight;i===s&&r===n||(e.width=s,e.height=n)}(h),this.copyGLTo2D(a,h),a.bindTexture(a.TEXTURE_2D,null),a.deleteTexture(h.sourceTexture),a.deleteTexture(h.targetTexture),a.deleteFramebuffer(c),l.setTransform(1,0,0,1,0,0),h}dispose(){this.canvas&&(this.canvas=null,this.gl=null),this.clearWebGLCaches()}clearWebGLCaches(){this.programCache={},this.textureCache={}}createTexture(t,e,i,r,s){let{NEAREST:n,TEXTURE_2D:o,RGBA:a,UNSIGNED_BYTE:l,CLAMP_TO_EDGE:h,TEXTURE_MAG_FILTER:c,TEXTURE_MIN_FILTER:d,TEXTURE_WRAP_S:u,TEXTURE_WRAP_T:g}=t,f=t.createTexture();return t.bindTexture(o,f),t.texParameteri(o,c,s||n),t.texParameteri(o,d,s||n),t.texParameteri(o,u,h),t.texParameteri(o,g,h),r?t.texImage2D(o,0,a,a,l,r):t.texImage2D(o,0,a,e,i,0,a,l,null),f}getCachedTexture(t,e,i){let{textureCache:r}=this;if(r[t])return r[t];{let s=this.createTexture(this.gl,e.width,e.height,e,i);return s&&(r[t]=s),s}}evictCachesForKey(t){this.textureCache[t]&&(this.gl.deleteTexture(this.textureCache[t]),delete this.textureCache[t])}copyGLTo2D(t,e){let i=t.canvas,r=e.targetCanvas,s=r.getContext("2d");if(!s)return;s.translate(0,r.height),s.scale(1,-1);let n=i.height-r.height;s.drawImage(i,0,n,r.width,r.height,0,0,r.width,r.height)}copyGLTo2DPutImageData(t,e){let i=e.targetCanvas.getContext("2d"),r=e.destinationWidth,s=e.destinationHeight,n=r*s*4;if(!i)return;let o=new Uint8Array(this.imageBuffer,0,n),a=new Uint8ClampedArray(this.imageBuffer,0,n);t.readPixels(0,0,r,s,t.RGBA,t.UNSIGNED_BYTE,o);let l=new ImageData(a,r,s);i.putImageData(l,0,0)}captureGPUInfo(){if(this.gpuInfo)return this.gpuInfo;let t=this.gl,e={renderer:"",vendor:""};if(!t)return e;let i=t.getExtension("WEBGL_debug_renderer_info");if(i){let r=t.getParameter(i.UNMASKED_RENDERER_WEBGL),s=t.getParameter(i.UNMASKED_VENDOR_WEBGL);r&&(e.renderer=r.toLowerCase()),s&&(e.vendor=s.toLowerCase())}return this.gpuInfo=e,e}}function o5(){return n||arguments.length>0&&void 0!==arguments[0]&&!arguments[0]||(n=function(){let{WebGLProbe:t}=ef();return t.queryWebGL(eZ()),ea.enableGLFiltering&&t.isSupported(ea.textureSize)?new o6({tileSize:ea.textureSize}):new o3}()),n}let o8=["filters","resizeFilter","src","crossOrigin","type"],o4=["cropX","cropY"];class o9 extends sw{static getDefaults(){return ee(ee({},super.getDefaults()),o9.ownDefaults)}constructor(t,e){super(),ei(this,"_lastScaleX",1),ei(this,"_lastScaleY",1),ei(this,"_filterScalingX",1),ei(this,"_filterScalingY",1),this.filters=[],Object.assign(this,o9.ownDefaults),this.setOptions(e),this.cacheKey="texture".concat(eU()),this.setElement("string"==typeof t?(this.canvas&&iM(this.canvas.getElement())||ep()).getElementById(t):t,e)}getElement(){return this._element}setElement(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.removeTexture(this.cacheKey),this.removeTexture("".concat(this.cacheKey,"_filtered")),this._element=t,this._originalElement=t,this._setWidthHeight(e),t.classList.add(o9.CSS_CANVAS),0!==this.filters.length&&this.applyFilters(),this.resizeFilter&&this.applyResizeFilters()}removeTexture(t){let e=o5(!1);e instanceof o6&&e.evictCachesForKey(t)}dispose(){super.dispose(),this.removeTexture(this.cacheKey),this.removeTexture("".concat(this.cacheKey,"_filtered")),this._cacheContext=null,["_originalElement","_element","_filteredEl","_cacheCanvas"].forEach(t=>{let e=this[t];e&&ef().dispose(e),this[t]=void 0})}getCrossOrigin(){return this._originalElement&&(this._originalElement.crossOrigin||null)}getOriginalSize(){let t=this.getElement();return t?{width:t.naturalWidth||t.width,height:t.naturalHeight||t.height}:{width:0,height:0}}_stroke(t){if(!this.stroke||0===this.strokeWidth)return;let e=this.width/2,i=this.height/2;t.beginPath(),t.moveTo(-e,-i),t.lineTo(e,-i),t.lineTo(e,i),t.lineTo(-e,i),t.lineTo(-e,-i),t.closePath()}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[];return this.filters.forEach(t=>{t&&e.push(t.toObject())}),ee(ee({},super.toObject([...o4,...t])),{},{src:this.getSrc(),crossOrigin:this.getCrossOrigin(),filters:e},this.resizeFilter?{resizeFilter:this.resizeFilter.toObject()}:{})}hasCrop(){return!!this.cropX||!!this.cropY||this.width<this._element.width||this.height<this._element.height}_toSVG(){let t=[],e=this._element,i=-this.width/2,r=-this.height/2,s=[],n=[],o="",a="";if(!e)return[];if(this.hasCrop()){let t=eU();s.push('<clipPath id="imageCrop_'+t+'">\n','	<rect x="'+i+'" y="'+r+'" width="'+this.width+'" height="'+this.height+'" />\n',"</clipPath>\n"),o=' clip-path="url(#imageCrop_'+t+')" '}if(this.imageSmoothing||(a=' image-rendering="optimizeSpeed"'),t.push("	<image ","COMMON_PARTS",'xlink:href="'.concat(this.getSvgSrc(!0),'" x="').concat(i-this.cropX,'" y="').concat(r-this.cropY,'" width="').concat(e.width||e.naturalWidth,'" height="').concat(e.height||e.naturalHeight,'"').concat(a).concat(o,"></image>\n")),this.stroke||this.strokeDashArray){let t=this.fill;this.fill=null,n=['	<rect x="'.concat(i,'" y="').concat(r,'" width="').concat(this.width,'" height="').concat(this.height,'" style="').concat(this.getSvgStyles(),'" />\n')],this.fill=t}return"fill"!==this.paintFirst?s.concat(n,t):s.concat(t,n)}getSrc(t){let e=t?this._element:this._originalElement;return e?e.toDataURL?e.toDataURL():this.srcFromAttribute?e.getAttribute("src")||"":e.src:this.src||""}getSvgSrc(t){return this.getSrc(t)}setSrc(t){let{crossOrigin:e,signal:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return is(t,{crossOrigin:e,signal:i}).then(t=>{void 0!==e&&this.set({crossOrigin:e}),this.setElement(t)})}toString(){return'#<Image: { src: "'.concat(this.getSrc(),'" }>')}applyResizeFilters(){let t=this.resizeFilter,e=this.minimumScaleTrigger,i=this.getTotalObjectScaling(),r=i.x,s=i.y,n=this._filteredEl||this._originalElement;if(this.group&&this.set("dirty",!0),!t||r>e&&s>e)return this._element=n,this._filterScalingX=1,this._filterScalingY=1,this._lastScaleX=r,void(this._lastScaleY=s);let o=eZ(),a=n.width,l=n.height;o.width=a,o.height=l,this._element=o,this._lastScaleX=t.scaleX=r,this._lastScaleY=t.scaleY=s,o5().applyFilters([t],n,a,l,this._element),this._filterScalingX=o.width/this._originalElement.width,this._filterScalingY=o.height/this._originalElement.height}applyFilters(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.filters||[];if(t=t.filter(t=>t&&!t.isNeutralState()),this.set("dirty",!0),this.removeTexture("".concat(this.cacheKey,"_filtered")),0===t.length)return this._element=this._originalElement,this._filteredEl=void 0,this._filterScalingX=1,void(this._filterScalingY=1);let e=this._originalElement,i=e.naturalWidth||e.width,r=e.naturalHeight||e.height;if(this._element===this._originalElement){let t=eZ();t.width=i,t.height=r,this._element=t,this._filteredEl=t}else this._filteredEl&&(this._element=this._filteredEl,this._filteredEl.getContext("2d").clearRect(0,0,i,r),this._lastScaleX=1,this._lastScaleY=1);o5().applyFilters(t,this._originalElement,i,r,this._element),this._originalElement.width===this._element.width&&this._originalElement.height===this._element.height||(this._filterScalingX=this._element.width/this._originalElement.width,this._filterScalingY=this._element.height/this._originalElement.height)}_render(t){t.imageSmoothingEnabled=this.imageSmoothing,!0!==this.isMoving&&this.resizeFilter&&this._needsResize()&&this.applyResizeFilters(),this._stroke(t),this._renderPaintInOrder(t)}drawCacheOnCanvas(t){t.imageSmoothingEnabled=this.imageSmoothing,super.drawCacheOnCanvas(t)}shouldCache(){return this.needsItsOwnCache()}_renderFill(t){let e=this._element;if(!e)return;let i=this._filterScalingX,r=this._filterScalingY,s=this.width,n=this.height,o=Math.max(this.cropX,0),a=Math.max(this.cropY,0),l=e.naturalWidth||e.width,h=e.naturalHeight||e.height,c=o*i,d=a*r,u=Math.min(s*i,l-c),g=Math.min(n*r,h-d),f=Math.min(s,l/i-o),p=Math.min(n,h/r-a);e&&t.drawImage(e,c,d,u,g,-s/2,-n/2,f,p)}_needsResize(){let t=this.getTotalObjectScaling();return t.x!==this._lastScaleX||t.y!==this._lastScaleY}_resetWidthHeight(){this.set(this.getOriginalSize())}_setWidthHeight(){let{width:t,height:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=this.getOriginalSize();this.width=t||i.width,this.height=e||i.height}parsePreserveAspectRatioAttribute(){let t=i_(this.preserveAspectRatio||""),e=this.width,i=this.height,r={width:e,height:i},s,n=this._element.width,o=this._element.height,a=1,l=1,h=0,c=0,d=0,u=0;return t&&(t.alignX!==eM||t.alignY!==eM)?("meet"===t.meetOrSlice&&(s=(e-n*(a=l=nl(this._element,r)))/2,"Min"===t.alignX&&(h=-s),"Max"===t.alignX&&(h=s),s=(i-o*l)/2,"Min"===t.alignY&&(c=-s),"Max"===t.alignY&&(c=s)),"slice"===t.meetOrSlice&&(s=n-e/(a=l=nh(this._element,r)),"Mid"===t.alignX&&(d=s/2),"Max"===t.alignX&&(d=s),s=o-i/l,"Mid"===t.alignY&&(u=s/2),"Max"===t.alignY&&(u=s),n=e/a,o=i/l)):(a=e/n,l=i/o),{width:n,height:o,scaleX:a,scaleY:l,offsetLeft:h,offsetTop:c,cropX:d,cropY:u}}static fromObject(t,e){let{filters:i,resizeFilter:r,src:s,crossOrigin:n,type:o}=t,a=er(t,o8);return Promise.all([is(s,ee(ee({},e),{},{crossOrigin:n})),i&&io(i,e),r&&io([r],e),ia(a,e)]).then(t=>{let[e,i=[],[r]=[],n={}]=t;return new this(e,ee(ee({},a),{},{src:s,filters:i,resizeFilter:r},n))})}static fromURL(t){let{crossOrigin:e=null,signal:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0;return is(t,{crossOrigin:e,signal:i}).then(t=>new this(t,r))}static async fromElement(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0,r=s0(t,this.ATTRIBUTE_NAMES,i);return this.fromURL(r["xlink:href"],e,r).catch(t=>(el("log","Unable to parse Image",t),null))}}ei(o9,"type","Image"),ei(o9,"cacheProperties",[...rq,...o4]),ei(o9,"ownDefaults",{strokeWidth:0,srcFromAttribute:!1,minimumScaleTrigger:.5,cropX:0,cropY:0,imageSmoothing:!0}),ei(o9,"CSS_CANVAS","canvas-img"),ei(o9,"ATTRIBUTE_NAMES",[...sR,"x","y","width","height","preserveAspectRatio","xlink:href","crossOrigin","image-rendering"]),eA.setClass(o9),eA.setSVGClass(o9),rI(["pattern","defs","symbol","metadata","clipPath","mask","desc"]);let o7="modifyPoly",at=t=>function(e,i,r){let{points:s,pathOffset:n}=r;return new eN(s[t]).subtract(n).transform(e2(r.getViewportTransform(),r.calcTransformMatrix()))},ae=(t,e,i,r)=>{let{target:s,pointIndex:n}=e,o=iK(new eN(i,r),void 0,s.calcOwnMatrix());return s.points[n]=o.add(s.pathOffset),s.setDimensions(),!0},ai=(t,e)=>function(i,r,s,n){let o=r.target,a=new eN(o.points[(t>0?t:o.points.length)-1]),l=a.subtract(o.pathOffset).transform(o.calcOwnMatrix()),h=e(i,ee(ee({},r),{},{pointIndex:t}),s,n),c=a.subtract(o.pathOffset).transform(o.calcOwnMatrix()).subtract(l);return o.left-=c.x,o.top-=c.y,h},ar=t=>r$(o7,ai(t,ae));var as=Object.freeze({__proto__:null,changeWidth:r1,createObjectDefaultControls:sm,createPolyActionHandler:ar,createPolyControls:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i={};for(let r=0;r<("number"==typeof t?t:t.points.length);r++)i["p".concat(r)]=new r6(ee({actionName:o7,positionHandler:at(r),actionHandler:ar(r)},e));return i},createPolyPositionHandler:at,createResizeControls:sx,createTextboxDefaultControls:sy,dragHandler:i7,factoryPolyActionHandler:ai,getLocalPoint:i9,polyActionHandler:ae,renderCircleControl:r2,renderSquareControl:r3,rotationStyleHandler:r5,rotationWithSnapping:r8,scaleCursorStyleHandler:st,scaleOrSkewActionName:sg,scaleSkewCursorStyleHandler:sf,scalingEqually:si,scalingX:sr,scalingXOrSkewingY:sp,scalingY:ss,scalingYOrSkewingX:sv,skewCursorStyleHandler:sl,skewHandlerX:sc,skewHandlerY:sd,wrapWithFireEvent:r$,wrapWithFixedAnchor:r0});let an=t=>void 0!==t.webgl,ao="precision highp float",aa="\n    ".concat(ao,";\n    varying vec2 vTexCoord;\n    uniform sampler2D uTexture;\n    void main() {\n      gl_FragColor = texture2D(uTexture, vTexCoord);\n    }"),al=["type"],ah=["type"],ac=RegExp(ao,"g");class ad{get type(){return this.constructor.type}constructor(){let t=er(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},al);ei(this,"vertexSource","\n    attribute vec2 aPosition;\n    varying vec2 vTexCoord;\n    void main() {\n      vTexCoord = aPosition;\n      gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);\n    }"),Object.assign(this,this.constructor.defaults,t)}getFragmentSource(){return aa}createProgram(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getFragmentSource(),i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.vertexSource,{WebGLProbe:{GLPrecision:r="highp"}}=ef();"highp"!==r&&(e=e.replace(ac,ao.replace("highp",r)));let s=t.createShader(t.VERTEX_SHADER),n=t.createShader(t.FRAGMENT_SHADER),o=t.createProgram();if(!s||!n||!o)throw new eh("Vertex, fragment shader or program creation error");if(t.shaderSource(s,i),t.compileShader(s),!t.getShaderParameter(s,t.COMPILE_STATUS))throw new eh("Vertex shader compile error for ".concat(this.type,": ").concat(t.getShaderInfoLog(s)));if(t.shaderSource(n,e),t.compileShader(n),!t.getShaderParameter(n,t.COMPILE_STATUS))throw new eh("Fragment shader compile error for ".concat(this.type,": ").concat(t.getShaderInfoLog(n)));if(t.attachShader(o,s),t.attachShader(o,n),t.linkProgram(o),!t.getProgramParameter(o,t.LINK_STATUS))throw new eh('Shader link error for "'.concat(this.type,'" ').concat(t.getProgramInfoLog(o)));let a=this.getUniformLocations(t,o)||{};return a.uStepW=t.getUniformLocation(o,"uStepW"),a.uStepH=t.getUniformLocation(o,"uStepH"),{program:o,attributeLocations:this.getAttributeLocations(t,o),uniformLocations:a}}getAttributeLocations(t,e){return{aPosition:t.getAttribLocation(e,"aPosition")}}getUniformLocations(t,e){return{}}sendAttributeData(t,e,i){let r=e.aPosition,s=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,s),t.enableVertexAttribArray(r),t.vertexAttribPointer(r,2,t.FLOAT,!1,0,0),t.bufferData(t.ARRAY_BUFFER,i,t.STATIC_DRAW)}_setupFrameBuffer(t){let e=t.context;if(t.passes>1){let i=t.destinationWidth,r=t.destinationHeight;t.sourceWidth===i&&t.sourceHeight===r||(e.deleteTexture(t.targetTexture),t.targetTexture=t.filterBackend.createTexture(e,i,r)),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,t.targetTexture,0)}else e.bindFramebuffer(e.FRAMEBUFFER,null),e.finish()}_swapTextures(t){t.passes--,t.pass++;let e=t.targetTexture;t.targetTexture=t.sourceTexture,t.sourceTexture=e}isNeutralState(t){let e=this.mainParameter,i=this.constructor.defaults[e];if(e){let t=this[e];return Array.isArray(i)&&Array.isArray(t)?i.every((e,i)=>e===t[i]):i===t}return!1}applyTo(t){an(t)?(this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)}applyTo2d(t){}getCacheKey(){return this.type}retrieveShader(t){let e=this.getCacheKey();return t.programCache[e]||(t.programCache[e]=this.createProgram(t.context)),t.programCache[e]}applyToWebGL(t){let e=t.context,i=this.retrieveShader(t);0===t.pass&&t.originalTexture?e.bindTexture(e.TEXTURE_2D,t.originalTexture):e.bindTexture(e.TEXTURE_2D,t.sourceTexture),e.useProgram(i.program),this.sendAttributeData(e,i.attributeLocations,t.aPosition),e.uniform1f(i.uniformLocations.uStepW,1/t.sourceWidth),e.uniform1f(i.uniformLocations.uStepH,1/t.sourceHeight),this.sendUniformData(e,i.uniformLocations),e.viewport(0,0,t.destinationWidth,t.destinationHeight),e.drawArrays(e.TRIANGLE_STRIP,0,4)}bindAdditionalTexture(t,e,i){t.activeTexture(i),t.bindTexture(t.TEXTURE_2D,e),t.activeTexture(t.TEXTURE0)}unbindAdditionalTexture(t,e){t.activeTexture(e),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE0)}getMainParameter(){return this.mainParameter?this[this.mainParameter]:void 0}setMainParameter(t){this.mainParameter&&(this[this.mainParameter]=t)}sendUniformData(t,e){}createHelpLayer(t){if(!t.helpLayer){let e=eZ();e.width=t.sourceWidth,e.height=t.sourceHeight,t.helpLayer=e}}toObject(){let t=this.mainParameter;return ee({type:this.type},t?{[t]:this[t]}:{})}toJSON(){return this.toObject()}static async fromObject(t,e){return new this(er(t,ah))}}ei(ad,"type","BaseFilter");let au={multiply:"gl_FragColor.rgb *= uColor.rgb;\n",screen:"gl_FragColor.rgb = 1.0 - (1.0 - gl_FragColor.rgb) * (1.0 - uColor.rgb);\n",add:"gl_FragColor.rgb += uColor.rgb;\n",difference:"gl_FragColor.rgb = abs(gl_FragColor.rgb - uColor.rgb);\n",subtract:"gl_FragColor.rgb -= uColor.rgb;\n",lighten:"gl_FragColor.rgb = max(gl_FragColor.rgb, uColor.rgb);\n",darken:"gl_FragColor.rgb = min(gl_FragColor.rgb, uColor.rgb);\n",exclusion:"gl_FragColor.rgb += uColor.rgb - 2.0 * (uColor.rgb * gl_FragColor.rgb);\n",overlay:"\n    if (uColor.r < 0.5) {\n      gl_FragColor.r *= 2.0 * uColor.r;\n    } else {\n      gl_FragColor.r = 1.0 - 2.0 * (1.0 - gl_FragColor.r) * (1.0 - uColor.r);\n    }\n    if (uColor.g < 0.5) {\n      gl_FragColor.g *= 2.0 * uColor.g;\n    } else {\n      gl_FragColor.g = 1.0 - 2.0 * (1.0 - gl_FragColor.g) * (1.0 - uColor.g);\n    }\n    if (uColor.b < 0.5) {\n      gl_FragColor.b *= 2.0 * uColor.b;\n    } else {\n      gl_FragColor.b = 1.0 - 2.0 * (1.0 - gl_FragColor.b) * (1.0 - uColor.b);\n    }\n    ",tint:"\n    gl_FragColor.rgb *= (1.0 - uColor.a);\n    gl_FragColor.rgb += uColor.rgb;\n    "};class ag extends ad{getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return"\n      precision highp float;\n      uniform sampler2D uTexture;\n      uniform vec4 uColor;\n      varying vec2 vTexCoord;\n      void main() {\n        vec4 color = texture2D(uTexture, vTexCoord);\n        gl_FragColor = color;\n        if (color.a > 0.0) {\n          ".concat(au[this.mode],"\n        }\n      }\n      ")}applyTo2d(t){let{imageData:{data:e}}=t,i=new im(this.color).getSource(),r=i[0]*this.alpha,s=i[1]*this.alpha,n=i[2]*this.alpha,o=1-this.alpha;for(let t=0;t<e.length;t+=4){let i=e[t],a=e[t+1],l=e[t+2];switch(this.mode){case"multiply":e[t]=i*r/255,e[t+1]=a*s/255,e[t+2]=l*n/255;break;case"screen":e[t]=255-(255-i)*(255-r)/255,e[t+1]=255-(255-a)*(255-s)/255,e[t+2]=255-(255-l)*(255-n)/255;break;case"add":e[t]=i+r,e[t+1]=a+s,e[t+2]=l+n;break;case"difference":e[t]=Math.abs(i-r),e[t+1]=Math.abs(a-s),e[t+2]=Math.abs(l-n);break;case"subtract":e[t]=i-r,e[t+1]=a-s,e[t+2]=l-n;break;case"darken":e[t]=Math.min(i,r),e[t+1]=Math.min(a,s),e[t+2]=Math.min(l,n);break;case"lighten":e[t]=Math.max(i,r),e[t+1]=Math.max(a,s),e[t+2]=Math.max(l,n);break;case"overlay":e[t]=r<128?2*i*r/255:255-2*(255-i)*(255-r)/255,e[t+1]=s<128?2*a*s/255:255-2*(255-a)*(255-s)/255,e[t+2]=n<128?2*l*n/255:255-2*(255-l)*(255-n)/255;break;case"exclusion":e[t]=r+i-2*r*i/255,e[t+1]=s+a-2*s*a/255,e[t+2]=n+l-2*n*l/255;break;case"tint":e[t]=r+i*o,e[t+1]=s+a*o,e[t+2]=n+l*o}}}getUniformLocations(t,e){return{uColor:t.getUniformLocation(e,"uColor")}}sendUniformData(t,e){let i=new im(this.color).getSource();i[0]=this.alpha*i[0]/255,i[1]=this.alpha*i[1]/255,i[2]=this.alpha*i[2]/255,i[3]=this.alpha,t.uniform4fv(e.uColor,i)}toObject(){return{type:this.type,color:this.color,mode:this.mode,alpha:this.alpha}}}ei(ag,"defaults",{color:"#F95C63",mode:"multiply",alpha:1}),ei(ag,"type","BlendColor"),eA.setClass(ag);let af={multiply:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform sampler2D uImage;\n    uniform vec4 uColor;\n    varying vec2 vTexCoord;\n    varying vec2 vTexCoord2;\n    void main() {\n      vec4 color = texture2D(uTexture, vTexCoord);\n      vec4 color2 = texture2D(uImage, vTexCoord2);\n      color.rgba *= color2.rgba;\n      gl_FragColor = color;\n    }\n    ",mask:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform sampler2D uImage;\n    uniform vec4 uColor;\n    varying vec2 vTexCoord;\n    varying vec2 vTexCoord2;\n    void main() {\n      vec4 color = texture2D(uTexture, vTexCoord);\n      vec4 color2 = texture2D(uImage, vTexCoord2);\n      color.a = color2.a;\n      gl_FragColor = color;\n    }\n    "},ap=["type","image"];class av extends ad{getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return af[this.mode]}applyToWebGL(t){let e=t.context,i=this.createTexture(t.filterBackend,this.image);this.bindAdditionalTexture(e,i,e.TEXTURE1),super.applyToWebGL(t),this.unbindAdditionalTexture(e,e.TEXTURE1)}createTexture(t,e){return t.getCachedTexture(e.cacheKey,e.getElement())}calculateMatrix(){let t=this.image,{width:e,height:i}=t.getElement();return[1/t.scaleX,0,0,0,1/t.scaleY,0,-t.left/e,-t.top/i,1]}applyTo2d(t){let{imageData:{data:e,width:i,height:r},filterBackend:{resources:s}}=t,n=this.image;s.blendImage||(s.blendImage=eZ());let o=s.blendImage,a=o.getContext("2d");o.width!==i||o.height!==r?(o.width=i,o.height=r):a.clearRect(0,0,i,r),a.setTransform(n.scaleX,0,0,n.scaleY,n.left,n.top),a.drawImage(n.getElement(),0,0,i,r);let l=a.getImageData(0,0,i,r).data;for(let t=0;t<e.length;t+=4){let i=e[t],r=e[t+1],s=e[t+2],n=e[t+3],o=l[t],a=l[t+1],h=l[t+2],c=l[t+3];switch(this.mode){case"multiply":e[t]=i*o/255,e[t+1]=r*a/255,e[t+2]=s*h/255,e[t+3]=n*c/255;break;case"mask":e[t+3]=c}}}getUniformLocations(t,e){return{uTransformMatrix:t.getUniformLocation(e,"uTransformMatrix"),uImage:t.getUniformLocation(e,"uImage")}}sendUniformData(t,e){let i=this.calculateMatrix();t.uniform1i(e.uImage,1),t.uniformMatrix3fv(e.uTransformMatrix,!1,i)}toObject(){return{type:this.type,image:this.image&&this.image.toObject(),mode:this.mode,alpha:this.alpha}}static fromObject(t,e){let{type:i,image:r}=t,s=er(t,ap);return o9.fromObject(r,e).then(t=>new this(ee(ee({},s),{},{image:t})))}}ei(av,"type","BlendImage"),ei(av,"defaults",{mode:"multiply",alpha:1,vertexSource:"\n    attribute vec2 aPosition;\n    varying vec2 vTexCoord;\n    varying vec2 vTexCoord2;\n    uniform mat3 uTransformMatrix;\n    void main() {\n      vTexCoord = aPosition;\n      vTexCoord2 = (uTransformMatrix * vec3(aPosition, 1.0)).xy;\n      gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);\n    }\n    "}),eA.setClass(av);class am extends ad{getFragmentSource(){return"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform vec2 uDelta;\n    varying vec2 vTexCoord;\n    const float nSamples = 15.0;\n    vec3 v3offset = vec3(12.9898, 78.233, 151.7182);\n    float random(vec3 scale) {\n      /* use the fragment position for a different seed per-pixel */\n      return fract(sin(dot(gl_FragCoord.xyz, scale)) * 43758.5453);\n    }\n    void main() {\n      vec4 color = vec4(0.0);\n      float total = 0.0;\n      float offset = random(v3offset);\n      for (float t = -nSamples; t <= nSamples; t++) {\n        float percent = (t + offset - 0.5) / nSamples;\n        float weight = 1.0 - abs(percent);\n        color += texture2D(uTexture, vTexCoord + uDelta * percent) * weight;\n        total += weight;\n      }\n      gl_FragColor = color / total;\n    }\n  "}applyTo(t){an(t)?(this.aspectRatio=t.sourceWidth/t.sourceHeight,t.passes++,this._setupFrameBuffer(t),this.horizontal=!0,this.applyToWebGL(t),this._swapTextures(t),this._setupFrameBuffer(t),this.horizontal=!1,this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)}applyTo2d(t){t.imageData=this.simpleBlur(t)}simpleBlur(t){let e,i,r,s,{ctx:n,imageData:o,filterBackend:{resources:a}}=t,{width:l,height:h}=o;a.blurLayer1||(a.blurLayer1=eZ(),a.blurLayer2=eZ());let c=a.blurLayer1,d=a.blurLayer2;c.width===l&&c.height===h||(d.width=c.width=l,d.height=c.height=h);let u=c.getContext("2d"),g=d.getContext("2d"),f=.06*this.blur*.5;for(u.putImageData(o,0,0),g.clearRect(0,0,l,h),s=-15;s<=15;s++)e=(Math.random()-.5)/4,r=f*(i=s/15)*l+e,g.globalAlpha=1-Math.abs(i),g.drawImage(c,r,e),u.drawImage(d,0,0),g.globalAlpha=1,g.clearRect(0,0,d.width,d.height);for(s=-15;s<=15;s++)e=(Math.random()-.5)/4,r=f*(i=s/15)*h+e,g.globalAlpha=1-Math.abs(i),g.drawImage(c,e,r),u.drawImage(d,0,0),g.globalAlpha=1,g.clearRect(0,0,d.width,d.height);n.drawImage(c,0,0);let p=n.getImageData(0,0,c.width,c.height);return u.globalAlpha=1,u.clearRect(0,0,c.width,c.height),p}getUniformLocations(t,e){return{delta:t.getUniformLocation(e,"uDelta")}}sendUniformData(t,e){let i=this.chooseRightDelta();t.uniform2fv(e.delta,i)}chooseRightDelta(){let t=1,e=[0,0];this.horizontal?this.aspectRatio>1&&(t=1/this.aspectRatio):this.aspectRatio<1&&(t=this.aspectRatio);let i=t*this.blur*.12;return this.horizontal?e[0]=i:e[1]=i,e}}ei(am,"type","Blur"),ei(am,"defaults",{blur:0,mainParameter:"blur"}),eA.setClass(am);class ax extends ad{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform float uBrightness;\n  varying vec2 vTexCoord;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    color.rgb += uBrightness;\n    gl_FragColor = color;\n  }\n"}applyTo2d(t){let{imageData:{data:e}}=t;if(0===this.brightness)return;let i=Math.round(255*this.brightness);for(let t=0;t<e.length;t+=4)e[t]=e[t]+i,e[t+1]=e[t+1]+i,e[t+2]=e[t+2]+i}getUniformLocations(t,e){return{uBrightness:t.getUniformLocation(e,"uBrightness")}}sendUniformData(t,e){t.uniform1f(e.uBrightness,this.brightness)}}ei(ax,"type","Brightness"),ei(ax,"defaults",{brightness:0,mainParameter:"brightness"}),eA.setClass(ax);let ay=["matrix"],a_={matrix:[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],mainParameter:"matrix",colorsOnly:!0};class ab extends ad{setOptions(t){let{matrix:e}=t,i=er(t,ay);e&&(this.matrix=[...e]),Object.assign(this,i)}getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  varying vec2 vTexCoord;\n  uniform mat4 uColorMatrix;\n  uniform vec4 uConstants;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    color *= uColorMatrix;\n    color += uConstants;\n    gl_FragColor = color;\n  }"}applyTo2d(t){let e=t.imageData.data,i=this.matrix,r=this.colorsOnly;for(let t=0;t<e.length;t+=4){let s=e[t],n=e[t+1],o=e[t+2];if(r)e[t]=s*i[0]+n*i[1]+o*i[2]+255*i[4],e[t+1]=s*i[5]+n*i[6]+o*i[7]+255*i[9],e[t+2]=s*i[10]+n*i[11]+o*i[12]+255*i[14];else{let r=e[t+3];e[t]=s*i[0]+n*i[1]+o*i[2]+r*i[3]+255*i[4],e[t+1]=s*i[5]+n*i[6]+o*i[7]+r*i[8]+255*i[9],e[t+2]=s*i[10]+n*i[11]+o*i[12]+r*i[13]+255*i[14],e[t+3]=s*i[15]+n*i[16]+o*i[17]+r*i[18]+255*i[19]}}}getUniformLocations(t,e){return{uColorMatrix:t.getUniformLocation(e,"uColorMatrix"),uConstants:t.getUniformLocation(e,"uConstants")}}sendUniformData(t,e){let i=this.matrix,r=[i[0],i[1],i[2],i[3],i[5],i[6],i[7],i[8],i[10],i[11],i[12],i[13],i[15],i[16],i[17],i[18]],s=[i[4],i[9],i[14],i[19]];t.uniformMatrix4fv(e.uColorMatrix,!1,r),t.uniform4fv(e.uConstants,s)}}function aw(t,e){var i;let r=(ei(i=class extends ab{},"type",t),ei(i,"defaults",ee(ee({},a_),{},{mainParameter:void 0,matrix:e})),i);return eA.setClass(r,t),r}ei(ab,"type","ColorMatrix"),ei(ab,"defaults",a_),eA.setClass(ab);let aC=aw("Brownie",[.5997,.34553,-.27082,0,.186,-.0377,.86095,.15059,0,-.1449,.24113,-.07441,.44972,0,-.02965,0,0,0,1,0]),aS=aw("Vintage",[.62793,.32021,-.03965,0,.03784,.02578,.64411,.03259,0,.02926,.0466,-.08512,.52416,0,.02023,0,0,0,1,0]),aT=aw("Kodachrome",[1.12855,-.39673,-.03992,0,.24991,-.16404,1.08352,-.05498,0,.09698,-.16786,-.56034,1.60148,0,.13972,0,0,0,1,0]),aO=aw("Technicolor",[1.91252,-.85453,-.09155,0,.04624,-.30878,1.76589,-.10601,0,-.27589,-.2311,-.75018,1.84759,0,.12137,0,0,0,1,0]),ak=aw("Polaroid",[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0]),aj=aw("Sepia",[.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0]),aM=aw("BlackWhite",[1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,0,0,0,1,0]),aE=["subFilters"];class aD extends ad{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{subFilters:e=[]}=t;super(er(t,aE)),this.subFilters=e}applyTo(t){an(t)&&(t.passes+=this.subFilters.length-1),this.subFilters.forEach(e=>{e.applyTo(t)})}toObject(){return ee(ee({},super.toObject()),{},{subFilters:this.subFilters.map(t=>t.toObject())})}isNeutralState(){return!this.subFilters.some(t=>!t.isNeutralState())}static fromObject(t,e){return Promise.all((t.subFilters||[]).map(t=>eA.getClass(t.type).fromObject(t,e))).then(t=>new this({subFilters:t}))}}ei(aD,"type","Composed"),eA.setClass(aD);class aA extends ad{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform float uContrast;\n  varying vec2 vTexCoord;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    float contrastF = 1.015 * (uContrast + 1.0) / (1.0 * (1.015 - uContrast));\n    color.rgb = contrastF * (color.rgb - 0.5) + 0.5;\n    gl_FragColor = color;\n  }"}applyTo2d(t){let{imageData:{data:e}}=t;if(0===this.contrast)return;let i=Math.floor(255*this.contrast),r=259*(i+255)/(255*(259-i));for(let t=0;t<e.length;t+=4)e[t]=r*(e[t]-128)+128,e[t+1]=r*(e[t+1]-128)+128,e[t+2]=r*(e[t+2]-128)+128}getUniformLocations(t,e){return{uContrast:t.getUniformLocation(e,"uContrast")}}sendUniformData(t,e){t.uniform1f(e.uContrast,this.contrast)}}ei(aA,"type","Contrast"),ei(aA,"defaults",{contrast:0,mainParameter:"contrast"}),eA.setClass(aA);let aL={Convolute_3_1:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[9];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 0);\n      for (float h = 0.0; h < 3.0; h+=1.0) {\n        for (float w = 0.0; w < 3.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 1), uStepH * (h - 1));\n          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 3.0 + w)];\n        }\n      }\n      gl_FragColor = color;\n    }\n    ",Convolute_3_0:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[9];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 1);\n      for (float h = 0.0; h < 3.0; h+=1.0) {\n        for (float w = 0.0; w < 3.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 1.0), uStepH * (h - 1.0));\n          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 3.0 + w)];\n        }\n      }\n      float alpha = texture2D(uTexture, vTexCoord).a;\n      gl_FragColor = color;\n      gl_FragColor.a = alpha;\n    }\n    ",Convolute_5_1:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[25];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 0);\n      for (float h = 0.0; h < 5.0; h+=1.0) {\n        for (float w = 0.0; w < 5.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));\n          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 5.0 + w)];\n        }\n      }\n      gl_FragColor = color;\n    }\n    ",Convolute_5_0:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[25];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 1);\n      for (float h = 0.0; h < 5.0; h+=1.0) {\n        for (float w = 0.0; w < 5.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));\n          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 5.0 + w)];\n        }\n      }\n      float alpha = texture2D(uTexture, vTexCoord).a;\n      gl_FragColor = color;\n      gl_FragColor.a = alpha;\n    }\n    ",Convolute_7_1:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[49];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 0);\n      for (float h = 0.0; h < 7.0; h+=1.0) {\n        for (float w = 0.0; w < 7.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));\n          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 7.0 + w)];\n        }\n      }\n      gl_FragColor = color;\n    }\n    ",Convolute_7_0:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[49];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 1);\n      for (float h = 0.0; h < 7.0; h+=1.0) {\n        for (float w = 0.0; w < 7.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));\n          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 7.0 + w)];\n        }\n      }\n      float alpha = texture2D(uTexture, vTexCoord).a;\n      gl_FragColor = color;\n      gl_FragColor.a = alpha;\n    }\n    ",Convolute_9_1:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[81];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 0);\n      for (float h = 0.0; h < 9.0; h+=1.0) {\n        for (float w = 0.0; w < 9.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));\n          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 9.0 + w)];\n        }\n      }\n      gl_FragColor = color;\n    }\n    ",Convolute_9_0:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[81];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 1);\n      for (float h = 0.0; h < 9.0; h+=1.0) {\n        for (float w = 0.0; w < 9.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));\n          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 9.0 + w)];\n        }\n      }\n      float alpha = texture2D(uTexture, vTexCoord).a;\n      gl_FragColor = color;\n      gl_FragColor.a = alpha;\n    }\n    "};class aP extends ad{getCacheKey(){return"".concat(this.type,"_").concat(Math.sqrt(this.matrix.length),"_").concat(this.opaque?1:0)}getFragmentSource(){return aL[this.getCacheKey()]}applyTo2d(t){let e,i,r,s,n,o,a,l,h,c,d,u,g;let f=t.imageData,p=f.data,v=this.matrix,m=Math.round(Math.sqrt(v.length)),x=Math.floor(m/2),y=f.width,_=f.height,b=t.ctx.createImageData(y,_),w=b.data,C=this.opaque?1:0;for(d=0;d<_;d++)for(c=0;c<y;c++){for(n=4*(d*y+c),e=0,i=0,r=0,s=0,g=0;g<m;g++)for(u=0;u<m;u++)a=d+g-x,o=c+u-x,a<0||a>=_||o<0||o>=y||(l=4*(a*y+o),h=v[g*m+u],e+=p[l]*h,i+=p[l+1]*h,r+=p[l+2]*h,C||(s+=p[l+3]*h));w[n]=e,w[n+1]=i,w[n+2]=r,w[n+3]=C?p[n+3]:s}t.imageData=b}getUniformLocations(t,e){return{uMatrix:t.getUniformLocation(e,"uMatrix"),uOpaque:t.getUniformLocation(e,"uOpaque"),uHalfSize:t.getUniformLocation(e,"uHalfSize"),uSize:t.getUniformLocation(e,"uSize")}}sendUniformData(t,e){t.uniform1fv(e.uMatrix,this.matrix)}toObject(){return ee(ee({},super.toObject()),{},{opaque:this.opaque,matrix:[...this.matrix]})}}ei(aP,"type","Convolute"),ei(aP,"defaults",{opaque:!1,matrix:[0,0,0,0,1,0,0,0,0]}),eA.setClass(aP);let aF=["gamma"];class aR extends ad{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform vec3 uGamma;\n  varying vec2 vTexCoord;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    vec3 correction = (1.0 / uGamma);\n    color.r = pow(color.r, correction.r);\n    color.g = pow(color.g, correction.g);\n    color.b = pow(color.b, correction.b);\n    gl_FragColor = color;\n    gl_FragColor.rgb *= color.a;\n  }\n"}constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{gamma:e=[1,1,1]}=t;super(er(t,aF)),this.gamma=e}applyTo2d(t){let{imageData:{data:e}}=t,i=this.gamma,r=1/i[0],s=1/i[1],n=1/i[2];this.rgbValues||(this.rgbValues={r:new Uint8Array(256),g:new Uint8Array(256),b:new Uint8Array(256)});let o=this.rgbValues;for(let t=0;t<256;t++)o.r[t]=255*Math.pow(t/255,r),o.g[t]=255*Math.pow(t/255,s),o.b[t]=255*Math.pow(t/255,n);for(let t=0;t<e.length;t+=4)e[t]=o.r[e[t]],e[t+1]=o.g[e[t+1]],e[t+2]=o.b[e[t+2]]}getUniformLocations(t,e){return{uGamma:t.getUniformLocation(e,"uGamma")}}sendUniformData(t,e){t.uniform3fv(e.uGamma,this.gamma)}}ei(aR,"type","Gamma"),ei(aR,"defaults",{mainParameter:"gamma",gamma:[1,1,1]}),eA.setClass(aR);let aI={average:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = texture2D(uTexture, vTexCoord);\n      float average = (color.r + color.b + color.g) / 3.0;\n      gl_FragColor = vec4(average, average, average, color.a);\n    }\n    ",lightness:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform int uMode;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 col = texture2D(uTexture, vTexCoord);\n      float average = (max(max(col.r, col.g),col.b) + min(min(col.r, col.g),col.b)) / 2.0;\n      gl_FragColor = vec4(average, average, average, col.a);\n    }\n    ",luminosity:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform int uMode;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 col = texture2D(uTexture, vTexCoord);\n      float average = 0.21 * col.r + 0.72 * col.g + 0.07 * col.b;\n      gl_FragColor = vec4(average, average, average, col.a);\n    }\n    "};class aB extends ad{applyTo2d(t){let{imageData:{data:e}}=t;for(let t,i=0;i<e.length;i+=4){switch(this.mode){case"average":t=(e[i]+e[i+1]+e[i+2])/3;break;case"lightness":t=(Math.min(e[i],e[i+1],e[i+2])+Math.max(e[i],e[i+1],e[i+2]))/2;break;case"luminosity":t=.21*e[i]+.72*e[i+1]+.07*e[i+2]}e[i]=t,e[i+1]=t,e[i+2]=t}}getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return aI[this.mode]}getUniformLocations(t,e){return{uMode:t.getUniformLocation(e,"uMode")}}sendUniformData(t,e){t.uniform1i(e.uMode,1)}isNeutralState(){return!1}}ei(aB,"type","Grayscale"),ei(aB,"defaults",{mode:"average",mainParameter:"mode"}),eA.setClass(aB);class aN extends ab{calculateMatrix(){let t=this.rotation*Math.PI,e=eI(t),i=eB(t),r=1/3,s=Math.sqrt(1/3)*i,n=1-e;this.matrix=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],this.matrix[0]=e+n/3,this.matrix[1]=r*n-s,this.matrix[2]=r*n+s,this.matrix[5]=r*n+s,this.matrix[6]=e+r*n,this.matrix[7]=r*n-s,this.matrix[10]=r*n-s,this.matrix[11]=r*n+s,this.matrix[12]=e+r*n}isNeutralState(){return this.calculateMatrix(),super.isNeutralState()}applyTo(t){this.calculateMatrix(),super.applyTo(t)}}ei(aN,"type","HueRotation"),ei(aN,"defaults",{rotation:0,mainParameter:"rotation"}),eA.setClass(aN);class aV extends ad{applyTo2d(t){let{imageData:{data:e}}=t;for(let t=0;t<e.length;t+=4)e[t]=255-e[t],e[t+1]=255-e[t+1],e[t+2]=255-e[t+2],this.alpha&&(e[t+3]=255-e[t+3])}getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform int uInvert;\n  uniform int uAlpha;\n  varying vec2 vTexCoord;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    if (uInvert == 1) {\n      if (uAlpha == 1) {\n        gl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,1.0 -color.a);\n      } else {\n        gl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,color.a);\n      }\n    } else {\n      gl_FragColor = color;\n    }\n  }\n"}isNeutralState(){return!this.invert}getUniformLocations(t,e){return{uInvert:t.getUniformLocation(e,"uInvert"),uAlpha:t.getUniformLocation(e,"uAlpha")}}sendUniformData(t,e){t.uniform1i(e.uInvert,Number(this.invert)),t.uniform1i(e.uAlpha,Number(this.alpha))}}ei(aV,"type","Invert"),ei(aV,"defaults",{alpha:!1,invert:!0,mainParameter:"invert"}),eA.setClass(aV);class aX extends ad{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform float uStepH;\n  uniform float uNoise;\n  uniform float uSeed;\n  varying vec2 vTexCoord;\n  float rand(vec2 co, float seed, float vScale) {\n    return fract(sin(dot(co.xy * vScale ,vec2(12.9898 , 78.233))) * 43758.5453 * (seed + 0.01) / 2.0);\n  }\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    color.rgb += (0.5 - rand(vTexCoord, uSeed, 0.1 / uStepH)) * uNoise;\n    gl_FragColor = color;\n  }\n"}applyTo2d(t){let{imageData:{data:e}}=t;if(0===this.noise)return;let i=this.noise;for(let t=0;t<e.length;t+=4){let r=(.5-Math.random())*i;e[t]+=r,e[t+1]+=r,e[t+2]+=r}}getUniformLocations(t,e){return{uNoise:t.getUniformLocation(e,"uNoise"),uSeed:t.getUniformLocation(e,"uSeed")}}sendUniformData(t,e){t.uniform1f(e.uNoise,this.noise/255),t.uniform1f(e.uSeed,Math.random())}toObject(){return ee(ee({},super.toObject()),{},{noise:this.noise})}}ei(aX,"type","Noise"),ei(aX,"defaults",{mainParameter:"noise",noise:0}),eA.setClass(aX);class az extends ad{applyTo2d(t){let{imageData:{data:e,width:i,height:r}}=t;for(let t=0;t<r;t+=this.blocksize)for(let s=0;s<i;s+=this.blocksize){let n=4*t*i+4*s,o=e[n],a=e[n+1],l=e[n+2],h=e[n+3];for(let n=t;n<Math.min(t+this.blocksize,r);n++)for(let t=s;t<Math.min(s+this.blocksize,i);t++){let r=4*n*i+4*t;e[r]=o,e[r+1]=a,e[r+2]=l,e[r+3]=h}}}isNeutralState(){return 1===this.blocksize}getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform float uBlocksize;\n  uniform float uStepW;\n  uniform float uStepH;\n  varying vec2 vTexCoord;\n  void main() {\n    float blockW = uBlocksize * uStepW;\n    float blockH = uBlocksize * uStepW;\n    int posX = int(vTexCoord.x / blockW);\n    int posY = int(vTexCoord.y / blockH);\n    float fposX = float(posX);\n    float fposY = float(posY);\n    vec2 squareCoords = vec2(fposX * blockW, fposY * blockH);\n    vec4 color = texture2D(uTexture, squareCoords);\n    gl_FragColor = color;\n  }\n"}getUniformLocations(t,e){return{uBlocksize:t.getUniformLocation(e,"uBlocksize"),uStepW:t.getUniformLocation(e,"uStepW"),uStepH:t.getUniformLocation(e,"uStepH")}}sendUniformData(t,e){t.uniform1f(e.uBlocksize,this.blocksize)}}ei(az,"type","Pixelate"),ei(az,"defaults",{blocksize:4,mainParameter:"blocksize"}),eA.setClass(az);class aW extends ad{getFragmentShader(){return"\nprecision highp float;\nuniform sampler2D uTexture;\nuniform vec4 uLow;\nuniform vec4 uHigh;\nvarying vec2 vTexCoord;\nvoid main() {\n  gl_FragColor = texture2D(uTexture, vTexCoord);\n  if(all(greaterThan(gl_FragColor.rgb,uLow.rgb)) && all(greaterThan(uHigh.rgb,gl_FragColor.rgb))) {\n    gl_FragColor.a = 0.0;\n  }\n}\n"}applyTo2d(t){let{imageData:{data:e}}=t,i=255*this.distance,r=new im(this.color).getSource(),s=[r[0]-i,r[1]-i,r[2]-i],n=[r[0]+i,r[1]+i,r[2]+i];for(let t=0;t<e.length;t+=4){let i=e[t],r=e[t+1],o=e[t+2];i>s[0]&&r>s[1]&&o>s[2]&&i<n[0]&&r<n[1]&&o<n[2]&&(e[t+3]=0)}}getUniformLocations(t,e){return{uLow:t.getUniformLocation(e,"uLow"),uHigh:t.getUniformLocation(e,"uHigh")}}sendUniformData(t,e){let i=new im(this.color).getSource(),r=this.distance,s=[0+i[0]/255-r,0+i[1]/255-r,0+i[2]/255-r,1],n=[i[0]/255+r,i[1]/255+r,i[2]/255+r,1];t.uniform4fv(e.uLow,s),t.uniform4fv(e.uHigh,n)}toObject(){return ee(ee({},super.toObject()),{},{color:this.color,distance:this.distance})}}ei(aW,"type","RemoveColor"),ei(aW,"defaults",{color:"#FFFFFF",distance:.02,useAlpha:!1}),eA.setClass(aW);class aY extends ad{getUniformLocations(t,e){return{uDelta:t.getUniformLocation(e,"uDelta"),uTaps:t.getUniformLocation(e,"uTaps")}}sendUniformData(t,e){t.uniform2fv(e.uDelta,this.horizontal?[1/this.width,0]:[0,1/this.height]),t.uniform1fv(e.uTaps,this.taps)}getFilterWindow(){let t=this.tempScale;return Math.ceil(this.lanczosLobes/t)}getCacheKey(){let t=this.getFilterWindow();return"".concat(this.type,"_").concat(t)}getFragmentSource(){let t=this.getFilterWindow();return this.generateShader(t)}getTaps(){let t=this.lanczosCreate(this.lanczosLobes),e=this.tempScale,i=this.getFilterWindow(),r=Array(i);for(let s=1;s<=i;s++)r[s-1]=t(s*e);return r}generateShader(t){let e=Array(t);for(let i=1;i<=t;i++)e[i-1]="".concat(i,".0 * uDelta");return"\n      ".concat(this.fragmentSourceTOP,"\n      uniform float uTaps[").concat(t,"];\n      void main() {\n        vec4 color = texture2D(uTexture, vTexCoord);\n        float sum = 1.0;\n        ").concat(e.map((t,e)=>"\n              color += texture2D(uTexture, vTexCoord + ".concat(t,") * uTaps[").concat(e,"] + texture2D(uTexture, vTexCoord - ").concat(t,") * uTaps[").concat(e,"];\n              sum += 2.0 * uTaps[").concat(e,"];\n            ")).join("\n"),"\n        gl_FragColor = color / sum;\n      }\n    ")}applyToForWebgl(t){t.passes++,this.width=t.sourceWidth,this.horizontal=!0,this.dW=Math.round(this.width*this.scaleX),this.dH=t.sourceHeight,this.tempScale=this.dW/this.width,this.taps=this.getTaps(),t.destinationWidth=this.dW,super.applyTo(t),t.sourceWidth=t.destinationWidth,this.height=t.sourceHeight,this.horizontal=!1,this.dH=Math.round(this.height*this.scaleY),this.tempScale=this.dH/this.height,this.taps=this.getTaps(),t.destinationHeight=this.dH,super.applyTo(t),t.sourceHeight=t.destinationHeight}applyTo(t){an(t)?this.applyToForWebgl(t):this.applyTo2d(t)}isNeutralState(){return 1===this.scaleX&&1===this.scaleY}lanczosCreate(t){return e=>{if(e>=t||e<=-t)return 0;if(e<11920929e-14&&e>-11920929e-14)return 1;let i=(e*=Math.PI)/t;return Math.sin(e)/e*Math.sin(i)/i}}applyTo2d(t){let e;let i=t.imageData,r=this.scaleX,s=this.scaleY;this.rcpScaleX=1/r,this.rcpScaleY=1/s;let n=i.width,o=i.height,a=Math.round(n*r),l=Math.round(o*s);e="sliceHack"===this.resizeType?this.sliceByTwo(t,n,o,a,l):"hermite"===this.resizeType?this.hermiteFastResize(t,n,o,a,l):"bilinear"===this.resizeType?this.bilinearFiltering(t,n,o,a,l):"lanczos"===this.resizeType?this.lanczosResize(t,n,o,a,l):new ImageData(a,l),t.imageData=e}sliceByTwo(t,e,i,r,s){let n=t.imageData,o=!1,a=!1,l=.5*e,h=.5*i,c=t.filterBackend.resources,d=0,u=0,g=e,f=0;c.sliceByTwo||(c.sliceByTwo=eZ());let p=c.sliceByTwo;(p.width<1.5*e||p.height<i)&&(p.width=1.5*e,p.height=i);let v=p.getContext("2d");for(v.clearRect(0,0,1.5*e,i),v.putImageData(n,0,0),r=Math.floor(r),s=Math.floor(s);!o||!a;)e=l,i=h,r<Math.floor(.5*l)?l=Math.floor(.5*l):(l=r,o=!0),s<Math.floor(.5*h)?h=Math.floor(.5*h):(h=s,a=!0),v.drawImage(p,d,u,e,i,g,f,l,h),d=g,u=f,f+=h;return v.getImageData(d,u,r,s)}lanczosResize(t,e,i,r,s){let n=t.imageData.data,o=t.ctx.createImageData(r,s),a=o.data,l=this.lanczosCreate(this.lanczosLobes),h=this.rcpScaleX,c=this.rcpScaleY,d=2/this.rcpScaleX,u=2/this.rcpScaleY,g=Math.ceil(h*this.lanczosLobes/2),f=Math.ceil(c*this.lanczosLobes/2),p={},v={x:0,y:0},m={x:0,y:0};return function t(x){let y,_,b,w,C,S,T,O,k,j,M;for(v.x=(x+.5)*h,m.x=Math.floor(v.x),y=0;y<s;y++){for(v.y=(y+.5)*c,m.y=Math.floor(v.y),C=0,S=0,T=0,O=0,k=0,_=m.x-g;_<=m.x+g;_++)if(!(_<0||_>=e)){p[j=Math.floor(1e3*Math.abs(_-v.x))]||(p[j]={});for(let t=m.y-f;t<=m.y+f;t++)t<0||t>=i||(M=Math.floor(1e3*Math.abs(t-v.y)),p[j][M]||(p[j][M]=l(Math.sqrt(Math.pow(j*d,2)+Math.pow(M*u,2))/1e3)),(b=p[j][M])>0&&(w=4*(t*e+_),C+=b,S+=b*n[w],T+=b*n[w+1],O+=b*n[w+2],k+=b*n[w+3]))}a[w=4*(y*r+x)]=S/C,a[w+1]=T/C,a[w+2]=O/C,a[w+3]=k/C}return++x<r?t(x):o}(0)}bilinearFiltering(t,e,i,r,s){let n,o,a,l,h,c,d,u,g,f,p,v,m=0,x=this.rcpScaleX,y=this.rcpScaleY,_=4*(e-1),b=t.imageData.data,w=t.ctx.createImageData(r,s),C=w.data;for(c=0;c<s;c++)for(d=0;d<r;d++)for(l=Math.floor(x*d),h=Math.floor(y*c),u=x*d-l,g=y*c-h,v=4*(h*e+l),f=0;f<4;f++)n=b[v+f],o=b[v+4+f],a=b[v+_+f],p=n*(1-u)*(1-g)+o*u*(1-g)+a*g*(1-u)+b[v+_+4+f]*u*g,C[m++]=p;return w}hermiteFastResize(t,e,i,r,s){let n=this.rcpScaleX,o=this.rcpScaleY,a=Math.ceil(n/2),l=Math.ceil(o/2),h=t.imageData.data,c=t.ctx.createImageData(r,s),d=c.data;for(let t=0;t<s;t++)for(let i=0;i<r;i++){let s=4*(i+t*r),c=0,u=0,g=0,f=0,p=0,v=0,m=0,x=(t+.5)*o;for(let r=Math.floor(t*o);r<(t+1)*o;r++){let t=Math.abs(x-(r+.5))/l,s=(i+.5)*n,o=t*t;for(let t=Math.floor(i*n);t<(i+1)*n;t++){let i=Math.abs(s-(t+.5))/a,n=Math.sqrt(o+i*i);n>1&&n<-1||(c=2*n*n*n-3*n*n+1)>0&&(m+=c*h[(i=4*(t+r*e))+3],g+=c,h[i+3]<255&&(c=c*h[i+3]/250),f+=c*h[i],p+=c*h[i+1],v+=c*h[i+2],u+=c)}}d[s]=f/u,d[s+1]=p/u,d[s+2]=v/u,d[s+3]=m/g}return c}toObject(){return{type:this.type,scaleX:this.scaleX,scaleY:this.scaleY,resizeType:this.resizeType,lanczosLobes:this.lanczosLobes}}}ei(aY,"type","Resize"),ei(aY,"defaults",{resizeType:"hermite",scaleX:1,scaleY:1,lanczosLobes:3,fragmentSourceTOP:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform vec2 uDelta;\n    varying vec2 vTexCoord;\n  "}),eA.setClass(aY);class aH extends ad{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform float uSaturation;\n  varying vec2 vTexCoord;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    float rgMax = max(color.r, color.g);\n    float rgbMax = max(rgMax, color.b);\n    color.r += rgbMax != color.r ? (rgbMax - color.r) * uSaturation : 0.00;\n    color.g += rgbMax != color.g ? (rgbMax - color.g) * uSaturation : 0.00;\n    color.b += rgbMax != color.b ? (rgbMax - color.b) * uSaturation : 0.00;\n    gl_FragColor = color;\n  }\n"}applyTo2d(t){let{imageData:{data:e}}=t;if(0===this.saturation)return;let i=-this.saturation;for(let t=0;t<e.length;t+=4){let r=Math.max(e[t],e[t+1],e[t+2]);e[t]+=r!==e[t]?(r-e[t])*i:0,e[t+1]+=r!==e[t+1]?(r-e[t+1])*i:0,e[t+2]+=r!==e[t+2]?(r-e[t+2])*i:0}}getUniformLocations(t,e){return{uSaturation:t.getUniformLocation(e,"uSaturation")}}sendUniformData(t,e){t.uniform1f(e.uSaturation,-this.saturation)}}ei(aH,"type","Saturation"),ei(aH,"defaults",{saturation:0,mainParameter:"saturation"}),eA.setClass(aH);class aG extends ad{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform float uVibrance;\n  varying vec2 vTexCoord;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    float max = max(color.r, max(color.g, color.b));\n    float avg = (color.r + color.g + color.b) / 3.0;\n    float amt = (abs(max - avg) * 2.0) * uVibrance;\n    color.r += max != color.r ? (max - color.r) * amt : 0.00;\n    color.g += max != color.g ? (max - color.g) * amt : 0.00;\n    color.b += max != color.b ? (max - color.b) * amt : 0.00;\n    gl_FragColor = color;\n  }\n"}applyTo2d(t){let{imageData:{data:e}}=t;if(0===this.vibrance)return;let i=-this.vibrance;for(let t=0;t<e.length;t+=4){let r=Math.max(e[t],e[t+1],e[t+2]),s=2*Math.abs(r-(e[t]+e[t+1]+e[t+2])/3)/255*i;e[t]+=r!==e[t]?(r-e[t])*s:0,e[t+1]+=r!==e[t+1]?(r-e[t+1])*s:0,e[t+2]+=r!==e[t+2]?(r-e[t+2])*s:0}}getUniformLocations(t,e){return{uVibrance:t.getUniformLocation(e,"uVibrance")}}sendUniformData(t,e){t.uniform1f(e.uVibrance,-this.vibrance)}}ei(aG,"type","Vibrance"),ei(aG,"defaults",{vibrance:0,mainParameter:"vibrance"}),eA.setClass(aG),Object.freeze({__proto__:null,BaseFilter:ad,BlackWhite:aM,BlendColor:ag,BlendImage:av,Blur:am,Brightness:ax,Brownie:aC,ColorMatrix:ab,Composed:aD,Contrast:aA,Convolute:aP,Gamma:aR,Grayscale:aB,HueRotation:aN,Invert:aV,Kodachrome:aT,Noise:aX,Pixelate:az,Polaroid:ak,RemoveColor:aW,Resize:aY,Saturation:aH,Sepia:aj,Technicolor:aO,Vibrance:aG,Vintage:aS});var aU=t=>{var{element:e}=t,{width:i,height:r}=e,s=e.getObjects()[0],{customFixedType:n}=s,o=e.getObjects()[1],{strokeWidth:a=0}=o,l=i-a,h=r-a;o.set({width:l,height:h,left:-i/2,top:-r/2});var{width:c,height:d}=s.getOriginalSize(),u=(i-2*a+1)/c,g=(r-2*a+1)/d,f=Math.min(u,g),p=Math.max(u,g),v=f,m=f;n===tO.FILL?(v=p,m=p):n===tO.FULL&&(v=u,m=g);var x=-(c*v)/2,y=-(d*m)/2;n===tO.AUTO&&o.set({width:Math.min(l,c*v+a),height:Math.min(h,d*m+a),left:Math.max(-i/2,x-a),top:Math.max(-r/2,y-a)}),s.set({left:x,top:y,width:c,height:d,scaleX:v,scaleY:m})},aZ=t=>{var{canvas:e,point:i,zoomLevel:r,minZoom:s,maxZoom:n}=t;return r=Math.min(r=Math.max(r,s),n),null==e||e.zoomToPoint(i,r),[...null==e?void 0:e.viewportTransform]},aq=t=>{var{canvas:e,vpt:i}=t;return null==e||e.setViewportTransform(i),null==e||e.requestRenderAll(),[...null==e?void 0:e.viewportTransform]},aK=t=>{var{canvas:e,scale:i,point:r}=t,s=e.viewportTransform,n=s[0],o=s[3],a=s[4],l=s[5],h=(r.x*n+a)*i,c=(r.y*o+l)*i;return{x:h,y:c}},aJ=t=>{var{canvas:e,scale:i}=t,r=null==e?void 0:e.getActiveObject();if(e&&r){var s=r.getBoundingRect(),n={x:s.left,y:s.top},o={x:s.left+s.width,y:s.top+s.height};return{tl:aK({canvas:e,scale:i,point:n}),br:aK({canvas:e,scale:i,point:o})}}return{tl:{x:-9999,y:-9999},br:{x:-9999,y:-9999}}},aQ=t=>{var e,{element:i}=t;if(!!i.clipPath){var r=i.clipPath,s=null!==(e=i.padding)&&void 0!==e?e:0,{height:n,width:o}=i,a=n+2*s,l=o+2*s;null==r||r.set({originX:"left",originY:"top",left:-l/2,top:-a/2,height:a,width:l,absolutePositioned:!1})}},a$=t=>(null==t?void 0:t.customType)===tC.GROUP,a0={borderColor:"#4D53E8",borderWidth:2,cornerStyle:"circle",cornerColor:"#ffffff",cornerStrokeColor:"#4D53E8",transparentCorners:!1,borderOpacityWhenMoving:.8},a1={fontSize:24,fontFamily:"常规体-思源黑体",fill:"#000000ff",stroke:"#000000ff",strokeWidth:0,textAlign:tT.LEFT,lineHeight:1.2},a2={fill:"#ccccccff",stroke:"#000000ff",strokeWidth:0,width:200,height:200},a3={[tC.INLINE_TEXT]:a1,[tC.BLOCK_TEXT]:(0,D._)((0,E._)({},a1),{width:200,height:200,padding:12,splitByGrapheme:!0}),[tC.RECT]:a2,[tC.CIRCLE]:(0,D._)((0,E._)({},a2),{rx:a2.width/2,ry:a2.height/2}),[tC.TRIANGLE]:a2,[tC.STRAIGHT_LINE]:{strokeWidth:1,stroke:"#ccccccff",strokeLineCap:"round"},[tC.PENCIL]:{strokeWidth:1,stroke:"#000000ff"},[tC.IMAGE]:{customFixedType:tO.FILL,stroke:"#000000ff",strokeWidth:0,width:400,height:400,opacity:1},[tC.GROUP]:{}},a6=t=>{var{left:e,top:i,height:r,width:s,scaleX:n,scaleY:o,angle:a,strokeWidth:l}=t,h=r*o+l,c=s*n+l,d=Math.PI/180*a;return lt({tl:{x:e,y:i},tr:{x:e+c*Math.cos(d),y:i+c*Math.sin(d)},bl:{x:e-h*Math.sin(d),y:i+h*Math.cos(d)},br:{x:e-h*Math.sin(d)+c*Math.cos(d),y:i+h*Math.cos(d)+c*Math.sin(d)}})},a5=t=>{var{width:e,scaleX:i,strokeWidth:r}=t;return e*i+r},a8=t=>{var{height:e,scaleY:i,strokeWidth:r}=t;return e*i+r},a4=t=>{var{nextWidth:e,target:i}=t;return(e-i.strokeWidth)/i.scaleX},a9=t=>{var{nextHeight:e,target:i}=t;return(e-i.strokeWidth)/i.scaleY},a7=(t,e)=>.01>Math.abs(t-e),lt=t=>{var{tl:e,tr:i,bl:r,br:s}=t;return{tl:e,tr:i,m:{x:e.x+(s.x-e.x)/2,y:e.y+(s.y-e.y)/2},bl:r,br:s}},le=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"y",r=[],s=[],n=1/0,o=1/0;return e.forEach(e=>{t.forEach(t=>{Object.values(t).forEach(a=>{var l=Math.abs(a[i]-e);a7(l,o)?(r.push(t),s.push(a)):l<o&&(r=[t],s=[a],n=a[i]-e,o=l)})})}),{object:r,snapPoints:s,distance:n,distanceAbs:o}},li=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=t.filter(Boolean),i=e.sort((t,e)=>t.snapDistance-e.snapDistance)[0],r=e.filter(t=>a7(t.snapDistance,i.snapDistance)).map(t=>t.helplines).flat();return(0,D._)((0,E._)({},i),{helplines:r})},lr=t=>{var e=t.tl;return Object.values(t).forEach(t=>{t.y>e.y&&(e=t)}),e},ls=t=>{var e=t.tl;return Object.values(t).forEach(t=>{t.y<e.y&&(e=t)}),e},ln=t=>{var e=t.tl;return Object.values(t).forEach(t=>{t.x<e.x&&(e=t)}),e},lo=t=>{var e=t.tl;return Object.values(t).forEach(t=>{t.x>e.x&&(e=t)}),e},la=(t,e)=>t.min>=e.min&&t.min<=e.max||t.max>=e.min&&t.max<=e.max||t.min<=e.min&&t.max>=e.max,ll=(t,e)=>{if(t.min<=e.min&&t.max>=e.max)return e.min+(e.max-e.min)/2;if(t.min>=e.min&&t.max<=e.max)return t.min+(t.max-t.min)/2;if(t.min>=e.min&&t.min<=e.max)return t.min+(e.max-t.min)/2;return t.max-(t.max-e.min)/2},lh=t=>{var{otherPoints:e,targetPoint:i,direction:r}=t,s=e.sort((t,e)=>t.tl[r]-e.tl[r]),n={prev:{point:s[0],distance:1/0},next:{point:s[0],distance:1/0},durationObjects:[]};if("x"===r){var o={min:i.tl.y,max:i.bl.y},a=s.filter(t=>la({min:ls(t).y,max:lr(t).y},o));n.durationObjects=a,a.forEach(t=>{var e=ln(t),r=lo(t),s=i.tl.x-r.x;s>0&&s<n.prev.distance&&(n.prev={point:t,distance:s});var o=e.x-i.tr.x;o>0&&o<n.next.distance&&(n.next={point:t,distance:o})})}else{var l={min:i.tl.x,max:i.tr.x},h=s.filter(t=>la({min:ln(t).x,max:lo(t).x},l));n.durationObjects=h,h.forEach(t=>{var e=ls(t),r=lr(t),s=i.tl.y-r.y;s>0&&s<n.prev.distance&&(n.prev={point:t,distance:s});var o=e.y-i.bl.y;o>0&&o<n.next.distance&&(n.next={point:t,distance:o})})}return n},lc=t=>{var e,i,r,s,{otherPoints:n,targetPoint:o,threshold:a,controlType:l}=t,h={top:{helplines:[],snapDistance:1/0,next:o.tl.y},left:{helplines:[],snapDistance:1/0,next:o.tl.x}};if(!n||0===n.length)return h;var c=lh({otherPoints:n,targetPoint:o,direction:"x"}),d=[],u=c.durationObjects.findIndex(t=>t===c.next.point);if(-1!==u)for(var g=u;g<c.durationObjects.length-1;g++){var f=c.durationObjects[g],p=c.durationObjects[g+1],v=ln(p).x-lo(f).x;v>0&&d.push({from:f,to:p,distance:v})}var m=[];if(-1!==(u=c.durationObjects.findIndex(t=>t===c.prev.point)))for(var y=u;y>0;y--){var _=c.durationObjects[y],b=c.durationObjects[y-1],w=ln(_).x-lo(b).x;w>0&&m.push({from:b,to:_,distance:w})}if(c.prev.distance!==1/0&&c.next.distance!==1/0&&Math.abs(c.prev.distance-c.next.distance)<2*a){var C=(c.next.distance-c.prev.distance)/2;[x.ControlType.TopLeft,x.ControlType.Left,x.ControlType.BottomLeft].includes(l)?C=c.next.distance-c.prev.distance:[x.ControlType.TopRight,x.ControlType.Right,x.ControlType.BottomRight].includes(l)&&(C=-(c.next.distance-c.prev.distance));var S=ll({min:ls(c.prev.point).y,max:lr(c.prev.point).y},{min:o.tl.y,max:o.bl.y}),T=ll({min:ls(c.next.point).y,max:lr(c.next.point).y},{min:o.tl.y,max:o.bl.y}),O=C+o.tl.x,k=C+o.tr.x;[x.ControlType.TopLeft,x.ControlType.Left,x.ControlType.BottomLeft].includes(l)?k=o.tr.x:[x.ControlType.TopRight,x.ControlType.Right,x.ControlType.BottomRight].includes(l)&&(O=o.tl.x,k=o.tr.x-C),h=(0,D._)((0,E._)({},h),{left:{snapDistance:Math.abs(C),helplines:[[{x:lo(c.prev.point).x,y:S},{x:O,y:S}],[{x:k,y:T},{x:ln(c.next.point).x,y:T}],...d.concat(m).filter(t=>a7(t.distance,(c.prev.distance+c.next.distance)/2)).map(t=>{var e=ll({min:ls(t.from).y,max:lr(t.from).y},{min:ls(t.to).y,max:lr(t.to).y});return[{x:lo(t.from).x,y:e},{x:ln(t.to).x,y:e}]})],next:C,isSnap:!0}})}else{if(d.length>0){var j=[],M=999,A=d[0];if(d.forEach(t=>{var e=t.distance-c.next.distance;Math.abs(e)<Math.abs(M)&&(M=e,A=t)}),Math.abs(M)<=a&&(d.forEach(t=>{a7(t.distance,A.distance)&&j.push(t)}),j.length>0)){var L=c.next.distance-j[0].distance;[x.ControlType.TopRight,x.ControlType.Right,x.ControlType.BottomRight].includes(l)&&(L=-(c.next.distance-j[0].distance));var P=ll({min:ls(c.next.point).y,max:lr(c.next.point).y},{min:o.tl.y,max:o.bl.y}),F=L+o.tr.x;[x.ControlType.TopRight,x.ControlType.Right,x.ControlType.BottomRight].includes(l)&&(F=o.tr.x-L),h=(0,D._)((0,E._)({},h),{left:{snapDistance:Math.abs(L),helplines:[[{x:F,y:P},{x:ln(c.next.point).x,y:P}],...j.map(t=>{var e=ll({min:ls(t.from).y,max:lr(t.from).y},{min:ls(t.to).y,max:lr(t.to).y});return[{x:lo(t.from).x,y:e},{x:ln(t.to).x,y:e}]})],next:L,isSnap:!0}})}}if(m.length>0){var R=[],I=999,B=m[0];if(m.forEach(t=>{var e=t.distance-c.prev.distance;Math.abs(e)<Math.abs(I)&&(I=e,B=t)}),Math.abs(I)<=a&&(m.forEach(t=>{a7(t.distance,B.distance)&&R.push(t)}),R.length>0)){var N,V=R[0].distance-c.prev.distance,X=ll({min:ls(c.prev.point).y,max:lr(c.prev.point).y},{min:o.tl.y,max:o.bl.y});Math.abs(V)<((null===(N=h.left)||void 0===N?void 0:N.snapDistance)||1/0)&&(h=(0,D._)((0,E._)({},h),{left:{snapDistance:Math.abs(V),helplines:[[{x:V+o.tl.x,y:X},{x:lo(c.prev.point).x,y:X}],...R.map(t=>{var e=ll({min:ls(t.from).y,max:lr(t.from).y},{min:ls(t.to).y,max:lr(t.to).y});return[{x:lo(t.from).x,y:e},{x:ln(t.to).x,y:e}]})],next:V,isSnap:!0}}))}}}var z=lh({otherPoints:n,targetPoint:o,direction:"y"});if(d=[],-1!==(u=z.durationObjects.findIndex(t=>t===z.next.point)))for(var W=u;W<z.durationObjects.length-1;W++){var Y=z.durationObjects[W],H=z.durationObjects[W+1],G=ls(H).y-lr(Y).y;G>0&&d.push({from:Y,to:H,distance:G})}if(m=[],-1!==(u=z.durationObjects.findIndex(t=>t===z.prev.point)))for(var U=u;U>0;U--){var Z=z.durationObjects[U],q=z.durationObjects[U-1],K=ls(Z).y-lr(q).y;K>0&&m.push({from:q,to:Z,distance:K})}if(z.prev.distance!==1/0&&z.next.distance!==1/0&&Math.abs(z.prev.distance-z.next.distance)<2*a){var J=(z.next.distance-z.prev.distance)/2;[x.ControlType.TopLeft,x.ControlType.TopRight,x.ControlType.Top].includes(l)?J=z.next.distance-z.prev.distance:[x.ControlType.BottomLeft,x.ControlType.BottomRight,x.ControlType.Bottom].includes(l)&&(J=z.prev.distance-z.next.distance);var Q=ll({min:ln(z.prev.point).x,max:lo(z.prev.point).x},{min:o.tl.x,max:o.tr.x}),$=ll({min:ln(z.next.point).x,max:lo(z.next.point).x},{min:o.tl.x,max:o.tr.x}),tt=J+o.tl.y,te=J+o.bl.y;[x.ControlType.TopLeft,x.ControlType.TopRight,x.ControlType.Top].includes(l)?te=o.bl.y:[x.ControlType.BottomLeft,x.ControlType.BottomRight,x.ControlType.Bottom].includes(l)&&(tt=o.tl.y,te=o.bl.y-J),h=(0,D._)((0,E._)({},h),{top:{snapDistance:Math.abs(J),helplines:[[{x:Q,y:lr(z.prev.point).y},{x:Q,y:tt}],[{x:$,y:te},{x:$,y:ls(z.next.point).y}],...d.concat(m).filter(t=>a7(t.distance,(z.prev.distance+z.next.distance)/2)).map(t=>{var e=ll({min:ls(t.from).y,max:lr(t.from).y},{min:ls(t.to).y,max:lr(t.to).y});return[{x:lo(t.from).x,y:e},{x:ln(t.to).x,y:e}]})],next:J,isSnap:!0}})}else{if(d.length>0){var ti=[],tr=999,ts=d[0];if(d.forEach(t=>{var e=t.distance-z.next.distance;Math.abs(e)<Math.abs(tr)&&(tr=e,ts=t)}),Math.abs(tr)<=a&&(d.forEach(t=>{a7(t.distance,ts.distance)&&ti.push(t)}),ti.length>0)){var tn=z.next.distance-ti[0].distance;[x.ControlType.Bottom,x.ControlType.BottomLeft,x.ControlType.BottomRight].includes(l)&&(tn=ti[0].distance-z.next.distance);var to=ll({min:ln(z.next.point).x,max:lo(z.next.point).x},{min:o.tl.x,max:o.tr.x}),ta=tn+o.bl.y;[x.ControlType.BottomRight,x.ControlType.Bottom,x.ControlType.BottomLeft].includes(l)&&(ta=o.bl.y-tn),h=(0,D._)((0,E._)({},h),{top:{snapDistance:Math.abs(tn),helplines:[[{x:to,y:ta},{x:to,y:ls(z.next.point).y}],...ti.map(t=>{var e=ll({min:ln(t.from).x,max:lo(t.from).x},{min:ln(t.to).x,max:lo(t.to).x});return[{x:e,y:lr(t.from).y},{x:e,y:ls(t.to).y}]})],next:tn,isSnap:!0}})}}if(m.length>0){var tl=[],th=999,tc=m[0];if(m.forEach(t=>{var e=t.distance-z.prev.distance;Math.abs(e)<Math.abs(th)&&(th=e,tc=t)}),Math.abs(th)<=a&&(m.forEach(t=>{a7(t.distance,tc.distance)&&tl.push(t)}),tl.length>0)){var td,tu=-(z.prev.distance-tl[0].distance),tg=ll({min:ln(z.prev.point).x,max:lo(z.prev.point).x},{min:o.tl.x,max:o.tr.x});Math.abs(tu)<((null===(td=h.top)||void 0===td?void 0:td.snapDistance)||1/0)&&(h=(0,D._)((0,E._)({},h),{top:{snapDistance:Math.abs(tu),helplines:[[{x:tg,y:tu+o.tl.y},{x:tg,y:lr(z.prev.point).y}],...tl.map(t=>{var e=ll({min:ln(t.from).x,max:lo(t.from).x},{min:ln(t.to).x,max:lo(t.to).x});return[{x:e,y:lr(t.from).y},{x:e,y:ls(t.to).y}]})],next:tu,isSnap:!0}}))}}}return[x.ControlType.TopLeft,x.ControlType.TopRight,x.ControlType.Top].includes(l)?(null===(e=h.top)||void 0===e?void 0:e.isSnap)&&(h.height=(0,D._)((0,E._)({},h.top),{helplines:[],next:-h.top.next})):[x.ControlType.BottomLeft,x.ControlType.BottomRight,x.ControlType.Bottom].includes(l)&&(null===(i=h.top)||void 0===i?void 0:i.isSnap)&&(h.height=(0,D._)((0,E._)({},h.top),{helplines:[],next:-h.top.next}),h.top.next=0),[x.ControlType.TopLeft,x.ControlType.Left,x.ControlType.BottomLeft].includes(l)?(null===(r=h.left)||void 0===r?void 0:r.isSnap)&&(h.width=(0,D._)((0,E._)({},h.left),{helplines:[],next:-h.left.next})):[x.ControlType.TopRight,x.ControlType.Right,x.ControlType.BottomRight].includes(l)&&(null===(s=h.left)||void 0===s?void 0:s.isSnap)&&(h.width=(0,D._)((0,E._)({},h.left),{helplines:[],next:-h.left.next}),h.left.next=0),h},ld=t=>{var{targetPoint:e,latestDistance:i,controlType:r,direction:s}=t;return({[x.ControlType.TopLeft]:[e.tl],[x.ControlType.TopRight]:[e.tr],[x.ControlType.BottomLeft]:[e.bl],[x.ControlType.BottomRight]:[e.br],[x.ControlType.Top]:[{x:e.tl.x+(e.tr.x-e.tl.x)/2,y:e.tl.y}],[x.ControlType.Left]:[{x:e.tl.x,y:e.tl.y+(e.bl.y-e.tl.y)/2}],[x.ControlType.Bottom]:[{x:e.tl.x+(e.br.x-e.tl.x)/2,y:e.bl.y}],[x.ControlType.Right]:[{x:e.tr.x,y:e.tl.y+(e.br.y-e.tl.y)/2}],[x.ControlType.Center]:Object.values(e).map(t=>(0,D._)((0,E._)({},t),{isTarget:!0}))})[r].map(t=>(0,D._)((0,E._)({},t),{[s]:t[s]+i}))},lu=t=>{var{latestDistance:e,latestDistanceAbs:i,helplines:r,attribute:s}=t;if("top|height"===s)return{top:{helplines:r,snapDistance:i,next:e,isSnap:!0},height:{helplines:[],snapDistance:999,next:-e,isSnap:!0}};if("left|width"===s)return{left:{helplines:r,snapDistance:i,next:e,isSnap:!0},width:{helplines:[],snapDistance:999,next:-e,isSnap:!0}};if("top"===s)return{top:{helplines:r,snapDistance:i,next:e,isSnap:!0}};else if("left"===s)return{left:{helplines:r,snapDistance:i,next:e,isSnap:!0}};else if("width"===s)return{width:{helplines:r,snapDistance:i,next:e,isSnap:!0}};else if("height"===s)return{height:{helplines:r,snapDistance:i,next:e,isSnap:!0}};return{}},lg=t=>{var e,i,r,s,n,o,{otherPoints:a,targetPoint:l,threshold:h,controlType:c}=t,d={top:{helplines:[],snapDistance:999,next:l.tl.y},left:{helplines:[],snapDistance:999,next:l.tl.x},width:{helplines:[],snapDistance:999,next:l.tl.x},height:{helplines:[],snapDistance:999,next:l.tl.y}};return a&&0!==a.length?(({[x.ControlType.TopLeft]:[{key:"top|height",direction:"y",snapValue:[l.tl.y]},{key:"left|width",direction:"x",snapValue:[l.tl.x]}],[x.ControlType.TopRight]:[{key:"top|height",direction:"y",snapValue:[l.tr.y]},{key:"width",direction:"x",snapValue:[l.tr.x]}],[x.ControlType.BottomLeft]:[{key:"height",direction:"y",snapValue:[l.bl.y]},{key:"left|width",direction:"x",snapValue:[l.bl.x]}],[x.ControlType.BottomRight]:[{key:"height",direction:"y",snapValue:[l.br.y]},{key:"width",direction:"x",snapValue:[l.br.x]}],[x.ControlType.Top]:[{key:"top|height",direction:"y",snapValue:[l.tl.y]}],[x.ControlType.Left]:[{key:"left|width",direction:"x",snapValue:[l.tl.x]}],[x.ControlType.Bottom]:[{key:"height",direction:"y",snapValue:[l.bl.y]}],[x.ControlType.Right]:[{key:"width",direction:"x",snapValue:[l.tr.x]}],[x.ControlType.Center]:[{key:"top",direction:"y",snapValue:Array.from(new Set(Object.values(l).map(t=>t.y)))},{key:"left",direction:"x",snapValue:Array.from(new Set(Object.values(l).map(t=>t.x)))}]})[c].forEach(t=>{var{snapPoints:e,distance:i,distanceAbs:r}=le(a,t.snapValue,t.direction);if(r<=h){var s=[],n=[];a.forEach(t=>{n.push(...Object.values(t))}),n.push(...ld({targetPoint:l,latestDistance:i,controlType:c,direction:t.direction}));var o="x"===t.direction?"y":"x";e.forEach(e=>{var i=n.filter(i=>a7(i[t.direction],e[t.direction])).sort((t,e)=>t[o]-e[o]);s.push(i)}),d=(0,E._)({},d,lu({latestDistance:i,latestDistanceAbs:r,helplines:s,attribute:t.key}))}}),c===x.ControlType.Center&&(null===(e=d.top)||void 0===e?void 0:e.isSnap)&&(null===(r=d.left)||void 0===r?void 0:null===(i=r.helplines)||void 0===i?void 0:i.length)&&(d.left.helplines=d.left.helplines.map(t=>t.map(t=>{if(t.isTarget){var e,i;return(0,D._)((0,E._)({},t),{y:t.y+(null!==(i=null===(e=d.top)||void 0===e?void 0:e.next)&&void 0!==i?i:0)})}return t}))),c===x.ControlType.Center&&(null===(s=d.left)||void 0===s?void 0:s.isSnap)&&(null===(o=d.top)||void 0===o?void 0:null===(n=o.helplines)||void 0===n?void 0:n.length)&&(d.top.helplines=d.top.helplines.map(t=>t.map(t=>{if(t.isTarget){var e,i;return(0,D._)((0,E._)({},t),{x:t.x+(null!==(i=null===(e=d.left)||void 0===e?void 0:e.next)&&void 0!==i?i:0)})}return t}))),d):d},lf=t=>'\n    <svg\n      viewBox="0 0 1024 1024"\n      version="1.1"\n      xmlns="http://www.w3.org/2000/svg"\n      width="'.concat(12,'"\n      height="').concat(12,'"\n      class="absolute opacity-80 rotate-45"\n      style="\n        top: ').concat(t.y-6,"px;\n        left: ").concat(t.x-6,'px;\n        fill: #00B2B2;\n      "\n      >\n        <path d="M930.688 487.338667s3.584 0.469333 6.314667 1.322666a32 32 0 0 1 5.973333 58.496c-4.906667 2.730667-15.530667 4.053333-15.530667 4.053334H96.554667s-3.584-0.085333-6.442667-0.682667a31.786667 31.786667 0 0 1-16.725333-9.301333 32.256 32.256 0 0 1 0-44.074667 32.213333 32.213333 0 0 1 7.637333-5.930667c4.906667-2.730667 15.530667-4.010667 15.530667-4.010666l834.133333 0.128z" p-id="4210"></path>\n        <path d="M516.864 72.149333a32.042667 32.042667 0 0 1 25.685333 22.016 59.733333 59.733333 0 0 1 1.450667 9.6v830.848s-1.322667 10.666667-4.010667 15.530667a31.573333 31.573333 0 0 1-13.909333 13.226667 32.341333 32.341333 0 0 1-36.138667-5.546667 32.341333 32.341333 0 0 1-9.301333-16.768c-0.554667-2.816-0.64-6.442667-0.64-6.442667V103.765333s0.469333-6.4 1.450667-9.6a32.298667 32.298667 0 0 1 19.456-20.394666 34.816 34.816 0 0 1 12.714666-1.962667l3.242667 0.341333z"></path>\n      </svg>'),lp=(t,e)=>{var i="";return a7(t.x,e.x)?i+='<div\n    class="absolute bg-[#00B2B2]"\n    style="\n      top: '.concat(Math.min(t.y,e.y),"px;\n      left: ").concat(t.x-.5,"px;\n      width: ").concat(1,"px;\n      height: ").concat(Math.abs(e.y-t.y),'px;\n    "\n    ></div>'):i+='<div\n    class="absolute bg-[#00B2B2]"\n    style="\n      top: '.concat(t.y-.5,"px;\n      left: ").concat(Math.min(t.x,e.x),"px;\n      width: ").concat(Math.abs(e.x-t.x),"px;\n      height: ").concat(1,'px;\n    "\n    ></div>'),i},lv=class t{constructor(t,e,i){this.objects=[],this.resetScale=t=>{this.scale=t},this.test=t=>{var e=document.getElementById(this.helpLineLayerId);if(!!e){var i="";t.forEach(t=>{var e=aK({canvas:this.canvas,scale:this.scale,point:t});i+=lf(e)}),e.innerHTML=i}},this.show=t=>{var e=document.getElementById(this.helpLineLayerId);if(e){var i="";t.forEach(t=>{t.forEach(t=>{var e=aK({canvas:this.canvas,scale:this.scale,point:t});i+=lf(e)});var e=aK({canvas:this.canvas,scale:this.scale,point:t[0]}),r=aK({canvas:this.canvas,scale:this.scale,point:t[t.length-1]});i+=lp(e,r)}),e.innerHTML=i}},this.hide=()=>{var t=document.getElementById(this.helpLineLayerId);t&&(t.innerHTML="")},this.canvas=t,this.helpLineLayerId=e,this.scale=null!=i?i:1}};class lm{constructor(t,e,i){this.threshold=10,this.rules=[lc,lg],this.testPoints=[],this.snapOpen=!0,this.devMode=!1,this.onKeyDown=t=>{if("meta"===t.key.toLowerCase());else if("shift"===t.key.toLowerCase()&&this.devMode){var e=this.canvas.getActiveObject();if(e){var i=a6(e);this.testPoints=Object.values(i),this.helpline.test(this.testPoints)}}},this.onKeyUp=t=>{"meta"===t.key.toLowerCase()||"shift"===t.key.toLowerCase()&&this.devMode&&this.helpline.hide()},this.points=[],this.resetAllObjectsPosition=t=>{var e,i,r=this.canvas.getObjects(),s=[],n=[t,...null!==(i=null==t?void 0:null===(e=t.getObjects)||void 0===e?void 0:e.call(t))&&void 0!==i?i:[]];r.filter(t=>!n.includes(t)).forEach(t=>{s.push(t.aCoords)}),this.points=s.map(lt)},this.reset=()=>{this.helpline.hide()},this._move=t=>{var e,i,r,s,n,o,a,l,{target:h,controlType:c}=t;if(!!this.snapOpen){var d=a6(h),u=this.rules.map(t=>t({otherPoints:this.points,targetPoint:d,threshold:this.threshold,controlType:c})),g={top:li(u.map(t=>t.top).filter(Boolean)),left:li(u.map(t=>t.left).filter(Boolean)),height:li(u.map(t=>t.height).filter(Boolean)),width:li(u.map(t=>t.width).filter(Boolean))},f=[...(null===(e=g.top)||void 0===e?void 0:e.helplines)||[],...(null===(i=g.left)||void 0===i?void 0:i.helplines)||[],...(null===(r=g.height)||void 0===r?void 0:r.helplines)||[],...(null===(s=g.width)||void 0===s?void 0:s.helplines)||[]];this.helpline.show(f);var p={top:(null===(n=g.top)||void 0===n?void 0:n.isSnap)?h.top+g.top.next:h.top,left:(null===(o=g.left)||void 0===o?void 0:o.isSnap)?h.left+g.left.next:h.left,width:(null===(a=g.width)||void 0===a?void 0:a.isSnap)?a4({nextWidth:a5(h)+g.width.next,target:h}):h.width,height:(null===(l=g.height)||void 0===l?void 0:l.isSnap)?a9({nextHeight:a8(h)+g.height.next,target:h}):h.height};return Object.keys(p).forEach(t=>{var e;(null===(e=g[t])||void 0===e?void 0:e.isSnap)&&h.set(t,p[t])}),p}},this.move=t=>this._move({target:t,controlType:x.ControlType.Center}),this.resize=(t,e)=>{if(0===t.angle)return this._move({target:t,controlType:e})},this.destroy=()=>{this.reset(),document.removeEventListener("keydown",this.onKeyDown),document.removeEventListener("keyup",this.onKeyUp)},this.canvas=t,this.helpline=new lv(t,e,i),document.addEventListener("keydown",this.onKeyDown),document.addEventListener("keyup",this.onKeyUp)}}var lx=(t,e,i)=>(M&&M.destroy(),M=new lm(t,e,i)),ly=t=>t>=360?ly(t-360):t<0?ly(t+360):t,l_=t=>"data:image/svg+xml;base64,".concat(btoa(t)),lb=l_('<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><g filter="url(#a)"><mask id="b" width="16" height="16" x="8" y="8" fill="#000" maskUnits="userSpaceOnUse"><path fill="#fff" d="M8 8h16v16H8z"/><path fill-rule="evenodd" d="M12.87 22 10 19.13h1.99v-.659a6.483 6.483 0 0 1 6.482-6.482h.723V10l2.869 2.87-2.87 2.869v-1.99h-.722a4.722 4.722 0 0 0-4.722 4.722v.66h1.989L12.869 22" clip-rule="evenodd"/></mask><path fill="#000" fill-rule="evenodd" d="M12.87 22 10 19.13h1.99v-.659a6.483 6.483 0 0 1 6.482-6.482h.723V10l2.869 2.87-2.87 2.869v-1.99h-.722a4.722 4.722 0 0 0-4.722 4.722v.66h1.989L12.869 22" clip-rule="evenodd"/><path fill="#fff" d="M10 19.13v-.8H8.068l1.366 1.367.566-.566M12.87 22l-.567.566.566.566.566-.566L12.87 22m-.88-2.87v.801h.8v-.8h-.8m6.482-7.141v-.8.8m.723 0v.8h.8v-.8h-.8m0-1.989.565-.566-1.366-1.366V10h.8m2.869 2.87.566.565.566-.566-.566-.566-.566.566m-2.87 2.869h-.8v1.932l1.366-1.366-.566-.566m0-1.99h.8v-.8h-.8v.8m-.722 0v-.8.8m-4.722 5.382h-.8v.8h.8v-.8m1.989 0 .566.566 1.366-1.367h-1.932v.8m-6.305.566 2.87 2.869 1.131-1.132-2.87-2.87-1.13 1.133m2.555-1.367H10v1.601h1.99v-1.6m-.8.142v.659h1.6v-.66h-1.6m7.283-7.284a7.283 7.283 0 0 0-7.283 7.283h1.6a5.682 5.682 0 0 1 5.683-5.682v-1.6m.723 0h-.723v1.601h.723v-1.6m.8.8V10h-1.6v1.989h1.6m-1.366-1.422 2.869 2.87 1.132-1.133-2.87-2.869-1.131 1.132m2.869 1.737-2.87 2.87 1.132 1.132 2.87-2.87-1.132-1.132m-1.503 3.436v-1.99h-1.6v1.99h1.6m-1.523-1.19h.723v-1.6h-.723v1.6m-3.922 3.922a3.922 3.922 0 0 1 3.922-3.921v-1.601a5.522 5.522 0 0 0-5.523 5.522h1.601m0 .66v-.66h-1.6v.66h1.6m1.189-.8h-1.99v1.6h1.99v-1.6m-2.304 4.235 2.87-2.87-1.132-1.131-2.87 2.87 1.132 1.13" mask="url(#b)"/></g><defs><filter id="a" width="18.728" height="18.665" x="6.268" y="7.268" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="1"/><feGaussianBlur stdDeviation=".9"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.65 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow_184_36430"/><feBlend in="SourceGraphic" in2="effect1_dropShadow_184_36430" result="shape"/></filter></defs></svg>'),lw=l_('<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><g filter="url(#a)"><mask id="b" width="16" height="16" x="8" y="8" fill="#000" maskUnits="userSpaceOnUse"><path fill="#fff" d="M8 8h16v16H8z"/><path fill-rule="evenodd" d="M10 12.87 12.87 10v1.99h.658a6.482 6.482 0 0 1 6.482 6.482v.722H22l-2.87 2.87-2.868-2.87h1.989v-.722a4.722 4.722 0 0 0-4.722-4.722h-.659v1.988L10 12.87" clip-rule="evenodd"/></mask><path fill="#000" fill-rule="evenodd" d="M10 12.87 12.87 10v1.99h.658a6.482 6.482 0 0 1 6.482 6.482v.722H22l-2.87 2.87-2.868-2.87h1.989v-.722a4.722 4.722 0 0 0-4.722-4.722h-.659v1.988L10 12.87" clip-rule="evenodd"/><path fill="#fff" d="M12.87 10h.8V8.068l-1.367 1.366.566.566M10 12.87l-.566-.567-.566.566.566.566.566-.566m2.87-.88h-.801v.8h.8v-.8m7.14 7.204h-.8v.8h.8v-.8m1.989 0 .566.566 1.366-1.366H22v.8m-2.87 2.87-.565.565.566.566.566-.566-.566-.566m-2.868-2.87v-.8h-1.932l1.366 1.366.566-.566m1.989 0v.8h.8v-.8h-.8m-5.38-5.443v-.8h-.801v.8h.8m0 1.987-.567.566 1.366 1.366v-1.932h-.8m-.567-6.304-2.869 2.87 1.132 1.131 2.869-2.87-1.132-1.13m1.366 2.556V10h-1.6v1.99h1.6m-.141-.8h-.659v1.6h.659v-1.6m7.283 7.282a7.283 7.283 0 0 0-7.283-7.282v1.6a5.682 5.682 0 0 1 5.682 5.682h1.6m0 .722v-.722h-1.6v.722h1.6m-.8.8h1.988v-1.6H20.01v1.6m1.422-1.366-2.869 2.87 1.132 1.131 2.869-2.869-1.132-1.132m-1.737 2.87-2.87-2.87-1.131 1.132 2.869 2.87 1.132-1.132m-3.435-1.503h1.989v-1.6h-1.99v1.6m1.188-1.523v.722h1.601v-.722h-1.6m-3.921-3.921a3.921 3.921 0 0 1 3.921 3.921h1.601a5.522 5.522 0 0 0-5.522-5.522v1.6m-.659 0h.659v-1.6h-.659v1.6m.8 1.187v-1.987h-1.6v1.987h1.6m-4.235-2.303 2.87 2.87 1.131-1.133-2.87-2.869-1.13 1.132" mask="url(#b)"/></g><defs><filter id="a" width="18.663" height="18.727" x="7.068" y="7.268" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="1"/><feGaussianBlur stdDeviation=".9"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.65 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow_184_36425"/><feBlend in="SourceGraphic" in2="effect1_dropShadow_184_36425" result="shape"/></filter></defs></svg>'),lC=l_('<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><g filter="url(#a)"><mask id="b" width="16" height="16" x="8" y="8" fill="#000" maskUnits="userSpaceOnUse"><path fill="#fff" d="M8 8h16v16H8z"/><path fill-rule="evenodd" d="M19.13 10 22 12.87h-1.99v.659a6.483 6.483 0 0 1-6.482 6.482h-.723V22l-2.869-2.87 2.87-2.869v1.99h.722a4.722 4.722 0 0 0 4.722-4.723v-.659h-1.989L19.131 10" clip-rule="evenodd"/></mask><path fill="#000" fill-rule="evenodd" d="M19.13 10 22 12.87h-1.99v.659a6.483 6.483 0 0 1-6.482 6.482h-.723V22l-2.869-2.87 2.87-2.869v1.99h.722a4.722 4.722 0 0 0 4.722-4.723v-.659h-1.989L19.131 10" clip-rule="evenodd"/><path fill="#fff" d="M22 12.87v.8h1.932l-1.366-1.367-.566.566M19.13 10l.567-.566-.566-.566-.566.566.566.566m.88 2.87v-.801h-.8v.8h.8m0 .659h-.8.8m-7.205 6.482v-.8h-.8v.8h.8m0 1.989-.565.566 1.366 1.366V22h-.8m-2.869-2.87-.566-.565-.566.566.566.566.566-.566m2.87-2.869h.8V14.33l-1.366 1.366.566.566m0 1.99h-.8v.8h.8v-.8m5.444-4.723h-.8.8m0-.659h.8v-.8h-.8v.8m-1.989 0-.566-.566-1.366 1.367h1.932v-.8m6.305-.566-2.87-2.869-1.131 1.132 2.87 2.87 1.13-1.133M20.01 13.67H22v-1.601h-1.99v1.6m.8-.142v-.659h-1.6v.66h1.6m-7.283 7.284a7.283 7.283 0 0 0 7.283-7.284h-1.6a5.682 5.682 0 0 1-5.683 5.683v1.6m-.723 0h.723V19.21h-.723v1.6m-.8-.8V22h1.6V20.01h-1.6m1.366 1.422-2.869-2.87-1.132 1.133 2.87 2.869 1.131-1.132m-2.869-1.737 2.87-2.87-1.132-1.132-2.87 2.87 1.132 1.132m1.503-3.436v1.99h1.6v-1.99h-1.6m1.523 1.19h-.723v1.6h.723v-1.6m3.922-3.923a3.922 3.922 0 0 1-3.922 3.922v1.601a5.522 5.522 0 0 0 5.522-5.522h-1.6m0-.659v.66h1.6v-.66h-1.6m-1.189.8h1.99v-1.6h-1.99v1.6m2.304-4.235-2.87 2.87 1.132 1.131 2.87-2.87-1.132-1.13" mask="url(#b)"/></g><defs><filter id="a" width="18.728" height="18.665" x="7.004" y="8.067" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="1"/><feGaussianBlur stdDeviation=".9"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.65 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow_184_36440"/><feBlend in="SourceGraphic" in2="effect1_dropShadow_184_36440" result="shape"/></filter></defs></svg>'),lS=l_('<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><g filter="url(#a)"><mask id="b" width="16" height="16" x="8" y="8" fill="#000" maskUnits="userSpaceOnUse"><path fill="#fff" d="M8 8h16v16H8z"/><path fill-rule="evenodd" d="M22 19.13 19.13 22v-1.99h-.658a6.483 6.483 0 0 1-6.483-6.483v-.722H10l2.87-2.87 2.869 2.87h-1.99v.722a4.722 4.722 0 0 0 4.723 4.722h.659v-1.988L22 19.131" clip-rule="evenodd"/></mask><path fill="#000" fill-rule="evenodd" d="M22 19.13 19.13 22v-1.99h-.658a6.483 6.483 0 0 1-6.483-6.483v-.722H10l2.87-2.87 2.869 2.87h-1.99v.722a4.722 4.722 0 0 0 4.723 4.722h.659v-1.988L22 19.131" clip-rule="evenodd"/><path fill="#fff" d="M19.13 22h-.8v1.932l1.367-1.366L19.13 22M22 19.13l.566.567.566-.566-.566-.566-.566.566m-2.87.88h.801v-.8h-.8v.8m-7.141-6.483h.8-.8m0-.722h.8v-.8h-.8v.8m-1.989 0-.566-.566-1.366 1.366H10v-.8m2.87-2.87.565-.565-.566-.566-.566.566.566.566m2.869 2.87v.8h1.932l-1.366-1.366-.566.566m-1.99 0v-.8h-.8v.8h.8m0 .722h.801-.8m5.382 4.722v.8h.8v-.8h-.8m0-1.988.566-.566-1.367-1.366v1.932h.8m.566 6.305 2.869-2.87-1.132-1.131-2.87 2.87 1.133 1.13M18.33 20.01V22h1.601v-1.99h-1.6m.142.8h.659v-1.6h-.66v1.6m-7.284-7.283a7.283 7.283 0 0 0 7.284 7.283v-1.6a5.682 5.682 0 0 1-5.683-5.683h-1.6m0-.722v.722h1.601v-.722h-1.6m.8-.8H10v1.6h1.989v-1.6m-1.422 1.366 2.87-2.87-1.133-1.131-2.869 2.869 1.132 1.132m1.737-2.87 2.87 2.87 1.132-1.132-2.87-2.87-1.132 1.133m3.436 1.504h-1.99v1.6h1.99v-1.6m-1.189 1.522v-.722h-1.6v.722h1.6m3.922 3.922a3.922 3.922 0 0 1-3.922-3.922h-1.6a5.522 5.522 0 0 0 5.522 5.523v-1.601m.659 0h-.66v1.6h.66v-1.6m-.8-1.188v1.988h1.6v-1.988h-1.6m4.235 2.304-2.87-2.87-1.131 1.132 2.87 2.87 1.13-1.132" mask="url(#b)"/></g><defs><filter id="a" width="18.664" height="18.727" x="6.268" y="8.005" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="1"/><feGaussianBlur stdDeviation=".9"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.65 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow_184_36435"/><feBlend in="SourceGraphic" in2="effect1_dropShadow_184_36435" result="shape"/></filter></defs></svg>'),lT=t=>{var e=ly(t);if(e>=225&&e<315)return lS;if(e>=135&&e<225)return lC;if(e>=45&&e<135)return lw;else return lb},{scalingEqually:lO,scaleCursorStyleHandler:lk,rotationWithSnapping:lj,scalingX:lM,scalingY:lE}=as,lD=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{x:e,y:i,callback:r}=t;return new r6({x:e,y:i,actionHandler:(t,e,i,s)=>(e.target.set({x1:i,y1:s,x2:e.lastX+e.width*("tl"===e.corner?1:-1),y2:e.lastY+e.height}),null==r||r({element:e.target}),!0),actionName:"startControl"})},lA=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{x:e,y:i,callback:r}=t;return new r6({x:e,y:i,actionHandler:(t,e,i,s)=>(e.target.set({x1:e.lastX-e.width*("br"===e.corner?1:-1),y1:e.lastY-e.height,x2:i,y2:s}),null==r||r({element:e.target}),!0),actionName:"endControl"})},lL={width:0,height:0,top:0,left:0},lP=(t,e)=>{var{width:i,height:r,scaleX:s,scaleY:n,strokeWidth:o,angle:a}=t.target,l=Math.max((i+o)*s-o,1),h=Math.max((r+o)*n-o,1);(null==e?void 0:e.scaleEqual)&&(l-lL.width>h-lL.height?h=lL.height/lL.width*l:l=lL.width/lL.height*h);var c=lL.left,d=lL.top;if(null==e?void 0:e.leftTopCalcFn){var u=e.leftTopCalcFn({angle:a,originTop:lL.top,originLeft:lL.left,originWidth:lL.width,originHeight:lL.height,newWidth:l,newHeight:h});c=u.left,d=u.top}t.target.set({width:l,height:h,customFixedHeight:h,scaleX:1,scaleY:1,top:d,left:c})},lF=t=>{var{angle:e,originTop:i,originLeft:r,originWidth:s,originHeight:n,newWidth:o,newHeight:a}=t,l=Math.PI/180*e;return{left:r+s*Math.cos(l)-n*Math.sin(l)-o*Math.cos(l)+a*Math.sin(l),top:i+s*Math.sin(l)+lL.height*Math.cos(l)-o*Math.sin(l)-a*Math.cos(l)}},lR=t=>{var{angle:e,originTop:i,originLeft:r,originHeight:s,newHeight:n}=t,o=Math.PI/180*e;return{left:r-(s-n)*Math.sin(o),top:i+(s-n)*Math.cos(o)}},lI=t=>{var{angle:e,originTop:i,originLeft:r,originWidth:s,newWidth:n,newHeight:o}=t,a=e*Math.PI/180,l=r+s*Math.cos(a),h=i+s*Math.sin(a),c=l-o*Math.sin(a)-n*Math.cos(a),d=h+o*Math.cos(a)-n*Math.sin(a);return{left:c+o*Math.sin(a),top:d-o*Math.cos(a)}},lB=(t,e)=>(lL.width=e.target.width,lL.height=e.target.height,lL.top=e.target.top,lL.left=e.target.left,!1),lN={"e-resize":"ew-resize","w-resize":"ew-resize","n-resize":"ns-resize","s-resize":"ns-resize","nw-resize":"nwse-resize","ne-resize":"nesw-resize","sw-resize":"nesw-resize","se-resize":"nwse-resize"},lV=(t,e,i)=>{var r,s=lk(t,e,i);return null!==(r=lN[s])&&void 0!==r?r:s},lX=t=>{var{e,transformData:i,x:r,y:s,needResetScaleAndSnap:n,fn:o,callback:a,snapPosition:l,leftTopCalcFn:h}=t,c=o((0,D._)((0,E._)({},e),{shiftKey:!!n||!e.shiftKey}),i,r,s);return n&&(lP(i,{scaleEqual:e.shiftKey,leftTopCalcFn:h}),M.resize(i.target,l)),null==a||a({element:i.target}),c},lz=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{callback:e,needResetScaleAndSnap:i=!0}=t;return new r6({x:-.5,y:-.5,cursorStyleHandler:lV,actionHandler:(t,r,s,n)=>lX({e:t,transformData:r,x:s,y:n,needResetScaleAndSnap:i,callback:e,fn:lO,leftTopCalcFn:lF,snapPosition:x.ControlType.TopLeft}),mouseDownHandler:lB,actionName:"resizeTLControl"})},lW=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{callback:e,needResetScaleAndSnap:i=!0}=t;return new r6({x:0,y:-.5,cursorStyleHandler:lV,actionHandler:(t,r,s,n)=>lX({e:t,transformData:r,x:s,y:n,needResetScaleAndSnap:i,callback:e,fn:lE,leftTopCalcFn:lF,snapPosition:x.ControlType.Top}),mouseDownHandler:lB,actionName:"resizeMTControl"})},lY=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{callback:e,needResetScaleAndSnap:i}=t;return new r6({x:.5,y:-.5,cursorStyleHandler:lV,actionHandler:(t,r,s,n)=>lX({e:t,transformData:r,x:s,y:n,needResetScaleAndSnap:i,callback:e,fn:lO,leftTopCalcFn:lR,snapPosition:x.ControlType.TopRight}),mouseDownHandler:lB,actionName:"resizeTRControl"})},lH=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{callback:e,needResetScaleAndSnap:i}=t;return new r6({x:-.5,y:0,cursorStyleHandler:lV,actionHandler:(t,r,s,n)=>lX({e:t,transformData:r,x:s,y:n,needResetScaleAndSnap:i,callback:e,fn:lM,leftTopCalcFn:lI,snapPosition:x.ControlType.Left}),mouseDownHandler:lB,actionName:"resizeMLControl"})},lG=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{callback:e,needResetScaleAndSnap:i}=t;return new r6({x:.5,y:0,cursorStyleHandler:lV,actionHandler:(t,r,s,n)=>lX({e:t,transformData:r,x:s,y:n,needResetScaleAndSnap:i,callback:e,fn:lM,snapPosition:x.ControlType.Right}),mouseDownHandler:lB,actionName:"resizeMRControl"})},lU=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{callback:e,needResetScaleAndSnap:i}=t;return new r6({x:-.5,y:.5,cursorStyleHandler:lV,actionHandler:(t,r,s,n)=>lX({e:t,transformData:r,x:s,y:n,needResetScaleAndSnap:i,callback:e,fn:lO,leftTopCalcFn:lI,snapPosition:x.ControlType.BottomLeft}),mouseDownHandler:lB,actionName:"resizeBLControl"})},lZ=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{callback:e,needResetScaleAndSnap:i}=t;return new r6({x:0,y:.5,cursorStyleHandler:lV,actionHandler:(t,r,s,n)=>lX({e:t,transformData:r,x:s,y:n,needResetScaleAndSnap:i,callback:e,fn:lE,snapPosition:x.ControlType.Bottom}),mouseDownHandler:lB,actionName:"resizeMBControl"})},lq=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{callback:e,needResetScaleAndSnap:i}=t;return new r6({x:.5,y:.5,cursorStyleHandler:lV,actionHandler:(t,r,s,n)=>lX({e:t,transformData:r,x:s,y:n,needResetScaleAndSnap:i,callback:e,fn:lO,snapPosition:x.ControlType.BottomRight}),mouseDownHandler:lB,actionName:"resizeBRControl"})},lK=t=>{var{x:e,y:i,offsetY:r,offsetX:s,actionName:n,rotateStaff:o}=t;return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{callback:a}=t;return new r6({x:e,y:i,sizeX:20,sizeY:20,offsetY:12*r,offsetX:12*s,render:()=>{},cursorStyleHandler:(t,e,i)=>"url(".concat(lT(i.angle+o),") 16 16, crosshair"),actionHandler:(t,e,i,r)=>{t.shiftKey?e.target.set({snapAngle:15}):e.target.set({snapAngle:void 0});var s=lj(t,(0,D._)((0,E._)({},e),{originX:"center",originY:"center"}),i,r);return null==a||a({element:e.target}),s},actionName:n})}},lJ=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return lK({x:-.5,y:-.5,offsetY:-1,offsetX:-1,rotateStaff:0,actionName:"rotateTLControl"})(t)},lQ=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return lK({x:.5,y:-.5,offsetY:-1,offsetX:1,rotateStaff:90,actionName:"rotateTRControl"})(t)},l$=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return lK({x:.5,y:.5,offsetY:1,offsetX:1,rotateStaff:180,actionName:"rotateBRControl"})(t)},l0=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return lK({x:-.5,y:.5,offsetY:1,offsetX:-1,rotateStaff:270,actionName:"rotateBLControl"})(t)},l1=t=>{var{element:e}=t,{x1:i,x2:r,y1:s,y2:n}=e;i<r&&s<n||i>r&&s>n?e.setControlsVisibility({ml:!1,mr:!1,mt:!1,mb:!1,bl:!1,br:!0,tl:!0,tr:!1,mtr:!1}):e.setControlsVisibility({ml:!1,mr:!1,mt:!1,mb:!1,bl:!0,br:!1,tl:!1,tr:!0,mtr:!1})},l2=t=>{var{element:e}=t,{width:i,height:r}=e;e.set({rx:i/2,ry:r/2})},l3=t=>{var{element:e,needResetScaleAndSnap:i=!0}=t;e.setControlsVisibility({mtr:!1}),e.controls.tl=lz({needResetScaleAndSnap:i}),e.controls.mt=lW({needResetScaleAndSnap:i}),e.controls.tr=lY({needResetScaleAndSnap:i}),e.controls.ml=lH({needResetScaleAndSnap:i}),e.controls.mr=lG({needResetScaleAndSnap:i}),e.controls.bl=lU({needResetScaleAndSnap:i}),e.controls.mb=lZ({needResetScaleAndSnap:i}),e.controls.br=lq({needResetScaleAndSnap:i}),e.controls.tlr=lJ(),e.controls.trr=lQ(),e.controls.blr=l0(),e.controls.brr=l$()},l6={[tC.STRAIGHT_LINE]:t=>{var{element:e}=t;l1({element:e}),e.controls.tl=lD({x:-.5,y:-.5,callback:l1}),e.controls.tr=lD({x:.5,y:-.5,callback:l1}),e.controls.bl=lA({x:-.5,y:.5,callback:l1}),e.controls.br=lA({x:.5,y:.5,callback:l1})},[tC.RECT]:l3,[tC.TRIANGLE]:l3,[tC.PENCIL]:t=>{l3((0,D._)((0,E._)({},t),{needResetScaleAndSnap:!1}))},[tC.CIRCLE]:t=>{var{element:e}=t;e.setControlsVisibility({mtr:!1});var i={callback:l2,needResetScaleAndSnap:!0};e.controls.tl=lz(i),e.controls.mt=lW(i),e.controls.tr=lY(i),e.controls.ml=lH(i),e.controls.mr=lG(i),e.controls.bl=lU(i),e.controls.mb=lZ(i),e.controls.br=lq(i),e.controls.tlr=lJ(i),e.controls.trr=lQ(i),e.controls.blr=l0(i),e.controls.brr=l$(i)},[tC.BLOCK_TEXT]:t=>{var{element:e}=t;e.setControlsVisibility({mtr:!1});var i={callback:()=>{aQ({element:e}),e.fire("moving")},needResetScaleAndSnap:!0};e.controls.tl=lz(i),e.controls.mt=lW(i),e.controls.tr=lY(i),e.controls.ml=lH(i),e.controls.mr=lG(i),e.controls.bl=lU(i),e.controls.mb=lZ(i),e.controls.br=lq(i),e.controls.tlr=lJ(i),e.controls.trr=lQ(i),e.controls.blr=l0(i),e.controls.brr=l$(i)},[tC.INLINE_TEXT]:t=>{var{element:e}=t;e.setControlsVisibility({mtr:!1}),e.controls.tlr=lJ(),e.controls.trr=lQ(),e.controls.blr=l0(),e.controls.brr=l$()},[tC.IMAGE]:t=>{var{element:e}=t;e.setControlsVisibility({mtr:!1});var i={callback:()=>{aU({element:e}),aQ({element:e}),e.fire("moving")},needResetScaleAndSnap:!0};e.controls.tl=lz(i),e.controls.mt=lW(i),e.controls.tr=lY(i),e.controls.ml=lH(i),e.controls.mr=lG(i),e.controls.bl=lU(i),e.controls.mb=lZ(i),e.controls.br=lq(i),e.controls.tlr=lJ(i),e.controls.trr=lQ(i),e.controls.blr=l0(i),e.controls.brr=l$(i)}},l5=oJ.prototype.calcTextHeight;oJ.prototype.calcTextHeight=function(){var t;return null!==(t=this.customFixedHeight)&&void 0!==t?t:l5.call(this)},oK.getDefaults=()=>({}),Object.assign(oK.prototype,oJ.ownDefaults),Object.assign(Text.prototype,oK.ownDefaults),Object.assign(sw.prototype,sw.ownDefaults);var l8=Z.o.t("imageflow_canvas_text_default"),l4=Z.o.t("imageflow_canvas_text_default"),l9=t=>({customId:(0,P.x0)(),customType:t});var l7=(f=(0,tF._)(function*(t){var e,{mode:i,position:r,element:s,elementProps:n={},canvas:o}=t,a=null!==(u=null!==(d=null==s?void 0:s.left)&&void 0!==d?d:null==r?void 0:r[0])&&void 0!==u?u:0,l=null!==(f=null!==(g=null==s?void 0:s.top)&&void 0!==g?g:null==r?void 0:r[1])&&void 0!==f?f:0,h=null!=i?i:null==s?void 0:s.customType,c=l9(h);switch(h){case tC.RECT:var d,u,g,f,p,v=s;return!v&&(v=new s3((0,D._)((0,E._)({},c,a3[h],n),{left:a,top:l,width:1,height:1}))),null===(p=l6[h])||void 0===p||p.call(l6,{element:v}),v;case tC.CIRCLE:var m,x=s;return!x&&(x=new ox((0,D._)((0,E._)({},c,a3[h],n),{left:a,top:l,rx:1,ry:1}))),null===(m=l6[h])||void 0===m||m.call(l6,{element:x}),x;case tC.TRIANGLE:var y,_=s;return!_&&(_=new ov((0,D._)((0,E._)({},c,a3[h],n),{left:a,top:l,width:1,height:1}))),null===(y=l6[h])||void 0===y||y.call(l6,{element:_}),_;case tC.STRAIGHT_LINE:var b,w=s;return!w&&(w=new op([a,l,a,l],(0,E._)({},c,a3[h],n))).on("start-end:modified",()=>{l1({element:w})}),null===(b=l6[h])||void 0===b||b.call(l6,{element:w}),w;case tC.INLINE_TEXT:var C,S,T=s;return!T&&(T=new oK(null!==(S=null==n?void 0:n.text)&&void 0!==S?S:l8,(0,D._)((0,E._)({},c,a3[h],n),{left:a,top:l}))),null===(C=l6[h])||void 0===C||C.call(l6,{element:T}),T;case tC.BLOCK_TEXT:var O=s,k=null!==(L=null!==(A=null==n?void 0:n.width)&&void 0!==A?A:null==O?void 0:O.width)&&void 0!==L?L:1,j=null!==(F=null!==(P=null==n?void 0:n.height)&&void 0!==P?P:null==O?void 0:O.height)&&void 0!==F?F:1;if(!O){O=new oJ(null!==(R=null==n?void 0:n.text)&&void 0!==R?R:l4,(0,E._)((0,D._)((0,E._)({},c,a3[h]),{customFixedHeight:j,left:a,top:l,width:k,height:j}),n));var M,A,L,P,F,R,I=new s3;O.set({clipPath:I})}return aQ({element:O}),null===(M=l6[h])||void 0===M||M.call(l6,{element:O}),O;case tC.IMAGE:var B=s;if(!B&&(null==n?void 0:n.src)){var N,V,X,z,W=yield o9.fromURL(null==n?void 0:n.src),Y=a3[h].width,H=a3[h].height,G=null!=a?a:null!==(V=null==n?void 0:n.left)&&void 0!==V?V:a3[h].left,U=null!=l?l:null!==(X=null==n?void 0:n.top)&&void 0!==X?X:a3[h].top,Z=(0,E._)({},a3[h],n),{stroke:q,strokeWidth:K}=Z,J=(0,tm._)(Z,["stroke","strokeWidth"]);W.set(J),B=new na([W]);var Q=(0,D._)((0,E._)({},c),{left:G,top:U,width:Y,height:H,customId:null!==(z=null==n?void 0:n.customId)&&void 0!==z?z:c.customId}),$=new s3({width:Q.width,height:Q.height,stroke:q,strokeWidth:K,fill:"#00000000"});B.add($),B.set(Q);var tt=new s3;B.set({clipPath:tt})}return aQ({element:B}),aU({element:B}),null===(N=l6[h])||void 0===N||N.call(l6,{element:B}),B;case tC.GROUP:var te=s;if(!te){var{objects:ti=[]}=n;te=new na(ti,(0,E._)({},c,a3[h],n))}return te;case tC.PENCIL:return s&&(null===(e=l6[h])||void 0===e||e.call(l6,{element:s})),s;default:return s}}),function(t){return f.apply(this,arguments)});var ht=(p=(0,tF._)(function*(t){var{element:e,options:{readonly:i},canvas:r}=t;e.selectable=!i,yield l7({element:e,canvas:r}),i&&e.set({hoverCursor:"default"})}),function(t){return p.apply(this,arguments)}),he=t=>{var e,i,r,{schema:s,activeObjectId:n,formMeta:o}=t,a=s.objects.find(t=>t.customId===n);(null==a?void 0:a.customType)===tC.IMAGE&&(a=(0,D._)((0,E._)({},a,null===(e=a.objects)||void 0===e?void 0:e[0]),{stroke:null===(i=a.objects)||void 0===i?void 0:i[1].stroke,strokeWidth:null===(r=a.objects)||void 0===r?void 0:r[1].strokeWidth}));var l={};return null==o||o.content.forEach(t=>{var e,i,r,s,n;t.name&&(l[t.name]=null!==(r=null==a?void 0:a[t.name])&&void 0!==r?r:null===(i=t.setterProps)||void 0===i?void 0:i.defaultValue),(null!==(s=null===(e=t.tooltip)||void 0===e?void 0:e.content.length)&&void 0!==s?s:0)>0&&(null===(n=t.tooltip)||void 0===n||n.content.forEach(t=>{if(t.name){var e,i;l[t.name]=null!==(i=null==a?void 0:a[t.name])&&void 0!==i?i:null===(e=t.setterProps)||void 0===e?void 0:e.defaultValue}}))}),l},hi=i("178621"),hr=i("474523"),hs=["1-中等-思源黑体.otf","1-常规体-思源黑体.otf","1-特细-思源黑体.otf","1-粗体-思源黑体.otf","1-细体-思源黑体.otf","1-黑体-思源黑体.otf","10-字语趣淘体.ttf","11-字语金农漆书体.ttf","12-字语文娱体.ttf","13-中等-思源宋体.otf","13-常规体-思源宋体.otf","13-次粗体-思源宋体.otf","13-特细-思源宋体.otf","13-粗体-思源宋体.otf","13-细体-思源宋体.otf","13-黑体-思源宋体.otf","14-字语文刻体.ttf","15-字语国文楷书.ttf","16-字语咏楷体.ttf","17-字语纤隶体.ttf","18-字语古兰体.ttf","19-字语古隶体.ttf","2-抖音美好体.ttf","20-常规体-字语文圆体.ttf","20-粗体-字语文圆体.ttf","20-细体-字语文圆体.ttf","21-字语趣味像素.ttf","22-字语文畅体.ttf","23-字语漫雅手书.ttf","24-字语香雪煮茶.ttf","25-字语逸风手书.ttf","26-字语家书体.ttf","27-字语青梅硬笔.ttf","28-字语明媚体.ttf","29-字语萌酱体.ttf","3-字语咏乐体.ttf","30-字语软糖体.ttf","31-中等-源云明体（繁体）.ttc","31-常规体-源云明体（繁体）.ttc","31-次粗体-源云明体（繁体）.ttc","31-特细-源云明体（繁体）.ttc","31-粗体-源云明体（繁体）.ttc","31-细体-源云明体（繁体）.ttc","31-黑体-源云明体（繁体）.ttc","32-Bold-WixMadefor.otf","32-ExtraBold-WixMadefor.otf","32-Medium-WixMadefor.otf","32-Regular-WixMadefor.otf","32-SemiBold-WixMadefor.otf","33-Black-Outfit.otf","33-ExtraBold-Outfit.otf","33-Extralight-Outfit.otf","33-Light-Outfit.otf","33-Medium-Outfit.otf","33-Regular-Outfit.otf","33-SemiBold-Outfit.otf","33-Thin-Outfit.otf","34-110Medium-LibreClarendonNormal.otf","34-162Bold-LibreClarendonNormal.otf","34-212Black-LibreClarendonNormal.otf","34-42Light-LibreClarendonNormal.otf","34-68Regular-LibreClarendonNormal.otf","35-BoldExt-Coconat.otf","35-Regular-Coconat.otf","36-Joan.otf","37-Bold-Messapia.otf","37-Regular-Messapia.otf","38-Squatina.otf","39-ZYLAAAgoodbook.ttf","4-字语咏宏体.ttf","40-ZYLAABravery.ttf","41-ZYLAADontforget.ttf","42-ZYLAAElegance.ttf","43-ZYLAAAnemone.ttf","44-StoryScript.otf","45-ZYLAAIridescent.ttf","46-ZYENADelicacy.ttf","47-Bolderslant.ttf","48-PinyonScript.otf","49-ZYLAADeepblue.ttf","5-站酷庆科黄油体.ttf","50-ZYLAASylph.ttf","51-ZYENAFetching.ttf","52-ZYLAACosy.ttf","53-ZYENAConfectionary.ttf","54-ZYENAGambol.ttf","55-RubikBubbles.ttf","56-Bold-KabinettFraktur.ttf","56-Regular-KabinettFraktur.ttf","57-RibesBlack.otf","58-Bold-DynaPuff.otf","58-Medium-DynaPuff.otf","58-Regular-DynaPuff.otf","58-SemiBold-DynaPuff.otf","59-ZYLAAAugenstern.ttf","6-字语寂黑体.ttf","60-MatrixSans.otf","61-MatrixSansPrint.otf","62-MatrixSansRaster.otf","63-MatrixSansScreen.otf","64-MatrixSansVideo.otf","7-字语墨黑体.ttf","8-字语酷黑体.ttf","9-字语趣逗体.ttf"],hn=t=>{var e=t.match(/^\d+-(.*?)\./);return e?e[1]:null},ho="".concat((0,hr.q9)(""),"/fonts"),ha=hs.map(hn),hl=t=>{if(ha.includes(t)){var e=hs.find(e=>hn(e)===t);return"".concat(ho,"/image-canvas-fonts/").concat(e)}},hh=["1-特细-思源黑体.svg","1-细体-思源黑体.svg","1-常规体-思源黑体.svg","1-中等-思源黑体.svg","1-粗体-思源黑体.svg","1-黑体-思源黑体.svg","1-思源黑体.svg","10-字语趣淘体.svg","11-字语金农漆书体.svg","12-字语文娱体.svg","13-特细-思源宋体.svg","13-细体-思源宋体.svg","13-常规体-思源宋体.svg","13-中等-思源宋体.svg","13-次粗体-思源宋体.svg","13-粗体-思源宋体.svg","13-黑体-思源宋体.svg","13-思源宋体.svg","14-字语文刻体.svg","15-字语国文楷书.svg","16-字语咏楷体.svg","17-字语纤隶体.svg","18-字语古兰体.svg","19-字语古隶体.svg","2-抖音美好体.svg","20-细体-字语文圆体.svg","20-常规体-字语文圆体.svg","20-粗体-字语文圆体.svg","20-字语文圆体.svg","21-字语趣味像素.svg","22-字语文畅体.svg","23-字语漫雅手书.svg","24-字语香雪煮茶.svg","25-字语逸风手书.svg","26-字语家书体.svg","27-字语青梅硬笔.svg","28-字语明媚体.svg","29-字语萌酱体.svg","3-字语咏乐体.svg","30-字语软糖体.svg","31-特细-源云明体（繁体）.svg","31-细体-源云明体（繁体）.svg","31-常规体-源云明体（繁体）.svg","31-中等-源云明体（繁体）.svg","31-次粗体-源云明体（繁体）.svg","31-粗体-源云明体（繁体）.svg","31-黑体-源云明体（繁体）.svg","31-源云明体（繁体）.svg","32-Regular-WixMadefor.svg","32-Medium-WixMadefor.svg","32-SemiBold-WixMadefor.svg","32-Bold-WixMadefor.svg","32-ExtraBold-WixMadefor.svg","32-WixMadefor.svg","33-Thin-Outfit.svg","33-Extralight-Outfit.svg","33-Light-Outfit.svg","33-Regular-Outfit.svg","33-Medium-Outfit.svg","33-SemiBold-Outfit.svg","33-Bold-Outfit.svg","33-Black-Outfit.svg","33-ExtraBold-Outfit.svg","33-Outfit.svg","34-42Light-LibreClarendonNormal.svg","34-68Regular-LibreClarendonNormal.svg","34-110Medium-LibreClarendonNormal.svg","34-162Bold-LibreClarendonNormal.svg","34-212Black-LibreClarendonNormal.svg","34-LibreClarendonNormal.svg","35-Regular-Coconat.svg","35-BoldExt-Coconat.svg","35-Coconat.svg","36-Joan.svg","37-Regular-Messapia.svg","37-Bold-Messapia.svg","37-Messapia.svg","38-Squatina.svg","39-ZYLAAAgoodbook.svg","4-字语咏宏体.svg","40-ZYLAABravery.svg","41-ZYLAADontforget.svg","42-ZYLAAElegance.svg","43-ZYLAAAnemone.svg","44-StoryScript.svg","45-ZYLAAIridescent.svg","46-ZYENADelicacy.svg","47-Bolderslant.svg","48-PinyonScript.svg","49-ZYLAADeepblue.svg","50-ZYLAASylph.svg","51-ZYENAFetching.svg","52-ZYLAACosy.svg","53-ZYENAConfectionary.svg","54-ZYENAGambol.svg","55-RubikBubbles.svg","56-Regular-KabinettFraktur.svg","56-Bold-KabinettFraktur.svg","56-KabinettFraktur.svg","57-RibesBlack.svg","58-Regular-DynaPuff.svg","58-Medium-DynaPuff.svg","58-SemiBold-DynaPuff.svg","58-Bold-DynaPuff.svg","58-DynaPuff.svg","59-ZYLAAAugenstern.svg","6-字语寂黑体.svg","60-MatrixSans.svg","61-MatrixSansPrint.svg","62-MatrixSansRaster.svg","63-MatrixSansScreen.svg","64-MatrixSansVideo.svg","7-字语墨黑体.svg","8-字语酷黑体.svg","9-字语趣逗体.svg"].map(t=>{var e=t.replace(".svg","").split("-"),i=e[1],r=e[2];return{value:e[1],label:(0,A.jsx)("img",{alt:i,className:"h-[12px]",src:"".concat(ho,"/image-canvas-fonts-preview-svg/").concat(t)}),order:Number(e[0]),name:i,groupName:r}}),hc=hh.filter(t=>!t.groupName);hc.forEach(t=>{var e=hh.filter(e=>e.groupName===t.name);t.children=e});var hd=hc.sort((t,e)=>t.order-e.order),hu=new hi.S({defaultOptions:{queries:{staleTime:1/0}}});var hg=(v=(0,tF._)(function*(t){yield hu.fetchQuery({queryKey:[t],queryFn:(0,tF._)(function*(){if(ha.includes(t)){var e=hl(t),i=new FontFace(t,"url(".concat(e,")"));document.fonts.add(i),yield i.load()}return t})})}),function(t){return v.apply(this,arguments)}),hf=t=>{var e,{schema:i,canvas:r,fontFamily:s}=t,n=s?[s]:[];i&&(n=Array.from(new Set(n=i.objects.filter(t=>[tC.INLINE_TEXT,tC.BLOCK_TEXT].includes(t.customType)).map(t=>t.fontFamily)))),n.forEach((e=(0,tF._)(function*(t){yield hg(t),null==r||r.getObjects().filter(e=>(null==e?void 0:e.fontFamily)===t).forEach(e=>{e.set({fontFamily:t})}),null==r||r.requestRenderAll()}),function(t){return e.apply(this,arguments)}))},hp=t=>{var{value:e,max:i,min:r}=t;return e>i?i:e<r?r:e},hv=i("681536"),hm={};hm.styleTagTransform=to(),hm.setAttributes=ti(),hm.insert=tt().bind(null,"head"),hm.domAPI=Q(),hm.insertStyleElement=ts(),K()(hv.Z,hm);var hx=hv.Z&&hv.Z.locals?hv.Z.locals:void 0,hy=t=>{var e,i,r,s,n,o,a,l,h=(0,L.useRef)(null),{left:c=0,top:d=0,children:u,position:g="bottom-center",zIndex:f=1e3,onClick:p,className:v,limitRect:m}=t,x=(0,B.Z)(h.current),y=(null!==(e=null==m?void 0:m.width)&&void 0!==e?e:1/0)-(null!==(i=null==x?void 0:x.width)&&void 0!==i?i:0)/2,_=(null!==(r=null==x?void 0:x.width)&&void 0!==r?r:0)/2,b="translate(-50%, 0)";"bottom-right"===g?(y=(null!==(s=null==m?void 0:m.width)&&void 0!==s?s:1/0)-(null!==(n=null==x?void 0:x.width)&&void 0!==n?n:0),_=0,b="translate(0, 0)"):"top-center"===g&&(b="translate(-50%, -100%)");var[w,C]=(0,L.useState)(""),S=(0,L.useRef)();return(0,L.useEffect)(()=>{clearTimeout(S.current),!x&&(S.current=setTimeout(()=>{C("".concat(Math.random()))},100))},[x]),(0,A.jsxs)("div",{ref:h,onClick:p,className:I()([hx["pop-in-screen"],"!fixed","coz-tooltip semi-tooltip-wrapper","p-0",v]),style:{left:hp({value:c,max:y,min:_}),top:hp({value:d,max:(null!==(o=null==m?void 0:m.height)&&void 0!==o?o:1/0)-(null!==(a=null==x?void 0:x.height)&&void 0!==a?a:0),min:"top-center"===g&&null!==(l=null==x?void 0:x.height)&&void 0!==l?l:0}),zIndex:f,opacity:1,maxWidth:"unset",transform:b},children:[(0,A.jsx)("div",{className:"hidden",id:w}),u]})},h_=t=>({display:"row",style:{padding:"8px"},content:[{name:"customId",setter:"RefSelect",setterProps:{label:Z.o.t("imageflow_canvas_reference",{},"引用"),labelInside:!0,className:"w-[160px]"}},{name:"fontFamily",splitLine:!1,setter:"TextFamily",setterProps:{treeData:hd,defaultValue:a3[t].fontFamily}},{name:"fontSize",setter:"FontSize",setterProps:{min:10,max:300,optionList:[12,16,20,24,32,40,48,56,72,92,120,160,220].map(t=>({value:t,label:"".concat(t)})),defaultValue:a3[t].fontSize}},{name:"lineHeight",splitLine:!1,setter:"LineHeight",setterProps:{optionList:[10,50,100,120,150,200,250,300,350,400].map(t=>({value:t,label:"".concat(t,"%")})),min:10,max:400,defaultValue:a3[t].lineHeight}},{setter:t=>{var{tooltipVisible:e}=t;return(0,A.jsx)(Y.u,{mouseEnterDelay:300,mouseLeaveDelay:300,content:Z.o.t("imageflow_canvas_style_tooltip"),children:(0,A.jsx)(tb,{inForm:!0,className:"!w-[48px]",color:e?"highlight":"secondary",icon:(0,A.jsxs)("div",{className:"flex flex-row items-center gap-[2px]",children:[(0,A.jsx)(W.A8x,{className:"text-[16px]"}),(0,A.jsx)(W.Wdl,{className:"text-[16px]"})]})})})},tooltip:{content:[{name:"colorMode",cacheSave:!0,setter:"SingleSelect",setterProps:{options:[{value:tS.FILL,label:Z.o.t("imageflow_canvas_fill")},{value:tS.STROKE,label:Z.o.t("imageflow_canvas_stroke")}],layout:"fill",defaultValue:tS.FILL}},{name:"strokeWidth",visible:t=>(null==t?void 0:t.colorMode)===tS.STROKE,setter:"BorderWidth",setterProps:{min:0,max:20,defaultValue:a3[t].strokeWidth},splitLine:!0},{name:"stroke",visible:t=>(null==t?void 0:t.colorMode)===tS.STROKE,setter:"ColorPicker",setterProps:{showOpacity:!1,defaultValue:a3[t].stroke}},{name:"fill",visible:t=>(null==t?void 0:t.colorMode)!==tS.STROKE,setter:"ColorPicker",setterProps:{defaultValue:a3[t].fill}}]}},{name:"textAlign",setter:"TextAlign",setterProps:{defaultValue:a3[t].textAlign}},{name:"customType",setter:"TextType",setterProps:{defaultValue:t}}]}),hb=t=>({display:"col",style:{padding:"16px"},content:[{name:"colorMode",cacheSave:!0,setter:"SingleSelect",setterProps:{options:[{value:tS.FILL,label:Z.o.t("imageflow_canvas_fill")},{value:tS.STROKE,label:Z.o.t("imageflow_canvas_stroke")}],layout:"fill",defaultValue:tS.FILL}},{title:Z.o.t("imageflow_canvas_fill"),name:"fill",visible:t=>(null==t?void 0:t.colorMode)===tS.FILL,setter:"ColorPicker",setterProps:{defaultValue:a3[t].fill}},{title:Z.o.t("imageflow_canvas_stroke"),name:"strokeWidth",visible:t=>(null==t?void 0:t.colorMode)===tS.STROKE,setter:"BorderWidth",setterProps:{min:0,max:50,defaultValue:a3[t].strokeWidth},splitLine:!0},{name:"stroke",visible:t=>(null==t?void 0:t.colorMode)===tS.STROKE,setter:"ColorPicker",setterProps:{showOpacity:!1,defaultValue:a3[t].stroke}}]}),hw=t=>({display:"col",style:{padding:"16px"},content:[{title:Z.o.t("imageflow_canvas_line_style"),name:"strokeWidth",setter:"BorderWidth",setterProps:{min:0,max:20,defaultValue:a3[t].strokeWidth},splitLine:!0},{name:"stroke",setter:"ColorPicker",setterProps:{showOpacity:!1,defaultValue:a3[t].stroke}}]}),hC={[tC.BLOCK_TEXT]:h_(tC.BLOCK_TEXT),[tC.INLINE_TEXT]:h_(tC.INLINE_TEXT),[tC.RECT]:hb(tC.RECT),[tC.CIRCLE]:hb(tC.CIRCLE),[tC.TRIANGLE]:hb(tC.TRIANGLE),[tC.STRAIGHT_LINE]:hw(tC.STRAIGHT_LINE),[tC.PENCIL]:hw(tC.PENCIL),[tC.IMAGE]:{display:"col",style:{padding:"16px"},content:[{name:"customId",setter:"RefSelect",setterProps:{label:Z.o.t("imageflow_canvas_reference",{},"引用"),labelInside:!1,className:"flex-1 overflow-hidden max-w-[320px]"},splitLine:!0},{name:"colorMode",cacheSave:!0,setter:"SingleSelect",setterProps:{options:[{value:tS.FILL,label:Z.o.t("imageflow_canvas_fill")},{value:tS.STROKE,label:Z.o.t("imageflow_canvas_stroke")}],layout:"fill",defaultValue:tS.FILL}},{name:"src",visible:t=>(null==t?void 0:t.colorMode)===tS.FILL,setter:"Uploader",setterProps:{getLabel:t=>t?Z.o.t("imageflow_canvas_fill_preview",{},"内容预览"):Z.o.t("imageflow_canvas_fill_image",{},"内容")}},{name:"customFixedType",visible:t=>(null==t?void 0:t.colorMode)===tS.FILL,setter:"LabelSelect",setterProps:{className:"flex-1",label:Z.o.t("imageflow_canvas_fill_mode"),optionList:[{value:tO.AUTO,label:Z.o.t("imageflow_canvas_fill1")},{value:tO.FILL,label:Z.o.t("imageflow_canvas_fill2")},{value:tO.FULL,label:Z.o.t("imageflow_canvas_fill3")}],defaultValue:tO.FILL}},{name:"opacity",visible:t=>(null==t?void 0:t.colorMode)===tS.FILL,setter:"ColorPicker",setterProps:{showColor:!1,defaultValue:a3[tC.IMAGE].opacity}},{title:Z.o.t("imageflow_canvas_stroke"),name:"strokeWidth",visible:t=>(null==t?void 0:t.colorMode)===tS.STROKE,setter:"BorderWidth",setterProps:{min:0,max:50,defaultValue:a3[tC.IMAGE].strokeWidth},splitLine:!0},{name:"stroke",visible:t=>(null==t?void 0:t.colorMode)===tS.STROKE,setter:"ColorPicker",setterProps:{showOpacity:!1,defaultValue:a3[tC.IMAGE].stroke}}]}},hS=t=>{var{metaItem:e,value:i,onChange:r,tooltipVisible:s,isRefElement:n}=t,{setter:o,setterProps:a,title:l}=e,h=o;if("function"==typeof o&&(h=o({value:i,onChange:r,tooltipVisible:s})),"string"==typeof o&&t7[o]){var c=t7[o];h=(0,A.jsx)(c,(0,E._)({value:i,onChange:r,isRefElement:n},a))}return[l?(0,A.jsx)("div",{className:"w-full text-[14px] font-medium",children:l}):void 0,(0,A.jsx)("div",{className:"flex items-center gap-[2px] text-[16px]",children:h})]},hT=(0,L.memo)(t=>{var e,{metaItem:i,isLast:r,isRow:s,formValue:n,onChange:o,isRefElement:a}=t,{name:l="",tooltip:h,splitLine:c,visible:d}=i,[u,g]=(0,L.useState)(!1);return null===(e=null==d?void 0:d(n))||void 0===e||e?(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("div",{className:"flex flex-col gap-[12px]",children:h?(0,A.jsx)(Y.u,{onVisibleChange:g,showArrow:!1,position:"bottom",trigger:"click",style:{maxWidth:"unset"},spacing:{y:12,x:0},content:(0,A.jsx)("div",{className:"flex flex-col gap-[12px]",children:h.content.filter(t=>{var e,i;return null===(i=null===(e=t.visible)||void 0===e?void 0:e.call(t,n))||void 0===i||i}).map(t=>{var e;return hS({metaItem:t,value:n[null!==(e=t.name)&&void 0!==e?e:""],isRefElement:a,onChange:e=>{var i;o({[null!==(i=t.name)&&void 0!==i?i:""]:e},t.cacheSave)}})})},"tooltip-".concat(l)),children:hS({metaItem:i,value:n[l],isRefElement:a,tooltipVisible:u,onChange:t=>{o({[l]:t},i.cacheSave)}})}):hS({metaItem:i,value:n[l],isRefElement:a,onChange:t=>{o({[l]:t},i.cacheSave)}})},"form-item-".concat(l)),(null!=c?!c:!s||r)?void 0:s?(0,A.jsx)("div",{className:"w-[1px] h-[24px] coz-mg-primary-pressed"},"split-".concat(l)):(0,A.jsx)("div",{className:"w-full h-[1px] coz-mg-primary-pressed"},"split-".concat(l))]}):(0,A.jsx)(A.Fragment,{})}),hO=t=>{var e,{position:i,offsetY:r=10,offsetX:s=0,schema:n,activeObjects:o,onChange:a,limitRect:l,canvasHeight:h}=t,{tl:c,br:d}=i,u=c.x+(d.x-c.x)/2,{y:g}=d,f="bottom-center";h&&c.y+(d.y-c.y)/2>h/2&&(g=c.y,f="top-center");var p=(0,L.useMemo)(()=>hC[o[0].customType],[o]),[v,m]=(0,L.useState)({}),x=(0,E._)({},he({schema:n,activeObjectId:o[0].customId,formMeta:p}),v),y="row"===p.display,_="col"===p.display,b=(0,N.Z)(v),w=(0,L.useCallback)((t,e)=>{!e&&a(t),m((0,E._)({},b.current,t))},[a]),C=(0,L.useMemo)(()=>p.content.map((t,e)=>{var i=e===p.content.length-1;return(0,A.jsx)(hT,{formValue:x,metaItem:t,isLast:i,isRow:y,onChange:w,isRefElement:o[0].customId.startsWith(tw)},t.name)}),[p,y,_,w,x]);return(0,A.jsx)(Y.iV,{getPopupContainer:()=>document.body,children:(0,A.jsx)(hy,{left:u+s,top:g+r+("top-center"===f?-10:10),position:f,limitRect:l,children:(0,A.jsx)("div",{tabIndex:0,style:(0,E._)({},null!==(e=p.style)&&void 0!==e?e:{}),onClick:t=>{t.stopPropagation()},children:(0,A.jsx)("div",{className:I()(["flex gap-[12px]",{"flex-col":_,"flex-row items-center":y}]),children:C})})})})},hk=t=>{var{left:e=0,top:i=0,offsetX:r=0,offsetY:s=0,cancelMenu:n,hasActiveObject:o,copy:a,paste:l,moveToFront:h,moveToBackend:c,moveToFrontOne:d,moveToBackendOne:u,isActiveObjectsInBack:g,isActiveObjectsInFront:f,disabledPaste:p,limitRect:v}=t,m=navigator.platform.toLowerCase().includes("mac")?"⌘":"ctrl",x=[{label:Z.o.t("imageflow_canvas_copy"),suffix:"".concat(m," + C"),onClick:()=>{null==a||a(tj.CtrlCV)}},{label:Z.o.t("imageflow_canvas_paste"),suffix:"".concat(m," + V"),onClick:()=>{null==l||l({mode:tj.CtrlCV})},disabled:p,alwaysShow:!0},{label:Z.o.t("Copy"),suffix:"".concat(m," + D"),onClick:(0,tF._)(function*(){yield null==a?void 0:a(tj.CtrlD),null==l||l({mode:tj.CtrlD})})},{key:"divider1",divider:!0},{label:Z.o.t("imageflow_canvas_top_1"),suffix:"]",onClick:()=>{null==d||d()},disabled:f},{label:Z.o.t("imageflow_canvas_down_1"),suffix:"[",onClick:()=>{null==u||u()},disabled:g},{label:Z.o.t("imageflow_canvas_to_front"),suffix:"".concat(m," + ]"),onClick:()=>{null==h||h()},disabled:f},{label:Z.o.t("imageflow_canvas_to_back"),suffix:"".concat(m," + ["),onClick:()=>{null==c||c()},disabled:g}].filter(t=>{var e;return null!==(e=t.alwaysShow)&&void 0!==e?e:o});return(0,A.jsx)(hy,{position:"bottom-right",left:e+r,top:i+s,zIndex:1001,onClick:t=>{t.stopPropagation(),null==n||n()},limitRect:v,children:(0,A.jsx)(Y.v2.SubMenu,{mode:"menu",children:x.map(t=>t.divider?(0,A.jsx)(Y.v2.Divider,{},t.key):(0,A.jsx)(Y.v2.Item,{itemKey:t.label,onClick:t.onClick,disabled:t.disabled,children:(0,A.jsxs)("div",{className:"w-[120px] flex justify-between",children:[(0,A.jsx)("div",{children:t.label}),(0,A.jsx)("div",{className:"coz-fg-secondary",children:t.suffix})]})}))})})},hj=t=>{var{canvas:e,schema:i,readonly:r}=t,[s,n]=(0,L.useState)(!1);return(0,L.useEffect)(()=>{n(!0),null==e||e.loadFromJSON(JSON.stringify(i),(t,i)=>{ht({element:i,options:{readonly:r},canvas:e})}).then(()=>{n(!1),null==e||e.requestRenderAll()})},[i,e]),{loading:s}},hM=i("989957"),hE=i("606947"),hD=t=>{var e,{startInit:i,ref:r,schema:s,readonly:n,resize:o,scale:a=1,onClick:l}=t,[h,c]=(0,L.useState)(void 0);(0,hM.Z)((0,tF._)(function*(){if(!!i&&!!r){var t=new n0(r,{width:s.width*a,height:s.height*a,backgroundColor:s.backgroundColor,selection:!n,preserveObjectStacking:!0});null==o||o(t),yield d(s,t),c(t),hf({schema:s,canvas:t}),!n&&(window._fabric_canvas=t)}}),[i]),(0,hE.Z)(()=>{null==h||h.dispose(),c(void 0)});var d=(0,L.useCallback)((e=(0,tF._)(function*(t,e){var i,r=null!=e?e:h;yield null==r?void 0:r.loadFromJSON(JSON.stringify(t),(i=(0,tF._)(function*(t,e){yield ht({element:e,options:{readonly:n},canvas:r})}),function(t,e){return i.apply(this,arguments)})),null==r||r.requestRenderAll()}),function(t,i){return e.apply(this,arguments)}),[h]);return(0,L.useEffect)(()=>{var t=[];return h&&t.push(h.on("mouse:down",t=>{null==l||l(t)})),()=>{t.forEach(t=>t())}},[h,l]),{canvas:h,loadFromJSON:d}},hA=t=>{var{maxWidth:e,maxHeight:i,width:r,height:s}=t,n=Math.min(e/r,i/s);return{resize:(0,L.useCallback)(t=>{if(!!e&&!!i&&!!t)null==t||t.setDimensions({width:r,height:s}),null==t||t.setDimensions({width:"".concat(r*n,"px"),height:"".concat(s*n,"px")},{cssOnly:!0})},[e,i,r,s,n]),scale:n}},hL=t=>{var{ref:e,schema:i,maxWidth:r,maxHeight:s,startInit:n}=t,{resize:o,scale:a}=hA({maxWidth:r,maxHeight:s,width:i.width,height:i.height}),{canvas:l}=hD({ref:e.current,schema:i,startInit:n,readonly:!0,resize:o,scale:a});return(0,L.useEffect)(()=>{l&&o(l)},[o,l]),hj({canvas:l,schema:i,readonly:!0}),{state:{cssScale:a}}},hP=t=>{var{canvas:e,schema:i,minZoom:r,maxZoom:s}=t,[n,o]=(0,L.useState)([1,0,0,1,0,0]),a=(0,L.useCallback)(t=>{if(!!e){var r=[...t];r[4]>0&&(r[4]=0),r[4]<-i.width*(r[0]-1)&&(r[4]=-i.width*(r[0]-1)),r[5]>0&&(r[5]=0),r[5]<-i.height*(r[0]-1)&&(r[5]=-i.height*(r[0]-1)),aq({canvas:e,vpt:r}),o(r),e.fire("object:moving")}},[e,i,r,s]),l=(0,L.useCallback)((t,i)=>{a(aZ({canvas:e,point:t,zoomLevel:i,minZoom:r,maxZoom:s}))},[a]);return{setViewport:a,viewport:n,zoomToPoint:l}},hF=t=>{var{canvas:e,helpLineLayerId:i,scale:r}=t;(0,L.useEffect)(()=>{if(!!e){var t=lx(e,i,r);return e.on("mouse:down",t=>{M.resetAllObjectsPosition(t.target)}),e.on("mouse:up",e=>{t.reset()}),null==e||e.on("object:moving",function(e){e.target&&t.move(e.target)}),()=>{t.destroy()}}},[e]),(0,L.useEffect)(()=>{M&&M.helpline.resetScale(r)},[r])},hR=i("335740"),hI=i("346707"),hB=i("998600"),hN=i("136515"),hV=i("537845"),hX={};function hz(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};function i(){var i;return null!==(i=null==hX?void 0:hX[t])&&void 0!==i?i:null==e?void 0:e.defaultValue}var[r,s]=(0,L.useState)(i);return(0,hN.Z)(()=>{s(i())},[t]),[r,(0,hV.Z)(e=>{s(e),hX[t]=e})]}var hW=t=>{var{schema:e,loadFromJSON:i,stopListen:r,startListen:s,onChange:n,id:o}=t,[a,l]=hz("".concat(o,"-history"),{defaultValue:[(0,hR.Z)(e)]}),h=(0,N.Z)(a),[c,d]=hz("".concat(o,"-step"),{defaultValue:0}),u=(0,N.Z)(c),[g,f]=(0,L.useState)(!1),p=(0,L.useCallback)(t=>{if(!((0,hI.Z)(h.current)||(0,hI.Z)(u.current))&&JSON.stringify(t)!==JSON.stringify(h.current[u.current])){var e=u.current+1,i=Math.max(0,e-20),r=[...h.current.splice(i,e),t];l(r),d(r.length-1)}},[]),v=(0,L.useCallback)((0,tF._)(function*(){if(!((0,hI.Z)(h.current)||(0,hI.Z)(u.current))&&0!==u.current){var t=u.current-1,e=h.current[t];f(!0),r(),null==n||n(e),yield null==i?void 0:i(e),d(t),s(),f(!1)}}),[i]),m=(0,L.useCallback)((0,tF._)(function*(){if(!((0,hI.Z)(h.current)||(0,hI.Z)(u.current))&&u.current!==h.current.length-1){var t=u.current+1,e=h.current[t];f(!0),r(),null==n||n(e),yield null==i?void 0:i(e),d(t),s(),f(!1)}}),[i]),{run:x}=(0,hB.Z)(t=>{p((0,hR.Z)(t))},{wait:300});return{pushOperation:x,undo:(0,tF._)(function*(){!g&&(yield v())}),redo:(0,tF._)(function*(){!g&&(yield m())}),disabledUndo:0===c,disabledRedo:a&&c===a.length-1,redoUndoing:g}},hY="".concat((0,hr.q9)(""),"/workflow/fabric-canvas/img-placeholder.png"),hH=["width","height","editable","text","backgroundColor","padding","customFixedHeight","customId","customType","customFixedType","customVariableRefs"],hG=t=>{var e,i,{variables:r,canvas:s,onChange:n,schema:o,listenerEvents:a=["object:modified","object:added","object:removed","object:moving","object:modified-zIndex"]}=t,l=(0,L.useRef)([]),[h,c]=(0,L.useState)(!0),d=(0,N.Z)(n),u=(0,N.Z)(o),g=(0,L.useRef)(null!==(i=null==o?void 0:o.customVariableRefs)&&void 0!==i?i:[]),f=(0,L.useCallback)(t=>{var{schema:e}=t,i=g.current,r=e.objects.map(t=>t.customId);return i=null==i?void 0:i.filter(t=>r.includes(t.objectId)),g.current=i,i},[]);(0,L.useEffect)(()=>{if(s&&d.current&&h){var t=t=>{var e,{isRemove:i}=t,r=s.toObject(hH);i?r.customVariableRefs=f({schema:r}):r.customVariableRefs=(0,hR.Z)(g.current),null===(e=d.current)||void 0===e||e.call(d,r)};l.current.forEach(t=>t()),l.current=[],a.forEach(e=>{var i=s.on(e,function(i){t({isRemove:"object:removed"===e})});l.current.push(i)})}return()=>{l.current.forEach(t=>null==t?void 0:t()),l.current=[]}},[s,h]);var p=(0,L.useCallback)((e=(0,tF._)(function*(t,e){if(!!s){var i,{customVariableRefs:r=[],width:n=0,height:o=0}=null!==(i=u.current)&&void 0!==i?i:{},{id:a,name:l,type:h}=t,c=[n/2+16*r.length,o/2+16*r.length],d=e;!d&&(h===U.ow.Image?d=yield l7({mode:tC.IMAGE,position:[c[0]-a3[tC.IMAGE].width/2,c[1]-a3[tC.IMAGE].height/2],elementProps:{width:a3[tC.IMAGE].width,height:a3[tC.IMAGE].width,editable:!1,src:hY}}):h===U.ow.String&&(d=yield l7({mode:tC.BLOCK_TEXT,position:[c[0]-a3[tC.BLOCK_TEXT].width/2,c[1]-a3[tC.BLOCK_TEXT].height/2],elementProps:{text:Z.o.t("imageflow_canvas_change_text",{},"点击编辑文本预览"),width:a3[tC.BLOCK_TEXT].width,height:a3[tC.BLOCK_TEXT].height}}))),d&&(g.current.push({variableId:a,objectId:d.customId,variableName:l}),s.add(d),s.setActiveObject(d))}}),function(t,i){return e.apply(this,arguments)}),[s]),v=(0,L.useCallback)(t=>{var e,{objectId:i,variable:r}=t,s=g.current,n=s.find(t=>t.objectId===i),o=[];o=r?n?s.map(t=>t.objectId===i?(0,D._)((0,E._)({},t),{variableId:r.id,variableName:r.name}):t):[...s,{variableId:r.id,objectId:i,variableName:r.name}]:s.filter(t=>t.objectId!==i),g.current=o,null===(e=d.current)||void 0===e||e.call(d,(0,D._)((0,E._)({},u.current),{customVariableRefs:o}))},[d,u]);(0,L.useEffect)(()=>{var{customVariableRefs:t=[]}=null!==(e=u.current)&&void 0!==e?e:{};if(t.some(t=>{var e,i=null==r?void 0:r.find(e=>e.id===t.variableId);return t.variableName!==(null!==(e=null==i?void 0:i.name)&&void 0!==e?e:tk)})){var e,i,s=t.map(t=>{var e,i=null==r?void 0:r.find(e=>e.id===t.variableId);return(0,D._)((0,E._)({},t),{variableName:null!==(e=null==i?void 0:i.name)&&void 0!==e?e:tk})});g.current=s,null===(i=d.current)||void 0===i||i.call(d,(0,D._)((0,E._)({},u.current),{customVariableRefs:s}))}},[r]);var m=(0,L.useCallback)(()=>{c(!1)},[]),x=(0,L.useCallback)(()=>{var t,e;c(!0),g.current=null!==(e=null===(t=u.current)||void 0===t?void 0:t.customVariableRefs)&&void 0!==e?e:[]},[]);return{customVariableRefs:g.current,addRefObjectByVariable:p,updateRefByObjectId:v,stopListen:m,startListen:x}},hU=t=>{var{element:e,scale:i}=t,r=e.isType("group"),s=e.customType===tC.INLINE_TEXT,n=r?e.getObjects()[0]:e,o=n.calcOCoords().tl,{width:a,scaleX:l=1,padding:h=0}=n,c=o.x*i,d=o.y*i;if(r){var u,g,f,p=null!==(f=null===(g=e.getObjects())||void 0===g?void 0:null===(u=g[1])||void 0===u?void 0:u.strokeWidth)&&void 0!==f?f:0;d-=p/2,c-=p/2;var v=e.calcOCoords().tl;c=Math.max(v.x*i,c),d=Math.max(v.y*i,d)}return{left:c,top:d,angle:e.angle,id:e.customId,maxWidth:s?999:(a*l+2*h)*i,isImg:r}},hZ=t=>{var{canvas:e,scale:i,viewport:r}=t,[s,n]=(0,L.useState)([]),o=(0,L.useCallback)(()=>{setTimeout(()=>{if(!!e){var t=e.getObjects();n(null==t?void 0:t.map(t=>hU({element:t,scale:i})))}},0)},[e,i,r]);return(0,L.useEffect)(()=>{o()},[o]),hG({canvas:e,onChange:o}),{allObjectsPositionInScreen:s}},hq=t=>{var{canvas:e}=t,[i,r]=(0,L.useState)({left:0,top:0});return(0,L.useEffect)(()=>{if(!!e)return e.on("mouse:move",t=>{var i=e.getScenePoint(t.e);r({left:i.x,top:i.y})})},[e]),{mousePosition:i}},hK=t=>{var{canvas:e,onShapeAdded:i}=t,r=(0,L.useRef)([]);return{enterAddInlineText:()=>{if(!!e){var t,s=e.on("mouse:down",(t=(0,tF._)(function*(t){var{e:r}=t,s=e.getScenePoint(r);r.preventDefault(),e.selection=!1;var n=yield l7({mode:tC.INLINE_TEXT,position:[s.x,s.y],canvas:e});n&&(e.add(n),e.setActiveObject(n),null==i||i({element:n}))}),function(e){return t.apply(this,arguments)}));r.current.push(s)}},exitAddInlineText:()=>{if(!!e)e.selection=!0,r.current.length>0&&(r.current.forEach(t=>t()),r.current=[])}}},hJ=t=>{var e,{canvas:i,onShapeAdded:r}=t;return{addImage:(e=(0,tF._)(function*(t){var e=yield l7({mode:tC.IMAGE,position:[(null==i?void 0:i.width)/2-a3[tC.IMAGE].width/2,(null==i?void 0:i.height)/2-a3[tC.IMAGE].height/2],canvas:i,elementProps:{src:t}});e&&(null==i||i.add(e),null==i||i.setActiveObject(e),null==r||r({element:e}))}),function(t){return e.apply(this,arguments)})}},hQ=t=>{var{canvas:e}=t,i=(0,L.useCallback)((0,tF._)(function*(){var t,i=null==e?void 0:e.getActiveObject(),r=null==i?void 0:i.getObjects();if((null!==(t=null==r?void 0:r.length)&&void 0!==t?t:0)>1){var s=yield l7({mode:tC.GROUP,elementProps:{left:null==i?void 0:i.left,top:null==i?void 0:i.top,width:null==i?void 0:i.width,height:null==i?void 0:i.height}});s.add(...r),null==e||e.add(s),null==e||e.setActiveObject(s),null==e||e.remove(...r)}}),[e]);return{group:i,unGroup:(0,L.useCallback)((0,tF._)(function*(){var t=null==e?void 0:e.getActiveObject();if(a$(t)){var i,r=t.getObjects();yield Promise.all(r.map((i=(0,tF._)(function*(i){var r=yield l7({mode:i.customType,element:i});t.remove(i),null==e||e.add(r)}),function(t){return i.apply(this,arguments)}))),null==e||e.discardActiveObject(),null==e||e.remove(t);var s=new o2(r);null==e||e.setActiveObject(s),null==e||e.requestRenderAll()}}),[e])}},h$=t=>{var{canvas:e}=t;return(0,L.useEffect)(()=>{if(!!e){var t=e.on("path:created",function(t){var i,{path:r}=t,s=l9(tC.PENCIL);r.set((0,E._)({},s,a3[tC.PENCIL])),null===(i=l6[tC.PENCIL])||void 0===i||i.call(l6,{element:r}),e.fire("object:modified")});return()=>{t()}}},[e]),{enterFreePencil:()=>{if(!!e)e.isDrawingMode=!0,e.freeDrawingBrush=new ol(e),e.freeDrawingBrush.color=a3[tC.PENCIL].stroke,e.freeDrawingBrush.width=a3[tC.PENCIL].strokeWidth},exitFreePencil:()=>{if(!!e)e.isDrawingMode=!1}}},h0={[tC.RECT]:{down:t=>{var{left:e,top:i,canvas:r}=t;return l7({mode:tC.RECT,position:[e,i],canvas:r})},move:t=>{var{element:e,dx:i,dy:r}=t;e.set({width:i,height:r}),M.resize(e,x.ControlType.BottomRight)},up:t=>{var{element:e}=t;e.set({width:a3[tC.RECT].width,height:a3[tC.RECT].height})}},[tC.CIRCLE]:{down:t=>{var{left:e,top:i,canvas:r}=t;return l7({mode:tC.CIRCLE,position:[e,i],canvas:r})},move:t=>{var{element:e,dx:i,dy:r}=t;e.set({rx:Math.max(i/2,0),ry:Math.max(r/2,0)}),M.resize(e,x.ControlType.BottomRight)},up:t=>{var{element:e}=t;e.set({rx:a3[tC.CIRCLE].rx,ry:a3[tC.CIRCLE].ry})}},[tC.TRIANGLE]:{down:t=>{var{left:e,top:i,canvas:r}=t;return l7({mode:tC.TRIANGLE,position:[e,i],canvas:r})},move:t=>{var{element:e,dx:i,dy:r}=t;e.set({width:i,height:r}),M.resize(e,x.ControlType.BottomRight)},up:t=>{var{element:e}=t;e.set({width:a3[tC.TRIANGLE].width,height:a3[tC.TRIANGLE].height})}},[tC.STRAIGHT_LINE]:{down:t=>{var{left:e,top:i,canvas:r}=t;return l7({mode:tC.STRAIGHT_LINE,position:[e,i],canvas:r})},move:t=>{var{element:e,dx:i,dy:r}=t;e.set({x2:i+e.x1,y2:r+e.y1}),e.fire("start-end:modified")}},[tC.BLOCK_TEXT]:{down:t=>{var{left:e,top:i,canvas:r}=t;return l7({mode:tC.BLOCK_TEXT,position:[e,i],canvas:r})},move:t=>{var{element:e,dx:i,dy:r}=t;e.set({customFixedHeight:r,width:i,height:r}),M.resize(e,x.ControlType.BottomRight),aQ({element:e})},up:t=>{var{element:e}=t;e.set({width:a3[tC.BLOCK_TEXT].width,height:a3[tC.BLOCK_TEXT].height,customFixedHeight:a3[tC.BLOCK_TEXT].height}),aQ({element:e})}}},h1=t=>{var{canvas:e,onShapeAdded:i}=t,r=(0,L.useRef)(),s=(0,L.useRef)([]);return{enterDragAddElement:t=>{if(!!e){var n,o=e.on("mouse:down",(n=(0,tF._)(function*(i){var s,{e:n}=i;e.selection=!1;var o=e.getScenePoint(n);n.preventDefault();var a=yield null===(s=h0[t])||void 0===s?void 0:s.down({left:o.x,top:o.y,canvas:e});a&&(e.add(a),e.setActiveObject(a),r.current={element:a,x:o.x,y:o.y,moved:!1},a.set("hasControls",!1))}),function(t){return n.apply(this,arguments)})),a=e.on("mouse:move",function(i){var{e:s}=i;if(s.preventDefault(),r.current){var n,{element:o,x:a,y:l}=r.current,h=e.getScenePoint(s),c=h.x-a,d=h.y-l;null===(n=h0[t])||void 0===n||n.move({element:o,dx:c,dy:d}),o.setCoords(),r.current.moved=!0,e.fire("object:modified"),e.requestRenderAll()}}),l=e.on("mouse:up",function(s){var{e:n}=s;if(n.preventDefault(),r.current){var o,a,{element:l}=r.current;!r.current.moved&&(null===(a=h0[t])||void 0===a||null===(o=a.up)||void 0===o||o.call(a,{element:l})),null==i||i({element:l}),l.set("hasControls",!0),r.current=void 0,e.requestRenderAll()}});s.current.push(o,a,l)}},exitDragAddElement:()=>{e&&(e.selection=!0),s.current.length>0&&(s.current.forEach(t=>t()),s.current=[])}}},h2=t=>{var e,i,{canvas:r,mousePosition:s,couldAddNewObject:n,customVariableRefs:o,variables:a,addRefObjectByVariable:l}=t,h=(0,L.useRef)(),c=(0,L.useRef)(),d=(0,L.useRef)(),u=(0,N.Z)(o),g=(0,N.Z)(a),[f,p]=(0,L.useState)({left:0,top:0}),v=(0,N.Z)(f),m=(0,N.Z)(n),[x,y]=(0,L.useState)({left:0,top:0}),_=(0,N.Z)(x);(0,L.useEffect)(()=>{var t,e,i,r;p({left:s.left-(null!==(i=null===(t=h.current)||void 0===t?void 0:t.width)&&void 0!==i?i:0)/2,top:s.top-(null!==(r=null===(e=h.current)||void 0===e?void 0:e.height)&&void 0!==r?r:0)/2})},[s]);var b=(e=(0,tF._)(function*(t){var e,i,s=t.customId,n=(0,P.x0)();t.set({customId:n});var o=yield l7({element:t,canvas:r}),a=null===(e=u.current)||void 0===e?void 0:e.find(t=>t.objectId===s),h=null===(i=g.current)||void 0===i?void 0:i.find(t=>t.id===(null==a?void 0:a.variableId));h?l(h,o):null==r||r.add(o)}),function(t){return e.apply(this,arguments)}),w=(0,L.useCallback)((0,tF._)(function*(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:tj.CtrlCV;if(!r)return;var e=r.getActiveObject();if(!!e)switch(y({left:e.left+16,top:e.top+16}),p({left:e.left+16,top:e.top+16}),t){case tj.CtrlCV:h.current=yield e.clone(hH);break;case tj.CtrlD:c.current=yield e.clone(hH);break;case tj.DragCV:d.current=yield e.clone(hH)}}),[r]);var C=(0,L.useCallback)((i=(0,tF._)(function*(t){if(!m.current){Y.FN.warning({content:"元素数量已达上限，无法添加新元素",duration:3});return}var e,i,s,n,o=null!==(i=null==t?void 0:t.mode)&&void 0!==i?i:tj.CtrlCV;switch(o){case tj.CtrlCV:s=h.current;break;case tj.CtrlD:s=c.current;break;case tj.DragCV:s=d.current}if(!!r&&!!s){var a=yield s.clone(hH),l=o!==tj.CtrlCV,{left:u,top:g}=l?_.current:v.current;l?y({left:u+16,top:g+16}):p({left:u+16,top:g+16}),a.set({left:hp({value:u,min:0,max:r.width-a.getBoundingRect().width}),top:hp({value:g,min:0,max:r.height-a.getBoundingRect().height})});var f=[],x={left:a.left+a.width/2,top:a.top+a.height/2};return a.isType("activeselection")?a.getObjects().forEach(t=>{t.set({left:t.left+x.left,top:t.top+x.top}),f.push(t)}):f.push(a),yield Promise.all(f.map((e=(0,tF._)(function*(t){return b(t)}),function(t){return e.apply(this,arguments)}))),a.isType("activeselection")&&(n=new o2(f.map(t=>(t.set({left:t.left-x.left,top:t.top-x.top}),t)))),r.discardActiveObject(),r.setActiveObject(null!=n?n:a),r.requestRenderAll(),null!=n?n:a}}),function(t){return i.apply(this,arguments)}),[r]);return(0,L.useEffect)(()=>{var t,e,i=!1,s=["Alt"],n=t=>{s.includes(t.key)&&(t.preventDefault(),i=!0)},o=t=>{s.includes(t.key)&&(t.preventDefault(),i=!1)},a=()=>{i=!1},l=t=>{t.preventDefault()};document.addEventListener("keydown",n),document.addEventListener("keyup",o),document.addEventListener("contextmenu",l),window.addEventListener("blur",a);var h=!1,c={left:0,top:0};var u=[null==r?void 0:r.on("mouse:down",(t=(0,tF._)(function*(t){if(i){if(!m.current){Y.FN.warning({content:"元素数量已达上限，无法添加新元素",duration:3});return}h=!0;var s,n,o=r.getActiveObject();null==o||o.set({lockMovementX:!0,lockMovementY:!0});try{yield w(tj.DragCV),e=yield C({mode:tj.DragCV}),c={left:null!==(s=null==e?void 0:e.left)&&void 0!==s?s:0,top:null!==(n=null==e?void 0:e.top)&&void 0!==n?n:0}}finally{null==o||o.set({lockMovementX:!1,lockMovementY:!1})}}}),function(e){return t.apply(this,arguments)})),null==r?void 0:r.on("mouse:move",t=>{if(i&&h&&e){var s,n,o,a,l=r.getScenePoint(t.e);if(t.e.shiftKey){;Math.abs(l.x-c.left)>Math.abs(l.y-c.top)?null==e||e.set({left:l.x-(null!==(s=null==e?void 0:e.width)&&void 0!==s?s:0)/2,top:c.top}):null==e||e.set({left:c.left,top:l.y-(null!==(n=null==e?void 0:e.height)&&void 0!==n?n:0)/2})}else null==e||e.set({left:l.x-(null!==(o=null==e?void 0:e.width)&&void 0!==o?o:0)/2,top:l.y-(null!==(a=null==e?void 0:e.height)&&void 0!==a?a:0)/2});M.move(e),r.requestRenderAll(),r.fire("object:moving")}}),null==r?void 0:r.on("mouse:up",()=>{h=!1,e=void 0,d.current=void 0})];return()=>{document.removeEventListener("keydown",n),document.removeEventListener("keyup",o),document.removeEventListener("contextmenu",l),window.removeEventListener("blur",a),u.forEach(t=>null==t?void 0:t())}},[r,w,C]),{copy:w,paste:C,disabledPaste:!h.current}},h3=t=>{var{canvas:e}=t,i=()=>{null==e||e.discardActiveObject(),null==e||e.requestRenderAll()};return{moveActiveObject:function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=null==e?void 0:e.getActiveObject();switch(t){case"left":null==r||r.set({left:r.left-i});break;case"right":null==r||r.set({left:r.left+i});break;case"up":null==r||r.set({top:r.top-i});break;case"down":null==r||r.set({top:r.top+i})}var s=null==r?void 0:r.isType("activeselection"),n=s?e:r;null==n||n.fire(s?"object:moving":"moving",{target:r}),null==e||e.fire("object:modified"),null==e||e.requestRenderAll()},removeActiveObjects:()=>{var t,r=null!==(t=null==e?void 0:e.getActiveObjects())&&void 0!==t?t:[];e&&r.length>0&&(r.forEach(t=>{e.remove(t)}),i())},discardActiveObject:i,moveTo:t=>{var i,r=null!==(i=null==e?void 0:e.getActiveObjects())&&void 0!==i?i:[];e&&r.length>0&&("front"===t?r.forEach(t=>{e.bringObjectToFront(t)}):"backend"===t?r.forEach(t=>{e.sendObjectToBack(t)}):"front-one"===t?r.forEach(t=>{e.bringObjectForward(t)}):"backend-one"===t&&r.forEach(t=>{e.sendObjectBackwards(t)}),e.fire("object:modified-zIndex"),e.requestRenderAll())},resetWidthHeight:t=>{var{width:i,height:r}=t;i&&(null==e||e.setWidth(i)),r&&(null==e||e.setHeight(r)),null==e||e.fire("object:modified"),null==e||e.requestRenderAll()}}},h6=i("523830"),h5=t=>{var{canvas:e,schema:i}=t,[r,s]=(0,L.useState)();return(0,L.useEffect)(()=>{if(!!e)s(e.backgroundColor)},[e]),(0,h6.Z)(()=>{s(i.backgroundColor)},[i.backgroundColor],{wait:300}),(0,L.useEffect)(()=>{r&&e&&e.backgroundColor!==r&&(e.set({backgroundColor:r}),e.fire("object:modified"),e.requestRenderAll())},[r,e]),{backgroundColor:r,setBackgroundColor:s}},h8=t=>{var{canvas:e,selectObjects:i=[]}=t,r=(0,L.useCallback)(()=>{if(!e||i.length<2)return;var t=e.getActiveObject();if(!!t)i.forEach(e=>{e.set({left:-t.width/2}),e.setCoords()}),t.setCoords(),e.fire("object:moving"),e.requestRenderAll()},[e,i]),s=(0,L.useCallback)(()=>{if(!e||i.length<2)return;var t=e.getActiveObject();if(!!t)i.forEach(e=>{e.set({left:t.width/2-e.getBoundingRect().width})}),e.fire("object:moving"),e.requestRenderAll()},[e,i]),n=(0,L.useCallback)(()=>{if(!!e&&!(i.length<2)&&!!e.getActiveObject())i.forEach(t=>{t.set({left:-t.getBoundingRect().width/2})}),e.fire("object:moving"),e.requestRenderAll()},[e,i]),o=(0,L.useCallback)(()=>{if(!e||i.length<2)return;var t=e.getActiveObject();if(!!t)i.forEach(e=>{e.set({top:-t.height/2})}),e.fire("object:moving"),e.requestRenderAll()},[e,i]),a=(0,L.useCallback)(()=>{if(!!e&&!(i.length<2)&&!!e.getActiveObject())i.forEach(t=>{t.set({top:-t.getBoundingRect().height/2})}),e.fire("object:moving"),e.requestRenderAll()},[e,i]),l=(0,L.useCallback)(()=>{if(!e||i.length<2)return;var t=e.getActiveObject();if(!!t)i.forEach(e=>{e.set({top:t.height/2-e.getBoundingRect().height})}),e.fire("object:moving"),e.requestRenderAll()},[e,i]),h=(0,L.useCallback)(()=>{if(!e||i.length<2)return;var t=e.getActiveObject();if(!!t){var r=i.reduce((t,e)=>t+e.getBoundingRect().width,0),s=(t.width-r)/(i.length-1),n=-t.width/2;i.sort((t,e)=>t.getBoundingRect().left-e.getBoundingRect().left).forEach(t=>{t.set({left:n}),n+=t.getBoundingRect().width+s}),e.fire("object:moving"),e.requestRenderAll()}},[e,i]);return{alignLeft:r,alignRight:s,alignCenter:n,alignTop:o,alignMiddle:a,alignBottom:l,horizontalAverage:(0,L.useCallback)(()=>{if(!e||i.length<2)return;var t=e.getActiveObject();if(!!t){var r=i.reduce((t,e)=>t+e.getBoundingRect().height,0),s=(t.height-r)/(i.length-1),n=-t.height/2;i.sort((t,e)=>t.getBoundingRect().top-e.getBoundingRect().top).forEach(t=>{t.set({top:n}),n+=t.getBoundingRect().height+s}),e.fire("object:moving"),e.requestRenderAll()}},[e,i]),verticalAverage:h}};var h4=(m=(0,tF._)(function*(t){var e,i,{element:r,props:s,canvas:n}=t;if((null==r?void 0:r.isType("group"))&&(null==r?void 0:null===(i=r.getObjects())||void 0===i?void 0:null===(e=i[0])||void 0===e?void 0:e.isType("image"))){var{stroke:o,strokeWidth:a,src:l}=s,h=(0,tm._)(s,["stroke","strokeWidth","src"]),c=r.getObjects()[0],d=r.getObjects()[1];if(o&&d.set({stroke:o}),"number"==typeof a&&d.set({strokeWidth:a}),l){var u=document.createElement("img");yield new Promise((t,e)=>{u.onload=()=>{c.setElement(u),t(0)},u.src=l})}Object.keys(h).length>0&&c.set(h),aU({element:r})}else{var{customType:g}=s,f=(0,tm._)(s,["customType"]);if(g&&[tC.BLOCK_TEXT,tC.INLINE_TEXT].includes(g)){var p=r.left;p<0?p=0:p>(null==n?void 0:n.width)&&(p=null==n?void 0:n.width);var v=r.top;v<0?v=0:v>(null==n?void 0:n.height)&&(v=null==n?void 0:n.height);var m=Math.round(r.fontSize*r.scaleY),x={};["customId","text","fontSize","fontFamily","fill","stroke","strokeWidth","textAlign","lineHeight","editable"].forEach(t=>{r[t]&&(x[t]=r[t])});var y=yield l7({mode:g,canvas:n,position:[p,v],elementProps:(0,E._)({},x,s.customType===tC.INLINE_TEXT?{}:{fontSize:m,padding:m/4,width:200,height:200})});Object.keys(f).length>0&&(null==y||y.set(f)),null==n||n.add(y),null==n||n.remove(r),null==n||n.discardActiveObject(),null==n||n.setActiveObject(y),null==n||n.requestRenderAll()}else{var{fontFamily:_}=s;_&&(yield hg(_)),(null==r?void 0:r.isType("textbox"))&&(null==r||r.set({styles:{}})),(null==r?void 0:r.isType("textbox"))&&"number"==typeof s.fontSize&&(null==r||r.set({padding:s.fontSize/4}),aQ({element:r})),null==r||r.set(s)}}}),function(t){return m.apply(this,arguments)}),h9=t=>{var e,{canvas:i,scale:r}=t,[s,n]=(0,L.useState)(),[o,a]=(0,L.useState)({tl:{x:-9999,y:-9999},br:{x:-9999,y:-9999}}),[l,h]=(0,L.useState)(!1),[c,d]=(0,L.useState)(!1),u=(0,L.useCallback)(()=>{var t=null==i?void 0:i.getObjects();h((null==s?void 0:s.length)===1&&(null==t?void 0:t[t.length-1])===(null==s?void 0:s[0])),d((null==s?void 0:s.length)===1&&(null==t?void 0:t[0])===(null==s?void 0:s[0]))},[i,s]);hG({canvas:i,onChange:u,listenerEvents:["object:modified-zIndex"]}),(0,L.useEffect)(()=>{u()},[s,u]);var g=()=>{i&&a(aJ({canvas:i,scale:r}))};(0,L.useEffect)(()=>{var t=["selection:created","selection:updated"].map(t=>null==i?void 0:i.on(t,t=>{n(null==i?void 0:i.getActiveObjects()),g();var e=null==i?void 0:i.getActiveObject();e&&(e.set(a0),e.isType("activeselection")&&e.setControlsVisibility({tl:!1,tr:!1,bl:!1,br:!1,ml:!1,mt:!1,mr:!1,mb:!1,mtr:!1}),null==i||i.requestRenderAll())})),e=null==i?void 0:i.on("selection:cleared",t=>{n(void 0),g()});return t.push(e),()=>{t.forEach(t=>null==t?void 0:t())}},[i]),(0,L.useEffect)(()=>{g()},[r]),hG({canvas:i,onChange:g,listenerEvents:["object:modified","object:added","object:removed","object:moving"]});var f=(e=(0,tF._)(function*(t,e){var r=s;if(e){var n=null==i?void 0:i.getObjects().find(t=>t.customId===e);n&&(r=[n])}yield Promise.all((null!=r?r:[]).map(e=>h4({element:e,props:t,canvas:i}))),null==i||i.requestRenderAll(),null==i||i.fire("object:modified")}),function(t,i){return e.apply(this,arguments)});(0,L.useEffect)(()=>{if(!!i){var t={left:0,top:0},e=[i.on("object:moving",function(e){var i,r=e.target;if(!!r)0===t.left&&0===t.top&&(t={left:r.left,top:r.top}),(null==e?void 0:null===(i=e.e)||void 0===i?void 0:i.shiftKey)&&(Math.abs(r.left-t.left)>Math.abs(r.top-t.top)?r.set("top",t.top):r.set("left",t.left))}),i.on("object:modified",function(e){t={left:0,top:0}})];return()=>{e.forEach(t=>null==t?void 0:t())}}},[i]);var p=(0,L.useRef)();return(0,L.useEffect)(()=>{var t=[];if((null==s?void 0:s.length)===1){var e=s[0];t.push(e.on("moving",()=>{!p.current&&(p.current=Object.assign({ml:!0,mr:!0,mt:!0,mb:!0,bl:!0,br:!0,tl:!0,tr:!0},e._controlsVisibility)),e.setControlsVisibility({ml:!1,mr:!1,mt:!1,mb:!1,bl:!1,br:!1,tl:!1,tr:!1})})),t.push(e.on("mouseup",()=>{p.current&&(e.setControlsVisibility(p.current),p.current=void 0)}))}return()=>{t.forEach(t=>t())}},[s]),{activeObjects:s,activeObjectsPopPosition:o,setActiveObjectsProps:f,isActiveObjectsInBack:c,isActiveObjectsInFront:l}},h7=t=>{var{ref:e,schema:i,onChange:r,maxWidth:s,maxHeight:n,startInit:o,maxZoom:a=3,minZoom:l=.3,readonly:h=!1,onShapeAdded:c,variables:d,id:u,helpLineLayerId:g,onClick:f}=t,p=(0,L.useMemo)(()=>{if(!(null==i?void 0:i.customVariableRefs)&&(null!==(r=null==i?void 0:null===(e=i.objects)||void 0===e?void 0:null===(t=e.filter(t=>t.customVariableName))||void 0===t?void 0:t.length)&&void 0!==r?r:0)>0){var t,e,r,s,n,o=null==i?void 0:null===(s=i.objects)||void 0===s?void 0:s.filter(t=>t.customVariableName),a=null!==(n=null==o?void 0:o.map(t=>({variableId:t.customId.replace("".concat(tw,"-img-"),"").replace("".concat(tw,"-text-"),""),objectId:t.customId,variableName:t.customVariableName})))&&void 0!==n?n:[];return(0,D._)((0,E._)({},i),{customVariableRefs:a})}return i},[i]),v=(0,L.useMemo)(()=>p.objects.length,[p]),m=(0,L.useMemo)(()=>v<50,[v]),{resize:x,scale:y}=hA({maxWidth:s,maxHeight:n,width:p.width,height:p.height}),{canvas:_,loadFromJSON:b}=hD({startInit:o,ref:e.current,schema:p,resize:x,scale:y,readonly:h,onClick:f}),{viewport:w,setViewport:C,zoomToPoint:S}=hP({canvas:_,minZoom:l,maxZoom:a,schema:p}),{mousePosition:T}=hq({canvas:_}),{group:O,unGroup:k}=hQ({canvas:_}),{startListen:j,stopListen:M,addRefObjectByVariable:A,updateRefByObjectId:P,customVariableRefs:F}=hG({variables:d,canvas:_,schema:p,onChange:t=>{null==r||r(t),N(t)}});hF({canvas:_,helpLineLayerId:g,scale:y});var{copy:R,paste:I,disabledPaste:B}=h2({canvas:_,mousePosition:T,couldAddNewObject:m,customVariableRefs:F,addRefObjectByVariable:A,variables:d}),{pushOperation:N,undo:V,redo:X,disabledRedo:z,disabledUndo:W,redoUndoing:Y}=hW({id:u,schema:p,loadFromJSON:b,startListen:j,stopListen:M,onChange:r}),{activeObjects:H,activeObjectsPopPosition:G,setActiveObjectsProps:U,isActiveObjectsInBack:Z,isActiveObjectsInFront:q}=h9({canvas:_,scale:y}),{alignLeft:K,alignRight:J,alignCenter:Q,alignTop:$,alignBottom:tt,alignMiddle:te,verticalAverage:ti,horizontalAverage:tr}=h8({canvas:_,selectObjects:H});(0,L.useEffect)(()=>{_&&x(_)},[x,_]);var{backgroundColor:ts,setBackgroundColor:tn}=h5({canvas:_,schema:p}),{moveActiveObject:to,removeActiveObjects:ta,moveTo:tl,discardActiveObject:th,resetWidthHeight:tc}=h3({canvas:_}),{addImage:td}=hJ({canvas:_,onShapeAdded:c}),{enterDragAddElement:tu,exitDragAddElement:tg}=h1({canvas:_,onShapeAdded:c}),{enterFreePencil:tf,exitFreePencil:tp}=h$({canvas:_}),{enterAddInlineText:tv,exitAddInlineText:tm}=hK({canvas:_,onShapeAdded:c}),{allObjectsPositionInScreen:tx}=hZ({canvas:_,scale:y,viewport:w});return{canvas:_,canvasSettings:{width:p.width,height:p.height,backgroundColor:ts},state:{viewport:w,cssScale:y,activeObjects:H,activeObjectsPopPosition:G,objectLength:v,couldAddNewObject:m,disabledUndo:W,disabledRedo:z,redoUndoing:Y,disabledPaste:B,isActiveObjectsInBack:Z,isActiveObjectsInFront:q,canvasWidth:null==_?void 0:_.getElement().getBoundingClientRect().width,canvasHeight:null==_?void 0:_.getElement().getBoundingClientRect().height,customVariableRefs:F,allObjectsPositionInScreen:tx},sdk:{discardActiveObject:th,setActiveObjectsProps:U,setBackgroundColor:tn,moveToFront:()=>{tl("front")},moveToBackend:()=>{tl("backend")},moveToFrontOne:()=>{tl("front-one")},moveToBackendOne:()=>{tl("backend-one")},zoomToPoint:S,setViewport:C,moveActiveObject:to,removeActiveObjects:ta,enterDragAddElement:tu,exitDragAddElement:tg,enterFreePencil:tf,exitFreePencil:tp,enterAddInlineText:tv,exitAddInlineText:tm,addImage:td,undo:V,redo:X,copy:R,paste:I,group:O,unGroup:k,alignLeft:K,alignRight:J,alignCenter:Q,alignTop:$,alignBottom:tt,alignMiddle:te,verticalAverage:ti,horizontalAverage:tr,resetWidthHeight:tc,addRefObjectByVariable:A,updateRefByObjectId:P}}},ct=t=>{var e,{ref:i,state:{isTextEditing:r,disabledPaste:s},sdk:{moveActiveObject:n,removeActiveObjects:o,undo:a,redo:l,copy:h,paste:c,group:d,unGroup:u,moveToFront:g,moveToBackend:f,moveToFrontOne:p,moveToBackendOne:v,alignLeft:m,alignRight:x,alignCenter:y,alignTop:_,alignBottom:b,alignMiddle:w,verticalAverage:C,horizontalAverage:S}}=t;(0,H.Z)(["uparrow","downarrow","leftarrow","rightarrow"],t=>{switch(t.key){case"ArrowUp":n("up");break;case"ArrowDown":n("down");break;case"ArrowLeft":n("left");break;case"ArrowRight":n("right")}},{target:i}),(0,H.Z)(["backspace","delete"],t=>{!r&&o()},{target:i}),(0,H.Z)(["ctrl.z","meta.z"],t=>{t.preventDefault(),t.shiftKey?l():a()},{events:["keydown"],target:i}),(0,H.Z)(["ctrl.c","meta.c"],t=>{t.preventDefault(),h(tj.CtrlCV)},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["ctrl.v","meta.v"],t=>{t.preventDefault(),!s&&c({mode:tj.CtrlCV})},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["ctrl.d","meta.d"],(e=(0,tF._)(function*(t){t.preventDefault(),yield h(tj.CtrlD),c({mode:tj.CtrlD})}),function(t){return e.apply(this,arguments)}),{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["openbracket"],t=>{!r&&v()},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["closebracket"],t=>{!r&&p()},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["meta.openbracket","meta.closebracket"],t=>{!r&&t.preventDefault()},{events:["keydown","keyup"],exactMatch:!0,target:i}),(0,H.Z)(["meta.openbracket"],t=>{!r&&f()},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["meta.closebracket"],t=>{!r&&g()},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["alt.a"],t=>{t.preventDefault(),m()},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["alt.d"],t=>{t.preventDefault(),x()},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["alt.h"],t=>{t.preventDefault(),y()},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["alt.w"],t=>{t.preventDefault(),_()},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["alt.s"],t=>{t.preventDefault(),b()},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["alt.v"],t=>{t.preventDefault(),w()},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["alt.ctrl.h"],t=>{t.preventDefault(),S()},{events:["keydown"],exactMatch:!0,target:i}),(0,H.Z)(["alt.ctrl.v"],t=>{t.preventDefault(),C()},{events:["keydown"],exactMatch:!0,target:i})},ce=([X.f,X.w].forEach(X.r),function(t,e){let{handlers:i,nativeHandlers:r,config:s}=(0,z.V)(t,e||{});return function(t,e={},i,r){let s=L.useMemo(()=>new z.Q(t),[]);if(s.applyHandlers(t,r),s.applyConfig(e,i),L.useEffect(s.effect.bind(s)),L.useEffect(()=>s.clean.bind(s),[]),void 0===e.target)return s.bind.bind(s)}(i,s,void 0,r)}),ci=t=>{var e,i,r,s,{onClose:n,icon:o,title:a,schema:l,onChange:h,readonly:c,variables:d,className:u}=t,g=(0,L.useMemo)(()=>null==d?void 0:d.filter(t=>t.type),[d]),[f,p]=(0,L.useState)(l),v=(0,L.useCallback)(t=>{p(t),h(t)},[h]),[m]=(0,L.useState)(null!==(i=t.id)&&void 0!==i?i:(0,P.x0)()),x="help-line-".concat(m),y=(0,L.useRef)(null),_=(0,L.useRef)(null),b=(0,L.useRef)(null),w=(0,L.useRef)(null),C=(0,B.Z)(w),S=(0,L.useRef)(null),T=(0,B.Z)(S),O=(0,L.useRef)(null),[k,j]=(0,L.useState)(),M=(0,N.Z)(k),[R,X]=(0,L.useState)(),[z,H]=(0,L.useState)(!1),G=(0,L.useCallback)(()=>{X(void 0)},[]),{state:{activeObjects:U,activeObjectsPopPosition:Z,viewport:q,couldAddNewObject:K,disabledUndo:J,disabledRedo:Q,redoUndoing:$,disabledPaste:tt,isActiveObjectsInBack:te,isActiveObjectsInFront:ti,canvasWidth:tr,canvasHeight:ts,customVariableRefs:tn,allObjectsPositionInScreen:to},sdk:{setActiveObjectsProps:ta,moveToFront:tl,moveToBackend:th,moveToFrontOne:tc,moveToBackendOne:td,addImage:tu,removeActiveObjects:tg,moveActiveObject:tf,enterFreePencil:tp,exitFreePencil:tv,enterDragAddElement:tm,exitDragAddElement:tx,enterAddInlineText:ty,exitAddInlineText:t_,zoomToPoint:tw,setViewport:tS,setBackgroundColor:tT,discardActiveObject:tO,redo:tk,undo:tj,copy:tE,paste:tD,group:tP,unGroup:tF,alignLeft:tR,alignRight:tI,alignCenter:tB,alignTop:tN,alignBottom:tV,alignMiddle:tX,verticalAverage:tz,horizontalAverage:tW,resetWidthHeight:tY,addRefObjectByVariable:tH,updateRefByObjectId:tG},canvasSettings:{backgroundColor:tU},canvas:tK}=h7({id:m,helpLineLayerId:x,onChange:v,ref:O,variables:g,schema:f,maxWidth:(null==C?void 0:C.width)||0,maxHeight:(null==C?void 0:C.height)?C.height-2:0,startInit:!!(null==C?void 0:C.width),maxZoom:3,minZoom:1,readonly:c,onShapeAdded:()=>{if(M.current){var t;null===(t=tJ[M.current])||void 0===t||t.exitFn(),j(void 0)}},onClick:G}),tJ={[tC.INLINE_TEXT]:{enterFn:()=>{ty()},exitFn:()=>{t_()}},[tC.BLOCK_TEXT]:{enterFn:()=>{tm(tC.BLOCK_TEXT)},exitFn:()=>{tx()}},[tC.RECT]:{enterFn:()=>{tm(tC.RECT)},exitFn:()=>{tx()}},[tC.CIRCLE]:{enterFn:()=>{tm(tC.CIRCLE)},exitFn:()=>{tx()}},[tC.STRAIGHT_LINE]:{enterFn:()=>{tm(tC.STRAIGHT_LINE)},exitFn:()=>{tx()}},[tC.TRIANGLE]:{enterFn:()=>{tm(tC.TRIANGLE)},exitFn:()=>{tx()}},[tC.PENCIL]:{enterFn:()=>{tp()},exitFn:()=>{tv()}}};(0,L.useEffect)(()=>{if(k&&!K){var t;null===(t=tJ[k])||void 0===t||t.exitFn(),j(void 0)}},[K,k]);var tQ=(0,L.useRef)(),t$=(t,e)=>{if(!!tK){var i=q[0],r=t.deltaY;if(e){var s=tK.getViewportPoint(t);tQ.current=s}r<0?i+=.05:i-=.05,tw(tQ.current,Number(i.toFixed(2)))}},t0=(t,e)=>{var i=[...q];i[4]-=t,i[5]-=e,tS(i)},t1=ce({onPinch:t=>{var e=t.event;e.preventDefault(),t$(e,t.first),t.first?H(!0):t.last&&H(!1)},onWheel:t=>{var e=t.event;e.preventDefault(),!t.pinching&&(t.metaKey?t$(e,t.first):t0(e.deltaX,e.deltaY)),t.first?H(!0):t.last&&H(!1)}},{eventOptions:{passive:!1}}),[t2,t3]=(0,L.useState)(!1);(0,L.useEffect)(()=>{var t=[];return(null==U?void 0:U.length)===1&&[tC.BLOCK_TEXT,tC.INLINE_TEXT].includes(U[0].customType)&&(t.push(U[0].on("editing:entered",()=>{t3(!0)})),t.push(U[0].on("editing:exited",()=>{t3(!1)}))),()=>{t.forEach(t=>t()),t=[]}},[U]),(0,L.useEffect)(()=>{var t=t=>{t.preventDefault();var e,i,r,s=null===(e=w.current)||void 0===e?void 0:e.getBoundingClientRect();X({left:t.clientX-(null!==(i=null==s?void 0:s.left)&&void 0!==i?i:0),top:t.clientY-(null!==(r=null==s?void 0:s.top)&&void 0!==r?r:0)})};return w.current&&w.current.addEventListener("contextmenu",t),()=>{w.current&&w.current.removeEventListener("contextmenu",t)}},[w]),(0,L.useEffect)(()=>{var t=t=>{X(void 0),tO()};return document.addEventListener("click",t),()=>{document.removeEventListener("click",t)}},[tO]),ct({ref:y,state:{isTextEditing:t2,disabledPaste:tt},sdk:{moveActiveObject:tf,removeActiveObjects:tg,undo:tj,redo:tk,copy:tE,paste:tD,group:tP,unGroup:tF,moveToFront:tl,moveToBackend:th,moveToFrontOne:tc,moveToBackendOne:td,alignLeft:tR,alignRight:tI,alignCenter:tB,alignTop:tN,alignBottom:tV,alignMiddle:tX,verticalAverage:tz,horizontalAverage:tW}});var t6=!c&&R,t5=1===Array.from(new Set(null==U?void 0:U.map(t=>t.customType))).length,{canvasMaxWidth:t8,canvasMaxHeight:t4}=(0,L.useMemo)(()=>({canvasMaxWidth:Math.min(8294400/f.height,1e4),canvasMaxHeight:Math.min(8294400/f.width,1e4)}),[f.width,f.height]),[t9,t7]=(0,L.useState)(!1),et=(0,V.Z)(t9,{wait:300});return(0,L.useEffect)(()=>{setTimeout(()=>{var t;null===(t=y.current)||void 0===t||t.focus()},300)},[]),(0,A.jsx)("div",{tabIndex:0,className:"flex flex-col w-full h-full relative ".concat(u," min-w-[900px]"),ref:y,onFocus:()=>{t7(!0)},onBlur:()=>{t7(!1)},children:(0,A.jsxs)(tL.Provider,{value:{variables:g,customVariableRefs:tn,allObjectsPositionInScreen:to,activeObjects:U,addRefObjectByVariable:tH,updateRefByObjectId:tG},children:[(0,A.jsx)("div",{ref:_}),(0,A.jsx)("div",{className:tA["top-bar-pop-align-right"],ref:b}),(0,A.jsx)(Y.iV,{getPopupContainer:()=>{var t;return null!==(t=_.current)&&void 0!==t?t:document.body},children:(0,A.jsxs)(A.Fragment,{children:[(0,A.jsxs)("div",{className:I()(["flex gap-[8px] items-center","w-full h-[55px]","px-[16px]"]),children:[(0,A.jsx)("img",{className:"w-[20px] h-[20px] rounded-[2px]",src:o}),(0,A.jsx)("div",{className:"text-xxl font-semibold",children:a}),(0,A.jsx)("div",{className:"flex-1",children:(0,A.jsx)(tZ,{redo:tk,undo:tj,disabledRedo:Q,disabledUndo:J,redoUndoing:$,popRefAlignRight:b,readonly:c||!tK,maxLimit:!K,mode:k,onModeChange:(t,e)=>{var i,r;j(t),e&&(null===(i=tJ[e])||void 0===i||i.exitFn()),t&&(null===(r=tJ[t])||void 0===r||r.enterFn())},isActiveObjectsInBack:te,isActiveObjectsInFront:ti,onMoveToTop:t=>{t.stopPropagation(),tl()},onMoveToBackend:t=>{t.stopPropagation(),th()},onAddImg:t=>{tu(t)},zoomSettings:{reset:()=>{tS([1,0,0,1,0,0])},zoom:q[0],onChange(t){if(!isNaN(t)){var e=[...q],i=Number(t.toFixed(2));i>3?i=3:i<1&&(i=1),e[0]=i,e[3]=i,tS(e)}},max:3,min:1},aligns:{[tM.Left]:tR,[tM.Right]:tI,[tM.Center]:tB,[tM.Top]:tN,[tM.Bottom]:tV,[tM.Middle]:tX,[tM.VerticalAverage]:tz,[tM.HorizontalAverage]:tW},canvasSettings:{width:f.width,minWidth:1,maxWidth:t8,height:f.height,minHeight:1,maxHeight:t4,background:tU,onChange(t){if(t.background){tT(t.background);return}var e=(0,F.Z)(t,["width","height"]);e.width&&(e.width>t8&&(e.width=t8),e.width<1&&(e.width=1)),e.height&&(e.height>t4&&(e.height=t4),e.height<1&&(e.height=1)),tY((0,E._)({},e))}}})}),(0,A.jsx)(tb,{onClick:n,icon:(0,A.jsx)(W.rmE,{className:"text-[16px]"})})]}),(0,A.jsxs)("div",{className:I()(["flex-1 flex items-center justify-center","p-[16px]","overflow-hidden","coz-bg-primary","border-0 border-t coz-stroke-primary border-solid","scale-100"]),ref:S,children:[(0,A.jsx)("div",{onMouseDown:t=>{0===t.button&&H(!0)},onMouseUp:t=>{0===t.button&&H(!1)},ref:w,tabIndex:0,className:I()(["flex items-center justify-center","w-full h-full overflow-hidden"]),children:(0,A.jsxs)("div",(0,D._)((0,E._)({},t1()),{className:"border border-solid ".concat(et?"coz-stroke-hglt":"coz-stroke-primary"," rounded-small overflow-hidden"),onClick:t=>{t.stopPropagation()},children:[(0,A.jsx)(tq,{visible:!z}),(0,A.jsxs)("div",{className:"w-fit h-fit overflow-hidden",children:[(0,A.jsx)("div",{id:x,className:"relative top-0 left-0 bg-red-500 z-[2] pointer-events-none"}),(0,A.jsx)("canvas",{ref:O,className:"h-[0px]"})]})]}))}),t6?(0,A.jsx)(hk,{limitRect:T,left:R.left,top:R.top,cancelMenu:()=>{X(void 0)},hasActiveObject:!!(null==U?void 0:U.length),copy:tE,paste:tD,disabledPaste:tt,moveToFront:tl,moveToBackend:th,moveToFrontOne:tc,moveToBackendOne:td,isActiveObjectsInBack:te,isActiveObjectsInFront:ti,offsetX:8,offsetY:8}):(0,A.jsx)(A.Fragment,{}),t6||z||!t5?(0,A.jsx)(A.Fragment,{}):(0,A.jsx)(hO,{schema:f,activeObjects:U,position:Z,onChange:t=>{ta(t)},offsetX:((null!==(r=null==T?void 0:T.width)&&void 0!==r?r:0)-(null!=tr?tr:0))/2,offsetY:((null!==(s=null==T?void 0:T.height)&&void 0!==s?s:0)-(null!=ts?ts:0))/2,canvasHeight:ts,limitRect:T},null==U?void 0:null===(e=U[0])||void 0===e?void 0:e.customType)]})]})})]})})},cr=t=>{var{schema:e,showPlaceholder:i}=t,r=(0,L.useRef)(null),s=(0,L.useRef)(null),n=(0,B.Z)(s),o=(0,L.useRef)(0);(0,L.useEffect)(()=>{(null==n?void 0:n.width)&&!o.current&&(o.current=(null==n?void 0:n.width)||0),(null==n?void 0:n.width)&&n.width-o.current>20&&(o.current=(null==n?void 0:n.width)||0)},[null==n?void 0:n.width]);var a=o.current;hL({schema:e,ref:r,maxWidth:a,maxHeight:456,startInit:!!(null==n?void 0:n.width)});var l=0===e.objects.length;return(0,A.jsxs)("div",{className:"w-full relative",children:[(0,A.jsx)("div",{ref:s,className:"w-full"}),(0,A.jsx)("canvas",{ref:r,className:"h-[0px]"}),l&&i?(0,A.jsx)("div",{className:"w-full h-full absolute top-0 left-0 flex items-center justify-center",children:(0,A.jsx)("div",{className:"text-[14px] coz-fg-secondary",children:Z.o.t("imageflow_canvas_double_click",{},"双击开始编辑")})}):(0,A.jsx)(A.Fragment,{})]})}},913983:function(t,e,i){i.r(e),i.d(e,{Canvas:function(){return g}});var r=i(151064),s=i(455069),n=i(252237),o=i.n(n),a=i(117354),l=i(873183),h=i(659596),c=i(340291),d=i(184449),u=i(864431),g=t=>{var e,{value:i,onChange:n,readonly:g,context:f,variables:p}=t,v=null==f?void 0:null===(e=f.form)||void 0===e?void 0:e.getFormItemValueByPath("/nodeMeta"),{icon:m,title:x}=null!=v?v:{},y=f.node.id,_="sheet-of-canvas-node-".concat(y),{visible:b}=(0,u.mx)(_),{setFullscreenPanel:w,fullscreenPanel:C}=(0,c.Tk)(),{isNodeSideSheet:S}=(0,d.$v)(),T=!!C,O=(0,s.useMemo)(()=>S&&(0,r.jsx)(a.HL,{id:_,variables:p,onChange:n,onClose:()=>w(null),icon:m,title:x,schema:i,readonly:g,className:"min-w-[800px]"}),[S,_,p,n,w,m,x,g]),k=(0,s.useCallback)(()=>{w(O)},[w]);(0,s.useEffect)(()=>{O&&T&&w(O)},[O]);var{start:j,stop:M}=(0,d.TA)();return(0,s.useEffect)(()=>{b||T?M():j()},[b,T]),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{style:{display:"flex",position:"absolute",top:10,right:12},children:(0,r.jsx)(h.hU,{size:"small",color:"secondary",icon:g?null:(0,r.jsx)(l.v3u,{className:"coz-fg-hglt text-sm"}),onClick:()=>k()})}),(0,r.jsx)("div",{className:o()(["flex flex-col items-center justify-center","overflow-hidden","rounded-mini mt-[8px]","border border-solid coz-stroke-plus hover:coz-stroke-hglt active:coz-stroke-hglt"]),onDoubleClick:()=>{k()},children:(0,r.jsx)(a.Ue,{schema:i,showPlaceholder:!T})})]})}},220193:function(t,e,i){var r=i(125161),s=i.n(r),n=i(404442),o=i.n(n)()(s());o.push([t.id,".icon-button-sW7tEa.coz-fg-secondary-y0OZNL{color:rgba(var(--coze-fg-3),var(--coze-fg-3-alpha))!important}.icon-button-sW7tEa.coz-fg-secondary-y0OZNL:disabled .semi-button-content{color:rgba(var(--coze-fg-1),var(--coze-fg-1-alpha))}",""]),o.locals={"icon-button":"icon-button-sW7tEa",iconButton:"icon-button-sW7tEa","coz-fg-secondary":"coz-fg-secondary-y0OZNL",cozFgSecondary:"coz-fg-secondary-y0OZNL"},e.Z=o},681536:function(t,e,i){var r=i(125161),s=i.n(r),n=i(404442),o=i.n(n)()(s());o.push([t.id,".pop-in-screen-UQiACl :focus{outline:none}",""]),o.locals={"pop-in-screen":"pop-in-screen-UQiACl",popInScreen:"pop-in-screen-UQiACl"},e.Z=o},416013:function(t,e,i){var r=i(125161),s=i.n(r),n=i(404442),o=i.n(n)()(s());o.push([t.id,".imageflow-canvas-border-width-iykQOX .semi-slider-handle{width:8px;height:8px;margin-top:12px}",""]),o.locals={"imageflow-canvas-border-width":"imageflow-canvas-border-width-iykQOX",imageflowCanvasBorderWidth:"imageflow-canvas-border-width-iykQOX"},e.Z=o},410391:function(t,e,i){var r=i(125161),s=i.n(r),n=i(404442),o=i.n(n)()(s());o.push([t.id,".color-picker-slider-l5kbqr .semi-slider{padding:0}.color-picker-slider-l5kbqr .semi-slider-handle{width:8px;height:8px;margin-top:12px}",""]),o.locals={"color-picker-slider":"color-picker-slider-l5kbqr",colorPickerSlider:"color-picker-slider-l5kbqr"},e.Z=o},691045:function(t,e,i){var r=i(125161),s=i.n(r),n=i(404442),o=i.n(n)()(s());o.push([t.id,".ref-select-NL3ZUH .semi-select-selection{margin-left:4px!important}",""]),o.locals={"ref-select":"ref-select-NL3ZUH",refSelect:"ref-select-NL3ZUH"},e.Z=o},228594:function(t,e,i){var r=i(125161),s=i.n(r),n=i(404442),o=i.n(n)()(s());o.push([t.id,".imageflow-canvas-font-family-cascader-vkjVlM .semi-cascader-option-lists{height:300px}",""]),o.locals={"imageflow-canvas-font-family-cascader":"imageflow-canvas-font-family-cascader-vkjVlM",imageflowCanvasFontFamilyCascader:"imageflow-canvas-font-family-cascader-vkjVlM"},e.Z=o},913552:function(t,e,i){var r=i(125161),s=i.n(r),n=i(404442),o=i.n(n)()(s());o.push([t.id,".loading-container-pWnZxj .loading{animation:.6s linear infinite forwards semi-animation-rotate}.loading-container-pWnZxj .hover-hidden-rwlUMc{display:block}.loading-container-pWnZxj .hover-visible-pBObVV,.loading-container-pWnZxj:hover .hover-hidden-rwlUMc{display:none}.loading-container-pWnZxj:hover .hover-visible-pBObVV{display:block}",""]),o.locals={"loading-container":"loading-container-pWnZxj",loadingContainer:"loading-container-pWnZxj","hover-hidden":"hover-hidden-rwlUMc",hoverHidden:"hover-hidden-rwlUMc","hover-visible":"hover-visible-pBObVV",hoverVisible:"hover-visible-pBObVV"},e.Z=o},665412:function(t,e,i){var r=i(125161),s=i.n(r),n=i(404442),o=i.n(n)()(s());o.push([t.id,".top-bar-DIywUq .hide-selected-label .semi-select-selection{display:none}.top-bar-DIywUq .hide-border{border-width:0}.top-bar-pop-align-right-uSSd_v .semi-portal-inner{transform:translate(calc(24px - 100%))translateY(0%)!important}.select-hidden-group-label-sX_ksZ .semi-select-group{height:0;margin:0;overflow:hidden;padding:0!important}textarea[data-fabric=textarea]{top:0!important;left:0!important}",""]),o.locals={"top-bar":"top-bar-DIywUq",topBar:"top-bar-DIywUq","top-bar-pop-align-right":"top-bar-pop-align-right-uSSd_v",topBarPopAlignRight:"top-bar-pop-align-right-uSSd_v","select-hidden-group-label":"select-hidden-group-label-sX_ksZ",selectHiddenGroupLabel:"select-hidden-group-label-sX_ksZ"},e.Z=o}}]);
//# sourceMappingURL=2161.24b14e86.js.map