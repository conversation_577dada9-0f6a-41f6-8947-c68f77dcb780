<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="186" height="186" viewBox="0 0 186 186">
  <defs>
    <radialGradient id="glassGrad" cx="50%" cy="42%" r="60%">
      <stop offset="0%" stop-color="#FFF7F6" stop-opacity="0.95"/>
      <stop offset="55%" stop-color="#FFD9D2" stop-opacity="0.35"/>
      <stop offset="100%" stop-color="#F2F4F9" stop-opacity="0.05"/>
    </radialGradient>
    <linearGradient id="shadowSuit" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#FFFFFF"/>
      <stop offset="100%" stop-color="#F3F5FA"/>
    </linearGradient>
    <linearGradient id="greyGrad" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#E7EBF3"/>
      <stop offset="100%" stop-color="#D7DCEA"/>
    </linearGradient>
    <linearGradient id="bootGrad" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#D1D8E8"/>
      <stop offset="100%" stop-color="#BAC3D8"/>
    </linearGradient>
    <clipPath id="helmetClip">
      <circle cx="93" cy="63" r="42"/>
    </clipPath>
  </defs>

  <!-- Backpack -->
  <rect x="20" y="88" width="22" height="54" rx="10" fill="url(#greyGrad)"/>

  <!-- Helmet ring -->
  <circle cx="93" cy="63" r="50" fill="#FFFFFF"/>
  <circle cx="93" cy="63" r="50" fill="none" stroke="#E6E9F2" stroke-width="8"/>
  <!-- Glass -->
  <circle cx="93" cy="63" r="42" fill="url(#glassGrad)"/>

  <!-- Glass highlight -->
  <path d="M60 42c10-9 25-14 40-12" fill="none" stroke="#FFFFFF" stroke-opacity="0.7" stroke-width="4" stroke-linecap="round"/>

  <!-- Face -->
  <g clip-path="url(#helmetClip)">
    <circle cx="93" cy="67" r="28" fill="#F7D1C6"/>
    <!-- eyes -->
    <circle cx="83.5" cy="64.5" r="5.5" fill="#2B2B2B"/>
    <circle cx="103.5" cy="64.5" r="5.5" fill="#2B2B2B"/>
    <circle cx="81.8" cy="62.8" r="1.6" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="101.8" cy="62.8" r="1.6" fill="#FFFFFF" opacity="0.9"/>
    <!-- blush -->
    <circle cx="77" cy="72" r="4.2" fill="#F2A3A3" opacity="0.45"/>
    <circle cx="109" cy="72" r="4.2" fill="#F2A3A3" opacity="0.45"/>
    <!-- smile -->
    <path d="M84 78c3 4 15 4 18 0" stroke="#C7756D" stroke-width="2.2" stroke-linecap="round" fill="none"/>
  </g>

  <!-- Left side ear/connector -->
  <rect x="38" y="48" width="10" height="30" rx="5" fill="url(#greyGrad)"/>

  <!-- Body -->
  <g>
    <!-- torso -->
    <rect x="56" y="98" width="74" height="68" rx="18" fill="url(#shadowSuit)" stroke="#E6E9F2" stroke-width="3"/>
    <!-- chest logo -->
    <circle cx="93" cy="132" r="14" fill="#111"/>
    <text x="93" y="135" text-anchor="middle" font-size="9" font-family="Arial, Helvetica, sans-serif" fill="#FFFFFF" style="letter-spacing:0.2px;">bodor</text>

    <!-- arms -->
    <rect x="32" y="106" width="28" height="40" rx="14" fill="#FFFFFF" stroke="#E6E9F2" stroke-width="3"/>
    <rect x="126" y="106" width="28" height="40" rx="14" fill="#FFFFFF" stroke="#E6E9F2" stroke-width="3"/>
    <!-- gloves -->
    <circle cx="46" cy="148" r="9" fill="url(#greyGrad)" stroke="#CDD4E5" stroke-width="2"/>
    <circle cx="140" cy="148" r="9" fill="url(#greyGrad)" stroke="#CDD4E5" stroke-width="2"/>

    <!-- legs -->
    <rect x="66" y="158" width="22" height="20" rx="6" fill="url(#greyGrad)" stroke="#D7DDEB" stroke-width="2"/>
    <rect x="98" y="158" width="22" height="20" rx="6" fill="url(#greyGrad)" stroke="#D7DDEB" stroke-width="2"/>

    <!-- boots -->
    <rect x="56" y="174" width="40" height="10" rx="5" fill="url(#bootGrad)"/>
    <rect x="90" y="174" width="40" height="10" rx="5" fill="url(#bootGrad)"/>
  </g>

  <!-- Subtle ground shadow -->
  <ellipse cx="93" cy="182" rx="38" ry="4" fill="#000" opacity="0.06"/>
</svg>