<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="186" height="186" viewBox="0 0 186 186">
  <defs>
    <radialGradient id="glassGrad" cx="50%" cy="42%" r="60%">
      <stop offset="0%" stop-color="#FFF7F6" stop-opacity="0.95"/>
      <stop offset="55%" stop-color="#FFD9D2" stop-opacity="0.35"/>
      <stop offset="100%" stop-color="#F2F4F9" stop-opacity="0.05"/>
    </radialGradient>
    <linearGradient id="shadowSuit" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#FFFFFF"/>
      <stop offset="100%" stop-color="#F3F5FA"/>
    </linearGradient>
    <linearGradient id="greyGrad" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#E7EBF3"/>
      <stop offset="100%" stop-color="#D7DCEA"/>
    </linearGradient>
    <linearGradient id="bootGrad" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#D1D8E8"/>
      <stop offset="100%" stop-color="#BAC3D8"/>
    </linearGradient>
    <clipPath id="helmetClip">
      <circle cx="93" cy="63" r="42"/>
    </clipPath>
  </defs>

  <!-- Backpack（更窄一点） -->
  <rect x="24" y="88" width="18" height="54" rx="9" fill="url(#greyGrad)"/>

  <!-- Helmet ring（更细） -->
  <circle cx="93" cy="63" r="50" fill="none" stroke="#E6E9F2" stroke-width="6"/>

  <!-- Glass -->
  <circle cx="93" cy="63" r="42" fill="url(#glassGrad)"/>

  <!-- Glass highlight -->
  <path d="M60 42c10-9 25-14 40-12" fill="none" stroke="#FFFFFF" stroke-opacity="0.7" stroke-width="4" stroke-linecap="round"/>

  <!-- Face（更精致比例） -->
  <g clip-path="url(#helmetClip)">
    <circle cx="93" cy="67" r="26" fill="#F7D1C6"/>
    <!-- eyes -->
    <circle cx="83.5" cy="64.5" r="5" fill="#2B2B2B"/>
    <circle cx="103.5" cy="64.5" r="5" fill="#2B2B2B"/>
    <circle cx="81.8" cy="62.8" r="1.5" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="101.8" cy="62.8" r="1.5" fill="#FFFFFF" opacity="0.9"/>
    <!-- blush -->
    <circle cx="77" cy="72" r="4" fill="#F2A3A3" opacity="0.45"/>
    <circle cx="109" cy="72" r="4" fill="#F2A3A3" opacity="0.45"/>
    <!-- smile -->
    <path d="M84 78c3 4 15 4 18 0" stroke="#C7756D" stroke-width="2" stroke-linecap="round" fill="none"/>
  </g>

  <!-- Left side ear/connector（更细） -->
  <rect x="40" y="48" width="8" height="30" rx="4" fill="url(#greyGrad)"/>

  <!-- Body（更瘦的躯干） -->
  <g>
    <!-- torso -->
    <rect x="64" y="100" width="58" height="66" rx="16" fill="url(#shadowSuit)" stroke="#E6E9F2" stroke-width="3"/>

    <!-- chest logo（略小） -->
    <circle cx="93" cy="132" r="12" fill="#111"/>
    <text x="93" y="134" text-anchor="middle" font-size="8" font-family="Arial, Helvetica, sans-serif" fill="#FFFFFF" style="letter-spacing:0.2px;">bodor</text>

    <!-- arms（更窄） -->
    <rect x="40" y="108" width="22" height="38" rx="12" fill="#FFFFFF" stroke="#E6E9F2" stroke-width="3"/>
    <rect x="124" y="108" width="22" height="38" rx="12" fill="#FFFFFF" stroke="#E6E9F2" stroke-width="3"/>

    <!-- gloves（略缩小） -->
    <circle cx="51" cy="148" r="8" fill="url(#greyGrad)" stroke="#CDD4E5" stroke-width="2"/>
    <circle cx="135" cy="148" r="8" fill="url(#greyGrad)" stroke="#CDD4E5" stroke-width="2"/>

    <!-- legs（更细） -->
    <rect x="72" y="158" width="18" height="20" rx="6" fill="url(#greyGrad)" stroke="#D7DDEB" stroke-width="2"/>
    <rect x="96" y="158" width="18" height="20" rx="6" fill="url(#greyGrad)" stroke="#D7DDEB" stroke-width="2"/>

    <!-- boots（略缩小） -->
    <rect x="64" y="174" width="34" height="10" rx="5" fill="url(#bootGrad)"/>
    <rect x="88" y="174" width="34" height="10" rx="5" fill="url(#bootGrad)"/>
  </g>

  <!-- Subtle ground shadow（与身体比例匹配） -->
  <ellipse cx="93" cy="182" rx="34" ry="4" fill="#000" opacity="0.06"/>
</svg>