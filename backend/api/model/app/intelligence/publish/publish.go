// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package publish

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
	"github.com/coze-dev/coze-studio/backend/api/model/app/intelligence/common"
	"github.com/coze-dev/coze-studio/backend/api/model/base"
)

type ConnectorClassification int64

const (
	// API or SDK
	ConnectorClassification_APIOrSDK ConnectorClassification = 1
	// social platform
	ConnectorClassification_SocialPlatform ConnectorClassification = 2
	// Coze Shop/Template
	ConnectorClassification_Coze ConnectorClassification = 3
	// Mini Program
	ConnectorClassification_MiniProgram ConnectorClassification = 4
	// MCP Extension Library
	ConnectorClassification_CozeSpaceExtensionLibrary ConnectorClassification = 5
)

func (p ConnectorClassification) String() string {
	switch p {
	case ConnectorClassification_APIOrSDK:
		return "APIOrSDK"
	case ConnectorClassification_SocialPlatform:
		return "SocialPlatform"
	case ConnectorClassification_Coze:
		return "Coze"
	case ConnectorClassification_MiniProgram:
		return "MiniProgram"
	case ConnectorClassification_CozeSpaceExtensionLibrary:
		return "CozeSpaceExtensionLibrary"
	}
	return "<UNSET>"
}

func ConnectorClassificationFromString(s string) (ConnectorClassification, error) {
	switch s {
	case "APIOrSDK":
		return ConnectorClassification_APIOrSDK, nil
	case "SocialPlatform":
		return ConnectorClassification_SocialPlatform, nil
	case "Coze":
		return ConnectorClassification_Coze, nil
	case "MiniProgram":
		return ConnectorClassification_MiniProgram, nil
	case "CozeSpaceExtensionLibrary":
		return ConnectorClassification_CozeSpaceExtensionLibrary, nil
	}
	return ConnectorClassification(0), fmt.Errorf("not a valid ConnectorClassification string")
}

func ConnectorClassificationPtr(v ConnectorClassification) *ConnectorClassification { return &v }
func (p *ConnectorClassification) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ConnectorClassification(result.Int64)
	return
}

func (p *ConnectorClassification) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ConnectorConfigStatus int64

const (
	// Configured
	ConnectorConfigStatus_Configured ConnectorConfigStatus = 1
	// Not configured
	ConnectorConfigStatus_NotConfigured ConnectorConfigStatus = 2
	// Token changes
	ConnectorConfigStatus_Disconnected ConnectorConfigStatus = 3
	// Configuring, authorizing
	ConnectorConfigStatus_Configuring ConnectorConfigStatus = 4
	// Need to reconfigure
	ConnectorConfigStatus_NeedReconfiguring ConnectorConfigStatus = 5
)

func (p ConnectorConfigStatus) String() string {
	switch p {
	case ConnectorConfigStatus_Configured:
		return "Configured"
	case ConnectorConfigStatus_NotConfigured:
		return "NotConfigured"
	case ConnectorConfigStatus_Disconnected:
		return "Disconnected"
	case ConnectorConfigStatus_Configuring:
		return "Configuring"
	case ConnectorConfigStatus_NeedReconfiguring:
		return "NeedReconfiguring"
	}
	return "<UNSET>"
}

func ConnectorConfigStatusFromString(s string) (ConnectorConfigStatus, error) {
	switch s {
	case "Configured":
		return ConnectorConfigStatus_Configured, nil
	case "NotConfigured":
		return ConnectorConfigStatus_NotConfigured, nil
	case "Disconnected":
		return ConnectorConfigStatus_Disconnected, nil
	case "Configuring":
		return ConnectorConfigStatus_Configuring, nil
	case "NeedReconfiguring":
		return ConnectorConfigStatus_NeedReconfiguring, nil
	}
	return ConnectorConfigStatus(0), fmt.Errorf("not a valid ConnectorConfigStatus string")
}

func ConnectorConfigStatusPtr(v ConnectorConfigStatus) *ConnectorConfigStatus { return &v }
func (p *ConnectorConfigStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ConnectorConfigStatus(result.Int64)
	return
}

func (p *ConnectorConfigStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ConnectorStatus int64

const (
	// Normal
	ConnectorStatus_Normal ConnectorStatus = 0
	// Under review.
	ConnectorStatus_InReview ConnectorStatus = 1
	// offline
	ConnectorStatus_Offline ConnectorStatus = 2
)

func (p ConnectorStatus) String() string {
	switch p {
	case ConnectorStatus_Normal:
		return "Normal"
	case ConnectorStatus_InReview:
		return "InReview"
	case ConnectorStatus_Offline:
		return "Offline"
	}
	return "<UNSET>"
}

func ConnectorStatusFromString(s string) (ConnectorStatus, error) {
	switch s {
	case "Normal":
		return ConnectorStatus_Normal, nil
	case "InReview":
		return ConnectorStatus_InReview, nil
	case "Offline":
		return ConnectorStatus_Offline, nil
	}
	return ConnectorStatus(0), fmt.Errorf("not a valid ConnectorStatus string")
}

func ConnectorStatusPtr(v ConnectorStatus) *ConnectorStatus { return &v }
func (p *ConnectorStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ConnectorStatus(result.Int64)
	return
}

func (p *ConnectorStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ConnectorBindType int64

const (
	// No binding required
	ConnectorBindType_NoBindRequired ConnectorBindType = 1
	// Auth binding
	ConnectorBindType_AuthBind ConnectorBindType = 2
	// Kv binding
	ConnectorBindType_KvBind ConnectorBindType = 3
	// Kv and Auth authorization
	ConnectorBindType_KvAuthBind ConnectorBindType = 4
	// API channel binding
	ConnectorBindType_ApiBind    ConnectorBindType = 5
	ConnectorBindType_WebSDKBind ConnectorBindType = 6
	ConnectorBindType_StoreBind  ConnectorBindType = 7
	// One button each for authorization and configuration
	ConnectorBindType_AuthAndConfig ConnectorBindType = 8
	// template channel binding
	ConnectorBindType_TemplateBind ConnectorBindType = 9
)

func (p ConnectorBindType) String() string {
	switch p {
	case ConnectorBindType_NoBindRequired:
		return "NoBindRequired"
	case ConnectorBindType_AuthBind:
		return "AuthBind"
	case ConnectorBindType_KvBind:
		return "KvBind"
	case ConnectorBindType_KvAuthBind:
		return "KvAuthBind"
	case ConnectorBindType_ApiBind:
		return "ApiBind"
	case ConnectorBindType_WebSDKBind:
		return "WebSDKBind"
	case ConnectorBindType_StoreBind:
		return "StoreBind"
	case ConnectorBindType_AuthAndConfig:
		return "AuthAndConfig"
	case ConnectorBindType_TemplateBind:
		return "TemplateBind"
	}
	return "<UNSET>"
}

func ConnectorBindTypeFromString(s string) (ConnectorBindType, error) {
	switch s {
	case "NoBindRequired":
		return ConnectorBindType_NoBindRequired, nil
	case "AuthBind":
		return ConnectorBindType_AuthBind, nil
	case "KvBind":
		return ConnectorBindType_KvBind, nil
	case "KvAuthBind":
		return ConnectorBindType_KvAuthBind, nil
	case "ApiBind":
		return ConnectorBindType_ApiBind, nil
	case "WebSDKBind":
		return ConnectorBindType_WebSDKBind, nil
	case "StoreBind":
		return ConnectorBindType_StoreBind, nil
	case "AuthAndConfig":
		return ConnectorBindType_AuthAndConfig, nil
	case "TemplateBind":
		return ConnectorBindType_TemplateBind, nil
	}
	return ConnectorBindType(0), fmt.Errorf("not a valid ConnectorBindType string")
}

func ConnectorBindTypePtr(v ConnectorBindType) *ConnectorBindType { return &v }
func (p *ConnectorBindType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ConnectorBindType(result.Int64)
	return
}

func (p *ConnectorBindType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type UserAuthStatus int64

const (
	// Authorized
	UserAuthStatus_Authorized UserAuthStatus = 1
	// unauthorized
	UserAuthStatus_UnAuthorized UserAuthStatus = 2
	// Authorizing
	UserAuthStatus_Authorizing UserAuthStatus = 3
)

func (p UserAuthStatus) String() string {
	switch p {
	case UserAuthStatus_Authorized:
		return "Authorized"
	case UserAuthStatus_UnAuthorized:
		return "UnAuthorized"
	case UserAuthStatus_Authorizing:
		return "Authorizing"
	}
	return "<UNSET>"
}

func UserAuthStatusFromString(s string) (UserAuthStatus, error) {
	switch s {
	case "Authorized":
		return UserAuthStatus_Authorized, nil
	case "UnAuthorized":
		return UserAuthStatus_UnAuthorized, nil
	case "Authorizing":
		return UserAuthStatus_Authorizing, nil
	}
	return UserAuthStatus(0), fmt.Errorf("not a valid UserAuthStatus string")
}

func UserAuthStatusPtr(v UserAuthStatus) *UserAuthStatus { return &v }
func (p *UserAuthStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = UserAuthStatus(result.Int64)
	return
}

func (p *UserAuthStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PublishRecordStatus int64

const (
	// Packing
	PublishRecordStatus_Packing PublishRecordStatus = 0
	// Packaging failed
	PublishRecordStatus_PackFailed PublishRecordStatus = 1
	// Under review.
	PublishRecordStatus_Auditing PublishRecordStatus = 2
	// review disapproved
	PublishRecordStatus_AuditNotPass PublishRecordStatus = 3
	// Channel is being released.
	PublishRecordStatus_ConnectorPublishing PublishRecordStatus = 4
	// release complete
	PublishRecordStatus_PublishDone PublishRecordStatus = 5
)

func (p PublishRecordStatus) String() string {
	switch p {
	case PublishRecordStatus_Packing:
		return "Packing"
	case PublishRecordStatus_PackFailed:
		return "PackFailed"
	case PublishRecordStatus_Auditing:
		return "Auditing"
	case PublishRecordStatus_AuditNotPass:
		return "AuditNotPass"
	case PublishRecordStatus_ConnectorPublishing:
		return "ConnectorPublishing"
	case PublishRecordStatus_PublishDone:
		return "PublishDone"
	}
	return "<UNSET>"
}

func PublishRecordStatusFromString(s string) (PublishRecordStatus, error) {
	switch s {
	case "Packing":
		return PublishRecordStatus_Packing, nil
	case "PackFailed":
		return PublishRecordStatus_PackFailed, nil
	case "Auditing":
		return PublishRecordStatus_Auditing, nil
	case "AuditNotPass":
		return PublishRecordStatus_AuditNotPass, nil
	case "ConnectorPublishing":
		return PublishRecordStatus_ConnectorPublishing, nil
	case "PublishDone":
		return PublishRecordStatus_PublishDone, nil
	}
	return PublishRecordStatus(0), fmt.Errorf("not a valid PublishRecordStatus string")
}

func PublishRecordStatusPtr(v PublishRecordStatus) *PublishRecordStatus { return &v }
func (p *PublishRecordStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PublishRecordStatus(result.Int64)
	return
}

func (p *PublishRecordStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// project
type ConnectorPublishStatus int64

const (
	// In release
	ConnectorPublishStatus_Default ConnectorPublishStatus = 0
	// Under review.
	ConnectorPublishStatus_Auditing ConnectorPublishStatus = 1
	// success
	ConnectorPublishStatus_Success ConnectorPublishStatus = 2
	// fail
	ConnectorPublishStatus_Failed ConnectorPublishStatus = 3
	//disable
	ConnectorPublishStatus_Disable ConnectorPublishStatus = 4
)

func (p ConnectorPublishStatus) String() string {
	switch p {
	case ConnectorPublishStatus_Default:
		return "Default"
	case ConnectorPublishStatus_Auditing:
		return "Auditing"
	case ConnectorPublishStatus_Success:
		return "Success"
	case ConnectorPublishStatus_Failed:
		return "Failed"
	case ConnectorPublishStatus_Disable:
		return "Disable"
	}
	return "<UNSET>"
}

func ConnectorPublishStatusFromString(s string) (ConnectorPublishStatus, error) {
	switch s {
	case "Default":
		return ConnectorPublishStatus_Default, nil
	case "Auditing":
		return ConnectorPublishStatus_Auditing, nil
	case "Success":
		return ConnectorPublishStatus_Success, nil
	case "Failed":
		return ConnectorPublishStatus_Failed, nil
	case "Disable":
		return ConnectorPublishStatus_Disable, nil
	}
	return ConnectorPublishStatus(0), fmt.Errorf("not a valid ConnectorPublishStatus string")
}

func ConnectorPublishStatusPtr(v ConnectorPublishStatus) *ConnectorPublishStatus { return &v }
func (p *ConnectorPublishStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ConnectorPublishStatus(result.Int64)
	return
}

func (p *ConnectorPublishStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type GetProjectPublishedConnectorRequest struct {
	ProjectID int64      `thrift:"project_id,1,required" form:"project_id,required" json:"project_id,string,required" query:"project_id,required"`
	Base      *base.Base `thrift:"Base,255,optional" form:"-" json:"-" query:"-"`
}

func NewGetProjectPublishedConnectorRequest() *GetProjectPublishedConnectorRequest {
	return &GetProjectPublishedConnectorRequest{}
}

func (p *GetProjectPublishedConnectorRequest) InitDefault() {
}

func (p *GetProjectPublishedConnectorRequest) GetProjectID() (v int64) {
	return p.ProjectID
}

var GetProjectPublishedConnectorRequest_Base_DEFAULT *base.Base

func (p *GetProjectPublishedConnectorRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetProjectPublishedConnectorRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_GetProjectPublishedConnectorRequest = map[int16]string{
	1:   "project_id",
	255: "Base",
}

func (p *GetProjectPublishedConnectorRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetProjectPublishedConnectorRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProjectID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjectID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetProjectID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetProjectPublishedConnectorRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetProjectPublishedConnectorRequest[fieldId]))
}

func (p *GetProjectPublishedConnectorRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *GetProjectPublishedConnectorRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetProjectPublishedConnectorRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetProjectPublishedConnectorRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetProjectPublishedConnectorRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("project_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetProjectPublishedConnectorRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetProjectPublishedConnectorRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProjectPublishedConnectorRequest(%+v)", *p)

}

type GetProjectPublishedConnectorResponse struct {
	Data     []*common.ConnectorInfo `thrift:"data,1" form:"data" json:"data" query:"data"`
	Code     int64                   `thrift:"code,253" form:"code" json:"code" query:"code"`
	Msg      string                  `thrift:"msg,254" form:"msg" json:"msg" query:"msg"`
	BaseResp *base.BaseResp          `thrift:"BaseResp,255,optional" form:"-" json:"-" query:"-"`
}

func NewGetProjectPublishedConnectorResponse() *GetProjectPublishedConnectorResponse {
	return &GetProjectPublishedConnectorResponse{}
}

func (p *GetProjectPublishedConnectorResponse) InitDefault() {
}

func (p *GetProjectPublishedConnectorResponse) GetData() (v []*common.ConnectorInfo) {
	return p.Data
}

func (p *GetProjectPublishedConnectorResponse) GetCode() (v int64) {
	return p.Code
}

func (p *GetProjectPublishedConnectorResponse) GetMsg() (v string) {
	return p.Msg
}

var GetProjectPublishedConnectorResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetProjectPublishedConnectorResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetProjectPublishedConnectorResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_GetProjectPublishedConnectorResponse = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *GetProjectPublishedConnectorResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetProjectPublishedConnectorResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetProjectPublishedConnectorResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetProjectPublishedConnectorResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*common.ConnectorInfo, 0, size)
	values := make([]common.ConnectorInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *GetProjectPublishedConnectorResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *GetProjectPublishedConnectorResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *GetProjectPublishedConnectorResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetProjectPublishedConnectorResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetProjectPublishedConnectorResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetProjectPublishedConnectorResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Data)); err != nil {
		return err
	}
	for _, v := range p.Data {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetProjectPublishedConnectorResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *GetProjectPublishedConnectorResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *GetProjectPublishedConnectorResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetProjectPublishedConnectorResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetProjectPublishedConnectorResponse(%+v)", *p)

}

type PublishConnectorListRequest struct {
	ProjectID int64      `thrift:"project_id,1,required" form:"project_id,required" json:"project_id,string,required" query:"project_id,required"`
	Base      *base.Base `thrift:"Base,255,optional" form:"-" json:"-" query:"-"`
}

func NewPublishConnectorListRequest() *PublishConnectorListRequest {
	return &PublishConnectorListRequest{}
}

func (p *PublishConnectorListRequest) InitDefault() {
}

func (p *PublishConnectorListRequest) GetProjectID() (v int64) {
	return p.ProjectID
}

var PublishConnectorListRequest_Base_DEFAULT *base.Base

func (p *PublishConnectorListRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return PublishConnectorListRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_PublishConnectorListRequest = map[int16]string{
	1:   "project_id",
	255: "Base",
}

func (p *PublishConnectorListRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *PublishConnectorListRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProjectID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjectID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetProjectID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PublishConnectorListRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PublishConnectorListRequest[fieldId]))
}

func (p *PublishConnectorListRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *PublishConnectorListRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *PublishConnectorListRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PublishConnectorListRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PublishConnectorListRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("project_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PublishConnectorListRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *PublishConnectorListRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PublishConnectorListRequest(%+v)", *p)

}

type PublishConnectorListResponse struct {
	Data     *PublishConnectorListData `thrift:"data,1" form:"data" json:"data" query:"data"`
	Code     int64                     `thrift:"code,253" form:"code" json:"code" query:"code"`
	Msg      string                    `thrift:"msg,254" form:"msg" json:"msg" query:"msg"`
	BaseResp *base.BaseResp            `thrift:"BaseResp,255,optional" form:"-" json:"-" query:"-"`
}

func NewPublishConnectorListResponse() *PublishConnectorListResponse {
	return &PublishConnectorListResponse{}
}

func (p *PublishConnectorListResponse) InitDefault() {
}

var PublishConnectorListResponse_Data_DEFAULT *PublishConnectorListData

func (p *PublishConnectorListResponse) GetData() (v *PublishConnectorListData) {
	if !p.IsSetData() {
		return PublishConnectorListResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *PublishConnectorListResponse) GetCode() (v int64) {
	return p.Code
}

func (p *PublishConnectorListResponse) GetMsg() (v string) {
	return p.Msg
}

var PublishConnectorListResponse_BaseResp_DEFAULT *base.BaseResp

func (p *PublishConnectorListResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return PublishConnectorListResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_PublishConnectorListResponse = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *PublishConnectorListResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *PublishConnectorListResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *PublishConnectorListResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PublishConnectorListResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PublishConnectorListResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPublishConnectorListData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *PublishConnectorListResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *PublishConnectorListResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *PublishConnectorListResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *PublishConnectorListResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PublishConnectorListResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PublishConnectorListResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PublishConnectorListResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *PublishConnectorListResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *PublishConnectorListResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *PublishConnectorListResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PublishConnectorListResponse(%+v)", *p)

}

type PublishConnectorListData struct {
	ConnectorList   []*PublishConnectorInfo `thrift:"connector_list,1" form:"connector_list" json:"connector_list" query:"connector_list"`
	LastPublishInfo *LastPublishInfo        `thrift:"last_publish_info,2" form:"last_publish_info" json:"last_publish_info" query:"last_publish_info"`
	// Channel collection information, the key is connector_union_id
	ConnectorUnionInfoMap map[int64]*ConnectorUnionInfo `thrift:"connector_union_info_map,3" form:"connector_union_info_map" json:"connector_union_info_map" query:"connector_union_info_map"`
}

func NewPublishConnectorListData() *PublishConnectorListData {
	return &PublishConnectorListData{}
}

func (p *PublishConnectorListData) InitDefault() {
}

func (p *PublishConnectorListData) GetConnectorList() (v []*PublishConnectorInfo) {
	return p.ConnectorList
}

var PublishConnectorListData_LastPublishInfo_DEFAULT *LastPublishInfo

func (p *PublishConnectorListData) GetLastPublishInfo() (v *LastPublishInfo) {
	if !p.IsSetLastPublishInfo() {
		return PublishConnectorListData_LastPublishInfo_DEFAULT
	}
	return p.LastPublishInfo
}

func (p *PublishConnectorListData) GetConnectorUnionInfoMap() (v map[int64]*ConnectorUnionInfo) {
	return p.ConnectorUnionInfoMap
}

var fieldIDToName_PublishConnectorListData = map[int16]string{
	1: "connector_list",
	2: "last_publish_info",
	3: "connector_union_info_map",
}

func (p *PublishConnectorListData) IsSetLastPublishInfo() bool {
	return p.LastPublishInfo != nil
}

func (p *PublishConnectorListData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PublishConnectorListData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PublishConnectorListData) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PublishConnectorInfo, 0, size)
	values := make([]PublishConnectorInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ConnectorList = _field
	return nil
}
func (p *PublishConnectorListData) ReadField2(iprot thrift.TProtocol) error {
	_field := NewLastPublishInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.LastPublishInfo = _field
	return nil
}
func (p *PublishConnectorListData) ReadField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[int64]*ConnectorUnionInfo, size)
	values := make([]ConnectorUnionInfo, size)
	for i := 0; i < size; i++ {
		var _key int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_key = v
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.ConnectorUnionInfoMap = _field
	return nil
}

func (p *PublishConnectorListData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PublishConnectorListData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PublishConnectorListData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ConnectorList)); err != nil {
		return err
	}
	for _, v := range p.ConnectorList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PublishConnectorListData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("last_publish_info", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.LastPublishInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PublishConnectorListData) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_union_info_map", thrift.MAP, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.I64, thrift.STRUCT, len(p.ConnectorUnionInfoMap)); err != nil {
		return err
	}
	for k, v := range p.ConnectorUnionInfoMap {
		if err := oprot.WriteI64(k); err != nil {
			return err
		}
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *PublishConnectorListData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PublishConnectorListData(%+v)", *p)

}

type PublishConnectorInfo struct {
	ID      int64  `thrift:"id,1,required" form:"id,required" json:"id,string,required" query:"id,required"`
	Name    string `thrift:"name,2,required" form:"name,required" json:"name,required" query:"name,required"`
	IconURL string `thrift:"icon_url,3,required" form:"icon_url,required" json:"icon_url,required" query:"icon_url,required"`
	// describe
	Description string `thrift:"description,4,required" form:"description,required" json:"description,required" query:"description,required"`
	// description extension
	DescriptionExtra string `thrift:"description_extra,5" form:"description_extra" json:"description_extra" query:"description_extra"`
	// channel type
	ConnectorClassification ConnectorClassification `thrift:"connector_classification,6,required" form:"connector_classification,required" json:"connector_classification,required" query:"connector_classification,required"`
	// configuration status
	ConfigStatus ConnectorConfigStatus `thrift:"config_status,7,required" form:"config_status,required" json:"config_status,required" query:"config_status,required"`
	// channel status
	ConnectorStatus ConnectorStatus `thrift:"connector_status,8" form:"connector_status" json:"connector_status" query:"connector_status"`
	// binding type
	BindType ConnectorBindType `thrift:"bind_type,9,required" form:"bind_type,required" json:"bind_type,required" query:"bind_type,required"`
	// Binding information key field name value is value
	BindInfo map[string]string `thrift:"bind_info,10,required" form:"bind_info,required" json:"bind_info,required" query:"bind_info,required"`
	// Bind id information for unbinding and use
	BindID *string `thrift:"bind_id,11,optional" form:"bind_id" json:"bind_id,omitempty" query:"bind_id"`
	// user authorization login information
	AuthLoginInfo *AuthLoginInfo `thrift:"auth_login_info,12,optional" form:"auth_login_info" json:"auth_login_info,omitempty" query:"auth_login_info"`
	// Privacy Policy
	PrivacyPolicy string `thrift:"privacy_policy,13" form:"privacy_policy" json:"privacy_policy" query:"privacy_policy"`
	// User Agreement
	UserAgreement string `thrift:"user_agreement,14" form:"user_agreement" json:"user_agreement" query:"user_agreement"`
	// Whether to allow publishing
	AllowPublish bool `thrift:"allow_publish,15" form:"allow_publish" json:"allow_publish" query:"allow_publish"`
	// Reasons for not allowing publishing
	NotAllowPublishReason *string `thrift:"not_allow_publish_reason,16,optional" form:"not_allow_publish_reason" json:"not_allow_publish_reason,omitempty" query:"not_allow_publish_reason"`
	// Channel collection id, indicating the channel that needs to be aggregated and displayed.
	ConnectorUnionID *int64 `thrift:"connector_union_id,17,optional" form:"connector_union_id" json:"connector_union_id,string,omitempty" query:"connector_union_id"`
	// UI Options
	UIOptions []*UIOption `thrift:"UIOptions,18,optional" form:"UIOptions" json:"UIOptions,omitempty" query:"UIOptions"`
	// Support commercialization
	SupportMonetization *bool `thrift:"support_monetization,19,optional" form:"support_monetization" json:"support_monetization,omitempty" query:"support_monetization"`
	// Installation Guidelines
	InstallationGuide *string `thrift:"installation_guide,20,optional" form:"installation_guide" json:"installation_guide,omitempty" query:"installation_guide"`
	// Currently this field is only available bind_type == 8
	AuthStatus *UserAuthStatus `thrift:"auth_status,21,optional" form:"auth_status" json:"auth_status,omitempty" query:"auth_status"`
	// Configuration status toast
	ConfigStatusToast *string `thrift:"config_status_toast,22,optional" form:"config_status_toast" json:"config_status_toast,omitempty" query:"config_status_toast"`
	// connector_status the URL of the Complete Info button while under review
	ToCompleteInfoURL *string `thrift:"to_complete_info_url,23,optional" form:"to_complete_info_url" json:"to_complete_info_url,omitempty" query:"to_complete_info_url"`
	// Channel release tips
	ConnectorTips *string `thrift:"connector_tips,24,optional" form:"connector_tips" json:"connector_tips,omitempty" query:"connector_tips"`
}

func NewPublishConnectorInfo() *PublishConnectorInfo {
	return &PublishConnectorInfo{}
}

func (p *PublishConnectorInfo) InitDefault() {
}

func (p *PublishConnectorInfo) GetID() (v int64) {
	return p.ID
}

func (p *PublishConnectorInfo) GetName() (v string) {
	return p.Name
}

func (p *PublishConnectorInfo) GetIconURL() (v string) {
	return p.IconURL
}

func (p *PublishConnectorInfo) GetDescription() (v string) {
	return p.Description
}

func (p *PublishConnectorInfo) GetDescriptionExtra() (v string) {
	return p.DescriptionExtra
}

func (p *PublishConnectorInfo) GetConnectorClassification() (v ConnectorClassification) {
	return p.ConnectorClassification
}

func (p *PublishConnectorInfo) GetConfigStatus() (v ConnectorConfigStatus) {
	return p.ConfigStatus
}

func (p *PublishConnectorInfo) GetConnectorStatus() (v ConnectorStatus) {
	return p.ConnectorStatus
}

func (p *PublishConnectorInfo) GetBindType() (v ConnectorBindType) {
	return p.BindType
}

func (p *PublishConnectorInfo) GetBindInfo() (v map[string]string) {
	return p.BindInfo
}

var PublishConnectorInfo_BindID_DEFAULT string

func (p *PublishConnectorInfo) GetBindID() (v string) {
	if !p.IsSetBindID() {
		return PublishConnectorInfo_BindID_DEFAULT
	}
	return *p.BindID
}

var PublishConnectorInfo_AuthLoginInfo_DEFAULT *AuthLoginInfo

func (p *PublishConnectorInfo) GetAuthLoginInfo() (v *AuthLoginInfo) {
	if !p.IsSetAuthLoginInfo() {
		return PublishConnectorInfo_AuthLoginInfo_DEFAULT
	}
	return p.AuthLoginInfo
}

func (p *PublishConnectorInfo) GetPrivacyPolicy() (v string) {
	return p.PrivacyPolicy
}

func (p *PublishConnectorInfo) GetUserAgreement() (v string) {
	return p.UserAgreement
}

func (p *PublishConnectorInfo) GetAllowPublish() (v bool) {
	return p.AllowPublish
}

var PublishConnectorInfo_NotAllowPublishReason_DEFAULT string

func (p *PublishConnectorInfo) GetNotAllowPublishReason() (v string) {
	if !p.IsSetNotAllowPublishReason() {
		return PublishConnectorInfo_NotAllowPublishReason_DEFAULT
	}
	return *p.NotAllowPublishReason
}

var PublishConnectorInfo_ConnectorUnionID_DEFAULT int64

func (p *PublishConnectorInfo) GetConnectorUnionID() (v int64) {
	if !p.IsSetConnectorUnionID() {
		return PublishConnectorInfo_ConnectorUnionID_DEFAULT
	}
	return *p.ConnectorUnionID
}

var PublishConnectorInfo_UIOptions_DEFAULT []*UIOption

func (p *PublishConnectorInfo) GetUIOptions() (v []*UIOption) {
	if !p.IsSetUIOptions() {
		return PublishConnectorInfo_UIOptions_DEFAULT
	}
	return p.UIOptions
}

var PublishConnectorInfo_SupportMonetization_DEFAULT bool

func (p *PublishConnectorInfo) GetSupportMonetization() (v bool) {
	if !p.IsSetSupportMonetization() {
		return PublishConnectorInfo_SupportMonetization_DEFAULT
	}
	return *p.SupportMonetization
}

var PublishConnectorInfo_InstallationGuide_DEFAULT string

func (p *PublishConnectorInfo) GetInstallationGuide() (v string) {
	if !p.IsSetInstallationGuide() {
		return PublishConnectorInfo_InstallationGuide_DEFAULT
	}
	return *p.InstallationGuide
}

var PublishConnectorInfo_AuthStatus_DEFAULT UserAuthStatus

func (p *PublishConnectorInfo) GetAuthStatus() (v UserAuthStatus) {
	if !p.IsSetAuthStatus() {
		return PublishConnectorInfo_AuthStatus_DEFAULT
	}
	return *p.AuthStatus
}

var PublishConnectorInfo_ConfigStatusToast_DEFAULT string

func (p *PublishConnectorInfo) GetConfigStatusToast() (v string) {
	if !p.IsSetConfigStatusToast() {
		return PublishConnectorInfo_ConfigStatusToast_DEFAULT
	}
	return *p.ConfigStatusToast
}

var PublishConnectorInfo_ToCompleteInfoURL_DEFAULT string

func (p *PublishConnectorInfo) GetToCompleteInfoURL() (v string) {
	if !p.IsSetToCompleteInfoURL() {
		return PublishConnectorInfo_ToCompleteInfoURL_DEFAULT
	}
	return *p.ToCompleteInfoURL
}

var PublishConnectorInfo_ConnectorTips_DEFAULT string

func (p *PublishConnectorInfo) GetConnectorTips() (v string) {
	if !p.IsSetConnectorTips() {
		return PublishConnectorInfo_ConnectorTips_DEFAULT
	}
	return *p.ConnectorTips
}

var fieldIDToName_PublishConnectorInfo = map[int16]string{
	1:  "id",
	2:  "name",
	3:  "icon_url",
	4:  "description",
	5:  "description_extra",
	6:  "connector_classification",
	7:  "config_status",
	8:  "connector_status",
	9:  "bind_type",
	10: "bind_info",
	11: "bind_id",
	12: "auth_login_info",
	13: "privacy_policy",
	14: "user_agreement",
	15: "allow_publish",
	16: "not_allow_publish_reason",
	17: "connector_union_id",
	18: "UIOptions",
	19: "support_monetization",
	20: "installation_guide",
	21: "auth_status",
	22: "config_status_toast",
	23: "to_complete_info_url",
	24: "connector_tips",
}

func (p *PublishConnectorInfo) IsSetBindID() bool {
	return p.BindID != nil
}

func (p *PublishConnectorInfo) IsSetAuthLoginInfo() bool {
	return p.AuthLoginInfo != nil
}

func (p *PublishConnectorInfo) IsSetNotAllowPublishReason() bool {
	return p.NotAllowPublishReason != nil
}

func (p *PublishConnectorInfo) IsSetConnectorUnionID() bool {
	return p.ConnectorUnionID != nil
}

func (p *PublishConnectorInfo) IsSetUIOptions() bool {
	return p.UIOptions != nil
}

func (p *PublishConnectorInfo) IsSetSupportMonetization() bool {
	return p.SupportMonetization != nil
}

func (p *PublishConnectorInfo) IsSetInstallationGuide() bool {
	return p.InstallationGuide != nil
}

func (p *PublishConnectorInfo) IsSetAuthStatus() bool {
	return p.AuthStatus != nil
}

func (p *PublishConnectorInfo) IsSetConfigStatusToast() bool {
	return p.ConfigStatusToast != nil
}

func (p *PublishConnectorInfo) IsSetToCompleteInfoURL() bool {
	return p.ToCompleteInfoURL != nil
}

func (p *PublishConnectorInfo) IsSetConnectorTips() bool {
	return p.ConnectorTips != nil
}

func (p *PublishConnectorInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetName bool = false
	var issetIconURL bool = false
	var issetDescription bool = false
	var issetConnectorClassification bool = false
	var issetConfigStatus bool = false
	var issetBindType bool = false
	var issetBindInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetIconURL = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescription = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetConnectorClassification = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetConfigStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetBindType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetBindInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField23(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField24(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetIconURL {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDescription {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetConnectorClassification {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetConfigStatus {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetBindType {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetBindInfo {
		fieldId = 10
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PublishConnectorInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PublishConnectorInfo[fieldId]))
}

func (p *PublishConnectorInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IconURL = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DescriptionExtra = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field ConnectorClassification
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ConnectorClassification(v)
	}
	p.ConnectorClassification = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field ConnectorConfigStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ConnectorConfigStatus(v)
	}
	p.ConfigStatus = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field ConnectorStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ConnectorStatus(v)
	}
	p.ConnectorStatus = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field ConnectorBindType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ConnectorBindType(v)
	}
	p.BindType = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField10(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.BindInfo = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BindID = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField12(iprot thrift.TProtocol) error {
	_field := NewAuthLoginInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.AuthLoginInfo = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PrivacyPolicy = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField14(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserAgreement = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField15(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowPublish = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField16(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NotAllowPublishReason = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField17(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ConnectorUnionID = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField18(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*UIOption, 0, size)
	values := make([]UIOption, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.UIOptions = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField19(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SupportMonetization = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField20(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstallationGuide = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField21(iprot thrift.TProtocol) error {

	var _field *UserAuthStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := UserAuthStatus(v)
		_field = &tmp
	}
	p.AuthStatus = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField22(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ConfigStatusToast = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField23(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ToCompleteInfoURL = _field
	return nil
}
func (p *PublishConnectorInfo) ReadField24(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ConnectorTips = _field
	return nil
}

func (p *PublishConnectorInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PublishConnectorInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
		if err = p.writeField23(oprot); err != nil {
			fieldId = 23
			goto WriteFieldError
		}
		if err = p.writeField24(oprot); err != nil {
			fieldId = 24
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PublishConnectorInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("icon_url", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IconURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("description", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("description_extra", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DescriptionExtra); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_classification", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ConnectorClassification)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("config_status", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ConfigStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_status", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ConnectorStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bind_type", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BindType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bind_info", thrift.MAP, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.BindInfo)); err != nil {
		return err
	}
	for k, v := range p.BindInfo {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetBindID() {
		if err = oprot.WriteFieldBegin("bind_id", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BindID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuthLoginInfo() {
		if err = oprot.WriteFieldBegin("auth_login_info", thrift.STRUCT, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.AuthLoginInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("privacy_policy", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PrivacyPolicy); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_agreement", thrift.STRING, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserAgreement); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("allow_publish", thrift.BOOL, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.AllowPublish); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetNotAllowPublishReason() {
		if err = oprot.WriteFieldBegin("not_allow_publish_reason", thrift.STRING, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NotAllowPublishReason); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetConnectorUnionID() {
		if err = oprot.WriteFieldBegin("connector_union_id", thrift.I64, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ConnectorUnionID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetUIOptions() {
		if err = oprot.WriteFieldBegin("UIOptions", thrift.LIST, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.UIOptions)); err != nil {
			return err
		}
		for _, v := range p.UIOptions {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetSupportMonetization() {
		if err = oprot.WriteFieldBegin("support_monetization", thrift.BOOL, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.SupportMonetization); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstallationGuide() {
		if err = oprot.WriteFieldBegin("installation_guide", thrift.STRING, 20); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstallationGuide); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuthStatus() {
		if err = oprot.WriteFieldBegin("auth_status", thrift.I32, 21); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.AuthStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetConfigStatusToast() {
		if err = oprot.WriteFieldBegin("config_status_toast", thrift.STRING, 22); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ConfigStatusToast); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField23(oprot thrift.TProtocol) (err error) {
	if p.IsSetToCompleteInfoURL() {
		if err = oprot.WriteFieldBegin("to_complete_info_url", thrift.STRING, 23); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ToCompleteInfoURL); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 end error: ", p), err)
}
func (p *PublishConnectorInfo) writeField24(oprot thrift.TProtocol) (err error) {
	if p.IsSetConnectorTips() {
		if err = oprot.WriteFieldBegin("connector_tips", thrift.STRING, 24); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ConnectorTips); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 end error: ", p), err)
}

func (p *PublishConnectorInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PublishConnectorInfo(%+v)", *p)

}

type LastPublishInfo struct {
	VersionNumber string  `thrift:"version_number,1" form:"version_number" json:"version_number" query:"version_number"`
	ConnectorIds  []int64 `thrift:"connector_ids,2" form:"connector_ids" json:"connector_ids,string" query:"connector_ids"`
	// channel release configuration
	ConnectorPublishConfig map[int64]*ConnectorPublishConfig `thrift:"connector_publish_config,3" form:"connector_publish_config" json:"connector_publish_config" query:"connector_publish_config"`
}

func NewLastPublishInfo() *LastPublishInfo {
	return &LastPublishInfo{}
}

func (p *LastPublishInfo) InitDefault() {
}

func (p *LastPublishInfo) GetVersionNumber() (v string) {
	return p.VersionNumber
}

func (p *LastPublishInfo) GetConnectorIds() (v []int64) {
	return p.ConnectorIds
}

func (p *LastPublishInfo) GetConnectorPublishConfig() (v map[int64]*ConnectorPublishConfig) {
	return p.ConnectorPublishConfig
}

var fieldIDToName_LastPublishInfo = map[int16]string{
	1: "version_number",
	2: "connector_ids",
	3: "connector_publish_config",
}

func (p *LastPublishInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LastPublishInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LastPublishInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VersionNumber = _field
	return nil
}
func (p *LastPublishInfo) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int64, 0, size)
	for i := 0; i < size; i++ {

		var _elem int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ConnectorIds = _field
	return nil
}
func (p *LastPublishInfo) ReadField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[int64]*ConnectorPublishConfig, size)
	values := make([]ConnectorPublishConfig, size)
	for i := 0; i < size; i++ {
		var _key int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_key = v
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.ConnectorPublishConfig = _field
	return nil
}

func (p *LastPublishInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LastPublishInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LastPublishInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version_number", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VersionNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LastPublishInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_ids", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.I64, len(p.ConnectorIds)); err != nil {
		return err
	}
	for _, v := range p.ConnectorIds {
		if err := oprot.WriteI64(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LastPublishInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_publish_config", thrift.MAP, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.I64, thrift.STRUCT, len(p.ConnectorPublishConfig)); err != nil {
		return err
	}
	for k, v := range p.ConnectorPublishConfig {
		if err := oprot.WriteI64(k); err != nil {
			return err
		}
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *LastPublishInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LastPublishInfo(%+v)", *p)

}

type ConnectorUnionInfo struct {
	ID               int64                       `thrift:"id,1,required" form:"id,required" json:"id,string,required" query:"id,required"`
	Name             string                      `thrift:"name,2,required" form:"name,required" json:"name,required" query:"name,required"`
	Description      string                      `thrift:"description,3,required" form:"description,required" json:"description,required" query:"description,required"`
	IconURL          string                      `thrift:"icon_url,4,required" form:"icon_url,required" json:"icon_url,required" query:"icon_url,required"`
	ConnectorOptions []*ConnectorUnionInfoOption `thrift:"connector_options,5,required" form:"connector_options,required" json:"connector_options,required" query:"connector_options,required"`
}

func NewConnectorUnionInfo() *ConnectorUnionInfo {
	return &ConnectorUnionInfo{}
}

func (p *ConnectorUnionInfo) InitDefault() {
}

func (p *ConnectorUnionInfo) GetID() (v int64) {
	return p.ID
}

func (p *ConnectorUnionInfo) GetName() (v string) {
	return p.Name
}

func (p *ConnectorUnionInfo) GetDescription() (v string) {
	return p.Description
}

func (p *ConnectorUnionInfo) GetIconURL() (v string) {
	return p.IconURL
}

func (p *ConnectorUnionInfo) GetConnectorOptions() (v []*ConnectorUnionInfoOption) {
	return p.ConnectorOptions
}

var fieldIDToName_ConnectorUnionInfo = map[int16]string{
	1: "id",
	2: "name",
	3: "description",
	4: "icon_url",
	5: "connector_options",
}

func (p *ConnectorUnionInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetName bool = false
	var issetDescription bool = false
	var issetIconURL bool = false
	var issetConnectorOptions bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescription = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetIconURL = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetConnectorOptions = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDescription {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetIconURL {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetConnectorOptions {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConnectorUnionInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ConnectorUnionInfo[fieldId]))
}

func (p *ConnectorUnionInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *ConnectorUnionInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *ConnectorUnionInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *ConnectorUnionInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IconURL = _field
	return nil
}
func (p *ConnectorUnionInfo) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ConnectorUnionInfoOption, 0, size)
	values := make([]ConnectorUnionInfoOption, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ConnectorOptions = _field
	return nil
}

func (p *ConnectorUnionInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ConnectorUnionInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConnectorUnionInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ConnectorUnionInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ConnectorUnionInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("description", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ConnectorUnionInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("icon_url", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IconURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ConnectorUnionInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_options", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ConnectorOptions)); err != nil {
		return err
	}
	for _, v := range p.ConnectorOptions {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ConnectorUnionInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConnectorUnionInfo(%+v)", *p)

}

type AuthLoginInfo struct {
	AppID        string `thrift:"app_id,1" form:"app_id" json:"app_id" query:"app_id"`
	ResponseType string `thrift:"response_type,2" form:"response_type" json:"response_type" query:"response_type"`
	AuthorizeURL string `thrift:"authorize_url,3" form:"authorize_url" json:"authorize_url" query:"authorize_url"`
	Scope        string `thrift:"scope,4" form:"scope" json:"scope" query:"scope"`
	ClientID     string `thrift:"client_id,5" form:"client_id" json:"client_id" query:"client_id"`
	Duration     string `thrift:"duration,6" form:"duration" json:"duration" query:"duration"`
	Aid          string `thrift:"aid,7" form:"aid" json:"aid" query:"aid"`
	ClientKey    string `thrift:"client_key,8" form:"client_key" json:"client_key" query:"client_key"`
}

func NewAuthLoginInfo() *AuthLoginInfo {
	return &AuthLoginInfo{}
}

func (p *AuthLoginInfo) InitDefault() {
}

func (p *AuthLoginInfo) GetAppID() (v string) {
	return p.AppID
}

func (p *AuthLoginInfo) GetResponseType() (v string) {
	return p.ResponseType
}

func (p *AuthLoginInfo) GetAuthorizeURL() (v string) {
	return p.AuthorizeURL
}

func (p *AuthLoginInfo) GetScope() (v string) {
	return p.Scope
}

func (p *AuthLoginInfo) GetClientID() (v string) {
	return p.ClientID
}

func (p *AuthLoginInfo) GetDuration() (v string) {
	return p.Duration
}

func (p *AuthLoginInfo) GetAid() (v string) {
	return p.Aid
}

func (p *AuthLoginInfo) GetClientKey() (v string) {
	return p.ClientKey
}

var fieldIDToName_AuthLoginInfo = map[int16]string{
	1: "app_id",
	2: "response_type",
	3: "authorize_url",
	4: "scope",
	5: "client_id",
	6: "duration",
	7: "aid",
	8: "client_key",
}

func (p *AuthLoginInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AuthLoginInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AuthLoginInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppID = _field
	return nil
}
func (p *AuthLoginInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ResponseType = _field
	return nil
}
func (p *AuthLoginInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AuthorizeURL = _field
	return nil
}
func (p *AuthLoginInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Scope = _field
	return nil
}
func (p *AuthLoginInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ClientID = _field
	return nil
}
func (p *AuthLoginInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Duration = _field
	return nil
}
func (p *AuthLoginInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Aid = _field
	return nil
}
func (p *AuthLoginInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ClientKey = _field
	return nil
}

func (p *AuthLoginInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("AuthLoginInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AuthLoginInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AppID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *AuthLoginInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("response_type", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ResponseType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *AuthLoginInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("authorize_url", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AuthorizeURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *AuthLoginInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("scope", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Scope); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *AuthLoginInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("client_id", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ClientID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *AuthLoginInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("duration", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Duration); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *AuthLoginInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("aid", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Aid); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *AuthLoginInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("client_key", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ClientKey); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *AuthLoginInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AuthLoginInfo(%+v)", *p)

}

type UIOption struct {
	// UIChannel Options
	UIChannel int64 `thrift:"ui_channel,1" form:"ui_channel" json:"ui_channel,string" query:"ui_channel"`
	// Is it optional
	Available bool `thrift:"available,2" form:"available" json:"available" query:"available"`
	// unselectable reason
	UnavailableReason string `thrift:"unavailable_reason,3" form:"unavailable_reason" json:"unavailable_reason" query:"unavailable_reason"`
}

func NewUIOption() *UIOption {
	return &UIOption{}
}

func (p *UIOption) InitDefault() {
}

func (p *UIOption) GetUIChannel() (v int64) {
	return p.UIChannel
}

func (p *UIOption) GetAvailable() (v bool) {
	return p.Available
}

func (p *UIOption) GetUnavailableReason() (v string) {
	return p.UnavailableReason
}

var fieldIDToName_UIOption = map[int16]string{
	1: "ui_channel",
	2: "available",
	3: "unavailable_reason",
}

func (p *UIOption) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UIOption[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UIOption) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UIChannel = _field
	return nil
}
func (p *UIOption) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Available = _field
	return nil
}
func (p *UIOption) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UnavailableReason = _field
	return nil
}

func (p *UIOption) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UIOption"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UIOption) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ui_channel", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UIChannel); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UIOption) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("available", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Available); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UIOption) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("unavailable_reason", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UnavailableReason); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *UIOption) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UIOption(%+v)", *p)

}

type ConnectorPublishConfig struct {
	// Workflow/ChatFlow selected by publishing channel
	SelectedWorkflows []*SelectedWorkflow `thrift:"selected_workflows,1" form:"selected_workflows" json:"selected_workflows" query:"selected_workflows"`
}

func NewConnectorPublishConfig() *ConnectorPublishConfig {
	return &ConnectorPublishConfig{}
}

func (p *ConnectorPublishConfig) InitDefault() {
}

func (p *ConnectorPublishConfig) GetSelectedWorkflows() (v []*SelectedWorkflow) {
	return p.SelectedWorkflows
}

var fieldIDToName_ConnectorPublishConfig = map[int16]string{
	1: "selected_workflows",
}

func (p *ConnectorPublishConfig) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConnectorPublishConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConnectorPublishConfig) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SelectedWorkflow, 0, size)
	values := make([]SelectedWorkflow, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SelectedWorkflows = _field
	return nil
}

func (p *ConnectorPublishConfig) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ConnectorPublishConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConnectorPublishConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("selected_workflows", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SelectedWorkflows)); err != nil {
		return err
	}
	for _, v := range p.SelectedWorkflows {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ConnectorPublishConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConnectorPublishConfig(%+v)", *p)

}

type ConnectorUnionInfoOption struct {
	// Channel ID
	ConnectorID int64 `thrift:"connector_id,1,required" form:"connector_id,required" json:"connector_id,string,required" query:"connector_id,required"`
	// Display name, such as: hosted release, download code
	ShowName string `thrift:"show_name,2,required" form:"show_name,required" json:"show_name,required" query:"show_name,required"`
}

func NewConnectorUnionInfoOption() *ConnectorUnionInfoOption {
	return &ConnectorUnionInfoOption{}
}

func (p *ConnectorUnionInfoOption) InitDefault() {
}

func (p *ConnectorUnionInfoOption) GetConnectorID() (v int64) {
	return p.ConnectorID
}

func (p *ConnectorUnionInfoOption) GetShowName() (v string) {
	return p.ShowName
}

var fieldIDToName_ConnectorUnionInfoOption = map[int16]string{
	1: "connector_id",
	2: "show_name",
}

func (p *ConnectorUnionInfoOption) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConnectorID bool = false
	var issetShowName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetConnectorID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetShowName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetConnectorID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetShowName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConnectorUnionInfoOption[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ConnectorUnionInfoOption[fieldId]))
}

func (p *ConnectorUnionInfoOption) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorID = _field
	return nil
}
func (p *ConnectorUnionInfoOption) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ShowName = _field
	return nil
}

func (p *ConnectorUnionInfoOption) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ConnectorUnionInfoOption"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConnectorUnionInfoOption) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ConnectorID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ConnectorUnionInfoOption) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("show_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ShowName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ConnectorUnionInfoOption) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConnectorUnionInfoOption(%+v)", *p)

}

type SelectedWorkflow struct {
	WorkflowID   int64  `thrift:"workflow_id,1" form:"workflow_id" json:"workflow_id,string" query:"workflow_id"`
	WorkflowName string `thrift:"workflow_name,2" form:"workflow_name" json:"workflow_name" query:"workflow_name"`
}

func NewSelectedWorkflow() *SelectedWorkflow {
	return &SelectedWorkflow{}
}

func (p *SelectedWorkflow) InitDefault() {
}

func (p *SelectedWorkflow) GetWorkflowID() (v int64) {
	return p.WorkflowID
}

func (p *SelectedWorkflow) GetWorkflowName() (v string) {
	return p.WorkflowName
}

var fieldIDToName_SelectedWorkflow = map[int16]string{
	1: "workflow_id",
	2: "workflow_name",
}

func (p *SelectedWorkflow) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SelectedWorkflow[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SelectedWorkflow) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkflowID = _field
	return nil
}
func (p *SelectedWorkflow) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkflowName = _field
	return nil
}

func (p *SelectedWorkflow) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SelectedWorkflow"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SelectedWorkflow) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("workflow_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.WorkflowID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SelectedWorkflow) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("workflow_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkflowName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SelectedWorkflow) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SelectedWorkflow(%+v)", *p)

}

type CheckProjectVersionNumberRequest struct {
	ProjectID     int64      `thrift:"project_id,1,required" form:"project_id,required" json:"project_id,string,required" query:"project_id,required"`
	VersionNumber string     `thrift:"version_number,2,required" form:"version_number,required" json:"version_number,required" query:"version_number,required"`
	Base          *base.Base `thrift:"Base,255,optional" form:"-" json:"-" query:"-"`
}

func NewCheckProjectVersionNumberRequest() *CheckProjectVersionNumberRequest {
	return &CheckProjectVersionNumberRequest{}
}

func (p *CheckProjectVersionNumberRequest) InitDefault() {
}

func (p *CheckProjectVersionNumberRequest) GetProjectID() (v int64) {
	return p.ProjectID
}

func (p *CheckProjectVersionNumberRequest) GetVersionNumber() (v string) {
	return p.VersionNumber
}

var CheckProjectVersionNumberRequest_Base_DEFAULT *base.Base

func (p *CheckProjectVersionNumberRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CheckProjectVersionNumberRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_CheckProjectVersionNumberRequest = map[int16]string{
	1:   "project_id",
	2:   "version_number",
	255: "Base",
}

func (p *CheckProjectVersionNumberRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *CheckProjectVersionNumberRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProjectID bool = false
	var issetVersionNumber bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjectID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetVersionNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetProjectID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetVersionNumber {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckProjectVersionNumberRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CheckProjectVersionNumberRequest[fieldId]))
}

func (p *CheckProjectVersionNumberRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *CheckProjectVersionNumberRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VersionNumber = _field
	return nil
}
func (p *CheckProjectVersionNumberRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CheckProjectVersionNumberRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CheckProjectVersionNumberRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckProjectVersionNumberRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("project_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CheckProjectVersionNumberRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version_number", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VersionNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CheckProjectVersionNumberRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CheckProjectVersionNumberRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckProjectVersionNumberRequest(%+v)", *p)

}

type CheckProjectVersionNumberResponse struct {
	Data     *CheckProjectVersionNumberData `thrift:"data,1" form:"data" json:"data" query:"data"`
	Code     int64                          `thrift:"code,253" form:"code" json:"code" query:"code"`
	Msg      string                         `thrift:"msg,254" form:"msg" json:"msg" query:"msg"`
	BaseResp *base.BaseResp                 `thrift:"BaseResp,255,optional" form:"-" json:"-" query:"-"`
}

func NewCheckProjectVersionNumberResponse() *CheckProjectVersionNumberResponse {
	return &CheckProjectVersionNumberResponse{}
}

func (p *CheckProjectVersionNumberResponse) InitDefault() {
}

var CheckProjectVersionNumberResponse_Data_DEFAULT *CheckProjectVersionNumberData

func (p *CheckProjectVersionNumberResponse) GetData() (v *CheckProjectVersionNumberData) {
	if !p.IsSetData() {
		return CheckProjectVersionNumberResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *CheckProjectVersionNumberResponse) GetCode() (v int64) {
	return p.Code
}

func (p *CheckProjectVersionNumberResponse) GetMsg() (v string) {
	return p.Msg
}

var CheckProjectVersionNumberResponse_BaseResp_DEFAULT *base.BaseResp

func (p *CheckProjectVersionNumberResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CheckProjectVersionNumberResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_CheckProjectVersionNumberResponse = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *CheckProjectVersionNumberResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *CheckProjectVersionNumberResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CheckProjectVersionNumberResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckProjectVersionNumberResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CheckProjectVersionNumberResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCheckProjectVersionNumberData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *CheckProjectVersionNumberResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *CheckProjectVersionNumberResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *CheckProjectVersionNumberResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CheckProjectVersionNumberResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CheckProjectVersionNumberResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckProjectVersionNumberResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CheckProjectVersionNumberResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *CheckProjectVersionNumberResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *CheckProjectVersionNumberResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CheckProjectVersionNumberResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckProjectVersionNumberResponse(%+v)", *p)

}

type CheckProjectVersionNumberData struct {
	IsDuplicate bool `thrift:"is_duplicate,1" form:"is_duplicate" json:"is_duplicate" query:"is_duplicate"`
}

func NewCheckProjectVersionNumberData() *CheckProjectVersionNumberData {
	return &CheckProjectVersionNumberData{}
}

func (p *CheckProjectVersionNumberData) InitDefault() {
}

func (p *CheckProjectVersionNumberData) GetIsDuplicate() (v bool) {
	return p.IsDuplicate
}

var fieldIDToName_CheckProjectVersionNumberData = map[int16]string{
	1: "is_duplicate",
}

func (p *CheckProjectVersionNumberData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckProjectVersionNumberData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CheckProjectVersionNumberData) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsDuplicate = _field
	return nil
}

func (p *CheckProjectVersionNumberData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CheckProjectVersionNumberData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckProjectVersionNumberData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("is_duplicate", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsDuplicate); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CheckProjectVersionNumberData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckProjectVersionNumberData(%+v)", *p)

}

type PublishProjectRequest struct {
	ProjectID int64 `thrift:"project_id,1,required" form:"project_id,required" json:"project_id,string,required" query:"project_id,required"`
	// version number
	VersionNumber string `thrift:"version_number,2,required" form:"version_number,required" json:"version_number,required" query:"version_number,required"`
	// describe
	Description *string `thrift:"description,3,optional" form:"description" json:"description,omitempty" query:"description"`
	// The key represents connector_id, and the value is the parameter published by the channel
	Connectors map[int64]map[string]string `thrift:"connectors,4,optional" form:"connectors" json:"connectors,omitempty" query:"connectors"`
	// Channel release configuration, key represents connector_id
	ConnectorPublishConfig map[int64]*ConnectorPublishConfig `thrift:"connector_publish_config,5,optional" form:"connector_publish_config" json:"connector_publish_config,omitempty" query:"connector_publish_config"`
	Base                   *base.Base                        `thrift:"Base,255,optional" form:"-" json:"-" query:"-"`
}

func NewPublishProjectRequest() *PublishProjectRequest {
	return &PublishProjectRequest{}
}

func (p *PublishProjectRequest) InitDefault() {
}

func (p *PublishProjectRequest) GetProjectID() (v int64) {
	return p.ProjectID
}

func (p *PublishProjectRequest) GetVersionNumber() (v string) {
	return p.VersionNumber
}

var PublishProjectRequest_Description_DEFAULT string

func (p *PublishProjectRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return PublishProjectRequest_Description_DEFAULT
	}
	return *p.Description
}

var PublishProjectRequest_Connectors_DEFAULT map[int64]map[string]string

func (p *PublishProjectRequest) GetConnectors() (v map[int64]map[string]string) {
	if !p.IsSetConnectors() {
		return PublishProjectRequest_Connectors_DEFAULT
	}
	return p.Connectors
}

var PublishProjectRequest_ConnectorPublishConfig_DEFAULT map[int64]*ConnectorPublishConfig

func (p *PublishProjectRequest) GetConnectorPublishConfig() (v map[int64]*ConnectorPublishConfig) {
	if !p.IsSetConnectorPublishConfig() {
		return PublishProjectRequest_ConnectorPublishConfig_DEFAULT
	}
	return p.ConnectorPublishConfig
}

var PublishProjectRequest_Base_DEFAULT *base.Base

func (p *PublishProjectRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return PublishProjectRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_PublishProjectRequest = map[int16]string{
	1:   "project_id",
	2:   "version_number",
	3:   "description",
	4:   "connectors",
	5:   "connector_publish_config",
	255: "Base",
}

func (p *PublishProjectRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *PublishProjectRequest) IsSetConnectors() bool {
	return p.Connectors != nil
}

func (p *PublishProjectRequest) IsSetConnectorPublishConfig() bool {
	return p.ConnectorPublishConfig != nil
}

func (p *PublishProjectRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *PublishProjectRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProjectID bool = false
	var issetVersionNumber bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjectID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetVersionNumber = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetProjectID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetVersionNumber {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PublishProjectRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PublishProjectRequest[fieldId]))
}

func (p *PublishProjectRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *PublishProjectRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VersionNumber = _field
	return nil
}
func (p *PublishProjectRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *PublishProjectRequest) ReadField4(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[int64]map[string]string, size)
	for i := 0; i < size; i++ {
		var _key int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_key = v
		}
		_, _, size, err := iprot.ReadMapBegin()
		if err != nil {
			return err
		}
		_val := make(map[string]string, size)
		for i := 0; i < size; i++ {
			var _key1 string
			if v, err := iprot.ReadString(); err != nil {
				return err
			} else {
				_key1 = v
			}

			var _val1 string
			if v, err := iprot.ReadString(); err != nil {
				return err
			} else {
				_val1 = v
			}

			_val[_key1] = _val1
		}
		if err := iprot.ReadMapEnd(); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Connectors = _field
	return nil
}
func (p *PublishProjectRequest) ReadField5(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[int64]*ConnectorPublishConfig, size)
	values := make([]ConnectorPublishConfig, size)
	for i := 0; i < size; i++ {
		var _key int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_key = v
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.ConnectorPublishConfig = _field
	return nil
}
func (p *PublishProjectRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *PublishProjectRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PublishProjectRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PublishProjectRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("project_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PublishProjectRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version_number", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VersionNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PublishProjectRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("description", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PublishProjectRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetConnectors() {
		if err = oprot.WriteFieldBegin("connectors", thrift.MAP, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.I64, thrift.MAP, len(p.Connectors)); err != nil {
			return err
		}
		for k, v := range p.Connectors {
			if err := oprot.WriteI64(k); err != nil {
				return err
			}
			if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(v)); err != nil {
				return err
			}
			for k, v := range v {
				if err := oprot.WriteString(k); err != nil {
					return err
				}
				if err := oprot.WriteString(v); err != nil {
					return err
				}
			}
			if err := oprot.WriteMapEnd(); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *PublishProjectRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetConnectorPublishConfig() {
		if err = oprot.WriteFieldBegin("connector_publish_config", thrift.MAP, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.I64, thrift.STRUCT, len(p.ConnectorPublishConfig)); err != nil {
			return err
		}
		for k, v := range p.ConnectorPublishConfig {
			if err := oprot.WriteI64(k); err != nil {
				return err
			}
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *PublishProjectRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *PublishProjectRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PublishProjectRequest(%+v)", *p)

}

type PublishProjectResponse struct {
	Data     *PublishProjectData `thrift:"data,1" form:"data" json:"data" query:"data"`
	Code     int64               `thrift:"code,253" form:"code" json:"code" query:"code"`
	Msg      string              `thrift:"msg,254" form:"msg" json:"msg" query:"msg"`
	BaseResp *base.BaseResp      `thrift:"BaseResp,255,optional" form:"-" json:"-" query:"-"`
}

func NewPublishProjectResponse() *PublishProjectResponse {
	return &PublishProjectResponse{}
}

func (p *PublishProjectResponse) InitDefault() {
}

var PublishProjectResponse_Data_DEFAULT *PublishProjectData

func (p *PublishProjectResponse) GetData() (v *PublishProjectData) {
	if !p.IsSetData() {
		return PublishProjectResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *PublishProjectResponse) GetCode() (v int64) {
	return p.Code
}

func (p *PublishProjectResponse) GetMsg() (v string) {
	return p.Msg
}

var PublishProjectResponse_BaseResp_DEFAULT *base.BaseResp

func (p *PublishProjectResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return PublishProjectResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_PublishProjectResponse = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *PublishProjectResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *PublishProjectResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *PublishProjectResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PublishProjectResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PublishProjectResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPublishProjectData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *PublishProjectResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *PublishProjectResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *PublishProjectResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *PublishProjectResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PublishProjectResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PublishProjectResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PublishProjectResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *PublishProjectResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *PublishProjectResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *PublishProjectResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PublishProjectResponse(%+v)", *p)

}

type PublishProjectData struct {
	// Publish record ID for front-end polling
	PublishRecordID int64 `thrift:"publish_record_id,1" form:"publish_record_id" json:"publish_record_id,string" query:"publish_record_id"`
	// The charging configuration is released, and the overseas environment is only available.
	PublishMonetizationResult *bool `thrift:"publish_monetization_result,2,optional" form:"publish_monetization_result" json:"publish_monetization_result,omitempty" query:"publish_monetization_result"`
}

func NewPublishProjectData() *PublishProjectData {
	return &PublishProjectData{}
}

func (p *PublishProjectData) InitDefault() {
}

func (p *PublishProjectData) GetPublishRecordID() (v int64) {
	return p.PublishRecordID
}

var PublishProjectData_PublishMonetizationResult_DEFAULT bool

func (p *PublishProjectData) GetPublishMonetizationResult() (v bool) {
	if !p.IsSetPublishMonetizationResult() {
		return PublishProjectData_PublishMonetizationResult_DEFAULT
	}
	return *p.PublishMonetizationResult
}

var fieldIDToName_PublishProjectData = map[int16]string{
	1: "publish_record_id",
	2: "publish_monetization_result",
}

func (p *PublishProjectData) IsSetPublishMonetizationResult() bool {
	return p.PublishMonetizationResult != nil
}

func (p *PublishProjectData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PublishProjectData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PublishProjectData) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PublishRecordID = _field
	return nil
}
func (p *PublishProjectData) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PublishMonetizationResult = _field
	return nil
}

func (p *PublishProjectData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PublishProjectData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PublishProjectData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("publish_record_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.PublishRecordID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PublishProjectData) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPublishMonetizationResult() {
		if err = oprot.WriteFieldBegin("publish_monetization_result", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.PublishMonetizationResult); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *PublishProjectData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PublishProjectData(%+v)", *p)

}

type GetPublishRecordListRequest struct {
	ProjectID int64      `thrift:"project_id,1,required" form:"project_id,required" json:"project_id,string,required" query:"project_id,required"`
	Base      *base.Base `thrift:"Base,255,optional" form:"-" json:"-" query:"-"`
}

func NewGetPublishRecordListRequest() *GetPublishRecordListRequest {
	return &GetPublishRecordListRequest{}
}

func (p *GetPublishRecordListRequest) InitDefault() {
}

func (p *GetPublishRecordListRequest) GetProjectID() (v int64) {
	return p.ProjectID
}

var GetPublishRecordListRequest_Base_DEFAULT *base.Base

func (p *GetPublishRecordListRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetPublishRecordListRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_GetPublishRecordListRequest = map[int16]string{
	1:   "project_id",
	255: "Base",
}

func (p *GetPublishRecordListRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetPublishRecordListRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProjectID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjectID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetProjectID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetPublishRecordListRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetPublishRecordListRequest[fieldId]))
}

func (p *GetPublishRecordListRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *GetPublishRecordListRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetPublishRecordListRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetPublishRecordListRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetPublishRecordListRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("project_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetPublishRecordListRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetPublishRecordListRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPublishRecordListRequest(%+v)", *p)

}

type GetPublishRecordListResponse struct {
	Data     []*PublishRecordDetail `thrift:"data,1" form:"data" json:"data" query:"data"`
	Code     int64                  `thrift:"code,253" form:"code" json:"code" query:"code"`
	Msg      string                 `thrift:"msg,254" form:"msg" json:"msg" query:"msg"`
	BaseResp *base.BaseResp         `thrift:"BaseResp,255,optional" form:"-" json:"-" query:"-"`
}

func NewGetPublishRecordListResponse() *GetPublishRecordListResponse {
	return &GetPublishRecordListResponse{}
}

func (p *GetPublishRecordListResponse) InitDefault() {
}

func (p *GetPublishRecordListResponse) GetData() (v []*PublishRecordDetail) {
	return p.Data
}

func (p *GetPublishRecordListResponse) GetCode() (v int64) {
	return p.Code
}

func (p *GetPublishRecordListResponse) GetMsg() (v string) {
	return p.Msg
}

var GetPublishRecordListResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetPublishRecordListResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetPublishRecordListResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_GetPublishRecordListResponse = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *GetPublishRecordListResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetPublishRecordListResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetPublishRecordListResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetPublishRecordListResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PublishRecordDetail, 0, size)
	values := make([]PublishRecordDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *GetPublishRecordListResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *GetPublishRecordListResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *GetPublishRecordListResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetPublishRecordListResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetPublishRecordListResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetPublishRecordListResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Data)); err != nil {
		return err
	}
	for _, v := range p.Data {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetPublishRecordListResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *GetPublishRecordListResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *GetPublishRecordListResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetPublishRecordListResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPublishRecordListResponse(%+v)", *p)

}

type PublishRecordDetail struct {
	PublishRecordID int64  `thrift:"publish_record_id,1" form:"publish_record_id" json:"publish_record_id,string" query:"publish_record_id"`
	VersionNumber   string `thrift:"version_number,2" form:"version_number" json:"version_number" query:"version_number"`
	// release status
	PublishStatus PublishRecordStatus `thrift:"publish_status,3" form:"publish_status" json:"publish_status" query:"publish_status"`
	// This field is deprecated, please use publish_status_detail
	PublishStatusMsg string `thrift:"publish_status_msg,4" form:"publish_status_msg" json:"publish_status_msg" query:"publish_status_msg"`
	// Channel release results
	ConnectorPublishResult []*ConnectorPublishResult `thrift:"connector_publish_result,5,optional" form:"connector_publish_result" json:"connector_publish_result,omitempty" query:"connector_publish_result"`
	// Release status Supplementary information
	PublishStatusDetail *PublishRecordStatusDetail `thrift:"publish_status_detail,6,optional" form:"publish_status_detail" json:"publish_status_detail,omitempty" query:"publish_status_detail"`
}

func NewPublishRecordDetail() *PublishRecordDetail {
	return &PublishRecordDetail{}
}

func (p *PublishRecordDetail) InitDefault() {
}

func (p *PublishRecordDetail) GetPublishRecordID() (v int64) {
	return p.PublishRecordID
}

func (p *PublishRecordDetail) GetVersionNumber() (v string) {
	return p.VersionNumber
}

func (p *PublishRecordDetail) GetPublishStatus() (v PublishRecordStatus) {
	return p.PublishStatus
}

func (p *PublishRecordDetail) GetPublishStatusMsg() (v string) {
	return p.PublishStatusMsg
}

var PublishRecordDetail_ConnectorPublishResult_DEFAULT []*ConnectorPublishResult

func (p *PublishRecordDetail) GetConnectorPublishResult() (v []*ConnectorPublishResult) {
	if !p.IsSetConnectorPublishResult() {
		return PublishRecordDetail_ConnectorPublishResult_DEFAULT
	}
	return p.ConnectorPublishResult
}

var PublishRecordDetail_PublishStatusDetail_DEFAULT *PublishRecordStatusDetail

func (p *PublishRecordDetail) GetPublishStatusDetail() (v *PublishRecordStatusDetail) {
	if !p.IsSetPublishStatusDetail() {
		return PublishRecordDetail_PublishStatusDetail_DEFAULT
	}
	return p.PublishStatusDetail
}

var fieldIDToName_PublishRecordDetail = map[int16]string{
	1: "publish_record_id",
	2: "version_number",
	3: "publish_status",
	4: "publish_status_msg",
	5: "connector_publish_result",
	6: "publish_status_detail",
}

func (p *PublishRecordDetail) IsSetConnectorPublishResult() bool {
	return p.ConnectorPublishResult != nil
}

func (p *PublishRecordDetail) IsSetPublishStatusDetail() bool {
	return p.PublishStatusDetail != nil
}

func (p *PublishRecordDetail) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PublishRecordDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PublishRecordDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PublishRecordID = _field
	return nil
}
func (p *PublishRecordDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VersionNumber = _field
	return nil
}
func (p *PublishRecordDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field PublishRecordStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = PublishRecordStatus(v)
	}
	p.PublishStatus = _field
	return nil
}
func (p *PublishRecordDetail) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PublishStatusMsg = _field
	return nil
}
func (p *PublishRecordDetail) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ConnectorPublishResult, 0, size)
	values := make([]ConnectorPublishResult, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ConnectorPublishResult = _field
	return nil
}
func (p *PublishRecordDetail) ReadField6(iprot thrift.TProtocol) error {
	_field := NewPublishRecordStatusDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PublishStatusDetail = _field
	return nil
}

func (p *PublishRecordDetail) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PublishRecordDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PublishRecordDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("publish_record_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.PublishRecordID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PublishRecordDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version_number", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VersionNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PublishRecordDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("publish_status", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.PublishStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PublishRecordDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("publish_status_msg", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PublishStatusMsg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *PublishRecordDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetConnectorPublishResult() {
		if err = oprot.WriteFieldBegin("connector_publish_result", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ConnectorPublishResult)); err != nil {
			return err
		}
		for _, v := range p.ConnectorPublishResult {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *PublishRecordDetail) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPublishStatusDetail() {
		if err = oprot.WriteFieldBegin("publish_status_detail", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.PublishStatusDetail.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *PublishRecordDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PublishRecordDetail(%+v)", *p)

}

type ConnectorPublishResult struct {
	ConnectorID      int64  `thrift:"connector_id,1" form:"connector_id" json:"connector_id,string" query:"connector_id"`
	ConnectorName    string `thrift:"connector_name,2" form:"connector_name" json:"connector_name" query:"connector_name"`
	ConnectorIconURL string `thrift:"connector_icon_url,3" form:"connector_icon_url" json:"connector_icon_url" query:"connector_icon_url"`
	// channel release status
	ConnectorPublishStatus ConnectorPublishStatus `thrift:"connector_publish_status,4" form:"connector_publish_status" json:"connector_publish_status" query:"connector_publish_status"`
	// Channel Release Status Supplementary Information
	ConnectorPublishStatusMsg string `thrift:"connector_publish_status_msg,5" form:"connector_publish_status_msg" json:"connector_publish_status_msg" query:"connector_publish_status_msg"`
	// OpenIn Link
	ShareLink *string `thrift:"share_link,6,optional" form:"share_link" json:"share_link,omitempty" query:"share_link"`
	// Mini Program Channel Download Link
	DownloadLink *string `thrift:"download_link,7,optional" form:"download_link" json:"download_link,omitempty" query:"download_link"`
	// channel release configuration
	ConnectorPublishConfig *ConnectorPublishConfig `thrift:"connector_publish_config,8,optional" form:"connector_publish_config" json:"connector_publish_config,omitempty" query:"connector_publish_config"`
	// Channel binding information key field name value is value
	ConnectorBindInfo map[string]string `thrift:"connector_bind_info,9,optional" form:"connector_bind_info" json:"connector_bind_info,omitempty" query:"connector_bind_info"`
}

func NewConnectorPublishResult() *ConnectorPublishResult {
	return &ConnectorPublishResult{}
}

func (p *ConnectorPublishResult) InitDefault() {
}

func (p *ConnectorPublishResult) GetConnectorID() (v int64) {
	return p.ConnectorID
}

func (p *ConnectorPublishResult) GetConnectorName() (v string) {
	return p.ConnectorName
}

func (p *ConnectorPublishResult) GetConnectorIconURL() (v string) {
	return p.ConnectorIconURL
}

func (p *ConnectorPublishResult) GetConnectorPublishStatus() (v ConnectorPublishStatus) {
	return p.ConnectorPublishStatus
}

func (p *ConnectorPublishResult) GetConnectorPublishStatusMsg() (v string) {
	return p.ConnectorPublishStatusMsg
}

var ConnectorPublishResult_ShareLink_DEFAULT string

func (p *ConnectorPublishResult) GetShareLink() (v string) {
	if !p.IsSetShareLink() {
		return ConnectorPublishResult_ShareLink_DEFAULT
	}
	return *p.ShareLink
}

var ConnectorPublishResult_DownloadLink_DEFAULT string

func (p *ConnectorPublishResult) GetDownloadLink() (v string) {
	if !p.IsSetDownloadLink() {
		return ConnectorPublishResult_DownloadLink_DEFAULT
	}
	return *p.DownloadLink
}

var ConnectorPublishResult_ConnectorPublishConfig_DEFAULT *ConnectorPublishConfig

func (p *ConnectorPublishResult) GetConnectorPublishConfig() (v *ConnectorPublishConfig) {
	if !p.IsSetConnectorPublishConfig() {
		return ConnectorPublishResult_ConnectorPublishConfig_DEFAULT
	}
	return p.ConnectorPublishConfig
}

var ConnectorPublishResult_ConnectorBindInfo_DEFAULT map[string]string

func (p *ConnectorPublishResult) GetConnectorBindInfo() (v map[string]string) {
	if !p.IsSetConnectorBindInfo() {
		return ConnectorPublishResult_ConnectorBindInfo_DEFAULT
	}
	return p.ConnectorBindInfo
}

var fieldIDToName_ConnectorPublishResult = map[int16]string{
	1: "connector_id",
	2: "connector_name",
	3: "connector_icon_url",
	4: "connector_publish_status",
	5: "connector_publish_status_msg",
	6: "share_link",
	7: "download_link",
	8: "connector_publish_config",
	9: "connector_bind_info",
}

func (p *ConnectorPublishResult) IsSetShareLink() bool {
	return p.ShareLink != nil
}

func (p *ConnectorPublishResult) IsSetDownloadLink() bool {
	return p.DownloadLink != nil
}

func (p *ConnectorPublishResult) IsSetConnectorPublishConfig() bool {
	return p.ConnectorPublishConfig != nil
}

func (p *ConnectorPublishResult) IsSetConnectorBindInfo() bool {
	return p.ConnectorBindInfo != nil
}

func (p *ConnectorPublishResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConnectorPublishResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConnectorPublishResult) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorID = _field
	return nil
}
func (p *ConnectorPublishResult) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorName = _field
	return nil
}
func (p *ConnectorPublishResult) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorIconURL = _field
	return nil
}
func (p *ConnectorPublishResult) ReadField4(iprot thrift.TProtocol) error {

	var _field ConnectorPublishStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ConnectorPublishStatus(v)
	}
	p.ConnectorPublishStatus = _field
	return nil
}
func (p *ConnectorPublishResult) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorPublishStatusMsg = _field
	return nil
}
func (p *ConnectorPublishResult) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShareLink = _field
	return nil
}
func (p *ConnectorPublishResult) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DownloadLink = _field
	return nil
}
func (p *ConnectorPublishResult) ReadField8(iprot thrift.TProtocol) error {
	_field := NewConnectorPublishConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ConnectorPublishConfig = _field
	return nil
}
func (p *ConnectorPublishResult) ReadField9(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.ConnectorBindInfo = _field
	return nil
}

func (p *ConnectorPublishResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ConnectorPublishResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConnectorPublishResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ConnectorID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ConnectorPublishResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConnectorName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ConnectorPublishResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_icon_url", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConnectorIconURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ConnectorPublishResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_publish_status", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ConnectorPublishStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ConnectorPublishResult) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_publish_status_msg", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConnectorPublishStatusMsg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ConnectorPublishResult) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetShareLink() {
		if err = oprot.WriteFieldBegin("share_link", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShareLink); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ConnectorPublishResult) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetDownloadLink() {
		if err = oprot.WriteFieldBegin("download_link", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DownloadLink); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ConnectorPublishResult) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetConnectorPublishConfig() {
		if err = oprot.WriteFieldBegin("connector_publish_config", thrift.STRUCT, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ConnectorPublishConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *ConnectorPublishResult) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetConnectorBindInfo() {
		if err = oprot.WriteFieldBegin("connector_bind_info", thrift.MAP, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ConnectorBindInfo)); err != nil {
			return err
		}
		for k, v := range p.ConnectorBindInfo {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ConnectorPublishResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConnectorPublishResult(%+v)", *p)

}

type PublishRecordStatusDetail struct {
	// Packaging failure details
	PackFailedDetail []*PackFailedDetail `thrift:"pack_failed_detail,1,optional" form:"pack_failed_detail" json:"pack_failed_detail,omitempty" query:"pack_failed_detail"`
}

func NewPublishRecordStatusDetail() *PublishRecordStatusDetail {
	return &PublishRecordStatusDetail{}
}

func (p *PublishRecordStatusDetail) InitDefault() {
}

var PublishRecordStatusDetail_PackFailedDetail_DEFAULT []*PackFailedDetail

func (p *PublishRecordStatusDetail) GetPackFailedDetail() (v []*PackFailedDetail) {
	if !p.IsSetPackFailedDetail() {
		return PublishRecordStatusDetail_PackFailedDetail_DEFAULT
	}
	return p.PackFailedDetail
}

var fieldIDToName_PublishRecordStatusDetail = map[int16]string{
	1: "pack_failed_detail",
}

func (p *PublishRecordStatusDetail) IsSetPackFailedDetail() bool {
	return p.PackFailedDetail != nil
}

func (p *PublishRecordStatusDetail) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PublishRecordStatusDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PublishRecordStatusDetail) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PackFailedDetail, 0, size)
	values := make([]PackFailedDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PackFailedDetail = _field
	return nil
}

func (p *PublishRecordStatusDetail) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PublishRecordStatusDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PublishRecordStatusDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPackFailedDetail() {
		if err = oprot.WriteFieldBegin("pack_failed_detail", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PackFailedDetail)); err != nil {
			return err
		}
		for _, v := range p.PackFailedDetail {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PublishRecordStatusDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PublishRecordStatusDetail(%+v)", *p)

}

type PackFailedDetail struct {
	EntityID   int64               `thrift:"entity_id,1" form:"entity_id" json:"entity_id,string" query:"entity_id"`
	EntityType common.ResourceType `thrift:"entity_type,2" form:"entity_type" json:"entity_type" query:"entity_type"`
	EntityName string              `thrift:"entity_name,3" form:"entity_name" json:"entity_name" query:"entity_name"`
}

func NewPackFailedDetail() *PackFailedDetail {
	return &PackFailedDetail{}
}

func (p *PackFailedDetail) InitDefault() {
}

func (p *PackFailedDetail) GetEntityID() (v int64) {
	return p.EntityID
}

func (p *PackFailedDetail) GetEntityType() (v common.ResourceType) {
	return p.EntityType
}

func (p *PackFailedDetail) GetEntityName() (v string) {
	return p.EntityName
}

var fieldIDToName_PackFailedDetail = map[int16]string{
	1: "entity_id",
	2: "entity_type",
	3: "entity_name",
}

func (p *PackFailedDetail) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PackFailedDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PackFailedDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EntityID = _field
	return nil
}
func (p *PackFailedDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field common.ResourceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = common.ResourceType(v)
	}
	p.EntityType = _field
	return nil
}
func (p *PackFailedDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EntityName = _field
	return nil
}

func (p *PackFailedDetail) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PackFailedDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PackFailedDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("entity_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.EntityID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PackFailedDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("entity_type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EntityType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PackFailedDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("entity_name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EntityName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *PackFailedDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PackFailedDetail(%+v)", *p)

}

type GetPublishRecordDetailRequest struct {
	ProjectID int64 `thrift:"project_id,1,required" form:"project_id,required" json:"project_id,string,required" query:"project_id,required"`
	// If you don't upload it, get the last release record.
	PublishRecordID *int64     `thrift:"publish_record_id,2,optional" form:"publish_record_id" json:"publish_record_id,string,omitempty" query:"publish_record_id"`
	Base            *base.Base `thrift:"Base,255,optional" form:"-" json:"-" query:"-"`
}

func NewGetPublishRecordDetailRequest() *GetPublishRecordDetailRequest {
	return &GetPublishRecordDetailRequest{}
}

func (p *GetPublishRecordDetailRequest) InitDefault() {
}

func (p *GetPublishRecordDetailRequest) GetProjectID() (v int64) {
	return p.ProjectID
}

var GetPublishRecordDetailRequest_PublishRecordID_DEFAULT int64

func (p *GetPublishRecordDetailRequest) GetPublishRecordID() (v int64) {
	if !p.IsSetPublishRecordID() {
		return GetPublishRecordDetailRequest_PublishRecordID_DEFAULT
	}
	return *p.PublishRecordID
}

var GetPublishRecordDetailRequest_Base_DEFAULT *base.Base

func (p *GetPublishRecordDetailRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetPublishRecordDetailRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_GetPublishRecordDetailRequest = map[int16]string{
	1:   "project_id",
	2:   "publish_record_id",
	255: "Base",
}

func (p *GetPublishRecordDetailRequest) IsSetPublishRecordID() bool {
	return p.PublishRecordID != nil
}

func (p *GetPublishRecordDetailRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetPublishRecordDetailRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProjectID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjectID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetProjectID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetPublishRecordDetailRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetPublishRecordDetailRequest[fieldId]))
}

func (p *GetPublishRecordDetailRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *GetPublishRecordDetailRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PublishRecordID = _field
	return nil
}
func (p *GetPublishRecordDetailRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetPublishRecordDetailRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetPublishRecordDetailRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetPublishRecordDetailRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("project_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetPublishRecordDetailRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPublishRecordID() {
		if err = oprot.WriteFieldBegin("publish_record_id", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.PublishRecordID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetPublishRecordDetailRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetPublishRecordDetailRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPublishRecordDetailRequest(%+v)", *p)

}

type GetPublishRecordDetailResponse struct {
	Data     *PublishRecordDetail `thrift:"data,1" form:"data" json:"data" query:"data"`
	Code     int64                `thrift:"code,253" form:"code" json:"code" query:"code"`
	Msg      string               `thrift:"msg,254" form:"msg" json:"msg" query:"msg"`
	BaseResp *base.BaseResp       `thrift:"BaseResp,255,optional" form:"-" json:"-" query:"-"`
}

func NewGetPublishRecordDetailResponse() *GetPublishRecordDetailResponse {
	return &GetPublishRecordDetailResponse{}
}

func (p *GetPublishRecordDetailResponse) InitDefault() {
}

var GetPublishRecordDetailResponse_Data_DEFAULT *PublishRecordDetail

func (p *GetPublishRecordDetailResponse) GetData() (v *PublishRecordDetail) {
	if !p.IsSetData() {
		return GetPublishRecordDetailResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *GetPublishRecordDetailResponse) GetCode() (v int64) {
	return p.Code
}

func (p *GetPublishRecordDetailResponse) GetMsg() (v string) {
	return p.Msg
}

var GetPublishRecordDetailResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetPublishRecordDetailResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetPublishRecordDetailResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_GetPublishRecordDetailResponse = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *GetPublishRecordDetailResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *GetPublishRecordDetailResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetPublishRecordDetailResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetPublishRecordDetailResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetPublishRecordDetailResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPublishRecordDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *GetPublishRecordDetailResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *GetPublishRecordDetailResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *GetPublishRecordDetailResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetPublishRecordDetailResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetPublishRecordDetailResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetPublishRecordDetailResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetPublishRecordDetailResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *GetPublishRecordDetailResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *GetPublishRecordDetailResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetPublishRecordDetailResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPublishRecordDetailResponse(%+v)", *p)

}
