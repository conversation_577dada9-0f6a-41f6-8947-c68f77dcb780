// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package common

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

type ResType int64

const (
	ResType_Plugin    ResType = 1
	ResType_Workflow  ResType = 2
	ResType_Imageflow ResType = 3
	ResType_Knowledge ResType = 4
	ResType_UI        ResType = 5
	ResType_Prompt    ResType = 6
	ResType_Database  ResType = 7
	ResType_Variable  ResType = 8
	ResType_Voice     ResType = 9
)

func (p ResType) String() string {
	switch p {
	case ResType_Plugin:
		return "Plugin"
	case ResType_Workflow:
		return "Workflow"
	case ResType_Imageflow:
		return "Imageflow"
	case ResType_Knowledge:
		return "Knowledge"
	case ResType_UI:
		return "UI"
	case ResType_Prompt:
		return "Prompt"
	case ResType_Database:
		return "Database"
	case ResType_Variable:
		return "Variable"
	case ResType_Voice:
		return "Voice"
	}
	return "<UNSET>"
}

func ResTypeFromString(s string) (ResType, error) {
	switch s {
	case "Plugin":
		return ResType_Plugin, nil
	case "Workflow":
		return ResType_Workflow, nil
	case "Imageflow":
		return ResType_Imageflow, nil
	case "Knowledge":
		return ResType_Knowledge, nil
	case "UI":
		return ResType_UI, nil
	case "Prompt":
		return ResType_Prompt, nil
	case "Database":
		return ResType_Database, nil
	case "Variable":
		return ResType_Variable, nil
	case "Voice":
		return ResType_Voice, nil
	}
	return ResType(0), fmt.Errorf("not a valid ResType string")
}

func ResTypePtr(v ResType) *ResType { return &v }
func (p *ResType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ResType(result.Int64)
	return
}

func (p *ResType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PublishStatus int64

const (
	// unpublished
	PublishStatus_UnPublished PublishStatus = 1
	// Published
	PublishStatus_Published PublishStatus = 2
)

func (p PublishStatus) String() string {
	switch p {
	case PublishStatus_UnPublished:
		return "UnPublished"
	case PublishStatus_Published:
		return "Published"
	}
	return "<UNSET>"
}

func PublishStatusFromString(s string) (PublishStatus, error) {
	switch s {
	case "UnPublished":
		return PublishStatus_UnPublished, nil
	case "Published":
		return PublishStatus_Published, nil
	}
	return PublishStatus(0), fmt.Errorf("not a valid PublishStatus string")
}

func PublishStatusPtr(v PublishStatus) *PublishStatus { return &v }
func (p *PublishStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PublishStatus(result.Int64)
	return
}

func (p *PublishStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ActionKey int64

const (
	// copy
	ActionKey_Copy ActionKey = 1
	// delete
	ActionKey_Delete ActionKey = 2
	// enable/disable
	ActionKey_EnableSwitch ActionKey = 3
	// edit
	ActionKey_Edit ActionKey = 4
	// Switch to funcflow
	ActionKey_SwitchToFuncflow ActionKey = 8
	// Switch to chatflow
	ActionKey_SwitchToChatflow ActionKey = 9
	// Cross-space copy
	ActionKey_CrossSpaceCopy ActionKey = 10
)

func (p ActionKey) String() string {
	switch p {
	case ActionKey_Copy:
		return "Copy"
	case ActionKey_Delete:
		return "Delete"
	case ActionKey_EnableSwitch:
		return "EnableSwitch"
	case ActionKey_Edit:
		return "Edit"
	case ActionKey_SwitchToFuncflow:
		return "SwitchToFuncflow"
	case ActionKey_SwitchToChatflow:
		return "SwitchToChatflow"
	case ActionKey_CrossSpaceCopy:
		return "CrossSpaceCopy"
	}
	return "<UNSET>"
}

func ActionKeyFromString(s string) (ActionKey, error) {
	switch s {
	case "Copy":
		return ActionKey_Copy, nil
	case "Delete":
		return ActionKey_Delete, nil
	case "EnableSwitch":
		return ActionKey_EnableSwitch, nil
	case "Edit":
		return ActionKey_Edit, nil
	case "SwitchToFuncflow":
		return ActionKey_SwitchToFuncflow, nil
	case "SwitchToChatflow":
		return ActionKey_SwitchToChatflow, nil
	case "CrossSpaceCopy":
		return ActionKey_CrossSpaceCopy, nil
	}
	return ActionKey(0), fmt.Errorf("not a valid ActionKey string")
}

func ActionKeyPtr(v ActionKey) *ActionKey { return &v }
func (p *ActionKey) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ActionKey(result.Int64)
	return
}

func (p *ActionKey) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ProjectResourceActionKey int64

const (
	//rename
	ProjectResourceActionKey_Rename ProjectResourceActionKey = 1
	//Create a copy/copy to the current project
	ProjectResourceActionKey_Copy ProjectResourceActionKey = 2
	//Copy to Library
	ProjectResourceActionKey_CopyToLibrary ProjectResourceActionKey = 3
	//Move to Library
	ProjectResourceActionKey_MoveToLibrary ProjectResourceActionKey = 4
	//delete
	ProjectResourceActionKey_Delete ProjectResourceActionKey = 5
	//enable
	ProjectResourceActionKey_Enable ProjectResourceActionKey = 6
	//disable
	ProjectResourceActionKey_Disable ProjectResourceActionKey = 7
	// Switch to funcflow
	ProjectResourceActionKey_SwitchToFuncflow ProjectResourceActionKey = 8
	// Switch to chatflow
	ProjectResourceActionKey_SwitchToChatflow ProjectResourceActionKey = 9
	// Modify description
	ProjectResourceActionKey_UpdateDesc ProjectResourceActionKey = 10
)

func (p ProjectResourceActionKey) String() string {
	switch p {
	case ProjectResourceActionKey_Rename:
		return "Rename"
	case ProjectResourceActionKey_Copy:
		return "Copy"
	case ProjectResourceActionKey_CopyToLibrary:
		return "CopyToLibrary"
	case ProjectResourceActionKey_MoveToLibrary:
		return "MoveToLibrary"
	case ProjectResourceActionKey_Delete:
		return "Delete"
	case ProjectResourceActionKey_Enable:
		return "Enable"
	case ProjectResourceActionKey_Disable:
		return "Disable"
	case ProjectResourceActionKey_SwitchToFuncflow:
		return "SwitchToFuncflow"
	case ProjectResourceActionKey_SwitchToChatflow:
		return "SwitchToChatflow"
	case ProjectResourceActionKey_UpdateDesc:
		return "UpdateDesc"
	}
	return "<UNSET>"
}

func ProjectResourceActionKeyFromString(s string) (ProjectResourceActionKey, error) {
	switch s {
	case "Rename":
		return ProjectResourceActionKey_Rename, nil
	case "Copy":
		return ProjectResourceActionKey_Copy, nil
	case "CopyToLibrary":
		return ProjectResourceActionKey_CopyToLibrary, nil
	case "MoveToLibrary":
		return ProjectResourceActionKey_MoveToLibrary, nil
	case "Delete":
		return ProjectResourceActionKey_Delete, nil
	case "Enable":
		return ProjectResourceActionKey_Enable, nil
	case "Disable":
		return ProjectResourceActionKey_Disable, nil
	case "SwitchToFuncflow":
		return ProjectResourceActionKey_SwitchToFuncflow, nil
	case "SwitchToChatflow":
		return ProjectResourceActionKey_SwitchToChatflow, nil
	case "UpdateDesc":
		return ProjectResourceActionKey_UpdateDesc, nil
	}
	return ProjectResourceActionKey(0), fmt.Errorf("not a valid ProjectResourceActionKey string")
}

func ProjectResourceActionKeyPtr(v ProjectResourceActionKey) *ProjectResourceActionKey { return &v }
func (p *ProjectResourceActionKey) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ProjectResourceActionKey(result.Int64)
	return
}

func (p *ProjectResourceActionKey) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ProjectResourceGroupType int64

const (
	ProjectResourceGroupType_Workflow ProjectResourceGroupType = 1
	ProjectResourceGroupType_Plugin   ProjectResourceGroupType = 2
	ProjectResourceGroupType_Data     ProjectResourceGroupType = 3
)

func (p ProjectResourceGroupType) String() string {
	switch p {
	case ProjectResourceGroupType_Workflow:
		return "Workflow"
	case ProjectResourceGroupType_Plugin:
		return "Plugin"
	case ProjectResourceGroupType_Data:
		return "Data"
	}
	return "<UNSET>"
}

func ProjectResourceGroupTypeFromString(s string) (ProjectResourceGroupType, error) {
	switch s {
	case "Workflow":
		return ProjectResourceGroupType_Workflow, nil
	case "Plugin":
		return ProjectResourceGroupType_Plugin, nil
	case "Data":
		return ProjectResourceGroupType_Data, nil
	}
	return ProjectResourceGroupType(0), fmt.Errorf("not a valid ProjectResourceGroupType string")
}

func ProjectResourceGroupTypePtr(v ProjectResourceGroupType) *ProjectResourceGroupType { return &v }
func (p *ProjectResourceGroupType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ProjectResourceGroupType(result.Int64)
	return
}

func (p *ProjectResourceGroupType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ResourceCopyScene int64

const (
	//Copy resources within the project, shallow copy
	ResourceCopyScene_CopyProjectResource ResourceCopyScene = 1
	//Copy the project resources to the Library, and publish after copying
	ResourceCopyScene_CopyResourceToLibrary ResourceCopyScene = 2
	//Move project resources to Library, copy to publish, and delete project resources later
	ResourceCopyScene_MoveResourceToLibrary ResourceCopyScene = 3
	//Copy Library Resources to Project
	ResourceCopyScene_CopyResourceFromLibrary ResourceCopyScene = 4
	//Copy the project, along with the resources. Copy the current draft.
	ResourceCopyScene_CopyProject ResourceCopyScene = 5
	//The project is published to the channel, and the associated resources need to be published (including the store). Publish with the current draft.
	ResourceCopyScene_PublishProject ResourceCopyScene = 6
	// Copy the project template.
	ResourceCopyScene_CopyProjectTemplate ResourceCopyScene = 7
	// The project is published to a template, and the specified version of the project is published as a temporary template.
	ResourceCopyScene_PublishProjectTemplate ResourceCopyScene = 8
	// The template is approved, put on the shelves, and the official template is copied according to the temporary template.
	ResourceCopyScene_LaunchTemplate ResourceCopyScene = 9
	//    Draft version archive
	ResourceCopyScene_ArchiveProject ResourceCopyScene = 10
	// Online version loaded into draft, draft version loaded into draft
	ResourceCopyScene_RollbackProject ResourceCopyScene = 11
	// Cross-space copy of a single resource
	ResourceCopyScene_CrossSpaceCopy ResourceCopyScene = 12
	// item cross-space copy
	ResourceCopyScene_CrossSpaceCopyProject ResourceCopyScene = 13
)

func (p ResourceCopyScene) String() string {
	switch p {
	case ResourceCopyScene_CopyProjectResource:
		return "CopyProjectResource"
	case ResourceCopyScene_CopyResourceToLibrary:
		return "CopyResourceToLibrary"
	case ResourceCopyScene_MoveResourceToLibrary:
		return "MoveResourceToLibrary"
	case ResourceCopyScene_CopyResourceFromLibrary:
		return "CopyResourceFromLibrary"
	case ResourceCopyScene_CopyProject:
		return "CopyProject"
	case ResourceCopyScene_PublishProject:
		return "PublishProject"
	case ResourceCopyScene_CopyProjectTemplate:
		return "CopyProjectTemplate"
	case ResourceCopyScene_PublishProjectTemplate:
		return "PublishProjectTemplate"
	case ResourceCopyScene_LaunchTemplate:
		return "LaunchTemplate"
	case ResourceCopyScene_ArchiveProject:
		return "ArchiveProject"
	case ResourceCopyScene_RollbackProject:
		return "RollbackProject"
	case ResourceCopyScene_CrossSpaceCopy:
		return "CrossSpaceCopy"
	case ResourceCopyScene_CrossSpaceCopyProject:
		return "CrossSpaceCopyProject"
	}
	return "<UNSET>"
}

func ResourceCopySceneFromString(s string) (ResourceCopyScene, error) {
	switch s {
	case "CopyProjectResource":
		return ResourceCopyScene_CopyProjectResource, nil
	case "CopyResourceToLibrary":
		return ResourceCopyScene_CopyResourceToLibrary, nil
	case "MoveResourceToLibrary":
		return ResourceCopyScene_MoveResourceToLibrary, nil
	case "CopyResourceFromLibrary":
		return ResourceCopyScene_CopyResourceFromLibrary, nil
	case "CopyProject":
		return ResourceCopyScene_CopyProject, nil
	case "PublishProject":
		return ResourceCopyScene_PublishProject, nil
	case "CopyProjectTemplate":
		return ResourceCopyScene_CopyProjectTemplate, nil
	case "PublishProjectTemplate":
		return ResourceCopyScene_PublishProjectTemplate, nil
	case "LaunchTemplate":
		return ResourceCopyScene_LaunchTemplate, nil
	case "ArchiveProject":
		return ResourceCopyScene_ArchiveProject, nil
	case "RollbackProject":
		return ResourceCopyScene_RollbackProject, nil
	case "CrossSpaceCopy":
		return ResourceCopyScene_CrossSpaceCopy, nil
	case "CrossSpaceCopyProject":
		return ResourceCopyScene_CrossSpaceCopyProject, nil
	}
	return ResourceCopyScene(0), fmt.Errorf("not a valid ResourceCopyScene string")
}

func ResourceCopyScenePtr(v ResourceCopyScene) *ResourceCopyScene { return &v }
func (p *ResourceCopyScene) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ResourceCopyScene(result.Int64)
	return
}

func (p *ResourceCopyScene) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type TaskStatus int64

const (
	TaskStatus_Successed  TaskStatus = 1
	TaskStatus_Processing TaskStatus = 2
	TaskStatus_Failed     TaskStatus = 3
	TaskStatus_Canceled   TaskStatus = 4
)

func (p TaskStatus) String() string {
	switch p {
	case TaskStatus_Successed:
		return "Successed"
	case TaskStatus_Processing:
		return "Processing"
	case TaskStatus_Failed:
		return "Failed"
	case TaskStatus_Canceled:
		return "Canceled"
	}
	return "<UNSET>"
}

func TaskStatusFromString(s string) (TaskStatus, error) {
	switch s {
	case "Successed":
		return TaskStatus_Successed, nil
	case "Processing":
		return TaskStatus_Processing, nil
	case "Failed":
		return TaskStatus_Failed, nil
	case "Canceled":
		return TaskStatus_Canceled, nil
	}
	return TaskStatus(0), fmt.Errorf("not a valid TaskStatus string")
}

func TaskStatusPtr(v TaskStatus) *TaskStatus { return &v }
func (p *TaskStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TaskStatus(result.Int64)
	return
}

func (p *TaskStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// Library Resource Operations
type ResourceAction struct {
	// An operation corresponds to a unique key, and the key is constrained by the resource side
	Key ActionKey `thrift:"Key,1,required" json:"key" form:"Key,required" query:"Key,required"`
	//ture = can operate this Action, false = grey out
	Enable bool `thrift:"Enable,2,required" json:"enable" form:"Enable,required" query:"Enable,required"`
}

func NewResourceAction() *ResourceAction {
	return &ResourceAction{}
}

func (p *ResourceAction) InitDefault() {
}

func (p *ResourceAction) GetKey() (v ActionKey) {
	return p.Key
}

func (p *ResourceAction) GetEnable() (v bool) {
	return p.Enable
}

var fieldIDToName_ResourceAction = map[int16]string{
	1: "Key",
	2: "Enable",
}

func (p *ResourceAction) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetKey bool = false
	var issetEnable bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetKey = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetKey {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEnable {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceAction[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ResourceAction[fieldId]))
}

func (p *ResourceAction) ReadField1(iprot thrift.TProtocol) error {

	var _field ActionKey
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ActionKey(v)
	}
	p.Key = _field
	return nil
}
func (p *ResourceAction) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Enable = _field
	return nil
}

func (p *ResourceAction) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceAction"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceAction) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Key", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Key)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceAction) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Enable", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Enable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ResourceAction) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceAction(%+v)", *p)

}

// front end
type ResourceInfo struct {
	// Resource ID
	ResID *int64 `thrift:"ResID,1,optional" form:"res_id" json:"res_id,string,omitempty"`
	// resource type
	ResType *ResType `thrift:"ResType,2,optional" json:"res_type" form:"ResType" query:"ResType"`
	// Resource subtype, defined by the resource implementer.
	// Plugin：1-Http; 2-App; 6-Local；Knowledge：0-text; 1-table; 2-image；UI：1-Card
	ResSubType *int32 `thrift:"ResSubType,3,optional" json:"res_sub_type" form:"ResSubType" query:"ResSubType"`
	// resource name
	Name *string `thrift:"Name,4,optional" json:"name" form:"Name" query:"Name"`
	// resource description
	Desc *string `thrift:"Desc,5,optional" json:"desc" form:"Desc" query:"Desc"`
	// Resource Icon, full url
	Icon *string `thrift:"Icon,6,optional" json:"icon" form:"Icon" query:"Icon"`
	// Resource creator
	CreatorID *int64 `thrift:"CreatorID,7,optional" form:"creator_id" json:"creator_id,string,omitempty"`
	// Resource creator
	CreatorAvatar *string `thrift:"CreatorAvatar,8,optional" json:"creator_avatar" form:"CreatorAvatar" query:"CreatorAvatar"`
	// Resource creator
	CreatorName *string `thrift:"CreatorName,9,optional" json:"creator_name" form:"CreatorName" query:"CreatorName"`
	// Resource creator
	UserName *string `thrift:"UserName,10,optional" json:"user_name" form:"UserName" query:"UserName"`
	// Resource release status, 1 - unpublished, 2 - published
	PublishStatus *PublishStatus `thrift:"PublishStatus,11,optional" json:"publish_status" form:"PublishStatus" query:"PublishStatus"`
	// Resource status, each type of resource defines itself
	BizResStatus *int32 `thrift:"BizResStatus,12,optional" json:"biz_res_status" form:"BizResStatus" query:"BizResStatus"`
	// Whether to enable multi-person editing
	CollaborationEnable *bool `thrift:"CollaborationEnable,13,optional" json:"collaboration_enable" form:"CollaborationEnable" query:"CollaborationEnable"`
	// Last edited, unix timestamp
	EditTime *int64 `thrift:"EditTime,14,optional" form:"edit_time" json:"edit_time,string,omitempty"`
	// Resource Ownership Space ID
	SpaceID *int64 `thrift:"SpaceID,15,optional" form:"space_id" json:"space_id,string,omitempty"`
	// Business carry extended information to res_type distinguish, each res_type defined schema and meaning is not the same, need to judge before use res_type
	BizExtend map[string]string `thrift:"BizExtend,16,optional" json:"biz_extend" form:"BizExtend" query:"BizExtend"`
	// Different types of different operation buttons are agreed upon by the resource implementer and the front end. Return is displayed, if you want to hide a button, do not return;
	Actions []*ResourceAction `thrift:"Actions,17,optional" json:"actions" form:"Actions" query:"Actions"`
	// Whether to ban entering the details page
	DetailDisable *bool `thrift:"DetailDisable,18,optional" json:"detail_disable" form:"DetailDisable" query:"DetailDisable"`
	// [Data delay optimization] Delete identifier, true-deleted-frontend hides the item, false-normal
	DelFlag *bool `thrift:"DelFlag,19,optional" json:"del_flag" form:"DelFlag" query:"DelFlag"`
}

func NewResourceInfo() *ResourceInfo {
	return &ResourceInfo{}
}

func (p *ResourceInfo) InitDefault() {
}

var ResourceInfo_ResID_DEFAULT int64

func (p *ResourceInfo) GetResID() (v int64) {
	if !p.IsSetResID() {
		return ResourceInfo_ResID_DEFAULT
	}
	return *p.ResID
}

var ResourceInfo_ResType_DEFAULT ResType

func (p *ResourceInfo) GetResType() (v ResType) {
	if !p.IsSetResType() {
		return ResourceInfo_ResType_DEFAULT
	}
	return *p.ResType
}

var ResourceInfo_ResSubType_DEFAULT int32

func (p *ResourceInfo) GetResSubType() (v int32) {
	if !p.IsSetResSubType() {
		return ResourceInfo_ResSubType_DEFAULT
	}
	return *p.ResSubType
}

var ResourceInfo_Name_DEFAULT string

func (p *ResourceInfo) GetName() (v string) {
	if !p.IsSetName() {
		return ResourceInfo_Name_DEFAULT
	}
	return *p.Name
}

var ResourceInfo_Desc_DEFAULT string

func (p *ResourceInfo) GetDesc() (v string) {
	if !p.IsSetDesc() {
		return ResourceInfo_Desc_DEFAULT
	}
	return *p.Desc
}

var ResourceInfo_Icon_DEFAULT string

func (p *ResourceInfo) GetIcon() (v string) {
	if !p.IsSetIcon() {
		return ResourceInfo_Icon_DEFAULT
	}
	return *p.Icon
}

var ResourceInfo_CreatorID_DEFAULT int64

func (p *ResourceInfo) GetCreatorID() (v int64) {
	if !p.IsSetCreatorID() {
		return ResourceInfo_CreatorID_DEFAULT
	}
	return *p.CreatorID
}

var ResourceInfo_CreatorAvatar_DEFAULT string

func (p *ResourceInfo) GetCreatorAvatar() (v string) {
	if !p.IsSetCreatorAvatar() {
		return ResourceInfo_CreatorAvatar_DEFAULT
	}
	return *p.CreatorAvatar
}

var ResourceInfo_CreatorName_DEFAULT string

func (p *ResourceInfo) GetCreatorName() (v string) {
	if !p.IsSetCreatorName() {
		return ResourceInfo_CreatorName_DEFAULT
	}
	return *p.CreatorName
}

var ResourceInfo_UserName_DEFAULT string

func (p *ResourceInfo) GetUserName() (v string) {
	if !p.IsSetUserName() {
		return ResourceInfo_UserName_DEFAULT
	}
	return *p.UserName
}

var ResourceInfo_PublishStatus_DEFAULT PublishStatus

func (p *ResourceInfo) GetPublishStatus() (v PublishStatus) {
	if !p.IsSetPublishStatus() {
		return ResourceInfo_PublishStatus_DEFAULT
	}
	return *p.PublishStatus
}

var ResourceInfo_BizResStatus_DEFAULT int32

func (p *ResourceInfo) GetBizResStatus() (v int32) {
	if !p.IsSetBizResStatus() {
		return ResourceInfo_BizResStatus_DEFAULT
	}
	return *p.BizResStatus
}

var ResourceInfo_CollaborationEnable_DEFAULT bool

func (p *ResourceInfo) GetCollaborationEnable() (v bool) {
	if !p.IsSetCollaborationEnable() {
		return ResourceInfo_CollaborationEnable_DEFAULT
	}
	return *p.CollaborationEnable
}

var ResourceInfo_EditTime_DEFAULT int64

func (p *ResourceInfo) GetEditTime() (v int64) {
	if !p.IsSetEditTime() {
		return ResourceInfo_EditTime_DEFAULT
	}
	return *p.EditTime
}

var ResourceInfo_SpaceID_DEFAULT int64

func (p *ResourceInfo) GetSpaceID() (v int64) {
	if !p.IsSetSpaceID() {
		return ResourceInfo_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

var ResourceInfo_BizExtend_DEFAULT map[string]string

func (p *ResourceInfo) GetBizExtend() (v map[string]string) {
	if !p.IsSetBizExtend() {
		return ResourceInfo_BizExtend_DEFAULT
	}
	return p.BizExtend
}

var ResourceInfo_Actions_DEFAULT []*ResourceAction

func (p *ResourceInfo) GetActions() (v []*ResourceAction) {
	if !p.IsSetActions() {
		return ResourceInfo_Actions_DEFAULT
	}
	return p.Actions
}

var ResourceInfo_DetailDisable_DEFAULT bool

func (p *ResourceInfo) GetDetailDisable() (v bool) {
	if !p.IsSetDetailDisable() {
		return ResourceInfo_DetailDisable_DEFAULT
	}
	return *p.DetailDisable
}

var ResourceInfo_DelFlag_DEFAULT bool

func (p *ResourceInfo) GetDelFlag() (v bool) {
	if !p.IsSetDelFlag() {
		return ResourceInfo_DelFlag_DEFAULT
	}
	return *p.DelFlag
}

var fieldIDToName_ResourceInfo = map[int16]string{
	1:  "ResID",
	2:  "ResType",
	3:  "ResSubType",
	4:  "Name",
	5:  "Desc",
	6:  "Icon",
	7:  "CreatorID",
	8:  "CreatorAvatar",
	9:  "CreatorName",
	10: "UserName",
	11: "PublishStatus",
	12: "BizResStatus",
	13: "CollaborationEnable",
	14: "EditTime",
	15: "SpaceID",
	16: "BizExtend",
	17: "Actions",
	18: "DetailDisable",
	19: "DelFlag",
}

func (p *ResourceInfo) IsSetResID() bool {
	return p.ResID != nil
}

func (p *ResourceInfo) IsSetResType() bool {
	return p.ResType != nil
}

func (p *ResourceInfo) IsSetResSubType() bool {
	return p.ResSubType != nil
}

func (p *ResourceInfo) IsSetName() bool {
	return p.Name != nil
}

func (p *ResourceInfo) IsSetDesc() bool {
	return p.Desc != nil
}

func (p *ResourceInfo) IsSetIcon() bool {
	return p.Icon != nil
}

func (p *ResourceInfo) IsSetCreatorID() bool {
	return p.CreatorID != nil
}

func (p *ResourceInfo) IsSetCreatorAvatar() bool {
	return p.CreatorAvatar != nil
}

func (p *ResourceInfo) IsSetCreatorName() bool {
	return p.CreatorName != nil
}

func (p *ResourceInfo) IsSetUserName() bool {
	return p.UserName != nil
}

func (p *ResourceInfo) IsSetPublishStatus() bool {
	return p.PublishStatus != nil
}

func (p *ResourceInfo) IsSetBizResStatus() bool {
	return p.BizResStatus != nil
}

func (p *ResourceInfo) IsSetCollaborationEnable() bool {
	return p.CollaborationEnable != nil
}

func (p *ResourceInfo) IsSetEditTime() bool {
	return p.EditTime != nil
}

func (p *ResourceInfo) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *ResourceInfo) IsSetBizExtend() bool {
	return p.BizExtend != nil
}

func (p *ResourceInfo) IsSetActions() bool {
	return p.Actions != nil
}

func (p *ResourceInfo) IsSetDetailDisable() bool {
	return p.DetailDisable != nil
}

func (p *ResourceInfo) IsSetDelFlag() bool {
	return p.DelFlag != nil
}

func (p *ResourceInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ResID = _field
	return nil
}
func (p *ResourceInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *ResType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ResType(v)
		_field = &tmp
	}
	p.ResType = _field
	return nil
}
func (p *ResourceInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ResSubType = _field
	return nil
}
func (p *ResourceInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *ResourceInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Desc = _field
	return nil
}
func (p *ResourceInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Icon = _field
	return nil
}
func (p *ResourceInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreatorID = _field
	return nil
}
func (p *ResourceInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreatorAvatar = _field
	return nil
}
func (p *ResourceInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreatorName = _field
	return nil
}
func (p *ResourceInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserName = _field
	return nil
}
func (p *ResourceInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field *PublishStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := PublishStatus(v)
		_field = &tmp
	}
	p.PublishStatus = _field
	return nil
}
func (p *ResourceInfo) ReadField12(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BizResStatus = _field
	return nil
}
func (p *ResourceInfo) ReadField13(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CollaborationEnable = _field
	return nil
}
func (p *ResourceInfo) ReadField14(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EditTime = _field
	return nil
}
func (p *ResourceInfo) ReadField15(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SpaceID = _field
	return nil
}
func (p *ResourceInfo) ReadField16(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.BizExtend = _field
	return nil
}
func (p *ResourceInfo) ReadField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ResourceAction, 0, size)
	values := make([]ResourceAction, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Actions = _field
	return nil
}
func (p *ResourceInfo) ReadField18(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DetailDisable = _field
	return nil
}
func (p *ResourceInfo) ReadField19(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DelFlag = _field
	return nil
}

func (p *ResourceInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetResID() {
		if err = oprot.WriteFieldBegin("ResID", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ResID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetResType() {
		if err = oprot.WriteFieldBegin("ResType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ResType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ResourceInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetResSubType() {
		if err = oprot.WriteFieldBegin("ResSubType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ResSubType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ResourceInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("Name", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ResourceInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetDesc() {
		if err = oprot.WriteFieldBegin("Desc", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Desc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ResourceInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetIcon() {
		if err = oprot.WriteFieldBegin("Icon", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Icon); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ResourceInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreatorID() {
		if err = oprot.WriteFieldBegin("CreatorID", thrift.I64, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.CreatorID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ResourceInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreatorAvatar() {
		if err = oprot.WriteFieldBegin("CreatorAvatar", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreatorAvatar); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *ResourceInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreatorName() {
		if err = oprot.WriteFieldBegin("CreatorName", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreatorName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *ResourceInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserName() {
		if err = oprot.WriteFieldBegin("UserName", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *ResourceInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetPublishStatus() {
		if err = oprot.WriteFieldBegin("PublishStatus", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.PublishStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *ResourceInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizResStatus() {
		if err = oprot.WriteFieldBegin("BizResStatus", thrift.I32, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.BizResStatus); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *ResourceInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetCollaborationEnable() {
		if err = oprot.WriteFieldBegin("CollaborationEnable", thrift.BOOL, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.CollaborationEnable); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *ResourceInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetEditTime() {
		if err = oprot.WriteFieldBegin("EditTime", thrift.I64, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.EditTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *ResourceInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetSpaceID() {
		if err = oprot.WriteFieldBegin("SpaceID", thrift.I64, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.SpaceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *ResourceInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizExtend() {
		if err = oprot.WriteFieldBegin("BizExtend", thrift.MAP, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.BizExtend)); err != nil {
			return err
		}
		for k, v := range p.BizExtend {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *ResourceInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetActions() {
		if err = oprot.WriteFieldBegin("Actions", thrift.LIST, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Actions)); err != nil {
			return err
		}
		for _, v := range p.Actions {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}
func (p *ResourceInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetDetailDisable() {
		if err = oprot.WriteFieldBegin("DetailDisable", thrift.BOOL, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.DetailDisable); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}
func (p *ResourceInfo) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetDelFlag() {
		if err = oprot.WriteFieldBegin("DelFlag", thrift.BOOL, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.DelFlag); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *ResourceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceInfo(%+v)", *p)

}

type ProjectResourceAction struct {
	// An operation corresponds to a unique key, and the key is constrained by the resource side
	Key ProjectResourceActionKey `thrift:"Key,1,required" json:"key" form:"Key,required" query:"Key,required"`
	//ture = can operate this Action, false = grey out
	Enable bool `thrift:"Enable,2,required" json:"enable" form:"Enable,required" query:"Enable,required"`
	// When enable = false, prompt the copywriter. The backend returns the Starling Key, be careful to put it under the same space.
	Hint *string `thrift:"Hint,3,optional" json:"hint" form:"Hint" query:"Hint"`
}

func NewProjectResourceAction() *ProjectResourceAction {
	return &ProjectResourceAction{}
}

func (p *ProjectResourceAction) InitDefault() {
}

func (p *ProjectResourceAction) GetKey() (v ProjectResourceActionKey) {
	return p.Key
}

func (p *ProjectResourceAction) GetEnable() (v bool) {
	return p.Enable
}

var ProjectResourceAction_Hint_DEFAULT string

func (p *ProjectResourceAction) GetHint() (v string) {
	if !p.IsSetHint() {
		return ProjectResourceAction_Hint_DEFAULT
	}
	return *p.Hint
}

var fieldIDToName_ProjectResourceAction = map[int16]string{
	1: "Key",
	2: "Enable",
	3: "Hint",
}

func (p *ProjectResourceAction) IsSetHint() bool {
	return p.Hint != nil
}

func (p *ProjectResourceAction) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetKey bool = false
	var issetEnable bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetKey = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetKey {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEnable {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ProjectResourceAction[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ProjectResourceAction[fieldId]))
}

func (p *ProjectResourceAction) ReadField1(iprot thrift.TProtocol) error {

	var _field ProjectResourceActionKey
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ProjectResourceActionKey(v)
	}
	p.Key = _field
	return nil
}
func (p *ProjectResourceAction) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Enable = _field
	return nil
}
func (p *ProjectResourceAction) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Hint = _field
	return nil
}

func (p *ProjectResourceAction) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ProjectResourceAction"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ProjectResourceAction) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Key", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Key)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ProjectResourceAction) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Enable", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Enable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ProjectResourceAction) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetHint() {
		if err = oprot.WriteFieldBegin("Hint", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Hint); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ProjectResourceAction) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectResourceAction(%+v)", *p)

}

// The implementer provides display information
type ProjectResourceInfo struct {
	// Resource ID
	ResID int64 `thrift:"ResID,1" form:"res_id" json:"res_id,string"`
	// resource name
	Name string `thrift:"Name,2" json:"name" form:"Name" query:"Name"`
	// Different types of different operation buttons are agreed upon by the resource implementer and the front end. Return is displayed, if you want to hide a button, do not return;
	Actions []*ProjectResourceAction `thrift:"Actions,3" json:"actions" form:"Actions" query:"Actions"`
	// Is the user read-only to the resource?
	//    4: bool ReadOnly (go.tag = "json:\"read_only\"", agw.key = "read_only")
	// resource type
	ResType ResType `thrift:"ResType,5" json:"res_type" form:"ResType" query:"ResType"`
	// Resource subtype, defined by the resource implementer. Plugin: 1-Http; 2-App; 6-Local; Knowledge: 0-text; 1-table; 2-image; UI: 1-Card
	ResSubType *int32 `thrift:"ResSubType,6,optional" json:"res_sub_type" form:"ResSubType" query:"ResSubType"`
	// Business carry extended information to res_type distinguish, each res_type defined schema and meaning is not the same, need to judge before use res_type
	BizExtend map[string]string `thrift:"BizExtend,7,optional" json:"biz_extend" form:"BizExtend" query:"BizExtend"`
	// Resource status, each type of resource defines itself. The front end agrees with each resource party.
	BizResStatus *int32 `thrift:"BizResStatus,8,optional" json:"biz_res_status" form:"BizResStatus" query:"BizResStatus"`
	// The edited version of the current resource
	VersionStr *string `thrift:"VersionStr,9,optional" json:"version_str" form:"VersionStr" query:"VersionStr"`
}

func NewProjectResourceInfo() *ProjectResourceInfo {
	return &ProjectResourceInfo{}
}

func (p *ProjectResourceInfo) InitDefault() {
}

func (p *ProjectResourceInfo) GetResID() (v int64) {
	return p.ResID
}

func (p *ProjectResourceInfo) GetName() (v string) {
	return p.Name
}

func (p *ProjectResourceInfo) GetActions() (v []*ProjectResourceAction) {
	return p.Actions
}

func (p *ProjectResourceInfo) GetResType() (v ResType) {
	return p.ResType
}

var ProjectResourceInfo_ResSubType_DEFAULT int32

func (p *ProjectResourceInfo) GetResSubType() (v int32) {
	if !p.IsSetResSubType() {
		return ProjectResourceInfo_ResSubType_DEFAULT
	}
	return *p.ResSubType
}

var ProjectResourceInfo_BizExtend_DEFAULT map[string]string

func (p *ProjectResourceInfo) GetBizExtend() (v map[string]string) {
	if !p.IsSetBizExtend() {
		return ProjectResourceInfo_BizExtend_DEFAULT
	}
	return p.BizExtend
}

var ProjectResourceInfo_BizResStatus_DEFAULT int32

func (p *ProjectResourceInfo) GetBizResStatus() (v int32) {
	if !p.IsSetBizResStatus() {
		return ProjectResourceInfo_BizResStatus_DEFAULT
	}
	return *p.BizResStatus
}

var ProjectResourceInfo_VersionStr_DEFAULT string

func (p *ProjectResourceInfo) GetVersionStr() (v string) {
	if !p.IsSetVersionStr() {
		return ProjectResourceInfo_VersionStr_DEFAULT
	}
	return *p.VersionStr
}

var fieldIDToName_ProjectResourceInfo = map[int16]string{
	1: "ResID",
	2: "Name",
	3: "Actions",
	5: "ResType",
	6: "ResSubType",
	7: "BizExtend",
	8: "BizResStatus",
	9: "VersionStr",
}

func (p *ProjectResourceInfo) IsSetResSubType() bool {
	return p.ResSubType != nil
}

func (p *ProjectResourceInfo) IsSetBizExtend() bool {
	return p.BizExtend != nil
}

func (p *ProjectResourceInfo) IsSetBizResStatus() bool {
	return p.BizResStatus != nil
}

func (p *ProjectResourceInfo) IsSetVersionStr() bool {
	return p.VersionStr != nil
}

func (p *ProjectResourceInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ProjectResourceInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ProjectResourceInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ResID = _field
	return nil
}
func (p *ProjectResourceInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *ProjectResourceInfo) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ProjectResourceAction, 0, size)
	values := make([]ProjectResourceAction, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Actions = _field
	return nil
}
func (p *ProjectResourceInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field ResType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ResType(v)
	}
	p.ResType = _field
	return nil
}
func (p *ProjectResourceInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ResSubType = _field
	return nil
}
func (p *ProjectResourceInfo) ReadField7(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.BizExtend = _field
	return nil
}
func (p *ProjectResourceInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BizResStatus = _field
	return nil
}
func (p *ProjectResourceInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.VersionStr = _field
	return nil
}

func (p *ProjectResourceInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ProjectResourceInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ProjectResourceInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ResID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ResID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ProjectResourceInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ProjectResourceInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Actions", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Actions)); err != nil {
		return err
	}
	for _, v := range p.Actions {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ProjectResourceInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ResType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ResType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ProjectResourceInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetResSubType() {
		if err = oprot.WriteFieldBegin("ResSubType", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ResSubType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ProjectResourceInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizExtend() {
		if err = oprot.WriteFieldBegin("BizExtend", thrift.MAP, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.BizExtend)); err != nil {
			return err
		}
		for k, v := range p.BizExtend {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ProjectResourceInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizResStatus() {
		if err = oprot.WriteFieldBegin("BizResStatus", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.BizResStatus); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *ProjectResourceInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetVersionStr() {
		if err = oprot.WriteFieldBegin("VersionStr", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.VersionStr); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ProjectResourceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectResourceInfo(%+v)", *p)

}

type ProjectResourceGroup struct {
	// resource grouping
	GroupType    ProjectResourceGroupType `thrift:"GroupType,1" json:"group_type" form:"GroupType" query:"GroupType"`
	ResourceList []*ProjectResourceInfo   `thrift:"ResourceList,2,optional" json:"resource_list" form:"ResourceList" query:"ResourceList"`
}

func NewProjectResourceGroup() *ProjectResourceGroup {
	return &ProjectResourceGroup{}
}

func (p *ProjectResourceGroup) InitDefault() {
}

func (p *ProjectResourceGroup) GetGroupType() (v ProjectResourceGroupType) {
	return p.GroupType
}

var ProjectResourceGroup_ResourceList_DEFAULT []*ProjectResourceInfo

func (p *ProjectResourceGroup) GetResourceList() (v []*ProjectResourceInfo) {
	if !p.IsSetResourceList() {
		return ProjectResourceGroup_ResourceList_DEFAULT
	}
	return p.ResourceList
}

var fieldIDToName_ProjectResourceGroup = map[int16]string{
	1: "GroupType",
	2: "ResourceList",
}

func (p *ProjectResourceGroup) IsSetResourceList() bool {
	return p.ResourceList != nil
}

func (p *ProjectResourceGroup) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ProjectResourceGroup[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ProjectResourceGroup) ReadField1(iprot thrift.TProtocol) error {

	var _field ProjectResourceGroupType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ProjectResourceGroupType(v)
	}
	p.GroupType = _field
	return nil
}
func (p *ProjectResourceGroup) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ProjectResourceInfo, 0, size)
	values := make([]ProjectResourceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ResourceList = _field
	return nil
}

func (p *ProjectResourceGroup) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ProjectResourceGroup"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ProjectResourceGroup) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("GroupType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.GroupType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ProjectResourceGroup) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetResourceList() {
		if err = oprot.WriteFieldBegin("ResourceList", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResourceList)); err != nil {
			return err
		}
		for _, v := range p.ResourceList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ProjectResourceGroup) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectResourceGroup(%+v)", *p)

}

type ResourceCopyFailedReason struct {
	ResID   int64   `thrift:"ResID,1" form:"res_id" json:"res_id,string"`
	ResType ResType `thrift:"ResType,2" json:"res_type" form:"ResType" query:"ResType"`
	ResName string  `thrift:"ResName,3" json:"res_name" form:"ResName" query:"ResName"`
	Reason  string  `thrift:"Reason,4" json:"reason" form:"Reason" query:"Reason"`
	// abandoned
	PublishVersion *int64 `thrift:"PublishVersion,5,optional" json:"publish_version" form:"PublishVersion" query:"PublishVersion"`
	// The current version of the resource, either nil or empty string, is considered the latest version. Project release or Library release.
	PublishVersionStr *string `thrift:"PublishVersionStr,6,optional" json:"publish_version_str" form:"PublishVersionStr" query:"PublishVersionStr"`
}

func NewResourceCopyFailedReason() *ResourceCopyFailedReason {
	return &ResourceCopyFailedReason{}
}

func (p *ResourceCopyFailedReason) InitDefault() {
}

func (p *ResourceCopyFailedReason) GetResID() (v int64) {
	return p.ResID
}

func (p *ResourceCopyFailedReason) GetResType() (v ResType) {
	return p.ResType
}

func (p *ResourceCopyFailedReason) GetResName() (v string) {
	return p.ResName
}

func (p *ResourceCopyFailedReason) GetReason() (v string) {
	return p.Reason
}

var ResourceCopyFailedReason_PublishVersion_DEFAULT int64

func (p *ResourceCopyFailedReason) GetPublishVersion() (v int64) {
	if !p.IsSetPublishVersion() {
		return ResourceCopyFailedReason_PublishVersion_DEFAULT
	}
	return *p.PublishVersion
}

var ResourceCopyFailedReason_PublishVersionStr_DEFAULT string

func (p *ResourceCopyFailedReason) GetPublishVersionStr() (v string) {
	if !p.IsSetPublishVersionStr() {
		return ResourceCopyFailedReason_PublishVersionStr_DEFAULT
	}
	return *p.PublishVersionStr
}

var fieldIDToName_ResourceCopyFailedReason = map[int16]string{
	1: "ResID",
	2: "ResType",
	3: "ResName",
	4: "Reason",
	5: "PublishVersion",
	6: "PublishVersionStr",
}

func (p *ResourceCopyFailedReason) IsSetPublishVersion() bool {
	return p.PublishVersion != nil
}

func (p *ResourceCopyFailedReason) IsSetPublishVersionStr() bool {
	return p.PublishVersionStr != nil
}

func (p *ResourceCopyFailedReason) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCopyFailedReason[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceCopyFailedReason) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ResID = _field
	return nil
}
func (p *ResourceCopyFailedReason) ReadField2(iprot thrift.TProtocol) error {

	var _field ResType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ResType(v)
	}
	p.ResType = _field
	return nil
}
func (p *ResourceCopyFailedReason) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ResName = _field
	return nil
}
func (p *ResourceCopyFailedReason) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Reason = _field
	return nil
}
func (p *ResourceCopyFailedReason) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PublishVersion = _field
	return nil
}
func (p *ResourceCopyFailedReason) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PublishVersionStr = _field
	return nil
}

func (p *ResourceCopyFailedReason) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyFailedReason"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCopyFailedReason) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ResID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ResID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceCopyFailedReason) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ResType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ResType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ResourceCopyFailedReason) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ResName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ResName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ResourceCopyFailedReason) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Reason", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Reason); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ResourceCopyFailedReason) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPublishVersion() {
		if err = oprot.WriteFieldBegin("PublishVersion", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.PublishVersion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ResourceCopyFailedReason) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPublishVersionStr() {
		if err = oprot.WriteFieldBegin("PublishVersionStr", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.PublishVersionStr); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ResourceCopyFailedReason) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCopyFailedReason(%+v)", *p)

}

type ResourceCopyTaskDetail struct {
	TaskID string `thrift:"task_id,1" form:"task_id" json:"task_id" query:"task_id"`
	// task status
	Status TaskStatus `thrift:"status,2" form:"status" json:"status" query:"status"`
	// Replicated resource id
	ResID   int64             `thrift:"res_id,3" form:"res_id" json:"res_id,string" query:"res_id"`
	ResType ResType           `thrift:"res_type,4" form:"res_type" json:"res_type" query:"res_type"`
	Scene   ResourceCopyScene `thrift:"scene,5" form:"scene" json:"scene" query:"scene"`
	// Resource name before copy
	ResName *string `thrift:"res_name,6,optional" form:"res_name" json:"res_name,omitempty" query:"res_name"`
}

func NewResourceCopyTaskDetail() *ResourceCopyTaskDetail {
	return &ResourceCopyTaskDetail{}
}

func (p *ResourceCopyTaskDetail) InitDefault() {
}

func (p *ResourceCopyTaskDetail) GetTaskID() (v string) {
	return p.TaskID
}

func (p *ResourceCopyTaskDetail) GetStatus() (v TaskStatus) {
	return p.Status
}

func (p *ResourceCopyTaskDetail) GetResID() (v int64) {
	return p.ResID
}

func (p *ResourceCopyTaskDetail) GetResType() (v ResType) {
	return p.ResType
}

func (p *ResourceCopyTaskDetail) GetScene() (v ResourceCopyScene) {
	return p.Scene
}

var ResourceCopyTaskDetail_ResName_DEFAULT string

func (p *ResourceCopyTaskDetail) GetResName() (v string) {
	if !p.IsSetResName() {
		return ResourceCopyTaskDetail_ResName_DEFAULT
	}
	return *p.ResName
}

var fieldIDToName_ResourceCopyTaskDetail = map[int16]string{
	1: "task_id",
	2: "status",
	3: "res_id",
	4: "res_type",
	5: "scene",
	6: "res_name",
}

func (p *ResourceCopyTaskDetail) IsSetResName() bool {
	return p.ResName != nil
}

func (p *ResourceCopyTaskDetail) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCopyTaskDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceCopyTaskDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskID = _field
	return nil
}
func (p *ResourceCopyTaskDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field TaskStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TaskStatus(v)
	}
	p.Status = _field
	return nil
}
func (p *ResourceCopyTaskDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ResID = _field
	return nil
}
func (p *ResourceCopyTaskDetail) ReadField4(iprot thrift.TProtocol) error {

	var _field ResType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ResType(v)
	}
	p.ResType = _field
	return nil
}
func (p *ResourceCopyTaskDetail) ReadField5(iprot thrift.TProtocol) error {

	var _field ResourceCopyScene
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ResourceCopyScene(v)
	}
	p.Scene = _field
	return nil
}
func (p *ResourceCopyTaskDetail) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ResName = _field
	return nil
}

func (p *ResourceCopyTaskDetail) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyTaskDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCopyTaskDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("task_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceCopyTaskDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("status", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ResourceCopyTaskDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("res_id", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ResID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ResourceCopyTaskDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("res_type", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ResType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ResourceCopyTaskDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("scene", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Scene)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ResourceCopyTaskDetail) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetResName() {
		if err = oprot.WriteFieldBegin("res_name", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ResName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ResourceCopyTaskDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCopyTaskDetail(%+v)", *p)

}
