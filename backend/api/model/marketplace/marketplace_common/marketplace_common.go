// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package marketplace_common

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

type FollowType int64

const (
	// Unknown
	FollowType_Unknown FollowType = 0
	// followee
	FollowType_Followee FollowType = 1
	// follower
	FollowType_Follower FollowType = 2
	// MutualFollow
	FollowType_MutualFollow FollowType = 3
)

func (p FollowType) String() string {
	switch p {
	case FollowType_Unknown:
		return "Unknown"
	case FollowType_Followee:
		return "Followee"
	case FollowType_Follower:
		return "Follower"
	case FollowType_MutualFollow:
		return "MutualFollow"
	}
	return "<UNSET>"
}

func FollowTypeFromString(s string) (FollowType, error) {
	switch s {
	case "Unknown":
		return FollowType_Unknown, nil
	case "Followee":
		return FollowType_Followee, nil
	case "Follower":
		return FollowType_Follower, nil
	case "MutualFollow":
		return FollowType_MutualFollow, nil
	}
	return FollowType(0), fmt.<PERSON><PERSON>rf("not a valid FollowType string")
}

func FollowTypePtr(v FollowType) *FollowType { return &v }
func (p *FollowType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = FollowType(result.Int64)
	return
}

func (p *FollowType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type Price struct {
	// amount
	Amount int64 `thrift:"Amount,1" form:"amount" json:"amount,string"`
	// Currencies such as USD and CNY
	Currency string `thrift:"Currency,2" form:"currency" json:"currency"`
	// decimal places
	DecimalNum int8 `thrift:"DecimalNum,3" form:"decimal_num" json:"decimal_num"`
}

func NewPrice() *Price {
	return &Price{}
}

func (p *Price) InitDefault() {
}

func (p *Price) GetAmount() (v int64) {
	return p.Amount
}

func (p *Price) GetCurrency() (v string) {
	return p.Currency
}

func (p *Price) GetDecimalNum() (v int8) {
	return p.DecimalNum
}

var fieldIDToName_Price = map[int16]string{
	1: "Amount",
	2: "Currency",
	3: "DecimalNum",
}

func (p *Price) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Price[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Price) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Amount = _field
	return nil
}
func (p *Price) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Currency = _field
	return nil
}
func (p *Price) ReadField3(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DecimalNum = _field
	return nil
}

func (p *Price) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Price"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Price) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Amount", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Amount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Price) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Currency", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Currency); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Price) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DecimalNum", thrift.BYTE, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.DecimalNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Price) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Price(%+v)", *p)

}
