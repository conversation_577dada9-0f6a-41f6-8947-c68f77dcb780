// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package openapiauth

import (
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

const (
	All = "all"

	Others = "others"

	Owned = "owned"

	Active = "active"

	Deactivated = "deactivated"

	Offboarded = "offboarded"
)

type PatSearchOption = string

type UserStatus = string

type CreatePersonalAccessTokenAndPermissionRequest struct {
	// PAT name
	Name string `thrift:"name,1,required" form:"name,required" json:"name,required" query:"name,required"`
	// PAT custom expiration time
	ExpireAt int64 `thrift:"expire_at,2" form:"expire_at" json:"expire_at" query:"expire_at"`
	// PAT user enumeration expiration time 1, 30, 60, 90, 180, 365, permanent
	DurationDay string `thrift:"duration_day,3" form:"duration_day" json:"duration_day" query:"duration_day"`
	// organization id
	OrganizationID string `thrift:"organization_id,4" form:"organization_id" json:"organization_id" query:"organization_id"`
}

func NewCreatePersonalAccessTokenAndPermissionRequest() *CreatePersonalAccessTokenAndPermissionRequest {
	return &CreatePersonalAccessTokenAndPermissionRequest{}
}

func (p *CreatePersonalAccessTokenAndPermissionRequest) InitDefault() {
}

func (p *CreatePersonalAccessTokenAndPermissionRequest) GetName() (v string) {
	return p.Name
}

func (p *CreatePersonalAccessTokenAndPermissionRequest) GetExpireAt() (v int64) {
	return p.ExpireAt
}

func (p *CreatePersonalAccessTokenAndPermissionRequest) GetDurationDay() (v string) {
	return p.DurationDay
}

func (p *CreatePersonalAccessTokenAndPermissionRequest) GetOrganizationID() (v string) {
	return p.OrganizationID
}

var fieldIDToName_CreatePersonalAccessTokenAndPermissionRequest = map[int16]string{
	1: "name",
	2: "expire_at",
	3: "duration_day",
	4: "organization_id",
}

func (p *CreatePersonalAccessTokenAndPermissionRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreatePersonalAccessTokenAndPermissionRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreatePersonalAccessTokenAndPermissionRequest[fieldId]))
}

func (p *CreatePersonalAccessTokenAndPermissionRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *CreatePersonalAccessTokenAndPermissionRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExpireAt = _field
	return nil
}
func (p *CreatePersonalAccessTokenAndPermissionRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DurationDay = _field
	return nil
}
func (p *CreatePersonalAccessTokenAndPermissionRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrganizationID = _field
	return nil
}

func (p *CreatePersonalAccessTokenAndPermissionRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreatePersonalAccessTokenAndPermissionRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreatePersonalAccessTokenAndPermissionRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreatePersonalAccessTokenAndPermissionRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("expire_at", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ExpireAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreatePersonalAccessTokenAndPermissionRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("duration_day", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DurationDay); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CreatePersonalAccessTokenAndPermissionRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("organization_id", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrganizationID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreatePersonalAccessTokenAndPermissionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreatePersonalAccessTokenAndPermissionRequest(%+v)", *p)

}

type PersonalAccessToken struct {
	ID        int64  `thrift:"id,1,required" form:"id,required" json:"id,string,required" query:"id,required"`
	Name      string `thrift:"name,2,required" form:"name,required" json:"name,required" query:"name,required"`
	CreatedAt int64  `thrift:"created_at,3,required" form:"created_at,required" json:"created_at,required" query:"created_at,required"`
	UpdatedAt int64  `thrift:"updated_at,4,required" form:"updated_at,required" json:"updated_at,required" query:"updated_at,required"`
	// -1 means unused
	LastUsedAt int64 `thrift:"last_used_at,5,required" form:"last_used_at,required" json:"last_used_at,required" query:"last_used_at,required"`
	// -1 means indefinite
	ExpireAt int64 `thrift:"expire_at,6,required" form:"expire_at,required" json:"expire_at,required" query:"expire_at,required"`
}

func NewPersonalAccessToken() *PersonalAccessToken {
	return &PersonalAccessToken{}
}

func (p *PersonalAccessToken) InitDefault() {
}

func (p *PersonalAccessToken) GetID() (v int64) {
	return p.ID
}

func (p *PersonalAccessToken) GetName() (v string) {
	return p.Name
}

func (p *PersonalAccessToken) GetCreatedAt() (v int64) {
	return p.CreatedAt
}

func (p *PersonalAccessToken) GetUpdatedAt() (v int64) {
	return p.UpdatedAt
}

func (p *PersonalAccessToken) GetLastUsedAt() (v int64) {
	return p.LastUsedAt
}

func (p *PersonalAccessToken) GetExpireAt() (v int64) {
	return p.ExpireAt
}

var fieldIDToName_PersonalAccessToken = map[int16]string{
	1: "id",
	2: "name",
	3: "created_at",
	4: "updated_at",
	5: "last_used_at",
	6: "expire_at",
}

func (p *PersonalAccessToken) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetName bool = false
	var issetCreatedAt bool = false
	var issetUpdatedAt bool = false
	var issetLastUsedAt bool = false
	var issetExpireAt bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreatedAt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdatedAt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetLastUsedAt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetExpireAt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCreatedAt {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetUpdatedAt {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetLastUsedAt {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetExpireAt {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PersonalAccessToken[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PersonalAccessToken[fieldId]))
}

func (p *PersonalAccessToken) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *PersonalAccessToken) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *PersonalAccessToken) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatedAt = _field
	return nil
}
func (p *PersonalAccessToken) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdatedAt = _field
	return nil
}
func (p *PersonalAccessToken) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LastUsedAt = _field
	return nil
}
func (p *PersonalAccessToken) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExpireAt = _field
	return nil
}

func (p *PersonalAccessToken) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PersonalAccessToken"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PersonalAccessToken) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PersonalAccessToken) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PersonalAccessToken) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("created_at", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreatedAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PersonalAccessToken) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("updated_at", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UpdatedAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *PersonalAccessToken) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("last_used_at", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.LastUsedAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *PersonalAccessToken) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("expire_at", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ExpireAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *PersonalAccessToken) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PersonalAccessToken(%+v)", *p)

}

type CreatePersonalAccessTokenAndPermissionResponseData struct {
	PersonalAccessToken *PersonalAccessToken `thrift:"personal_access_token,1,required" form:"personal_access_token,required" json:"personal_access_token,required" query:"personal_access_token,required"`
	// PAT token plaintext
	Token string `thrift:"token,2,required" form:"token,required" json:"token,required" query:"token,required"`
}

func NewCreatePersonalAccessTokenAndPermissionResponseData() *CreatePersonalAccessTokenAndPermissionResponseData {
	return &CreatePersonalAccessTokenAndPermissionResponseData{}
}

func (p *CreatePersonalAccessTokenAndPermissionResponseData) InitDefault() {
}

var CreatePersonalAccessTokenAndPermissionResponseData_PersonalAccessToken_DEFAULT *PersonalAccessToken

func (p *CreatePersonalAccessTokenAndPermissionResponseData) GetPersonalAccessToken() (v *PersonalAccessToken) {
	if !p.IsSetPersonalAccessToken() {
		return CreatePersonalAccessTokenAndPermissionResponseData_PersonalAccessToken_DEFAULT
	}
	return p.PersonalAccessToken
}

func (p *CreatePersonalAccessTokenAndPermissionResponseData) GetToken() (v string) {
	return p.Token
}

var fieldIDToName_CreatePersonalAccessTokenAndPermissionResponseData = map[int16]string{
	1: "personal_access_token",
	2: "token",
}

func (p *CreatePersonalAccessTokenAndPermissionResponseData) IsSetPersonalAccessToken() bool {
	return p.PersonalAccessToken != nil
}

func (p *CreatePersonalAccessTokenAndPermissionResponseData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetPersonalAccessToken bool = false
	var issetToken bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetPersonalAccessToken = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetToken = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetPersonalAccessToken {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetToken {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreatePersonalAccessTokenAndPermissionResponseData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreatePersonalAccessTokenAndPermissionResponseData[fieldId]))
}

func (p *CreatePersonalAccessTokenAndPermissionResponseData) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPersonalAccessToken()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PersonalAccessToken = _field
	return nil
}
func (p *CreatePersonalAccessTokenAndPermissionResponseData) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Token = _field
	return nil
}

func (p *CreatePersonalAccessTokenAndPermissionResponseData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreatePersonalAccessTokenAndPermissionResponseData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreatePersonalAccessTokenAndPermissionResponseData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("personal_access_token", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.PersonalAccessToken.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreatePersonalAccessTokenAndPermissionResponseData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("token", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Token); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreatePersonalAccessTokenAndPermissionResponseData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreatePersonalAccessTokenAndPermissionResponseData(%+v)", *p)

}

type CreatePersonalAccessTokenAndPermissionResponse struct {
	Data *CreatePersonalAccessTokenAndPermissionResponseData `thrift:"data,1,required" form:"data,required" json:"data,required" query:"data,required"`
	Code int32                                               `thrift:"code,2,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg  string                                              `thrift:"msg,3,required" form:"msg,required" json:"msg,required" query:"msg,required"`
}

func NewCreatePersonalAccessTokenAndPermissionResponse() *CreatePersonalAccessTokenAndPermissionResponse {
	return &CreatePersonalAccessTokenAndPermissionResponse{}
}

func (p *CreatePersonalAccessTokenAndPermissionResponse) InitDefault() {
}

var CreatePersonalAccessTokenAndPermissionResponse_Data_DEFAULT *CreatePersonalAccessTokenAndPermissionResponseData

func (p *CreatePersonalAccessTokenAndPermissionResponse) GetData() (v *CreatePersonalAccessTokenAndPermissionResponseData) {
	if !p.IsSetData() {
		return CreatePersonalAccessTokenAndPermissionResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *CreatePersonalAccessTokenAndPermissionResponse) GetCode() (v int32) {
	return p.Code
}

func (p *CreatePersonalAccessTokenAndPermissionResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_CreatePersonalAccessTokenAndPermissionResponse = map[int16]string{
	1: "data",
	2: "code",
	3: "msg",
}

func (p *CreatePersonalAccessTokenAndPermissionResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *CreatePersonalAccessTokenAndPermissionResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetData bool = false
	var issetCode bool = false
	var issetMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetData = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetData {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCode {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreatePersonalAccessTokenAndPermissionResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreatePersonalAccessTokenAndPermissionResponse[fieldId]))
}

func (p *CreatePersonalAccessTokenAndPermissionResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCreatePersonalAccessTokenAndPermissionResponseData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *CreatePersonalAccessTokenAndPermissionResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *CreatePersonalAccessTokenAndPermissionResponse) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *CreatePersonalAccessTokenAndPermissionResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreatePersonalAccessTokenAndPermissionResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreatePersonalAccessTokenAndPermissionResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreatePersonalAccessTokenAndPermissionResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreatePersonalAccessTokenAndPermissionResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreatePersonalAccessTokenAndPermissionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreatePersonalAccessTokenAndPermissionResponse(%+v)", *p)

}

type ListPersonalAccessTokensRequest struct {
	// organization id
	OrganizationID *string `thrift:"organization_id,1,optional" json:"organization_id,omitempty" query:"organization_id"`
	// zero-indexed
	Page *int64 `thrift:"page,2,optional" json:"page,omitempty" query:"page"`
	// page size
	Size *int64 `thrift:"size,3,optional" json:"size,omitempty" query:"size"`
	// search option
	SearchOption *PatSearchOption `thrift:"search_option,4,optional" json:"search_option,omitempty" query:"search_option"`
}

func NewListPersonalAccessTokensRequest() *ListPersonalAccessTokensRequest {
	return &ListPersonalAccessTokensRequest{}
}

func (p *ListPersonalAccessTokensRequest) InitDefault() {
}

var ListPersonalAccessTokensRequest_OrganizationID_DEFAULT string

func (p *ListPersonalAccessTokensRequest) GetOrganizationID() (v string) {
	if !p.IsSetOrganizationID() {
		return ListPersonalAccessTokensRequest_OrganizationID_DEFAULT
	}
	return *p.OrganizationID
}

var ListPersonalAccessTokensRequest_Page_DEFAULT int64

func (p *ListPersonalAccessTokensRequest) GetPage() (v int64) {
	if !p.IsSetPage() {
		return ListPersonalAccessTokensRequest_Page_DEFAULT
	}
	return *p.Page
}

var ListPersonalAccessTokensRequest_Size_DEFAULT int64

func (p *ListPersonalAccessTokensRequest) GetSize() (v int64) {
	if !p.IsSetSize() {
		return ListPersonalAccessTokensRequest_Size_DEFAULT
	}
	return *p.Size
}

var ListPersonalAccessTokensRequest_SearchOption_DEFAULT PatSearchOption

func (p *ListPersonalAccessTokensRequest) GetSearchOption() (v PatSearchOption) {
	if !p.IsSetSearchOption() {
		return ListPersonalAccessTokensRequest_SearchOption_DEFAULT
	}
	return *p.SearchOption
}

var fieldIDToName_ListPersonalAccessTokensRequest = map[int16]string{
	1: "organization_id",
	2: "page",
	3: "size",
	4: "search_option",
}

func (p *ListPersonalAccessTokensRequest) IsSetOrganizationID() bool {
	return p.OrganizationID != nil
}

func (p *ListPersonalAccessTokensRequest) IsSetPage() bool {
	return p.Page != nil
}

func (p *ListPersonalAccessTokensRequest) IsSetSize() bool {
	return p.Size != nil
}

func (p *ListPersonalAccessTokensRequest) IsSetSearchOption() bool {
	return p.SearchOption != nil
}

func (p *ListPersonalAccessTokensRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListPersonalAccessTokensRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ListPersonalAccessTokensRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OrganizationID = _field
	return nil
}
func (p *ListPersonalAccessTokensRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Page = _field
	return nil
}
func (p *ListPersonalAccessTokensRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Size = _field
	return nil
}
func (p *ListPersonalAccessTokensRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *PatSearchOption
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SearchOption = _field
	return nil
}

func (p *ListPersonalAccessTokensRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListPersonalAccessTokensRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListPersonalAccessTokensRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrganizationID() {
		if err = oprot.WriteFieldBegin("organization_id", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OrganizationID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ListPersonalAccessTokensRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPage() {
		if err = oprot.WriteFieldBegin("page", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Page); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ListPersonalAccessTokensRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSize() {
		if err = oprot.WriteFieldBegin("size", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Size); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ListPersonalAccessTokensRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchOption() {
		if err = oprot.WriteFieldBegin("search_option", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SearchOption); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListPersonalAccessTokensRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListPersonalAccessTokensRequest(%+v)", *p)

}

type PersonalAccessTokenWithCreatorInfo struct {
	ID        int64  `thrift:"id,1,required" form:"id,required" json:"id,string,required" query:"id,required"`
	Name      string `thrift:"name,2,required" form:"name,required" json:"name,required" query:"name,required"`
	CreatedAt int64  `thrift:"created_at,3,required" form:"created_at,required" json:"created_at,required" query:"created_at,required"`
	UpdatedAt int64  `thrift:"updated_at,4,required" form:"updated_at,required" json:"updated_at,required" query:"updated_at,required"`
	// -1 means unused
	LastUsedAt int64 `thrift:"last_used_at,5,required" form:"last_used_at,required" json:"last_used_at,required" query:"last_used_at,required"`
	// -1 means indefinite
	ExpireAt          int64      `thrift:"expire_at,6,required" form:"expire_at,required" json:"expire_at,required" query:"expire_at,required"`
	CreatorName       string     `thrift:"creator_name,7" form:"creator_name" json:"creator_name" query:"creator_name"`
	CreatorUniqueName string     `thrift:"creator_unique_name,8" form:"creator_unique_name" json:"creator_unique_name" query:"creator_unique_name"`
	CreatorAvatarURL  string     `thrift:"creator_avatar_url,9" form:"creator_avatar_url" json:"creator_avatar_url" query:"creator_avatar_url"`
	CreatorIconURL    string     `thrift:"creator_icon_url,10" form:"creator_icon_url" json:"creator_icon_url" query:"creator_icon_url"`
	Locked            bool       `thrift:"locked,11" form:"locked" json:"locked" query:"locked"`
	CreatorStatus     UserStatus `thrift:"creator_status,12" form:"creator_status" json:"creator_status" query:"creator_status"`
}

func NewPersonalAccessTokenWithCreatorInfo() *PersonalAccessTokenWithCreatorInfo {
	return &PersonalAccessTokenWithCreatorInfo{}
}

func (p *PersonalAccessTokenWithCreatorInfo) InitDefault() {
}

func (p *PersonalAccessTokenWithCreatorInfo) GetID() (v int64) {
	return p.ID
}

func (p *PersonalAccessTokenWithCreatorInfo) GetName() (v string) {
	return p.Name
}

func (p *PersonalAccessTokenWithCreatorInfo) GetCreatedAt() (v int64) {
	return p.CreatedAt
}

func (p *PersonalAccessTokenWithCreatorInfo) GetUpdatedAt() (v int64) {
	return p.UpdatedAt
}

func (p *PersonalAccessTokenWithCreatorInfo) GetLastUsedAt() (v int64) {
	return p.LastUsedAt
}

func (p *PersonalAccessTokenWithCreatorInfo) GetExpireAt() (v int64) {
	return p.ExpireAt
}

func (p *PersonalAccessTokenWithCreatorInfo) GetCreatorName() (v string) {
	return p.CreatorName
}

func (p *PersonalAccessTokenWithCreatorInfo) GetCreatorUniqueName() (v string) {
	return p.CreatorUniqueName
}

func (p *PersonalAccessTokenWithCreatorInfo) GetCreatorAvatarURL() (v string) {
	return p.CreatorAvatarURL
}

func (p *PersonalAccessTokenWithCreatorInfo) GetCreatorIconURL() (v string) {
	return p.CreatorIconURL
}

func (p *PersonalAccessTokenWithCreatorInfo) GetLocked() (v bool) {
	return p.Locked
}

func (p *PersonalAccessTokenWithCreatorInfo) GetCreatorStatus() (v UserStatus) {
	return p.CreatorStatus
}

var fieldIDToName_PersonalAccessTokenWithCreatorInfo = map[int16]string{
	1:  "id",
	2:  "name",
	3:  "created_at",
	4:  "updated_at",
	5:  "last_used_at",
	6:  "expire_at",
	7:  "creator_name",
	8:  "creator_unique_name",
	9:  "creator_avatar_url",
	10: "creator_icon_url",
	11: "locked",
	12: "creator_status",
}

func (p *PersonalAccessTokenWithCreatorInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetName bool = false
	var issetCreatedAt bool = false
	var issetUpdatedAt bool = false
	var issetLastUsedAt bool = false
	var issetExpireAt bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreatedAt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdatedAt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetLastUsedAt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetExpireAt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCreatedAt {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetUpdatedAt {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetLastUsedAt {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetExpireAt {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PersonalAccessTokenWithCreatorInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PersonalAccessTokenWithCreatorInfo[fieldId]))
}

func (p *PersonalAccessTokenWithCreatorInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *PersonalAccessTokenWithCreatorInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *PersonalAccessTokenWithCreatorInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatedAt = _field
	return nil
}
func (p *PersonalAccessTokenWithCreatorInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdatedAt = _field
	return nil
}
func (p *PersonalAccessTokenWithCreatorInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LastUsedAt = _field
	return nil
}
func (p *PersonalAccessTokenWithCreatorInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExpireAt = _field
	return nil
}
func (p *PersonalAccessTokenWithCreatorInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatorName = _field
	return nil
}
func (p *PersonalAccessTokenWithCreatorInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatorUniqueName = _field
	return nil
}
func (p *PersonalAccessTokenWithCreatorInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatorAvatarURL = _field
	return nil
}
func (p *PersonalAccessTokenWithCreatorInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatorIconURL = _field
	return nil
}
func (p *PersonalAccessTokenWithCreatorInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Locked = _field
	return nil
}
func (p *PersonalAccessTokenWithCreatorInfo) ReadField12(iprot thrift.TProtocol) error {

	var _field UserStatus
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatorStatus = _field
	return nil
}

func (p *PersonalAccessTokenWithCreatorInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PersonalAccessTokenWithCreatorInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PersonalAccessTokenWithCreatorInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PersonalAccessTokenWithCreatorInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PersonalAccessTokenWithCreatorInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("created_at", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreatedAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PersonalAccessTokenWithCreatorInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("updated_at", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UpdatedAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *PersonalAccessTokenWithCreatorInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("last_used_at", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.LastUsedAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *PersonalAccessTokenWithCreatorInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("expire_at", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ExpireAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *PersonalAccessTokenWithCreatorInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creator_name", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreatorName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *PersonalAccessTokenWithCreatorInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creator_unique_name", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreatorUniqueName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *PersonalAccessTokenWithCreatorInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creator_avatar_url", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreatorAvatarURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *PersonalAccessTokenWithCreatorInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creator_icon_url", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreatorIconURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *PersonalAccessTokenWithCreatorInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("locked", thrift.BOOL, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Locked); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *PersonalAccessTokenWithCreatorInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creator_status", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreatorStatus); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *PersonalAccessTokenWithCreatorInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PersonalAccessTokenWithCreatorInfo(%+v)", *p)

}

type ListPersonalAccessTokensResponse struct {
	Data *ListPersonalAccessTokensResponseData `thrift:"data,1,required" form:"data,required" json:"data,required" query:"data,required"`
	Code int32                                 `thrift:"code,2,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg  string                                `thrift:"msg,3,required" form:"msg,required" json:"msg,required" query:"msg,required"`
}

func NewListPersonalAccessTokensResponse() *ListPersonalAccessTokensResponse {
	return &ListPersonalAccessTokensResponse{}
}

func (p *ListPersonalAccessTokensResponse) InitDefault() {
}

var ListPersonalAccessTokensResponse_Data_DEFAULT *ListPersonalAccessTokensResponseData

func (p *ListPersonalAccessTokensResponse) GetData() (v *ListPersonalAccessTokensResponseData) {
	if !p.IsSetData() {
		return ListPersonalAccessTokensResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *ListPersonalAccessTokensResponse) GetCode() (v int32) {
	return p.Code
}

func (p *ListPersonalAccessTokensResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_ListPersonalAccessTokensResponse = map[int16]string{
	1: "data",
	2: "code",
	3: "msg",
}

func (p *ListPersonalAccessTokensResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *ListPersonalAccessTokensResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetData bool = false
	var issetCode bool = false
	var issetMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetData = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetData {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCode {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListPersonalAccessTokensResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListPersonalAccessTokensResponse[fieldId]))
}

func (p *ListPersonalAccessTokensResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewListPersonalAccessTokensResponseData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *ListPersonalAccessTokensResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *ListPersonalAccessTokensResponse) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *ListPersonalAccessTokensResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListPersonalAccessTokensResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListPersonalAccessTokensResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ListPersonalAccessTokensResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ListPersonalAccessTokensResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ListPersonalAccessTokensResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListPersonalAccessTokensResponse(%+v)", *p)

}

type ListPersonalAccessTokensResponseData struct {
	// PAT list
	PersonalAccessTokens []*PersonalAccessTokenWithCreatorInfo `thrift:"personal_access_tokens,1,required" form:"personal_access_tokens,required" json:"personal_access_tokens,required" query:"personal_access_tokens,required"`
	// Is there any more data?
	HasMore bool `thrift:"has_more,2" form:"has_more" json:"has_more" query:"has_more"`
}

func NewListPersonalAccessTokensResponseData() *ListPersonalAccessTokensResponseData {
	return &ListPersonalAccessTokensResponseData{}
}

func (p *ListPersonalAccessTokensResponseData) InitDefault() {
}

func (p *ListPersonalAccessTokensResponseData) GetPersonalAccessTokens() (v []*PersonalAccessTokenWithCreatorInfo) {
	return p.PersonalAccessTokens
}

func (p *ListPersonalAccessTokensResponseData) GetHasMore() (v bool) {
	return p.HasMore
}

var fieldIDToName_ListPersonalAccessTokensResponseData = map[int16]string{
	1: "personal_access_tokens",
	2: "has_more",
}

func (p *ListPersonalAccessTokensResponseData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetPersonalAccessTokens bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetPersonalAccessTokens = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetPersonalAccessTokens {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListPersonalAccessTokensResponseData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListPersonalAccessTokensResponseData[fieldId]))
}

func (p *ListPersonalAccessTokensResponseData) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PersonalAccessTokenWithCreatorInfo, 0, size)
	values := make([]PersonalAccessTokenWithCreatorInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PersonalAccessTokens = _field
	return nil
}
func (p *ListPersonalAccessTokensResponseData) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HasMore = _field
	return nil
}

func (p *ListPersonalAccessTokensResponseData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListPersonalAccessTokensResponseData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListPersonalAccessTokensResponseData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("personal_access_tokens", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PersonalAccessTokens)); err != nil {
		return err
	}
	for _, v := range p.PersonalAccessTokens {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ListPersonalAccessTokensResponseData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("has_more", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HasMore); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ListPersonalAccessTokensResponseData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListPersonalAccessTokensResponseData(%+v)", *p)

}

type DeletePersonalAccessTokenAndPermissionRequest struct {
	// PAT Id
	ID int64 `thrift:"id,1,required" form:"id,required" json:"id,string,required" query:"id,required"`
}

func NewDeletePersonalAccessTokenAndPermissionRequest() *DeletePersonalAccessTokenAndPermissionRequest {
	return &DeletePersonalAccessTokenAndPermissionRequest{}
}

func (p *DeletePersonalAccessTokenAndPermissionRequest) InitDefault() {
}

func (p *DeletePersonalAccessTokenAndPermissionRequest) GetID() (v int64) {
	return p.ID
}

var fieldIDToName_DeletePersonalAccessTokenAndPermissionRequest = map[int16]string{
	1: "id",
}

func (p *DeletePersonalAccessTokenAndPermissionRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeletePersonalAccessTokenAndPermissionRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeletePersonalAccessTokenAndPermissionRequest[fieldId]))
}

func (p *DeletePersonalAccessTokenAndPermissionRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}

func (p *DeletePersonalAccessTokenAndPermissionRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeletePersonalAccessTokenAndPermissionRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeletePersonalAccessTokenAndPermissionRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeletePersonalAccessTokenAndPermissionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeletePersonalAccessTokenAndPermissionRequest(%+v)", *p)

}

type DeletePersonalAccessTokenAndPermissionResponse struct {
	Code int32  `thrift:"code,1,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg  string `thrift:"msg,2,required" form:"msg,required" json:"msg,required" query:"msg,required"`
}

func NewDeletePersonalAccessTokenAndPermissionResponse() *DeletePersonalAccessTokenAndPermissionResponse {
	return &DeletePersonalAccessTokenAndPermissionResponse{}
}

func (p *DeletePersonalAccessTokenAndPermissionResponse) InitDefault() {
}

func (p *DeletePersonalAccessTokenAndPermissionResponse) GetCode() (v int32) {
	return p.Code
}

func (p *DeletePersonalAccessTokenAndPermissionResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_DeletePersonalAccessTokenAndPermissionResponse = map[int16]string{
	1: "code",
	2: "msg",
}

func (p *DeletePersonalAccessTokenAndPermissionResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeletePersonalAccessTokenAndPermissionResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeletePersonalAccessTokenAndPermissionResponse[fieldId]))
}

func (p *DeletePersonalAccessTokenAndPermissionResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *DeletePersonalAccessTokenAndPermissionResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *DeletePersonalAccessTokenAndPermissionResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeletePersonalAccessTokenAndPermissionResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeletePersonalAccessTokenAndPermissionResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DeletePersonalAccessTokenAndPermissionResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeletePersonalAccessTokenAndPermissionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeletePersonalAccessTokenAndPermissionResponse(%+v)", *p)

}

type GetPersonalAccessTokenAndPermissionRequest struct {
	// PAT Id
	ID int64 `thrift:"id,1,required" json:"id,string,required" query:"id,required"`
}

func NewGetPersonalAccessTokenAndPermissionRequest() *GetPersonalAccessTokenAndPermissionRequest {
	return &GetPersonalAccessTokenAndPermissionRequest{}
}

func (p *GetPersonalAccessTokenAndPermissionRequest) InitDefault() {
}

func (p *GetPersonalAccessTokenAndPermissionRequest) GetID() (v int64) {
	return p.ID
}

var fieldIDToName_GetPersonalAccessTokenAndPermissionRequest = map[int16]string{
	1: "id",
}

func (p *GetPersonalAccessTokenAndPermissionRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetPersonalAccessTokenAndPermissionRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetPersonalAccessTokenAndPermissionRequest[fieldId]))
}

func (p *GetPersonalAccessTokenAndPermissionRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}

func (p *GetPersonalAccessTokenAndPermissionRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetPersonalAccessTokenAndPermissionRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetPersonalAccessTokenAndPermissionRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetPersonalAccessTokenAndPermissionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPersonalAccessTokenAndPermissionRequest(%+v)", *p)

}

type GetPersonalAccessTokenAndPermissionResponseData struct {
	PersonalAccessToken *PersonalAccessToken `thrift:"personal_access_token,1,required" form:"personal_access_token,required" json:"personal_access_token,required" query:"personal_access_token,required"`
}

func NewGetPersonalAccessTokenAndPermissionResponseData() *GetPersonalAccessTokenAndPermissionResponseData {
	return &GetPersonalAccessTokenAndPermissionResponseData{}
}

func (p *GetPersonalAccessTokenAndPermissionResponseData) InitDefault() {
}

var GetPersonalAccessTokenAndPermissionResponseData_PersonalAccessToken_DEFAULT *PersonalAccessToken

func (p *GetPersonalAccessTokenAndPermissionResponseData) GetPersonalAccessToken() (v *PersonalAccessToken) {
	if !p.IsSetPersonalAccessToken() {
		return GetPersonalAccessTokenAndPermissionResponseData_PersonalAccessToken_DEFAULT
	}
	return p.PersonalAccessToken
}

var fieldIDToName_GetPersonalAccessTokenAndPermissionResponseData = map[int16]string{
	1: "personal_access_token",
}

func (p *GetPersonalAccessTokenAndPermissionResponseData) IsSetPersonalAccessToken() bool {
	return p.PersonalAccessToken != nil
}

func (p *GetPersonalAccessTokenAndPermissionResponseData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetPersonalAccessToken bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetPersonalAccessToken = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetPersonalAccessToken {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetPersonalAccessTokenAndPermissionResponseData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetPersonalAccessTokenAndPermissionResponseData[fieldId]))
}

func (p *GetPersonalAccessTokenAndPermissionResponseData) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPersonalAccessToken()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PersonalAccessToken = _field
	return nil
}

func (p *GetPersonalAccessTokenAndPermissionResponseData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetPersonalAccessTokenAndPermissionResponseData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetPersonalAccessTokenAndPermissionResponseData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("personal_access_token", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.PersonalAccessToken.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetPersonalAccessTokenAndPermissionResponseData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPersonalAccessTokenAndPermissionResponseData(%+v)", *p)

}

type GetPersonalAccessTokenAndPermissionResponse struct {
	Data *GetPersonalAccessTokenAndPermissionResponseData `thrift:"data,1,required" form:"data,required" json:"data,required" query:"data,required"`
	Code int32                                            `thrift:"code,2,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg  string                                           `thrift:"msg,3,required" form:"msg,required" json:"msg,required" query:"msg,required"`
}

func NewGetPersonalAccessTokenAndPermissionResponse() *GetPersonalAccessTokenAndPermissionResponse {
	return &GetPersonalAccessTokenAndPermissionResponse{}
}

func (p *GetPersonalAccessTokenAndPermissionResponse) InitDefault() {
}

var GetPersonalAccessTokenAndPermissionResponse_Data_DEFAULT *GetPersonalAccessTokenAndPermissionResponseData

func (p *GetPersonalAccessTokenAndPermissionResponse) GetData() (v *GetPersonalAccessTokenAndPermissionResponseData) {
	if !p.IsSetData() {
		return GetPersonalAccessTokenAndPermissionResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *GetPersonalAccessTokenAndPermissionResponse) GetCode() (v int32) {
	return p.Code
}

func (p *GetPersonalAccessTokenAndPermissionResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_GetPersonalAccessTokenAndPermissionResponse = map[int16]string{
	1: "data",
	2: "code",
	3: "msg",
}

func (p *GetPersonalAccessTokenAndPermissionResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *GetPersonalAccessTokenAndPermissionResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetData bool = false
	var issetCode bool = false
	var issetMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetData = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetData {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCode {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetPersonalAccessTokenAndPermissionResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetPersonalAccessTokenAndPermissionResponse[fieldId]))
}

func (p *GetPersonalAccessTokenAndPermissionResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetPersonalAccessTokenAndPermissionResponseData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *GetPersonalAccessTokenAndPermissionResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *GetPersonalAccessTokenAndPermissionResponse) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *GetPersonalAccessTokenAndPermissionResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetPersonalAccessTokenAndPermissionResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetPersonalAccessTokenAndPermissionResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetPersonalAccessTokenAndPermissionResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetPersonalAccessTokenAndPermissionResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *GetPersonalAccessTokenAndPermissionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPersonalAccessTokenAndPermissionResponse(%+v)", *p)

}

type UpdatePersonalAccessTokenAndPermissionRequest struct {
	// PAT Id
	ID int64 `thrift:"id,1,required" form:"id,required" json:"id,string,required" query:"id,required"`
	// PAT name
	Name string `thrift:"name,2" form:"name" json:"name" query:"name"`
}

func NewUpdatePersonalAccessTokenAndPermissionRequest() *UpdatePersonalAccessTokenAndPermissionRequest {
	return &UpdatePersonalAccessTokenAndPermissionRequest{}
}

func (p *UpdatePersonalAccessTokenAndPermissionRequest) InitDefault() {
}

func (p *UpdatePersonalAccessTokenAndPermissionRequest) GetID() (v int64) {
	return p.ID
}

func (p *UpdatePersonalAccessTokenAndPermissionRequest) GetName() (v string) {
	return p.Name
}

var fieldIDToName_UpdatePersonalAccessTokenAndPermissionRequest = map[int16]string{
	1: "id",
	2: "name",
}

func (p *UpdatePersonalAccessTokenAndPermissionRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdatePersonalAccessTokenAndPermissionRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdatePersonalAccessTokenAndPermissionRequest[fieldId]))
}

func (p *UpdatePersonalAccessTokenAndPermissionRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *UpdatePersonalAccessTokenAndPermissionRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}

func (p *UpdatePersonalAccessTokenAndPermissionRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdatePersonalAccessTokenAndPermissionRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdatePersonalAccessTokenAndPermissionRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpdatePersonalAccessTokenAndPermissionRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UpdatePersonalAccessTokenAndPermissionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdatePersonalAccessTokenAndPermissionRequest(%+v)", *p)

}

type UpdatePersonalAccessTokenAndPermissionResponse struct {
	Code int32  `thrift:"code,1,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg  string `thrift:"msg,2,required" form:"msg,required" json:"msg,required" query:"msg,required"`
}

func NewUpdatePersonalAccessTokenAndPermissionResponse() *UpdatePersonalAccessTokenAndPermissionResponse {
	return &UpdatePersonalAccessTokenAndPermissionResponse{}
}

func (p *UpdatePersonalAccessTokenAndPermissionResponse) InitDefault() {
}

func (p *UpdatePersonalAccessTokenAndPermissionResponse) GetCode() (v int32) {
	return p.Code
}

func (p *UpdatePersonalAccessTokenAndPermissionResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_UpdatePersonalAccessTokenAndPermissionResponse = map[int16]string{
	1: "code",
	2: "msg",
}

func (p *UpdatePersonalAccessTokenAndPermissionResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdatePersonalAccessTokenAndPermissionResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdatePersonalAccessTokenAndPermissionResponse[fieldId]))
}

func (p *UpdatePersonalAccessTokenAndPermissionResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *UpdatePersonalAccessTokenAndPermissionResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *UpdatePersonalAccessTokenAndPermissionResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdatePersonalAccessTokenAndPermissionResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdatePersonalAccessTokenAndPermissionResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpdatePersonalAccessTokenAndPermissionResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UpdatePersonalAccessTokenAndPermissionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdatePersonalAccessTokenAndPermissionResponse(%+v)", *p)

}
