// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package passport

import (
	"context"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

type AppUserInfo struct {
	UserUniqueName string `thrift:"user_unique_name,1,required" form:"user_unique_name,required" json:"user_unique_name,required" query:"user_unique_name,required"`
}

func NewAppUserInfo() *AppUserInfo {
	return &AppUserInfo{}
}

func (p *AppUserInfo) InitDefault() {
}

func (p *AppUserInfo) GetUserUniqueName() (v string) {
	return p.UserUniqueName
}

var fieldIDToName_AppUserInfo = map[int16]string{
	1: "user_unique_name",
}

func (p *AppUserInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetUserUniqueName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserUniqueName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetUserUniqueName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AppUserInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AppUserInfo[fieldId]))
}

func (p *AppUserInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserUniqueName = _field
	return nil
}

func (p *AppUserInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("AppUserInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AppUserInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_unique_name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserUniqueName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AppUserInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppUserInfo(%+v)", *p)

}

type User struct {
	// Align with the original interface field name
	UserIDStr      int64        `thrift:"user_id_str,1,required" form:"user_id_str,required" json:"user_id_str,string,required" query:"user_id_str,required"`
	Name           string       `thrift:"name,2,required" form:"name,required" json:"name,required" query:"name,required"`
	UserUniqueName string       `thrift:"user_unique_name,3,required" form:"user_unique_name,required" json:"user_unique_name,required" query:"user_unique_name,required"`
	Email          string       `thrift:"email,4,required" form:"email,required" json:"email,required" query:"email,required"`
	Description    string       `thrift:"description,5,required" form:"description,required" json:"description,required" query:"description,required"`
	AvatarURL      string       `thrift:"avatar_url,6,required" form:"avatar_url,required" json:"avatar_url,required" query:"avatar_url,required"`
	ScreenName     *string      `thrift:"screen_name,7,optional" form:"screen_name" json:"screen_name,omitempty" query:"screen_name"`
	AppUserInfo    *AppUserInfo `thrift:"app_user_info,8,optional" form:"app_user_info" json:"app_user_info,omitempty" query:"app_user_info"`
	Locale         *string      `thrift:"locale,9,optional" form:"locale" json:"locale,omitempty" query:"locale"`
	// unix timestamp in seconds
	UserCreateTime int64 `thrift:"user_create_time,10" form:"user_create_time" json:"user_create_time" query:"user_create_time"`
}

func NewUser() *User {
	return &User{}
}

func (p *User) InitDefault() {
}

func (p *User) GetUserIDStr() (v int64) {
	return p.UserIDStr
}

func (p *User) GetName() (v string) {
	return p.Name
}

func (p *User) GetUserUniqueName() (v string) {
	return p.UserUniqueName
}

func (p *User) GetEmail() (v string) {
	return p.Email
}

func (p *User) GetDescription() (v string) {
	return p.Description
}

func (p *User) GetAvatarURL() (v string) {
	return p.AvatarURL
}

var User_ScreenName_DEFAULT string

func (p *User) GetScreenName() (v string) {
	if !p.IsSetScreenName() {
		return User_ScreenName_DEFAULT
	}
	return *p.ScreenName
}

var User_AppUserInfo_DEFAULT *AppUserInfo

func (p *User) GetAppUserInfo() (v *AppUserInfo) {
	if !p.IsSetAppUserInfo() {
		return User_AppUserInfo_DEFAULT
	}
	return p.AppUserInfo
}

var User_Locale_DEFAULT string

func (p *User) GetLocale() (v string) {
	if !p.IsSetLocale() {
		return User_Locale_DEFAULT
	}
	return *p.Locale
}

func (p *User) GetUserCreateTime() (v int64) {
	return p.UserCreateTime
}

var fieldIDToName_User = map[int16]string{
	1:  "user_id_str",
	2:  "name",
	3:  "user_unique_name",
	4:  "email",
	5:  "description",
	6:  "avatar_url",
	7:  "screen_name",
	8:  "app_user_info",
	9:  "locale",
	10: "user_create_time",
}

func (p *User) IsSetScreenName() bool {
	return p.ScreenName != nil
}

func (p *User) IsSetAppUserInfo() bool {
	return p.AppUserInfo != nil
}

func (p *User) IsSetLocale() bool {
	return p.Locale != nil
}

func (p *User) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetUserIDStr bool = false
	var issetName bool = false
	var issetUserUniqueName bool = false
	var issetEmail bool = false
	var issetDescription bool = false
	var issetAvatarURL bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserIDStr = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserUniqueName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEmail = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescription = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetAvatarURL = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetUserIDStr {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUserUniqueName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEmail {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetDescription {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetAvatarURL {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_User[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_User[fieldId]))
}

func (p *User) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserIDStr = _field
	return nil
}
func (p *User) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *User) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserUniqueName = _field
	return nil
}
func (p *User) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Email = _field
	return nil
}
func (p *User) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *User) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AvatarURL = _field
	return nil
}
func (p *User) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ScreenName = _field
	return nil
}
func (p *User) ReadField8(iprot thrift.TProtocol) error {
	_field := NewAppUserInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.AppUserInfo = _field
	return nil
}
func (p *User) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Locale = _field
	return nil
}
func (p *User) ReadField10(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserCreateTime = _field
	return nil
}

func (p *User) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("User"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *User) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_id_str", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserIDStr); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *User) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *User) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_unique_name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserUniqueName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *User) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("email", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Email); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *User) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("description", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *User) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("avatar_url", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AvatarURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *User) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetScreenName() {
		if err = oprot.WriteFieldBegin("screen_name", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ScreenName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *User) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetAppUserInfo() {
		if err = oprot.WriteFieldBegin("app_user_info", thrift.STRUCT, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.AppUserInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *User) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetLocale() {
		if err = oprot.WriteFieldBegin("locale", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Locale); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *User) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_create_time", thrift.I64, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserCreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *User) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("User(%+v)", *p)

}

type PassportWebEmailRegisterV2PostRequest struct {
	Password string `thrift:"password,11,required" form:"password,required" json:"password,required" query:"password,required"`
	Email    string `thrift:"email,23" form:"email" json:"email" query:"email"`
}

func NewPassportWebEmailRegisterV2PostRequest() *PassportWebEmailRegisterV2PostRequest {
	return &PassportWebEmailRegisterV2PostRequest{}
}

func (p *PassportWebEmailRegisterV2PostRequest) InitDefault() {
}

func (p *PassportWebEmailRegisterV2PostRequest) GetPassword() (v string) {
	return p.Password
}

func (p *PassportWebEmailRegisterV2PostRequest) GetEmail() (v string) {
	return p.Email
}

var fieldIDToName_PassportWebEmailRegisterV2PostRequest = map[int16]string{
	11: "password",
	23: "email",
}

func (p *PassportWebEmailRegisterV2PostRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetPassword bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetPassword = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField23(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetPassword {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportWebEmailRegisterV2PostRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PassportWebEmailRegisterV2PostRequest[fieldId]))
}

func (p *PassportWebEmailRegisterV2PostRequest) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Password = _field
	return nil
}
func (p *PassportWebEmailRegisterV2PostRequest) ReadField23(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Email = _field
	return nil
}

func (p *PassportWebEmailRegisterV2PostRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebEmailRegisterV2PostRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField23(oprot); err != nil {
			fieldId = 23
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportWebEmailRegisterV2PostRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("password", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Password); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *PassportWebEmailRegisterV2PostRequest) writeField23(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("email", thrift.STRING, 23); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Email); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 end error: ", p), err)
}

func (p *PassportWebEmailRegisterV2PostRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportWebEmailRegisterV2PostRequest(%+v)", *p)

}

type PassportWebEmailRegisterV2PostResponse struct {
	Data *User  `thrift:"data,1,required" form:"data,required" json:"data,required" query:"data,required"`
	Code int32  `thrift:"code,253,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg  string `thrift:"msg,254,required" form:"msg,required" json:"msg,required" query:"msg,required"`
}

func NewPassportWebEmailRegisterV2PostResponse() *PassportWebEmailRegisterV2PostResponse {
	return &PassportWebEmailRegisterV2PostResponse{}
}

func (p *PassportWebEmailRegisterV2PostResponse) InitDefault() {
}

var PassportWebEmailRegisterV2PostResponse_Data_DEFAULT *User

func (p *PassportWebEmailRegisterV2PostResponse) GetData() (v *User) {
	if !p.IsSetData() {
		return PassportWebEmailRegisterV2PostResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *PassportWebEmailRegisterV2PostResponse) GetCode() (v int32) {
	return p.Code
}

func (p *PassportWebEmailRegisterV2PostResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_PassportWebEmailRegisterV2PostResponse = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
}

func (p *PassportWebEmailRegisterV2PostResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *PassportWebEmailRegisterV2PostResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetData bool = false
	var issetCode bool = false
	var issetMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetData = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetData {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCode {
		fieldId = 253
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportWebEmailRegisterV2PostResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PassportWebEmailRegisterV2PostResponse[fieldId]))
}

func (p *PassportWebEmailRegisterV2PostResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUser()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *PassportWebEmailRegisterV2PostResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *PassportWebEmailRegisterV2PostResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *PassportWebEmailRegisterV2PostResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebEmailRegisterV2PostResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportWebEmailRegisterV2PostResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PassportWebEmailRegisterV2PostResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *PassportWebEmailRegisterV2PostResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}

func (p *PassportWebEmailRegisterV2PostResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportWebEmailRegisterV2PostResponse(%+v)", *p)

}

type PassportWebLogoutGetRequest struct {
}

func NewPassportWebLogoutGetRequest() *PassportWebLogoutGetRequest {
	return &PassportWebLogoutGetRequest{}
}

func (p *PassportWebLogoutGetRequest) InitDefault() {
}

var fieldIDToName_PassportWebLogoutGetRequest = map[int16]string{}

func (p *PassportWebLogoutGetRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportWebLogoutGetRequest) Write(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteStructBegin("PassportWebLogoutGetRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportWebLogoutGetRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportWebLogoutGetRequest(%+v)", *p)

}

type PassportWebLogoutGetResponse struct {
	RedirectURL string `thrift:"redirect_url,1,required" form:"redirect_url,required" json:"redirect_url,required" query:"redirect_url,required"`
	Code        int32  `thrift:"code,253,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg         string `thrift:"msg,254,required" form:"msg,required" json:"msg,required" query:"msg,required"`
}

func NewPassportWebLogoutGetResponse() *PassportWebLogoutGetResponse {
	return &PassportWebLogoutGetResponse{}
}

func (p *PassportWebLogoutGetResponse) InitDefault() {
}

func (p *PassportWebLogoutGetResponse) GetRedirectURL() (v string) {
	return p.RedirectURL
}

func (p *PassportWebLogoutGetResponse) GetCode() (v int32) {
	return p.Code
}

func (p *PassportWebLogoutGetResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_PassportWebLogoutGetResponse = map[int16]string{
	1:   "redirect_url",
	253: "code",
	254: "msg",
}

func (p *PassportWebLogoutGetResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRedirectURL bool = false
	var issetCode bool = false
	var issetMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRedirectURL = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRedirectURL {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCode {
		fieldId = 253
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportWebLogoutGetResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PassportWebLogoutGetResponse[fieldId]))
}

func (p *PassportWebLogoutGetResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RedirectURL = _field
	return nil
}
func (p *PassportWebLogoutGetResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *PassportWebLogoutGetResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *PassportWebLogoutGetResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebLogoutGetResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportWebLogoutGetResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("redirect_url", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RedirectURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PassportWebLogoutGetResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *PassportWebLogoutGetResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}

func (p *PassportWebLogoutGetResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportWebLogoutGetResponse(%+v)", *p)

}

type PassportWebEmailLoginPostRequest struct {
	Email    string `thrift:"email,6,required" form:"email,required" json:"email,required" query:"email,required"`
	Password string `thrift:"password,7,required" form:"password,required" json:"password,required" query:"password,required"`
}

func NewPassportWebEmailLoginPostRequest() *PassportWebEmailLoginPostRequest {
	return &PassportWebEmailLoginPostRequest{}
}

func (p *PassportWebEmailLoginPostRequest) InitDefault() {
}

func (p *PassportWebEmailLoginPostRequest) GetEmail() (v string) {
	return p.Email
}

func (p *PassportWebEmailLoginPostRequest) GetPassword() (v string) {
	return p.Password
}

var fieldIDToName_PassportWebEmailLoginPostRequest = map[int16]string{
	6: "email",
	7: "password",
}

func (p *PassportWebEmailLoginPostRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetEmail bool = false
	var issetPassword bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetEmail = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetPassword = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetEmail {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetPassword {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportWebEmailLoginPostRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PassportWebEmailLoginPostRequest[fieldId]))
}

func (p *PassportWebEmailLoginPostRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Email = _field
	return nil
}
func (p *PassportWebEmailLoginPostRequest) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Password = _field
	return nil
}

func (p *PassportWebEmailLoginPostRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebEmailLoginPostRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportWebEmailLoginPostRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("email", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Email); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *PassportWebEmailLoginPostRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("password", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Password); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *PassportWebEmailLoginPostRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportWebEmailLoginPostRequest(%+v)", *p)

}

type PassportWebEmailLoginPostResponse struct {
	Data *User  `thrift:"data,1,required" form:"data,required" json:"data,required" query:"data,required"`
	Code int32  `thrift:"code,253,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg  string `thrift:"msg,254,required" form:"msg,required" json:"msg,required" query:"msg,required"`
}

func NewPassportWebEmailLoginPostResponse() *PassportWebEmailLoginPostResponse {
	return &PassportWebEmailLoginPostResponse{}
}

func (p *PassportWebEmailLoginPostResponse) InitDefault() {
}

var PassportWebEmailLoginPostResponse_Data_DEFAULT *User

func (p *PassportWebEmailLoginPostResponse) GetData() (v *User) {
	if !p.IsSetData() {
		return PassportWebEmailLoginPostResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *PassportWebEmailLoginPostResponse) GetCode() (v int32) {
	return p.Code
}

func (p *PassportWebEmailLoginPostResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_PassportWebEmailLoginPostResponse = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
}

func (p *PassportWebEmailLoginPostResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *PassportWebEmailLoginPostResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetData bool = false
	var issetCode bool = false
	var issetMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetData = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetData {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCode {
		fieldId = 253
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportWebEmailLoginPostResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PassportWebEmailLoginPostResponse[fieldId]))
}

func (p *PassportWebEmailLoginPostResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUser()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *PassportWebEmailLoginPostResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *PassportWebEmailLoginPostResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *PassportWebEmailLoginPostResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebEmailLoginPostResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportWebEmailLoginPostResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PassportWebEmailLoginPostResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *PassportWebEmailLoginPostResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}

func (p *PassportWebEmailLoginPostResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportWebEmailLoginPostResponse(%+v)", *p)

}

type PassportWebEmailPasswordResetGetRequest struct {
	Password string `thrift:"password,1" form:"password" json:"password" query:"password"`
	Code     string `thrift:"code,2" form:"code" json:"code" query:"code"`
	Email    string `thrift:"email,3" form:"email" json:"email" query:"email"`
}

func NewPassportWebEmailPasswordResetGetRequest() *PassportWebEmailPasswordResetGetRequest {
	return &PassportWebEmailPasswordResetGetRequest{}
}

func (p *PassportWebEmailPasswordResetGetRequest) InitDefault() {
}

func (p *PassportWebEmailPasswordResetGetRequest) GetPassword() (v string) {
	return p.Password
}

func (p *PassportWebEmailPasswordResetGetRequest) GetCode() (v string) {
	return p.Code
}

func (p *PassportWebEmailPasswordResetGetRequest) GetEmail() (v string) {
	return p.Email
}

var fieldIDToName_PassportWebEmailPasswordResetGetRequest = map[int16]string{
	1: "password",
	2: "code",
	3: "email",
}

func (p *PassportWebEmailPasswordResetGetRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportWebEmailPasswordResetGetRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportWebEmailPasswordResetGetRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Password = _field
	return nil
}
func (p *PassportWebEmailPasswordResetGetRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *PassportWebEmailPasswordResetGetRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Email = _field
	return nil
}

func (p *PassportWebEmailPasswordResetGetRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebEmailPasswordResetGetRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportWebEmailPasswordResetGetRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("password", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Password); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PassportWebEmailPasswordResetGetRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PassportWebEmailPasswordResetGetRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("email", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Email); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *PassportWebEmailPasswordResetGetRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportWebEmailPasswordResetGetRequest(%+v)", *p)

}

type PassportWebEmailPasswordResetGetResponse struct {
	Code int32  `thrift:"code,253,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg  string `thrift:"msg,254,required" form:"msg,required" json:"msg,required" query:"msg,required"`
}

func NewPassportWebEmailPasswordResetGetResponse() *PassportWebEmailPasswordResetGetResponse {
	return &PassportWebEmailPasswordResetGetResponse{}
}

func (p *PassportWebEmailPasswordResetGetResponse) InitDefault() {
}

func (p *PassportWebEmailPasswordResetGetResponse) GetCode() (v int32) {
	return p.Code
}

func (p *PassportWebEmailPasswordResetGetResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_PassportWebEmailPasswordResetGetResponse = map[int16]string{
	253: "code",
	254: "msg",
}

func (p *PassportWebEmailPasswordResetGetResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 253
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportWebEmailPasswordResetGetResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PassportWebEmailPasswordResetGetResponse[fieldId]))
}

func (p *PassportWebEmailPasswordResetGetResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *PassportWebEmailPasswordResetGetResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *PassportWebEmailPasswordResetGetResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebEmailPasswordResetGetResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportWebEmailPasswordResetGetResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *PassportWebEmailPasswordResetGetResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}

func (p *PassportWebEmailPasswordResetGetResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportWebEmailPasswordResetGetResponse(%+v)", *p)

}

type PassportAccountInfoV2Request struct {
}

func NewPassportAccountInfoV2Request() *PassportAccountInfoV2Request {
	return &PassportAccountInfoV2Request{}
}

func (p *PassportAccountInfoV2Request) InitDefault() {
}

var fieldIDToName_PassportAccountInfoV2Request = map[int16]string{}

func (p *PassportAccountInfoV2Request) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportAccountInfoV2Request) Write(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteStructBegin("PassportAccountInfoV2Request"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportAccountInfoV2Request) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportAccountInfoV2Request(%+v)", *p)

}

type PassportAccountInfoV2Response struct {
	Data *User  `thrift:"data,1,required" form:"data,required" json:"data,required" query:"data,required"`
	Code int32  `thrift:"code,253,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg  string `thrift:"msg,254,required" form:"msg,required" json:"msg,required" query:"msg,required"`
}

func NewPassportAccountInfoV2Response() *PassportAccountInfoV2Response {
	return &PassportAccountInfoV2Response{}
}

func (p *PassportAccountInfoV2Response) InitDefault() {
}

var PassportAccountInfoV2Response_Data_DEFAULT *User

func (p *PassportAccountInfoV2Response) GetData() (v *User) {
	if !p.IsSetData() {
		return PassportAccountInfoV2Response_Data_DEFAULT
	}
	return p.Data
}

func (p *PassportAccountInfoV2Response) GetCode() (v int32) {
	return p.Code
}

func (p *PassportAccountInfoV2Response) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_PassportAccountInfoV2Response = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
}

func (p *PassportAccountInfoV2Response) IsSetData() bool {
	return p.Data != nil
}

func (p *PassportAccountInfoV2Response) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetData bool = false
	var issetCode bool = false
	var issetMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetData = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetData {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCode {
		fieldId = 253
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportAccountInfoV2Response[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PassportAccountInfoV2Response[fieldId]))
}

func (p *PassportAccountInfoV2Response) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUser()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *PassportAccountInfoV2Response) ReadField253(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *PassportAccountInfoV2Response) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *PassportAccountInfoV2Response) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportAccountInfoV2Response"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportAccountInfoV2Response) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PassportAccountInfoV2Response) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *PassportAccountInfoV2Response) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}

func (p *PassportAccountInfoV2Response) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportAccountInfoV2Response(%+v)", *p)

}

type UserUpdateAvatarRequest struct {
	Avatar []byte `thrift:"avatar,3,required" form:"avatar,required" json:"avatar,required"`
}

func NewUserUpdateAvatarRequest() *UserUpdateAvatarRequest {
	return &UserUpdateAvatarRequest{}
}

func (p *UserUpdateAvatarRequest) InitDefault() {
}

func (p *UserUpdateAvatarRequest) GetAvatar() (v []byte) {
	return p.Avatar
}

var fieldIDToName_UserUpdateAvatarRequest = map[int16]string{
	3: "avatar",
}

func (p *UserUpdateAvatarRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAvatar bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAvatar = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAvatar {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UserUpdateAvatarRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UserUpdateAvatarRequest[fieldId]))
}

func (p *UserUpdateAvatarRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field []byte
	if v, err := iprot.ReadBinary(); err != nil {
		return err
	} else {
		_field = []byte(v)
	}
	p.Avatar = _field
	return nil
}

func (p *UserUpdateAvatarRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserUpdateAvatarRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UserUpdateAvatarRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("avatar", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBinary([]byte(p.Avatar)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *UserUpdateAvatarRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserUpdateAvatarRequest(%+v)", *p)

}

type UserUpdateAvatarResponseData struct {
	WebURI string `thrift:"web_uri,1,required" form:"web_uri,required" json:"web_uri,required" query:"web_uri,required"`
}

func NewUserUpdateAvatarResponseData() *UserUpdateAvatarResponseData {
	return &UserUpdateAvatarResponseData{}
}

func (p *UserUpdateAvatarResponseData) InitDefault() {
}

func (p *UserUpdateAvatarResponseData) GetWebURI() (v string) {
	return p.WebURI
}

var fieldIDToName_UserUpdateAvatarResponseData = map[int16]string{
	1: "web_uri",
}

func (p *UserUpdateAvatarResponseData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetWebURI bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetWebURI = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetWebURI {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UserUpdateAvatarResponseData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UserUpdateAvatarResponseData[fieldId]))
}

func (p *UserUpdateAvatarResponseData) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WebURI = _field
	return nil
}

func (p *UserUpdateAvatarResponseData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserUpdateAvatarResponseData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UserUpdateAvatarResponseData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("web_uri", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WebURI); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UserUpdateAvatarResponseData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserUpdateAvatarResponseData(%+v)", *p)

}

type UserUpdateAvatarResponse struct {
	Data *UserUpdateAvatarResponseData `thrift:"data,1,required" form:"data,required" json:"data,required" query:"data,required"`
	Code int32                         `thrift:"code,253,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg  string                        `thrift:"msg,254,required" form:"msg,required" json:"msg,required" query:"msg,required"`
}

func NewUserUpdateAvatarResponse() *UserUpdateAvatarResponse {
	return &UserUpdateAvatarResponse{}
}

func (p *UserUpdateAvatarResponse) InitDefault() {
}

var UserUpdateAvatarResponse_Data_DEFAULT *UserUpdateAvatarResponseData

func (p *UserUpdateAvatarResponse) GetData() (v *UserUpdateAvatarResponseData) {
	if !p.IsSetData() {
		return UserUpdateAvatarResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *UserUpdateAvatarResponse) GetCode() (v int32) {
	return p.Code
}

func (p *UserUpdateAvatarResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_UserUpdateAvatarResponse = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
}

func (p *UserUpdateAvatarResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *UserUpdateAvatarResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetData bool = false
	var issetCode bool = false
	var issetMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetData = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetData {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCode {
		fieldId = 253
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UserUpdateAvatarResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UserUpdateAvatarResponse[fieldId]))
}

func (p *UserUpdateAvatarResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUserUpdateAvatarResponseData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *UserUpdateAvatarResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *UserUpdateAvatarResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *UserUpdateAvatarResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserUpdateAvatarResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UserUpdateAvatarResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UserUpdateAvatarResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *UserUpdateAvatarResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}

func (p *UserUpdateAvatarResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserUpdateAvatarResponse(%+v)", *p)

}

type UserUpdateProfileRequest struct {
	Name           *string `thrift:"name,2,optional" form:"name" json:"name,omitempty" query:"name"`
	UserUniqueName *string `thrift:"user_unique_name,3,optional" form:"user_unique_name" json:"user_unique_name,omitempty" query:"user_unique_name"`
	Description    *string `thrift:"description,5,optional" form:"description" json:"description,omitempty" query:"description"`
	Locale         *string `thrift:"locale,6,optional" form:"locale" json:"locale,omitempty" query:"locale"`
}

func NewUserUpdateProfileRequest() *UserUpdateProfileRequest {
	return &UserUpdateProfileRequest{}
}

func (p *UserUpdateProfileRequest) InitDefault() {
}

var UserUpdateProfileRequest_Name_DEFAULT string

func (p *UserUpdateProfileRequest) GetName() (v string) {
	if !p.IsSetName() {
		return UserUpdateProfileRequest_Name_DEFAULT
	}
	return *p.Name
}

var UserUpdateProfileRequest_UserUniqueName_DEFAULT string

func (p *UserUpdateProfileRequest) GetUserUniqueName() (v string) {
	if !p.IsSetUserUniqueName() {
		return UserUpdateProfileRequest_UserUniqueName_DEFAULT
	}
	return *p.UserUniqueName
}

var UserUpdateProfileRequest_Description_DEFAULT string

func (p *UserUpdateProfileRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return UserUpdateProfileRequest_Description_DEFAULT
	}
	return *p.Description
}

var UserUpdateProfileRequest_Locale_DEFAULT string

func (p *UserUpdateProfileRequest) GetLocale() (v string) {
	if !p.IsSetLocale() {
		return UserUpdateProfileRequest_Locale_DEFAULT
	}
	return *p.Locale
}

var fieldIDToName_UserUpdateProfileRequest = map[int16]string{
	2: "name",
	3: "user_unique_name",
	5: "description",
	6: "locale",
}

func (p *UserUpdateProfileRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *UserUpdateProfileRequest) IsSetUserUniqueName() bool {
	return p.UserUniqueName != nil
}

func (p *UserUpdateProfileRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *UserUpdateProfileRequest) IsSetLocale() bool {
	return p.Locale != nil
}

func (p *UserUpdateProfileRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UserUpdateProfileRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UserUpdateProfileRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *UserUpdateProfileRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserUniqueName = _field
	return nil
}
func (p *UserUpdateProfileRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *UserUpdateProfileRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Locale = _field
	return nil
}

func (p *UserUpdateProfileRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserUpdateProfileRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UserUpdateProfileRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UserUpdateProfileRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserUniqueName() {
		if err = oprot.WriteFieldBegin("user_unique_name", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserUniqueName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UserUpdateProfileRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("description", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *UserUpdateProfileRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetLocale() {
		if err = oprot.WriteFieldBegin("locale", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Locale); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *UserUpdateProfileRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserUpdateProfileRequest(%+v)", *p)

}

type UserUpdateProfileResponse struct {
	Code int32  `thrift:"code,253,required" form:"code,required" json:"code,required" query:"code,required"`
	Msg  string `thrift:"msg,254,required" form:"msg,required" json:"msg,required" query:"msg,required"`
}

func NewUserUpdateProfileResponse() *UserUpdateProfileResponse {
	return &UserUpdateProfileResponse{}
}

func (p *UserUpdateProfileResponse) InitDefault() {
}

func (p *UserUpdateProfileResponse) GetCode() (v int32) {
	return p.Code
}

func (p *UserUpdateProfileResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_UserUpdateProfileResponse = map[int16]string{
	253: "code",
	254: "msg",
}

func (p *UserUpdateProfileResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 253
		goto RequiredFieldNotSetError
	}

	if !issetMsg {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UserUpdateProfileResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UserUpdateProfileResponse[fieldId]))
}

func (p *UserUpdateProfileResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *UserUpdateProfileResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *UserUpdateProfileResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserUpdateProfileResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UserUpdateProfileResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *UserUpdateProfileResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}

func (p *UserUpdateProfileResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserUpdateProfileResponse(%+v)", *p)

}

type PassportService interface {
	// Email password registration
	PassportWebEmailRegisterV2Post(ctx context.Context, req *PassportWebEmailRegisterV2PostRequest) (r *PassportWebEmailRegisterV2PostResponse, err error)
	// log out
	PassportWebLogoutGet(ctx context.Context, req *PassportWebLogoutGetRequest) (r *PassportWebLogoutGetResponse, err error)
	// Email account password login
	PassportWebEmailLoginPost(ctx context.Context, req *PassportWebEmailLoginPostRequest) (r *PassportWebEmailLoginPostResponse, err error)
	// Reset password via email
	PassportWebEmailPasswordResetGet(ctx context.Context, req *PassportWebEmailPasswordResetGetRequest) (r *PassportWebEmailPasswordResetGetResponse, err error)
	// account information
	PassportAccountInfoV2(ctx context.Context, req *PassportAccountInfoV2Request) (r *PassportAccountInfoV2Response, err error)

	UserUpdateAvatar(ctx context.Context, req *UserUpdateAvatarRequest) (r *UserUpdateAvatarResponse, err error)

	UserUpdateProfile(ctx context.Context, req *UserUpdateProfileRequest) (r *UserUpdateProfileResponse, err error)
}

type PassportServiceClient struct {
	c thrift.TClient
}

func NewPassportServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *PassportServiceClient {
	return &PassportServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewPassportServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *PassportServiceClient {
	return &PassportServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewPassportServiceClient(c thrift.TClient) *PassportServiceClient {
	return &PassportServiceClient{
		c: c,
	}
}

func (p *PassportServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *PassportServiceClient) PassportWebEmailRegisterV2Post(ctx context.Context, req *PassportWebEmailRegisterV2PostRequest) (r *PassportWebEmailRegisterV2PostResponse, err error) {
	var _args PassportServicePassportWebEmailRegisterV2PostArgs
	_args.Req = req
	var _result PassportServicePassportWebEmailRegisterV2PostResult
	if err = p.Client_().Call(ctx, "PassportWebEmailRegisterV2Post", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *PassportServiceClient) PassportWebLogoutGet(ctx context.Context, req *PassportWebLogoutGetRequest) (r *PassportWebLogoutGetResponse, err error) {
	var _args PassportServicePassportWebLogoutGetArgs
	_args.Req = req
	var _result PassportServicePassportWebLogoutGetResult
	if err = p.Client_().Call(ctx, "PassportWebLogoutGet", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *PassportServiceClient) PassportWebEmailLoginPost(ctx context.Context, req *PassportWebEmailLoginPostRequest) (r *PassportWebEmailLoginPostResponse, err error) {
	var _args PassportServicePassportWebEmailLoginPostArgs
	_args.Req = req
	var _result PassportServicePassportWebEmailLoginPostResult
	if err = p.Client_().Call(ctx, "PassportWebEmailLoginPost", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *PassportServiceClient) PassportWebEmailPasswordResetGet(ctx context.Context, req *PassportWebEmailPasswordResetGetRequest) (r *PassportWebEmailPasswordResetGetResponse, err error) {
	var _args PassportServicePassportWebEmailPasswordResetGetArgs
	_args.Req = req
	var _result PassportServicePassportWebEmailPasswordResetGetResult
	if err = p.Client_().Call(ctx, "PassportWebEmailPasswordResetGet", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *PassportServiceClient) PassportAccountInfoV2(ctx context.Context, req *PassportAccountInfoV2Request) (r *PassportAccountInfoV2Response, err error) {
	var _args PassportServicePassportAccountInfoV2Args
	_args.Req = req
	var _result PassportServicePassportAccountInfoV2Result
	if err = p.Client_().Call(ctx, "PassportAccountInfoV2", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *PassportServiceClient) UserUpdateAvatar(ctx context.Context, req *UserUpdateAvatarRequest) (r *UserUpdateAvatarResponse, err error) {
	var _args PassportServiceUserUpdateAvatarArgs
	_args.Req = req
	var _result PassportServiceUserUpdateAvatarResult
	if err = p.Client_().Call(ctx, "UserUpdateAvatar", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *PassportServiceClient) UserUpdateProfile(ctx context.Context, req *UserUpdateProfileRequest) (r *UserUpdateProfileResponse, err error) {
	var _args PassportServiceUserUpdateProfileArgs
	_args.Req = req
	var _result PassportServiceUserUpdateProfileResult
	if err = p.Client_().Call(ctx, "UserUpdateProfile", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

type PassportServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      PassportService
}

func (p *PassportServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *PassportServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *PassportServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewPassportServiceProcessor(handler PassportService) *PassportServiceProcessor {
	self := &PassportServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self.AddToProcessorMap("PassportWebEmailRegisterV2Post", &passportServiceProcessorPassportWebEmailRegisterV2Post{handler: handler})
	self.AddToProcessorMap("PassportWebLogoutGet", &passportServiceProcessorPassportWebLogoutGet{handler: handler})
	self.AddToProcessorMap("PassportWebEmailLoginPost", &passportServiceProcessorPassportWebEmailLoginPost{handler: handler})
	self.AddToProcessorMap("PassportWebEmailPasswordResetGet", &passportServiceProcessorPassportWebEmailPasswordResetGet{handler: handler})
	self.AddToProcessorMap("PassportAccountInfoV2", &passportServiceProcessorPassportAccountInfoV2{handler: handler})
	self.AddToProcessorMap("UserUpdateAvatar", &passportServiceProcessorUserUpdateAvatar{handler: handler})
	self.AddToProcessorMap("UserUpdateProfile", &passportServiceProcessorUserUpdateProfile{handler: handler})
	return self
}
func (p *PassportServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush(ctx)
	return false, x
}

type passportServiceProcessorPassportWebEmailRegisterV2Post struct {
	handler PassportService
}

func (p *passportServiceProcessorPassportWebEmailRegisterV2Post) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := PassportServicePassportWebEmailRegisterV2PostArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("PassportWebEmailRegisterV2Post", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := PassportServicePassportWebEmailRegisterV2PostResult{}
	var retval *PassportWebEmailRegisterV2PostResponse
	if retval, err2 = p.handler.PassportWebEmailRegisterV2Post(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing PassportWebEmailRegisterV2Post: "+err2.Error())
		oprot.WriteMessageBegin("PassportWebEmailRegisterV2Post", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("PassportWebEmailRegisterV2Post", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type passportServiceProcessorPassportWebLogoutGet struct {
	handler PassportService
}

func (p *passportServiceProcessorPassportWebLogoutGet) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := PassportServicePassportWebLogoutGetArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("PassportWebLogoutGet", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := PassportServicePassportWebLogoutGetResult{}
	var retval *PassportWebLogoutGetResponse
	if retval, err2 = p.handler.PassportWebLogoutGet(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing PassportWebLogoutGet: "+err2.Error())
		oprot.WriteMessageBegin("PassportWebLogoutGet", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("PassportWebLogoutGet", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type passportServiceProcessorPassportWebEmailLoginPost struct {
	handler PassportService
}

func (p *passportServiceProcessorPassportWebEmailLoginPost) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := PassportServicePassportWebEmailLoginPostArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("PassportWebEmailLoginPost", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := PassportServicePassportWebEmailLoginPostResult{}
	var retval *PassportWebEmailLoginPostResponse
	if retval, err2 = p.handler.PassportWebEmailLoginPost(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing PassportWebEmailLoginPost: "+err2.Error())
		oprot.WriteMessageBegin("PassportWebEmailLoginPost", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("PassportWebEmailLoginPost", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type passportServiceProcessorPassportWebEmailPasswordResetGet struct {
	handler PassportService
}

func (p *passportServiceProcessorPassportWebEmailPasswordResetGet) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := PassportServicePassportWebEmailPasswordResetGetArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("PassportWebEmailPasswordResetGet", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := PassportServicePassportWebEmailPasswordResetGetResult{}
	var retval *PassportWebEmailPasswordResetGetResponse
	if retval, err2 = p.handler.PassportWebEmailPasswordResetGet(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing PassportWebEmailPasswordResetGet: "+err2.Error())
		oprot.WriteMessageBegin("PassportWebEmailPasswordResetGet", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("PassportWebEmailPasswordResetGet", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type passportServiceProcessorPassportAccountInfoV2 struct {
	handler PassportService
}

func (p *passportServiceProcessorPassportAccountInfoV2) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := PassportServicePassportAccountInfoV2Args{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("PassportAccountInfoV2", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := PassportServicePassportAccountInfoV2Result{}
	var retval *PassportAccountInfoV2Response
	if retval, err2 = p.handler.PassportAccountInfoV2(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing PassportAccountInfoV2: "+err2.Error())
		oprot.WriteMessageBegin("PassportAccountInfoV2", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("PassportAccountInfoV2", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type passportServiceProcessorUserUpdateAvatar struct {
	handler PassportService
}

func (p *passportServiceProcessorUserUpdateAvatar) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := PassportServiceUserUpdateAvatarArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UserUpdateAvatar", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := PassportServiceUserUpdateAvatarResult{}
	var retval *UserUpdateAvatarResponse
	if retval, err2 = p.handler.UserUpdateAvatar(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UserUpdateAvatar: "+err2.Error())
		oprot.WriteMessageBegin("UserUpdateAvatar", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UserUpdateAvatar", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type passportServiceProcessorUserUpdateProfile struct {
	handler PassportService
}

func (p *passportServiceProcessorUserUpdateProfile) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := PassportServiceUserUpdateProfileArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UserUpdateProfile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := PassportServiceUserUpdateProfileResult{}
	var retval *UserUpdateProfileResponse
	if retval, err2 = p.handler.UserUpdateProfile(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UserUpdateProfile: "+err2.Error())
		oprot.WriteMessageBegin("UserUpdateProfile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UserUpdateProfile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type PassportServicePassportWebEmailRegisterV2PostArgs struct {
	Req *PassportWebEmailRegisterV2PostRequest `thrift:"req,1"`
}

func NewPassportServicePassportWebEmailRegisterV2PostArgs() *PassportServicePassportWebEmailRegisterV2PostArgs {
	return &PassportServicePassportWebEmailRegisterV2PostArgs{}
}

func (p *PassportServicePassportWebEmailRegisterV2PostArgs) InitDefault() {
}

var PassportServicePassportWebEmailRegisterV2PostArgs_Req_DEFAULT *PassportWebEmailRegisterV2PostRequest

func (p *PassportServicePassportWebEmailRegisterV2PostArgs) GetReq() (v *PassportWebEmailRegisterV2PostRequest) {
	if !p.IsSetReq() {
		return PassportServicePassportWebEmailRegisterV2PostArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_PassportServicePassportWebEmailRegisterV2PostArgs = map[int16]string{
	1: "req",
}

func (p *PassportServicePassportWebEmailRegisterV2PostArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *PassportServicePassportWebEmailRegisterV2PostArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServicePassportWebEmailRegisterV2PostArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServicePassportWebEmailRegisterV2PostArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPassportWebEmailRegisterV2PostRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *PassportServicePassportWebEmailRegisterV2PostArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebEmailRegisterV2Post_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServicePassportWebEmailRegisterV2PostArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PassportServicePassportWebEmailRegisterV2PostArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServicePassportWebEmailRegisterV2PostArgs(%+v)", *p)

}

type PassportServicePassportWebEmailRegisterV2PostResult struct {
	Success *PassportWebEmailRegisterV2PostResponse `thrift:"success,0,optional"`
}

func NewPassportServicePassportWebEmailRegisterV2PostResult() *PassportServicePassportWebEmailRegisterV2PostResult {
	return &PassportServicePassportWebEmailRegisterV2PostResult{}
}

func (p *PassportServicePassportWebEmailRegisterV2PostResult) InitDefault() {
}

var PassportServicePassportWebEmailRegisterV2PostResult_Success_DEFAULT *PassportWebEmailRegisterV2PostResponse

func (p *PassportServicePassportWebEmailRegisterV2PostResult) GetSuccess() (v *PassportWebEmailRegisterV2PostResponse) {
	if !p.IsSetSuccess() {
		return PassportServicePassportWebEmailRegisterV2PostResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_PassportServicePassportWebEmailRegisterV2PostResult = map[int16]string{
	0: "success",
}

func (p *PassportServicePassportWebEmailRegisterV2PostResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PassportServicePassportWebEmailRegisterV2PostResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServicePassportWebEmailRegisterV2PostResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServicePassportWebEmailRegisterV2PostResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewPassportWebEmailRegisterV2PostResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *PassportServicePassportWebEmailRegisterV2PostResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebEmailRegisterV2Post_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServicePassportWebEmailRegisterV2PostResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *PassportServicePassportWebEmailRegisterV2PostResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServicePassportWebEmailRegisterV2PostResult(%+v)", *p)

}

type PassportServicePassportWebLogoutGetArgs struct {
	Req *PassportWebLogoutGetRequest `thrift:"req,1"`
}

func NewPassportServicePassportWebLogoutGetArgs() *PassportServicePassportWebLogoutGetArgs {
	return &PassportServicePassportWebLogoutGetArgs{}
}

func (p *PassportServicePassportWebLogoutGetArgs) InitDefault() {
}

var PassportServicePassportWebLogoutGetArgs_Req_DEFAULT *PassportWebLogoutGetRequest

func (p *PassportServicePassportWebLogoutGetArgs) GetReq() (v *PassportWebLogoutGetRequest) {
	if !p.IsSetReq() {
		return PassportServicePassportWebLogoutGetArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_PassportServicePassportWebLogoutGetArgs = map[int16]string{
	1: "req",
}

func (p *PassportServicePassportWebLogoutGetArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *PassportServicePassportWebLogoutGetArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServicePassportWebLogoutGetArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServicePassportWebLogoutGetArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPassportWebLogoutGetRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *PassportServicePassportWebLogoutGetArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebLogoutGet_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServicePassportWebLogoutGetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PassportServicePassportWebLogoutGetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServicePassportWebLogoutGetArgs(%+v)", *p)

}

type PassportServicePassportWebLogoutGetResult struct {
	Success *PassportWebLogoutGetResponse `thrift:"success,0,optional"`
}

func NewPassportServicePassportWebLogoutGetResult() *PassportServicePassportWebLogoutGetResult {
	return &PassportServicePassportWebLogoutGetResult{}
}

func (p *PassportServicePassportWebLogoutGetResult) InitDefault() {
}

var PassportServicePassportWebLogoutGetResult_Success_DEFAULT *PassportWebLogoutGetResponse

func (p *PassportServicePassportWebLogoutGetResult) GetSuccess() (v *PassportWebLogoutGetResponse) {
	if !p.IsSetSuccess() {
		return PassportServicePassportWebLogoutGetResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_PassportServicePassportWebLogoutGetResult = map[int16]string{
	0: "success",
}

func (p *PassportServicePassportWebLogoutGetResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PassportServicePassportWebLogoutGetResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServicePassportWebLogoutGetResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServicePassportWebLogoutGetResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewPassportWebLogoutGetResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *PassportServicePassportWebLogoutGetResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebLogoutGet_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServicePassportWebLogoutGetResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *PassportServicePassportWebLogoutGetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServicePassportWebLogoutGetResult(%+v)", *p)

}

type PassportServicePassportWebEmailLoginPostArgs struct {
	Req *PassportWebEmailLoginPostRequest `thrift:"req,1"`
}

func NewPassportServicePassportWebEmailLoginPostArgs() *PassportServicePassportWebEmailLoginPostArgs {
	return &PassportServicePassportWebEmailLoginPostArgs{}
}

func (p *PassportServicePassportWebEmailLoginPostArgs) InitDefault() {
}

var PassportServicePassportWebEmailLoginPostArgs_Req_DEFAULT *PassportWebEmailLoginPostRequest

func (p *PassportServicePassportWebEmailLoginPostArgs) GetReq() (v *PassportWebEmailLoginPostRequest) {
	if !p.IsSetReq() {
		return PassportServicePassportWebEmailLoginPostArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_PassportServicePassportWebEmailLoginPostArgs = map[int16]string{
	1: "req",
}

func (p *PassportServicePassportWebEmailLoginPostArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *PassportServicePassportWebEmailLoginPostArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServicePassportWebEmailLoginPostArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServicePassportWebEmailLoginPostArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPassportWebEmailLoginPostRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *PassportServicePassportWebEmailLoginPostArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebEmailLoginPost_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServicePassportWebEmailLoginPostArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PassportServicePassportWebEmailLoginPostArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServicePassportWebEmailLoginPostArgs(%+v)", *p)

}

type PassportServicePassportWebEmailLoginPostResult struct {
	Success *PassportWebEmailLoginPostResponse `thrift:"success,0,optional"`
}

func NewPassportServicePassportWebEmailLoginPostResult() *PassportServicePassportWebEmailLoginPostResult {
	return &PassportServicePassportWebEmailLoginPostResult{}
}

func (p *PassportServicePassportWebEmailLoginPostResult) InitDefault() {
}

var PassportServicePassportWebEmailLoginPostResult_Success_DEFAULT *PassportWebEmailLoginPostResponse

func (p *PassportServicePassportWebEmailLoginPostResult) GetSuccess() (v *PassportWebEmailLoginPostResponse) {
	if !p.IsSetSuccess() {
		return PassportServicePassportWebEmailLoginPostResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_PassportServicePassportWebEmailLoginPostResult = map[int16]string{
	0: "success",
}

func (p *PassportServicePassportWebEmailLoginPostResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PassportServicePassportWebEmailLoginPostResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServicePassportWebEmailLoginPostResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServicePassportWebEmailLoginPostResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewPassportWebEmailLoginPostResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *PassportServicePassportWebEmailLoginPostResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebEmailLoginPost_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServicePassportWebEmailLoginPostResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *PassportServicePassportWebEmailLoginPostResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServicePassportWebEmailLoginPostResult(%+v)", *p)

}

type PassportServicePassportWebEmailPasswordResetGetArgs struct {
	Req *PassportWebEmailPasswordResetGetRequest `thrift:"req,1"`
}

func NewPassportServicePassportWebEmailPasswordResetGetArgs() *PassportServicePassportWebEmailPasswordResetGetArgs {
	return &PassportServicePassportWebEmailPasswordResetGetArgs{}
}

func (p *PassportServicePassportWebEmailPasswordResetGetArgs) InitDefault() {
}

var PassportServicePassportWebEmailPasswordResetGetArgs_Req_DEFAULT *PassportWebEmailPasswordResetGetRequest

func (p *PassportServicePassportWebEmailPasswordResetGetArgs) GetReq() (v *PassportWebEmailPasswordResetGetRequest) {
	if !p.IsSetReq() {
		return PassportServicePassportWebEmailPasswordResetGetArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_PassportServicePassportWebEmailPasswordResetGetArgs = map[int16]string{
	1: "req",
}

func (p *PassportServicePassportWebEmailPasswordResetGetArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *PassportServicePassportWebEmailPasswordResetGetArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServicePassportWebEmailPasswordResetGetArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServicePassportWebEmailPasswordResetGetArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPassportWebEmailPasswordResetGetRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *PassportServicePassportWebEmailPasswordResetGetArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebEmailPasswordResetGet_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServicePassportWebEmailPasswordResetGetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PassportServicePassportWebEmailPasswordResetGetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServicePassportWebEmailPasswordResetGetArgs(%+v)", *p)

}

type PassportServicePassportWebEmailPasswordResetGetResult struct {
	Success *PassportWebEmailPasswordResetGetResponse `thrift:"success,0,optional"`
}

func NewPassportServicePassportWebEmailPasswordResetGetResult() *PassportServicePassportWebEmailPasswordResetGetResult {
	return &PassportServicePassportWebEmailPasswordResetGetResult{}
}

func (p *PassportServicePassportWebEmailPasswordResetGetResult) InitDefault() {
}

var PassportServicePassportWebEmailPasswordResetGetResult_Success_DEFAULT *PassportWebEmailPasswordResetGetResponse

func (p *PassportServicePassportWebEmailPasswordResetGetResult) GetSuccess() (v *PassportWebEmailPasswordResetGetResponse) {
	if !p.IsSetSuccess() {
		return PassportServicePassportWebEmailPasswordResetGetResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_PassportServicePassportWebEmailPasswordResetGetResult = map[int16]string{
	0: "success",
}

func (p *PassportServicePassportWebEmailPasswordResetGetResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PassportServicePassportWebEmailPasswordResetGetResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServicePassportWebEmailPasswordResetGetResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServicePassportWebEmailPasswordResetGetResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewPassportWebEmailPasswordResetGetResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *PassportServicePassportWebEmailPasswordResetGetResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportWebEmailPasswordResetGet_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServicePassportWebEmailPasswordResetGetResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *PassportServicePassportWebEmailPasswordResetGetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServicePassportWebEmailPasswordResetGetResult(%+v)", *p)

}

type PassportServicePassportAccountInfoV2Args struct {
	Req *PassportAccountInfoV2Request `thrift:"req,1"`
}

func NewPassportServicePassportAccountInfoV2Args() *PassportServicePassportAccountInfoV2Args {
	return &PassportServicePassportAccountInfoV2Args{}
}

func (p *PassportServicePassportAccountInfoV2Args) InitDefault() {
}

var PassportServicePassportAccountInfoV2Args_Req_DEFAULT *PassportAccountInfoV2Request

func (p *PassportServicePassportAccountInfoV2Args) GetReq() (v *PassportAccountInfoV2Request) {
	if !p.IsSetReq() {
		return PassportServicePassportAccountInfoV2Args_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_PassportServicePassportAccountInfoV2Args = map[int16]string{
	1: "req",
}

func (p *PassportServicePassportAccountInfoV2Args) IsSetReq() bool {
	return p.Req != nil
}

func (p *PassportServicePassportAccountInfoV2Args) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServicePassportAccountInfoV2Args[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServicePassportAccountInfoV2Args) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPassportAccountInfoV2Request()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *PassportServicePassportAccountInfoV2Args) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportAccountInfoV2_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServicePassportAccountInfoV2Args) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PassportServicePassportAccountInfoV2Args) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServicePassportAccountInfoV2Args(%+v)", *p)

}

type PassportServicePassportAccountInfoV2Result struct {
	Success *PassportAccountInfoV2Response `thrift:"success,0,optional"`
}

func NewPassportServicePassportAccountInfoV2Result() *PassportServicePassportAccountInfoV2Result {
	return &PassportServicePassportAccountInfoV2Result{}
}

func (p *PassportServicePassportAccountInfoV2Result) InitDefault() {
}

var PassportServicePassportAccountInfoV2Result_Success_DEFAULT *PassportAccountInfoV2Response

func (p *PassportServicePassportAccountInfoV2Result) GetSuccess() (v *PassportAccountInfoV2Response) {
	if !p.IsSetSuccess() {
		return PassportServicePassportAccountInfoV2Result_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_PassportServicePassportAccountInfoV2Result = map[int16]string{
	0: "success",
}

func (p *PassportServicePassportAccountInfoV2Result) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PassportServicePassportAccountInfoV2Result) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServicePassportAccountInfoV2Result[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServicePassportAccountInfoV2Result) ReadField0(iprot thrift.TProtocol) error {
	_field := NewPassportAccountInfoV2Response()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *PassportServicePassportAccountInfoV2Result) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PassportAccountInfoV2_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServicePassportAccountInfoV2Result) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *PassportServicePassportAccountInfoV2Result) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServicePassportAccountInfoV2Result(%+v)", *p)

}

type PassportServiceUserUpdateAvatarArgs struct {
	Req *UserUpdateAvatarRequest `thrift:"req,1"`
}

func NewPassportServiceUserUpdateAvatarArgs() *PassportServiceUserUpdateAvatarArgs {
	return &PassportServiceUserUpdateAvatarArgs{}
}

func (p *PassportServiceUserUpdateAvatarArgs) InitDefault() {
}

var PassportServiceUserUpdateAvatarArgs_Req_DEFAULT *UserUpdateAvatarRequest

func (p *PassportServiceUserUpdateAvatarArgs) GetReq() (v *UserUpdateAvatarRequest) {
	if !p.IsSetReq() {
		return PassportServiceUserUpdateAvatarArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_PassportServiceUserUpdateAvatarArgs = map[int16]string{
	1: "req",
}

func (p *PassportServiceUserUpdateAvatarArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *PassportServiceUserUpdateAvatarArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServiceUserUpdateAvatarArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServiceUserUpdateAvatarArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUserUpdateAvatarRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *PassportServiceUserUpdateAvatarArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserUpdateAvatar_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServiceUserUpdateAvatarArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PassportServiceUserUpdateAvatarArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServiceUserUpdateAvatarArgs(%+v)", *p)

}

type PassportServiceUserUpdateAvatarResult struct {
	Success *UserUpdateAvatarResponse `thrift:"success,0,optional"`
}

func NewPassportServiceUserUpdateAvatarResult() *PassportServiceUserUpdateAvatarResult {
	return &PassportServiceUserUpdateAvatarResult{}
}

func (p *PassportServiceUserUpdateAvatarResult) InitDefault() {
}

var PassportServiceUserUpdateAvatarResult_Success_DEFAULT *UserUpdateAvatarResponse

func (p *PassportServiceUserUpdateAvatarResult) GetSuccess() (v *UserUpdateAvatarResponse) {
	if !p.IsSetSuccess() {
		return PassportServiceUserUpdateAvatarResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_PassportServiceUserUpdateAvatarResult = map[int16]string{
	0: "success",
}

func (p *PassportServiceUserUpdateAvatarResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PassportServiceUserUpdateAvatarResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServiceUserUpdateAvatarResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServiceUserUpdateAvatarResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUserUpdateAvatarResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *PassportServiceUserUpdateAvatarResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserUpdateAvatar_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServiceUserUpdateAvatarResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *PassportServiceUserUpdateAvatarResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServiceUserUpdateAvatarResult(%+v)", *p)

}

type PassportServiceUserUpdateProfileArgs struct {
	Req *UserUpdateProfileRequest `thrift:"req,1"`
}

func NewPassportServiceUserUpdateProfileArgs() *PassportServiceUserUpdateProfileArgs {
	return &PassportServiceUserUpdateProfileArgs{}
}

func (p *PassportServiceUserUpdateProfileArgs) InitDefault() {
}

var PassportServiceUserUpdateProfileArgs_Req_DEFAULT *UserUpdateProfileRequest

func (p *PassportServiceUserUpdateProfileArgs) GetReq() (v *UserUpdateProfileRequest) {
	if !p.IsSetReq() {
		return PassportServiceUserUpdateProfileArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_PassportServiceUserUpdateProfileArgs = map[int16]string{
	1: "req",
}

func (p *PassportServiceUserUpdateProfileArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *PassportServiceUserUpdateProfileArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServiceUserUpdateProfileArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServiceUserUpdateProfileArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUserUpdateProfileRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *PassportServiceUserUpdateProfileArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserUpdateProfile_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServiceUserUpdateProfileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PassportServiceUserUpdateProfileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServiceUserUpdateProfileArgs(%+v)", *p)

}

type PassportServiceUserUpdateProfileResult struct {
	Success *UserUpdateProfileResponse `thrift:"success,0,optional"`
}

func NewPassportServiceUserUpdateProfileResult() *PassportServiceUserUpdateProfileResult {
	return &PassportServiceUserUpdateProfileResult{}
}

func (p *PassportServiceUserUpdateProfileResult) InitDefault() {
}

var PassportServiceUserUpdateProfileResult_Success_DEFAULT *UserUpdateProfileResponse

func (p *PassportServiceUserUpdateProfileResult) GetSuccess() (v *UserUpdateProfileResponse) {
	if !p.IsSetSuccess() {
		return PassportServiceUserUpdateProfileResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_PassportServiceUserUpdateProfileResult = map[int16]string{
	0: "success",
}

func (p *PassportServiceUserUpdateProfileResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PassportServiceUserUpdateProfileResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PassportServiceUserUpdateProfileResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PassportServiceUserUpdateProfileResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUserUpdateProfileResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *PassportServiceUserUpdateProfileResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserUpdateProfile_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PassportServiceUserUpdateProfileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *PassportServiceUserUpdateProfileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassportServiceUserUpdateProfileResult(%+v)", *p)

}
