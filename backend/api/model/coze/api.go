// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package coze

import (
	"github.com/apache/thrift/lib/go/thrift"
	"github.com/coze-dev/coze-studio/backend/api/model/app/bot_open_api"
	"github.com/coze-dev/coze-studio/backend/api/model/app/developer_api"
	"github.com/coze-dev/coze-studio/backend/api/model/app/intelligence"
	"github.com/coze-dev/coze-studio/backend/api/model/conversation/agentrun"
	"github.com/coze-dev/coze-studio/backend/api/model/conversation/conversation"
	"github.com/coze-dev/coze-studio/backend/api/model/conversation/message"
	"github.com/coze-dev/coze-studio/backend/api/model/data/database"
	"github.com/coze-dev/coze-studio/backend/api/model/data/knowledge"
	"github.com/coze-dev/coze-studio/backend/api/model/data/variable"
	"github.com/coze-dev/coze-studio/backend/api/model/file/upload"
	"github.com/coze-dev/coze-studio/backend/api/model/marketplace/product_public_api"
	"github.com/coze-dev/coze-studio/backend/api/model/passport"
	"github.com/coze-dev/coze-studio/backend/api/model/permission/openapiauth"
	"github.com/coze-dev/coze-studio/backend/api/model/playground"
	"github.com/coze-dev/coze-studio/backend/api/model/plugin_develop"
	"github.com/coze-dev/coze-studio/backend/api/model/resource"
	"github.com/coze-dev/coze-studio/backend/api/model/workflow"
)

type IntelligenceService interface {
	intelligence.IntelligenceService
}

type IntelligenceServiceClient struct {
	*intelligence.IntelligenceServiceClient
}

func NewIntelligenceServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *IntelligenceServiceClient {
	return &IntelligenceServiceClient{
		IntelligenceServiceClient: intelligence.NewIntelligenceServiceClientFactory(t, f),
	}
}

func NewIntelligenceServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *IntelligenceServiceClient {
	return &IntelligenceServiceClient{
		IntelligenceServiceClient: intelligence.NewIntelligenceServiceClientProtocol(t, iprot, oprot),
	}
}

func NewIntelligenceServiceClient(c thrift.TClient) *IntelligenceServiceClient {
	return &IntelligenceServiceClient{
		IntelligenceServiceClient: intelligence.NewIntelligenceServiceClient(c),
	}
}

type ConversationService interface {
	conversation.ConversationService
}

type ConversationServiceClient struct {
	*conversation.ConversationServiceClient
}

func NewConversationServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ConversationServiceClient {
	return &ConversationServiceClient{
		ConversationServiceClient: conversation.NewConversationServiceClientFactory(t, f),
	}
}

func NewConversationServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ConversationServiceClient {
	return &ConversationServiceClient{
		ConversationServiceClient: conversation.NewConversationServiceClientProtocol(t, iprot, oprot),
	}
}

func NewConversationServiceClient(c thrift.TClient) *ConversationServiceClient {
	return &ConversationServiceClient{
		ConversationServiceClient: conversation.NewConversationServiceClient(c),
	}
}

type MessageService interface {
	message.MessageService
}

type MessageServiceClient struct {
	*message.MessageServiceClient
}

func NewMessageServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *MessageServiceClient {
	return &MessageServiceClient{
		MessageServiceClient: message.NewMessageServiceClientFactory(t, f),
	}
}

func NewMessageServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *MessageServiceClient {
	return &MessageServiceClient{
		MessageServiceClient: message.NewMessageServiceClientProtocol(t, iprot, oprot),
	}
}

func NewMessageServiceClient(c thrift.TClient) *MessageServiceClient {
	return &MessageServiceClient{
		MessageServiceClient: message.NewMessageServiceClient(c),
	}
}

type AgentRunService interface {
	agentrun.AgentRunService
}

type AgentRunServiceClient struct {
	*agentrun.AgentRunServiceClient
}

func NewAgentRunServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *AgentRunServiceClient {
	return &AgentRunServiceClient{
		AgentRunServiceClient: agentrun.NewAgentRunServiceClientFactory(t, f),
	}
}

func NewAgentRunServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *AgentRunServiceClient {
	return &AgentRunServiceClient{
		AgentRunServiceClient: agentrun.NewAgentRunServiceClientProtocol(t, iprot, oprot),
	}
}

func NewAgentRunServiceClient(c thrift.TClient) *AgentRunServiceClient {
	return &AgentRunServiceClient{
		AgentRunServiceClient: agentrun.NewAgentRunServiceClient(c),
	}
}

type OpenAPIAuthService interface {
	openapiauth.OpenAPIAuthService
}

type OpenAPIAuthServiceClient struct {
	*openapiauth.OpenAPIAuthServiceClient
}

func NewOpenAPIAuthServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *OpenAPIAuthServiceClient {
	return &OpenAPIAuthServiceClient{
		OpenAPIAuthServiceClient: openapiauth.NewOpenAPIAuthServiceClientFactory(t, f),
	}
}

func NewOpenAPIAuthServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *OpenAPIAuthServiceClient {
	return &OpenAPIAuthServiceClient{
		OpenAPIAuthServiceClient: openapiauth.NewOpenAPIAuthServiceClientProtocol(t, iprot, oprot),
	}
}

func NewOpenAPIAuthServiceClient(c thrift.TClient) *OpenAPIAuthServiceClient {
	return &OpenAPIAuthServiceClient{
		OpenAPIAuthServiceClient: openapiauth.NewOpenAPIAuthServiceClient(c),
	}
}

type MemoryService interface {
	variable.MemoryService
}

type MemoryServiceClient struct {
	*variable.MemoryServiceClient
}

func NewMemoryServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *MemoryServiceClient {
	return &MemoryServiceClient{
		MemoryServiceClient: variable.NewMemoryServiceClientFactory(t, f),
	}
}

func NewMemoryServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *MemoryServiceClient {
	return &MemoryServiceClient{
		MemoryServiceClient: variable.NewMemoryServiceClientProtocol(t, iprot, oprot),
	}
}

func NewMemoryServiceClient(c thrift.TClient) *MemoryServiceClient {
	return &MemoryServiceClient{
		MemoryServiceClient: variable.NewMemoryServiceClient(c),
	}
}

type PluginDevelopService interface {
	plugin_develop.PluginDevelopService
}

type PluginDevelopServiceClient struct {
	*plugin_develop.PluginDevelopServiceClient
}

func NewPluginDevelopServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *PluginDevelopServiceClient {
	return &PluginDevelopServiceClient{
		PluginDevelopServiceClient: plugin_develop.NewPluginDevelopServiceClientFactory(t, f),
	}
}

func NewPluginDevelopServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *PluginDevelopServiceClient {
	return &PluginDevelopServiceClient{
		PluginDevelopServiceClient: plugin_develop.NewPluginDevelopServiceClientProtocol(t, iprot, oprot),
	}
}

func NewPluginDevelopServiceClient(c thrift.TClient) *PluginDevelopServiceClient {
	return &PluginDevelopServiceClient{
		PluginDevelopServiceClient: plugin_develop.NewPluginDevelopServiceClient(c),
	}
}

type PublicProductService interface {
	product_public_api.PublicProductService
}

type PublicProductServiceClient struct {
	*product_public_api.PublicProductServiceClient
}

func NewPublicProductServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *PublicProductServiceClient {
	return &PublicProductServiceClient{
		PublicProductServiceClient: product_public_api.NewPublicProductServiceClientFactory(t, f),
	}
}

func NewPublicProductServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *PublicProductServiceClient {
	return &PublicProductServiceClient{
		PublicProductServiceClient: product_public_api.NewPublicProductServiceClientProtocol(t, iprot, oprot),
	}
}

func NewPublicProductServiceClient(c thrift.TClient) *PublicProductServiceClient {
	return &PublicProductServiceClient{
		PublicProductServiceClient: product_public_api.NewPublicProductServiceClient(c),
	}
}

type DeveloperApiService interface {
	developer_api.DeveloperApiService
}

type DeveloperApiServiceClient struct {
	*developer_api.DeveloperApiServiceClient
}

func NewDeveloperApiServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DeveloperApiServiceClient {
	return &DeveloperApiServiceClient{
		DeveloperApiServiceClient: developer_api.NewDeveloperApiServiceClientFactory(t, f),
	}
}

func NewDeveloperApiServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DeveloperApiServiceClient {
	return &DeveloperApiServiceClient{
		DeveloperApiServiceClient: developer_api.NewDeveloperApiServiceClientProtocol(t, iprot, oprot),
	}
}

func NewDeveloperApiServiceClient(c thrift.TClient) *DeveloperApiServiceClient {
	return &DeveloperApiServiceClient{
		DeveloperApiServiceClient: developer_api.NewDeveloperApiServiceClient(c),
	}
}

type PlaygroundService interface {
	playground.PlaygroundService
}

type PlaygroundServiceClient struct {
	*playground.PlaygroundServiceClient
}

func NewPlaygroundServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *PlaygroundServiceClient {
	return &PlaygroundServiceClient{
		PlaygroundServiceClient: playground.NewPlaygroundServiceClientFactory(t, f),
	}
}

func NewPlaygroundServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *PlaygroundServiceClient {
	return &PlaygroundServiceClient{
		PlaygroundServiceClient: playground.NewPlaygroundServiceClientProtocol(t, iprot, oprot),
	}
}

func NewPlaygroundServiceClient(c thrift.TClient) *PlaygroundServiceClient {
	return &PlaygroundServiceClient{
		PlaygroundServiceClient: playground.NewPlaygroundServiceClient(c),
	}
}

type DatabaseService interface {
	database.DatabaseService
}

type DatabaseServiceClient struct {
	*database.DatabaseServiceClient
}

func NewDatabaseServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DatabaseServiceClient {
	return &DatabaseServiceClient{
		DatabaseServiceClient: database.NewDatabaseServiceClientFactory(t, f),
	}
}

func NewDatabaseServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DatabaseServiceClient {
	return &DatabaseServiceClient{
		DatabaseServiceClient: database.NewDatabaseServiceClientProtocol(t, iprot, oprot),
	}
}

func NewDatabaseServiceClient(c thrift.TClient) *DatabaseServiceClient {
	return &DatabaseServiceClient{
		DatabaseServiceClient: database.NewDatabaseServiceClient(c),
	}
}

type ResourceService interface {
	resource.ResourceService
}

type ResourceServiceClient struct {
	*resource.ResourceServiceClient
}

func NewResourceServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ResourceServiceClient {
	return &ResourceServiceClient{
		ResourceServiceClient: resource.NewResourceServiceClientFactory(t, f),
	}
}

func NewResourceServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ResourceServiceClient {
	return &ResourceServiceClient{
		ResourceServiceClient: resource.NewResourceServiceClientProtocol(t, iprot, oprot),
	}
}

func NewResourceServiceClient(c thrift.TClient) *ResourceServiceClient {
	return &ResourceServiceClient{
		ResourceServiceClient: resource.NewResourceServiceClient(c),
	}
}

type PassportService interface {
	passport.PassportService
}

type PassportServiceClient struct {
	*passport.PassportServiceClient
}

func NewPassportServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *PassportServiceClient {
	return &PassportServiceClient{
		PassportServiceClient: passport.NewPassportServiceClientFactory(t, f),
	}
}

func NewPassportServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *PassportServiceClient {
	return &PassportServiceClient{
		PassportServiceClient: passport.NewPassportServiceClientProtocol(t, iprot, oprot),
	}
}

func NewPassportServiceClient(c thrift.TClient) *PassportServiceClient {
	return &PassportServiceClient{
		PassportServiceClient: passport.NewPassportServiceClient(c),
	}
}

type WorkflowService interface {
	workflow.WorkflowService
}

type WorkflowServiceClient struct {
	*workflow.WorkflowServiceClient
}

func NewWorkflowServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *WorkflowServiceClient {
	return &WorkflowServiceClient{
		WorkflowServiceClient: workflow.NewWorkflowServiceClientFactory(t, f),
	}
}

func NewWorkflowServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *WorkflowServiceClient {
	return &WorkflowServiceClient{
		WorkflowServiceClient: workflow.NewWorkflowServiceClientProtocol(t, iprot, oprot),
	}
}

func NewWorkflowServiceClient(c thrift.TClient) *WorkflowServiceClient {
	return &WorkflowServiceClient{
		WorkflowServiceClient: workflow.NewWorkflowServiceClient(c),
	}
}

type KnowledgeService interface {
	knowledge.DatasetService
}

type KnowledgeServiceClient struct {
	*knowledge.DatasetServiceClient
}

func NewKnowledgeServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *KnowledgeServiceClient {
	return &KnowledgeServiceClient{
		DatasetServiceClient: knowledge.NewDatasetServiceClientFactory(t, f),
	}
}

func NewKnowledgeServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *KnowledgeServiceClient {
	return &KnowledgeServiceClient{
		DatasetServiceClient: knowledge.NewDatasetServiceClientProtocol(t, iprot, oprot),
	}
}

func NewKnowledgeServiceClient(c thrift.TClient) *KnowledgeServiceClient {
	return &KnowledgeServiceClient{
		DatasetServiceClient: knowledge.NewDatasetServiceClient(c),
	}
}

type BotOpenApiService interface {
	bot_open_api.BotOpenApiService
}

type BotOpenApiServiceClient struct {
	*bot_open_api.BotOpenApiServiceClient
}

func NewBotOpenApiServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *BotOpenApiServiceClient {
	return &BotOpenApiServiceClient{
		BotOpenApiServiceClient: bot_open_api.NewBotOpenApiServiceClientFactory(t, f),
	}
}

func NewBotOpenApiServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *BotOpenApiServiceClient {
	return &BotOpenApiServiceClient{
		BotOpenApiServiceClient: bot_open_api.NewBotOpenApiServiceClientProtocol(t, iprot, oprot),
	}
}

func NewBotOpenApiServiceClient(c thrift.TClient) *BotOpenApiServiceClient {
	return &BotOpenApiServiceClient{
		BotOpenApiServiceClient: bot_open_api.NewBotOpenApiServiceClient(c),
	}
}

type UploadService interface {
	upload.UploadService
}

type UploadServiceClient struct {
	*upload.UploadServiceClient
}

func NewUploadServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *UploadServiceClient {
	return &UploadServiceClient{
		UploadServiceClient: upload.NewUploadServiceClientFactory(t, f),
	}
}

func NewUploadServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *UploadServiceClient {
	return &UploadServiceClient{
		UploadServiceClient: upload.NewUploadServiceClientProtocol(t, iprot, oprot),
	}
}

func NewUploadServiceClient(c thrift.TClient) *UploadServiceClient {
	return &UploadServiceClient{
		UploadServiceClient: upload.NewUploadServiceClient(c),
	}
}

type IntelligenceServiceProcessor struct {
	*intelligence.IntelligenceServiceProcessor
}

func NewIntelligenceServiceProcessor(handler IntelligenceService) *IntelligenceServiceProcessor {
	self := &IntelligenceServiceProcessor{intelligence.NewIntelligenceServiceProcessor(handler)}
	return self
}

type ConversationServiceProcessor struct {
	*conversation.ConversationServiceProcessor
}

func NewConversationServiceProcessor(handler ConversationService) *ConversationServiceProcessor {
	self := &ConversationServiceProcessor{conversation.NewConversationServiceProcessor(handler)}
	return self
}

type MessageServiceProcessor struct {
	*message.MessageServiceProcessor
}

func NewMessageServiceProcessor(handler MessageService) *MessageServiceProcessor {
	self := &MessageServiceProcessor{message.NewMessageServiceProcessor(handler)}
	return self
}

type AgentRunServiceProcessor struct {
	*agentrun.AgentRunServiceProcessor
}

func NewAgentRunServiceProcessor(handler AgentRunService) *AgentRunServiceProcessor {
	self := &AgentRunServiceProcessor{agentrun.NewAgentRunServiceProcessor(handler)}
	return self
}

type OpenAPIAuthServiceProcessor struct {
	*openapiauth.OpenAPIAuthServiceProcessor
}

func NewOpenAPIAuthServiceProcessor(handler OpenAPIAuthService) *OpenAPIAuthServiceProcessor {
	self := &OpenAPIAuthServiceProcessor{openapiauth.NewOpenAPIAuthServiceProcessor(handler)}
	return self
}

type MemoryServiceProcessor struct {
	*variable.MemoryServiceProcessor
}

func NewMemoryServiceProcessor(handler MemoryService) *MemoryServiceProcessor {
	self := &MemoryServiceProcessor{variable.NewMemoryServiceProcessor(handler)}
	return self
}

type PluginDevelopServiceProcessor struct {
	*plugin_develop.PluginDevelopServiceProcessor
}

func NewPluginDevelopServiceProcessor(handler PluginDevelopService) *PluginDevelopServiceProcessor {
	self := &PluginDevelopServiceProcessor{plugin_develop.NewPluginDevelopServiceProcessor(handler)}
	return self
}

type PublicProductServiceProcessor struct {
	*product_public_api.PublicProductServiceProcessor
}

func NewPublicProductServiceProcessor(handler PublicProductService) *PublicProductServiceProcessor {
	self := &PublicProductServiceProcessor{product_public_api.NewPublicProductServiceProcessor(handler)}
	return self
}

type DeveloperApiServiceProcessor struct {
	*developer_api.DeveloperApiServiceProcessor
}

func NewDeveloperApiServiceProcessor(handler DeveloperApiService) *DeveloperApiServiceProcessor {
	self := &DeveloperApiServiceProcessor{developer_api.NewDeveloperApiServiceProcessor(handler)}
	return self
}

type PlaygroundServiceProcessor struct {
	*playground.PlaygroundServiceProcessor
}

func NewPlaygroundServiceProcessor(handler PlaygroundService) *PlaygroundServiceProcessor {
	self := &PlaygroundServiceProcessor{playground.NewPlaygroundServiceProcessor(handler)}
	return self
}

type DatabaseServiceProcessor struct {
	*database.DatabaseServiceProcessor
}

func NewDatabaseServiceProcessor(handler DatabaseService) *DatabaseServiceProcessor {
	self := &DatabaseServiceProcessor{database.NewDatabaseServiceProcessor(handler)}
	return self
}

type ResourceServiceProcessor struct {
	*resource.ResourceServiceProcessor
}

func NewResourceServiceProcessor(handler ResourceService) *ResourceServiceProcessor {
	self := &ResourceServiceProcessor{resource.NewResourceServiceProcessor(handler)}
	return self
}

type PassportServiceProcessor struct {
	*passport.PassportServiceProcessor
}

func NewPassportServiceProcessor(handler PassportService) *PassportServiceProcessor {
	self := &PassportServiceProcessor{passport.NewPassportServiceProcessor(handler)}
	return self
}

type WorkflowServiceProcessor struct {
	*workflow.WorkflowServiceProcessor
}

func NewWorkflowServiceProcessor(handler WorkflowService) *WorkflowServiceProcessor {
	self := &WorkflowServiceProcessor{workflow.NewWorkflowServiceProcessor(handler)}
	return self
}

type KnowledgeServiceProcessor struct {
	*knowledge.DatasetServiceProcessor
}

func NewKnowledgeServiceProcessor(handler KnowledgeService) *KnowledgeServiceProcessor {
	self := &KnowledgeServiceProcessor{knowledge.NewDatasetServiceProcessor(handler)}
	return self
}

type BotOpenApiServiceProcessor struct {
	*bot_open_api.BotOpenApiServiceProcessor
}

func NewBotOpenApiServiceProcessor(handler BotOpenApiService) *BotOpenApiServiceProcessor {
	self := &BotOpenApiServiceProcessor{bot_open_api.NewBotOpenApiServiceProcessor(handler)}
	return self
}

type UploadServiceProcessor struct {
	*upload.UploadServiceProcessor
}

func NewUploadServiceProcessor(handler UploadService) *UploadServiceProcessor {
	self := &UploadServiceProcessor{upload.NewUploadServiceProcessor(handler)}
	return self
}
