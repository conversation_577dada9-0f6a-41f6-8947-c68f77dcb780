/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package conversation

import (
	"context"
	"fmt"

	"github.com/apache/thrift/lib/go/thrift"
)

type ConversationService interface {
	ClearConversationCtx(ctx context.Context, request *ClearConversationCtxRequest) (r *ClearConversationCtxResponse, err error)

	ClearConversationHistory(ctx context.Context, request *ClearConversationHistoryRequest) (r *ClearConversationHistoryResponse, err error)

	CreateConversation(ctx context.Context, request *CreateConversationRequest) (r *CreateConversationResponse, err error)

	ClearConversationApi(ctx context.Context, req *ClearConversationApiRequest) (r *ClearConversationApiResponse, err error)

	ListConversationsApi(ctx context.Context, request *ListConversationsApiRequest) (r *ListConversationsApiResponse, err error)

	UpdateConversationApi(ctx context.Context, request *UpdateConversationApiRequest) (r *UpdateConversationApiResponse, err error)

	DeleteConversationApi(ctx context.Context, req *DeleteConversationApiRequest) (r *DeleteConversationApiResponse, err error)
}

type ConversationServiceClient struct {
	c thrift.TClient
}

func NewConversationServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ConversationServiceClient {
	return &ConversationServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewConversationServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ConversationServiceClient {
	return &ConversationServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewConversationServiceClient(c thrift.TClient) *ConversationServiceClient {
	return &ConversationServiceClient{
		c: c,
	}
}

func (p *ConversationServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *ConversationServiceClient) ClearConversationCtx(ctx context.Context, request *ClearConversationCtxRequest) (r *ClearConversationCtxResponse, err error) {
	var _args ConversationServiceClearConversationCtxArgs
	_args.Request = request
	var _result ConversationServiceClearConversationCtxResult
	if err = p.Client_().Call(ctx, "ClearConversationCtx", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *ConversationServiceClient) ClearConversationHistory(ctx context.Context, request *ClearConversationHistoryRequest) (r *ClearConversationHistoryResponse, err error) {
	var _args ConversationServiceClearConversationHistoryArgs
	_args.Request = request
	var _result ConversationServiceClearConversationHistoryResult
	if err = p.Client_().Call(ctx, "ClearConversationHistory", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *ConversationServiceClient) CreateConversation(ctx context.Context, request *CreateConversationRequest) (r *CreateConversationResponse, err error) {
	var _args ConversationServiceCreateConversationArgs
	_args.Request = request
	var _result ConversationServiceCreateConversationResult
	if err = p.Client_().Call(ctx, "CreateConversation", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *ConversationServiceClient) ClearConversationApi(ctx context.Context, req *ClearConversationApiRequest) (r *ClearConversationApiResponse, err error) {
	var _args ConversationServiceClearConversationApiArgs
	_args.Req = req
	var _result ConversationServiceClearConversationApiResult
	if err = p.Client_().Call(ctx, "ClearConversationApi", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *ConversationServiceClient) ListConversationsApi(ctx context.Context, request *ListConversationsApiRequest) (r *ListConversationsApiResponse, err error) {
	var _args ConversationServiceListConversationsApiArgs
	_args.Request = request
	var _result ConversationServiceListConversationsApiResult
	if err = p.Client_().Call(ctx, "ListConversationsApi", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *ConversationServiceClient) UpdateConversationApi(ctx context.Context, request *UpdateConversationApiRequest) (r *UpdateConversationApiResponse, err error) {
	var _args ConversationServiceUpdateConversationApiArgs
	_args.Request = request
	var _result ConversationServiceUpdateConversationApiResult
	if err = p.Client_().Call(ctx, "UpdateConversationApi", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *ConversationServiceClient) DeleteConversationApi(ctx context.Context, req *DeleteConversationApiRequest) (r *DeleteConversationApiResponse, err error) {
	var _args ConversationServiceDeleteConversationApiArgs
	_args.Req = req
	var _result ConversationServiceDeleteConversationApiResult
	if err = p.Client_().Call(ctx, "DeleteConversationApi", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

type ConversationServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      ConversationService
}

func (p *ConversationServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *ConversationServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *ConversationServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewConversationServiceProcessor(handler ConversationService) *ConversationServiceProcessor {
	self := &ConversationServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self.AddToProcessorMap("ClearConversationCtx", &conversationServiceProcessorClearConversationCtx{handler: handler})
	self.AddToProcessorMap("ClearConversationHistory", &conversationServiceProcessorClearConversationHistory{handler: handler})
	self.AddToProcessorMap("CreateConversation", &conversationServiceProcessorCreateConversation{handler: handler})
	self.AddToProcessorMap("ClearConversationApi", &conversationServiceProcessorClearConversationApi{handler: handler})
	self.AddToProcessorMap("ListConversationsApi", &conversationServiceProcessorListConversationsApi{handler: handler})
	self.AddToProcessorMap("UpdateConversationApi", &conversationServiceProcessorUpdateConversationApi{handler: handler})
	self.AddToProcessorMap("DeleteConversationApi", &conversationServiceProcessorDeleteConversationApi{handler: handler})
	return self
}
func (p *ConversationServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush(ctx)
	return false, x
}

type conversationServiceProcessorClearConversationCtx struct {
	handler ConversationService
}

func (p *conversationServiceProcessorClearConversationCtx) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ConversationServiceClearConversationCtxArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ClearConversationCtx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ConversationServiceClearConversationCtxResult{}
	var retval *ClearConversationCtxResponse
	if retval, err2 = p.handler.ClearConversationCtx(ctx, args.Request); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ClearConversationCtx: "+err2.Error())
		oprot.WriteMessageBegin("ClearConversationCtx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ClearConversationCtx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type conversationServiceProcessorClearConversationHistory struct {
	handler ConversationService
}

func (p *conversationServiceProcessorClearConversationHistory) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ConversationServiceClearConversationHistoryArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ClearConversationHistory", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ConversationServiceClearConversationHistoryResult{}
	var retval *ClearConversationHistoryResponse
	if retval, err2 = p.handler.ClearConversationHistory(ctx, args.Request); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ClearConversationHistory: "+err2.Error())
		oprot.WriteMessageBegin("ClearConversationHistory", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ClearConversationHistory", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type conversationServiceProcessorCreateConversation struct {
	handler ConversationService
}

func (p *conversationServiceProcessorCreateConversation) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ConversationServiceCreateConversationArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("CreateConversation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ConversationServiceCreateConversationResult{}
	var retval *CreateConversationResponse
	if retval, err2 = p.handler.CreateConversation(ctx, args.Request); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing CreateConversation: "+err2.Error())
		oprot.WriteMessageBegin("CreateConversation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("CreateConversation", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type conversationServiceProcessorClearConversationApi struct {
	handler ConversationService
}

func (p *conversationServiceProcessorClearConversationApi) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ConversationServiceClearConversationApiArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ClearConversationApi", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ConversationServiceClearConversationApiResult{}
	var retval *ClearConversationApiResponse
	if retval, err2 = p.handler.ClearConversationApi(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ClearConversationApi: "+err2.Error())
		oprot.WriteMessageBegin("ClearConversationApi", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ClearConversationApi", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type conversationServiceProcessorListConversationsApi struct {
	handler ConversationService
}

func (p *conversationServiceProcessorListConversationsApi) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ConversationServiceListConversationsApiArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ListConversationsApi", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ConversationServiceListConversationsApiResult{}
	var retval *ListConversationsApiResponse
	if retval, err2 = p.handler.ListConversationsApi(ctx, args.Request); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ListConversationsApi: "+err2.Error())
		oprot.WriteMessageBegin("ListConversationsApi", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ListConversationsApi", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type conversationServiceProcessorUpdateConversationApi struct {
	handler ConversationService
}

func (p *conversationServiceProcessorUpdateConversationApi) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ConversationServiceUpdateConversationApiArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UpdateConversationApi", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ConversationServiceUpdateConversationApiResult{}
	var retval *UpdateConversationApiResponse
	if retval, err2 = p.handler.UpdateConversationApi(ctx, args.Request); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UpdateConversationApi: "+err2.Error())
		oprot.WriteMessageBegin("UpdateConversationApi", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UpdateConversationApi", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type conversationServiceProcessorDeleteConversationApi struct {
	handler ConversationService
}

func (p *conversationServiceProcessorDeleteConversationApi) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ConversationServiceDeleteConversationApiArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("DeleteConversationApi", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ConversationServiceDeleteConversationApiResult{}
	var retval *DeleteConversationApiResponse
	if retval, err2 = p.handler.DeleteConversationApi(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DeleteConversationApi: "+err2.Error())
		oprot.WriteMessageBegin("DeleteConversationApi", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("DeleteConversationApi", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type ConversationServiceClearConversationCtxArgs struct {
	Request *ClearConversationCtxRequest `thrift:"request,1"`
}

func NewConversationServiceClearConversationCtxArgs() *ConversationServiceClearConversationCtxArgs {
	return &ConversationServiceClearConversationCtxArgs{}
}

func (p *ConversationServiceClearConversationCtxArgs) InitDefault() {
}

var ConversationServiceClearConversationCtxArgs_Request_DEFAULT *ClearConversationCtxRequest

func (p *ConversationServiceClearConversationCtxArgs) GetRequest() (v *ClearConversationCtxRequest) {
	if !p.IsSetRequest() {
		return ConversationServiceClearConversationCtxArgs_Request_DEFAULT
	}
	return p.Request
}

var fieldIDToName_ConversationServiceClearConversationCtxArgs = map[int16]string{
	1: "request",
}

func (p *ConversationServiceClearConversationCtxArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *ConversationServiceClearConversationCtxArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceClearConversationCtxArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceClearConversationCtxArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewClearConversationCtxRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Request = _field
	return nil
}

func (p *ConversationServiceClearConversationCtxArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ClearConversationCtx_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceClearConversationCtxArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("request", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Request.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ConversationServiceClearConversationCtxArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceClearConversationCtxArgs(%+v)", *p)

}

type ConversationServiceClearConversationCtxResult struct {
	Success *ClearConversationCtxResponse `thrift:"success,0,optional"`
}

func NewConversationServiceClearConversationCtxResult() *ConversationServiceClearConversationCtxResult {
	return &ConversationServiceClearConversationCtxResult{}
}

func (p *ConversationServiceClearConversationCtxResult) InitDefault() {
}

var ConversationServiceClearConversationCtxResult_Success_DEFAULT *ClearConversationCtxResponse

func (p *ConversationServiceClearConversationCtxResult) GetSuccess() (v *ClearConversationCtxResponse) {
	if !p.IsSetSuccess() {
		return ConversationServiceClearConversationCtxResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ConversationServiceClearConversationCtxResult = map[int16]string{
	0: "success",
}

func (p *ConversationServiceClearConversationCtxResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ConversationServiceClearConversationCtxResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceClearConversationCtxResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceClearConversationCtxResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewClearConversationCtxResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ConversationServiceClearConversationCtxResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ClearConversationCtx_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceClearConversationCtxResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ConversationServiceClearConversationCtxResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceClearConversationCtxResult(%+v)", *p)

}

type ConversationServiceClearConversationHistoryArgs struct {
	Request *ClearConversationHistoryRequest `thrift:"request,1"`
}

func NewConversationServiceClearConversationHistoryArgs() *ConversationServiceClearConversationHistoryArgs {
	return &ConversationServiceClearConversationHistoryArgs{}
}

func (p *ConversationServiceClearConversationHistoryArgs) InitDefault() {
}

var ConversationServiceClearConversationHistoryArgs_Request_DEFAULT *ClearConversationHistoryRequest

func (p *ConversationServiceClearConversationHistoryArgs) GetRequest() (v *ClearConversationHistoryRequest) {
	if !p.IsSetRequest() {
		return ConversationServiceClearConversationHistoryArgs_Request_DEFAULT
	}
	return p.Request
}

var fieldIDToName_ConversationServiceClearConversationHistoryArgs = map[int16]string{
	1: "request",
}

func (p *ConversationServiceClearConversationHistoryArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *ConversationServiceClearConversationHistoryArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceClearConversationHistoryArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceClearConversationHistoryArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewClearConversationHistoryRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Request = _field
	return nil
}

func (p *ConversationServiceClearConversationHistoryArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ClearConversationHistory_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceClearConversationHistoryArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("request", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Request.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ConversationServiceClearConversationHistoryArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceClearConversationHistoryArgs(%+v)", *p)

}

type ConversationServiceClearConversationHistoryResult struct {
	Success *ClearConversationHistoryResponse `thrift:"success,0,optional"`
}

func NewConversationServiceClearConversationHistoryResult() *ConversationServiceClearConversationHistoryResult {
	return &ConversationServiceClearConversationHistoryResult{}
}

func (p *ConversationServiceClearConversationHistoryResult) InitDefault() {
}

var ConversationServiceClearConversationHistoryResult_Success_DEFAULT *ClearConversationHistoryResponse

func (p *ConversationServiceClearConversationHistoryResult) GetSuccess() (v *ClearConversationHistoryResponse) {
	if !p.IsSetSuccess() {
		return ConversationServiceClearConversationHistoryResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ConversationServiceClearConversationHistoryResult = map[int16]string{
	0: "success",
}

func (p *ConversationServiceClearConversationHistoryResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ConversationServiceClearConversationHistoryResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceClearConversationHistoryResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceClearConversationHistoryResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewClearConversationHistoryResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ConversationServiceClearConversationHistoryResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ClearConversationHistory_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceClearConversationHistoryResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ConversationServiceClearConversationHistoryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceClearConversationHistoryResult(%+v)", *p)

}

type ConversationServiceCreateConversationArgs struct {
	Request *CreateConversationRequest `thrift:"request,1"`
}

func NewConversationServiceCreateConversationArgs() *ConversationServiceCreateConversationArgs {
	return &ConversationServiceCreateConversationArgs{}
}

func (p *ConversationServiceCreateConversationArgs) InitDefault() {
}

var ConversationServiceCreateConversationArgs_Request_DEFAULT *CreateConversationRequest

func (p *ConversationServiceCreateConversationArgs) GetRequest() (v *CreateConversationRequest) {
	if !p.IsSetRequest() {
		return ConversationServiceCreateConversationArgs_Request_DEFAULT
	}
	return p.Request
}

var fieldIDToName_ConversationServiceCreateConversationArgs = map[int16]string{
	1: "request",
}

func (p *ConversationServiceCreateConversationArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *ConversationServiceCreateConversationArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceCreateConversationArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceCreateConversationArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCreateConversationRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Request = _field
	return nil
}

func (p *ConversationServiceCreateConversationArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateConversation_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceCreateConversationArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("request", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Request.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ConversationServiceCreateConversationArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceCreateConversationArgs(%+v)", *p)

}

type ConversationServiceCreateConversationResult struct {
	Success *CreateConversationResponse `thrift:"success,0,optional"`
}

func NewConversationServiceCreateConversationResult() *ConversationServiceCreateConversationResult {
	return &ConversationServiceCreateConversationResult{}
}

func (p *ConversationServiceCreateConversationResult) InitDefault() {
}

var ConversationServiceCreateConversationResult_Success_DEFAULT *CreateConversationResponse

func (p *ConversationServiceCreateConversationResult) GetSuccess() (v *CreateConversationResponse) {
	if !p.IsSetSuccess() {
		return ConversationServiceCreateConversationResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ConversationServiceCreateConversationResult = map[int16]string{
	0: "success",
}

func (p *ConversationServiceCreateConversationResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ConversationServiceCreateConversationResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceCreateConversationResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceCreateConversationResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCreateConversationResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ConversationServiceCreateConversationResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateConversation_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceCreateConversationResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ConversationServiceCreateConversationResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceCreateConversationResult(%+v)", *p)

}

type ConversationServiceClearConversationApiArgs struct {
	Req *ClearConversationApiRequest `thrift:"req,1"`
}

func NewConversationServiceClearConversationApiArgs() *ConversationServiceClearConversationApiArgs {
	return &ConversationServiceClearConversationApiArgs{}
}

func (p *ConversationServiceClearConversationApiArgs) InitDefault() {
}

var ConversationServiceClearConversationApiArgs_Req_DEFAULT *ClearConversationApiRequest

func (p *ConversationServiceClearConversationApiArgs) GetReq() (v *ClearConversationApiRequest) {
	if !p.IsSetReq() {
		return ConversationServiceClearConversationApiArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_ConversationServiceClearConversationApiArgs = map[int16]string{
	1: "req",
}

func (p *ConversationServiceClearConversationApiArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *ConversationServiceClearConversationApiArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceClearConversationApiArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceClearConversationApiArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewClearConversationApiRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *ConversationServiceClearConversationApiArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ClearConversationApi_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceClearConversationApiArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ConversationServiceClearConversationApiArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceClearConversationApiArgs(%+v)", *p)

}

type ConversationServiceClearConversationApiResult struct {
	Success *ClearConversationApiResponse `thrift:"success,0,optional"`
}

func NewConversationServiceClearConversationApiResult() *ConversationServiceClearConversationApiResult {
	return &ConversationServiceClearConversationApiResult{}
}

func (p *ConversationServiceClearConversationApiResult) InitDefault() {
}

var ConversationServiceClearConversationApiResult_Success_DEFAULT *ClearConversationApiResponse

func (p *ConversationServiceClearConversationApiResult) GetSuccess() (v *ClearConversationApiResponse) {
	if !p.IsSetSuccess() {
		return ConversationServiceClearConversationApiResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ConversationServiceClearConversationApiResult = map[int16]string{
	0: "success",
}

func (p *ConversationServiceClearConversationApiResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ConversationServiceClearConversationApiResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceClearConversationApiResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceClearConversationApiResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewClearConversationApiResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ConversationServiceClearConversationApiResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ClearConversationApi_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceClearConversationApiResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ConversationServiceClearConversationApiResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceClearConversationApiResult(%+v)", *p)

}

type ConversationServiceListConversationsApiArgs struct {
	Request *ListConversationsApiRequest `thrift:"request,1"`
}

func NewConversationServiceListConversationsApiArgs() *ConversationServiceListConversationsApiArgs {
	return &ConversationServiceListConversationsApiArgs{}
}

func (p *ConversationServiceListConversationsApiArgs) InitDefault() {
}

var ConversationServiceListConversationsApiArgs_Request_DEFAULT *ListConversationsApiRequest

func (p *ConversationServiceListConversationsApiArgs) GetRequest() (v *ListConversationsApiRequest) {
	if !p.IsSetRequest() {
		return ConversationServiceListConversationsApiArgs_Request_DEFAULT
	}
	return p.Request
}

var fieldIDToName_ConversationServiceListConversationsApiArgs = map[int16]string{
	1: "request",
}

func (p *ConversationServiceListConversationsApiArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *ConversationServiceListConversationsApiArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceListConversationsApiArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceListConversationsApiArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewListConversationsApiRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Request = _field
	return nil
}

func (p *ConversationServiceListConversationsApiArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListConversationsApi_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceListConversationsApiArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("request", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Request.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ConversationServiceListConversationsApiArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceListConversationsApiArgs(%+v)", *p)

}

type ConversationServiceListConversationsApiResult struct {
	Success *ListConversationsApiResponse `thrift:"success,0,optional"`
}

func NewConversationServiceListConversationsApiResult() *ConversationServiceListConversationsApiResult {
	return &ConversationServiceListConversationsApiResult{}
}

func (p *ConversationServiceListConversationsApiResult) InitDefault() {
}

var ConversationServiceListConversationsApiResult_Success_DEFAULT *ListConversationsApiResponse

func (p *ConversationServiceListConversationsApiResult) GetSuccess() (v *ListConversationsApiResponse) {
	if !p.IsSetSuccess() {
		return ConversationServiceListConversationsApiResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ConversationServiceListConversationsApiResult = map[int16]string{
	0: "success",
}

func (p *ConversationServiceListConversationsApiResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ConversationServiceListConversationsApiResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceListConversationsApiResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceListConversationsApiResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewListConversationsApiResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ConversationServiceListConversationsApiResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListConversationsApi_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceListConversationsApiResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ConversationServiceListConversationsApiResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceListConversationsApiResult(%+v)", *p)

}

type ConversationServiceUpdateConversationApiArgs struct {
	Request *UpdateConversationApiRequest `thrift:"request,1"`
}

func NewConversationServiceUpdateConversationApiArgs() *ConversationServiceUpdateConversationApiArgs {
	return &ConversationServiceUpdateConversationApiArgs{}
}

func (p *ConversationServiceUpdateConversationApiArgs) InitDefault() {
}

var ConversationServiceUpdateConversationApiArgs_Request_DEFAULT *UpdateConversationApiRequest

func (p *ConversationServiceUpdateConversationApiArgs) GetRequest() (v *UpdateConversationApiRequest) {
	if !p.IsSetRequest() {
		return ConversationServiceUpdateConversationApiArgs_Request_DEFAULT
	}
	return p.Request
}

var fieldIDToName_ConversationServiceUpdateConversationApiArgs = map[int16]string{
	1: "request",
}

func (p *ConversationServiceUpdateConversationApiArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *ConversationServiceUpdateConversationApiArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceUpdateConversationApiArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceUpdateConversationApiArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUpdateConversationApiRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Request = _field
	return nil
}

func (p *ConversationServiceUpdateConversationApiArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateConversationApi_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceUpdateConversationApiArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("request", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Request.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ConversationServiceUpdateConversationApiArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceUpdateConversationApiArgs(%+v)", *p)

}

type ConversationServiceUpdateConversationApiResult struct {
	Success *UpdateConversationApiResponse `thrift:"success,0,optional"`
}

func NewConversationServiceUpdateConversationApiResult() *ConversationServiceUpdateConversationApiResult {
	return &ConversationServiceUpdateConversationApiResult{}
}

func (p *ConversationServiceUpdateConversationApiResult) InitDefault() {
}

var ConversationServiceUpdateConversationApiResult_Success_DEFAULT *UpdateConversationApiResponse

func (p *ConversationServiceUpdateConversationApiResult) GetSuccess() (v *UpdateConversationApiResponse) {
	if !p.IsSetSuccess() {
		return ConversationServiceUpdateConversationApiResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ConversationServiceUpdateConversationApiResult = map[int16]string{
	0: "success",
}

func (p *ConversationServiceUpdateConversationApiResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ConversationServiceUpdateConversationApiResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceUpdateConversationApiResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceUpdateConversationApiResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUpdateConversationApiResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ConversationServiceUpdateConversationApiResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateConversationApi_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceUpdateConversationApiResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ConversationServiceUpdateConversationApiResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceUpdateConversationApiResult(%+v)", *p)

}

type ConversationServiceDeleteConversationApiArgs struct {
	Req *DeleteConversationApiRequest `thrift:"req,1"`
}

func NewConversationServiceDeleteConversationApiArgs() *ConversationServiceDeleteConversationApiArgs {
	return &ConversationServiceDeleteConversationApiArgs{}
}

func (p *ConversationServiceDeleteConversationApiArgs) InitDefault() {
}

var ConversationServiceDeleteConversationApiArgs_Req_DEFAULT *DeleteConversationApiRequest

func (p *ConversationServiceDeleteConversationApiArgs) GetReq() (v *DeleteConversationApiRequest) {
	if !p.IsSetReq() {
		return ConversationServiceDeleteConversationApiArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_ConversationServiceDeleteConversationApiArgs = map[int16]string{
	1: "req",
}

func (p *ConversationServiceDeleteConversationApiArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *ConversationServiceDeleteConversationApiArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceDeleteConversationApiArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceDeleteConversationApiArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDeleteConversationApiRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *ConversationServiceDeleteConversationApiArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteConversationApi_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceDeleteConversationApiArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ConversationServiceDeleteConversationApiArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceDeleteConversationApiArgs(%+v)", *p)

}

type ConversationServiceDeleteConversationApiResult struct {
	Success *DeleteConversationApiResponse `thrift:"success,0,optional"`
}

func NewConversationServiceDeleteConversationApiResult() *ConversationServiceDeleteConversationApiResult {
	return &ConversationServiceDeleteConversationApiResult{}
}

func (p *ConversationServiceDeleteConversationApiResult) InitDefault() {
}

var ConversationServiceDeleteConversationApiResult_Success_DEFAULT *DeleteConversationApiResponse

func (p *ConversationServiceDeleteConversationApiResult) GetSuccess() (v *DeleteConversationApiResponse) {
	if !p.IsSetSuccess() {
		return ConversationServiceDeleteConversationApiResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ConversationServiceDeleteConversationApiResult = map[int16]string{
	0: "success",
}

func (p *ConversationServiceDeleteConversationApiResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ConversationServiceDeleteConversationApiResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConversationServiceDeleteConversationApiResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConversationServiceDeleteConversationApiResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDeleteConversationApiResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ConversationServiceDeleteConversationApiResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteConversationApi_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConversationServiceDeleteConversationApiResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ConversationServiceDeleteConversationApiResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConversationServiceDeleteConversationApiResult(%+v)", *p)

}
