/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator.

package coze

import (
	"context"
	"fmt"

	openapiauthApp "github.com/coze-dev/coze-studio/backend/application/openauth"
	"github.com/coze-dev/coze-studio/backend/application/plugin"
	"github.com/coze-dev/coze-studio/backend/application/singleagent"
	"github.com/coze-dev/coze-studio/backend/application/upload"
	"github.com/coze-dev/coze-studio/backend/domain/plugin/conf"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	"github.com/coze-dev/coze-studio/backend/api/model/app/bot_open_api"
)

// OauthAuthorizationCode .
// @router /api/oauth/authorization_code [GET]
func OauthAuthorizationCode(ctx context.Context, c *app.RequestContext) {
	var err error
	var req bot_open_api.OauthAuthorizationCodeReq
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.Code == "" {
		invalidParamRequestResponse(c, "authorization failed, code is required")
		return
	}
	if req.State == "" {
		invalidParamRequestResponse(c, "state is required")
		return
	}

	_, err = plugin.PluginApplicationSVC.OauthAuthorizationCode(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	redirectURL := fmt.Sprintf("%s/information/auth/success", conf.GetServerHost())
	c.Redirect(consts.StatusFound, []byte(redirectURL))
	c.Abort()

	return
}

// UploadFileOpen .
// @router /v1/files/upload [POST]
func UploadFileOpen(ctx context.Context, c *app.RequestContext) {
	var err error
	var req bot_open_api.UploadFileOpenRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp := new(bot_open_api.UploadFileOpenResponse)
	resp, err = upload.SVC.UploadFileOpen(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetBotOnlineInfo .
// @router /v1/bot/get_online_info [GET]
func GetBotOnlineInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req bot_open_api.GetBotOnlineInfoReq
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := singleagent.SingleAgentSVC.GetAgentOnlineInfo(ctx, &req)

	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// ImpersonateCozeUser .
// @router /api/permission_api/coze_web_app/impersonate_coze_user [POST]
func ImpersonateCozeUser(ctx context.Context, c *app.RequestContext) {
	var err error
	var req bot_open_api.ImpersonateCozeUserRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := openapiauthApp.OpenAuthApplication.ImpersonateCozeUserAccessToken(ctx, &req)

	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// OpenGetBotInfo .
// @router /v1/bots/:bot_id [GET]
func OpenGetBotInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req bot_open_api.OpenGetBotInfoRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := singleagent.SingleAgentSVC.OpenGetBotInfo(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}
