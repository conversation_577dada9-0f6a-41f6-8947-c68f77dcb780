/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator.

package coze

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	"github.com/coze-dev/coze-studio/backend/api/model/conversation/conversation"
	application "github.com/coze-dev/coze-studio/backend/application/conversation"
	"github.com/coze-dev/coze-studio/backend/pkg/errorx"
	"github.com/coze-dev/coze-studio/backend/types/errno"
)

// ClearConversationHistory .
// @router /api/conversation/clear_message [POST]
func ClearConversationHistory(ctx context.Context, c *app.RequestContext) {
	var err error
	var req conversation.ClearConversationHistoryRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if checkErr := checkCCHParams(ctx, &req); checkErr != nil {
		invalidParamRequestResponse(c, checkErr.Error())
		return
	}

	resp, err := application.ConversationSVC.ClearHistory(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

func checkCCHParams(_ context.Context, req *conversation.ClearConversationHistoryRequest) error {
	if req.ConversationID <= 0 {
		return errorx.New(errno.ErrConversationInvalidParamCode, errorx.KV("msg", "invalid conversation id"))
	}

	if req.Scene == nil {
		return errorx.New(errno.ErrConversationInvalidParamCode, errorx.KV("msg", "scene is required"))
	}

	return nil
}

// ClearConversationCtx .
// @router /api/conversation/create_section [POST]
func ClearConversationCtx(ctx context.Context, c *app.RequestContext) {
	resp := new(conversation.ClearConversationCtxResponse)
	var err error
	var req conversation.ClearConversationCtxRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if checkErr := checkCCCParams(ctx, &req); checkErr != nil {
		invalidParamRequestResponse(c, checkErr.Error())
		return
	}

	newSectionID, err := application.ConversationSVC.CreateSection(ctx, req.ConversationID)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	resp.NewSectionID = newSectionID

	c.JSON(consts.StatusOK, resp)
}

func checkCCCParams(ctx context.Context, req *conversation.ClearConversationCtxRequest) error {
	if req.ConversationID <= 0 {
		return errorx.New(errno.ErrConversationInvalidParamCode, errorx.KV("msg", "invalid conversation id"))
	}

	if req.Scene == nil {
		return errorx.New(errno.ErrConversationInvalidParamCode, errorx.KV("msg", "scene is required"))
	}
	return nil
}

// CreateConversation .
// @router /api/conversation/create [POST]
func CreateConversation(ctx context.Context, c *app.RequestContext) {
	var err error
	var req conversation.CreateConversationRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := application.ConversationSVC.CreateConversation(ctx, req.GetBotId(), req.GetConnectorId())
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// ClearConversationApi .
// @router /v1/conversations/:conversation_id/clear [POST]
func ClearConversationApi(ctx context.Context, c *app.RequestContext) {
	var err error
	var req conversation.ClearConversationApiRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp := new(conversation.ClearConversationApiResponse)

	sectionID, err := application.ConversationSVC.CreateSection(ctx, req.ConversationID)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	resp.Data = &conversation.Section{
		ID:             sectionID,
		ConversationID: req.ConversationID,
	}

	c.JSON(consts.StatusOK, resp)
}

// ListConversationsApi .
// @router /v1/conversations [GET]
func ListConversationsApi(ctx context.Context, c *app.RequestContext) {
	var err error
	var req conversation.ListConversationsApiRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := application.ConversationSVC.ListConversation(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UpdateConversationApi .
// @router /v1/conversations/:conversation_id [PUT]
func UpdateConversationApi(ctx context.Context, c *app.RequestContext) {
	var err error
	var req conversation.UpdateConversationApiRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := application.ConversationSVC.UpdateConversation(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DeleteConversationApi .
// @router /v1/conversations/:conversation_id [DELETE]
func DeleteConversationApi(ctx context.Context, c *app.RequestContext) {
	var err error
	var req conversation.DeleteConversationApiRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}
	resp, err := application.ConversationSVC.DeleteConversation(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}
