{"nodes": [{"id": "100001", "type": "1", "meta": {"position": {"x": -480.76936241417366, "y": -827.3429928800074}}, "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"type": "string", "name": "input", "required": false}], "trigger_parameters": []}}, {"id": "900001", "type": "2", "meta": {"position": {"x": 1074.4690350659453, "y": -1405.100726522103}}, "data": {"nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}, "inputs": {"terminatePlan": "returnVariables", "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "input"}, "rawMeta": {"type": 1}}}}]}}}, {"id": "138145", "type": "9", "meta": {"position": {"x": -337.08518741667314, "y": -1143.5656083443255}}, "data": {"nodeMeta": {"title": "c1", "description": "c1", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Workflow-v2.jpg", "isImageflow": false}, "inputs": {"workflowId": "7511615200781402118", "spaceId": "666", "workflowVersion": "", "inputDefs": [{"name": "input", "type": "string"}], "type": 0, "inputParameters": [], "settingOnError": {}}, "outputs": [{"type": "string", "name": "output"}]}}, {"id": "162612", "type": "4", "meta": {"position": {"x": 541.3322143572306, "y": -1280.9656083443256}}, "data": {"nodeMeta": {"title": "top_news", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "pl:top_news", "description": "帮助用户获取搜狐网上的每日热闻"}, "inputs": {"apiParam": [{"name": "apiID", "input": {"type": "string", "value": {"type": "literal", "content": "7511617581250248704", "rawMeta": {"type": 1}}}}, {"name": "apiName", "input": {"type": "string", "value": {"type": "literal", "content": "pl", "rawMeta": {"type": 1}}}}, {"name": "pluginID", "input": {"type": "string", "value": {"type": "literal", "content": "7511616454588891136", "rawMeta": {"type": 1}}}}, {"name": "pluginName", "input": {"type": "string", "value": {"type": "literal", "content": "pl", "rawMeta": {"type": 1}}}}, {"name": "pluginVersion", "input": {"type": "string", "value": {"type": "literal", "content": "0", "rawMeta": {"type": 1}}}}, {"name": "tips", "input": {"type": "string", "value": {"type": "literal", "content": "", "rawMeta": {"type": 1}}}}, {"name": "outDocLink", "input": {"type": "string", "value": {"type": "literal", "content": "", "rawMeta": {"type": 1}}}}], "inputParameters": [{"name": "count", "input": {"type": "integer", "value": {"type": "literal", "content": 12, "rawMeta": {"type": 2}}}}, {"name": "q", "input": {"type": "string", "value": {"type": "literal", "content": "12", "rawMeta": {"type": 1}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "traceId"}, {"type": "float", "name": "code"}, {"type": "object", "name": "data", "schema": [{"type": "object", "name": "coze_ark_001", "schema": [{"type": "list", "name": "list", "schema": {"type": "object", "schema": [{"type": "string", "name": "brief"}, {"type": "string", "name": "title"}, {"type": "string", "name": "url"}]}}]}]}, {"type": "float", "name": "total"}, {"type": "string", "name": "message"}, {"type": "boolean", "name": "success"}]}}, {"id": "199400", "type": "3", "meta": {"position": {"x": 51.00828691811297, "y": -1320.3134331225874}}, "data": {"nodeMeta": {"title": "大模型", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "description": "调用大语言模型,使用变量和提示词生成回复", "mainColor": "#5C62FF", "subTitle": "大模型"}, "inputs": {"inputParameters": [{"name": "input", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "input"}, "rawMeta": {"type": 1}}}}], "llmParam": [{"name": "modelType", "input": {"type": "integer", "value": {"type": "literal", "content": "1", "rawMeta": {"type": 2}}}}, {"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "豆包·1.5·Pro·32k", "rawMeta": {"type": 1}}}}, {"name": "generationDiversity", "input": {"type": "string", "value": {"type": "literal", "content": "balance", "rawMeta": {"type": 1}}}}, {"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": "0.8", "rawMeta": {"type": 4}}}}, {"name": "maxTokens", "input": {"type": "integer", "value": {"type": "literal", "content": "4096", "rawMeta": {"type": 2}}}}, {"name": "responseFormat", "input": {"type": "integer", "value": {"type": "literal", "content": "2", "rawMeta": {"type": 2}}}}, {"name": "prompt", "input": {"type": "string", "value": {"type": "literal", "content": "{{input}}", "rawMeta": {"type": 1}}}}, {"name": "enableChatHistory", "input": {"type": "boolean", "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}}}, {"name": "chatHistoryRound", "input": {"type": "integer", "value": {"type": "literal", "content": "3", "rawMeta": {"type": 2}}}}, {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "you are go coder", "rawMeta": {"type": 1}}}}], "fcParam": {"pluginFCParam": {"pluginList": [{"plugin_id": "7511616454588891136", "api_id": "7511617581250248704", "api_name": "top_news", "plugin_version": "0", "is_draft": false}]}}, "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "output"}], "version": "3"}}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "138145"}, {"sourceNodeID": "162612", "targetNodeID": "900001"}, {"sourceNodeID": "138145", "targetNodeID": "199400"}, {"sourceNodeID": "199400", "targetNodeID": "162612"}], "versions": {"loop": "v2"}}