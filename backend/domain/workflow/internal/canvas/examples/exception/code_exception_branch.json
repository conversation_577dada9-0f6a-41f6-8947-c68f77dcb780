{"nodes": [{"blocks": [], "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"name": "input", "required": false, "type": "string"}], "trigger_parameters": []}, "edges": null, "id": "100001", "meta": {"position": {"x": 323.05, "y": -304.1166666666667}}, "type": "1"}, {"blocks": [], "data": {"inputs": {"inputParameters": [{"input": {"type": "boolean", "value": {"content": {"blockID": "156675", "name": "isSuccess", "source": "block-output"}, "rawMeta": {"type": 3}, "type": "ref"}}, "name": "output"}, {"input": {"type": "string", "value": {"content": {"blockID": "198291", "name": "output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "output1"}], "terminatePlan": "returnVariables"}, "nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}}, "edges": null, "id": "900001", "meta": {"position": {"x": 344.4333333333333, "y": 178.68333333333334}}, "type": "2"}, {"blocks": [], "data": {"inputs": {"code": "# 在这里，您可以通过 ‘args’  获取节点中的输入变量，并通过 'ret' 输出结果\n# 'args' 和 'ret' 已经被正确地注入到环境中\n# 下面是一个示例，首先获取节点的全部输入参数params，其次获取其中参数名为‘input’的值：\n# params = args.params; \n# input = params.input;\n# 下面是一个示例，输出一个包含多种数据类型的 'ret' 对象：\n# ret: Output =  { \"name\": ‘小明’, \"hobbies\": [“看书”, “旅游”] };\n\nasync def main(args: Args) -> Output:\n    params = args.params\n    # 构建输出对象\n    ret: Output = {\n        \"key0\": params['input'] + fn(params['input']), # 拼接两次入参 input 的值\n        \"key1\": [\"hello\", \"world\"],  # 输出一个数组\n        \"key2\": { # 输出一个Object \n            \"key21\": \"hi\"\n        },\n    }\n    return ret", "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "input", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "input"}], "language": 3, "settingOnError": {"dataOnErr": "{\n    \"key0\": \"\",\n    \"key1\": [],\n    \"key2\": {\n        \"key21\": \"\"\n    }\n}", "processType": 3, "retryTimes": 0, "switch": true, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "mainColor": "#00B2B2", "subTitle": "代码", "title": "代码"}, "outputs": [{"name": "key0", "type": "string"}, {"name": "key1", "schema": {"type": "string"}, "type": "list"}, {"name": "key2", "schema": [{"name": "key21", "type": "string"}], "type": "object"}, {"name": "errorBody", "readonly": true, "schema": [{"name": "errorMessage", "readonly": true, "type": "string"}, {"name": "errorCode", "readonly": true, "type": "string"}], "type": "object"}, {"name": "isSuccess", "readonly": true, "type": "boolean"}]}, "edges": null, "id": "156675", "meta": {"position": {"x": 330.91666666666663, "y": -122.33383333333333}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"concatParams": [{"input": {"type": "string", "value": {"content": "code result: {{String1}}", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "concatResult"}, {"input": {"type": "string", "value": {"content": "，", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "arrayItemConcatChar"}, {"input": {"schema": {"schema": [{"name": "label", "required": true, "type": "string"}, {"name": "value", "required": true, "type": "string"}, {"name": "isDefault", "required": true, "type": "boolean"}], "type": "object"}, "type": "list", "value": {"content": [{"isDefault": true, "label": "换行", "value": "\n"}, {"isDefault": true, "label": "制表符", "value": "\t"}, {"isDefault": true, "label": "句号", "value": "。"}, {"isDefault": true, "label": "逗号", "value": "，"}, {"isDefault": true, "label": "分号", "value": "；"}, {"isDefault": true, "label": "空格", "value": " "}], "type": "literal"}}, "name": "allArrayItemConcatChars"}], "inputParameters": [{"input": {"type": "boolean", "value": {"content": {"blockID": "156675", "name": "isSuccess", "source": "block-output"}, "rawMeta": {"type": 3}, "type": "ref"}}, "name": "String1"}], "method": "concat"}, "nodeMeta": {"description": "用于处理多个字符串类型变量的格式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-StrConcat-v2.jpg", "mainColor": "#3071F2", "subTitle": "文本处理", "title": "文本处理"}, "outputs": [{"name": "output", "required": true, "type": "string"}]}, "edges": null, "id": "198291", "meta": {"position": {"x": 761.65, "y": 23.21616666666668}}, "type": "15"}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "156675", "sourcePortID": ""}, {"sourceNodeID": "156675", "targetNodeID": "900001", "sourcePortID": "default"}, {"sourceNodeID": "198291", "targetNodeID": "900001", "sourcePortID": ""}, {"sourceNodeID": "156675", "targetNodeID": "198291", "sourcePortID": "branch_error"}], "versions": {"loop": "v2"}}