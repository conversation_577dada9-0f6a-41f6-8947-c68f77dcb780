{"nodes": [{"id": "100001", "type": "1", "meta": {"position": {"x": 0, "y": 0}}, "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start.png", "subTitle": "", "title": "开始"}, "outputs": [{"type": "string", "name": "input", "required": false}], "trigger_parameters": []}}, {"id": "900001", "type": "2", "meta": {"position": {"x": 971, "y": -149}}, "data": {"nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End.png", "subTitle": "", "title": "结束"}, "inputs": {"terminatePlan": "returnVariables", "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "input"}, "rawMeta": {"type": 1}}}}]}}}, {"id": "166612", "type": "9", "meta": {"position": {"x": 418, "y": -191.5}}, "data": {"nodeMeta": {"title": "child_child_3", "description": "3", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Workflow-v2.jpg", "isImageflow": false}, "inputs": {"workflowId": "7515027249628708864", "spaceId": "666", "workflowVersion": "", "inputDefs": [{"name": "input", "type": "string"}], "type": 0, "inputParameters": [], "settingOnError": {}}, "outputs": [{"type": "string", "name": "output"}]}}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "166612"}, {"sourceNodeID": "166612", "targetNodeID": "900001"}], "versions": {"loop": "v2"}}