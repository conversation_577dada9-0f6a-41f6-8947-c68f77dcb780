{"nodes": [{"blocks": [], "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"name": "input", "required": false, "type": "string"}], "trigger_parameters": []}, "edges": null, "id": "100001", "meta": {"position": {"x": -10, "y": -12.649999999999999}}, "type": "1"}, {"blocks": [], "data": {"inputs": {"content": {"type": "string", "value": {"content": "pure_output_for_subworkflow exit: {{output}}", "type": "literal"}}, "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "156549", "name": "output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "output"}, {"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "input", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "input"}], "streamingOutput": true, "terminatePlan": "useAnswerContent"}, "nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}}, "edges": null, "id": "900001", "meta": {"position": {"x": 1298.5, "y": -25.65}}, "type": "2"}, {"blocks": [], "data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "input", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "input"}], "llmParam": [{"input": {"type": "float", "value": {"content": "0.8", "rawMeta": {"type": 4}, "type": "literal"}}, "name": "temperature"}, {"input": {"type": "integer", "value": {"content": "4096", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "integer", "value": {"content": "0", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "string", "value": {"content": "豆包·1.5·Pro·32k", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "modleName"}, {"input": {"type": "integer", "value": {"content": "1737521813", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "balance", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "string", "value": {"content": "{{input}}", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "rawMeta": {"type": 3}, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "systemPrompt"}], "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 600000}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "subTitle": "大模型", "title": "大模型"}, "outputs": [{"name": "output", "required": false, "type": "string"}], "version": "3"}, "edges": null, "id": "156549", "meta": {"position": {"x": 417.5, "y": -52.35}}, "type": "3"}, {"blocks": [], "data": {"inputs": {"callTransferVoice": true, "chatHistoryWriting": "historyWrite", "content": {"type": "string", "value": {"content": "emitter: {{output}}", "type": "literal"}}, "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "156549", "name": "output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "output"}], "streamingOutput": true}, "nodeMeta": {"description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "mainColor": "#5C62FF", "subTitle": "输出", "title": "输出"}}, "edges": null, "id": "198540", "meta": {"position": {"x": 858, "y": -26.35}}, "type": "13"}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "156549", "sourcePortID": ""}, {"sourceNodeID": "198540", "targetNodeID": "900001", "sourcePortID": ""}, {"sourceNodeID": "156549", "targetNodeID": "198540", "sourcePortID": ""}], "versions": {"loop": "v2"}}