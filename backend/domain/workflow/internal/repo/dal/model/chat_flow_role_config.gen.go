// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameChatFlowRoleConfig = "chat_flow_role_config"

// ChatFlowRoleConfig mapped from table <chat_flow_role_config>
type ChatFlowRoleConfig struct {
	ID                  int64          `gorm:"column:id;primaryKey;comment:id" json:"id"`                                                                                 // id
	WorkflowID          int64          `gorm:"column:workflow_id;not null;comment:workflow id" json:"workflow_id"`                                                        // workflow id
	Name                string         `gorm:"column:name;not null;comment:role name" json:"name"`                                                                        // role name
	Description         string         `gorm:"column:description;not null;comment:role description" json:"description"`                                                   // role description
	Version             string         `gorm:"column:version;not null;comment:version" json:"version"`                                                                    // version
	Avatar              string         `gorm:"column:avatar;not null;comment:avatar uri" json:"avatar"`                                                                   // avatar uri
	BackgroundImageInfo string         `gorm:"column:background_image_info;not null;comment:background image information, object structure" json:"background_image_info"` // background image information, object structure
	OnboardingInfo      string         `gorm:"column:onboarding_info;not null;comment:intro information, object structure" json:"onboarding_info"`                        // intro information, object structure
	SuggestReplyInfo    string         `gorm:"column:suggest_reply_info;not null;comment:user suggestions, object structure" json:"suggest_reply_info"`                   // user suggestions, object structure
	AudioConfig         string         `gorm:"column:audio_config;not null;comment:agent audio config, object structure" json:"audio_config"`                             // agent audio config, object structure
	UserInputConfig     string         `gorm:"column:user_input_config;not null;comment:user input config, object structure" json:"user_input_config"`                    // user input config, object structure
	CreatorID           int64          `gorm:"column:creator_id;not null;comment:creator id" json:"creator_id"`                                                           // creator id
	CreatedAt           int64          `gorm:"column:created_at;not null;autoCreateTime:milli;comment:create time in millisecond" json:"created_at"`                      // create time in millisecond
	UpdatedAt           int64          `gorm:"column:updated_at;autoUpdateTime:milli;comment:update time in millisecond" json:"updated_at"`                               // update time in millisecond
	DeletedAt           gorm.DeletedAt `gorm:"column:deleted_at;comment:delete time in millisecond" json:"deleted_at"`                                                    // delete time in millisecond
	ConnectorID         int64          `gorm:"column:connector_id;comment:connector id" json:"connector_id"`                                                              // connector id
}

// TableName ChatFlowRoleConfig's table name
func (*ChatFlowRoleConfig) TableName() string {
	return TableNameChatFlowRoleConfig
}
