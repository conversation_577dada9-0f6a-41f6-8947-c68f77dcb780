// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameWorkflowDraft = "workflow_draft"

// WorkflowDraft Workflow canvas draft table, used to record the latest draft canvas information of workflow
type WorkflowDraft struct {
	ID             int64          `gorm:"column:id;primaryKey;comment:workflow ID" json:"id"`                                                     // workflow ID
	Canvas         string         `gorm:"column:canvas;not null;comment:Front end schema" json:"canvas"`                                          // Front end schema
	InputParams    string         `gorm:"column:input_params;comment:Input schema" json:"input_params"`                                           // Input schema
	OutputParams   string         `gorm:"column:output_params;comment:Output parameter schema" json:"output_params"`                              // Output parameter schema
	TestRunSuccess bool           `gorm:"column:test_run_success;not null;comment:0 not running, 1 running successfully" json:"test_run_success"` // 0 not running, 1 running successfully
	Modified       bool           `gorm:"column:modified;not null;comment:0 has not been modified, 1 has been modified" json:"modified"`          // 0 has not been modified, 1 has been modified
	UpdatedAt      int64          `gorm:"column:updated_at;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"`           // Update Time in Milliseconds
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;comment:Delete Time" json:"deleted_at"`                                                // Delete Time
	CommitID       string         `gorm:"column:commit_id;not null;comment:used to uniquely identify a draft snapshot" json:"commit_id"`          // used to uniquely identify a draft snapshot
}

// TableName WorkflowDraft's table name
func (*WorkflowDraft) TableName() string {
	return TableNameWorkflowDraft
}
