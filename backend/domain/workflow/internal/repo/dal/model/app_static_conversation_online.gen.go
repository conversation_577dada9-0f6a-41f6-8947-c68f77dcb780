// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAppStaticConversationOnline = "app_static_conversation_online"

// AppStaticConversationOnline mapped from table <app_static_conversation_online>
type AppStaticConversationOnline struct {
	ID             int64 `gorm:"column:id;primaryKey;comment:id" json:"id"`                                                            // id
	TemplateID     int64 `gorm:"column:template_id;not null;comment:template id" json:"template_id"`                                   // template id
	UserID         int64 `gorm:"column:user_id;not null;comment:user id" json:"user_id"`                                               // user id
	ConnectorID    int64 `gorm:"column:connector_id;not null;comment:connector id" json:"connector_id"`                                // connector id
	ConversationID int64 `gorm:"column:conversation_id;not null;comment:conversation id" json:"conversation_id"`                       // conversation id
	CreatedAt      int64 `gorm:"column:created_at;not null;autoCreateTime:milli;comment:create time in millisecond" json:"created_at"` // create time in millisecond
}

// TableName AppStaticConversationOnline's table name
func (*AppStaticConversationOnline) TableName() string {
	return TableNameAppStaticConversationOnline
}
