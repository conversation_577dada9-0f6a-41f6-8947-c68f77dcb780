// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameWorkflowReference = "workflow_reference"

// WorkflowReference The workflow association table,used to record the direct mutual reference relationship between workflows
type WorkflowReference struct {
	ID               int64          `gorm:"column:id;primaryKey;comment:workflow id" json:"id"`                                                                                      // workflow id
	ReferredID       int64          `gorm:"column:referred_id;not null;comment:the id of the workflow that is referred by other entities" json:"referred_id"`                        // the id of the workflow that is referred by other entities
	ReferringID      int64          `gorm:"column:referring_id;not null;comment:the entity id that refers this workflow" json:"referring_id"`                                        // the entity id that refers this workflow
	ReferType        int32          `gorm:"column:refer_type;not null;comment:1 subworkflow 2 tool" json:"refer_type"`                                                               // 1 subworkflow 2 tool
	ReferringBizType int32          `gorm:"column:referring_biz_type;not null;comment:the biz type the referring entity belongs to: 1. workflow 2. agent" json:"referring_biz_type"` // the biz type the referring entity belongs to: 1. workflow 2. agent
	CreatedAt        int64          `gorm:"column:created_at;not null;autoCreateTime:milli;comment:create time in millisecond" json:"created_at"`                                    // create time in millisecond
	Status           int32          `gorm:"column:status;not null;comment:whether this reference currently takes effect. 0: disabled 1: enabled" json:"status"`                      // whether this reference currently takes effect. 0: disabled 1: enabled
	DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at;comment:Delete Time" json:"deleted_at"`                                                                                 // Delete Time
}

// TableName WorkflowReference's table name
func (*WorkflowReference) TableName() string {
	return TableNameWorkflowReference
}
