// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameAppConversationTemplateDraft = "app_conversation_template_draft"

// AppConversationTemplateDraft mapped from table <app_conversation_template_draft>
type AppConversationTemplateDraft struct {
	ID         int64          `gorm:"column:id;primaryKey;comment:id" json:"id"`                                                            // id
	AppID      int64          `gorm:"column:app_id;not null;comment:app id" json:"app_id"`                                                  // app id
	SpaceID    int64          `gorm:"column:space_id;not null;comment:space id" json:"space_id"`                                            // space id
	Name       string         `gorm:"column:name;not null;comment:conversion name" json:"name"`                                             // conversion name
	TemplateID int64          `gorm:"column:template_id;not null;comment:template id" json:"template_id"`                                   // template id
	CreatorID  int64          `gorm:"column:creator_id;not null;comment:creator id" json:"creator_id"`                                      // creator id
	CreatedAt  int64          `gorm:"column:created_at;not null;autoCreateTime:milli;comment:create time in millisecond" json:"created_at"` // create time in millisecond
	UpdatedAt  int64          `gorm:"column:updated_at;autoUpdateTime:milli;comment:update time in millisecond" json:"updated_at"`          // update time in millisecond
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at;comment:delete time in millisecond" json:"deleted_at"`                               // delete time in millisecond
}

// TableName AppConversationTemplateDraft's table name
func (*AppConversationTemplateDraft) TableName() string {
	return TableNameAppConversationTemplateDraft
}
