// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameAppDynamicConversationDraft = "app_dynamic_conversation_draft"

// AppDynamicConversationDraft mapped from table <app_dynamic_conversation_draft>
type AppDynamicConversationDraft struct {
	ID             int64          `gorm:"column:id;primaryKey;comment:id" json:"id"`                                                            // id
	AppID          int64          `gorm:"column:app_id;not null;comment:app id" json:"app_id"`                                                  // app id
	Name           string         `gorm:"column:name;not null;comment:conversion name" json:"name"`                                             // conversion name
	UserID         int64          `gorm:"column:user_id;not null;comment:user id" json:"user_id"`                                               // user id
	ConnectorID    int64          `gorm:"column:connector_id;not null;comment:connector id" json:"connector_id"`                                // connector id
	ConversationID int64          `gorm:"column:conversation_id;not null;comment:conversation id" json:"conversation_id"`                       // conversation id
	CreatedAt      int64          `gorm:"column:created_at;not null;autoCreateTime:milli;comment:create time in millisecond" json:"created_at"` // create time in millisecond
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;comment:delete time in millisecond" json:"deleted_at"`                               // delete time in millisecond
}

// TableName AppDynamicConversationDraft's table name
func (*AppDynamicConversationDraft) TableName() string {
	return TableNameAppDynamicConversationDraft
}
