// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameAppStaticConversationDraft = "app_static_conversation_draft"

// AppStaticConversationDraft mapped from table <app_static_conversation_draft>
type AppStaticConversationDraft struct {
	ID             int64          `gorm:"column:id;primaryKey;comment:id" json:"id"`                                                            // id
	TemplateID     int64          `gorm:"column:template_id;not null;comment:template id" json:"template_id"`                                   // template id
	UserID         int64          `gorm:"column:user_id;not null;comment:user id" json:"user_id"`                                               // user id
	ConnectorID    int64          `gorm:"column:connector_id;not null;comment:connector id" json:"connector_id"`                                // connector id
	ConversationID int64          `gorm:"column:conversation_id;not null;comment:conversation id" json:"conversation_id"`                       // conversation id
	CreatedAt      int64          `gorm:"column:created_at;not null;autoCreateTime:milli;comment:create time in millisecond" json:"created_at"` // create time in millisecond
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;comment:delete time in millisecond" json:"deleted_at"`                               // delete time in millisecond
}

// TableName AppStaticConversationDraft's table name
func (*AppStaticConversationDraft) TableName() string {
	return TableNameAppStaticConversationDraft
}
