// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameConnectorWorkflowVersion = "connector_workflow_version"

// ConnectorWorkflowVersion mapped from table <connector_workflow_version>
type ConnectorWorkflowVersion struct {
	ID          int64  `gorm:"column:id;primaryKey;autoIncrement:true;comment:id" json:"id"`                                         // id
	AppID       int64  `gorm:"column:app_id;not null;comment:app id" json:"app_id"`                                                  // app id
	ConnectorID int64  `gorm:"column:connector_id;not null;comment:connector id" json:"connector_id"`                                // connector id
	WorkflowID  int64  `gorm:"column:workflow_id;not null;comment:workflow id" json:"workflow_id"`                                   // workflow id
	Version     string `gorm:"column:version;not null;comment:version" json:"version"`                                               // version
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:create time in millisecond" json:"created_at"` // create time in millisecond
}

// TableName ConnectorWorkflowVersion's table name
func (*ConnectorWorkflowVersion) TableName() string {
	return TableNameConnectorWorkflowVersion
}
