// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newWorkflowSnapshot(db *gorm.DB, opts ...gen.DOOption) workflowSnapshot {
	_workflowSnapshot := workflowSnapshot{}

	_workflowSnapshot.workflowSnapshotDo.UseDB(db, opts...)
	_workflowSnapshot.workflowSnapshotDo.UseModel(&model.WorkflowSnapshot{})

	tableName := _workflowSnapshot.workflowSnapshotDo.TableName()
	_workflowSnapshot.ALL = field.NewAsterisk(tableName)
	_workflowSnapshot.WorkflowID = field.NewInt64(tableName, "workflow_id")
	_workflowSnapshot.CommitID = field.NewString(tableName, "commit_id")
	_workflowSnapshot.Canvas = field.NewString(tableName, "canvas")
	_workflowSnapshot.InputParams = field.NewString(tableName, "input_params")
	_workflowSnapshot.OutputParams = field.NewString(tableName, "output_params")
	_workflowSnapshot.CreatedAt = field.NewInt64(tableName, "created_at")
	_workflowSnapshot.ID = field.NewInt64(tableName, "id")

	_workflowSnapshot.fillFieldMap()

	return _workflowSnapshot
}

// workflowSnapshot snapshot for executed workflow draft
type workflowSnapshot struct {
	workflowSnapshotDo

	ALL          field.Asterisk
	WorkflowID   field.Int64  // workflow id this snapshot belongs to
	CommitID     field.String // the commit id of the workflow draft
	Canvas       field.String // frontend schema for this snapshot
	InputParams  field.String // input parameter info
	OutputParams field.String // output parameter info
	CreatedAt    field.Int64  // Create Time in Milliseconds
	ID           field.Int64  // ID

	fieldMap map[string]field.Expr
}

func (w workflowSnapshot) Table(newTableName string) *workflowSnapshot {
	w.workflowSnapshotDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w workflowSnapshot) As(alias string) *workflowSnapshot {
	w.workflowSnapshotDo.DO = *(w.workflowSnapshotDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *workflowSnapshot) updateTableName(table string) *workflowSnapshot {
	w.ALL = field.NewAsterisk(table)
	w.WorkflowID = field.NewInt64(table, "workflow_id")
	w.CommitID = field.NewString(table, "commit_id")
	w.Canvas = field.NewString(table, "canvas")
	w.InputParams = field.NewString(table, "input_params")
	w.OutputParams = field.NewString(table, "output_params")
	w.CreatedAt = field.NewInt64(table, "created_at")
	w.ID = field.NewInt64(table, "id")

	w.fillFieldMap()

	return w
}

func (w *workflowSnapshot) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *workflowSnapshot) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 7)
	w.fieldMap["workflow_id"] = w.WorkflowID
	w.fieldMap["commit_id"] = w.CommitID
	w.fieldMap["canvas"] = w.Canvas
	w.fieldMap["input_params"] = w.InputParams
	w.fieldMap["output_params"] = w.OutputParams
	w.fieldMap["created_at"] = w.CreatedAt
	w.fieldMap["id"] = w.ID
}

func (w workflowSnapshot) clone(db *gorm.DB) workflowSnapshot {
	w.workflowSnapshotDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w workflowSnapshot) replaceDB(db *gorm.DB) workflowSnapshot {
	w.workflowSnapshotDo.ReplaceDB(db)
	return w
}

type workflowSnapshotDo struct{ gen.DO }

type IWorkflowSnapshotDo interface {
	gen.SubQuery
	Debug() IWorkflowSnapshotDo
	WithContext(ctx context.Context) IWorkflowSnapshotDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWorkflowSnapshotDo
	WriteDB() IWorkflowSnapshotDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWorkflowSnapshotDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWorkflowSnapshotDo
	Not(conds ...gen.Condition) IWorkflowSnapshotDo
	Or(conds ...gen.Condition) IWorkflowSnapshotDo
	Select(conds ...field.Expr) IWorkflowSnapshotDo
	Where(conds ...gen.Condition) IWorkflowSnapshotDo
	Order(conds ...field.Expr) IWorkflowSnapshotDo
	Distinct(cols ...field.Expr) IWorkflowSnapshotDo
	Omit(cols ...field.Expr) IWorkflowSnapshotDo
	Join(table schema.Tabler, on ...field.Expr) IWorkflowSnapshotDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWorkflowSnapshotDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWorkflowSnapshotDo
	Group(cols ...field.Expr) IWorkflowSnapshotDo
	Having(conds ...gen.Condition) IWorkflowSnapshotDo
	Limit(limit int) IWorkflowSnapshotDo
	Offset(offset int) IWorkflowSnapshotDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWorkflowSnapshotDo
	Unscoped() IWorkflowSnapshotDo
	Create(values ...*model.WorkflowSnapshot) error
	CreateInBatches(values []*model.WorkflowSnapshot, batchSize int) error
	Save(values ...*model.WorkflowSnapshot) error
	First() (*model.WorkflowSnapshot, error)
	Take() (*model.WorkflowSnapshot, error)
	Last() (*model.WorkflowSnapshot, error)
	Find() ([]*model.WorkflowSnapshot, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WorkflowSnapshot, err error)
	FindInBatches(result *[]*model.WorkflowSnapshot, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WorkflowSnapshot) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWorkflowSnapshotDo
	Assign(attrs ...field.AssignExpr) IWorkflowSnapshotDo
	Joins(fields ...field.RelationField) IWorkflowSnapshotDo
	Preload(fields ...field.RelationField) IWorkflowSnapshotDo
	FirstOrInit() (*model.WorkflowSnapshot, error)
	FirstOrCreate() (*model.WorkflowSnapshot, error)
	FindByPage(offset int, limit int) (result []*model.WorkflowSnapshot, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWorkflowSnapshotDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w workflowSnapshotDo) Debug() IWorkflowSnapshotDo {
	return w.withDO(w.DO.Debug())
}

func (w workflowSnapshotDo) WithContext(ctx context.Context) IWorkflowSnapshotDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w workflowSnapshotDo) ReadDB() IWorkflowSnapshotDo {
	return w.Clauses(dbresolver.Read)
}

func (w workflowSnapshotDo) WriteDB() IWorkflowSnapshotDo {
	return w.Clauses(dbresolver.Write)
}

func (w workflowSnapshotDo) Session(config *gorm.Session) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Session(config))
}

func (w workflowSnapshotDo) Clauses(conds ...clause.Expression) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w workflowSnapshotDo) Returning(value interface{}, columns ...string) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w workflowSnapshotDo) Not(conds ...gen.Condition) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w workflowSnapshotDo) Or(conds ...gen.Condition) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w workflowSnapshotDo) Select(conds ...field.Expr) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w workflowSnapshotDo) Where(conds ...gen.Condition) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w workflowSnapshotDo) Order(conds ...field.Expr) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w workflowSnapshotDo) Distinct(cols ...field.Expr) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w workflowSnapshotDo) Omit(cols ...field.Expr) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w workflowSnapshotDo) Join(table schema.Tabler, on ...field.Expr) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w workflowSnapshotDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWorkflowSnapshotDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w workflowSnapshotDo) RightJoin(table schema.Tabler, on ...field.Expr) IWorkflowSnapshotDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w workflowSnapshotDo) Group(cols ...field.Expr) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w workflowSnapshotDo) Having(conds ...gen.Condition) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w workflowSnapshotDo) Limit(limit int) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w workflowSnapshotDo) Offset(offset int) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w workflowSnapshotDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w workflowSnapshotDo) Unscoped() IWorkflowSnapshotDo {
	return w.withDO(w.DO.Unscoped())
}

func (w workflowSnapshotDo) Create(values ...*model.WorkflowSnapshot) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w workflowSnapshotDo) CreateInBatches(values []*model.WorkflowSnapshot, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w workflowSnapshotDo) Save(values ...*model.WorkflowSnapshot) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w workflowSnapshotDo) First() (*model.WorkflowSnapshot, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowSnapshot), nil
	}
}

func (w workflowSnapshotDo) Take() (*model.WorkflowSnapshot, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowSnapshot), nil
	}
}

func (w workflowSnapshotDo) Last() (*model.WorkflowSnapshot, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowSnapshot), nil
	}
}

func (w workflowSnapshotDo) Find() ([]*model.WorkflowSnapshot, error) {
	result, err := w.DO.Find()
	return result.([]*model.WorkflowSnapshot), err
}

func (w workflowSnapshotDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WorkflowSnapshot, err error) {
	buf := make([]*model.WorkflowSnapshot, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w workflowSnapshotDo) FindInBatches(result *[]*model.WorkflowSnapshot, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w workflowSnapshotDo) Attrs(attrs ...field.AssignExpr) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w workflowSnapshotDo) Assign(attrs ...field.AssignExpr) IWorkflowSnapshotDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w workflowSnapshotDo) Joins(fields ...field.RelationField) IWorkflowSnapshotDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w workflowSnapshotDo) Preload(fields ...field.RelationField) IWorkflowSnapshotDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w workflowSnapshotDo) FirstOrInit() (*model.WorkflowSnapshot, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowSnapshot), nil
	}
}

func (w workflowSnapshotDo) FirstOrCreate() (*model.WorkflowSnapshot, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowSnapshot), nil
	}
}

func (w workflowSnapshotDo) FindByPage(offset int, limit int) (result []*model.WorkflowSnapshot, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w workflowSnapshotDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w workflowSnapshotDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w workflowSnapshotDo) Delete(models ...*model.WorkflowSnapshot) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *workflowSnapshotDo) withDO(do gen.Dao) *workflowSnapshotDo {
	w.DO = *do.(*gen.DO)
	return w
}
