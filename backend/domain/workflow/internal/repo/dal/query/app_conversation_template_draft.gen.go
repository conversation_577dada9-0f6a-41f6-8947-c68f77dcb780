// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newAppConversationTemplateDraft(db *gorm.DB, opts ...gen.DOOption) appConversationTemplateDraft {
	_appConversationTemplateDraft := appConversationTemplateDraft{}

	_appConversationTemplateDraft.appConversationTemplateDraftDo.UseDB(db, opts...)
	_appConversationTemplateDraft.appConversationTemplateDraftDo.UseModel(&model.AppConversationTemplateDraft{})

	tableName := _appConversationTemplateDraft.appConversationTemplateDraftDo.TableName()
	_appConversationTemplateDraft.ALL = field.NewAsterisk(tableName)
	_appConversationTemplateDraft.ID = field.NewInt64(tableName, "id")
	_appConversationTemplateDraft.AppID = field.NewInt64(tableName, "app_id")
	_appConversationTemplateDraft.SpaceID = field.NewInt64(tableName, "space_id")
	_appConversationTemplateDraft.Name = field.NewString(tableName, "name")
	_appConversationTemplateDraft.TemplateID = field.NewInt64(tableName, "template_id")
	_appConversationTemplateDraft.CreatorID = field.NewInt64(tableName, "creator_id")
	_appConversationTemplateDraft.CreatedAt = field.NewInt64(tableName, "created_at")
	_appConversationTemplateDraft.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_appConversationTemplateDraft.DeletedAt = field.NewField(tableName, "deleted_at")

	_appConversationTemplateDraft.fillFieldMap()

	return _appConversationTemplateDraft
}

type appConversationTemplateDraft struct {
	appConversationTemplateDraftDo

	ALL        field.Asterisk
	ID         field.Int64  // id
	AppID      field.Int64  // app id
	SpaceID    field.Int64  // space id
	Name       field.String // conversion name
	TemplateID field.Int64  // template id
	CreatorID  field.Int64  // creator id
	CreatedAt  field.Int64  // create time in millisecond
	UpdatedAt  field.Int64  // update time in millisecond
	DeletedAt  field.Field  // delete time in millisecond

	fieldMap map[string]field.Expr
}

func (a appConversationTemplateDraft) Table(newTableName string) *appConversationTemplateDraft {
	a.appConversationTemplateDraftDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appConversationTemplateDraft) As(alias string) *appConversationTemplateDraft {
	a.appConversationTemplateDraftDo.DO = *(a.appConversationTemplateDraftDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appConversationTemplateDraft) updateTableName(table string) *appConversationTemplateDraft {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.AppID = field.NewInt64(table, "app_id")
	a.SpaceID = field.NewInt64(table, "space_id")
	a.Name = field.NewString(table, "name")
	a.TemplateID = field.NewInt64(table, "template_id")
	a.CreatorID = field.NewInt64(table, "creator_id")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.DeletedAt = field.NewField(table, "deleted_at")

	a.fillFieldMap()

	return a
}

func (a *appConversationTemplateDraft) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appConversationTemplateDraft) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 9)
	a.fieldMap["id"] = a.ID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["space_id"] = a.SpaceID
	a.fieldMap["name"] = a.Name
	a.fieldMap["template_id"] = a.TemplateID
	a.fieldMap["creator_id"] = a.CreatorID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
}

func (a appConversationTemplateDraft) clone(db *gorm.DB) appConversationTemplateDraft {
	a.appConversationTemplateDraftDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appConversationTemplateDraft) replaceDB(db *gorm.DB) appConversationTemplateDraft {
	a.appConversationTemplateDraftDo.ReplaceDB(db)
	return a
}

type appConversationTemplateDraftDo struct{ gen.DO }

type IAppConversationTemplateDraftDo interface {
	gen.SubQuery
	Debug() IAppConversationTemplateDraftDo
	WithContext(ctx context.Context) IAppConversationTemplateDraftDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAppConversationTemplateDraftDo
	WriteDB() IAppConversationTemplateDraftDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAppConversationTemplateDraftDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAppConversationTemplateDraftDo
	Not(conds ...gen.Condition) IAppConversationTemplateDraftDo
	Or(conds ...gen.Condition) IAppConversationTemplateDraftDo
	Select(conds ...field.Expr) IAppConversationTemplateDraftDo
	Where(conds ...gen.Condition) IAppConversationTemplateDraftDo
	Order(conds ...field.Expr) IAppConversationTemplateDraftDo
	Distinct(cols ...field.Expr) IAppConversationTemplateDraftDo
	Omit(cols ...field.Expr) IAppConversationTemplateDraftDo
	Join(table schema.Tabler, on ...field.Expr) IAppConversationTemplateDraftDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAppConversationTemplateDraftDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAppConversationTemplateDraftDo
	Group(cols ...field.Expr) IAppConversationTemplateDraftDo
	Having(conds ...gen.Condition) IAppConversationTemplateDraftDo
	Limit(limit int) IAppConversationTemplateDraftDo
	Offset(offset int) IAppConversationTemplateDraftDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAppConversationTemplateDraftDo
	Unscoped() IAppConversationTemplateDraftDo
	Create(values ...*model.AppConversationTemplateDraft) error
	CreateInBatches(values []*model.AppConversationTemplateDraft, batchSize int) error
	Save(values ...*model.AppConversationTemplateDraft) error
	First() (*model.AppConversationTemplateDraft, error)
	Take() (*model.AppConversationTemplateDraft, error)
	Last() (*model.AppConversationTemplateDraft, error)
	Find() ([]*model.AppConversationTemplateDraft, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppConversationTemplateDraft, err error)
	FindInBatches(result *[]*model.AppConversationTemplateDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AppConversationTemplateDraft) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAppConversationTemplateDraftDo
	Assign(attrs ...field.AssignExpr) IAppConversationTemplateDraftDo
	Joins(fields ...field.RelationField) IAppConversationTemplateDraftDo
	Preload(fields ...field.RelationField) IAppConversationTemplateDraftDo
	FirstOrInit() (*model.AppConversationTemplateDraft, error)
	FirstOrCreate() (*model.AppConversationTemplateDraft, error)
	FindByPage(offset int, limit int) (result []*model.AppConversationTemplateDraft, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAppConversationTemplateDraftDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a appConversationTemplateDraftDo) Debug() IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Debug())
}

func (a appConversationTemplateDraftDo) WithContext(ctx context.Context) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appConversationTemplateDraftDo) ReadDB() IAppConversationTemplateDraftDo {
	return a.Clauses(dbresolver.Read)
}

func (a appConversationTemplateDraftDo) WriteDB() IAppConversationTemplateDraftDo {
	return a.Clauses(dbresolver.Write)
}

func (a appConversationTemplateDraftDo) Session(config *gorm.Session) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Session(config))
}

func (a appConversationTemplateDraftDo) Clauses(conds ...clause.Expression) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appConversationTemplateDraftDo) Returning(value interface{}, columns ...string) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appConversationTemplateDraftDo) Not(conds ...gen.Condition) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appConversationTemplateDraftDo) Or(conds ...gen.Condition) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appConversationTemplateDraftDo) Select(conds ...field.Expr) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appConversationTemplateDraftDo) Where(conds ...gen.Condition) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appConversationTemplateDraftDo) Order(conds ...field.Expr) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appConversationTemplateDraftDo) Distinct(cols ...field.Expr) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appConversationTemplateDraftDo) Omit(cols ...field.Expr) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appConversationTemplateDraftDo) Join(table schema.Tabler, on ...field.Expr) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appConversationTemplateDraftDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appConversationTemplateDraftDo) RightJoin(table schema.Tabler, on ...field.Expr) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appConversationTemplateDraftDo) Group(cols ...field.Expr) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appConversationTemplateDraftDo) Having(conds ...gen.Condition) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appConversationTemplateDraftDo) Limit(limit int) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appConversationTemplateDraftDo) Offset(offset int) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appConversationTemplateDraftDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appConversationTemplateDraftDo) Unscoped() IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appConversationTemplateDraftDo) Create(values ...*model.AppConversationTemplateDraft) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appConversationTemplateDraftDo) CreateInBatches(values []*model.AppConversationTemplateDraft, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appConversationTemplateDraftDo) Save(values ...*model.AppConversationTemplateDraft) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appConversationTemplateDraftDo) First() (*model.AppConversationTemplateDraft, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConversationTemplateDraft), nil
	}
}

func (a appConversationTemplateDraftDo) Take() (*model.AppConversationTemplateDraft, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConversationTemplateDraft), nil
	}
}

func (a appConversationTemplateDraftDo) Last() (*model.AppConversationTemplateDraft, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConversationTemplateDraft), nil
	}
}

func (a appConversationTemplateDraftDo) Find() ([]*model.AppConversationTemplateDraft, error) {
	result, err := a.DO.Find()
	return result.([]*model.AppConversationTemplateDraft), err
}

func (a appConversationTemplateDraftDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppConversationTemplateDraft, err error) {
	buf := make([]*model.AppConversationTemplateDraft, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appConversationTemplateDraftDo) FindInBatches(result *[]*model.AppConversationTemplateDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appConversationTemplateDraftDo) Attrs(attrs ...field.AssignExpr) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appConversationTemplateDraftDo) Assign(attrs ...field.AssignExpr) IAppConversationTemplateDraftDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appConversationTemplateDraftDo) Joins(fields ...field.RelationField) IAppConversationTemplateDraftDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appConversationTemplateDraftDo) Preload(fields ...field.RelationField) IAppConversationTemplateDraftDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appConversationTemplateDraftDo) FirstOrInit() (*model.AppConversationTemplateDraft, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConversationTemplateDraft), nil
	}
}

func (a appConversationTemplateDraftDo) FirstOrCreate() (*model.AppConversationTemplateDraft, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConversationTemplateDraft), nil
	}
}

func (a appConversationTemplateDraftDo) FindByPage(offset int, limit int) (result []*model.AppConversationTemplateDraft, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appConversationTemplateDraftDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appConversationTemplateDraftDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appConversationTemplateDraftDo) Delete(models ...*model.AppConversationTemplateDraft) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appConversationTemplateDraftDo) withDO(do gen.Dao) *appConversationTemplateDraftDo {
	a.DO = *do.(*gen.DO)
	return a
}
