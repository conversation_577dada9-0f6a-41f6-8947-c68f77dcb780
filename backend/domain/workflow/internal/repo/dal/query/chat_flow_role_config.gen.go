// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newChatFlowRoleConfig(db *gorm.DB, opts ...gen.DOOption) chatFlowRoleConfig {
	_chatFlowRoleConfig := chatFlowRoleConfig{}

	_chatFlowRoleConfig.chatFlowRoleConfigDo.UseDB(db, opts...)
	_chatFlowRoleConfig.chatFlowRoleConfigDo.UseModel(&model.ChatFlowRoleConfig{})

	tableName := _chatFlowRoleConfig.chatFlowRoleConfigDo.TableName()
	_chatFlowRoleConfig.ALL = field.NewAsterisk(tableName)
	_chatFlowRoleConfig.ID = field.NewInt64(tableName, "id")
	_chatFlowRoleConfig.WorkflowID = field.NewInt64(tableName, "workflow_id")
	_chatFlowRoleConfig.Name = field.NewString(tableName, "name")
	_chatFlowRoleConfig.Description = field.NewString(tableName, "description")
	_chatFlowRoleConfig.Version = field.NewString(tableName, "version")
	_chatFlowRoleConfig.Avatar = field.NewString(tableName, "avatar")
	_chatFlowRoleConfig.BackgroundImageInfo = field.NewString(tableName, "background_image_info")
	_chatFlowRoleConfig.OnboardingInfo = field.NewString(tableName, "onboarding_info")
	_chatFlowRoleConfig.SuggestReplyInfo = field.NewString(tableName, "suggest_reply_info")
	_chatFlowRoleConfig.AudioConfig = field.NewString(tableName, "audio_config")
	_chatFlowRoleConfig.UserInputConfig = field.NewString(tableName, "user_input_config")
	_chatFlowRoleConfig.CreatorID = field.NewInt64(tableName, "creator_id")
	_chatFlowRoleConfig.CreatedAt = field.NewInt64(tableName, "created_at")
	_chatFlowRoleConfig.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_chatFlowRoleConfig.DeletedAt = field.NewField(tableName, "deleted_at")
	_chatFlowRoleConfig.ConnectorID = field.NewInt64(tableName, "connector_id")

	_chatFlowRoleConfig.fillFieldMap()

	return _chatFlowRoleConfig
}

type chatFlowRoleConfig struct {
	chatFlowRoleConfigDo

	ALL                 field.Asterisk
	ID                  field.Int64  // id
	WorkflowID          field.Int64  // workflow id
	Name                field.String // role name
	Description         field.String // role description
	Version             field.String // version
	Avatar              field.String // avatar uri
	BackgroundImageInfo field.String // background image information, object structure
	OnboardingInfo      field.String // intro information, object structure
	SuggestReplyInfo    field.String // user suggestions, object structure
	AudioConfig         field.String // agent audio config, object structure
	UserInputConfig     field.String // user input config, object structure
	CreatorID           field.Int64  // creator id
	CreatedAt           field.Int64  // create time in millisecond
	UpdatedAt           field.Int64  // update time in millisecond
	DeletedAt           field.Field  // delete time in millisecond
	ConnectorID         field.Int64  // connector id

	fieldMap map[string]field.Expr
}

func (c chatFlowRoleConfig) Table(newTableName string) *chatFlowRoleConfig {
	c.chatFlowRoleConfigDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c chatFlowRoleConfig) As(alias string) *chatFlowRoleConfig {
	c.chatFlowRoleConfigDo.DO = *(c.chatFlowRoleConfigDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *chatFlowRoleConfig) updateTableName(table string) *chatFlowRoleConfig {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.WorkflowID = field.NewInt64(table, "workflow_id")
	c.Name = field.NewString(table, "name")
	c.Description = field.NewString(table, "description")
	c.Version = field.NewString(table, "version")
	c.Avatar = field.NewString(table, "avatar")
	c.BackgroundImageInfo = field.NewString(table, "background_image_info")
	c.OnboardingInfo = field.NewString(table, "onboarding_info")
	c.SuggestReplyInfo = field.NewString(table, "suggest_reply_info")
	c.AudioConfig = field.NewString(table, "audio_config")
	c.UserInputConfig = field.NewString(table, "user_input_config")
	c.CreatorID = field.NewInt64(table, "creator_id")
	c.CreatedAt = field.NewInt64(table, "created_at")
	c.UpdatedAt = field.NewInt64(table, "updated_at")
	c.DeletedAt = field.NewField(table, "deleted_at")
	c.ConnectorID = field.NewInt64(table, "connector_id")

	c.fillFieldMap()

	return c
}

func (c *chatFlowRoleConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *chatFlowRoleConfig) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 16)
	c.fieldMap["id"] = c.ID
	c.fieldMap["workflow_id"] = c.WorkflowID
	c.fieldMap["name"] = c.Name
	c.fieldMap["description"] = c.Description
	c.fieldMap["version"] = c.Version
	c.fieldMap["avatar"] = c.Avatar
	c.fieldMap["background_image_info"] = c.BackgroundImageInfo
	c.fieldMap["onboarding_info"] = c.OnboardingInfo
	c.fieldMap["suggest_reply_info"] = c.SuggestReplyInfo
	c.fieldMap["audio_config"] = c.AudioConfig
	c.fieldMap["user_input_config"] = c.UserInputConfig
	c.fieldMap["creator_id"] = c.CreatorID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["connector_id"] = c.ConnectorID
}

func (c chatFlowRoleConfig) clone(db *gorm.DB) chatFlowRoleConfig {
	c.chatFlowRoleConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c chatFlowRoleConfig) replaceDB(db *gorm.DB) chatFlowRoleConfig {
	c.chatFlowRoleConfigDo.ReplaceDB(db)
	return c
}

type chatFlowRoleConfigDo struct{ gen.DO }

type IChatFlowRoleConfigDo interface {
	gen.SubQuery
	Debug() IChatFlowRoleConfigDo
	WithContext(ctx context.Context) IChatFlowRoleConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IChatFlowRoleConfigDo
	WriteDB() IChatFlowRoleConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IChatFlowRoleConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IChatFlowRoleConfigDo
	Not(conds ...gen.Condition) IChatFlowRoleConfigDo
	Or(conds ...gen.Condition) IChatFlowRoleConfigDo
	Select(conds ...field.Expr) IChatFlowRoleConfigDo
	Where(conds ...gen.Condition) IChatFlowRoleConfigDo
	Order(conds ...field.Expr) IChatFlowRoleConfigDo
	Distinct(cols ...field.Expr) IChatFlowRoleConfigDo
	Omit(cols ...field.Expr) IChatFlowRoleConfigDo
	Join(table schema.Tabler, on ...field.Expr) IChatFlowRoleConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IChatFlowRoleConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IChatFlowRoleConfigDo
	Group(cols ...field.Expr) IChatFlowRoleConfigDo
	Having(conds ...gen.Condition) IChatFlowRoleConfigDo
	Limit(limit int) IChatFlowRoleConfigDo
	Offset(offset int) IChatFlowRoleConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IChatFlowRoleConfigDo
	Unscoped() IChatFlowRoleConfigDo
	Create(values ...*model.ChatFlowRoleConfig) error
	CreateInBatches(values []*model.ChatFlowRoleConfig, batchSize int) error
	Save(values ...*model.ChatFlowRoleConfig) error
	First() (*model.ChatFlowRoleConfig, error)
	Take() (*model.ChatFlowRoleConfig, error)
	Last() (*model.ChatFlowRoleConfig, error)
	Find() ([]*model.ChatFlowRoleConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ChatFlowRoleConfig, err error)
	FindInBatches(result *[]*model.ChatFlowRoleConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ChatFlowRoleConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IChatFlowRoleConfigDo
	Assign(attrs ...field.AssignExpr) IChatFlowRoleConfigDo
	Joins(fields ...field.RelationField) IChatFlowRoleConfigDo
	Preload(fields ...field.RelationField) IChatFlowRoleConfigDo
	FirstOrInit() (*model.ChatFlowRoleConfig, error)
	FirstOrCreate() (*model.ChatFlowRoleConfig, error)
	FindByPage(offset int, limit int) (result []*model.ChatFlowRoleConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IChatFlowRoleConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c chatFlowRoleConfigDo) Debug() IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Debug())
}

func (c chatFlowRoleConfigDo) WithContext(ctx context.Context) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c chatFlowRoleConfigDo) ReadDB() IChatFlowRoleConfigDo {
	return c.Clauses(dbresolver.Read)
}

func (c chatFlowRoleConfigDo) WriteDB() IChatFlowRoleConfigDo {
	return c.Clauses(dbresolver.Write)
}

func (c chatFlowRoleConfigDo) Session(config *gorm.Session) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Session(config))
}

func (c chatFlowRoleConfigDo) Clauses(conds ...clause.Expression) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c chatFlowRoleConfigDo) Returning(value interface{}, columns ...string) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c chatFlowRoleConfigDo) Not(conds ...gen.Condition) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c chatFlowRoleConfigDo) Or(conds ...gen.Condition) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c chatFlowRoleConfigDo) Select(conds ...field.Expr) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c chatFlowRoleConfigDo) Where(conds ...gen.Condition) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c chatFlowRoleConfigDo) Order(conds ...field.Expr) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c chatFlowRoleConfigDo) Distinct(cols ...field.Expr) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c chatFlowRoleConfigDo) Omit(cols ...field.Expr) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c chatFlowRoleConfigDo) Join(table schema.Tabler, on ...field.Expr) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c chatFlowRoleConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c chatFlowRoleConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c chatFlowRoleConfigDo) Group(cols ...field.Expr) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c chatFlowRoleConfigDo) Having(conds ...gen.Condition) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c chatFlowRoleConfigDo) Limit(limit int) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c chatFlowRoleConfigDo) Offset(offset int) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c chatFlowRoleConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c chatFlowRoleConfigDo) Unscoped() IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Unscoped())
}

func (c chatFlowRoleConfigDo) Create(values ...*model.ChatFlowRoleConfig) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c chatFlowRoleConfigDo) CreateInBatches(values []*model.ChatFlowRoleConfig, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c chatFlowRoleConfigDo) Save(values ...*model.ChatFlowRoleConfig) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c chatFlowRoleConfigDo) First() (*model.ChatFlowRoleConfig, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChatFlowRoleConfig), nil
	}
}

func (c chatFlowRoleConfigDo) Take() (*model.ChatFlowRoleConfig, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChatFlowRoleConfig), nil
	}
}

func (c chatFlowRoleConfigDo) Last() (*model.ChatFlowRoleConfig, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChatFlowRoleConfig), nil
	}
}

func (c chatFlowRoleConfigDo) Find() ([]*model.ChatFlowRoleConfig, error) {
	result, err := c.DO.Find()
	return result.([]*model.ChatFlowRoleConfig), err
}

func (c chatFlowRoleConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ChatFlowRoleConfig, err error) {
	buf := make([]*model.ChatFlowRoleConfig, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c chatFlowRoleConfigDo) FindInBatches(result *[]*model.ChatFlowRoleConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c chatFlowRoleConfigDo) Attrs(attrs ...field.AssignExpr) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c chatFlowRoleConfigDo) Assign(attrs ...field.AssignExpr) IChatFlowRoleConfigDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c chatFlowRoleConfigDo) Joins(fields ...field.RelationField) IChatFlowRoleConfigDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c chatFlowRoleConfigDo) Preload(fields ...field.RelationField) IChatFlowRoleConfigDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c chatFlowRoleConfigDo) FirstOrInit() (*model.ChatFlowRoleConfig, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChatFlowRoleConfig), nil
	}
}

func (c chatFlowRoleConfigDo) FirstOrCreate() (*model.ChatFlowRoleConfig, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChatFlowRoleConfig), nil
	}
}

func (c chatFlowRoleConfigDo) FindByPage(offset int, limit int) (result []*model.ChatFlowRoleConfig, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c chatFlowRoleConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c chatFlowRoleConfigDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c chatFlowRoleConfigDo) Delete(models ...*model.ChatFlowRoleConfig) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *chatFlowRoleConfigDo) withDO(do gen.Dao) *chatFlowRoleConfigDo {
	c.DO = *do.(*gen.DO)
	return c
}
