// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newAppStaticConversationOnline(db *gorm.DB, opts ...gen.DOOption) appStaticConversationOnline {
	_appStaticConversationOnline := appStaticConversationOnline{}

	_appStaticConversationOnline.appStaticConversationOnlineDo.UseDB(db, opts...)
	_appStaticConversationOnline.appStaticConversationOnlineDo.UseModel(&model.AppStaticConversationOnline{})

	tableName := _appStaticConversationOnline.appStaticConversationOnlineDo.TableName()
	_appStaticConversationOnline.ALL = field.NewAsterisk(tableName)
	_appStaticConversationOnline.ID = field.NewInt64(tableName, "id")
	_appStaticConversationOnline.TemplateID = field.NewInt64(tableName, "template_id")
	_appStaticConversationOnline.UserID = field.NewInt64(tableName, "user_id")
	_appStaticConversationOnline.ConnectorID = field.NewInt64(tableName, "connector_id")
	_appStaticConversationOnline.ConversationID = field.NewInt64(tableName, "conversation_id")
	_appStaticConversationOnline.CreatedAt = field.NewInt64(tableName, "created_at")

	_appStaticConversationOnline.fillFieldMap()

	return _appStaticConversationOnline
}

type appStaticConversationOnline struct {
	appStaticConversationOnlineDo

	ALL            field.Asterisk
	ID             field.Int64 // id
	TemplateID     field.Int64 // template id
	UserID         field.Int64 // user id
	ConnectorID    field.Int64 // connector id
	ConversationID field.Int64 // conversation id
	CreatedAt      field.Int64 // create time in millisecond

	fieldMap map[string]field.Expr
}

func (a appStaticConversationOnline) Table(newTableName string) *appStaticConversationOnline {
	a.appStaticConversationOnlineDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appStaticConversationOnline) As(alias string) *appStaticConversationOnline {
	a.appStaticConversationOnlineDo.DO = *(a.appStaticConversationOnlineDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appStaticConversationOnline) updateTableName(table string) *appStaticConversationOnline {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.TemplateID = field.NewInt64(table, "template_id")
	a.UserID = field.NewInt64(table, "user_id")
	a.ConnectorID = field.NewInt64(table, "connector_id")
	a.ConversationID = field.NewInt64(table, "conversation_id")
	a.CreatedAt = field.NewInt64(table, "created_at")

	a.fillFieldMap()

	return a
}

func (a *appStaticConversationOnline) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appStaticConversationOnline) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 6)
	a.fieldMap["id"] = a.ID
	a.fieldMap["template_id"] = a.TemplateID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["connector_id"] = a.ConnectorID
	a.fieldMap["conversation_id"] = a.ConversationID
	a.fieldMap["created_at"] = a.CreatedAt
}

func (a appStaticConversationOnline) clone(db *gorm.DB) appStaticConversationOnline {
	a.appStaticConversationOnlineDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appStaticConversationOnline) replaceDB(db *gorm.DB) appStaticConversationOnline {
	a.appStaticConversationOnlineDo.ReplaceDB(db)
	return a
}

type appStaticConversationOnlineDo struct{ gen.DO }

type IAppStaticConversationOnlineDo interface {
	gen.SubQuery
	Debug() IAppStaticConversationOnlineDo
	WithContext(ctx context.Context) IAppStaticConversationOnlineDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAppStaticConversationOnlineDo
	WriteDB() IAppStaticConversationOnlineDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAppStaticConversationOnlineDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAppStaticConversationOnlineDo
	Not(conds ...gen.Condition) IAppStaticConversationOnlineDo
	Or(conds ...gen.Condition) IAppStaticConversationOnlineDo
	Select(conds ...field.Expr) IAppStaticConversationOnlineDo
	Where(conds ...gen.Condition) IAppStaticConversationOnlineDo
	Order(conds ...field.Expr) IAppStaticConversationOnlineDo
	Distinct(cols ...field.Expr) IAppStaticConversationOnlineDo
	Omit(cols ...field.Expr) IAppStaticConversationOnlineDo
	Join(table schema.Tabler, on ...field.Expr) IAppStaticConversationOnlineDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAppStaticConversationOnlineDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAppStaticConversationOnlineDo
	Group(cols ...field.Expr) IAppStaticConversationOnlineDo
	Having(conds ...gen.Condition) IAppStaticConversationOnlineDo
	Limit(limit int) IAppStaticConversationOnlineDo
	Offset(offset int) IAppStaticConversationOnlineDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAppStaticConversationOnlineDo
	Unscoped() IAppStaticConversationOnlineDo
	Create(values ...*model.AppStaticConversationOnline) error
	CreateInBatches(values []*model.AppStaticConversationOnline, batchSize int) error
	Save(values ...*model.AppStaticConversationOnline) error
	First() (*model.AppStaticConversationOnline, error)
	Take() (*model.AppStaticConversationOnline, error)
	Last() (*model.AppStaticConversationOnline, error)
	Find() ([]*model.AppStaticConversationOnline, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppStaticConversationOnline, err error)
	FindInBatches(result *[]*model.AppStaticConversationOnline, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AppStaticConversationOnline) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAppStaticConversationOnlineDo
	Assign(attrs ...field.AssignExpr) IAppStaticConversationOnlineDo
	Joins(fields ...field.RelationField) IAppStaticConversationOnlineDo
	Preload(fields ...field.RelationField) IAppStaticConversationOnlineDo
	FirstOrInit() (*model.AppStaticConversationOnline, error)
	FirstOrCreate() (*model.AppStaticConversationOnline, error)
	FindByPage(offset int, limit int) (result []*model.AppStaticConversationOnline, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAppStaticConversationOnlineDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a appStaticConversationOnlineDo) Debug() IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Debug())
}

func (a appStaticConversationOnlineDo) WithContext(ctx context.Context) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appStaticConversationOnlineDo) ReadDB() IAppStaticConversationOnlineDo {
	return a.Clauses(dbresolver.Read)
}

func (a appStaticConversationOnlineDo) WriteDB() IAppStaticConversationOnlineDo {
	return a.Clauses(dbresolver.Write)
}

func (a appStaticConversationOnlineDo) Session(config *gorm.Session) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Session(config))
}

func (a appStaticConversationOnlineDo) Clauses(conds ...clause.Expression) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appStaticConversationOnlineDo) Returning(value interface{}, columns ...string) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appStaticConversationOnlineDo) Not(conds ...gen.Condition) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appStaticConversationOnlineDo) Or(conds ...gen.Condition) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appStaticConversationOnlineDo) Select(conds ...field.Expr) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appStaticConversationOnlineDo) Where(conds ...gen.Condition) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appStaticConversationOnlineDo) Order(conds ...field.Expr) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appStaticConversationOnlineDo) Distinct(cols ...field.Expr) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appStaticConversationOnlineDo) Omit(cols ...field.Expr) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appStaticConversationOnlineDo) Join(table schema.Tabler, on ...field.Expr) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appStaticConversationOnlineDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appStaticConversationOnlineDo) RightJoin(table schema.Tabler, on ...field.Expr) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appStaticConversationOnlineDo) Group(cols ...field.Expr) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appStaticConversationOnlineDo) Having(conds ...gen.Condition) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appStaticConversationOnlineDo) Limit(limit int) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appStaticConversationOnlineDo) Offset(offset int) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appStaticConversationOnlineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appStaticConversationOnlineDo) Unscoped() IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appStaticConversationOnlineDo) Create(values ...*model.AppStaticConversationOnline) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appStaticConversationOnlineDo) CreateInBatches(values []*model.AppStaticConversationOnline, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appStaticConversationOnlineDo) Save(values ...*model.AppStaticConversationOnline) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appStaticConversationOnlineDo) First() (*model.AppStaticConversationOnline, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppStaticConversationOnline), nil
	}
}

func (a appStaticConversationOnlineDo) Take() (*model.AppStaticConversationOnline, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppStaticConversationOnline), nil
	}
}

func (a appStaticConversationOnlineDo) Last() (*model.AppStaticConversationOnline, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppStaticConversationOnline), nil
	}
}

func (a appStaticConversationOnlineDo) Find() ([]*model.AppStaticConversationOnline, error) {
	result, err := a.DO.Find()
	return result.([]*model.AppStaticConversationOnline), err
}

func (a appStaticConversationOnlineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppStaticConversationOnline, err error) {
	buf := make([]*model.AppStaticConversationOnline, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appStaticConversationOnlineDo) FindInBatches(result *[]*model.AppStaticConversationOnline, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appStaticConversationOnlineDo) Attrs(attrs ...field.AssignExpr) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appStaticConversationOnlineDo) Assign(attrs ...field.AssignExpr) IAppStaticConversationOnlineDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appStaticConversationOnlineDo) Joins(fields ...field.RelationField) IAppStaticConversationOnlineDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appStaticConversationOnlineDo) Preload(fields ...field.RelationField) IAppStaticConversationOnlineDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appStaticConversationOnlineDo) FirstOrInit() (*model.AppStaticConversationOnline, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppStaticConversationOnline), nil
	}
}

func (a appStaticConversationOnlineDo) FirstOrCreate() (*model.AppStaticConversationOnline, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppStaticConversationOnline), nil
	}
}

func (a appStaticConversationOnlineDo) FindByPage(offset int, limit int) (result []*model.AppStaticConversationOnline, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appStaticConversationOnlineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appStaticConversationOnlineDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appStaticConversationOnlineDo) Delete(models ...*model.AppStaticConversationOnline) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appStaticConversationOnlineDo) withDO(do gen.Dao) *appStaticConversationOnlineDo {
	a.DO = *do.(*gen.DO)
	return a
}
