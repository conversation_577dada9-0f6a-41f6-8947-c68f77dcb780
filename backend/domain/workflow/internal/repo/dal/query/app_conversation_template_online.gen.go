// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newAppConversationTemplateOnline(db *gorm.DB, opts ...gen.DOOption) appConversationTemplateOnline {
	_appConversationTemplateOnline := appConversationTemplateOnline{}

	_appConversationTemplateOnline.appConversationTemplateOnlineDo.UseDB(db, opts...)
	_appConversationTemplateOnline.appConversationTemplateOnlineDo.UseModel(&model.AppConversationTemplateOnline{})

	tableName := _appConversationTemplateOnline.appConversationTemplateOnlineDo.TableName()
	_appConversationTemplateOnline.ALL = field.NewAsterisk(tableName)
	_appConversationTemplateOnline.ID = field.NewInt64(tableName, "id")
	_appConversationTemplateOnline.AppID = field.NewInt64(tableName, "app_id")
	_appConversationTemplateOnline.SpaceID = field.NewInt64(tableName, "space_id")
	_appConversationTemplateOnline.Name = field.NewString(tableName, "name")
	_appConversationTemplateOnline.TemplateID = field.NewInt64(tableName, "template_id")
	_appConversationTemplateOnline.Version = field.NewString(tableName, "version")
	_appConversationTemplateOnline.CreatorID = field.NewInt64(tableName, "creator_id")
	_appConversationTemplateOnline.CreatedAt = field.NewInt64(tableName, "created_at")

	_appConversationTemplateOnline.fillFieldMap()

	return _appConversationTemplateOnline
}

type appConversationTemplateOnline struct {
	appConversationTemplateOnlineDo

	ALL        field.Asterisk
	ID         field.Int64  // id
	AppID      field.Int64  // app id
	SpaceID    field.Int64  // space id
	Name       field.String // conversion name
	TemplateID field.Int64  // template id
	Version    field.String // version name
	CreatorID  field.Int64  // creator id
	CreatedAt  field.Int64  // create time in millisecond

	fieldMap map[string]field.Expr
}

func (a appConversationTemplateOnline) Table(newTableName string) *appConversationTemplateOnline {
	a.appConversationTemplateOnlineDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appConversationTemplateOnline) As(alias string) *appConversationTemplateOnline {
	a.appConversationTemplateOnlineDo.DO = *(a.appConversationTemplateOnlineDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appConversationTemplateOnline) updateTableName(table string) *appConversationTemplateOnline {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.AppID = field.NewInt64(table, "app_id")
	a.SpaceID = field.NewInt64(table, "space_id")
	a.Name = field.NewString(table, "name")
	a.TemplateID = field.NewInt64(table, "template_id")
	a.Version = field.NewString(table, "version")
	a.CreatorID = field.NewInt64(table, "creator_id")
	a.CreatedAt = field.NewInt64(table, "created_at")

	a.fillFieldMap()

	return a
}

func (a *appConversationTemplateOnline) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appConversationTemplateOnline) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 8)
	a.fieldMap["id"] = a.ID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["space_id"] = a.SpaceID
	a.fieldMap["name"] = a.Name
	a.fieldMap["template_id"] = a.TemplateID
	a.fieldMap["version"] = a.Version
	a.fieldMap["creator_id"] = a.CreatorID
	a.fieldMap["created_at"] = a.CreatedAt
}

func (a appConversationTemplateOnline) clone(db *gorm.DB) appConversationTemplateOnline {
	a.appConversationTemplateOnlineDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appConversationTemplateOnline) replaceDB(db *gorm.DB) appConversationTemplateOnline {
	a.appConversationTemplateOnlineDo.ReplaceDB(db)
	return a
}

type appConversationTemplateOnlineDo struct{ gen.DO }

type IAppConversationTemplateOnlineDo interface {
	gen.SubQuery
	Debug() IAppConversationTemplateOnlineDo
	WithContext(ctx context.Context) IAppConversationTemplateOnlineDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAppConversationTemplateOnlineDo
	WriteDB() IAppConversationTemplateOnlineDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAppConversationTemplateOnlineDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAppConversationTemplateOnlineDo
	Not(conds ...gen.Condition) IAppConversationTemplateOnlineDo
	Or(conds ...gen.Condition) IAppConversationTemplateOnlineDo
	Select(conds ...field.Expr) IAppConversationTemplateOnlineDo
	Where(conds ...gen.Condition) IAppConversationTemplateOnlineDo
	Order(conds ...field.Expr) IAppConversationTemplateOnlineDo
	Distinct(cols ...field.Expr) IAppConversationTemplateOnlineDo
	Omit(cols ...field.Expr) IAppConversationTemplateOnlineDo
	Join(table schema.Tabler, on ...field.Expr) IAppConversationTemplateOnlineDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAppConversationTemplateOnlineDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAppConversationTemplateOnlineDo
	Group(cols ...field.Expr) IAppConversationTemplateOnlineDo
	Having(conds ...gen.Condition) IAppConversationTemplateOnlineDo
	Limit(limit int) IAppConversationTemplateOnlineDo
	Offset(offset int) IAppConversationTemplateOnlineDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAppConversationTemplateOnlineDo
	Unscoped() IAppConversationTemplateOnlineDo
	Create(values ...*model.AppConversationTemplateOnline) error
	CreateInBatches(values []*model.AppConversationTemplateOnline, batchSize int) error
	Save(values ...*model.AppConversationTemplateOnline) error
	First() (*model.AppConversationTemplateOnline, error)
	Take() (*model.AppConversationTemplateOnline, error)
	Last() (*model.AppConversationTemplateOnline, error)
	Find() ([]*model.AppConversationTemplateOnline, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppConversationTemplateOnline, err error)
	FindInBatches(result *[]*model.AppConversationTemplateOnline, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AppConversationTemplateOnline) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAppConversationTemplateOnlineDo
	Assign(attrs ...field.AssignExpr) IAppConversationTemplateOnlineDo
	Joins(fields ...field.RelationField) IAppConversationTemplateOnlineDo
	Preload(fields ...field.RelationField) IAppConversationTemplateOnlineDo
	FirstOrInit() (*model.AppConversationTemplateOnline, error)
	FirstOrCreate() (*model.AppConversationTemplateOnline, error)
	FindByPage(offset int, limit int) (result []*model.AppConversationTemplateOnline, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAppConversationTemplateOnlineDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a appConversationTemplateOnlineDo) Debug() IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Debug())
}

func (a appConversationTemplateOnlineDo) WithContext(ctx context.Context) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appConversationTemplateOnlineDo) ReadDB() IAppConversationTemplateOnlineDo {
	return a.Clauses(dbresolver.Read)
}

func (a appConversationTemplateOnlineDo) WriteDB() IAppConversationTemplateOnlineDo {
	return a.Clauses(dbresolver.Write)
}

func (a appConversationTemplateOnlineDo) Session(config *gorm.Session) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Session(config))
}

func (a appConversationTemplateOnlineDo) Clauses(conds ...clause.Expression) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appConversationTemplateOnlineDo) Returning(value interface{}, columns ...string) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appConversationTemplateOnlineDo) Not(conds ...gen.Condition) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appConversationTemplateOnlineDo) Or(conds ...gen.Condition) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appConversationTemplateOnlineDo) Select(conds ...field.Expr) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appConversationTemplateOnlineDo) Where(conds ...gen.Condition) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appConversationTemplateOnlineDo) Order(conds ...field.Expr) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appConversationTemplateOnlineDo) Distinct(cols ...field.Expr) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appConversationTemplateOnlineDo) Omit(cols ...field.Expr) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appConversationTemplateOnlineDo) Join(table schema.Tabler, on ...field.Expr) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appConversationTemplateOnlineDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appConversationTemplateOnlineDo) RightJoin(table schema.Tabler, on ...field.Expr) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appConversationTemplateOnlineDo) Group(cols ...field.Expr) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appConversationTemplateOnlineDo) Having(conds ...gen.Condition) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appConversationTemplateOnlineDo) Limit(limit int) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appConversationTemplateOnlineDo) Offset(offset int) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appConversationTemplateOnlineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appConversationTemplateOnlineDo) Unscoped() IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appConversationTemplateOnlineDo) Create(values ...*model.AppConversationTemplateOnline) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appConversationTemplateOnlineDo) CreateInBatches(values []*model.AppConversationTemplateOnline, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appConversationTemplateOnlineDo) Save(values ...*model.AppConversationTemplateOnline) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appConversationTemplateOnlineDo) First() (*model.AppConversationTemplateOnline, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConversationTemplateOnline), nil
	}
}

func (a appConversationTemplateOnlineDo) Take() (*model.AppConversationTemplateOnline, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConversationTemplateOnline), nil
	}
}

func (a appConversationTemplateOnlineDo) Last() (*model.AppConversationTemplateOnline, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConversationTemplateOnline), nil
	}
}

func (a appConversationTemplateOnlineDo) Find() ([]*model.AppConversationTemplateOnline, error) {
	result, err := a.DO.Find()
	return result.([]*model.AppConversationTemplateOnline), err
}

func (a appConversationTemplateOnlineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppConversationTemplateOnline, err error) {
	buf := make([]*model.AppConversationTemplateOnline, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appConversationTemplateOnlineDo) FindInBatches(result *[]*model.AppConversationTemplateOnline, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appConversationTemplateOnlineDo) Attrs(attrs ...field.AssignExpr) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appConversationTemplateOnlineDo) Assign(attrs ...field.AssignExpr) IAppConversationTemplateOnlineDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appConversationTemplateOnlineDo) Joins(fields ...field.RelationField) IAppConversationTemplateOnlineDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appConversationTemplateOnlineDo) Preload(fields ...field.RelationField) IAppConversationTemplateOnlineDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appConversationTemplateOnlineDo) FirstOrInit() (*model.AppConversationTemplateOnline, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConversationTemplateOnline), nil
	}
}

func (a appConversationTemplateOnlineDo) FirstOrCreate() (*model.AppConversationTemplateOnline, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConversationTemplateOnline), nil
	}
}

func (a appConversationTemplateOnlineDo) FindByPage(offset int, limit int) (result []*model.AppConversationTemplateOnline, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appConversationTemplateOnlineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appConversationTemplateOnlineDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appConversationTemplateOnlineDo) Delete(models ...*model.AppConversationTemplateOnline) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appConversationTemplateOnlineDo) withDO(do gen.Dao) *appConversationTemplateOnlineDo {
	a.DO = *do.(*gen.DO)
	return a
}
