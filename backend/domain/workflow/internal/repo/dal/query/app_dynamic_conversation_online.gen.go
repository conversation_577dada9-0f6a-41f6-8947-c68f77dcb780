// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newAppDynamicConversationOnline(db *gorm.DB, opts ...gen.DOOption) appDynamicConversationOnline {
	_appDynamicConversationOnline := appDynamicConversationOnline{}

	_appDynamicConversationOnline.appDynamicConversationOnlineDo.UseDB(db, opts...)
	_appDynamicConversationOnline.appDynamicConversationOnlineDo.UseModel(&model.AppDynamicConversationOnline{})

	tableName := _appDynamicConversationOnline.appDynamicConversationOnlineDo.TableName()
	_appDynamicConversationOnline.ALL = field.NewAsterisk(tableName)
	_appDynamicConversationOnline.ID = field.NewInt64(tableName, "id")
	_appDynamicConversationOnline.AppID = field.NewInt64(tableName, "app_id")
	_appDynamicConversationOnline.Name = field.NewString(tableName, "name")
	_appDynamicConversationOnline.UserID = field.NewInt64(tableName, "user_id")
	_appDynamicConversationOnline.ConnectorID = field.NewInt64(tableName, "connector_id")
	_appDynamicConversationOnline.ConversationID = field.NewInt64(tableName, "conversation_id")
	_appDynamicConversationOnline.CreatedAt = field.NewInt64(tableName, "created_at")
	_appDynamicConversationOnline.DeletedAt = field.NewField(tableName, "deleted_at")

	_appDynamicConversationOnline.fillFieldMap()

	return _appDynamicConversationOnline
}

type appDynamicConversationOnline struct {
	appDynamicConversationOnlineDo

	ALL            field.Asterisk
	ID             field.Int64  // id
	AppID          field.Int64  // app id
	Name           field.String // conversion name
	UserID         field.Int64  // user id
	ConnectorID    field.Int64  // connector id
	ConversationID field.Int64  // conversation id
	CreatedAt      field.Int64  // create time in millisecond
	DeletedAt      field.Field  // delete time in millisecond

	fieldMap map[string]field.Expr
}

func (a appDynamicConversationOnline) Table(newTableName string) *appDynamicConversationOnline {
	a.appDynamicConversationOnlineDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appDynamicConversationOnline) As(alias string) *appDynamicConversationOnline {
	a.appDynamicConversationOnlineDo.DO = *(a.appDynamicConversationOnlineDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appDynamicConversationOnline) updateTableName(table string) *appDynamicConversationOnline {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.AppID = field.NewInt64(table, "app_id")
	a.Name = field.NewString(table, "name")
	a.UserID = field.NewInt64(table, "user_id")
	a.ConnectorID = field.NewInt64(table, "connector_id")
	a.ConversationID = field.NewInt64(table, "conversation_id")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.DeletedAt = field.NewField(table, "deleted_at")

	a.fillFieldMap()

	return a
}

func (a *appDynamicConversationOnline) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appDynamicConversationOnline) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 8)
	a.fieldMap["id"] = a.ID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["name"] = a.Name
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["connector_id"] = a.ConnectorID
	a.fieldMap["conversation_id"] = a.ConversationID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
}

func (a appDynamicConversationOnline) clone(db *gorm.DB) appDynamicConversationOnline {
	a.appDynamicConversationOnlineDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appDynamicConversationOnline) replaceDB(db *gorm.DB) appDynamicConversationOnline {
	a.appDynamicConversationOnlineDo.ReplaceDB(db)
	return a
}

type appDynamicConversationOnlineDo struct{ gen.DO }

type IAppDynamicConversationOnlineDo interface {
	gen.SubQuery
	Debug() IAppDynamicConversationOnlineDo
	WithContext(ctx context.Context) IAppDynamicConversationOnlineDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAppDynamicConversationOnlineDo
	WriteDB() IAppDynamicConversationOnlineDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAppDynamicConversationOnlineDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAppDynamicConversationOnlineDo
	Not(conds ...gen.Condition) IAppDynamicConversationOnlineDo
	Or(conds ...gen.Condition) IAppDynamicConversationOnlineDo
	Select(conds ...field.Expr) IAppDynamicConversationOnlineDo
	Where(conds ...gen.Condition) IAppDynamicConversationOnlineDo
	Order(conds ...field.Expr) IAppDynamicConversationOnlineDo
	Distinct(cols ...field.Expr) IAppDynamicConversationOnlineDo
	Omit(cols ...field.Expr) IAppDynamicConversationOnlineDo
	Join(table schema.Tabler, on ...field.Expr) IAppDynamicConversationOnlineDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAppDynamicConversationOnlineDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAppDynamicConversationOnlineDo
	Group(cols ...field.Expr) IAppDynamicConversationOnlineDo
	Having(conds ...gen.Condition) IAppDynamicConversationOnlineDo
	Limit(limit int) IAppDynamicConversationOnlineDo
	Offset(offset int) IAppDynamicConversationOnlineDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAppDynamicConversationOnlineDo
	Unscoped() IAppDynamicConversationOnlineDo
	Create(values ...*model.AppDynamicConversationOnline) error
	CreateInBatches(values []*model.AppDynamicConversationOnline, batchSize int) error
	Save(values ...*model.AppDynamicConversationOnline) error
	First() (*model.AppDynamicConversationOnline, error)
	Take() (*model.AppDynamicConversationOnline, error)
	Last() (*model.AppDynamicConversationOnline, error)
	Find() ([]*model.AppDynamicConversationOnline, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppDynamicConversationOnline, err error)
	FindInBatches(result *[]*model.AppDynamicConversationOnline, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AppDynamicConversationOnline) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAppDynamicConversationOnlineDo
	Assign(attrs ...field.AssignExpr) IAppDynamicConversationOnlineDo
	Joins(fields ...field.RelationField) IAppDynamicConversationOnlineDo
	Preload(fields ...field.RelationField) IAppDynamicConversationOnlineDo
	FirstOrInit() (*model.AppDynamicConversationOnline, error)
	FirstOrCreate() (*model.AppDynamicConversationOnline, error)
	FindByPage(offset int, limit int) (result []*model.AppDynamicConversationOnline, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAppDynamicConversationOnlineDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a appDynamicConversationOnlineDo) Debug() IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Debug())
}

func (a appDynamicConversationOnlineDo) WithContext(ctx context.Context) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appDynamicConversationOnlineDo) ReadDB() IAppDynamicConversationOnlineDo {
	return a.Clauses(dbresolver.Read)
}

func (a appDynamicConversationOnlineDo) WriteDB() IAppDynamicConversationOnlineDo {
	return a.Clauses(dbresolver.Write)
}

func (a appDynamicConversationOnlineDo) Session(config *gorm.Session) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Session(config))
}

func (a appDynamicConversationOnlineDo) Clauses(conds ...clause.Expression) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appDynamicConversationOnlineDo) Returning(value interface{}, columns ...string) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appDynamicConversationOnlineDo) Not(conds ...gen.Condition) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appDynamicConversationOnlineDo) Or(conds ...gen.Condition) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appDynamicConversationOnlineDo) Select(conds ...field.Expr) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appDynamicConversationOnlineDo) Where(conds ...gen.Condition) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appDynamicConversationOnlineDo) Order(conds ...field.Expr) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appDynamicConversationOnlineDo) Distinct(cols ...field.Expr) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appDynamicConversationOnlineDo) Omit(cols ...field.Expr) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appDynamicConversationOnlineDo) Join(table schema.Tabler, on ...field.Expr) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appDynamicConversationOnlineDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appDynamicConversationOnlineDo) RightJoin(table schema.Tabler, on ...field.Expr) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appDynamicConversationOnlineDo) Group(cols ...field.Expr) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appDynamicConversationOnlineDo) Having(conds ...gen.Condition) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appDynamicConversationOnlineDo) Limit(limit int) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appDynamicConversationOnlineDo) Offset(offset int) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appDynamicConversationOnlineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appDynamicConversationOnlineDo) Unscoped() IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appDynamicConversationOnlineDo) Create(values ...*model.AppDynamicConversationOnline) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appDynamicConversationOnlineDo) CreateInBatches(values []*model.AppDynamicConversationOnline, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appDynamicConversationOnlineDo) Save(values ...*model.AppDynamicConversationOnline) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appDynamicConversationOnlineDo) First() (*model.AppDynamicConversationOnline, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDynamicConversationOnline), nil
	}
}

func (a appDynamicConversationOnlineDo) Take() (*model.AppDynamicConversationOnline, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDynamicConversationOnline), nil
	}
}

func (a appDynamicConversationOnlineDo) Last() (*model.AppDynamicConversationOnline, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDynamicConversationOnline), nil
	}
}

func (a appDynamicConversationOnlineDo) Find() ([]*model.AppDynamicConversationOnline, error) {
	result, err := a.DO.Find()
	return result.([]*model.AppDynamicConversationOnline), err
}

func (a appDynamicConversationOnlineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppDynamicConversationOnline, err error) {
	buf := make([]*model.AppDynamicConversationOnline, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appDynamicConversationOnlineDo) FindInBatches(result *[]*model.AppDynamicConversationOnline, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appDynamicConversationOnlineDo) Attrs(attrs ...field.AssignExpr) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appDynamicConversationOnlineDo) Assign(attrs ...field.AssignExpr) IAppDynamicConversationOnlineDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appDynamicConversationOnlineDo) Joins(fields ...field.RelationField) IAppDynamicConversationOnlineDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appDynamicConversationOnlineDo) Preload(fields ...field.RelationField) IAppDynamicConversationOnlineDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appDynamicConversationOnlineDo) FirstOrInit() (*model.AppDynamicConversationOnline, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDynamicConversationOnline), nil
	}
}

func (a appDynamicConversationOnlineDo) FirstOrCreate() (*model.AppDynamicConversationOnline, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDynamicConversationOnline), nil
	}
}

func (a appDynamicConversationOnlineDo) FindByPage(offset int, limit int) (result []*model.AppDynamicConversationOnline, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appDynamicConversationOnlineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appDynamicConversationOnlineDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appDynamicConversationOnlineDo) Delete(models ...*model.AppDynamicConversationOnline) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appDynamicConversationOnlineDo) withDO(do gen.Dao) *appDynamicConversationOnlineDo {
	a.DO = *do.(*gen.DO)
	return a
}
