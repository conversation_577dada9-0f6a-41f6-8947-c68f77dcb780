// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newWorkflowExecution(db *gorm.DB, opts ...gen.DOOption) workflowExecution {
	_workflowExecution := workflowExecution{}

	_workflowExecution.workflowExecutionDo.UseDB(db, opts...)
	_workflowExecution.workflowExecutionDo.UseModel(&model.WorkflowExecution{})

	tableName := _workflowExecution.workflowExecutionDo.TableName()
	_workflowExecution.ALL = field.NewAsterisk(tableName)
	_workflowExecution.ID = field.NewInt64(tableName, "id")
	_workflowExecution.WorkflowID = field.NewInt64(tableName, "workflow_id")
	_workflowExecution.Version = field.NewString(tableName, "version")
	_workflowExecution.SpaceID = field.NewInt64(tableName, "space_id")
	_workflowExecution.Mode = field.NewInt32(tableName, "mode")
	_workflowExecution.OperatorID = field.NewInt64(tableName, "operator_id")
	_workflowExecution.ConnectorID = field.NewInt64(tableName, "connector_id")
	_workflowExecution.ConnectorUID = field.NewString(tableName, "connector_uid")
	_workflowExecution.CreatedAt = field.NewInt64(tableName, "created_at")
	_workflowExecution.LogID = field.NewString(tableName, "log_id")
	_workflowExecution.Status = field.NewInt32(tableName, "status")
	_workflowExecution.Duration = field.NewInt64(tableName, "duration")
	_workflowExecution.Input = field.NewString(tableName, "input")
	_workflowExecution.Output = field.NewString(tableName, "output")
	_workflowExecution.ErrorCode = field.NewString(tableName, "error_code")
	_workflowExecution.FailReason = field.NewString(tableName, "fail_reason")
	_workflowExecution.InputTokens = field.NewInt64(tableName, "input_tokens")
	_workflowExecution.OutputTokens = field.NewInt64(tableName, "output_tokens")
	_workflowExecution.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_workflowExecution.RootExecutionID = field.NewInt64(tableName, "root_execution_id")
	_workflowExecution.ParentNodeID = field.NewString(tableName, "parent_node_id")
	_workflowExecution.AppID = field.NewInt64(tableName, "app_id")
	_workflowExecution.NodeCount = field.NewInt32(tableName, "node_count")
	_workflowExecution.ResumeEventID = field.NewInt64(tableName, "resume_event_id")
	_workflowExecution.AgentID = field.NewInt64(tableName, "agent_id")
	_workflowExecution.SyncPattern = field.NewInt32(tableName, "sync_pattern")
	_workflowExecution.CommitID = field.NewString(tableName, "commit_id")

	_workflowExecution.fillFieldMap()

	return _workflowExecution
}

// workflowExecution Workflow Execution Record Table, used to record the status of each workflow execution
type workflowExecution struct {
	workflowExecutionDo

	ALL             field.Asterisk
	ID              field.Int64  // execute id
	WorkflowID      field.Int64  // workflow_id
	Version         field.String // workflow version. empty if is draft
	SpaceID         field.Int64  // the space id the workflow belongs to
	Mode            field.Int32  // the execution mode: 1. debug run 2. release run 3. node debug
	OperatorID      field.Int64  // the user id that runs this workflow
	ConnectorID     field.Int64  // the connector on which this execution happened
	ConnectorUID    field.String // user id of the connector
	CreatedAt       field.Int64  // create time in millisecond
	LogID           field.String // log id
	Status          field.Int32  // 1=running 2=success 3=fail 4=interrupted
	Duration        field.Int64  // execution duration in millisecond
	Input           field.String // actual input of this execution
	Output          field.String // the actual output of this execution
	ErrorCode       field.String // error code if any
	FailReason      field.String // the reason for failure
	InputTokens     field.Int64  // number of input tokens
	OutputTokens    field.Int64  // number of output tokens
	UpdatedAt       field.Int64  // update time in millisecond
	RootExecutionID field.Int64  // the top level execution id. Null if this is the root
	ParentNodeID    field.String // the node key for the sub_workflow node that executes this workflow
	AppID           field.Int64  // app id this workflow execution belongs to
	NodeCount       field.Int32  // the total node count of the workflow
	ResumeEventID   field.Int64  // the current event ID which is resuming
	AgentID         field.Int64  // the agent that this execution binds to
	SyncPattern     field.Int32  // the sync pattern 1. sync 2. async 3. stream
	CommitID        field.String // draft commit id this execution belongs to

	fieldMap map[string]field.Expr
}

func (w workflowExecution) Table(newTableName string) *workflowExecution {
	w.workflowExecutionDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w workflowExecution) As(alias string) *workflowExecution {
	w.workflowExecutionDo.DO = *(w.workflowExecutionDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *workflowExecution) updateTableName(table string) *workflowExecution {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt64(table, "id")
	w.WorkflowID = field.NewInt64(table, "workflow_id")
	w.Version = field.NewString(table, "version")
	w.SpaceID = field.NewInt64(table, "space_id")
	w.Mode = field.NewInt32(table, "mode")
	w.OperatorID = field.NewInt64(table, "operator_id")
	w.ConnectorID = field.NewInt64(table, "connector_id")
	w.ConnectorUID = field.NewString(table, "connector_uid")
	w.CreatedAt = field.NewInt64(table, "created_at")
	w.LogID = field.NewString(table, "log_id")
	w.Status = field.NewInt32(table, "status")
	w.Duration = field.NewInt64(table, "duration")
	w.Input = field.NewString(table, "input")
	w.Output = field.NewString(table, "output")
	w.ErrorCode = field.NewString(table, "error_code")
	w.FailReason = field.NewString(table, "fail_reason")
	w.InputTokens = field.NewInt64(table, "input_tokens")
	w.OutputTokens = field.NewInt64(table, "output_tokens")
	w.UpdatedAt = field.NewInt64(table, "updated_at")
	w.RootExecutionID = field.NewInt64(table, "root_execution_id")
	w.ParentNodeID = field.NewString(table, "parent_node_id")
	w.AppID = field.NewInt64(table, "app_id")
	w.NodeCount = field.NewInt32(table, "node_count")
	w.ResumeEventID = field.NewInt64(table, "resume_event_id")
	w.AgentID = field.NewInt64(table, "agent_id")
	w.SyncPattern = field.NewInt32(table, "sync_pattern")
	w.CommitID = field.NewString(table, "commit_id")

	w.fillFieldMap()

	return w
}

func (w *workflowExecution) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *workflowExecution) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 27)
	w.fieldMap["id"] = w.ID
	w.fieldMap["workflow_id"] = w.WorkflowID
	w.fieldMap["version"] = w.Version
	w.fieldMap["space_id"] = w.SpaceID
	w.fieldMap["mode"] = w.Mode
	w.fieldMap["operator_id"] = w.OperatorID
	w.fieldMap["connector_id"] = w.ConnectorID
	w.fieldMap["connector_uid"] = w.ConnectorUID
	w.fieldMap["created_at"] = w.CreatedAt
	w.fieldMap["log_id"] = w.LogID
	w.fieldMap["status"] = w.Status
	w.fieldMap["duration"] = w.Duration
	w.fieldMap["input"] = w.Input
	w.fieldMap["output"] = w.Output
	w.fieldMap["error_code"] = w.ErrorCode
	w.fieldMap["fail_reason"] = w.FailReason
	w.fieldMap["input_tokens"] = w.InputTokens
	w.fieldMap["output_tokens"] = w.OutputTokens
	w.fieldMap["updated_at"] = w.UpdatedAt
	w.fieldMap["root_execution_id"] = w.RootExecutionID
	w.fieldMap["parent_node_id"] = w.ParentNodeID
	w.fieldMap["app_id"] = w.AppID
	w.fieldMap["node_count"] = w.NodeCount
	w.fieldMap["resume_event_id"] = w.ResumeEventID
	w.fieldMap["agent_id"] = w.AgentID
	w.fieldMap["sync_pattern"] = w.SyncPattern
	w.fieldMap["commit_id"] = w.CommitID
}

func (w workflowExecution) clone(db *gorm.DB) workflowExecution {
	w.workflowExecutionDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w workflowExecution) replaceDB(db *gorm.DB) workflowExecution {
	w.workflowExecutionDo.ReplaceDB(db)
	return w
}

type workflowExecutionDo struct{ gen.DO }

type IWorkflowExecutionDo interface {
	gen.SubQuery
	Debug() IWorkflowExecutionDo
	WithContext(ctx context.Context) IWorkflowExecutionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWorkflowExecutionDo
	WriteDB() IWorkflowExecutionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWorkflowExecutionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWorkflowExecutionDo
	Not(conds ...gen.Condition) IWorkflowExecutionDo
	Or(conds ...gen.Condition) IWorkflowExecutionDo
	Select(conds ...field.Expr) IWorkflowExecutionDo
	Where(conds ...gen.Condition) IWorkflowExecutionDo
	Order(conds ...field.Expr) IWorkflowExecutionDo
	Distinct(cols ...field.Expr) IWorkflowExecutionDo
	Omit(cols ...field.Expr) IWorkflowExecutionDo
	Join(table schema.Tabler, on ...field.Expr) IWorkflowExecutionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWorkflowExecutionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWorkflowExecutionDo
	Group(cols ...field.Expr) IWorkflowExecutionDo
	Having(conds ...gen.Condition) IWorkflowExecutionDo
	Limit(limit int) IWorkflowExecutionDo
	Offset(offset int) IWorkflowExecutionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWorkflowExecutionDo
	Unscoped() IWorkflowExecutionDo
	Create(values ...*model.WorkflowExecution) error
	CreateInBatches(values []*model.WorkflowExecution, batchSize int) error
	Save(values ...*model.WorkflowExecution) error
	First() (*model.WorkflowExecution, error)
	Take() (*model.WorkflowExecution, error)
	Last() (*model.WorkflowExecution, error)
	Find() ([]*model.WorkflowExecution, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WorkflowExecution, err error)
	FindInBatches(result *[]*model.WorkflowExecution, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WorkflowExecution) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWorkflowExecutionDo
	Assign(attrs ...field.AssignExpr) IWorkflowExecutionDo
	Joins(fields ...field.RelationField) IWorkflowExecutionDo
	Preload(fields ...field.RelationField) IWorkflowExecutionDo
	FirstOrInit() (*model.WorkflowExecution, error)
	FirstOrCreate() (*model.WorkflowExecution, error)
	FindByPage(offset int, limit int) (result []*model.WorkflowExecution, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWorkflowExecutionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w workflowExecutionDo) Debug() IWorkflowExecutionDo {
	return w.withDO(w.DO.Debug())
}

func (w workflowExecutionDo) WithContext(ctx context.Context) IWorkflowExecutionDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w workflowExecutionDo) ReadDB() IWorkflowExecutionDo {
	return w.Clauses(dbresolver.Read)
}

func (w workflowExecutionDo) WriteDB() IWorkflowExecutionDo {
	return w.Clauses(dbresolver.Write)
}

func (w workflowExecutionDo) Session(config *gorm.Session) IWorkflowExecutionDo {
	return w.withDO(w.DO.Session(config))
}

func (w workflowExecutionDo) Clauses(conds ...clause.Expression) IWorkflowExecutionDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w workflowExecutionDo) Returning(value interface{}, columns ...string) IWorkflowExecutionDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w workflowExecutionDo) Not(conds ...gen.Condition) IWorkflowExecutionDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w workflowExecutionDo) Or(conds ...gen.Condition) IWorkflowExecutionDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w workflowExecutionDo) Select(conds ...field.Expr) IWorkflowExecutionDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w workflowExecutionDo) Where(conds ...gen.Condition) IWorkflowExecutionDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w workflowExecutionDo) Order(conds ...field.Expr) IWorkflowExecutionDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w workflowExecutionDo) Distinct(cols ...field.Expr) IWorkflowExecutionDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w workflowExecutionDo) Omit(cols ...field.Expr) IWorkflowExecutionDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w workflowExecutionDo) Join(table schema.Tabler, on ...field.Expr) IWorkflowExecutionDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w workflowExecutionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWorkflowExecutionDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w workflowExecutionDo) RightJoin(table schema.Tabler, on ...field.Expr) IWorkflowExecutionDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w workflowExecutionDo) Group(cols ...field.Expr) IWorkflowExecutionDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w workflowExecutionDo) Having(conds ...gen.Condition) IWorkflowExecutionDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w workflowExecutionDo) Limit(limit int) IWorkflowExecutionDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w workflowExecutionDo) Offset(offset int) IWorkflowExecutionDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w workflowExecutionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWorkflowExecutionDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w workflowExecutionDo) Unscoped() IWorkflowExecutionDo {
	return w.withDO(w.DO.Unscoped())
}

func (w workflowExecutionDo) Create(values ...*model.WorkflowExecution) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w workflowExecutionDo) CreateInBatches(values []*model.WorkflowExecution, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w workflowExecutionDo) Save(values ...*model.WorkflowExecution) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w workflowExecutionDo) First() (*model.WorkflowExecution, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowExecution), nil
	}
}

func (w workflowExecutionDo) Take() (*model.WorkflowExecution, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowExecution), nil
	}
}

func (w workflowExecutionDo) Last() (*model.WorkflowExecution, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowExecution), nil
	}
}

func (w workflowExecutionDo) Find() ([]*model.WorkflowExecution, error) {
	result, err := w.DO.Find()
	return result.([]*model.WorkflowExecution), err
}

func (w workflowExecutionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.WorkflowExecution, err error) {
	buf := make([]*model.WorkflowExecution, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w workflowExecutionDo) FindInBatches(result *[]*model.WorkflowExecution, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w workflowExecutionDo) Attrs(attrs ...field.AssignExpr) IWorkflowExecutionDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w workflowExecutionDo) Assign(attrs ...field.AssignExpr) IWorkflowExecutionDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w workflowExecutionDo) Joins(fields ...field.RelationField) IWorkflowExecutionDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w workflowExecutionDo) Preload(fields ...field.RelationField) IWorkflowExecutionDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w workflowExecutionDo) FirstOrInit() (*model.WorkflowExecution, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowExecution), nil
	}
}

func (w workflowExecutionDo) FirstOrCreate() (*model.WorkflowExecution, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WorkflowExecution), nil
	}
}

func (w workflowExecutionDo) FindByPage(offset int, limit int) (result []*model.WorkflowExecution, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w workflowExecutionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w workflowExecutionDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w workflowExecutionDo) Delete(models ...*model.WorkflowExecution) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *workflowExecutionDo) withDO(do gen.Dao) *workflowExecutionDo {
	w.DO = *do.(*gen.DO)
	return w
}
