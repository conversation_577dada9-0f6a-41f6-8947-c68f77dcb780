// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newAppStaticConversationDraft(db *gorm.DB, opts ...gen.DOOption) appStaticConversationDraft {
	_appStaticConversationDraft := appStaticConversationDraft{}

	_appStaticConversationDraft.appStaticConversationDraftDo.UseDB(db, opts...)
	_appStaticConversationDraft.appStaticConversationDraftDo.UseModel(&model.AppStaticConversationDraft{})

	tableName := _appStaticConversationDraft.appStaticConversationDraftDo.TableName()
	_appStaticConversationDraft.ALL = field.NewAsterisk(tableName)
	_appStaticConversationDraft.ID = field.NewInt64(tableName, "id")
	_appStaticConversationDraft.TemplateID = field.NewInt64(tableName, "template_id")
	_appStaticConversationDraft.UserID = field.NewInt64(tableName, "user_id")
	_appStaticConversationDraft.ConnectorID = field.NewInt64(tableName, "connector_id")
	_appStaticConversationDraft.ConversationID = field.NewInt64(tableName, "conversation_id")
	_appStaticConversationDraft.CreatedAt = field.NewInt64(tableName, "created_at")
	_appStaticConversationDraft.DeletedAt = field.NewField(tableName, "deleted_at")

	_appStaticConversationDraft.fillFieldMap()

	return _appStaticConversationDraft
}

type appStaticConversationDraft struct {
	appStaticConversationDraftDo

	ALL            field.Asterisk
	ID             field.Int64 // id
	TemplateID     field.Int64 // template id
	UserID         field.Int64 // user id
	ConnectorID    field.Int64 // connector id
	ConversationID field.Int64 // conversation id
	CreatedAt      field.Int64 // create time in millisecond
	DeletedAt      field.Field // delete time in millisecond

	fieldMap map[string]field.Expr
}

func (a appStaticConversationDraft) Table(newTableName string) *appStaticConversationDraft {
	a.appStaticConversationDraftDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appStaticConversationDraft) As(alias string) *appStaticConversationDraft {
	a.appStaticConversationDraftDo.DO = *(a.appStaticConversationDraftDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appStaticConversationDraft) updateTableName(table string) *appStaticConversationDraft {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.TemplateID = field.NewInt64(table, "template_id")
	a.UserID = field.NewInt64(table, "user_id")
	a.ConnectorID = field.NewInt64(table, "connector_id")
	a.ConversationID = field.NewInt64(table, "conversation_id")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.DeletedAt = field.NewField(table, "deleted_at")

	a.fillFieldMap()

	return a
}

func (a *appStaticConversationDraft) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appStaticConversationDraft) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 7)
	a.fieldMap["id"] = a.ID
	a.fieldMap["template_id"] = a.TemplateID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["connector_id"] = a.ConnectorID
	a.fieldMap["conversation_id"] = a.ConversationID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
}

func (a appStaticConversationDraft) clone(db *gorm.DB) appStaticConversationDraft {
	a.appStaticConversationDraftDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appStaticConversationDraft) replaceDB(db *gorm.DB) appStaticConversationDraft {
	a.appStaticConversationDraftDo.ReplaceDB(db)
	return a
}

type appStaticConversationDraftDo struct{ gen.DO }

type IAppStaticConversationDraftDo interface {
	gen.SubQuery
	Debug() IAppStaticConversationDraftDo
	WithContext(ctx context.Context) IAppStaticConversationDraftDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAppStaticConversationDraftDo
	WriteDB() IAppStaticConversationDraftDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAppStaticConversationDraftDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAppStaticConversationDraftDo
	Not(conds ...gen.Condition) IAppStaticConversationDraftDo
	Or(conds ...gen.Condition) IAppStaticConversationDraftDo
	Select(conds ...field.Expr) IAppStaticConversationDraftDo
	Where(conds ...gen.Condition) IAppStaticConversationDraftDo
	Order(conds ...field.Expr) IAppStaticConversationDraftDo
	Distinct(cols ...field.Expr) IAppStaticConversationDraftDo
	Omit(cols ...field.Expr) IAppStaticConversationDraftDo
	Join(table schema.Tabler, on ...field.Expr) IAppStaticConversationDraftDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAppStaticConversationDraftDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAppStaticConversationDraftDo
	Group(cols ...field.Expr) IAppStaticConversationDraftDo
	Having(conds ...gen.Condition) IAppStaticConversationDraftDo
	Limit(limit int) IAppStaticConversationDraftDo
	Offset(offset int) IAppStaticConversationDraftDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAppStaticConversationDraftDo
	Unscoped() IAppStaticConversationDraftDo
	Create(values ...*model.AppStaticConversationDraft) error
	CreateInBatches(values []*model.AppStaticConversationDraft, batchSize int) error
	Save(values ...*model.AppStaticConversationDraft) error
	First() (*model.AppStaticConversationDraft, error)
	Take() (*model.AppStaticConversationDraft, error)
	Last() (*model.AppStaticConversationDraft, error)
	Find() ([]*model.AppStaticConversationDraft, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppStaticConversationDraft, err error)
	FindInBatches(result *[]*model.AppStaticConversationDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AppStaticConversationDraft) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAppStaticConversationDraftDo
	Assign(attrs ...field.AssignExpr) IAppStaticConversationDraftDo
	Joins(fields ...field.RelationField) IAppStaticConversationDraftDo
	Preload(fields ...field.RelationField) IAppStaticConversationDraftDo
	FirstOrInit() (*model.AppStaticConversationDraft, error)
	FirstOrCreate() (*model.AppStaticConversationDraft, error)
	FindByPage(offset int, limit int) (result []*model.AppStaticConversationDraft, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAppStaticConversationDraftDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a appStaticConversationDraftDo) Debug() IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Debug())
}

func (a appStaticConversationDraftDo) WithContext(ctx context.Context) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appStaticConversationDraftDo) ReadDB() IAppStaticConversationDraftDo {
	return a.Clauses(dbresolver.Read)
}

func (a appStaticConversationDraftDo) WriteDB() IAppStaticConversationDraftDo {
	return a.Clauses(dbresolver.Write)
}

func (a appStaticConversationDraftDo) Session(config *gorm.Session) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Session(config))
}

func (a appStaticConversationDraftDo) Clauses(conds ...clause.Expression) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appStaticConversationDraftDo) Returning(value interface{}, columns ...string) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appStaticConversationDraftDo) Not(conds ...gen.Condition) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appStaticConversationDraftDo) Or(conds ...gen.Condition) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appStaticConversationDraftDo) Select(conds ...field.Expr) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appStaticConversationDraftDo) Where(conds ...gen.Condition) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appStaticConversationDraftDo) Order(conds ...field.Expr) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appStaticConversationDraftDo) Distinct(cols ...field.Expr) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appStaticConversationDraftDo) Omit(cols ...field.Expr) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appStaticConversationDraftDo) Join(table schema.Tabler, on ...field.Expr) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appStaticConversationDraftDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appStaticConversationDraftDo) RightJoin(table schema.Tabler, on ...field.Expr) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appStaticConversationDraftDo) Group(cols ...field.Expr) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appStaticConversationDraftDo) Having(conds ...gen.Condition) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appStaticConversationDraftDo) Limit(limit int) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appStaticConversationDraftDo) Offset(offset int) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appStaticConversationDraftDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appStaticConversationDraftDo) Unscoped() IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appStaticConversationDraftDo) Create(values ...*model.AppStaticConversationDraft) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appStaticConversationDraftDo) CreateInBatches(values []*model.AppStaticConversationDraft, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appStaticConversationDraftDo) Save(values ...*model.AppStaticConversationDraft) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appStaticConversationDraftDo) First() (*model.AppStaticConversationDraft, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppStaticConversationDraft), nil
	}
}

func (a appStaticConversationDraftDo) Take() (*model.AppStaticConversationDraft, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppStaticConversationDraft), nil
	}
}

func (a appStaticConversationDraftDo) Last() (*model.AppStaticConversationDraft, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppStaticConversationDraft), nil
	}
}

func (a appStaticConversationDraftDo) Find() ([]*model.AppStaticConversationDraft, error) {
	result, err := a.DO.Find()
	return result.([]*model.AppStaticConversationDraft), err
}

func (a appStaticConversationDraftDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppStaticConversationDraft, err error) {
	buf := make([]*model.AppStaticConversationDraft, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appStaticConversationDraftDo) FindInBatches(result *[]*model.AppStaticConversationDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appStaticConversationDraftDo) Attrs(attrs ...field.AssignExpr) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appStaticConversationDraftDo) Assign(attrs ...field.AssignExpr) IAppStaticConversationDraftDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appStaticConversationDraftDo) Joins(fields ...field.RelationField) IAppStaticConversationDraftDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appStaticConversationDraftDo) Preload(fields ...field.RelationField) IAppStaticConversationDraftDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appStaticConversationDraftDo) FirstOrInit() (*model.AppStaticConversationDraft, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppStaticConversationDraft), nil
	}
}

func (a appStaticConversationDraftDo) FirstOrCreate() (*model.AppStaticConversationDraft, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppStaticConversationDraft), nil
	}
}

func (a appStaticConversationDraftDo) FindByPage(offset int, limit int) (result []*model.AppStaticConversationDraft, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appStaticConversationDraftDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appStaticConversationDraftDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appStaticConversationDraftDo) Delete(models ...*model.AppStaticConversationDraft) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appStaticConversationDraftDo) withDO(do gen.Dao) *appStaticConversationDraftDo {
	a.DO = *do.(*gen.DO)
	return a
}
