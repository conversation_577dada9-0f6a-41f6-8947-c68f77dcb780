// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newConnectorWorkflowVersion(db *gorm.DB, opts ...gen.DOOption) connectorWorkflowVersion {
	_connectorWorkflowVersion := connectorWorkflowVersion{}

	_connectorWorkflowVersion.connectorWorkflowVersionDo.UseDB(db, opts...)
	_connectorWorkflowVersion.connectorWorkflowVersionDo.UseModel(&model.ConnectorWorkflowVersion{})

	tableName := _connectorWorkflowVersion.connectorWorkflowVersionDo.TableName()
	_connectorWorkflowVersion.ALL = field.NewAsterisk(tableName)
	_connectorWorkflowVersion.ID = field.NewInt64(tableName, "id")
	_connectorWorkflowVersion.AppID = field.NewInt64(tableName, "app_id")
	_connectorWorkflowVersion.ConnectorID = field.NewInt64(tableName, "connector_id")
	_connectorWorkflowVersion.WorkflowID = field.NewInt64(tableName, "workflow_id")
	_connectorWorkflowVersion.Version = field.NewString(tableName, "version")
	_connectorWorkflowVersion.CreatedAt = field.NewInt64(tableName, "created_at")

	_connectorWorkflowVersion.fillFieldMap()

	return _connectorWorkflowVersion
}

type connectorWorkflowVersion struct {
	connectorWorkflowVersionDo

	ALL         field.Asterisk
	ID          field.Int64  // id
	AppID       field.Int64  // app id
	ConnectorID field.Int64  // connector id
	WorkflowID  field.Int64  // workflow id
	Version     field.String // version
	CreatedAt   field.Int64  // create time in millisecond

	fieldMap map[string]field.Expr
}

func (c connectorWorkflowVersion) Table(newTableName string) *connectorWorkflowVersion {
	c.connectorWorkflowVersionDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c connectorWorkflowVersion) As(alias string) *connectorWorkflowVersion {
	c.connectorWorkflowVersionDo.DO = *(c.connectorWorkflowVersionDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *connectorWorkflowVersion) updateTableName(table string) *connectorWorkflowVersion {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.AppID = field.NewInt64(table, "app_id")
	c.ConnectorID = field.NewInt64(table, "connector_id")
	c.WorkflowID = field.NewInt64(table, "workflow_id")
	c.Version = field.NewString(table, "version")
	c.CreatedAt = field.NewInt64(table, "created_at")

	c.fillFieldMap()

	return c
}

func (c *connectorWorkflowVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *connectorWorkflowVersion) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 6)
	c.fieldMap["id"] = c.ID
	c.fieldMap["app_id"] = c.AppID
	c.fieldMap["connector_id"] = c.ConnectorID
	c.fieldMap["workflow_id"] = c.WorkflowID
	c.fieldMap["version"] = c.Version
	c.fieldMap["created_at"] = c.CreatedAt
}

func (c connectorWorkflowVersion) clone(db *gorm.DB) connectorWorkflowVersion {
	c.connectorWorkflowVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c connectorWorkflowVersion) replaceDB(db *gorm.DB) connectorWorkflowVersion {
	c.connectorWorkflowVersionDo.ReplaceDB(db)
	return c
}

type connectorWorkflowVersionDo struct{ gen.DO }

type IConnectorWorkflowVersionDo interface {
	gen.SubQuery
	Debug() IConnectorWorkflowVersionDo
	WithContext(ctx context.Context) IConnectorWorkflowVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IConnectorWorkflowVersionDo
	WriteDB() IConnectorWorkflowVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IConnectorWorkflowVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IConnectorWorkflowVersionDo
	Not(conds ...gen.Condition) IConnectorWorkflowVersionDo
	Or(conds ...gen.Condition) IConnectorWorkflowVersionDo
	Select(conds ...field.Expr) IConnectorWorkflowVersionDo
	Where(conds ...gen.Condition) IConnectorWorkflowVersionDo
	Order(conds ...field.Expr) IConnectorWorkflowVersionDo
	Distinct(cols ...field.Expr) IConnectorWorkflowVersionDo
	Omit(cols ...field.Expr) IConnectorWorkflowVersionDo
	Join(table schema.Tabler, on ...field.Expr) IConnectorWorkflowVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IConnectorWorkflowVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IConnectorWorkflowVersionDo
	Group(cols ...field.Expr) IConnectorWorkflowVersionDo
	Having(conds ...gen.Condition) IConnectorWorkflowVersionDo
	Limit(limit int) IConnectorWorkflowVersionDo
	Offset(offset int) IConnectorWorkflowVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IConnectorWorkflowVersionDo
	Unscoped() IConnectorWorkflowVersionDo
	Create(values ...*model.ConnectorWorkflowVersion) error
	CreateInBatches(values []*model.ConnectorWorkflowVersion, batchSize int) error
	Save(values ...*model.ConnectorWorkflowVersion) error
	First() (*model.ConnectorWorkflowVersion, error)
	Take() (*model.ConnectorWorkflowVersion, error)
	Last() (*model.ConnectorWorkflowVersion, error)
	Find() ([]*model.ConnectorWorkflowVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ConnectorWorkflowVersion, err error)
	FindInBatches(result *[]*model.ConnectorWorkflowVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ConnectorWorkflowVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IConnectorWorkflowVersionDo
	Assign(attrs ...field.AssignExpr) IConnectorWorkflowVersionDo
	Joins(fields ...field.RelationField) IConnectorWorkflowVersionDo
	Preload(fields ...field.RelationField) IConnectorWorkflowVersionDo
	FirstOrInit() (*model.ConnectorWorkflowVersion, error)
	FirstOrCreate() (*model.ConnectorWorkflowVersion, error)
	FindByPage(offset int, limit int) (result []*model.ConnectorWorkflowVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IConnectorWorkflowVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c connectorWorkflowVersionDo) Debug() IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Debug())
}

func (c connectorWorkflowVersionDo) WithContext(ctx context.Context) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c connectorWorkflowVersionDo) ReadDB() IConnectorWorkflowVersionDo {
	return c.Clauses(dbresolver.Read)
}

func (c connectorWorkflowVersionDo) WriteDB() IConnectorWorkflowVersionDo {
	return c.Clauses(dbresolver.Write)
}

func (c connectorWorkflowVersionDo) Session(config *gorm.Session) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Session(config))
}

func (c connectorWorkflowVersionDo) Clauses(conds ...clause.Expression) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c connectorWorkflowVersionDo) Returning(value interface{}, columns ...string) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c connectorWorkflowVersionDo) Not(conds ...gen.Condition) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c connectorWorkflowVersionDo) Or(conds ...gen.Condition) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c connectorWorkflowVersionDo) Select(conds ...field.Expr) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c connectorWorkflowVersionDo) Where(conds ...gen.Condition) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c connectorWorkflowVersionDo) Order(conds ...field.Expr) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c connectorWorkflowVersionDo) Distinct(cols ...field.Expr) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c connectorWorkflowVersionDo) Omit(cols ...field.Expr) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c connectorWorkflowVersionDo) Join(table schema.Tabler, on ...field.Expr) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c connectorWorkflowVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c connectorWorkflowVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c connectorWorkflowVersionDo) Group(cols ...field.Expr) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c connectorWorkflowVersionDo) Having(conds ...gen.Condition) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c connectorWorkflowVersionDo) Limit(limit int) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c connectorWorkflowVersionDo) Offset(offset int) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c connectorWorkflowVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c connectorWorkflowVersionDo) Unscoped() IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Unscoped())
}

func (c connectorWorkflowVersionDo) Create(values ...*model.ConnectorWorkflowVersion) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c connectorWorkflowVersionDo) CreateInBatches(values []*model.ConnectorWorkflowVersion, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c connectorWorkflowVersionDo) Save(values ...*model.ConnectorWorkflowVersion) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c connectorWorkflowVersionDo) First() (*model.ConnectorWorkflowVersion, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConnectorWorkflowVersion), nil
	}
}

func (c connectorWorkflowVersionDo) Take() (*model.ConnectorWorkflowVersion, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConnectorWorkflowVersion), nil
	}
}

func (c connectorWorkflowVersionDo) Last() (*model.ConnectorWorkflowVersion, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConnectorWorkflowVersion), nil
	}
}

func (c connectorWorkflowVersionDo) Find() ([]*model.ConnectorWorkflowVersion, error) {
	result, err := c.DO.Find()
	return result.([]*model.ConnectorWorkflowVersion), err
}

func (c connectorWorkflowVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ConnectorWorkflowVersion, err error) {
	buf := make([]*model.ConnectorWorkflowVersion, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c connectorWorkflowVersionDo) FindInBatches(result *[]*model.ConnectorWorkflowVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c connectorWorkflowVersionDo) Attrs(attrs ...field.AssignExpr) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c connectorWorkflowVersionDo) Assign(attrs ...field.AssignExpr) IConnectorWorkflowVersionDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c connectorWorkflowVersionDo) Joins(fields ...field.RelationField) IConnectorWorkflowVersionDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c connectorWorkflowVersionDo) Preload(fields ...field.RelationField) IConnectorWorkflowVersionDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c connectorWorkflowVersionDo) FirstOrInit() (*model.ConnectorWorkflowVersion, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConnectorWorkflowVersion), nil
	}
}

func (c connectorWorkflowVersionDo) FirstOrCreate() (*model.ConnectorWorkflowVersion, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConnectorWorkflowVersion), nil
	}
}

func (c connectorWorkflowVersionDo) FindByPage(offset int, limit int) (result []*model.ConnectorWorkflowVersion, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c connectorWorkflowVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c connectorWorkflowVersionDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c connectorWorkflowVersionDo) Delete(models ...*model.ConnectorWorkflowVersion) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *connectorWorkflowVersionDo) withDO(do gen.Dao) *connectorWorkflowVersionDo {
	c.DO = *do.(*gen.DO)
	return c
}
