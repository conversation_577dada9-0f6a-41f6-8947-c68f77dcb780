// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newAppDynamicConversationDraft(db *gorm.DB, opts ...gen.DOOption) appDynamicConversationDraft {
	_appDynamicConversationDraft := appDynamicConversationDraft{}

	_appDynamicConversationDraft.appDynamicConversationDraftDo.UseDB(db, opts...)
	_appDynamicConversationDraft.appDynamicConversationDraftDo.UseModel(&model.AppDynamicConversationDraft{})

	tableName := _appDynamicConversationDraft.appDynamicConversationDraftDo.TableName()
	_appDynamicConversationDraft.ALL = field.NewAsterisk(tableName)
	_appDynamicConversationDraft.ID = field.NewInt64(tableName, "id")
	_appDynamicConversationDraft.AppID = field.NewInt64(tableName, "app_id")
	_appDynamicConversationDraft.Name = field.NewString(tableName, "name")
	_appDynamicConversationDraft.UserID = field.NewInt64(tableName, "user_id")
	_appDynamicConversationDraft.ConnectorID = field.NewInt64(tableName, "connector_id")
	_appDynamicConversationDraft.ConversationID = field.NewInt64(tableName, "conversation_id")
	_appDynamicConversationDraft.CreatedAt = field.NewInt64(tableName, "created_at")
	_appDynamicConversationDraft.DeletedAt = field.NewField(tableName, "deleted_at")

	_appDynamicConversationDraft.fillFieldMap()

	return _appDynamicConversationDraft
}

type appDynamicConversationDraft struct {
	appDynamicConversationDraftDo

	ALL            field.Asterisk
	ID             field.Int64  // id
	AppID          field.Int64  // app id
	Name           field.String // conversion name
	UserID         field.Int64  // user id
	ConnectorID    field.Int64  // connector id
	ConversationID field.Int64  // conversation id
	CreatedAt      field.Int64  // create time in millisecond
	DeletedAt      field.Field  // delete time in millisecond

	fieldMap map[string]field.Expr
}

func (a appDynamicConversationDraft) Table(newTableName string) *appDynamicConversationDraft {
	a.appDynamicConversationDraftDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appDynamicConversationDraft) As(alias string) *appDynamicConversationDraft {
	a.appDynamicConversationDraftDo.DO = *(a.appDynamicConversationDraftDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appDynamicConversationDraft) updateTableName(table string) *appDynamicConversationDraft {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.AppID = field.NewInt64(table, "app_id")
	a.Name = field.NewString(table, "name")
	a.UserID = field.NewInt64(table, "user_id")
	a.ConnectorID = field.NewInt64(table, "connector_id")
	a.ConversationID = field.NewInt64(table, "conversation_id")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.DeletedAt = field.NewField(table, "deleted_at")

	a.fillFieldMap()

	return a
}

func (a *appDynamicConversationDraft) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appDynamicConversationDraft) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 8)
	a.fieldMap["id"] = a.ID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["name"] = a.Name
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["connector_id"] = a.ConnectorID
	a.fieldMap["conversation_id"] = a.ConversationID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
}

func (a appDynamicConversationDraft) clone(db *gorm.DB) appDynamicConversationDraft {
	a.appDynamicConversationDraftDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appDynamicConversationDraft) replaceDB(db *gorm.DB) appDynamicConversationDraft {
	a.appDynamicConversationDraftDo.ReplaceDB(db)
	return a
}

type appDynamicConversationDraftDo struct{ gen.DO }

type IAppDynamicConversationDraftDo interface {
	gen.SubQuery
	Debug() IAppDynamicConversationDraftDo
	WithContext(ctx context.Context) IAppDynamicConversationDraftDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAppDynamicConversationDraftDo
	WriteDB() IAppDynamicConversationDraftDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAppDynamicConversationDraftDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAppDynamicConversationDraftDo
	Not(conds ...gen.Condition) IAppDynamicConversationDraftDo
	Or(conds ...gen.Condition) IAppDynamicConversationDraftDo
	Select(conds ...field.Expr) IAppDynamicConversationDraftDo
	Where(conds ...gen.Condition) IAppDynamicConversationDraftDo
	Order(conds ...field.Expr) IAppDynamicConversationDraftDo
	Distinct(cols ...field.Expr) IAppDynamicConversationDraftDo
	Omit(cols ...field.Expr) IAppDynamicConversationDraftDo
	Join(table schema.Tabler, on ...field.Expr) IAppDynamicConversationDraftDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAppDynamicConversationDraftDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAppDynamicConversationDraftDo
	Group(cols ...field.Expr) IAppDynamicConversationDraftDo
	Having(conds ...gen.Condition) IAppDynamicConversationDraftDo
	Limit(limit int) IAppDynamicConversationDraftDo
	Offset(offset int) IAppDynamicConversationDraftDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAppDynamicConversationDraftDo
	Unscoped() IAppDynamicConversationDraftDo
	Create(values ...*model.AppDynamicConversationDraft) error
	CreateInBatches(values []*model.AppDynamicConversationDraft, batchSize int) error
	Save(values ...*model.AppDynamicConversationDraft) error
	First() (*model.AppDynamicConversationDraft, error)
	Take() (*model.AppDynamicConversationDraft, error)
	Last() (*model.AppDynamicConversationDraft, error)
	Find() ([]*model.AppDynamicConversationDraft, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppDynamicConversationDraft, err error)
	FindInBatches(result *[]*model.AppDynamicConversationDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AppDynamicConversationDraft) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAppDynamicConversationDraftDo
	Assign(attrs ...field.AssignExpr) IAppDynamicConversationDraftDo
	Joins(fields ...field.RelationField) IAppDynamicConversationDraftDo
	Preload(fields ...field.RelationField) IAppDynamicConversationDraftDo
	FirstOrInit() (*model.AppDynamicConversationDraft, error)
	FirstOrCreate() (*model.AppDynamicConversationDraft, error)
	FindByPage(offset int, limit int) (result []*model.AppDynamicConversationDraft, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAppDynamicConversationDraftDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a appDynamicConversationDraftDo) Debug() IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Debug())
}

func (a appDynamicConversationDraftDo) WithContext(ctx context.Context) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appDynamicConversationDraftDo) ReadDB() IAppDynamicConversationDraftDo {
	return a.Clauses(dbresolver.Read)
}

func (a appDynamicConversationDraftDo) WriteDB() IAppDynamicConversationDraftDo {
	return a.Clauses(dbresolver.Write)
}

func (a appDynamicConversationDraftDo) Session(config *gorm.Session) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Session(config))
}

func (a appDynamicConversationDraftDo) Clauses(conds ...clause.Expression) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appDynamicConversationDraftDo) Returning(value interface{}, columns ...string) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appDynamicConversationDraftDo) Not(conds ...gen.Condition) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appDynamicConversationDraftDo) Or(conds ...gen.Condition) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appDynamicConversationDraftDo) Select(conds ...field.Expr) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appDynamicConversationDraftDo) Where(conds ...gen.Condition) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appDynamicConversationDraftDo) Order(conds ...field.Expr) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appDynamicConversationDraftDo) Distinct(cols ...field.Expr) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appDynamicConversationDraftDo) Omit(cols ...field.Expr) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appDynamicConversationDraftDo) Join(table schema.Tabler, on ...field.Expr) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appDynamicConversationDraftDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appDynamicConversationDraftDo) RightJoin(table schema.Tabler, on ...field.Expr) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appDynamicConversationDraftDo) Group(cols ...field.Expr) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appDynamicConversationDraftDo) Having(conds ...gen.Condition) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appDynamicConversationDraftDo) Limit(limit int) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appDynamicConversationDraftDo) Offset(offset int) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appDynamicConversationDraftDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appDynamicConversationDraftDo) Unscoped() IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appDynamicConversationDraftDo) Create(values ...*model.AppDynamicConversationDraft) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appDynamicConversationDraftDo) CreateInBatches(values []*model.AppDynamicConversationDraft, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appDynamicConversationDraftDo) Save(values ...*model.AppDynamicConversationDraft) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appDynamicConversationDraftDo) First() (*model.AppDynamicConversationDraft, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDynamicConversationDraft), nil
	}
}

func (a appDynamicConversationDraftDo) Take() (*model.AppDynamicConversationDraft, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDynamicConversationDraft), nil
	}
}

func (a appDynamicConversationDraftDo) Last() (*model.AppDynamicConversationDraft, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDynamicConversationDraft), nil
	}
}

func (a appDynamicConversationDraftDo) Find() ([]*model.AppDynamicConversationDraft, error) {
	result, err := a.DO.Find()
	return result.([]*model.AppDynamicConversationDraft), err
}

func (a appDynamicConversationDraftDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppDynamicConversationDraft, err error) {
	buf := make([]*model.AppDynamicConversationDraft, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appDynamicConversationDraftDo) FindInBatches(result *[]*model.AppDynamicConversationDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appDynamicConversationDraftDo) Attrs(attrs ...field.AssignExpr) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appDynamicConversationDraftDo) Assign(attrs ...field.AssignExpr) IAppDynamicConversationDraftDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appDynamicConversationDraftDo) Joins(fields ...field.RelationField) IAppDynamicConversationDraftDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appDynamicConversationDraftDo) Preload(fields ...field.RelationField) IAppDynamicConversationDraftDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appDynamicConversationDraftDo) FirstOrInit() (*model.AppDynamicConversationDraft, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDynamicConversationDraft), nil
	}
}

func (a appDynamicConversationDraftDo) FirstOrCreate() (*model.AppDynamicConversationDraft, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDynamicConversationDraft), nil
	}
}

func (a appDynamicConversationDraftDo) FindByPage(offset int, limit int) (result []*model.AppDynamicConversationDraft, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appDynamicConversationDraftDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appDynamicConversationDraftDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appDynamicConversationDraftDo) Delete(models ...*model.AppDynamicConversationDraft) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appDynamicConversationDraftDo) withDO(do gen.Dao) *appDynamicConversationDraftDo {
	a.DO = *do.(*gen.DO)
	return a
}
