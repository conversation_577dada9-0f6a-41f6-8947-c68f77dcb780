// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/prompt/internal/dal/model"
)

func newPromptResource(db *gorm.DB, opts ...gen.DOOption) promptResource {
	_promptResource := promptResource{}

	_promptResource.promptResourceDo.UseDB(db, opts...)
	_promptResource.promptResourceDo.UseModel(&model.PromptResource{})

	tableName := _promptResource.promptResourceDo.TableName()
	_promptResource.ALL = field.NewAsterisk(tableName)
	_promptResource.ID = field.NewInt64(tableName, "id")
	_promptResource.SpaceID = field.NewInt64(tableName, "space_id")
	_promptResource.Name = field.NewString(tableName, "name")
	_promptResource.Description = field.NewString(tableName, "description")
	_promptResource.PromptText = field.NewString(tableName, "prompt_text")
	_promptResource.Status = field.NewInt32(tableName, "status")
	_promptResource.CreatorID = field.NewInt64(tableName, "creator_id")
	_promptResource.CreatedAt = field.NewInt64(tableName, "created_at")
	_promptResource.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_promptResource.fillFieldMap()

	return _promptResource
}

// promptResource prompt_resource
type promptResource struct {
	promptResourceDo

	ALL         field.Asterisk
	ID          field.Int64  // id
	SpaceID     field.Int64  // space id
	Name        field.String // name
	Description field.String // description
	PromptText  field.String // prompt text
	Status      field.Int32  // status, 0 is invalid, 1 is valid
	CreatorID   field.Int64  // creator id
	CreatedAt   field.Int64  // Create Time in Milliseconds
	UpdatedAt   field.Int64  // Update Time in Milliseconds

	fieldMap map[string]field.Expr
}

func (p promptResource) Table(newTableName string) *promptResource {
	p.promptResourceDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p promptResource) As(alias string) *promptResource {
	p.promptResourceDo.DO = *(p.promptResourceDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *promptResource) updateTableName(table string) *promptResource {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.SpaceID = field.NewInt64(table, "space_id")
	p.Name = field.NewString(table, "name")
	p.Description = field.NewString(table, "description")
	p.PromptText = field.NewString(table, "prompt_text")
	p.Status = field.NewInt32(table, "status")
	p.CreatorID = field.NewInt64(table, "creator_id")
	p.CreatedAt = field.NewInt64(table, "created_at")
	p.UpdatedAt = field.NewInt64(table, "updated_at")

	p.fillFieldMap()

	return p
}

func (p *promptResource) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *promptResource) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 9)
	p.fieldMap["id"] = p.ID
	p.fieldMap["space_id"] = p.SpaceID
	p.fieldMap["name"] = p.Name
	p.fieldMap["description"] = p.Description
	p.fieldMap["prompt_text"] = p.PromptText
	p.fieldMap["status"] = p.Status
	p.fieldMap["creator_id"] = p.CreatorID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
}

func (p promptResource) clone(db *gorm.DB) promptResource {
	p.promptResourceDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p promptResource) replaceDB(db *gorm.DB) promptResource {
	p.promptResourceDo.ReplaceDB(db)
	return p
}

type promptResourceDo struct{ gen.DO }

type IPromptResourceDo interface {
	gen.SubQuery
	Debug() IPromptResourceDo
	WithContext(ctx context.Context) IPromptResourceDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPromptResourceDo
	WriteDB() IPromptResourceDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPromptResourceDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPromptResourceDo
	Not(conds ...gen.Condition) IPromptResourceDo
	Or(conds ...gen.Condition) IPromptResourceDo
	Select(conds ...field.Expr) IPromptResourceDo
	Where(conds ...gen.Condition) IPromptResourceDo
	Order(conds ...field.Expr) IPromptResourceDo
	Distinct(cols ...field.Expr) IPromptResourceDo
	Omit(cols ...field.Expr) IPromptResourceDo
	Join(table schema.Tabler, on ...field.Expr) IPromptResourceDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPromptResourceDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPromptResourceDo
	Group(cols ...field.Expr) IPromptResourceDo
	Having(conds ...gen.Condition) IPromptResourceDo
	Limit(limit int) IPromptResourceDo
	Offset(offset int) IPromptResourceDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPromptResourceDo
	Unscoped() IPromptResourceDo
	Create(values ...*model.PromptResource) error
	CreateInBatches(values []*model.PromptResource, batchSize int) error
	Save(values ...*model.PromptResource) error
	First() (*model.PromptResource, error)
	Take() (*model.PromptResource, error)
	Last() (*model.PromptResource, error)
	Find() ([]*model.PromptResource, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PromptResource, err error)
	FindInBatches(result *[]*model.PromptResource, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PromptResource) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPromptResourceDo
	Assign(attrs ...field.AssignExpr) IPromptResourceDo
	Joins(fields ...field.RelationField) IPromptResourceDo
	Preload(fields ...field.RelationField) IPromptResourceDo
	FirstOrInit() (*model.PromptResource, error)
	FirstOrCreate() (*model.PromptResource, error)
	FindByPage(offset int, limit int) (result []*model.PromptResource, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPromptResourceDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p promptResourceDo) Debug() IPromptResourceDo {
	return p.withDO(p.DO.Debug())
}

func (p promptResourceDo) WithContext(ctx context.Context) IPromptResourceDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p promptResourceDo) ReadDB() IPromptResourceDo {
	return p.Clauses(dbresolver.Read)
}

func (p promptResourceDo) WriteDB() IPromptResourceDo {
	return p.Clauses(dbresolver.Write)
}

func (p promptResourceDo) Session(config *gorm.Session) IPromptResourceDo {
	return p.withDO(p.DO.Session(config))
}

func (p promptResourceDo) Clauses(conds ...clause.Expression) IPromptResourceDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p promptResourceDo) Returning(value interface{}, columns ...string) IPromptResourceDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p promptResourceDo) Not(conds ...gen.Condition) IPromptResourceDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p promptResourceDo) Or(conds ...gen.Condition) IPromptResourceDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p promptResourceDo) Select(conds ...field.Expr) IPromptResourceDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p promptResourceDo) Where(conds ...gen.Condition) IPromptResourceDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p promptResourceDo) Order(conds ...field.Expr) IPromptResourceDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p promptResourceDo) Distinct(cols ...field.Expr) IPromptResourceDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p promptResourceDo) Omit(cols ...field.Expr) IPromptResourceDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p promptResourceDo) Join(table schema.Tabler, on ...field.Expr) IPromptResourceDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p promptResourceDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPromptResourceDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p promptResourceDo) RightJoin(table schema.Tabler, on ...field.Expr) IPromptResourceDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p promptResourceDo) Group(cols ...field.Expr) IPromptResourceDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p promptResourceDo) Having(conds ...gen.Condition) IPromptResourceDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p promptResourceDo) Limit(limit int) IPromptResourceDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p promptResourceDo) Offset(offset int) IPromptResourceDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p promptResourceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPromptResourceDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p promptResourceDo) Unscoped() IPromptResourceDo {
	return p.withDO(p.DO.Unscoped())
}

func (p promptResourceDo) Create(values ...*model.PromptResource) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p promptResourceDo) CreateInBatches(values []*model.PromptResource, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p promptResourceDo) Save(values ...*model.PromptResource) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p promptResourceDo) First() (*model.PromptResource, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PromptResource), nil
	}
}

func (p promptResourceDo) Take() (*model.PromptResource, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PromptResource), nil
	}
}

func (p promptResourceDo) Last() (*model.PromptResource, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PromptResource), nil
	}
}

func (p promptResourceDo) Find() ([]*model.PromptResource, error) {
	result, err := p.DO.Find()
	return result.([]*model.PromptResource), err
}

func (p promptResourceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PromptResource, err error) {
	buf := make([]*model.PromptResource, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p promptResourceDo) FindInBatches(result *[]*model.PromptResource, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p promptResourceDo) Attrs(attrs ...field.AssignExpr) IPromptResourceDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p promptResourceDo) Assign(attrs ...field.AssignExpr) IPromptResourceDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p promptResourceDo) Joins(fields ...field.RelationField) IPromptResourceDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p promptResourceDo) Preload(fields ...field.RelationField) IPromptResourceDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p promptResourceDo) FirstOrInit() (*model.PromptResource, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PromptResource), nil
	}
}

func (p promptResourceDo) FirstOrCreate() (*model.PromptResource, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PromptResource), nil
	}
}

func (p promptResourceDo) FindByPage(offset int, limit int) (result []*model.PromptResource, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p promptResourceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p promptResourceDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p promptResourceDo) Delete(models ...*model.PromptResource) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *promptResourceDo) withDO(do gen.Dao) *promptResourceDo {
	p.DO = *do.(*gen.DO)
	return p
}
