/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import product_public_api "github.com/coze-dev/coze-studio/backend/api/model/marketplace/product_public_api"

const TableNameTemplate = "template"

// Template Template Info Table
type Template struct {
	ID                int64                                 `gorm:"column:id;primaryKey;autoIncrement:true;comment:Primary Key ID" json:"id"`                              // Primary Key ID
	AgentID           int64                                 `gorm:"column:agent_id;not null;comment:Agent ID" json:"agent_id"`                                             // Agent ID
	SpaceID           int64                                 `gorm:"column:space_id;not null;comment:Space ID" json:"space_id"`                                             // Space ID
	CreatedAt         int64                                 `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	Heat              int64                                 `gorm:"column:heat;not null;comment:Heat" json:"heat"`                                                         // Heat
	ProductEntityType int64                                 `gorm:"column:product_entity_type;not null;comment:Product Entity Type" json:"product_entity_type"`            // Product Entity Type
	MetaInfo          *product_public_api.ProductMetaInfo   `gorm:"column:meta_info;comment:Meta Info;serializer:json" json:"meta_info"`                                   // Meta Info
	PluginExtra       *product_public_api.PluginExtraInfo   `gorm:"column:plugin_extra;comment:Plugin Extra Info;serializer:json" json:"plugin_extra"`                     // Plugin Extra Info
	AgentExtra        *product_public_api.BotExtraInfo      `gorm:"column:agent_extra;comment:Agent Extra Info;serializer:json" json:"agent_extra"`                        // Agent Extra Info
	WorkflowExtra     *product_public_api.WorkflowExtraInfo `gorm:"column:workflow_extra;comment:Workflow Extra Info;serializer:json" json:"workflow_extra"`               // Workflow Extra Info
	ProjectExtra      *product_public_api.ProjectExtraInfo  `gorm:"column:project_extra;comment:Project Extra Info;serializer:json" json:"project_extra"`                  // Project Extra Info
}

// TableName Template's table name
func (*Template) TableName() string {
	return TableNameTemplate
}
