// Code generated by MockGen. DO NOT EDIT.
// Source: ./oauth_repository.go
//
// Generated by this command:
//
//	mockgen -source=./oauth_repository.go -package=mock_plugin_oauth -destination=./mock/mock_oauth_repository.go
//

// Package mock_plugin_oauth is a generated GoMock package.
package mock_plugin_oauth

import (
	context "context"
	reflect "reflect"

	entity "github.com/coze-dev/coze-studio/backend/domain/plugin/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockOAuthRepository is a mock of OAuthRepository interface.
type MockOAuthRepository struct {
	ctrl     *gomock.Controller
	recorder *MockOAuthRepositoryMockRecorder
}

// MockOAuthRepositoryMockRecorder is the mock recorder for MockOAuthRepository.
type MockOAuthRepositoryMockRecorder struct {
	mock *MockOAuthRepository
}

// NewMockOAuthRepository creates a new mock instance.
func NewMockOAuthRepository(ctrl *gomock.Controller) *MockOAuthRepository {
	mock := &MockOAuthRepository{ctrl: ctrl}
	mock.recorder = &MockOAuthRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOAuthRepository) EXPECT() *MockOAuthRepositoryMockRecorder {
	return m.recorder
}

// BatchDeleteAuthorizationCodeByIDs mocks base method.
func (m *MockOAuthRepository) BatchDeleteAuthorizationCodeByIDs(ctx context.Context, ids []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteAuthorizationCodeByIDs", ctx, ids)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteAuthorizationCodeByIDs indicates an expected call of BatchDeleteAuthorizationCodeByIDs.
func (mr *MockOAuthRepositoryMockRecorder) BatchDeleteAuthorizationCodeByIDs(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteAuthorizationCodeByIDs", reflect.TypeOf((*MockOAuthRepository)(nil).BatchDeleteAuthorizationCodeByIDs), ctx, ids)
}

// DeleteAuthorizationCode mocks base method.
func (m *MockOAuthRepository) DeleteAuthorizationCode(ctx context.Context, meta *entity.AuthorizationCodeMeta) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAuthorizationCode", ctx, meta)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAuthorizationCode indicates an expected call of DeleteAuthorizationCode.
func (mr *MockOAuthRepositoryMockRecorder) DeleteAuthorizationCode(ctx, meta any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAuthorizationCode", reflect.TypeOf((*MockOAuthRepository)(nil).DeleteAuthorizationCode), ctx, meta)
}

// DeleteExpiredAuthorizationCodeTokens mocks base method.
func (m *MockOAuthRepository) DeleteExpiredAuthorizationCodeTokens(ctx context.Context, expireAt int64, limit int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteExpiredAuthorizationCodeTokens", ctx, expireAt, limit)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteExpiredAuthorizationCodeTokens indicates an expected call of DeleteExpiredAuthorizationCodeTokens.
func (mr *MockOAuthRepositoryMockRecorder) DeleteExpiredAuthorizationCodeTokens(ctx, expireAt, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteExpiredAuthorizationCodeTokens", reflect.TypeOf((*MockOAuthRepository)(nil).DeleteExpiredAuthorizationCodeTokens), ctx, expireAt, limit)
}

// DeleteInactiveAuthorizationCodeTokens mocks base method.
func (m *MockOAuthRepository) DeleteInactiveAuthorizationCodeTokens(ctx context.Context, lastActiveAt int64, limit int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInactiveAuthorizationCodeTokens", ctx, lastActiveAt, limit)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteInactiveAuthorizationCodeTokens indicates an expected call of DeleteInactiveAuthorizationCodeTokens.
func (mr *MockOAuthRepositoryMockRecorder) DeleteInactiveAuthorizationCodeTokens(ctx, lastActiveAt, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInactiveAuthorizationCodeTokens", reflect.TypeOf((*MockOAuthRepository)(nil).DeleteInactiveAuthorizationCodeTokens), ctx, lastActiveAt, limit)
}

// GetAuthorizationCode mocks base method.
func (m *MockOAuthRepository) GetAuthorizationCode(ctx context.Context, meta *entity.AuthorizationCodeMeta) (*entity.AuthorizationCodeInfo, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthorizationCode", ctx, meta)
	ret0, _ := ret[0].(*entity.AuthorizationCodeInfo)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAuthorizationCode indicates an expected call of GetAuthorizationCode.
func (mr *MockOAuthRepositoryMockRecorder) GetAuthorizationCode(ctx, meta any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthorizationCode", reflect.TypeOf((*MockOAuthRepository)(nil).GetAuthorizationCode), ctx, meta)
}

// GetAuthorizationCodeRefreshTokens mocks base method.
func (m *MockOAuthRepository) GetAuthorizationCodeRefreshTokens(ctx context.Context, nextRefreshAt int64, limit int) ([]*entity.AuthorizationCodeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthorizationCodeRefreshTokens", ctx, nextRefreshAt, limit)
	ret0, _ := ret[0].([]*entity.AuthorizationCodeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthorizationCodeRefreshTokens indicates an expected call of GetAuthorizationCodeRefreshTokens.
func (mr *MockOAuthRepositoryMockRecorder) GetAuthorizationCodeRefreshTokens(ctx, nextRefreshAt, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthorizationCodeRefreshTokens", reflect.TypeOf((*MockOAuthRepository)(nil).GetAuthorizationCodeRefreshTokens), ctx, nextRefreshAt, limit)
}

// UpdateAuthorizationCodeLastActiveAt mocks base method.
func (m *MockOAuthRepository) UpdateAuthorizationCodeLastActiveAt(ctx context.Context, meta *entity.AuthorizationCodeMeta, lastActiveAtMs int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAuthorizationCodeLastActiveAt", ctx, meta, lastActiveAtMs)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAuthorizationCodeLastActiveAt indicates an expected call of UpdateAuthorizationCodeLastActiveAt.
func (mr *MockOAuthRepositoryMockRecorder) UpdateAuthorizationCodeLastActiveAt(ctx, meta, lastActiveAtMs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAuthorizationCodeLastActiveAt", reflect.TypeOf((*MockOAuthRepository)(nil).UpdateAuthorizationCodeLastActiveAt), ctx, meta, lastActiveAtMs)
}

// UpsertAuthorizationCode mocks base method.
func (m *MockOAuthRepository) UpsertAuthorizationCode(ctx context.Context, info *entity.AuthorizationCodeInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertAuthorizationCode", ctx, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertAuthorizationCode indicates an expected call of UpsertAuthorizationCode.
func (mr *MockOAuthRepositoryMockRecorder) UpsertAuthorizationCode(ctx, info any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertAuthorizationCode", reflect.TypeOf((*MockOAuthRepository)(nil).UpsertAuthorizationCode), ctx, info)
}
