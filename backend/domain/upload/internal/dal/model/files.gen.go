/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameFiles = "files"

// Files file resource table
type Files struct {
	ID            int64          `gorm:"column:id;primaryKey;comment:id" json:"id"`                                                             // id
	Name          string         `gorm:"column:name;not null;comment:file name" json:"name"`                                                    // file name
	FileSize      int64          `gorm:"column:file_size;not null;comment:file size" json:"file_size"`                                          // file size
	TosURI        string         `gorm:"column:tos_uri;not null;comment:TOS URI" json:"tos_uri"`                                                // TOS URI
	Status        int32          `gorm:"column:status;not null;comment:status，0invalid，1valid" json:"status"`                                   // status，0invalid，1valid
	Comment       string         `gorm:"column:comment;not null;comment:file comment" json:"comment"`                                           // file comment
	Source        int32          `gorm:"column:source;not null;comment:source：1 from API," json:"source"`                                       // source：1 from API,
	CreatorID     string         `gorm:"column:creator_id;not null;comment:creator id" json:"creator_id"`                                       // creator id
	ContentType   string         `gorm:"column:content_type;not null;comment:content type" json:"content_type"`                                 // content type
	CozeAccountID int64          `gorm:"column:coze_account_id;not null;comment:coze account id" json:"coze_account_id"`                        // coze account id
	CreatedAt     int64          `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	UpdatedAt     int64          `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"` // Update Time in Milliseconds
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;comment:Delete Time" json:"deleted_at"`                                               // Delete Time
}

// TableName Files's table name
func (*Files) TableName() string {
	return TableNameFiles
}
