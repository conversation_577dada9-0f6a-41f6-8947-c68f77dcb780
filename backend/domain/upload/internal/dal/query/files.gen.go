/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/upload/internal/dal/model"
)

func newFiles(db *gorm.DB, opts ...gen.DOOption) files {
	_files := files{}

	_files.filesDo.UseDB(db, opts...)
	_files.filesDo.UseModel(&model.Files{})

	tableName := _files.filesDo.TableName()
	_files.ALL = field.NewAsterisk(tableName)
	_files.ID = field.NewInt64(tableName, "id")
	_files.Name = field.NewString(tableName, "name")
	_files.FileSize = field.NewInt64(tableName, "file_size")
	_files.TosURI = field.NewString(tableName, "tos_uri")
	_files.Status = field.NewInt32(tableName, "status")
	_files.Comment = field.NewString(tableName, "comment")
	_files.Source = field.NewInt32(tableName, "source")
	_files.CreatorID = field.NewString(tableName, "creator_id")
	_files.ContentType = field.NewString(tableName, "content_type")
	_files.CozeAccountID = field.NewInt64(tableName, "coze_account_id")
	_files.CreatedAt = field.NewInt64(tableName, "created_at")
	_files.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_files.DeletedAt = field.NewField(tableName, "deleted_at")

	_files.fillFieldMap()

	return _files
}

// files file resource table
type files struct {
	filesDo

	ALL           field.Asterisk
	ID            field.Int64  // id
	Name          field.String // file name
	FileSize      field.Int64  // file size
	TosURI        field.String // TOS URI
	Status        field.Int32  // status，0invalid，1valid
	Comment       field.String // file comment
	Source        field.Int32  // source：1 from API,
	CreatorID     field.String // creator id
	ContentType   field.String // content type
	CozeAccountID field.Int64  // coze account id
	CreatedAt     field.Int64  // Create Time in Milliseconds
	UpdatedAt     field.Int64  // Update Time in Milliseconds
	DeletedAt     field.Field  // Delete Time

	fieldMap map[string]field.Expr
}

func (f files) Table(newTableName string) *files {
	f.filesDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f files) As(alias string) *files {
	f.filesDo.DO = *(f.filesDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *files) updateTableName(table string) *files {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.Name = field.NewString(table, "name")
	f.FileSize = field.NewInt64(table, "file_size")
	f.TosURI = field.NewString(table, "tos_uri")
	f.Status = field.NewInt32(table, "status")
	f.Comment = field.NewString(table, "comment")
	f.Source = field.NewInt32(table, "source")
	f.CreatorID = field.NewString(table, "creator_id")
	f.ContentType = field.NewString(table, "content_type")
	f.CozeAccountID = field.NewInt64(table, "coze_account_id")
	f.CreatedAt = field.NewInt64(table, "created_at")
	f.UpdatedAt = field.NewInt64(table, "updated_at")
	f.DeletedAt = field.NewField(table, "deleted_at")

	f.fillFieldMap()

	return f
}

func (f *files) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *files) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 13)
	f.fieldMap["id"] = f.ID
	f.fieldMap["name"] = f.Name
	f.fieldMap["file_size"] = f.FileSize
	f.fieldMap["tos_uri"] = f.TosURI
	f.fieldMap["status"] = f.Status
	f.fieldMap["comment"] = f.Comment
	f.fieldMap["source"] = f.Source
	f.fieldMap["creator_id"] = f.CreatorID
	f.fieldMap["content_type"] = f.ContentType
	f.fieldMap["coze_account_id"] = f.CozeAccountID
	f.fieldMap["created_at"] = f.CreatedAt
	f.fieldMap["updated_at"] = f.UpdatedAt
	f.fieldMap["deleted_at"] = f.DeletedAt
}

func (f files) clone(db *gorm.DB) files {
	f.filesDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f files) replaceDB(db *gorm.DB) files {
	f.filesDo.ReplaceDB(db)
	return f
}

type filesDo struct{ gen.DO }

type IFilesDo interface {
	gen.SubQuery
	Debug() IFilesDo
	WithContext(ctx context.Context) IFilesDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IFilesDo
	WriteDB() IFilesDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IFilesDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IFilesDo
	Not(conds ...gen.Condition) IFilesDo
	Or(conds ...gen.Condition) IFilesDo
	Select(conds ...field.Expr) IFilesDo
	Where(conds ...gen.Condition) IFilesDo
	Order(conds ...field.Expr) IFilesDo
	Distinct(cols ...field.Expr) IFilesDo
	Omit(cols ...field.Expr) IFilesDo
	Join(table schema.Tabler, on ...field.Expr) IFilesDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IFilesDo
	RightJoin(table schema.Tabler, on ...field.Expr) IFilesDo
	Group(cols ...field.Expr) IFilesDo
	Having(conds ...gen.Condition) IFilesDo
	Limit(limit int) IFilesDo
	Offset(offset int) IFilesDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IFilesDo
	Unscoped() IFilesDo
	Create(values ...*model.Files) error
	CreateInBatches(values []*model.Files, batchSize int) error
	Save(values ...*model.Files) error
	First() (*model.Files, error)
	Take() (*model.Files, error)
	Last() (*model.Files, error)
	Find() ([]*model.Files, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Files, err error)
	FindInBatches(result *[]*model.Files, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Files) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IFilesDo
	Assign(attrs ...field.AssignExpr) IFilesDo
	Joins(fields ...field.RelationField) IFilesDo
	Preload(fields ...field.RelationField) IFilesDo
	FirstOrInit() (*model.Files, error)
	FirstOrCreate() (*model.Files, error)
	FindByPage(offset int, limit int) (result []*model.Files, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IFilesDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (f filesDo) Debug() IFilesDo {
	return f.withDO(f.DO.Debug())
}

func (f filesDo) WithContext(ctx context.Context) IFilesDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f filesDo) ReadDB() IFilesDo {
	return f.Clauses(dbresolver.Read)
}

func (f filesDo) WriteDB() IFilesDo {
	return f.Clauses(dbresolver.Write)
}

func (f filesDo) Session(config *gorm.Session) IFilesDo {
	return f.withDO(f.DO.Session(config))
}

func (f filesDo) Clauses(conds ...clause.Expression) IFilesDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f filesDo) Returning(value interface{}, columns ...string) IFilesDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f filesDo) Not(conds ...gen.Condition) IFilesDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f filesDo) Or(conds ...gen.Condition) IFilesDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f filesDo) Select(conds ...field.Expr) IFilesDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f filesDo) Where(conds ...gen.Condition) IFilesDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f filesDo) Order(conds ...field.Expr) IFilesDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f filesDo) Distinct(cols ...field.Expr) IFilesDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f filesDo) Omit(cols ...field.Expr) IFilesDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f filesDo) Join(table schema.Tabler, on ...field.Expr) IFilesDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f filesDo) LeftJoin(table schema.Tabler, on ...field.Expr) IFilesDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f filesDo) RightJoin(table schema.Tabler, on ...field.Expr) IFilesDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f filesDo) Group(cols ...field.Expr) IFilesDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f filesDo) Having(conds ...gen.Condition) IFilesDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f filesDo) Limit(limit int) IFilesDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f filesDo) Offset(offset int) IFilesDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f filesDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IFilesDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f filesDo) Unscoped() IFilesDo {
	return f.withDO(f.DO.Unscoped())
}

func (f filesDo) Create(values ...*model.Files) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f filesDo) CreateInBatches(values []*model.Files, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f filesDo) Save(values ...*model.Files) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f filesDo) First() (*model.Files, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Files), nil
	}
}

func (f filesDo) Take() (*model.Files, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Files), nil
	}
}

func (f filesDo) Last() (*model.Files, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Files), nil
	}
}

func (f filesDo) Find() ([]*model.Files, error) {
	result, err := f.DO.Find()
	return result.([]*model.Files), err
}

func (f filesDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Files, err error) {
	buf := make([]*model.Files, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f filesDo) FindInBatches(result *[]*model.Files, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f filesDo) Attrs(attrs ...field.AssignExpr) IFilesDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f filesDo) Assign(attrs ...field.AssignExpr) IFilesDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f filesDo) Joins(fields ...field.RelationField) IFilesDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f filesDo) Preload(fields ...field.RelationField) IFilesDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f filesDo) FirstOrInit() (*model.Files, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Files), nil
	}
}

func (f filesDo) FirstOrCreate() (*model.Files, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Files), nil
	}
}

func (f filesDo) FindByPage(offset int, limit int) (result []*model.Files, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f filesDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f filesDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f filesDo) Delete(models ...*model.Files) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *filesDo) withDO(do gen.Dao) *filesDo {
	f.DO = *do.(*gen.DO)
	return f
}
