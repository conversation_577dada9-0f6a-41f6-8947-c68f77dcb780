// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/user/internal/dal/model"
)

func newSpace(db *gorm.DB, opts ...gen.DOOption) space {
	_space := space{}

	_space.spaceDo.UseDB(db, opts...)
	_space.spaceDo.UseModel(&model.Space{})

	tableName := _space.spaceDo.TableName()
	_space.ALL = field.NewAsterisk(tableName)
	_space.ID = field.NewInt64(tableName, "id")
	_space.OwnerID = field.NewInt64(tableName, "owner_id")
	_space.Name = field.NewString(tableName, "name")
	_space.Description = field.NewString(tableName, "description")
	_space.IconURI = field.NewString(tableName, "icon_uri")
	_space.CreatorID = field.NewInt64(tableName, "creator_id")
	_space.CreatedAt = field.NewInt64(tableName, "created_at")
	_space.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_space.DeletedAt = field.NewField(tableName, "deleted_at")

	_space.fillFieldMap()

	return _space
}

// space Space Table
type space struct {
	spaceDo

	ALL         field.Asterisk
	ID          field.Int64  // Primary Key ID, Space ID
	OwnerID     field.Int64  // Owner ID
	Name        field.String // Space Name
	Description field.String // Space Description
	IconURI     field.String // Icon URI
	CreatorID   field.Int64  // Creator ID
	CreatedAt   field.Int64  // Creation Time (Milliseconds)
	UpdatedAt   field.Int64  // Update Time (Milliseconds)
	DeletedAt   field.Field  // Deletion Time (Milliseconds)

	fieldMap map[string]field.Expr
}

func (s space) Table(newTableName string) *space {
	s.spaceDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s space) As(alias string) *space {
	s.spaceDo.DO = *(s.spaceDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *space) updateTableName(table string) *space {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.OwnerID = field.NewInt64(table, "owner_id")
	s.Name = field.NewString(table, "name")
	s.Description = field.NewString(table, "description")
	s.IconURI = field.NewString(table, "icon_uri")
	s.CreatorID = field.NewInt64(table, "creator_id")
	s.CreatedAt = field.NewInt64(table, "created_at")
	s.UpdatedAt = field.NewInt64(table, "updated_at")
	s.DeletedAt = field.NewField(table, "deleted_at")

	s.fillFieldMap()

	return s
}

func (s *space) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *space) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 9)
	s.fieldMap["id"] = s.ID
	s.fieldMap["owner_id"] = s.OwnerID
	s.fieldMap["name"] = s.Name
	s.fieldMap["description"] = s.Description
	s.fieldMap["icon_uri"] = s.IconURI
	s.fieldMap["creator_id"] = s.CreatorID
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["deleted_at"] = s.DeletedAt
}

func (s space) clone(db *gorm.DB) space {
	s.spaceDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s space) replaceDB(db *gorm.DB) space {
	s.spaceDo.ReplaceDB(db)
	return s
}

type spaceDo struct{ gen.DO }

type ISpaceDo interface {
	gen.SubQuery
	Debug() ISpaceDo
	WithContext(ctx context.Context) ISpaceDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISpaceDo
	WriteDB() ISpaceDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISpaceDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISpaceDo
	Not(conds ...gen.Condition) ISpaceDo
	Or(conds ...gen.Condition) ISpaceDo
	Select(conds ...field.Expr) ISpaceDo
	Where(conds ...gen.Condition) ISpaceDo
	Order(conds ...field.Expr) ISpaceDo
	Distinct(cols ...field.Expr) ISpaceDo
	Omit(cols ...field.Expr) ISpaceDo
	Join(table schema.Tabler, on ...field.Expr) ISpaceDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISpaceDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISpaceDo
	Group(cols ...field.Expr) ISpaceDo
	Having(conds ...gen.Condition) ISpaceDo
	Limit(limit int) ISpaceDo
	Offset(offset int) ISpaceDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISpaceDo
	Unscoped() ISpaceDo
	Create(values ...*model.Space) error
	CreateInBatches(values []*model.Space, batchSize int) error
	Save(values ...*model.Space) error
	First() (*model.Space, error)
	Take() (*model.Space, error)
	Last() (*model.Space, error)
	Find() ([]*model.Space, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Space, err error)
	FindInBatches(result *[]*model.Space, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Space) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISpaceDo
	Assign(attrs ...field.AssignExpr) ISpaceDo
	Joins(fields ...field.RelationField) ISpaceDo
	Preload(fields ...field.RelationField) ISpaceDo
	FirstOrInit() (*model.Space, error)
	FirstOrCreate() (*model.Space, error)
	FindByPage(offset int, limit int) (result []*model.Space, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISpaceDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s spaceDo) Debug() ISpaceDo {
	return s.withDO(s.DO.Debug())
}

func (s spaceDo) WithContext(ctx context.Context) ISpaceDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s spaceDo) ReadDB() ISpaceDo {
	return s.Clauses(dbresolver.Read)
}

func (s spaceDo) WriteDB() ISpaceDo {
	return s.Clauses(dbresolver.Write)
}

func (s spaceDo) Session(config *gorm.Session) ISpaceDo {
	return s.withDO(s.DO.Session(config))
}

func (s spaceDo) Clauses(conds ...clause.Expression) ISpaceDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s spaceDo) Returning(value interface{}, columns ...string) ISpaceDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s spaceDo) Not(conds ...gen.Condition) ISpaceDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s spaceDo) Or(conds ...gen.Condition) ISpaceDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s spaceDo) Select(conds ...field.Expr) ISpaceDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s spaceDo) Where(conds ...gen.Condition) ISpaceDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s spaceDo) Order(conds ...field.Expr) ISpaceDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s spaceDo) Distinct(cols ...field.Expr) ISpaceDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s spaceDo) Omit(cols ...field.Expr) ISpaceDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s spaceDo) Join(table schema.Tabler, on ...field.Expr) ISpaceDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s spaceDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISpaceDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s spaceDo) RightJoin(table schema.Tabler, on ...field.Expr) ISpaceDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s spaceDo) Group(cols ...field.Expr) ISpaceDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s spaceDo) Having(conds ...gen.Condition) ISpaceDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s spaceDo) Limit(limit int) ISpaceDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s spaceDo) Offset(offset int) ISpaceDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s spaceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISpaceDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s spaceDo) Unscoped() ISpaceDo {
	return s.withDO(s.DO.Unscoped())
}

func (s spaceDo) Create(values ...*model.Space) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s spaceDo) CreateInBatches(values []*model.Space, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s spaceDo) Save(values ...*model.Space) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s spaceDo) First() (*model.Space, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Space), nil
	}
}

func (s spaceDo) Take() (*model.Space, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Space), nil
	}
}

func (s spaceDo) Last() (*model.Space, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Space), nil
	}
}

func (s spaceDo) Find() ([]*model.Space, error) {
	result, err := s.DO.Find()
	return result.([]*model.Space), err
}

func (s spaceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Space, err error) {
	buf := make([]*model.Space, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s spaceDo) FindInBatches(result *[]*model.Space, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s spaceDo) Attrs(attrs ...field.AssignExpr) ISpaceDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s spaceDo) Assign(attrs ...field.AssignExpr) ISpaceDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s spaceDo) Joins(fields ...field.RelationField) ISpaceDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s spaceDo) Preload(fields ...field.RelationField) ISpaceDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s spaceDo) FirstOrInit() (*model.Space, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Space), nil
	}
}

func (s spaceDo) FirstOrCreate() (*model.Space, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Space), nil
	}
}

func (s spaceDo) FindByPage(offset int, limit int) (result []*model.Space, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s spaceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s spaceDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s spaceDo) Delete(models ...*model.Space) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *spaceDo) withDO(do gen.Dao) *spaceDo {
	s.DO = *do.(*gen.DO)
	return s
}
