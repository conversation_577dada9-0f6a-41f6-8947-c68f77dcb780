// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameVariableInstance = "variable_instance"

// VariableInstance KV Memory
type VariableInstance struct {
	ID           int64  `gorm:"column:id;primaryKey;comment:id" json:"id"`                                                             // id
	BizType      int32  `gorm:"column:biz_type;not null;comment:1 for agent，2 for app" json:"biz_type"`                                // 1 for agent，2 for app
	BizID        string `gorm:"column:biz_id;not null;comment:1 for agent_id，2 for app_id" json:"biz_id"`                              // 1 for agent_id，2 for app_id
	Version      string `gorm:"column:version;not null;comment:agent or project version empty represents draft status" json:"version"` // agent or project version empty represents draft status
	Keyword      string `gorm:"column:keyword;not null;comment:Keyword to Memory" json:"keyword"`                                      // Keyword to Memory
	Type         int32  `gorm:"column:type;not null;comment:Memory type 1 KV 2 list" json:"type"`                                      // Memory type 1 KV 2 list
	Content      string `gorm:"column:content;comment:content" json:"content"`                                                         // content
	ConnectorUID string `gorm:"column:connector_uid;not null;comment:connector_uid" json:"connector_uid"`                              // connector_uid
	ConnectorID  int64  `gorm:"column:connector_id;not null;comment:connector_id, e.g. coze = 10000010" json:"connector_id"`           // connector_id, e.g. coze = 10000010
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"` // Update Time in Milliseconds
}

// TableName VariableInstance's table name
func (*VariableInstance) TableName() string {
	return TableNameVariableInstance
}
