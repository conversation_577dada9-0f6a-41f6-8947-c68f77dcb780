// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/memory/variables/internal/dal/model"
)

func newVariablesMeta(db *gorm.DB, opts ...gen.DOOption) variablesMeta {
	_variablesMeta := variablesMeta{}

	_variablesMeta.variablesMetaDo.UseDB(db, opts...)
	_variablesMeta.variablesMetaDo.UseModel(&model.VariablesMeta{})

	tableName := _variablesMeta.variablesMetaDo.TableName()
	_variablesMeta.ALL = field.NewAsterisk(tableName)
	_variablesMeta.ID = field.NewInt64(tableName, "id")
	_variablesMeta.CreatorID = field.NewInt64(tableName, "creator_id")
	_variablesMeta.BizType = field.NewInt32(tableName, "biz_type")
	_variablesMeta.BizID = field.NewString(tableName, "biz_id")
	_variablesMeta.VariableList = field.NewField(tableName, "variable_list")
	_variablesMeta.CreatedAt = field.NewInt64(tableName, "created_at")
	_variablesMeta.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_variablesMeta.Version = field.NewString(tableName, "version")

	_variablesMeta.fillFieldMap()

	return _variablesMeta
}

// variablesMeta KV Memory meta
type variablesMeta struct {
	variablesMetaDo

	ALL          field.Asterisk
	ID           field.Int64  // id
	CreatorID    field.Int64  // creator id
	BizType      field.Int32  // 1 for agent，2 for app
	BizID        field.String // 1 for agent_id，2 for app_id
	VariableList field.Field  // JSON data for variable configuration
	CreatedAt    field.Int64  // Create Time in Milliseconds
	UpdatedAt    field.Int64  // Update Time in Milliseconds
	Version      field.String // Project version, empty represents draft status

	fieldMap map[string]field.Expr
}

func (v variablesMeta) Table(newTableName string) *variablesMeta {
	v.variablesMetaDo.UseTable(newTableName)
	return v.updateTableName(newTableName)
}

func (v variablesMeta) As(alias string) *variablesMeta {
	v.variablesMetaDo.DO = *(v.variablesMetaDo.As(alias).(*gen.DO))
	return v.updateTableName(alias)
}

func (v *variablesMeta) updateTableName(table string) *variablesMeta {
	v.ALL = field.NewAsterisk(table)
	v.ID = field.NewInt64(table, "id")
	v.CreatorID = field.NewInt64(table, "creator_id")
	v.BizType = field.NewInt32(table, "biz_type")
	v.BizID = field.NewString(table, "biz_id")
	v.VariableList = field.NewField(table, "variable_list")
	v.CreatedAt = field.NewInt64(table, "created_at")
	v.UpdatedAt = field.NewInt64(table, "updated_at")
	v.Version = field.NewString(table, "version")

	v.fillFieldMap()

	return v
}

func (v *variablesMeta) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := v.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (v *variablesMeta) fillFieldMap() {
	v.fieldMap = make(map[string]field.Expr, 8)
	v.fieldMap["id"] = v.ID
	v.fieldMap["creator_id"] = v.CreatorID
	v.fieldMap["biz_type"] = v.BizType
	v.fieldMap["biz_id"] = v.BizID
	v.fieldMap["variable_list"] = v.VariableList
	v.fieldMap["created_at"] = v.CreatedAt
	v.fieldMap["updated_at"] = v.UpdatedAt
	v.fieldMap["version"] = v.Version
}

func (v variablesMeta) clone(db *gorm.DB) variablesMeta {
	v.variablesMetaDo.ReplaceConnPool(db.Statement.ConnPool)
	return v
}

func (v variablesMeta) replaceDB(db *gorm.DB) variablesMeta {
	v.variablesMetaDo.ReplaceDB(db)
	return v
}

type variablesMetaDo struct{ gen.DO }

type IVariablesMetaDo interface {
	gen.SubQuery
	Debug() IVariablesMetaDo
	WithContext(ctx context.Context) IVariablesMetaDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IVariablesMetaDo
	WriteDB() IVariablesMetaDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IVariablesMetaDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IVariablesMetaDo
	Not(conds ...gen.Condition) IVariablesMetaDo
	Or(conds ...gen.Condition) IVariablesMetaDo
	Select(conds ...field.Expr) IVariablesMetaDo
	Where(conds ...gen.Condition) IVariablesMetaDo
	Order(conds ...field.Expr) IVariablesMetaDo
	Distinct(cols ...field.Expr) IVariablesMetaDo
	Omit(cols ...field.Expr) IVariablesMetaDo
	Join(table schema.Tabler, on ...field.Expr) IVariablesMetaDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IVariablesMetaDo
	RightJoin(table schema.Tabler, on ...field.Expr) IVariablesMetaDo
	Group(cols ...field.Expr) IVariablesMetaDo
	Having(conds ...gen.Condition) IVariablesMetaDo
	Limit(limit int) IVariablesMetaDo
	Offset(offset int) IVariablesMetaDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IVariablesMetaDo
	Unscoped() IVariablesMetaDo
	Create(values ...*model.VariablesMeta) error
	CreateInBatches(values []*model.VariablesMeta, batchSize int) error
	Save(values ...*model.VariablesMeta) error
	First() (*model.VariablesMeta, error)
	Take() (*model.VariablesMeta, error)
	Last() (*model.VariablesMeta, error)
	Find() ([]*model.VariablesMeta, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.VariablesMeta, err error)
	FindInBatches(result *[]*model.VariablesMeta, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.VariablesMeta) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IVariablesMetaDo
	Assign(attrs ...field.AssignExpr) IVariablesMetaDo
	Joins(fields ...field.RelationField) IVariablesMetaDo
	Preload(fields ...field.RelationField) IVariablesMetaDo
	FirstOrInit() (*model.VariablesMeta, error)
	FirstOrCreate() (*model.VariablesMeta, error)
	FindByPage(offset int, limit int) (result []*model.VariablesMeta, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IVariablesMetaDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (v variablesMetaDo) Debug() IVariablesMetaDo {
	return v.withDO(v.DO.Debug())
}

func (v variablesMetaDo) WithContext(ctx context.Context) IVariablesMetaDo {
	return v.withDO(v.DO.WithContext(ctx))
}

func (v variablesMetaDo) ReadDB() IVariablesMetaDo {
	return v.Clauses(dbresolver.Read)
}

func (v variablesMetaDo) WriteDB() IVariablesMetaDo {
	return v.Clauses(dbresolver.Write)
}

func (v variablesMetaDo) Session(config *gorm.Session) IVariablesMetaDo {
	return v.withDO(v.DO.Session(config))
}

func (v variablesMetaDo) Clauses(conds ...clause.Expression) IVariablesMetaDo {
	return v.withDO(v.DO.Clauses(conds...))
}

func (v variablesMetaDo) Returning(value interface{}, columns ...string) IVariablesMetaDo {
	return v.withDO(v.DO.Returning(value, columns...))
}

func (v variablesMetaDo) Not(conds ...gen.Condition) IVariablesMetaDo {
	return v.withDO(v.DO.Not(conds...))
}

func (v variablesMetaDo) Or(conds ...gen.Condition) IVariablesMetaDo {
	return v.withDO(v.DO.Or(conds...))
}

func (v variablesMetaDo) Select(conds ...field.Expr) IVariablesMetaDo {
	return v.withDO(v.DO.Select(conds...))
}

func (v variablesMetaDo) Where(conds ...gen.Condition) IVariablesMetaDo {
	return v.withDO(v.DO.Where(conds...))
}

func (v variablesMetaDo) Order(conds ...field.Expr) IVariablesMetaDo {
	return v.withDO(v.DO.Order(conds...))
}

func (v variablesMetaDo) Distinct(cols ...field.Expr) IVariablesMetaDo {
	return v.withDO(v.DO.Distinct(cols...))
}

func (v variablesMetaDo) Omit(cols ...field.Expr) IVariablesMetaDo {
	return v.withDO(v.DO.Omit(cols...))
}

func (v variablesMetaDo) Join(table schema.Tabler, on ...field.Expr) IVariablesMetaDo {
	return v.withDO(v.DO.Join(table, on...))
}

func (v variablesMetaDo) LeftJoin(table schema.Tabler, on ...field.Expr) IVariablesMetaDo {
	return v.withDO(v.DO.LeftJoin(table, on...))
}

func (v variablesMetaDo) RightJoin(table schema.Tabler, on ...field.Expr) IVariablesMetaDo {
	return v.withDO(v.DO.RightJoin(table, on...))
}

func (v variablesMetaDo) Group(cols ...field.Expr) IVariablesMetaDo {
	return v.withDO(v.DO.Group(cols...))
}

func (v variablesMetaDo) Having(conds ...gen.Condition) IVariablesMetaDo {
	return v.withDO(v.DO.Having(conds...))
}

func (v variablesMetaDo) Limit(limit int) IVariablesMetaDo {
	return v.withDO(v.DO.Limit(limit))
}

func (v variablesMetaDo) Offset(offset int) IVariablesMetaDo {
	return v.withDO(v.DO.Offset(offset))
}

func (v variablesMetaDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IVariablesMetaDo {
	return v.withDO(v.DO.Scopes(funcs...))
}

func (v variablesMetaDo) Unscoped() IVariablesMetaDo {
	return v.withDO(v.DO.Unscoped())
}

func (v variablesMetaDo) Create(values ...*model.VariablesMeta) error {
	if len(values) == 0 {
		return nil
	}
	return v.DO.Create(values)
}

func (v variablesMetaDo) CreateInBatches(values []*model.VariablesMeta, batchSize int) error {
	return v.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (v variablesMetaDo) Save(values ...*model.VariablesMeta) error {
	if len(values) == 0 {
		return nil
	}
	return v.DO.Save(values)
}

func (v variablesMetaDo) First() (*model.VariablesMeta, error) {
	if result, err := v.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.VariablesMeta), nil
	}
}

func (v variablesMetaDo) Take() (*model.VariablesMeta, error) {
	if result, err := v.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.VariablesMeta), nil
	}
}

func (v variablesMetaDo) Last() (*model.VariablesMeta, error) {
	if result, err := v.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.VariablesMeta), nil
	}
}

func (v variablesMetaDo) Find() ([]*model.VariablesMeta, error) {
	result, err := v.DO.Find()
	return result.([]*model.VariablesMeta), err
}

func (v variablesMetaDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.VariablesMeta, err error) {
	buf := make([]*model.VariablesMeta, 0, batchSize)
	err = v.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (v variablesMetaDo) FindInBatches(result *[]*model.VariablesMeta, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return v.DO.FindInBatches(result, batchSize, fc)
}

func (v variablesMetaDo) Attrs(attrs ...field.AssignExpr) IVariablesMetaDo {
	return v.withDO(v.DO.Attrs(attrs...))
}

func (v variablesMetaDo) Assign(attrs ...field.AssignExpr) IVariablesMetaDo {
	return v.withDO(v.DO.Assign(attrs...))
}

func (v variablesMetaDo) Joins(fields ...field.RelationField) IVariablesMetaDo {
	for _, _f := range fields {
		v = *v.withDO(v.DO.Joins(_f))
	}
	return &v
}

func (v variablesMetaDo) Preload(fields ...field.RelationField) IVariablesMetaDo {
	for _, _f := range fields {
		v = *v.withDO(v.DO.Preload(_f))
	}
	return &v
}

func (v variablesMetaDo) FirstOrInit() (*model.VariablesMeta, error) {
	if result, err := v.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.VariablesMeta), nil
	}
}

func (v variablesMetaDo) FirstOrCreate() (*model.VariablesMeta, error) {
	if result, err := v.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.VariablesMeta), nil
	}
}

func (v variablesMetaDo) FindByPage(offset int, limit int) (result []*model.VariablesMeta, count int64, err error) {
	result, err = v.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = v.Offset(-1).Limit(-1).Count()
	return
}

func (v variablesMetaDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = v.Count()
	if err != nil {
		return
	}

	err = v.Offset(offset).Limit(limit).Scan(result)
	return
}

func (v variablesMetaDo) Scan(result interface{}) (err error) {
	return v.DO.Scan(result)
}

func (v variablesMetaDo) Delete(models ...*model.VariablesMeta) (result gen.ResultInfo, err error) {
	return v.DO.Delete(models)
}

func (v *variablesMetaDo) withDO(do gen.Dao) *variablesMetaDo {
	v.DO = *do.(*gen.DO)
	return v
}
