{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"baseUrl": "./", "paths": {}, "jsx": "react", "isolatedModules": true, "strictNullChecks": true, "strictPropertyInitialization": false, "types": ["react", "react-dom"], "rootDir": "./src", "outDir": "./lib-ts", "tsBuildInfoFile": "./lib-ts/tsconfig.build.tsbuildinfo"}, "include": ["./src", "./src/**/*.json"], "references": [{"path": "../../common/flowgram-adapter/common/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../core/tsconfig.build.json"}, {"path": "../view/tsconfig.build.json"}]}