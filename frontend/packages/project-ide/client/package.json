{"name": "@coze-project-ide/client", "version": "0.0.1", "author": "<EMAIL>", "main": "./src/index.ts", "scripts": {"build": "exit 0", "build:fast": "tsup src/index.ts --format cjs,esm --sourcemap --legacy-output", "build:watch": "npm run build:fast -- --dts-resolve", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint ./ --cache --quiet", "ts-check": "tsc --noEmit", "watch": "npm run build:fast -- --dts-resolve --watch --ignore-watch dist"}, "dependencies": {"@coze-project-ide/core": "workspace:*", "@coze-project-ide/view": "workspace:*", "@flowgram-adapter/common": "workspace:*", "inversify": "^6.0.1"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@testing-library/react": "^14.1.2", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "eslint": "~9.12.0", "jsdom": "^22.1.0", "reflect-metadata": "^0.1.13", "tsup": "^8.0.1", "typescript": "~5.8.2", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=17", "react-dom": ">=17"}}