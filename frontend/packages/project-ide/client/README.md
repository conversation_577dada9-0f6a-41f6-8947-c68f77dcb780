# @coze-project-ide/client

A api & networking package for the Coze Studio monorepo

## Overview

This package is part of the Coze Studio monorepo and provides api & networking functionality. It includes component, store, service and more.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-project-ide/client": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-project-ide/client';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Component
- Store
- Service
- Manager

## API Reference

### Exports

- `*`
- `type CustomTitleType,
  type ViewOptionRegisterService,
  type CustomPreferenceConfig,
  type CustomTitleChanged,
  LayoutPanelType,
  ToolbarAlign,
  ReactWidget,
  ViewManager,
  WidgetFactory,
  WidgetManager,
  CurrentResourceContext,
  ReactWidgetContext,
  ViewContribution,
  useCurrentWidget,
  useCurrentWidgetFromArea,
  useCurrentResource,
  Widget,
  StatefulWidget,
  ApplicationShell,
  LayoutRestorer,
  CustomPreferenceContribution,
  ViewService,
  FlowDockPanel,
  HoverService,
  MenuService,
  DebugService,
  DEBUG_BAR_DRAGGABLE,
  SplitWidget,
  BoxLayout,
  DockLayout,
  BoxPanel,
  SplitLayout,
  SplitPanel,
  createBoxLayout,
  createSplitLayout,
  PerfectScrollbar,
  DISABLE_HANDLE_EVENT,
  TabBarToolbar,
  ACTIVITY_BAR_CONTENT,
  ViewRenderer,
  DragService,
  CustomTabBar,
  TabBar,
  type DragPropsType,
  type PresetConfigType,
  type ToolbarItem,`
- `createDefaultPreset`
- `type IDEClientOptions, IDEClientContext`
- `*`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript

- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
