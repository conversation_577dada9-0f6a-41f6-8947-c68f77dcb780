{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "include": ["tailwind.config.ts"], "exclude": ["**/node_modules", "./dist"], "references": [{"path": "./tsconfig.build.json"}], "compilerOptions": {"baseUrl": "./", "types": ["react", "react-dom"], "jsx": "react", "isolatedModules": true, "strictNullChecks": true, "strictPropertyInitialization": false, "paths": {"@/*": ["./src/*"]}, "rootDir": "./", "outDir": "./dist"}}