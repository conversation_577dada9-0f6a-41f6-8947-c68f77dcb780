/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { useState } from 'react';

import { workflowApi } from '@coze-arch/bot-api';
import { useIDEGlobalStore } from '@coze-project-ide/framework';

export const useUpdateChat = ({
  manualRefresh,
}: {
  manualRefresh: () => void;
}) => {
  const { spaceId, projectId } = useIDEGlobalStore(store => ({
    spaceId: store.spaceId,
    projectId: store.projectId,
  }));
  const [loading, setLoading] = useState(false);
  const handleUpdateChat = async (
    uniqueId: string,
    conversationName: string,
  ) => {
    try {
      setLoading(true);
      await workflowApi.UpdateProjectConversationDef({
        space_id: spaceId,
        project_id: projectId,
        unique_id: uniqueId,
        conversation_name: conversationName,
      });
      manualRefresh();
    } finally {
      setLoading(false);
    }
  };

  return { loading, handleUpdateChat };
};
