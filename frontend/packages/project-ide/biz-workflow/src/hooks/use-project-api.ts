/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { useMemoizedFn } from 'ahooks';
import { type ProjectApi } from '@coze-workflow/playground';
import {
  useSendMessageEvent,
  useIDENavigate,
  useCurrentWidgetContext,
  useIDEGlobalContext,
} from '@coze-project-ide/framework';

/**
 * The ability to inject project APIs into workflow.
 * Note: non-responsive
 */
export const useProjectApi = () => {
  const { sendOpen } = useSendMessageEvent();
  const { widget: uiWidget } = useCurrentWidgetContext();
  const navigate = useIDENavigate();
  const ideGlobalContext = useIDEGlobalContext();

  const getProjectAPI = useMemoizedFn(() => {
    const api: ProjectApi = {
      navigate,
      ideGlobalStore: ideGlobalContext,
      setWidgetUIState: (status: string) => uiWidget.setUIState(status as any),
      sendMsgOpenWidget: sendOpen,
    };
    return api;
  });

  return getProjectAPI;
};
