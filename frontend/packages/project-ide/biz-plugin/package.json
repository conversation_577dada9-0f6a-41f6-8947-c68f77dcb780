{"name": "@coze-project-ide/biz-plugin", "version": "0.0.1", "author": "<EMAIL>", "exports": {".": "./src/index.tsx", "./main": "./src/main.tsx", "./types": "./src/types.ts"}, "main": "./src/index.tsx", "typesVersions": {"*": {"main": ["./src/main.tsx"], "types": ["./src/types.ts"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache --quiet"}, "dependencies": {"@coze-agent-ide/bot-plugin": "workspace:*", "@coze-agent-ide/bot-plugin-export": "workspace:*", "@coze-agent-ide/plugin-shared": "workspace:*", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-project-ide/biz-components": "workspace:*", "@coze-project-ide/framework": "workspace:*", "@coze-studio/bot-plugin-store": "workspace:*", "@coze-workflow/base": "workspace:*", "@coze-workflow/components": "workspace:*", "lodash-es": "^4.17.21", "qs": "^6.11.2", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@types/qs": "^6.9.7", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "react": "~18.2.0", "react-dom": "~18.2.0", "typescript": "~5.8.2"}}