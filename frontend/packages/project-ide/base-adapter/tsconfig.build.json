{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"baseUrl": "./", "types": ["react"], "jsx": "react", "isolatedModules": true, "strictNullChecks": true, "strictPropertyInitialization": false, "rootDir": "./src", "outDir": "./lib-ts", "tsBuildInfoFile": "./lib-ts/tsconfig.build.tsbuildinfo"}, "include": ["./src", "./src/**/*.json"], "exclude": ["node_modules", "dist"], "references": [{"path": "../../arch/bot-typings/tsconfig.build.json"}, {"path": "../base-interface/tsconfig.build.json"}, {"path": "../client/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}]}