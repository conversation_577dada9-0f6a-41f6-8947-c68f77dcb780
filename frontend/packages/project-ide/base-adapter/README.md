# @coze-project-ide/base-adapter

Adapter of coze ProjectIDE

## Overview

This package is part of the Coze Studio monorepo and provides ide features functionality. It includes hook, store, service.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-project-ide/base-adapter": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-project-ide/base-adapter';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Hook
- Store
- Service

## API Reference

### Exports

- `useCommitVersion`
- `IDEGlobalProvider,
  useIDEGlobalContext,
  useIDEGlobalStore,`
- `OptionsService, WsService`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
