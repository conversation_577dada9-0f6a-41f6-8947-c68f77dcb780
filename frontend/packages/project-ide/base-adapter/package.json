{"name": "@coze-project-ide/base-adapter", "version": "0.0.1", "description": "Adapter of coze ProjectIDE", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-project-ide/base-interface": "workspace:*", "@coze-project-ide/client": "workspace:*", "inversify": "^6.0.1", "react-router-dom": "^6.22.0"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@types/node": "^18", "@types/react": "18.2.37", "@vitest/coverage-v8": "~3.0.5", "sucrase": "^3.32.0", "vitest": "~3.0.5"}}