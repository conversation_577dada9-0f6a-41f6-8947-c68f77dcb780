# @coze-project-ide/ui-adapter

Adapter of coze ProjectIDE

## Overview

This package is part of the Coze Studio monorepo and provides ui component functionality. It includes component, hook, store.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-project-ide/ui-adapter": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-project-ide/ui-adapter';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Component
- Hook
- Store

## API Reference

### Exports

- `useCommitVersion`
- `IDEGlobalProvider,
  useIDEGlobalContext,
  useIDEGlobalStore,`
- `ModeTab,
  LeftContentButtons,
  SecondarySidebar,
  UIBuilder,`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- React
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
