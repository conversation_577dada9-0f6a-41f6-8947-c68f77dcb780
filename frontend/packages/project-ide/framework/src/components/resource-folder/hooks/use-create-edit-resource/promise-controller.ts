/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

class PromiseController<T, O> {
  private lastVersion: number;
  private callbacks: Array<(v: O) => void> = [];
  private mainFunction: (v: T) => Promise<O>;

  constructor() {
    this.lastVersion = 0;
  }

  registerPromiseFn(fn: (v: T) => Promise<O>) {
    this.mainFunction = fn;
    return this;
  }

  registerCallbackFb(cb: (v: O) => void) {
    this.callbacks.push(cb);
    return this;
  }

  async excute(v: T) {
    if (!this.mainFunction) {
      return;
    }
    this.lastVersion += 1;
    const currentVersion = this.lastVersion;
    const res = await this.mainFunction(v);
    if (this.lastVersion === currentVersion) {
      this.callbacks.forEach(cb => cb(res));
    }
  }

  dispose() {
    this.callbacks = [];
  }
}

export { PromiseController };
