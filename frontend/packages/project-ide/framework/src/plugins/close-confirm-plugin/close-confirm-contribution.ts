/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { inject, injectable } from 'inversify';
import {
  type CommandContribution,
  type CommandRegistry,
  type CustomTitleType,
  Command,
} from '@coze-project-ide/client';

import { ModalService, ModalType } from '@/services';

@injectable()
export class CloseConfirmContribution implements CommandContribution {
  @inject(ModalService) private modalService: ModalService;

  registerCommands(commands: CommandRegistry): void {
    commands.registerCommand(Command.Default.VIEW_SAVING_WIDGET_CLOSE_CONFIRM, {
      execute: (titles: CustomTitleType[]) => {
        const hasUnsaved = titles.some(title => title?.saving);
        if (hasUnsaved) {
          this.modalService.onModalVisibleChangeEmitter.fire({
            type: ModalType.CLOSE_CONFIRM,
            options: titles,
          });
        } else {
          titles.forEach(title => title?.owner?.close?.());
        }
      },
    });
  }
}
