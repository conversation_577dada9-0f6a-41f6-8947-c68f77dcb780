{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"composite": true, "baseUrl": "./", "paths": {}, "jsx": "react", "isolatedModules": true, "strictNullChecks": true, "strictPropertyInitialization": false, "rootDir": "./src", "outDir": "./lib-ts", "tsBuildInfoFile": "./lib-ts/tsconfig.build.tsbuildinfo", "types": ["@types/node"]}, "include": ["./src", "./src/**/*.json", "tsup.config.ts"], "references": [{"path": "../../common/flowgram-adapter/common/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../core/tsconfig.build.json"}]}