# @coze-project-ide/base-interface

Adapter of coze ProjectIDE

## Overview

This package is part of the Coze Studio monorepo and provides ide features functionality. It includes store.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-project-ide/base-interface": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-project-ide/base-interface';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Store

## API Reference

### Exports

- `IDEGlobalProvider,
  useIDEGlobalContext,
  useIDEGlobalStore,`
- `type WsMessageProps`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- React
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
