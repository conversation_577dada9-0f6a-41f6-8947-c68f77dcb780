/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Support for filtering query time ranges
 */
export const DATE_FILTERING_DAYS_NUMBER = 7;
export const FILTERING_OPTION_ALL = 'ALL';
/**
 * Query the number of pieces loaded per time
 */
export const FILTERING_LIMIT = 30;
export const TRACES_ADVANCE_INFO_TIME_BUFFER = 1000;
export const TIME_MINUTE = 60;
/**
 * Query Pull Default Offset
 */
export const INITIAL_OFFSET = '0';
export const EMPTY_TEXT = '-';
/**
 * Query Pull anti-shake time
 */
export const QUERY_FILTER_DEBOUNCE_TIME = 300;
/**
 * Debug station location information localStorage key
 */
export const DEBUG_PANEL_LAYOUT_KEY = 'coze_debug_panel_layout_config';
