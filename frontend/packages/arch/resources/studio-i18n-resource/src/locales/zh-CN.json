{"700012014": "检测到您的网络发出了异常流量，因此暂时无法使用扣子", "About_Plugins_tip": "关于插件", "Actions": "操作", "AddFailedToast": "Workflow 中所引用的插件请求失败，Workflow添加失败。", "AddSuccessToast": "“{name}”添加成功。", "Add_1": "添加", "Add_2": "添加", "Added": "已添加", "All": "全部", "Cancel": "取消", "Complete": "完成", "Confirm": "确认", "Content": "内容", "Copy": "复制", "Copy_link": "复制链接", "Copy_name": "复制名称", "Coze_token_reload": "重新加载", "Coze_token_title": "扣子 Token", "Create_failed": "创建失败", "Create_newtool_s1_dercribe": "工具描述", "Create_newtool_s1_dercribe_empty": "请输入工具描述", "Create_newtool_s1_dercribe_error": "请描述工具的主要功能和使用场景，确保内容含义清晰且符合平台规范。帮助用户/大模型更好地理解", "Create_newtool_s1_method": "请求方法", "Create_newtool_s1_method_delete": "Delete 方法", "Create_newtool_s1_method_get": "Get 方法", "Create_newtool_s1_method_post": "Post 方法", "Create_newtool_s1_method_put": "Put 方法", "Create_newtool_s1_name": "工具名称", "Create_newtool_s1_title": "填写基本信息", "Create_newtool_s1_title_empty": "请输入工具名称，确保名称含义清晰且符合平台规范", "Create_newtool_s1_title_error1": "只能包含字母、数字、下划线", "Create_newtool_s1_url": "工具路径", "Create_newtool_s1_url_empty": "输入工具具体路径，以/开头，如“/search”", "Create_newtool_s1_url_error1": "路径需要以 / 开头", "Create_newtool_s1_url_error2": "请输入工具具体路径", "Create_newtool_s2": "配置输入参数", "Create_newtool_s2_table_des": "参数描述", "Create_newtool_s2_table_des_tooltip": "请描述参数的功能，帮助用户/大模型更好的理解。", "Create_newtool_s2_table_method": "传入方法", "Create_newtool_s2_table_name_empty": "请输入参数名称，确保名称含义清晰且符合平台规范", "Create_newtool_s2_table_name_error1": "请输入参数名称", "Create_newtool_s2_table_name_error2": "只能包含字母、数字和下划线", "Create_newtool_s2_table_name_tooltip": "传入的参数名称，仅字母、数字或下划线有效。", "Create_newtool_s2_table_required": "是否必填", "Create_newtool_s2_title": "配置输入参数", "Create_newtool_s3_Outputparameters": "配置输出参数", "Create_newtool_s3_button_auto": "自动解析", "Create_newtool_s3_table_des_empty": "请输入参数描述", "Create_newtool_s3_table_des_tooltip": "指明返回内容的含义,帮助用户/大模型更好的理解", "Create_newtool_s3_table_name": "参数名称", "Create_newtool_s3_table_name_tooltip": "返回的参数名称，仅支持输入字母、数字或下划线", "Create_newtool_s3_table_new": "新增参数", "Create_newtool_s3_table_type": "参数类型", "Create_newtool_s4_debug": "调试与校验", "Create_newtool_s4_done": "完成", "Create_newtool_s4_name": "参数名称", "Create_newtool_s4_result": "调试结果", "Create_newtool_s4_run": "运行", "Create_newtool_s4_title": "输入参数", "Create_newtool_s4_type": "参数类型", "Create_newtool_s4_value": "参数值", "Create_success": "创建成功", "Create_time": "创建时间", "Create_tool_s1_method_patch_name": "Patch 方法", "Create_tool_s1_method_patch_tooltip_desp": "更新现有资源，但不创建新资源。例子：", "Create_tool_s1_method_patch_tooltip_explain": "使用ID 123更新用户数据", "Create_tool_s1_method_patch_tooltip_title": "Patch", "Create_tool_s1_method_patch_tooltip_url": "PATCH /users/123", "Creator": "创建人", "Datasets": "知识库", "Delete": "删除", "Delete_failed": "删除失败", "Delete_success": "删除成功", "Description": "描述", "Display format": "展示方式", "Duplicate_success": "复制成功", "Edit": "编辑", "Edit_success": "编辑成功", "Edit_time_2": "编辑时间", "Imageflow_generate_standard": "生成质量", "Imageflow_mode_choose": "请选择模式", "Imageflow_model": "模型", "Imageflow_model_deploy": "模型设置", "Imageflow_negative": "负向提示词", "Imageflow_negative_placeholder": "用于生成图像的负向提示词，用来描述你的画面中不想生成的内容，你可以使用{{变量名}}的方式引用输入参数中的变量（系统默认拼接负向提示词：nsfw, nude, blurry, watermark, identifying mark, low resolution, mutated, lack of hierarchy）", "Imageflow_not_support": "该模型暂不支持使用该图像参考模式", "Imageflow_positive": "正向提示词", "Imageflow_positive_placeholder": "用于生成图像的正向提示词，你可以使用{{变量名}}的方式引用输入参数中的变量", "Imageflow_prompt": "提示词", "Imageflow_ratio": "比例", "Imageflow_reference_image": "参考图", "Imageflow_reference_info1": "模型", "Imageflow_reference_info2": "参考图", "Imageflow_reference_info3": "程度", "Imageflow_size_range": "支持的宽高范围：[512,1536]", "Input": "输入", "Lark_00001": "Lark", "Lark_00002": "Lark数据源", "Loading": "加载中", "Manual_crawling_040": "列名不可重复", "Me": "我的", "More": "更多", "Network_error": "网络错误", "Next_1": "下一步", "Next_2": "下一页", "No_more": "没有更多了", "No_recall_001": "回复", "No_recall_002": "无召回回复", "No_recall_003": "默认", "No_recall_004": "自定义", "No_recall_005": "当知识库没有召回有效切片时的回复话术", "No_recall_006": "抱歉，您的问题超出了我的知识范围，并且无法在当前阶段回答", "No_recall_007": "开启自定义 prompt 后，如果未命中切片时，将参照如下示例回复", "Output": "输出", "Plugin_button_code_tooltip": "使用代码编辑工具", "Plugin_button_publish_tooltip": "工具全部试运行通过后即可发布。", "Plugin_delisted": "该插件已下架", "Plugin_list_table_owner": "创建者", "Plugin_new_toast_success": "插件已创建，请继续添加工具", "Plugin_publish_update_toast_success": "已发布更新", "Plugin_update_info_text": "你修改了 {number} 个工具 {array}，一旦这个新版本发布，相关智能体将同步更新所引用的工具，确认发布吗？", "Plugin_update_info_title": "发布更新", "Plugin_update_success": "更新成功", "Plugin_update_toast_success": "更新成功", "Plugins": "插件", "Popular": "最受欢迎", "Previous_1": "上一步", "Previous_2": "上一页", "Publish": "发布", "PublishSuccessConfirm": "Workflow 发布成功！是否添加至当前智能体？", "Published_1": "已发布", "Remove": "移除", "Reset": "重置", "Responding": "响应回答中", "Retry": "重试", "Save": "保存", "Save_success": "保存成功", "Search": "搜索", "Searched": "搜索过", "Searching": "查询", "Sort": "排序", "Starling_filebox_api_list": "列表", "Starling_filebox_name": "文件盒子", "Success": "成功", "Tools": "工具", "Type": "类型", "Unpublished_1": "未发布", "Update": "更新", "Update_failed": "更新失败", "Update_success": "更新成功", "Update_time": "编辑时间", "Upload_failed": "上传失败", "Use_template": "使用模板", "Used": "已调用", "Using": "正在调用", "View": "查看", "Visited": "访问过", "Visiting": "访问", "Workflow": "工作流", "Workflows": "工作流", "about_privacy_policy": "隐私政策", "account_update_hint": "检测到账号状态变更，请刷新页面", "actions": "操作", "add": "添加", "add_api_token_1": "点击按钮添加个人访问令牌", "add_image": "插入图片", "add_link": "插入链接", "add_mock_data": "新增模拟数据", "add_new_pat_1": "添加新的个人访问令牌", "add_new_token_button_1": "添加新令牌", "add_nickname": "插入用户昵称", "add_resource_modal_copy_to_project": "复制到应用", "add_resource_modal_sidebar_library_tools": "资源库工具\n", "add_resource_modal_sidebar_project_tools": "应用工具\n", "agent_creat_tips": "智能体支持「提示词对比」和「模型对比」调试啦！", "agent_ide_default_input_option": "默认输入方式", "agent_ide_default_input_option_text": "打字输入", "agent_ide_default_input_option_voice": "语音输入", "agent_prompt_editor_insert_placeholder": "或输入 {keymap} 插入已配置的技能", "agentflow_addbot_select_empty_no_bot": "没有找到智能体", "agentflow_jump_running_process_backtrack": "回溯到", "agentflow_jump_running_process_jump": "跳转到", "agentflow_jump_running_process_jump_time": "切换节点", "agentflow_jump_running_process_trigger_condition": "触发条件：", "agentflow_transfer_ conversation_settings_backtrack_previous": "返回上一个对话的节点尝试解决用户问题", "agentflow_transfer_ conversation_settings_backtrack_start": "返回开始节点尝试解决用户问题", "agentflow_transfer_ conversation_settings_mode_node_title": "在当前节点的运行过程中识别", "agentflow_transfer_ conversation_settings_title": "切换节点设置", "ai_plugin_(fill_in_json)_*": "ai_plugin (填写 json)", "analytic_query_agenttype": "Agent 类型", "analytic_query_blank_context": "暂无数据", "analytic_query_calculating": "计算中", "analytic_query_calltype": "调用类型", "analytic_query_channel": "渠道", "analytic_query_clear": "清空", "analytic_query_clear_tips": "清空筛选项", "analytic_query_detail_copy_failed": "复制失败", "analytic_query_detail_copy_success": "复制成功", "analytic_query_detail_key_latency": "耗时", "analytic_query_detail_left_panel_flamethread": "火焰图", "analytic_query_detail_left_panel_runtree": "调用树", "analytic_query_detail_returnerror": "扣子当前存在问题，请刷新页面后进行重试", "analytic_query_detail_right_panel_title_nodedetail": "节点详情", "analytic_query_detail_subtype_value_userinput": "用户输入", "analytic_query_detail_tips_for_outputmode": "返回变量，由{outputMode}生成", "analytic_query_detail_title_channel": "渠道", "analytic_query_detail_title_endtime": "结束时间", "analytic_query_detail_title_success": "成功", "analytic_query_detail_topology": "拓扑", "analytic_query_detail_topology_tooltip": "{errorCount} 异常 / {callCount} 调用", "analytic_query_diagloground": "上下文轮数", "analytic_query_endtime": "结束时间", "analytic_query_env": "环境", "analytic_query_env_value_botmakerdebug": "智能体编排调试", "analytic_query_env_value_realuser": "线上真实用户", "analytic_query_export": "导出为 Excel", "analytic_query_export_content": "单次最大可导出 {maxExportCount} 条，目前选择 {selectedExportCount} 条。确定继续导出则导出前 {maxExportCount} 条吗？", "analytic_query_export_title": "超过单次可导出最大数量", "analytic_query_filters_key_botversion": "智能体版本", "analytic_query_filters_key_input": "输入", "analytic_query_filters_key_output": "输出", "analytic_query_filters_key_status": "状态", "analytic_query_firstrestime": "首字符回复时间", "analytic_query_gpt": "模型", "analytic_query_hook_resp_code": "Hook 响应码", "analytic_query_input": "输入", "analytic_query_inputtokens": "模型输入 Token", "analytic_query_inputtype": "输入类型", "analytic_query_latency": "整体耗时", "analytic_query_latencyfirst": "首次响应耗时", "analytic_query_logid": "<PERSON><PERSON><PERSON>", "analytic_query_model": "模型", "analytic_query_name": "名称", "analytic_query_network_tips": "网络超时，请", "analytic_query_network_tips_retry": "重试。", "analytic_query_noright_tips": "抱歉，您暂无权限查看该数据。请联系工作空间所有者，成为协作者后，方可进行查看。", "analytic_query_os": "操作系统", "analytic_query_output": "输出", "analytic_query_outputtokens": "模型输出 Token", "analytic_query_resmaxlen": "最大回复长度", "analytic_query_security_verify_check": "验证", "analytic_query_security_verify_context": "平台检测到消息记录的高频访问。为了保护用户隐私，请进行安全验证。详情请查看{agreementName} 。", "analytic_query_security_verify_context_agreementname": "扣子用户隐私协议", "analytic_query_security_verify_select": "我不是机器人", "analytic_query_security_verify_title": "安全验证", "analytic_query_starttime": "请求发起时间", "analytic_query_status": "状态", "analytic_query_status_broken": "损坏", "analytic_query_status_error": "异常", "analytic_query_status_success": "成功", "analytic_query_status_unknown": "未知", "analytic_query_subtype": "子类型", "analytic_query_subtype_value_card": "卡片", "analytic_query_subtype_value_code": "代码", "analytic_query_subtype_value_codebatch": "代码批处理", "analytic_query_subtype_value_condition": "选择器", "analytic_query_subtype_value_invokeagent": "调用Agent", "analytic_query_subtype_value_knowledge": "知识库", "analytic_query_subtype_value_llmbatchcall": "批量调用 LLM", "analytic_query_subtype_value_llmcall": "调用 LLM", "analytic_query_subtype_value_opendialog": "开场白", "analytic_query_subtype_value_plugintool": "插件工具调用", "analytic_query_subtype_value_plugintoolbatch": "插件工具批量调用", "analytic_query_subtype_value_restartagent": "重启Agent", "analytic_query_subtype_value_scheduledtasks": "定时任务", "analytic_query_subtype_value_switchagent": "切换Agent", "analytic_query_subtype_value_thirdparty": "第三方调用", "analytic_query_subtype_value_unknown": "未知", "analytic_query_subtype_value_userinput": "用户输入", "analytic_query_subtype_value_workflow": "工作流", "analytic_query_subtype_value_workflow_message": "消息", "analytic_query_subtype_value_workflowend": "工作流结束", "analytic_query_subtype_value_workflowstart": "工作流开始", "analytic_query_summary_errorrate": "错误率", "analytic_query_summary_queriescount": "消息数", "analytic_query_summary_title": "统计", "analytic_query_table_title_channel": "渠道", "analytic_query_table_title_starttime": "开始时间", "analytic_query_table_title_tokens": "Tokens", "analytic_query_temperature": "回复随机性", "analytic_query_token_0_tips": "数据计算中，消息上报完成后刷新页面查看", "analytic_query_tokens": "Tokens", "analytic_query_type": "类型", "analytic_query_type_cid": "消息 ID", "analytic_query_type_input_number_tips": "请填写正确的数值", "analytic_query_type_input_tips": "支持输入多个，回车确认", "analytic_query_type_sid": "会话 ID", "analytic_query_type_uid": "用户 ID", "analytic_query_type_value_agent": "Agent", "analytic_query_type_value_card": "卡片渲染", "analytic_query_type_value_code": "代码", "analytic_query_type_value_condition": "选择器", "analytic_query_type_value_knowledge": "知识库", "analytic_query_type_value_llmcall": "调用 LLM", "analytic_query_type_value_message": "消息", "analytic_query_type_value_plugin": "插件", "analytic_query_type_value_start": "开始", "analytic_query_type_value_unknown": "未知", "analytic_query_type_value_workflow": "工作流", "analytic_query_type_value_workflowend": "工作流结束", "analytic_query_type_value_workflowstart": "工作流开始", "analytic_query_workflowvers": "工作流版本", "analytic_streaming_output_status_close": "关闭", "analytic_streaming_output_status_open": "开启", "analytics_page_title": "分析", "analytics_query_aigc_detail": "查看详情", "analytics_query_aigc_errorpanel_context": "加载失败", "analytics_query_aigc_errorpanel_ok": "完成", "analytics_query_aigc_infopanel_cancel": "取消", "analytics_query_aigc_infopanel_context": "该类型不支持查看详情，你可以下载后查看", "analytics_query_aigc_infopanel_download": "下载", "analytics_query_aigc_infopanel_title": "无法查看", "analytics_query_aigc_inforpanel_title_file": "文件无法查看", "analytics_query_invoke": "调用 {name}", "analytics_query_type_variable": "变量", "analytics_timepicker_customize": "自定义", "analytics_timepicker_last_1_day": "过去1天", "analytics_timepicker_last_days": "过去{count}天", "api": "API", "api_analytics_null": "暂无数据", "api_analytics_refresh": "刷新", "api_permissionkey_notification_content": "会话等权限点已升级！若希望调用会话、消息和文件等接口，请查看并勾选对应的权限后再确认保存。更多详情请访问 API Docs。", "api_permissionkey_notification_title": "权限点迭代", "api_sdk_published": "API (通过 {coze_token} 用量收费)", "api_status_1": "状态", "api_status_active_1": "有效", "api_status_expired_1": "已过期", "api_status_permanent_1": "永久有效", "api_token_reminder_1": "每个账户赠送100次的API免费调用额度。免费额度用完后，可通过充值 Coze Token 继续使用 API。", "app_ide_publish_modal_publish_button": "去发布", "app_ide_publish_modal_publish_management": "发布管理", "app_ide_publish_modal_recent_publication": "最近发布", "app_ide_viewing_archive": "正在预览存档", "app_publish_connector_mcp": "MCP服务", "app_publish_connector_space_mcp_config_dialog_cancel": "取消", "app_publish_connector_space_mcp_config_dialog_choose_wf": "选择工作流", "app_publish_connector_space_mcp_config_dialog_confirm": "确认", "app_publish_connector_space_mcp_config_dialog_desc": "选择一个或多个工作流发布到扩展库中使用。", "app_publish_connector_space_mcp_config_dialog_desc2": "部分类型工作流不支持发布为扩展", "app_publish_connector_space_mcp_config_dialog_filter_all": "全部", "app_publish_connector_space_mcp_config_dialog_hover_wf_constraints": "不支持的工作流类型\n1. 使用了端插件\n2.节点形式为对话流\n3.使用了会话管理类节点\n4.使用了问答节点\n另外，如果配置了输出节点，则发布后只会返回最终输出", "app_publish_connector_space_mcp_config_dialog_no_results_found": "无搜索结果", "app_publish_connector_space_mcp_config_dialog_search_placeholder": "搜索工作流", "app_publish_connector_space_mcp_config_dialog_title": "扩展配置", "app_publish_sdk_confirm": "我知道了", "app_publish_sdk_step_1": "将 OAuth 令牌添加到 auth key 中，向用户授予访问令牌：\n{doc_link}", "app_publish_sdk_step_1_doc": "OAuth 授权概述", "app_publish_sdk_step_2": "复制代码并粘贴到网站的 <body> 标签中", "app_publish_sdk_step_3": "刷新网站,你的智能体将会显示在页面的右下角", "app_publish_sdk_title": "Web SDK 安装指引", "ask_quote": "提问", "asyn_task_reply_need": "回复内容必须设置", "asyn_task_reply_toolong": "回复内容过长", "asyn_task_setting_desc": "任务进入异步运行时默认返回一条回复内容，用户可以继续对话，任务在后台运行完成后会通知用户。目前仅支持扣子站内调试台和商店渠道。", "asyn_task_setting_response_content": "您可以在这里设置消息回复,任务运行时将自动回复,比如\"任务已在进行中,一旦完成我将第一时间向您报告结果,您还有其他需要我协助的事项吗?\n", "asyn_task_setting_response_title": "回复内容", "asyn_task_setting_title": "异步运行", "atasets_createpdf_over250": "PDF 页数超过 500 页\t", "attach_user_data_to_create_a_new_user_": "附带用户数据来创建一个新用户。", "audit_unsuccess_general_type": "内容不符合{link}，请修改后重试", "audit_unsuccess_general_type_url": "《扣子平台发布规范》", "auth_tab_auth": "授权", "auth_tab_pat": "个人访问令牌", "auto_generate": "自动生成", "back": "返回", "background_confirm": "确认", "basic_log_out": "退出登录", "basic_model": "基础款", "basic_setting": "设置", "bgi_adjust_tooltip_content": "拖动图片调整位置 / 滚动缩放图片", "bgi_adjust_tooltip_title": "调整背景图片", "bgi_already_set": "已配置背景图片", "bgi_desc": "为你的智能体在扣子智能体商店增加对话背景图片，对话时更加沉浸", "bgi_remove_popup_content": "移除后，需要重新发布智能体才能生效。", "bgi_remove_popup_title": "确认移除背景图片？", "bgi_reupload": "重新上传背景图", "bgi_title": "背景图片", "bgi_upload_image_format_requirement": "请上传一张高度不小于 640px 的背景图", "bgi_upload_image_format_requirement_title": "背景图片尺寸", "binding_add_card": "新增", "binding_card_list": "选择卡片样式", "binding_card_preview": "卡片预览", "binding_card_update": "待更新", "binding_duplicate_card": "复制", "binding_edit_card": "编辑", "binding_view_card": "查看", "bmv_enter_version_description": "此处填写本次提交版本说明", "bmv_load_to_draft": " 加载到草稿", "bmv_official_version": " 正式版本", "bmv_offline_time": " 下线时间", "bmv_please_release_the_official_version_before_releasing_it_": "首次发布正式版本后，才可以使用预发布功能", "bmv_ppe_lane": " PPE泳道", "bmv_pre_release_to_lane": "预发布到泳道", "bmv_submit_id": " 提交ID", "bmv_view_version": "查看版本", "bot_autosave_saving": "保存中...", "bot_build_title": "编排", "bot_copy_id_error": "缺失bot_id信息", "bot_copy_info_error": "缺失智能体信息", "bot_copy_success": "智能体副本已创建", "bot_create_desciption": "智能体功能介绍", "bot_create_description_placeholder": "介绍智能体的功能，将会展示给智能体的用户", "bot_create_name": "智能体名称", "bot_create_name_placeholder": "给智能体起一个独一无二的名字", "bot_created_toast": "智能体已创建", "bot_database": "数据库", "bot_database_add_field": "最多新增{number}个字段", "bot_database_ai_create": "使用 AI 新建", "bot_database_ai_create_tip": "描述数据表的用途（例如记录阅读笔记）", "bot_database_ai_generate": "新建", "bot_database_ai_replace": "替换当前数据表。", "bot_database_ai_replace_detailed": "清空当前内容，重新生成新的数据表。", "bot_database_ai_waiting": "AI 正在生成", "bot_datamemory_remove_field": "删除变量", "bot_debug_question_wait": "等待回复", "bot_delete_confirm_title": "删除智能体", "bot_deleted_toast": "智能体已删除", "bot_dev_privacy_setting_channel": "该功能目前对以下渠道生效：", "bot_dev_privacy_setting_conversation": "消息记录", "bot_dev_privacy_setting_desc": "开启后，你可以查看用户与智能体的对话记录，用户也将在开场白中看到以下提示：该智能体开发者已开启聊天记录访问功能，用于迭代优化智能体对话能力。请不要在对话中分享敏感信息。", "bot_dev_privacy_setting_developer_collect1": "收集的内容", "bot_dev_privacy_setting_developer_collect2": "联系信息", "bot_dev_privacy_setting_developer_collect3": "请输入开发者名称", "bot_dev_privacy_setting_developer_collect4": "请选择要收集的内容", "bot_dev_privacy_setting_developer_collect5": "请输入联系信息", "bot_dev_privacy_setting_developer_collect7": "本期只支持消息记录", "bot_dev_privacy_setting_developer_name": "开发者名称", "bot_dev_privacy_setting_generate_link1": "生成链接", "bot_dev_privacy_setting_generate_link2": "正在生成", "bot_dev_privacy_setting_invalid_link": "无效链接，请输入正确的链接格式", "bot_dev_privacy_setting_link1": "隐私政策链接", "bot_dev_privacy_setting_link2": "将在智能体配置项或对话中向用户展示", "bot_dev_privacy_setting_privacy_template": "用模板生成", "bot_dev_privacy_setting_privacy_template_1": "用模板生成链接", "bot_dev_privacy_setting_privacy_template_2": "请输入以下信息，将基于{privacy_template}为你生成链接。", "bot_dev_privacy_setting_privacy_template_3": "隐私政策模板", "bot_dev_privacy_setting_title": "查看消息记录", "bot_dev_privacy_title": "隐私设置", "bot_duplicateded_toast": "智能体已复制", "bot_edit_auto_suggestion_customize_description": "在智能体回复后，根据 Prompt 提供最多 3 条用户提问建议", "bot_edit_auto_suggestion_customize_failed_to_generate": "无法生成用户问题建议", "bot_edit_auto_suggestion_customize_modal_prompt_placeholder": "请输入用于生成用户问题建议的 Prompt", "bot_edit_auto_suggestion_customize_user_checkbox": "用户自定义 Prompt", "bot_edit_auto_suggestion_default_description": "在智能体回复后，自动根据对话内容提供 3 条用户提问建议", "bot_edit_auto_suggestion_off_description": "在每次智能体回复后，不会提供任何用户问题建议", "bot_edit_auto_suggestion_status_off": "关闭", "bot_edit_database_add_tooltip": "添加表", "bot_edit_dataset_add_tooltip": "添加知识库", "bot_edit_dataset_added_toast": "数据集{dataset_name}已添加", "bot_edit_dataset_explain": "将文件或网站 URL 上传为数据集后，用户发送消息时，智能体能够引用数据集中的内容回答用户问题。", "bot_edit_dataset_on_demand_prompt1": "请在“人设与回复逻辑”区域提示智能体调用 ", "bot_edit_dataset_on_demand_prompt2": " 方法，以供智能体根据特定或全部知识库中的内容回复用户。", "bot_edit_dataset_removed_toast": "数据集{dataset_name}已删除", "bot_edit_datasetsSettings_MaxTip": "从知识库中返回给大模型的最大段落数，数值越大返回的内容越多", "bot_edit_datasetsSettings_MinTip": "根据设置的匹配度选取段落返回给大模型，低于设定匹配度的内容不会被召回", "bot_edit_datasets_copyName": "复制名称", "bot_edit_memory_title_action": "操作", "bot_edit_memory_title_default": "默认值", "bot_edit_memory_title_description": "描述", "bot_edit_memory_title_filed": "名称", "bot_edit_opening_question_title": "开场白预置问题", "bot_edit_opening_questions_tooltip": "填写超过 3 条时，将随机显示 3 条问题建议。", "bot_edit_opening_text_title": "开场白文案", "bot_edit_opening_tooltip": "自动生成", "bot_edit_page_plugin_copy_tool_name_tip": "复制", "bot_edit_page_plugin_list_plugin_has_n_tools": "{n} 个工具", "bot_edit_page_plugin_list_plugin_n_bots_using": "{n} 个智能体正在使用", "bot_edit_page_plugin_tool_param_not_required": "非必填", "bot_edit_page_plugin_tool_param_required": "必填", "bot_edit_plugin_add_tooltip": "添加插件", "bot_edit_plugin_delete_tooltip": "删除问题", "bot_edit_plugin_explain": "插件能够让智能体调用外部 API，例如搜索信息、浏览网页、生成图片等，扩展智能体的能力和使用场景。", "bot_edit_plugin_select_title": "添加插件", "bot_edit_profile_pircture": "图标", "bot_edit_profile_pircture_autogen_quota_tooltip": "每天最多可以生成 25 张头像，请明天再试", "bot_edit_profile_pircture_autogen_tooltip": "根据名称和介绍，使用 DALL·E-3 自动生成", "bot_edit_profile_pircture_autogen_tooltip_cn": "输入名称和介绍后，点击可自动生成头像。", "bot_edit_remove_workflow": "删除工作流", "bot_edit_store": "商店", "bot_edit_suggestion": "用户问题建议", "bot_edit_title": "编辑智能体", "bot_edit_tool_added_toast": "API {api_name} 已添加", "bot_edit_tool_added_toast_error": "添加API {api_name} 失败\n", "bot_edit_tool_removed_toast": "API {api_name} 已删除", "bot_edit_type_character": "角色", "bot_edit_type_dialog": "对话体验", "bot_edit_type_knowledge": "知识", "bot_edit_type_memory": "记忆", "bot_edit_type_skills": "技能", "bot_edit_variable_add_tooltip": "添加变量", "bot_edit_variable_add_tooltip_edit": "编辑变量", "bot_edit_variable_default_value_placeholder": "默认值", "bot_edit_variable_description_placeholder": "字段描述", "bot_edit_variable_field_occupied_error": "字段名重复", "bot_edit_variable_field_required_error": "请填写字段名", "bot_edit_voices_modal_add_language": "语言和音色", "bot_edit_voices_modal_description": "你需要为不同语言选择音色。如果不指定语言和音色，{platform}用户将听到{platform} App语言对应的默认音色。", "bot_edit_voices_modal_language": "语言", "bot_edit_voices_modal_title": "选择音色", "bot_edit_voices_title": "语音", "bot_edit_workflow_add_tooltip": "添加工作流", "bot_edit_workflow_explain": "工作流支持通过可视化的方式，对插件、大语言模型、代码块等功能进行组合，从而实现复杂、稳定的业务流程编排，例如旅行规划、报告分析等。", "bot_element_unset": "暂未提供", "bot_execute_by_autosave": "正在保存，请稍后再试", "bot_has_changes_tip": "有尚未发布的修改", "bot_ide_browser_not_support_geolocation_toast": "当前浏览器不支持获取地理位置信息", "bot_ide_geolocation_not_usable_toast": "地理位置信息不可用", "bot_ide_geolocation_request_timeout_toast": "请求地理位置信息时超时", "bot_ide_geolocation_request_unknown_error_toast": "请求地理位置时发生未知错误", "bot_ide_knowledge_confirm_cancel": "不添加", "bot_ide_knowledge_confirm_content": "是否添加到智能体？", "bot_ide_knowledge_confirm_ok": "添加", "bot_ide_knowledge_confirm_title": "提示", "bot_ide_knowledge_table_desc": "用户上传表格后，支持按照表格的某列来匹配合适的行给智能体引用，同时也支持基于自然语言对数据库进行查询和计算。", "bot_ide_knowledge_text_desc": "将文档、URL、三方数据源上传为文本知识库后，用户发送消息时，智能体能够引用文本知识中的内容回答用户问题。", "bot_ide_plugin_setting_modal_default_value_select_mode_input": "输入", "bot_ide_plugin_setting_modal_default_value_select_mode_input_placeholder": "请填写", "bot_ide_plugin_setting_modal_default_value_select_mode_reference": "引用", "bot_ide_plugin_setting_modal_default_value_select_mode_reference_placeholder": "请选择", "bot_ide_plugin_setting_modal_mockset_tab": "模拟集", "bot_ide_plugin_setting_modal_parameter_tab": "参数", "bot_ide_shortcut": "快捷指令", "bot_ide_shortcut_add_button": "添加指令", "bot_ide_shortcut_intro": "快捷指令是对话输入框上方的按钮，配置完成后，用户可以快速发起预设对话", "bot_ide_shortcut_item_edit": "编辑", "bot_ide_shortcut_item_trash": "删除", "bot_ide_shortcut_max_limit": "最多添加10个指令", "bot_ide_shortcut_removal_confirm": "确认移除这个指令吗？", "bot_ide_user_declines_geolocation_auth_toast": "用户拒绝了地理位置信息获取请求，请在浏览器设置中手动开启", "bot_list_create": "创建智能体", "bot_list_delete_bot": "此操作仅会删除在 Coze 中的智能体。如果你想删除已经发布到 {platform}、飞书等平台的智能体，请前往对应 App 操作。", "bot_list_mine": "由我创建", "bot_list_open_button": "立即对话", "bot_list_rank_tag_edited": "最近编辑", "bot_list_rank_tag_published": "最近发布", "bot_list_team": "所有人", "bot_opening_remarks_replace_cancel_button": "取消", "bot_opening_remarks_replace_confirm_button": "替换", "bot_persona_and_prompt": "人设与回复逻辑", "bot_preview_attach_0319": "上传文件：image、pdf、docx、excel、csv、audio。请 {placeholder} 处理文件。", "bot_preview_attach_select": "选择工具", "bot_preview_debug_title": "预览与调试", "bot_preview_file_cancel": "取消", "bot_preview_file_copyURL": "复制URL", "bot_preview_file_retry": "重试", "bot_preview_hide_running_process": "隐藏运行过程", "bot_preview_opening_remarks": "开场白", "bot_preview_run_completed": "运行完毕", "bot_preview_run_terminated": "运行中止", "bot_preview_searched_dataset": "已搜索知识库", "bot_preview_task": "触发器", "bot_prompt_bracket_error": "{{双花括号}} 为特殊符号，暂不支持输入，请删除或替换为其他符号后重试", "bot_publish_ republish_btn": "重新发布", "bot_publish_action_configure": "配置", "bot_publish_bind_error": "ID已绑定到{bot_name}，请先解除绑定。", "bot_publish_button": "发布", "bot_publish_changelog": "发布记录", "bot_publish_columns_action_authorize": "授权", "bot_publish_columns_action_revoke_authorize": "撤销授权", "bot_publish_columns_platform": "发布平台", "bot_publish_columns_result": "发布结果", "bot_publish_columns_status_authorized": "已授权", "bot_publish_columns_status_configured": "已配置", "bot_publish_columns_status_disconnected": "已断开", "bot_publish_columns_status_failed": "失败", "bot_publish_columns_status_in_review": "审核中", "bot_publish_columns_status_not_configured": "未配置", "bot_publish_columns_status_offline": "已下线", "bot_publish_columns_status_unauthorized": "未授权", "bot_publish_disable_check_tip": "请先完成授权或配置", "bot_publish_disconnect": "解绑{platform}", "bot_publish_disconnect_desc": "解绑后，{platform} 智能体将无法接收消息。", "bot_publish_disconnect_success": "成功断开连接", "bot_publish_disconnect_title": "你确定要将{platform}与智能体解绑吗？", "bot_publish_field_placeholder": "请输入 {field}", "bot_publish_in_review_disable_check_tip": "发布渠道正在审核相关权限，请稍候。", "bot_publish_in_review_notice": "渠道审核中", "bot_publish_offline_notice_no_certain_time": "长时间无用户与你的 {platform}智能体交流，智能体已自动下线，请重新发布", "bot_publish_result_copy_bot_link": "复制智能体链接", "bot_publish_select_desc_compliance_new": "在以下平台发布你的智能体，即表示你已充分理解并同意遵循{publish_terms_title}（包括但不限于任何隐私政策、社区指南、数据处理协议等）。", "bot_publish_select_title": "选择发布平台", "bot_publish_success_back": "完成", "bot_publish_token_expired_notice": "{platform} 的凭证已失效，智能体已下线，请重新配置并发布智能体。", "bot_pulish_offline_modal_title1": "{platform} 已下线", "bot_pulish_offline_modal_title2": "智能体在 {platform_number} 个平台已下线", "bot_share_more_platforms": "更多", "bot_share_not_supported_opening": "如果智能体被发布至其他平台，请前往对应平台使用并分享智能体。", "bot_status_published": "已发布", "bot_suggestion_customize_default_gpt": "鉴于AI的角色设定，考虑用户之前与AI助手的聊天记录，思考用户在上一次查询中的场景、意图、背景，生成用户最有可能接下来问AI助手（你）的问题：\n1. 不要生成用户可能已经知道答案的问题，或与当前话题无关的问题。\n2. 一定要生成非常简洁明了的问题（少于15个字），这些问题可能由用户提问AI助手（你），而不是AI助手（你）问用户的问题。\n3. 不要生成重复或类似的问题。\n\n额外要求：\n1. 每次生成3个问题。\n2. 如果用户最新的问题涉及创造性任务（比如想出一个标题），至少生成一个直接询问如何增强AI助手之前回答的创造性或吸引力的问题。\n3. 如果AI助手没有回答或拒绝回答用户的问题，可以根据助手能回答的内容提出建议，引导话题朝更有成效的方向发展，与当前话题无关。\n4. 确保问题与聊天记录中的不同。", "bot_suggestion_customize_default_seed": "- 问题应该与你最后一轮的回复紧密相关，可以引发进一步的讨论。\n- 问题不要与上文已经提问或者回答过的内容重复。\n- 每句话只包含一个问题，但也可以不是问句而是一句指令。\n- 推荐你有能力回答的问题。", "bot_suggestion_switch_on_title": "开启", "bot_task": "定时任务", "bot_task_preset_day_of_month": "{day} 日", "bot_task_preset_day_of_week": "{day, plural, =0{周日} =1{周一} =2{周二} =3{周三} =4{周四} =5{周五} =6{周六} other{周一}}", "bot_task_preset_everyday_task": "每天 {time}", "bot_task_preset_everyweek_task": "每周 {day} {time}", "bot_task_preset_interval_task": "每隔 {day}日 {time}", "bot_task_preset_monthly_task": "每月 {day}日 {time}", "bot_task_preset_triggered_everyday": "每日触发", "bot_task_preset_triggered_everyweek": "每周触发", "bot_task_preset_triggered_interval": "间隔触发", "bot_task_preset_triggered_monthly": "每月触发", "bot_userProfile_add": "新增", "bottom": "置底", "browser_upgrade": "当前浏览器存在兼容性问题，请切换至最新版本 Chrome、Safari、Edge 或 Firefox 浏览器", "browser_upgrade_button": "点击升级", "builder_canvas_tools_pc": "桌面端", "builder_canvas_tools_phone": "移动端", "builder_publish_changelog_label": "版本描述", "builder_publish_changelog_placeholder": "请输入版本描述", "builder_publish_version_label": "版本号", "button": "按钮", "bwc_no_version_record": "暂无版本记录", "bwc_version_description_exceeds_word_limit": "版本说明超出字数限制，请修改后提交", "bwc_view_multiple_environments": "查看多环境", "bz_coop_upgrade_for_more": "当前最多支持添加 {max_coop} 人，升级解锁更多人数", "bz_reache_max": "协作{type_name}同时最多开启{max_cnt}个，升级后可解锁更多", "bz_upgrade_button": "升级", "bz_upgrade_detail": "升级专业版以解锁更多协作数量", "cancel": "取消", "cancel_template": "取消", "cannot_enable_mock_set_due_empty_return": "当前插件工具未配置返回值或尚未发布，因此无法启用模拟集", "cannot_enable_mock_set_due_to_no_configured_return_value": "当前插件未配置返回值或类型不符合要求，无法启用模拟集", "card_builder_api_http_delete_error": "保存失败", "card_builder_api_http_params_columns_type": "参数类型", "card_builder_builder_publish_changelog_label": "版本描述", "card_builder_builtinLogic_confirm_message": "确认详情", "card_builder_check_title": "错误列表", "card_builder_dataEditor_get_errormsg_please_enter": "请输入", "card_builder_hover_align_horizontal": "水平居中", "card_builder_hover_align_left": "左对齐", "card_builder_hover_align_right": "右对齐", "card_builder_image": "图片", "card_builder_move_to_bottom": "置底", "card_builder_move_to_top": "置顶", "card_builder_redoUndo_redo": "重做", "card_builder_redoUndo_undo": "撤销", "card_builder_releaseBtn_releaseApp_copyTip": "复制成功", "card_builder_releaseBtn_release_btn": "发布", "card_builder_userVar_list_search_empty": "没有找到对应的变量", "card_builder_varpanel_var_empty": "暂无变量", "card_not_support_display_content": "请检查数据返回结果是否正确", "card_not_support_display_title": "无法显示内容", "chat-area-knowledge-crawl-data-source": "手动抓取数据源", "chat-area-knowledge-custom-data-source": "自定义数据源", "chat-area-knowledge-feishu-data-source": "飞书数据源", "chat-area-knowledge-google-data-source": "google 数据源", "chat-area-knowledge-local-data-source": "本地数据源", "chat-area-knowledge-notion-data-source": "notion 数据源", "chat-area-knowledge-online-data-source": "在线抓取数据源", "chat_GenAI_tips": "内容由AI生成，无法确保真实准确，仅供参考。", "chat_geolocation_auth_allow_tip": "你同意{plugin}获取你的地理位置信息", "chat_geolocation_auth_decline_tip": "你拒绝{plugin}获取你的地理位置信息", "chat_geolocation_auth_request_message": "{plugin_name}需要获取你的地理位置信息以提供服务", "chat_geolocation_auth_request_message_allow_button": "允许一次", "chat_geolocation_auth_request_message_decline_button": "拒绝", "chat_input_hover_tip_keyboard_input_button": "键盘输入", "chat_input_hover_tip_voice_input_button": "语音输入", "chat_setting_user_input_default_mode": "默认用户输入方式", "chat_tooltips_resend": "重新发送", "chat_voice_input_speaking_cancel_send": "松手取消发送", "chat_voice_input_tip_speaking_cancel_and_send": "上滑取消输入，松手发送", "chat_voice_input_tip_speaking_cancel_and_send_when_hold_down_space": "松开发送", "chat_voice_input_tip_speaking_record_and_send_after_x_seconds": "后自动结束录制并发送", "chat_voice_input_toast_no_content_recognized": "未识别到内容", "chatflow_agent_menu_rename": "重命名", "chatflow_agent_skill_name": "技能", "chatflow_develop_tooltip_hide": "收起编排区", "chatflow_develop_tooltip_show": "展开编排区", "chatflow_error_create_failed": "创建Agent失败", "chatflow_error_delete_failed": "删除Agent失败", "chatflow_error_miss_start": "找不到开始节点", "chatflow_error_miss_start_agent": "开始节点必须连接到一个Agent", "chatflow_preview_tooltip_hide": "收起预览与调试区", "chatflow_preview_tooltip_show": "展开预览与调试区", "chatflow_switch_mode_title": "选择模式", "click_button_to_create_mockset": "点击按钮创建模拟集", "click_upload_or_drag_files": "点击或拖动文件到此处上传", "code": "代码", "code_mode": "代码模式", "code_node_help_doc": "帮助文档", "code_node_language": "语言", "code_node_more_info": "更多信息参考", "code_node_switch_language": "切换语言", "code_node_switch_language_description": "切换语言将清除当前代码并替换成目标语言的模板", "code_node_test_code": "测试代码", "code_snippet": "Code Snippet", "codedev_hook_hook_type": "Hook 类型", "codedev_hook_invoked_failed": "调用 Hook 失败", "codedev_hook_run_log_invoked": "已调用", "collapse": "收起", "collapse-chat-knowledge-source-header": "收起", "community_Group_Title_content": "正文", "community_Image_uploading": "图片上传中... ({upload_num}/{total_num})", "community_Please_enter_please_enter_your_post": "输入正文", "community_This_is_a_toast_Machine_review_failed": "内容包含敏感信息，请修改后发布", "community_time_date": "{yyyy} 年 {mm} 月 {dd} 日", "community_time_day": "{n} 天前", "community_time_hour": "{n} 小时前", "community_time_just_now": "刚刚", "community_time_min": "{n} 分钟前", "compare_guide_description_model": "新增对比调试模式，可以在该模式下进行不同模型配置的对比。", "compare_guide_description_prompt": "新增对比调试模式，可以在该模式下进行不同系统提示词的对比。", "compare_guide_title_model": "模型对比", "compare_guide_title_prompt": "提示词对比", "compare_model_compare_model": "模型对比调试", "compare_prompt_compare_debug": "提示词对比调试", "compare_tooltips_submit_to_the_prompt": "提交到提示词库", "confirm": "确定", "confirm_plugin_information": "确认插件信息", "confirm_switch_model": "确认切换模型？", "confirm_switch_to_on_demand_call": "确认切换至{call_method}？\n", "content_view_001": "内容视图", "content_view_002": "分段视图", "content_view_003": "分段正在处理中", "context_clear_finish": "上下文已清除", "copied": "已复制", "copy": "复制", "copy_failed": "复制失败", "copy_session_id": "复制 ID", "copy_success": "复制成功", "cover": "覆盖", "coze_api_instru": "API 使用说明", "coze_api_list1": "名称", "coze_api_list3": "创建时间", "coze_api_list4": "最近使用时间", "coze_api_list5": "操作", "coze_bot_diff_btn_view_diff": "查看差异", "coze_bot_diff_diffdetail_latestversion": "最新版本", "coze_bot_diff_diffdetail_mydraft": "我的草稿", "coze_bot_diff_diffdetail_onlineversion": "线上版本", "coze_bot_diff_diffdetail_pagetitle": "差异明细", "coze_bot_diff_diffdetail_tobereleasedversion": "待发布的版本", "coze_copy_to_tips_1": "在当前工作空间创建副本", "coze_cost_sharing": "成本承担", "coze_custom_publish_platform_10": "创建时间", "coze_custom_publish_platform_11": "操作", "coze_custom_publish_platform_12": "私有", "coze_custom_publish_platform_13": "公开", "coze_custom_publish_platform_14": "未配置", "coze_custom_publish_platform_15": "已配置", "coze_custom_publish_platform_16": "平台名称", "coze_custom_publish_platform_17": "描述", "coze_custom_publish_platform_18": "描述平台", "coze_custom_publish_platform_19": "绑定 Oauth 应用", "coze_custom_publish_platform_2": "我的渠道管理", "coze_custom_publish_platform_20": "请选择 Oauth 应用", "coze_custom_publish_platform_21": "创建新应用", "coze_custom_publish_platform_23": "平台回调 URL", "coze_custom_publish_platform_24": "用于获取智能体的发布和更新通知", "coze_custom_publish_platform_26": "请选择空间", "coze_custom_publish_platform_27": "取消", "coze_custom_publish_platform_28": "确认", "coze_custom_publish_platform_29": "平台名称为必填项。", "coze_custom_publish_platform_3": "添加平台", "coze_custom_publish_platform_30": "描述为必填项。", "coze_custom_publish_platform_31": "绑定 Oauth 应用为必填项。", "coze_custom_publish_platform_32": "请输入使用 https 协议的有效 URL。", "coze_custom_publish_platform_34": "请选择一个空间", "coze_custom_publish_platform_37": "编辑", "coze_custom_publish_platform_38": "删除", "coze_custom_publish_platform_39": "删除此平台？", "coze_custom_publish_platform_4": "为您的空间自定义创建智能体可发布的渠道。如果您希望成为 Coze 公共渠道，请填写", "coze_custom_publish_platform_41": "配置", "coze_custom_publish_platform_42": "<PERSON><PERSON><PERSON> 配置", "coze_custom_publish_platform_43": "授权 coze 用户访问渠道的账号信息，完成平台之间账号的绑定授权", "coze_custom_publish_platform_5": "申请表", "coze_custom_publish_platform_55": "回调 Token", "coze_custom_publish_platform_56": "此操作将不可逆转。", "coze_custom_publish_platform_57": "callback_token", "coze_custom_publish_platform_58": "复制", "coze_custom_publish_platform_6": "平台", "coze_custom_publish_platform_63": "空间", "coze_custom_publish_platform_64": "仅支持数字、字母、汉字、下划线", "coze_custom_publish_platform_7": "ID", "coze_custom_publish_platform_8": "公开状态", "coze_custom_publish_platform_9": "<PERSON><PERSON><PERSON>", "coze_free_credits_insufficient": "您的资源点余额不足，请等待额度刷新或升级到付费版：https://console.volcengine.com/coze-pro", "coze_home_delete_btn": "删除对话记录", "coze_home_delete_modal_btn_cancel": "取消", "coze_home_page_fail_btn_retry": "请重试", "coze_home_page_fail_text": "初始化失败，", "coze_home_stop_btn": "停止响应", "coze_premium_credits_cycle_1": "每一天", "coze_premium_credits_cycle_2": "每周都有", "coze_premium_credits_cycle_3": "每个月都有", "coze_premium_credits_cycle_4": "永远不要重置", "coze_premium_credits_cycle_5": "信用重置周期", "coze_premium_credits_cycle_tip1": "消息积分将在设置的间隔内重置为指定数量的赞助消息。 ", "coze_premium_credits_cycle_tip2": "过度试用会增加信用成本，明智使用", "coze_premium_credits_cycle_tip3": "免费试用让更多用户发现机器人", "coze_premium_credits_cycle_tip4": "增加赞助消息以启用信用刷新周期", "coze_premium_credits_cycle_tip6": "每天上午12:00 UTC+0", "coze_premium_credits_cycle_tip7": "每周一上午12:00 UTC+0", "coze_premium_credits_cycle_tip8": "每月第一天上午12:00 UTC+0", "coze_pro_payment_overdue": "您的账户已产生欠费，请前往火山引擎充值后继续使用：https://console.volcengine.com/coze-pro/overview", "coze_quota_exemption_notice": "累计500条，{link}豁免额度限制", "coze_upgrade_package": "升级套餐", "cozedev_collaborator_btn_confirm": "确定", "creat_new_prompt": "创建提示词", "creat_new_prompt_des_placeholder": "请输入提示词简介", "creat_new_prompt_edit_block": "编辑块", "creat_new_prompt_import_link": "导入当前提示词", "creat_new_prompt_name_placeholder": "请输入提示词名称", "creat_new_prompt_prompt": "提示词", "creat_new_prompt_prompt_description": "提示词描述", "creat_new_prompt_prompt_name": "提示词名称", "creat_popup_profilepicture_generategif": "生成 GIF", "creat_popup_profilepicture_generateimage": "生成图片", "creat_popup_profilepicture_upload": "上传", "creat_project_agent_describe": "适用于快速搭建对话式智能体", "creat_project_creat_agent": "创建智能体", "creat_project_creat_new_project": "创建空白应用", "creat_project_creat_project": "创建应用", "creat_project_describe": "适用于搭建包含用户界面的完整应用", "creat_project_describe_open": "适用于搭建多工作流协同的完整应用", "creat_project_project_describe": "应用介绍", "creat_project_project_name": "应用名称", "creat_project_templates": "应用模板", "creat_project_templates_load_failed": "模板加载失败", "creat_project_title": "创建应用", "creat_project_toast_success": "创建成功", "creat_project_use_template": "使用模板创建应用", "creat_project_use_template_preview": "预览", "creat_project_use_template_use": "使用", "creat_prompt_button_comfirm_and_compare": "确认并对比调试", "creat_tooltip_create": "创建", "create-dataset-import-type": "导入类型\n", "create-knowledge-table-type": "表格格式", "create-knowledge-text-type": "文本格式", "create_local_plugin_basic_tool_function": "工具函数", "create_local_plugin_basic_tool_function_input_placeholder": "请输入工具函数名", "create_local_plugin_basic_warning_no_tool_function_entered": "请输入工具函数名", "create_mockset": "创建模拟集", "create_plugin_modal_Authorization_no": "不需要授权", "create_plugin_modal_Authorization_oauth": "OAuth", "create_plugin_modal_Authorization_service": "Service", "create_plugin_modal_Parameter": "Parameter name", "create_plugin_modal_Parameter_empty": "请输入 Parameter Name", "create_plugin_modal_Parameter_error": "请输入 Parameter Name", "create_plugin_modal_Parameter_info1": "Parameter name", "create_plugin_modal_Parameter_info2": "这是您需要传递Service Token的参数名。您可以将其视为“键（Key）”，而Service Token则是与之对应的\"值\"。其作用是告诉API服务，您将在哪个参数中提供授权信息。如将 abc.com/getInfo?token=xxx 中的 token 填在此处。", "create_plugin_modal_Servicetoken": "Service token / API key", "create_plugin_modal_Servicetoken_empty": "请输入 service token / API key", "create_plugin_modal_Servicetoken_error": "请输入 service token / API key", "create_plugin_modal_Servicetoken_info1": "Service token / API key", "create_plugin_modal_Servicetoken_info2": "这是一个专属的API密钥，代表您的身份或给定的服务权限。API服务会验证此Token，以确保您有权进行相应的操作。如将 abc.com/getInfo?token=xxx 中的 xxx 填在此处。", "create_plugin_modal_URLerror": "请输入有效 URL", "create_plugin_modal_auth1": "授权方式", "create_plugin_modal_authorization_content_type_empty": "请输入 authorization_content_type", "create_plugin_modal_authorization_content_type_info1": "用于向OAuth提供者发送数据的内容类型。默认值是最常见的内容类型。示例：", "create_plugin_modal_authorization_content_type_info2": "application/json", "create_plugin_modal_authorization_url_empty": "请输入 authorization_url", "create_plugin_modal_authorization_url_info1": "Authorization_url: 用户被重定向以授权应用的OAuth提供者的URL。需要进行URL验证。示例：", "create_plugin_modal_authorization_url_info2": "https://authprovider.com/authorize", "create_plugin_modal_button_cancel": "取消", "create_plugin_modal_button_confirm": "确认", "create_plugin_modal_client_id2": "请输入 client_id", "create_plugin_modal_client_id4": "应用在OAuth提供者注册时获取的唯一标识。示例：", "create_plugin_modal_client_id5": "abc123xyz", "create_plugin_modal_client_secret2": "请输入 client_secret", "create_plugin_modal_client_secret4": "Client_secret: 与client_id配对的秘密，用于认证应用并获取令牌。", "create_plugin_modal_client_url_empty": "请输入 client_url", "create_plugin_modal_client_url_info1": "Client_url: 应用的回调URL，授权码将发送到此URL。这个URL需要是合法的。示例：", "create_plugin_modal_client_url_info2": "https://yourapp.com/callback", "create_plugin_modal_descrip1": "插件描述", "create_plugin_modal_descrip1_error": "请输入插件描述", "create_plugin_modal_descrip2": "请输入插件的主要功能和使用场景，确保内容符合平台规范。帮助用户/大模型更好地理解", "create_plugin_modal_descrip_error": "请输入有效字符。仅支持英文字母、数字、常用标点符号等ASCII字符", "create_plugin_modal_header": "Header", "create_plugin_modal_header_list": "请求头列表", "create_plugin_modal_header_list1": "HTTP请求头列表是客户端程序和服务器在每个HTTP请求和响应中发送和接收的字符串列表。这些标头通常对最终用户不可见，仅由服务器和客户端应用程序处理或记录。", "create_plugin_modal_info_None1": "None", "create_plugin_modal_info_None2": "无需授权认证。", "create_plugin_modal_info_Oauth1": "OAuth", "create_plugin_modal_info_Oauth2": "OAuth 是一个开放标准，常用于用户代理认证。允许第三方应用在不共享用户密码的情况下访问用户账户的特定资源。例如，开发者希望利用 API 发布 Twitter，又不希望透露密码，则可以使用OAuth方式。", "create_plugin_modal_info_Service1": "Service", "create_plugin_modal_info_Service2": "Service 认证通常是指一种简化的认证方式，其中 API 调用需要某种秘钥或令牌来验证其合法性。这种秘钥可能会通过查询参数或请求头传递。它是为了确保只有拥有此秘钥的用户或系统能够访问 API。例如，你可能已经看到过有些公开 API 会要求你注册以获得一个 API 秘钥。", "create_plugin_modal_info_all": "选择插件使用的授权或验证方式。目前支持如下三种类型：", "create_plugin_modal_location": "位置", "create_plugin_modal_name1": "插件名称", "create_plugin_modal_name1_error": "请输入插件名称", "create_plugin_modal_name2": "请输入插件名称，确保名称含义清晰且符合平台规范", "create_plugin_modal_nameerror": "仅支持输入字母、数字、下划线或空格", "create_plugin_modal_nameerror_cn": "仅支持输入中文、字母、数字、下划线或空格", "create_plugin_modal_query": "Query", "create_plugin_modal_scope_empty": "请输入 scope", "create_plugin_modal_scope_info1": "你的应用希望访问的资源的范围或级别。示例：", "create_plugin_modal_scope_info2": "read_profile, write_post", "create_plugin_modal_title1": "新建插件", "create_plugin_modal_url1": "插件 URL", "create_plugin_modal_url1_error": "请输入插件 URL", "create_plugin_modal_url2": "请输入插件的访问地址或相关资源的链接", "create_plugin_modal_url_error_https": "请输入使用https协议的有效URL。", "create_time": "创建时间", "create_title": "创建", "create_tool": "创建工具", "created_mockset_please_add_mock_data": "创建 mockset 成功，请添加 mock data", "created_mockset_please_add_mock_data_llm_generation": "创建 mockset 成功，mock data 正在生成中，请等待", "created_mockset_please_add_mock_data_random_generation": "创建 mockset 并自动生成 mock data 成功", "creators": "创作者", "curl": "cURL", "customize_key_1": "自定义", "data_error_msg": "请稍后重试或联系 coze 工作空间", "data_error_title": "加载 {module} 失败", "data_filter_values": "过滤第 {filterPages} 页", "database_240227_01": "清空数据", "database_240304_01": "最多支持{TableNumber}个表格", "database_240520_01": "数据的唯一标识（主键）", "database_240520_03": "数据表默认支持在Prompt中访问，取消勾选后将不支持在Prompt中访问（仅能在Workflow中访问）", "database_240522_01": "复制", "database_240522_02": "复制成功", "database_240618_01": "支持在Prompt中调用", "database_learnmore": "了解更多", "database_memory_menu": "记忆", "database_optimize_100": "请选择导入渠道", "database_optimize_200": "线上数据是应用程序在实际运行时产生的数据，请谨慎修改", "dataide001": "数据", "dataide002": "变量", "dataset-name-empty-tooltip": "知识库名称不能为空", "dataset-name-has-wrong-word-tooltip": "名称中不能含有特殊字符", "dataset-setting_recall_title": "召回", "dataset_automatic_call": "自动调用", "dataset_bot_count_tag": "{num} 个", "dataset_bot_create_time_knowledge": "创建时间 {time}", "dataset_bot_update_time_knowledge": "编辑时间 {time}", "dataset_call_method": "调用方式", "dataset_create_knowledge_generate_avatar_tips": "输入名称和介绍后，点击自动生成头像。", "dataset_create_knowledge_generate_content_tips": "请检查名称和介绍是否包含不符合规范的内容。", "dataset_data_processing_tag": "{num} 条数据处理中", "dataset_detail_source_custom": "自定义", "dataset_detail_source_local": "本地", "dataset_detail_source_online": "在线", "dataset_detail_tableTitle_actions": "操作", "dataset_detail_tableTitle_enable": "启用", "dataset_detail_table_deleteModel_description": "删除后关联智能体中的引用将失效", "dataset_detail_type_table": "表格", "dataset_detail_type_text": "文本", "dataset_max_recall": "最大召回数量", "dataset_max_recall_default": "默认", "dataset_max_recall_desc": "太大可能导致召回段落超过Token限制，超过Token部分的低匹配段落将不会传输到大模型。", "dataset_min_degree": "最小匹配度", "dataset_min_degree_default": "默认", "dataset_on_demand_call": "按需调用", "dataset_process_fail": "数据处理失败", "dataset_recall_copy_label": "RecallKnowledge", "dataset_recall_copy_value": "recallKnowledge", "dataset_segment_content": "分段内容：", "dataset_segment_empty_desc": "暂无分段", "dataset_set_title": "选择知识库", "dataset_settings_title": "知识库设置", "dataset_upload_image_warning": "请上传小于 2MB 的图片", "datasets_Custom_maxLength": "分段最大长度", "datasets_Custom_rule": "文本预处理规则", "datasets_Custom_rule_delete": "删除所有 URL 和电子邮箱地址", "datasets_Custom_rule_replace": "替换掉连续的空格、换行符和制表符", "datasets_Custom_segmentID": "分段标识符", "datasets_Custom_segmentID_2linebreak": "2个换行", "datasets_Custom_segmentID_cn_exclamation": "中文叹号", "datasets_Custom_segmentID_cn_question": "中文问号", "datasets_Custom_segmentID_cnperiod": "中文句号", "datasets_Custom_segmentID_custom": "自定义", "datasets_Custom_segmentID_en_exclamation": "英文叹号", "datasets_Custom_segmentID_en_question": "英文问号", "datasets_Custom_segmentID_enperiod": "英文句号", "datasets_Custom_segmentID_linebreak": "换行", "datasets_ID_miss": "缺少知识库ID", "datasets_createFileModel_CancelBtn": "取消", "datasets_createFileModel_NextBtn": "下一步", "datasets_createFileModel_previousBtn": "上一步", "datasets_createFileModel_step1_CustomDescription": "自定义内容，支持创建&编辑", "datasets_createFileModel_step1_CustomTitle": "自定义", "datasets_createFileModel_step1_LocalDescription": "上传 PDF, TXT, MD, DOC, DOCX 格式的本地文件", "datasets_createFileModel_step1_LocalTitle": "本地文档", "datasets_createFileModel_step1_TabCustomDescription": "自定义内容，支持创建&编辑", "datasets_createFileModel_step1_TabCustomTitle": "自定义", "datasets_createFileModel_step1_TabLocalDescription": "上传Excel或者CSV格式的文档", "datasets_createFileModel_step1_TabLocalTitle": "本地文档", "datasets_createFileModel_step1_apiTitle": "API", "datasets_createFileModel_step1_urlTitle": "在线数据", "datasets_createFileModel_step2": "上传", "datasets_createFileModel_step2_UploadDoc": "点击上传或拖拽文档到这里", "datasets_createFileModel_step2_UploadDoc_description": "支持 {fileFormat}，最多可上传 {maxDocNum} 个文件，每个文件不超过 {filesize}， PDF 最多 {pdfPageNum} 页", "datasets_createFileModel_step3": "分段设置", "datasets_createFileModel_step3_auto": "自动分段与清洗", "datasets_createFileModel_step3_autoDescription": "自动分段与预处理规则", "datasets_createFileModel_step3_custom": "自定义", "datasets_createFileModel_step3_customDescription": "自定义分段规则、分段长度及预处理规则", "datasets_createFileModel_step4": "数据处理", "datasets_createFileModel_step4_Finish": "服务器处理完成", "datasets_createFileModel_step4_failed": "数据处理失败", "datasets_createFileModel_step4_processing": "服务器处理中", "datasets_createFileModel_tab_DataSheet": "数据表", "datasets_createFileModel_tab_dataStarRow": "数据起始行", "datasets_createFileModel_tab_dataStarRow_value": "第{LineNumber}行", "datasets_createFileModel_tab_header": "表头", "datasets_createFileModel_tab_step2": "表结构配置", "datasets_createFileModel_tab_step3": "预览", "datasets_create_btn": "创建知识库", "datasets_custom_segmentID_error": "分段标识符不可为空", "datasets_custom_segmentID_placeholder": "请输入分段标识符", "datasets_editProfile_title": "编辑知识库", "datasets_empty_description": "请先创建后添加", "datasets_empty_title": "暂无知识库", "datasets_frequencyModal_frequency": "更新频率", "datasets_frequencyModal_frequency_day": "{num, plural, other {每 # 天 }}", "datasets_frequencyModal_frequency_noUpdate": "不自动更新", "datasets_frequencyModal_whenUpdate": "更新时", "datasets_frequencyModal_whenUpdate_overwrite": "覆盖原有数据", "datasets_frequencyModal_whenUpdate_overwrite_keep": "保留原有数据并追加新数据", "datasets_model_create_avatar": "图标", "datasets_model_create_description": "描述", "datasets_model_create_description_placeholder": "输入数据集内容的描述", "datasets_model_create_name": "名称", "datasets_model_create_name_placeholder": "输入数据集名称", "datasets_model_create_title": "创建知识库", "datasets_placeholder_search": "搜索", "datasets_processing_notice": "以下 Data 正在处理，处理完成后智能体即可引用相关数据", "datasets_segment_Update": "更新频率", "datasets_segment_card_bit": "字符", "datasets_segment_card_hit": "{num, plural, other {# 命中}}", "datasets_segment_detailModel_save": "保存", "datasets_segment_detailModel_title": "# {num}", "datasets_segment_edit": "编辑", "datasets_segment_resegment": "重新分段", "datasets_segment_tableStructure_add_field": "增加字段", "datasets_segment_tableStructure_delTips": "索引字段不能删除", "datasets_segment_tableStructure_field_errEmpty": "字段名不能为空", "datasets_segment_tableStructure_field_name": "字段名", "datasets_segment_tableStructure_field_type_errEmpty": "请选择数据类型", "datasets_segment_tableStructure_field_value": "字段值", "datasets_segment_tableStructure_semantic_name": "索引", "datasets_segment_tableStructure_semantic_no": "否", "datasets_segment_tableStructure_semantic_yes": "是", "datasets_segment_tableStructure_title": "表结构", "datasets_segment_tag_auto": "自动分段", "datasets_segment_tag_custom": "自定义分段", "datasets_segment_tag_overwrite": "更新时覆盖原有数据", "datasets_segment_tag_overwriteNo": "更新时保留原有数据并追加新数据", "datasets_segment_tag_processing": "处理中", "datasets_segment_tag_segments": "{num}分段", "datasets_segment_tag_updateFrequency": "{num, plural, =1{每日更新} other{每 # 天更新}}", "datasets_segment_tag_updateNo": "不自动更新", "datasets_table_title_actions_delete": "删除", "datasets_title": "知识库", "datasets_unit_config_title1": "更新内容", "datasets_unit_exception_name_empty": "请输入名称", "datasets_unit_process_success": "处理完成", "datasets_unit_tableformat_tips1": "总{TotalRows}条记录，当前预览只展示前{ShowRows}条记录", "datasets_unit_update_exception_tips3": "上传一份Excel或CSV格式的文档，文件大小限制20MB以内。", "datasets_unit_update_retry": "重新上传", "datasets_unit_upload_fail": "上传失败", "datasets_unit_upload_field_action": "操作", "datasets_unit_upload_field_size": "文件大小", "datasets_unit_upload_field_update_frequency": "更新频率", "datasets_unit_upload_state": "上传中...", "datasets_unit_upload_success": "上传完成", "datasets_update_type": "数据更新类型", "datasets_url_empty": "内容不能为空", "datasets_url_saveSuccess": "保存成功", "db2_003": "上一步", "db2_004": "确认", "db2_005": "数据表名称是必填项", "db2_008": "必填字段需填写", "db2_009": "创建人", "db2_010": "所有人", "db2_011": "排序方式", "db2_012": "创建时间", "db2_013": "编辑时间", "db2_014": "搜索", "db2_015": "自定义数据表", "db2_016": "基于模板创建", "db2_017": "示例", "db2_018": "读书笔记记录模板", "db2_019": "此模板可用于保存读书笔记。开发人员使用此模板开发 Bot 后，用户记录的书名、章节和相应的笔记将存储在 Bot 内的表格结构中。用户在使用 Bot 时可以搜索相关信息", "db2_020": "用户在 Bot 中存储相应数据的示例", "db2_021": "用户", "db2_022": "保存《可能性的艺术》，第 3 章 - '在这个世界上，比痛苦更可怕的是未知的痛苦'", "db2_023": "智能体", "db2_024": "好的，笔记已保存", "db2_025": "选择数据库", "db2_027": "数据未保存提示", "db2_028": "留在当前页面", "db2_029": "您提交的数据还没有保存，确定是否离开页面？", "db2_030": "添加到智能体", "db2_031": "从智能体移除", "db_add_table_cust": "自定义数据表", "db_add_table_desc": "数据表描述", "db_add_table_desc_tips": "请介绍数据表的主要用途，让大语言模型更加理解此表的功能", "db_add_table_field_desc": "描述", "db_add_table_field_desc_tips": "对存储字段的补充说明，可以是对存储字段的自然语言描述、示例数据，也可以是格式说明等。 如，书名使用《XXX》", "db_add_table_field_name": "存储字段名称", "db_add_table_field_name_tips": "定义存储表格的“表头”。开发者定义好存储字段后，用户可在对应字段下存储相关数据。以下为一个读书笔记相关的存储字段示例：", "db_add_table_field_name_tips1": "格式：abc或abc123或abc_123", "db_add_table_field_name_tips2": "只能包含小写字母或数字或_", "db_add_table_field_name_tips3": "必须以英文字母开头", "db_add_table_field_name_tips4": "最多64字符", "db_add_table_field_necessary": "是否必要", "db_add_table_field_necessary_tips1": "必要字段：用户在保存一行数据时，必须提供对应字段信息，否则无法保存该行数据", "db_add_table_field_necessary_tips2": "非必要字段：缺失该字段信息时，一行数据仍可被保存在表中", "db_add_table_field_type": "数据类型", "db_add_table_field_type_bool": "Boolean", "db_add_table_field_type_int": "Integer", "db_add_table_field_type_number": "Number", "db_add_table_field_type_time": "Time", "db_add_table_field_type_tips": "选择存储字段对应的数据类型，智能体将按照开发者选择的数据类型，对用户输入的内容进行处理和保存", "db_add_table_field_type_txt": "String", "db_add_table_name": "数据表名称", "db_add_table_name_tips": "请输入数据表名称", "db_add_table_temp_desc": "用于保存读书笔记", "db_add_table_temp_field_desc1": "用于记录书籍的名称", "db_add_table_temp_field_desc2": "用于记录书籍章节", "db_add_table_temp_field_desc3": "用于记录读书笔记", "db_add_table_temp_preview": "效果预览", "db_add_table_temp_preview_tips": "用户在智能体中存储对应数据的效果示例：", "db_add_table_temp_tips": "新建表格-表格模板的提示信息", "db_add_table_temp_title": "新建表格-书摘记录模板", "db_add_table_temp_use": "新建表格-模板使用模板", "db_add_table_title": "新建数据表", "db_del_field_confirm_info": "删除存储字段后会导致用户对应数据被清空，请谨慎操作。", "db_del_field_confirm_no": "取消", "db_del_field_confirm_title": "删除存储字段后会导致用户对应数据被清空，确认删除吗？", "db_del_field_confirm_yes": "确认", "db_del_table_confirm_info": "删除后不可恢复，确认删除表吗？", "db_del_table_confirm_title": "确认删除吗？", "db_del_table_title": "删除表格", "db_edit_save": "保存", "db_edit_table_title": "编辑表", "db_edit_tips1": "删除已有字段，会导致用户原有数据被清空，请谨慎操作。", "db_edit_tips2": "不再提示", "db_edit_title": "编辑表格", "db_memory_entry_tips": "表格存储支持智能体开发者定义表结构，并将用户数据存储在表中。实现收藏夹、todo list、书籍管理、财务管理等功能。", "db_new_0001": "表结构", "db_new_0002": "线上数据", "db_new_0003": "编辑表结构", "db_new_0004": "数据库名称只允许小写字母、数字和下划线，并以小写字母开头", "db_notable_tips": "以表格结构组织数据，可实现类似书签和图书管理等功能。", "db_optimize_002": "数据产生或使用的渠道", "db_optimize_003": "数据插入的时间", "db_optimize_009": "测试数据", "db_optimize_010": "测试数据主要用于辅助调试\"业务逻辑\"与\"用户界面\"，与线上数据相互隔离", "db_optimize_011": "清空", "db_optimize_012": "刷新", "db_optimize_013": "批量导入", "db_optimize_014": "上传", "db_optimize_015": "表结构配置", "db_optimize_016": "预览", "db_optimize_017": "数据处理", "db_optimize_018": "上传文件前可下载模板，根据指引在模板中编辑", "db_optimize_019": "下载模板", "db_optimize_020": "上一步", "db_optimize_021": "下一步", "db_optimize_022": "增加行", "db_optimize_023": "编辑行", "db_optimize_024": "取消", "db_optimize_025": "插入", "db_optimize_026": "确定删除吗？", "db_optimize_027": "删除的数据不可恢复", "db_optimize_028": "删除", "db_optimize_029": "取消", "db_optimize_030": "批量删除", "db_optimize_031": "已选择 {n} 条数据", "db_optimize_032": "共计 {n} 行数据", "db_optimize_033": "变量设置", "db_optimize_034": "提示词调用设置", "db_optimize_035": "开启后数据表支持在提示词中访问，否则仅支持在工作流中访问", "db_optimize_036": "提示词调用", "db_optimize_037": "是", "db_optimize_038": "否", "db_table_0126_001": "取消", "db_table_0126_003": "下一步", "db_table_0126_004": "上一步", "db_table_0126_005": "完成", "db_table_0126_010": "导入Excel/CSV", "db_table_0126_011": "导入 Excel/CSV 以新建数据表", "db_table_0126_012": "上传", "db_table_0126_013": "表结构配置", "db_table_0126_014": "预览", "db_table_0126_015": "数据处理", "db_table_0126_016": "点击上传或拖动文件到此处", "db_table_0126_017": "上传一份 XLSX 或 CSV 格式的文档，文件大小限制20MB以内。", "db_table_0126_018": "文件名称", "db_table_0126_019": "状态", "db_table_0126_020": "文件大小", "db_table_0126_021": "操作", "db_table_0126_027": "最多{ColumNum}列，请删除不需要保留的列", "db_table_0126_028": "总{TotalRows}条记录，当前预览只展示前{ShowRows}条记录", "db_table_0126_029": "正在导入数据", "db_table_0126_031": "失败", "db_table_0126_032": "文件格式不支持", "db_table_0129_001": "Table查询模式", "db_table_0129_002": "单用户模式", "db_table_0129_003": "只读模式", "db_table_0129_004": "多用户模式", "db_table_0129_005": "单用户模式：开发者&用户，可以添加记录，仅能读/修改/删除自己创建的来自同渠道的数据", "db_table_0129_006": "只读模式：开发者可读/写/修改/删除、用户只读", "db_table_0129_007": "多用户模式：开发者&用户，可读/写/修改/删除表中来自同渠道的任何数据，由业务逻辑控制读写权限，注意仅在工作流节点中生效", "db_table_data_entry": "已存数据库", "db_table_entry": "数据库", "db_table_save_exception_fieldname": "存储字段名称重复", "db_table_save_exception_fieldtype": "请选择数据类型", "db_table_save_exception_nofield": "请至少定义一个存储字段", "db_table_save_exception_nofieldname": "请输入存储字段名称", "debug_area_time_label_dataset": "知识库", "debug_area_time_label_llm": "LLM", "debug_area_time_label_model": "模型", "debug_area_time_label_plugin": "插件", "debug_area_time_label_tool": "工具", "debug_asyn_task_notask": "无数据", "debug_asyn_task_task_status": "状态", "debug_asyn_task_task_status_failed": "失败", "debug_asyn_task_task_status_success": "成功", "debug_btn": "调试", "debug_copy_report": "一键反馈", "debug_copy_success": "信息已复制到剪切板", "debug_copy_suggestion": "建议截图智能体配置信息", "debug_detail_tab": "调试详情", "debug_skills": "技能", "default_test_set_default_test_set": "默认测试集", "default_test_set_default_test_set_release_tips": "发布后可以提供其他用户作为测试集使用", "default_test_set_select_test_set": "选择测试集", "delete": "删除", "delete_desc": "此操作不可撤回", "delete_mock_data": "删除模拟数据？", "delete_the_mockset": "删除模拟集？", "delete_title": "确认删除", "delete_tool": "删除工具", "describe_use_scenarios_of_mockset": "描述模拟集的使用场景", "develop_list_card_copy_fail": "复制创建失败", "develop_list_card_tag_agent": "智能体", "develop_list_card_tag_project": "应用", "develop_list_rank_tag_opened": "最近打开", "develop_team_team": "工作空间", "devops_publish_changelog_generate_stop": "停止", "devops_publish_multibranch_BotInfo.BackgroundImageInfoList": "背景图片", "devops_publish_multibranch_BotInfo.OnboardingInfo": "开场白", "devops_publish_multibranch_Current": "当前", "devops_publish_multibranch_ModelInfo.ModelResponseFormat.JSON": "JSON", "devops_publish_multibranch_ModelInfo.ModelResponseFormat.Markdown": "<PERSON><PERSON>", "devops_publish_multibranch_ModelInfo.ModelResponseFormat.Text": "文本", "devops_publish_multibranch_ModelInfo.ResponseFormat": "输出格式", "devops_publish_multibranch_NetworkError": "网络异常，请刷新页面", "devops_publish_multibranch_PersionDrafts": "个人草稿", "devops_publish_multibranch_PersionDraftsInfo": "每个协作者在开发过程中，拥有独立的草稿。", "devops_publish_multibranch_RecentSubmit": "最近提交", "devops_publish_multibranch_RetrieveAndMerge": "拉取与合入", "devops_publish_multibranch_RetrieveAndMergeInfo": "多人同时修改时，支持拉取其他人提交的版本合入到自己的草稿。", "devops_publish_multibranch_Save": "保存", "devops_publish_multibranch_VersionControl": "支持提交版本", "devops_publish_multibranch_VersionControlInfo": "版本需要先提交后再发布。", "devops_publish_multibranch_auto_saved": "草稿自动保存于{time}", "devops_publish_multibranch_changes": "变更", "devops_publish_multibranch_changeset_add": "增加", "devops_publish_multibranch_changeset_delete": "删除", "devops_publish_multibranch_changeset_modify": "修改", "devops_publish_multibranch_changeset_remove": "移除", "devops_publish_multibranch_changetype": "变更类型", "devops_publish_multibranch_diffwithin": "与{connectorName}的差异", "devops_publish_multibranch_done": "完成", "devops_publish_multibranch_i_know": "我知道了", "devops_publish_multibranch_nodiff": "无差异", "devops_publish_multibranch_property": "属性", "devops_publish_multibranch_publish_disabled_tooltip": "多个开发者正在搭建这个智能体。在发布之前，请先将其提交到工作区。", "devops_publish_multibranch_viewdiff": "查看差异", "dialog_240305_01": "确认清空数据？", "dialog_240305_02": "数据清空后不可恢复", "dialog_240305_03": "确认", "dialog_240305_04": "取消", "dislike": "踩", "dislike_feedback_placeholder": "(可选) 请具体说明不满意的原因，或告诉我们满意的预期", "dislike_feedback_tag_harm": "有害信息", "dislike_feedback_tag_mislead": "信息有误", "dislike_feedback_tag_unfollow_instruction": "未遵循指令", "dislike_feedback_tag_unfollow_others": "其他...", "dislike_feedback_title": "告诉我们不满意的原因，帮助我们做的更好", "display_on_vertical_screen": "竖屏展示", "display_on_widescreen": "宽屏展示", "do_not_remind_again": "不再提醒", "drill_down_placeholer_select": "选择", "duplicate": "创建副本", "duplicate_rename_copy": "副本", "duplicate_select_workspace": "工作空间", "duplicate_tools_within_plugin": "插件内存在重复工具", "dy_avatar_add_workflow_limit": "只能添加当前 AI 分身下的工作流", "dy_avatar_evaluation_publish_tip": "请填写分身垂类后再发布", "dy_avatar_resource_add": "添加到抖音分身", "dy_avatar_resource_add_tip": "是否添加到抖音分身", "dy_avatar_resource_delete": "从抖音分身移除", "edit_block_api_disable_tooltips": "此资源当前不可用。请切换到其他可访问的资源。", "edit_block_api_empty": "暂无已配置的技能", "edit_block_api_imageflow": "图像流", "edit_block_api_knowledge_image": "照片", "edit_block_api_knowledge_table": "表格", "edit_block_api_knowledge_text": "文本", "edit_block_api_plugin": "插件", "edit_block_api_rename": "更新名称", "edit_block_api_workflow": "工作流", "edit_block_default_guidance_text": "请在此处输入提示词", "edit_block_guidance_text_placeholder": "请输入编辑块内容为空时的提示文案", "edit_block_guidance_text_when_empty": "空白引导", "edit_block_guild_describe": "通过编辑块来强调提示词中建议用户修改的区域，可以提供更清晰的使用引导。", "edit_block_guild_title": "编辑块", "edit_block_prefilled_text": "预设文本", "edit_block_set_as_edit_block": "设置为编辑块", "edit_mock_data": "编辑模拟数据", "edit_mockset": "编辑模拟集", "edit_pat_1": "编辑个人访问令牌", "edit_prompt": "编辑提示词", "edit_time": "编辑时间", "edit_variables_modal_cancel_text": "取消", "edit_variables_modal_ok_text": "保存", "edit_variables_modal_title": "编辑变量", "editor_toolbar_image": "图片", "enter_raw_content_or_url": "输入cURL，command Swagger，openAPI的原始内容，或指向它们的URL", "enterprise_id": "企业ID", "enterprise_sso_seetings_page_desc_button1": "修改配置", "enterprise_workspace_default_tips1_nonspace": "暂未加入任何空间，可在【团队/企业管理】-【空间管理】处选择空间", "enterprise_workspace_default_tips2_toast": "未找到空间，请先创建空间", "enterprise_workspace_no_space_title": "暂无空间", "error": "抱歉，发生了一些错误，请稍后重试。", "error_id_copy_success": "错误 ID 复制成功", "errorpage_bot_btn": "返回 Coze", "errorpage_bot_title": "无法查看智能体", "errorpage_subtitle": "请检查你的网址或加入对应工作空间后重试", "eval_status_referenced": "已引用", "event_type": "事件类型", "exit": "退出", "expand": "展开", "expire_time_1": "过期时间", "expired_time_days_1": "{num, plural, other {{num}天（{date}）}}", "expired_time_forbidden_1": "请谨慎选择过期时间，个人访问令牌生成后，将不支持修改", "explore_bot_category_all": "全部", "explore_tools": "探索工具", "failed": "抱歉，发生了一些错误...", "failed_to_import_tool": "{num, plural, =1 {1个工具} other {#个工具}}导入失败 ", "feedback_submit": "提交", "file_format_not_supported": "不支持的文件格式", "file_name_cannot_be_empty": "文件名不能为空", "file_name_exist": "文件名已存在", "file_too_large": "文件大小不可超过 {max_size}", "file_upload_success": "上传成功", "filebox_0002": "照片", "filebox_0003": "文档", "filebox_0007": "复制名字", "filebox_0008": "照片名字已经复制到剪贴板", "filebox_0010": "改名", "filebox_0013": "删除照片？", "filebox_0016": "照片已删除", "filebox_0017": "还没有照片", "filebox_0018": "名字", "filebox_0020": "上传时间", "filebox_0022": "删除文档？", "filebox_0023": "文档名字已经复制到剪贴板", "filebox_0024": "文档已删除", "filebox_0025": "还没有文档", "filebox_0040": "全部删除", "filebox_0042": "删除文件夹", "filebox_0047": "未命名文件", "filebox_010": "未找到相关图片", "filebox_011": "未找到相关文档", "files_exceeds_limit": "文件数量超出限制", "filter_all": "全部", "filter_develop_agent": "智能体", "filter_develop_all_creators": "所有人", "filter_develop_all_types": "全部", "filter_develop_created_by_me": "我创建的", "filter_develop_project": "应用", "filter_develop_recent_opened": "最近打开", "flowcanvas_shortcuts_backspace": "退格键", "flowcanvas_shortcuts_click": "点击", "flowcanvas_shortcuts_copy": "复制", "flowcanvas_shortcuts_delete": "删除", "flowcanvas_shortcuts_drag": "拖拽", "flowcanvas_shortcuts_duplicate": "创建副本", "flowcanvas_shortcuts_move_canvas": "移动画布", "flowcanvas_shortcuts_multiple_deselect": "反选", "flowcanvas_shortcuts_multiple_select": "多选", "flowcanvas_shortcuts_or": "或", "flowcanvas_shortcuts_paste": "粘贴", "flowcanvas_shortcuts_scroll": "鼠标滚轮", "flowcanvas_shortcuts_shortcuts": "快捷键", "flowcanvas_shortcuts_space": "空格键", "flowcanvas_shortcuts_zoom_in": "放大", "flowcanvas_shortcuts_zoom_out": "缩小", "form_mode": "表单模式", "free_chat_allowance": "免费聊天津贴", "free_chat_allowance_tips": "支持0-100免费聊天津贴，消息费用由机器人创建者承担。", "generate": "生成", "generate_bot_icon_content_filter": "生成失败：请检查名称和介绍是否不符合规范或表意不明确", "generate_failed": "未能生成表，请更改描述后重试", "generate_randomly_based_on_data_type_name": "根据数据类型和名称随机生成", "generating": "生成中", "go": "go", "good_mockset_name_descriptive_concise": "一个好的模拟集名称应该是描述性的和简洁的", "google": "谷歌", "got_it": "我知道了", "guidance_got_it": "知道了", "home_favor_desc1": "还没有收藏任何内容", "home_favor_desc2": "点击⭐️按钮可将内容添加到这里~", "horizontal": "横向", "http_node_auth_notopen": "暂未开启鉴权", "image_download_not_supported": "抱歉，该图片暂不支持下载。", "image_list": "图片列表", "imageflow_add": "添加图像流", "imageflow_add_toast_error": "获取图像流信息失败", "imageflow_canvas_a41": "A4(横)", "imageflow_canvas_a42": "A4(竖)", "imageflow_canvas_align1": "左对齐", "imageflow_canvas_align2": "水平居中", "imageflow_canvas_align3": "右对齐", "imageflow_canvas_align4": "顶部对齐", "imageflow_canvas_align5": "垂直居中", "imageflow_canvas_align6": "底部对齐", "imageflow_canvas_align7": "水平均分", "imageflow_canvas_align8": "垂直均分", "imageflow_canvas_cancel_change": "取消上传", "imageflow_canvas_change_img": "更换图片", "imageflow_canvas_change_text": "点击编辑文本预览 Click to edit text preview", "imageflow_canvas_circle": "圆形", "imageflow_canvas_color": "画板颜色", "imageflow_canvas_copy": "拷贝", "imageflow_canvas_desc": "自定义画板排版，支持引用添加文本和图片", "imageflow_canvas_double_click": "双击开始编辑", "imageflow_canvas_down_1": "下移一层", "imageflow_canvas_draw": "画笔模式", "imageflow_canvas_edit": "画板编辑", "imageflow_canvas_element_desc": "元素值", "imageflow_canvas_element_name": "元素名", "imageflow_canvas_element_set": "元素设置", "imageflow_canvas_elment_tooltip": "在画板添加引用元素，支持引用前序节点的输出", "imageflow_canvas_fill": "填充样式", "imageflow_canvas_fill1": "自适应", "imageflow_canvas_fill2": "比例填充", "imageflow_canvas_fill3": "拉伸填充", "imageflow_canvas_fill_image": "内容", "imageflow_canvas_fill_mode": "填充模式", "imageflow_canvas_fill_preview": "预览", "imageflow_canvas_frame": "画板尺寸", "imageflow_canvas_height": "高", "imageflow_canvas_line": "直线", "imageflow_canvas_line_style": "线条样式", "imageflow_canvas_paste": "粘贴", "imageflow_canvas_rect": "矩形", "imageflow_canvas_reference": "引用", "imageflow_canvas_restart": "重置视图", "imageflow_canvas_select_var": "选择变量", "imageflow_canvas_setting": "画板设置", "imageflow_canvas_stroke": "描边样式", "imageflow_canvas_stroke_width": "描边粗细", "imageflow_canvas_style_tooltip": "样式", "imageflow_canvas_text1": "单行文本", "imageflow_canvas_text2": "区块文本", "imageflow_canvas_text_default": "文本", "imageflow_canvas_text_tooltip1": "字号", "imageflow_canvas_text_tooltip2": "行高", "imageflow_canvas_to_back": "置于底层", "imageflow_canvas_to_front": "置于顶层", "imageflow_canvas_top_1": "上移一层", "imageflow_canvas_transparency": "透明度", "imageflow_canvas_trian": "三角形", "imageflow_canvas_var_add": "在画布中添加变量", "imageflow_canvas_var_delete": "变量被删除", "imageflow_canvas_var_no": "无变量", "imageflow_canvas_var_reference": "已引用{n}次", "imageflow_canvas_width": "宽", "imageflow_create": "创建图像流", "imageflow_create_description": "图像流描述", "imageflow_create_description_placeholder": "请输入描述，让大模型理解什么情况下应该调用此图像流", "imageflow_create_name": "图像流名称", "imageflow_create_name_placeholder": "请输入图像流名称", "imageflow_create_name_wrong_format": "图像流名称只允许字母、数字和下划线，并以字母开头", "imageflow_create_toast_success": "图像流创建成功", "imageflow_detail_no_search_result": "未找到图像流", "imageflow_detail_toast_createcopy_succeed": "已创建图像流副本", "imageflow_edit": "编辑图像流", "imageflow_explore": "探索图像流", "imageflow_generation_desc1": "默认为25，范围[1,40]，数值越大画面越精细，生成时间越久", "imageflow_generation_desc4": "编辑图像模型的提示词以生成内容", "imageflow_input_upload": "上传", "imageflow_input_upload_placeholder": "上传图片", "imageflow_output_display": "图片预览", "imageflow_output_display_desc1": "运行后可展示图片预览", "imageflow_output_display_desc2": "图片正在生成中...", "imageflow_output_display_desc3": "图片生成失败，请重试", "imageflow_output_display_save": "保存图片", "imageflow_title": "图像流", "imageflow_title_description": "图像流支持通过可视化的方式，对图像获取、生成、编辑和发布等功能进行组合，从而实现稳定的图像处理流程的编排。", "imageflow_upload_action": "点击或拖拽图片上传", "imageflow_upload_action_common": "点击或拖拽文件上传", "imageflow_upload_error": "图片上传失败，请重试", "imageflow_upload_error1": "图片宽高比不符合要求", "imageflow_upload_error2": "图片高度不能小于{value}", "imageflow_upload_error3": "图片宽度不能小于{value}", "imageflow_upload_error4": "图片高度不能超过{value}", "imageflow_upload_error5": "图片宽度不能超过{value}", "imageflow_upload_error6": "图片加载异常", "imageflow_upload_error7": "图片上传超时", "imageflow_upload_error_type": "图片格式需为{type}", "imageflow_upload_exceed": "上传文件大小不得超过{size}", "imageflow_upload_type": "支持{type}格式", "imageflow_workspace2": "工作空间图像流", "import": "导入", "import_plugin": "导入插件", "import_plugin_tool": "导入工具", "inappropriate_contents": "请检查是否包含不符合规范的内容。", "inifinit_list_empty_title": "没有内容", "inifinit_list_load_fail": "加载失败", "inifinit_list_retry": "重试", "inifinit_search_not_found": "没有找到内容", "input_component": "输入组件", "insert": "插入", "intelligently_generated_by_large_language_model": "基于模拟集名称和描述，使用大语言模型生成，可能存在偏差", "invalid_database": "无效数据库", "jinja_invalid": "人设与回复逻辑中包含不支持的语法，例如双花括号内包含非法变量命名，请删除或根据扣子 OpenAPI 开发文档修改后重试。", "keep": "保留", "kl2_002": "知识库内最多支持1万个分段", "kl2_003": "知识库下最多支持被添加300个文档", "kl2_004": "将对已添加的所有文档进行分段,时间可能相对较长,确认要重新分段吗?", "kl2_006": "删除文档", "kl2_007": "是否确认删除？", "kl2_009": "{num, plural, other {#个文档}}", "kl2_010": "全部类型", "kl2_011": "文档", "kl2_012": "表格", "kl2_013": "添加到智能体", "kl2_014": "从智能体移除", "kl_write_003": "请添加知识库到此节点，仅支持添加一个文档类型知识库", "kl_write_004": "快速解析", "kl_write_005": "不会对文档提取图像、表格等元素，适用于纯文本", "kl_write_006": "精准解析", "kl_write_007": "将从文档中提取图片、表格等元素，需要耗费更长的时间", "kl_write_008": "图片元素", "kl_write_009": "扫描件（OCR）", "kl_write_010": "表格元素", "kl_write_011": "分段策略", "kl_write_012": "自动分段", "kl_write_013": "基于内容段落的自动化切分", "kl_write_014": "分段重叠度%", "kl_write_015": "相邻切片之间的重叠度比例,重叠切片可以确保重要信息不会在切片边界处丢失,为模型提供更多上下文", "kl_write_017": "向量索引", "kl_write_018": "将每个切片及其对应的向量存储在向量数据库中,适用于基于语义的模糊查询。", "kl_write_019": "向量模型", "kl_write_020": "关键词索引", "kl_write_021": "构建从每个关键字到对应切片的映射，适用于基于关键词的精准查询。", "kl_write_022": "表格 SQL 查询", "kl_write_023": "同步将查询的自然语言转为 SQL 语句进行查询；SQL 执行结果与 RAG 召回段落一同输入给模型", "kl_write_024": "查询改写", "kl_write_025": "对输入的查询语句进行优化重构，从而能够更准确地捕捉用户意图，提升信息检索的效率", "kl_write_026": "结果重排", "kl_write_027": "根据相关性或质量对检索到的文档切片进行重新排序,以提高生成答案的准确性", "kl_write_028": "仅查看个人文档", "kl_write_029": "开启后用户只可以搜索用户自己上传的和开发者预置的文档，其他人上传的文档不可见", "kl_write_030": "按照所选的标识符切分文本", "kl_write_031": "切分后单个切片允许的最大长度", "kl_write_032": "文档解析策略", "kl_write_033": "数据写入设置", "kl_write_034": "示例参考", "kl_write_035": "假设用户在一个对话系统中先前提到：", "kl_write_036": "“我最近在学习Python编程。”", "kl_write_037": "然后用户接着问：", "kl_write_038": "“我该如何开始？”", "kl_write_039": "在这个上下文中，系统可以将查询改写为：", "kl_write_040": "“我应该从哪些Python学习资源或项目开始?”", "kl_write_041": "切片{index}：", "kl_write_042": "介绍意大利面的历史。", "kl_write_043": "讨论了不同种类的意大利面和它们的搭配。", "kl_write_044": "详细描述了制作意大利面的步骤,包括所需材料和烹饪技巧。", "kl_write_045": "提供了一些意大利面食谱。", "kl_write_046": "假设我们有一个用户查询:\"如何制作意大利面?\"首先会从知识库中检索得到以下几个文档片段,其中先按照ABCD先后顺序排列：", "kl_write_047": "在结果重排的过程中,通过分析用户查询的意图,重新排序切片顺序,使得最相关的内容排在前面。最终的排序可能变为：", "kl_write_100": "提取内容", "kl_write_102": "内容过滤", "kl_write_103": "设置过滤内容", "kl_write_104": "未设置任何过滤内容", "kl_write_105": "文档列表", "kl_write_106": "过滤此页", "kl_write_107": "创建设置", "kl_write_108": "完成创建", "kl_write_109": "创建并导入", "knowledg_table_increment_tips": "注意：新上传文件的Schema需要与现有table的schema对齐，否则上传失败", "knowledg_table_segments_content": "段落内容", "knowledg_table_structure_err_msg": "表头与当前表结构不一致,列名称及顺序需保持一致", "knowledg_table_structure_tips": "表头需要与现有表格结构保持一致。", "knowledg_unit_add_segments": "添加内容", "knowledge-3rd-party-feishu": "飞书", "knowledge-3rd-party-google-drive": "Google Drive", "knowledge-3rd-party-notion": "Notion", "knowledge-dataset-type-all": "全部", "knowledge-dataset-type-table": "表格", "knowledge-dataset-type-text": "文本", "knowledge_1218_001": "最多{MaxDocs}个文档", "knowledge_1221_02": "未读取到数据", "knowledge_1221_03": "正在读取数据", "knowledge_1222_01": "表格类型最多50列，请删除不需要保留的列", "knowledge_1226_001": "索引", "knowledge_add_btn_by_workflow": "添加到工作流", "knowledge_add_content_processing_tips": "请在服务处理完成后添加新的内容", "knowledge_add_unit_process_notice": "点击确认不影响数据处理，处理完毕后可进行引用", "knowledge_bot_update_databse_tnserr_msg": "数据库的名称、描述包含不适当的内容，请修改后重新提交", "knowledge_call_method_tooltip": "选择是否每轮对话自动召回或按需从特定知识库召回", "knowledge_content_illegal_error_msg": "内容不符合平台规范", "knowledge_custom_add_content": "添加值", "knowledge_detail_doc_rename": "重命名", "knowledge_document_view": "查看文档失败，请稍后重试", "knowledge_edit_unit_name_title": "编辑名称", "knowledge_es_001": "云搜索服务", "knowledge_es_024": "知识库存储失效，请前往知识库详情修改存储配置后重试", "knowledge_feishu_10": "授权", "knowledge_full_text_search_title": "全文", "knowledge_full_text_search_tooltip": "依赖于关键词的全文搜索，推荐在搜索具有特定名称、缩写词、短语或ID的场景使用。", "knowledge_hierarchies_categories": "支持对层级的操作类别", "knowledge_hierarchies_categories_01": "不能调整至此处", "knowledge_hierarchies_categories_02": "找不到合并主节点", "knowledge_hierarchies_categories_03": "不能合并标题节点", "knowledge_hierarchies_categories_04": "找不到要合并的节点", "knowledge_hybird_search_title": "混合", "knowledge_hybird_search_tooltip": "结合全文检索与语义检索的优势，并对结果进行综合排序", "knowledge_insert_img_001": "内容预览", "knowledge_insert_img_002": "插入图片", "knowledge_insert_img_003": "来源：{url}", "knowledge_insert_img_004": "查看", "knowledge_insert_img_005": "图片 URL", "knowledge_insert_img_006": "单击此处上传或拖动文件", "knowledge_insert_img_007": "重新上传并替换当前图片", "knowledge_insert_img_009": "正在上传", "knowledge_insert_img_010": "Image", "knowledge_insert_img_011": "带有图片的列无法设置为索引", "knowledge_insert_img_013": "最大上传 20M", "knowledge_level_001": "按层级分段", "knowledge_level_004": "分段层级", "knowledge_level_005": "检索切片保留层级信息", "knowledge_level_010": "原始文档预览", "knowledge_level_011": "分段预览", "knowledge_level_012": "文档列表", "knowledge_level_016": "层级分段", "knowledge_level_028": "删除", "knowledge_level_029": "合并为一个分段", "knowledge_level_030": "预览原始文档", "knowledge_level_110": "图片内容", "knowledge_level_adjust": "分段层级", "knowledge_limit_20": "表格类型单元最多50列，请删除不需要保留的列", "knowledge_multi_index": "1、用户Query会和索引字段内容做对比,根据相似度匹配。\n2、支持设置多个索引,用于匹配，到更多字段的信息。\n3、注意:过多的索引可能会导致精准度的下降。", "knowledge_multi_index_noti": "请至少配置1个索引,用于匹配用户Query", "knowledge_new_001": "查看或调整配置", "knowledge_new_002": "存在过滤内容", "knowledge_no_result": "无结果", "knowledge_optimize_0010": "新增一行", "knowledge_optimize_005": "确定加载最新内容？", "knowledge_optimize_006": "当加载最新内容，当前内容将被覆盖", "knowledge_optimize_007": "确定", "knowledge_optimize_008": "批量加载最新内容", "knowledge_optimize_009": "批量加载最新内容后，所有网页或三方数据来源的文档导入的内容将被覆盖", "knowledge_optimize_010": "所有网页或三方数据来源的文档", "knowledge_optimize_014": "批量设置更新频率", "knowledge_optimize_015": "批量设置更新频率后，所有网页或三方数据来源的文档将统一更新频率", "knowledge_optimize_016": "下方添加分段", "knowledge_optimize_017": "上方添加分段", "knowledge_optimize_019": "{n} 个智能体正在引用", "knowledge_optimize_020": "请稍后重试", "knowledge_optimize_099": "unauthorized", "knowledge_photo_001": "照片类型", "knowledge_photo_002": "本地图片", "knowledge_photo_003": "上传 JPG，JPEG，PNG 格式的图片", "knowledge_photo_004": "点击上传或拖拽图片到这里", "knowledge_photo_005": "支持 JPG，JPEG，PNG，每个文件不超过20 MB", "knowledge_photo_006": "上传图片", "knowledge_photo_007": "标注设置", "knowledge_photo_008": "智能标注", "knowledge_photo_009": "深度理解图片，自动提供全面详细的内容描述信息", "knowledge_photo_010": "人工标注", "knowledge_photo_011": "不执行处理，在图片导入完成后，手动添加图片描述", "knowledge_photo_013": "已标注", "knowledge_photo_014": "未标注", "knowledge_photo_015": "{num} 张图片", "knowledge_photo_016": "未标注数据将无法被检索召回", "knowledge_photo_019": "编辑描述信息", "knowledge_photo_020": "自动生成", "knowledge_photo_021": "自动生成确认", "knowledge_photo_022": "自动生成标注信息后，当前已有内容将被全部替换，是否确认执行？", "knowledge_photo_025": "照片", "knowledge_photo_026": "请输入照片的描述信息", "knowledge_photo_027": "照片上传到知识库后自动/手动添加语义描述，智能体可以基于照片的描述匹配到最合适的照片", "knowledge_photo_illegal_error_msg": "图片涉嫌违规禁止访问", "knowledge_remove_btn_by_workflow": "从工作流移除", "knowledge_search_strategy_title": "搜索策略", "knowledge_search_strategy_tooltip": "从知识库中获取知识的检索方式，不同的检索策略可以更有效地找到正确的信息，提高其生成的答案的准确性和可用性。", "knowledge_segment_config_table": "配置表结构", "knowledge_semantic_search_title": "语义", "knowledge_semantic_search_tooltip": "基于向量的文本相关性查询，推荐在需要理解语义关联度和跨语言查询的场景使用。", "knowledge_source_card_0001": "文本内容", "knowledge_source_card_0002": "卡片", "knowledge_source_card_0003": "引用来源在答案底部通过文本内容展示，所有应用端均可见", "knowledge_source_card_0004": "引用来源通过卡片独立展示，可展示应用端范围与卡片支持的范围对齐", "knowledge_source_display_status": "显示来源", "knowledge_source_display_title": "来源", "knowledge_source_display_tooltip_content": "扣子中的知识是指允许您的智能体与您的数据进行交互的强大功能。它提供了易于使用的知识库功能，使智能体能够处理和管理数据，无论是来自数十万字的 PDF 还是来自网站的实时信息[1]", "knowledge_source_display_tooltip_link": "扣子文档：知识库介绍", "knowledge_source_display_tooltip_title": "将召回的知识库原始切片呈现给最终用户并支持他们查看源文件。", "knowledge_tableStructure_errSystemField": "不允许使用系统字段", "knowledge_tableStructure_field_errLegally": "不能包含单引号、双引号、反引号、转义字符", "knowledge_table_content_empty": "内容不能为空", "knowledge_table_content_limt": "内容不能超过{number}个字符", "knowledge_table_custom_submit_tips": "请确保所有的索引列都已经正确填写，值不能为空", "knowledge_table_nl2sql_tooltip": "表格知识库支持基于索引列的匹配(表格按行进行划分)，同时也支持基于NL2SQL的查询和计算", "knowledge_table_structure_column_name": "列名", "knowledge_table_structure_column_tooltip": "目前仅支持从最多前 3 个 sheet 及前 50 行内选择对表头进行配置，设定为索引的列的数据会被转换为 string 类型", "knowledge_table_structure_data_type": "数据类型", "knowledge_table_structure_desc": "描述", "knowledge_table_structure_semantic": "索引", "knowledge_tableview_01": "查看 & 编辑行", "knowledge_tableview_02": "删除行", "knowledge_tableview_03": "新增片段成功", "knowledge_upload_create_title": "新增知识库", "knowledge_upload_format_error": "不支持该文件格式", "knowledge_upload_remaining_time_text": "剩余时间{minutes}分{seconds}秒", "knowledge_upload_text_custom_add_title": "文本填写", "knowledge_upload_text_custom_doc_content": "文档内容", "knowledge_upload_text_custom_doc_content_tips": "请输入文档内容", "knowledge_upload_text_custom_doc_name": "文档名称", "knowledge_upload_text_custom_doc_name_tips": "请输入文档名称", "knowledge_variable_description_placeholder": "描述", "knowledge_weixin_001": "公众号", "knowledge_weixin_015": "不自动追加", "knowledge_weixin_016": "每天", "knowledge_weixin_017": "每三天", "knowledge_weixin_018": "每七天", "knowledge_write_tips_doc": "需要写入知识库的文档，必须满足 File-Doc 类型", "knowledge_writing_101": "所有上传的文档将存入选择知识库中", "knowlege_qqq_001": "分段预览", "knowlege_qqq_002": "处理中", "knowlege_qqq_003": "处理失败", "landing_mobile_popup_button": "好的", "landing_mobile_popup_context": "此页面还没有适配当前的显示尺寸，建议在PC上使用，并调整为合适的尺寸", "landing_mobile_popup_title": "提示", "landingpage_description": "扣子是新一代 AI 大模型智能体开发平台。整合了插件、长短期记忆、工作流、卡片等丰富能力，扣子能帮你低门槛、快速搭建个性化或具备商业价值的智能体，并发布到豆包、飞书等各个平台。", "language": "语言", "latest_version": "当前版本", "layer": "图层", "left": "左侧", "level_997": "支持对切片的删除", "level_998": "按照层级合并为切片", "level_999": "拖拽调整层级结构", "library_actions": "操作", "library_actions_enable": "启用", "library_delete_desc": "删除后，历史引用了本资源的智能体或工作流将自动取消引用，且此操作不可撤回。", "library_edited_time": "编辑时间", "library_empty_clear_filters": "清空筛选", "library_empty_no_results_found_under": "当前筛选状态下未能找到相关结果", "library_filter_tags_all_creators": "所有人", "library_filter_tags_all_status": "全部", "library_filter_tags_all_types": "所有类型", "library_filter_tags_collaboration": "已开启多人协作", "library_filter_tags_created_by_me": "由我创建", "library_filter_tags_disabled": "已停用", "library_filter_tags_image": "照片", "library_filter_tags_processing": "处理中", "library_filter_tags_published": "已发布", "library_filter_tags_table": "表格", "library_filter_tags_text": "文本", "library_filter_tags_unpublished": "未发布", "library_name": "资源", "library_resource": "资源", "library_resource_detail_back": "返回", "library_resource_type_knowledge": "知识库", "library_resource_type_plugin": "插件", "library_resource_type_prompt": "提示词", "library_resource_type_workflow": "工作流", "library_type": "类型", "library_workflow_header_reference_graph_entry_hover_no_reference": "暂无引用关系", "library_workflow_header_reference_graph_entry_hover_view_graph": "查看引用关系", "like": "赞", "llm": "大语言模型", "llm_mode": "大语言模型模式", "loading": "加载中...", "local_file": "本地文件", "local_plugin_label": "端插件", "location": "位置", "log_out_desc": "确认要退出登录吗？", "log_pay_wall_download_tips": "升级到对应套餐权益后可下载该周期数据", "login_button_text": "登录", "login_failed": "登录失败", "ltm_240227_01": "从长期记忆召回的内容", "ltm_240617_02": "这个智能体的长期记忆开关状态：", "ltm_240826_01": "输入需要从长期记忆中匹配的关键信息。", "ltm_240826_02": "从长期记忆中返回的，最匹配输入信息的记忆内容。", "manage_mockset": "管理模拟集", "margin": "外边距", "markdown_bold": "加粗", "markdown_bold_syntax": "**{text}** {space}", "markdown_bulletedlist": "无序列表", "markdown_bulletedlist_syntax": "- {space}", "markdown_code": "代码", "markdown_code_syntax": "`{code}` {space}", "markdown_codeblock": "代码块", "markdown_codeblock_syntax": "``` {space} 或 ``` 代码语言 {space}", "markdown_heading1": "一级标题", "markdown_heading1_syntax": "# {space}", "markdown_heading2": "二级标题", "markdown_heading2_syntax": "## {space}", "markdown_heading3": "三级标题", "markdown_heading3_syntax": "### {space}", "markdown_intro": "你可以在这里使用 Markdown 语法来为文本添加格式，例如输入“**文本**”以加粗。具体 Markdown 语法请参考下表。", "markdown_is_supported": "支持 Markdown", "markdown_italic": "斜体", "markdown_italic_syntax": "*{text}* {space}", "markdown_numberedlist": "有序列表", "markdown_numberedlist_syntax": "1. {space}", "markdown_quote": "引用", "markdown_quote_syntax": "> {space}", "markdown_strickthrough": "删除线", "markdown_strickthrough_syntax": "~~{text}~~ {space}", "menu_bots": "智能体", "menu_datasets": "知识库", "menu_documents": "文档", "menu_plugins": "插件", "menu_profile_account": "账号", "menu_title_personal_space": "个人空间", "menu_title_store": "探索", "menu_widgets": "卡片", "menu_workflows": "工作流", "merge_duplicate_tools": "合并重复工具", "message_content_error": "该消息格式存在错误，暂不支持展示", "message_tool_regenerate": "重新生成", "mkl_plugin_created": "创建于", "mkl_plugin_publish": "发布于", "mkl_plugin_to_plugin_gallery": "探索插件", "mkl_plugin_updated": "更新于", "mkpl_bot_duplicate_tips": "智能体所使用的工作流暂不支持同步复制，敬请期待！", "mkpl_bots_category": "分类", "mkpl_bots_private_configuration": "私有配置", "mkpl_bots_private_configuration_description": "通过将您的智能体设置为“私有配置”，扣子的其他用户将无法看到您的智能体的系统 Prompt，但是他们可以与您的智能体聊天并查看您的机器人发布的第三方平台及其公共资源，包括任何集成的插件。", "mkpl_bots_public_configuration": "公开配置", "mkpl_bots_public_configuration_description": "通过将您的智能体设置为“公开配置”，根据我们的服务条款，代表您授予了扣子的开发者和用户使用您的智能体和内容来创建智能体的权利。", "mkpl_bots_visibility": "可见度", "mkpl_favorite": "收藏", "mkpl_load_btn": "更多", "mkpl_num_favorites": "收藏", "mkpl_plugin_delisted_tips": "该插件已被下架，无法使用", "mkpl_plugin_detail": "插件详情", "mkpl_plugin_disable_delete": "请将您的插件下架后再删除", "mkpl_plugin_tool_parameter_description": "参数说明", "mkpl_plugin_tooltip_official": "扣子官方插件", "mkpl_published": "最近发布", "mkpl_send_tooltips": "发送", "mobile": "移动端", "mock_data_counts": "数据数量", "mock_data_quantity": "模拟数据数量", "mock_enable_switch": "启用 Mockset 将不会向插件发送真实请求，而是返回您选择的模拟数据", "mockdata_field_empty": "必填字段 “{fieldName}” 不可以为空", "mockset": "模拟集", "mockset_data": "模拟集数据", "mockset_description": "模拟集描述", "mockset_field_is_required": "{field}为必填", "mockset_invaild_tip": "{MockSetName} 不再有效", "mockset_is_empty_add_data_before_use": "模拟集为空，请在使用前添加数据", "mockset_label_tip": "模拟集: {MockSetName}", "mockset_name": "模拟集名称", "mockset_of_toolname": "{toolName}的模拟集", "mockset_save": "保存", "mockset_save_description": "使用该插件的Project/Agent发布后将会自动切换为真实数据", "mockset_tip_data_will_lose": "填写的内容将丢失", "mockset_toast_data_size_limit": "输入的数据大小超过限制（100k），请修改", "model_config_generate_advance": "高级设置", "model_config_generate_balance": "平衡模式", "model_config_generate_creative": "创意模式", "model_config_generate_customize": "自定义", "model_config_generate_explain": "**精确模式**: \n\n- 严格遵循指令生成内容\n- 适用于需准确无误的场合，如正式文档、代码等\n\n**平衡模式**: \n\n- 在创新和精确之间寻求平衡\n- 适用于大多数日常应用场景，生成有趣但不失严谨的内容\n\n**创意模式**: \n\n- 激发创意，提供新颖独特的想法\n- 适合需要灵感和独特观点的场景，如头脑风暴、创意写作等\n\n**自定义模式**: \n\n- 通过高级设置，自定义生成方式\n- 根据需求，进行精细调整，实现个性化优化", "model_config_generate_precise": "精确模式", "model_config_history_json": "JSON", "model_config_history_markdown": "<PERSON><PERSON>", "model_config_history_round": "携带上下文轮数", "model_config_history_round_explain": "设置带入模型上下文的对话历史轮数。轮数越多，多轮对话的相关性越高，但消耗的 Token 也越多。", "model_config_history_text": "文本", "model_config_model": "模型", "model_config_response_format": "输出格式", "model_config_response_format_explain": "- **文本**: 使用普通文本格式回复\n- **Markdown**: 将引导模型使用Markdown格式输出回复\n- **JSON**: 将引导模型使用JSON格式输出", "model_config_title": "模型设置", "model_family": "模型家族", "model_form_fail_retry": "请重试", "model_form_fail_text": "网络异常，", "model_info": "模型信息\n", "model_list_ensure_service_quality": "为确保服务质量，新发布的模型将优先向专业版用户开放", "model_list_free": "限额使用", "model_list_model_company": "由{company}提供\n", "model_list_model_deprecation_date": "将于 {date} 下线", "model_list_model_deprecation_notice": "当前选中的模型即将下架", "model_list_model_setting": "{model} 参数设置\n", "model_list_model_switch_announcement": "当前选中的「 {model_deprecated} 」将于 {date} 下架\n届时将自动切换为「 {model_up} 」\n", "model_list_upgrade_button": "升级专业版", "model_list_upgrade_to_pro_advanced_tips": "专业版用户独享：抢先体验最新 AI 突破。高级模型带来更强大的 AI 能力和更精准的回答。", "model_list_upgrade_to_pro_version": "升级扣子专业版，抢先体验新模型\n", "model_list_upgrade_to_pro_version_advancedModel": "升级为付费版，使用高级模型", "model_list_willDeprecated": "即将下架", "model_max_token_alert": "若智能体回复异常中止，请点击智能体模型设置，并调高最大回复长度，并重新发布智能体（最大回复长度一般需要多于 1024）", "model_name": "模型名称", "model_not_supported": "以下功能当前模型不支持", "model_selection": "模型选择", "model_setting_alert": "注意: 数值小于0可能导致调用插件知识库等功能超时!", "model_setting_alert_2": "注意：设置的数值过小，可能导致function call请求超时！", "model_support_poor": "以下功能当前模型支持效果不佳", "model_support_poor_warning": "请注意{model<PERSON>ame}对以下功能支持效果不佳", "modules_menu_guide": "🚀 按需选择功能模块进行显示，使智能体开发更加高效！", "modules_menu_guide_gotit": "了解", "modules_menu_guide_warning": "无法隐藏已添加内容的模块", "monetization": "变现", "monetization_des": "启用后，用户承担聊天费用；禁用时，机器人创建者承担聊天费用。", "monetization_off": "货币化（关闭）", "monetization_off_des": "货币化被禁用，机器人创建者承担聊天费用。启用货币化后，用户承担聊天费用", "monetization_on": "货币化（开）", "monetization_on_des": "启用货币化。用户承担聊天费用，当用户余额耗尽时将发送账单。禁用货币化时，机器人创建者承担聊天费用。", "monetization_on_viewbill": "账单预览", "monetization_publish_fail": "变现失败", "monetization_publish_off": "关闭", "monetization_publish_on": "开", "monetization_support": "支持平台", "monetization_support_tips": "货币化设置目前支持这些平台", "move_desc1": "注：使用方舟模型的智能体迁移后需手动切换模型，否则无法正常使用", "move_failed": "迁移失败", "move_failed_btn_cancel": "取消迁移", "move_failed_btn_force": "强制迁移智能体", "move_failed_cancel_confirm_content": "即使取消迁移，已迁移过的资源仍会保留在新的工作空间，但智能体和未成功迁移的资源将保留在原工作空间内。", "move_failed_cancel_confirm_title": "确定取消迁移智能体？", "move_failed_desc": "由于以下资源未能成功迁移，因此智能体暂未迁移至新空间，请点击下方按钮重试。若选择“直接迁移智能体”，迁移失败的资源仍会保留在原空间，但智能体会被迁移至新空间。", "move_failed_force_confirm_content": "直接迁移后，未成功迁移的资源将保留在原空间内，智能体将被迁移至新工作空间。", "move_failed_force_confirm_title": "确定将智能体直接迁移到新工作空间？", "move_failed_toast": "智能体未能成功迁移，请点击智能体卡片查看详情", "move_not_allowed_contain_bot_nodes": "抱歉，当前智能体包含子智能体，暂不支持迁移。请先从当前智能体中删除子智能体，或先将子智能体迁移至相同目标工作空间后再迁移当前智能体。", "multiagent_node_scenarios_context_default": "用于“某功能”，帮助用户解决“某场景”相关的问题。", "multiagent_shortcut_modal_specify_node": "指定节点回答", "multiagent_shortcut_modal_specify_node_option_do_not_specify": "不指定节点", "multimodal_upload_file": "上传失败", "name_already_taken": "名称已被使用", "navi_bar_account_settings": "账号设置", "navigation_store": "商店", "navigation_workspace": "工作空间", "navigation_workspace_develop": "项目开发", "navigation_workspace_favourites": "收藏", "navigation_workspace_favourites_cancle": "取消收藏", "navigation_workspace_library": "资源库", "neutral_age_gate_confirm": "确定", "new_db_001": "数据库", "new_db_002": "数据库添加成功", "new_db_003": "数据库删除成功", "new_pat_1": "新的个人访问令牌", "new_pat_reminder_1": "此令牌仅显示一次。请将此密钥保存在安全且可获取的地方。不要与他人共享，也不要在浏览器或其他客户端代码中暴露它。", "next": "下一步", "no_api_token_1": "还没有个人访问令牌", "no_mock_yet": "尚无模拟数据", "no_mockset_yet": "还没有模拟集", "node_http_api": "API", "node_http_api_tooltips": "API接口的地址", "node_http_auth": "鉴权", "node_http_auth_basic": "Basic Auth", "node_http_auth_bearer": "<PERSON><PERSON>", "node_http_auth_custom": "自定义", "node_http_auth_desc": "用于验证请求者的身份和权限", "node_http_body": "请求体", "node_http_body_binary": "binary", "node_http_body_desc": "请求体数据，设置要发送的具体内容", "node_http_body_form_data": "form-data", "node_http_body_form_urlencoded": "x-www-form-urlencoded", "node_http_body_json": "JSON", "node_http_body_none": "none", "node_http_body_raw_text": "raw text", "node_http_headers": "请求头", "node_http_headers_desc": "配置 HTTP 请求头，用于传递认证信息、内容类型等关键参数", "node_http_import_curl": "导入 cURL", "node_http_import_curl_placeholder": "请输入cURL", "node_http_json_collapse": "收起", "node_http_json_data_input": "请输入JSON数据，可以使用\"{{\"引用变量", "node_http_json_format": "格式化", "node_http_json_input": "请输入JSON", "node_http_json_invalid_var": "JSON 中存在无效变量", "node_http_json_required": "JSON 不可为空", "node_http_name_rule": "只能包含字母、数字、中横线或下划线，并且以字母或下划线开头", "node_http_raw_text_input": "请输入raw text，可以使用\"{{\"引用变量", "node_http_raw_text_invalid_var": "raw text存在无效变量", "node_http_request_params": "请求参数", "node_http_request_params_desc": "设置调用接口时需要传入的参数详情", "node_http_response_data": "API的返回值", "node_http_retry_count": "重试次数", "node_http_timeout_setting": "超时设置（秒）", "node_http_url_input": "请输入接口的URL，可以使用\"{{\"引用变量", "node_http_url_invalid_var": "URL 中存在无效变量", "node_http_url_length_limit": "URL长度必须小于 10000 字符", "node_http_url_required": "URL 不可为空", "node_http_var_infer_delete": "变量失效", "not_show_again": "不再展示", "not_support_edit_1": "不支持编辑已过期的令牌", "not_supported": "模型不支持", "not_supported_explain": "{modelName}不支持此功能。建议关闭此功能或切换其他模型", "not_supported_explain_toolName": "{modelName}不支持{toolName}。建议关闭此功能或切换其他模型\n", "ocean_deploy_list_pkg_version": "版本号", "open_source_login_placeholder_email": "请输入邮箱", "open_source_login_placeholder_password": "请输入密码", "open_source_login_welcome": "欢迎使用扣子-开源版", "open_source_terms_linkname": "开源协议", "openapi_(fill_in_yaml)_*": "openapi (填写 yaml)", "opening_question_placeholder": "输入开场白引导问题", "opening_showall": "全部显示", "opening_showall_explain": "开启后，开场白会按顺序显示所有预置问题，否则将竖向随机显示3条预置问题。", "opening_showall_explain_demo_off": "关闭后，随机显示效果示例", "opening_showall_explain_demo_on": "开启全部显示效果示例", "operation_cannot_be_reversed": "此操作将不可逆", "or": "或", "org_api_pat_edit_reminder": "暂无修改权限", "org_id": "组织ID", "padding": "内边距", "parameters": "参数", "pat_reminder_1": "用于其他应用程序和平台的个人访问令牌。详细说明请查看：", "pat_reminder_2": "不要与他人共享您的个人访问令牌，也不要在浏览器或其他客户端代码中暴露它，以保护您账户的安全。若在公开场合发现任何泄露的个人访问令牌，该令牌可能会被自动禁用。", "path_has_duplicates": "{path} 有{num, plural, =1{#} other {#}}个重复", "pdf_encrypted": "PDF 文件已加密，上传失败", "people_using_mockset_delete": "{num, plural, =1 {1个人在使用} other {#个人在使用}}这个模拟集。删除模拟集？", "performance_knowledge": "知识库", "permission_manage_modal_cancel_auth_confirm_modal": "确认取消授权吗?", "permission_manage_modal_empty_list": "没有 OAuth 授权过的插件", "permission_manage_modal_reauth_hint": "如需重新授权,请在对话中重新触发授权", "permission_manage_modal_tab_name": "授权管理", "permission_manage_modal_table_action": "操作", "permission_manage_modal_table_name": "插件名称", "permission_manage_modal_title": "管理 OAuth 插件", "permission_manage_modal_title_hover_tip": "OAuth 插件允许第三方应用程序访问用户账户的特定资源，而无需共享用户的密码", "photo-size-limit": "{fileName} 大小超过 20MB", "pic_not_supported": "目前暂不支持 doc 类型文档的图片解析", "platform_name": "扣子", "platfrom_trigger_creat_name": "名称", "platfrom_trigger_dialog_trigge_icon": "触发器", "platfrom_triggers_title": "触发器", "please_enter_mockset_name": "请输入模拟集名称", "please_select_an_authorization_method": "请选择授权方式", "plugin_Parametename_error": "请输入有效字符。仅支持英文字母、数字、常用标点符号等ASCII字符。", "plugin_Parameter_des": "请输入参数描述", "plugin_Parameter_name_error": "参数名称不能重复", "plugin_Parameter_type": "请选择参数类型", "plugin_Update": "更新插件", "plugin_api_list_table_Create_time": "创建时间", "plugin_api_list_table_Parameter": "输入参数", "plugin_api_list_table_action": "操作", "plugin_api_list_table_botUsing": "智能体引用数", "plugin_api_list_table_debugicon_tooltip": "调试", "plugin_api_list_table_name": "工具列表", "plugin_api_list_table_status": "调试状态", "plugin_api_list_table_toolname": "工具名称", "plugin_api_type_fail": "失败", "plugin_api_type_pass": "通过", "plugin_auth_method_service_api_key": "Service token / API key", "plugin_auth_method_service_zti": "Bytedance Zero Trust Identity", "plugin_bot_ide_output_param_enable_tip": "当设为不可见时，该参数将不会被返回给大模型", "plugin_bot_ide_plugin_setting_icon_tip": "编辑参数", "plugin_bot_ide_plugin_setting_modal_input_param_title": "输入参数", "plugin_bot_ide_plugin_setting_modal_item_enable_tip": "当参数设置为不可见时，大模型将无法看到该参数。如果该参数设置了默认值并且不可见，则在调用插件时，智能体会默认只使用这个设定值", "plugin_bot_ide_plugin_setting_modal_output_param_title": "输出参数", "plugin_category_auto_suggestion": "自动推荐", "plugin_create": "创建插件", "plugin_create_action_btn": "操作", "plugin_create_draft_desc": "注册此插件时，请遵守隐私、版权和数据安全相关规定。", "plugin_create_guide_link": "Plugin创建指引", "plugin_create_header_list_title": "Header 列表", "plugin_create_modal_safe_error": "插件的名称、描述和图标包含不适当的内容，请修改后重新提交。", "plugin_creation_create_tool_in_ide": "在IDE中创建工具", "plugin_creation_method": "插件工具创建方式", "plugin_creation_method_cloud_plugin_use_existing_services": "云侧插件 - 基于已有服务创建", "plugin_creation_method_hover_tip_using_existing_services_desc": "直接将自己开发或公开的API配置为插件", "plugin_creation_method_hover_tip_using_existing_services_title": "使用外部API创建工具：", "plugin_creation_select_creation_method_warning": "请选择创建插件的方法", "plugin_detail_edit_modal_title": "编辑插件配置", "plugin_detail_view_modal_title": "插件配置", "plugin_edit_tool_default_value_array_edit_button": "编辑", "plugin_edit_tool_default_value_array_edit_modal_title": "默认值设置", "plugin_edit_tool_default_value_config_item_default_value": "默认值", "plugin_edit_tool_default_value_config_item_enable": "开启", "plugin_edit_tool_default_value_config_item_enable_disable_tip": "此参数是必填参数，填写默认值后，此开关可用", "plugin_edit_tool_default_value_config_item_enable_tip": "当参数设置为不可见时，插件的使用者和大模型将无法看到该参数。如果该参数设置了默认值并且不可见，则在调用插件时，智能体会默认只使用这个设定值", "plugin_edit_tool_default_value_input_placeholder": "请填写默认值", "plugin_edit_tool_edit_example": "编辑示例", "plugin_edit_tool_oauth_enabled_status_auth_disabled": "使用时跳过鉴权", "plugin_edit_tool_oauth_enabled_status_auth_optional": "使用时可以鉴权", "plugin_edit_tool_oauth_enabled_status_auth_required": "使用时需要鉴权", "plugin_edit_tool_oauth_enabled_title": "<PERSON><PERSON><PERSON> 设置", "plugin_edit_tool_oauth_enabled_title_hover_tip": "关闭Oauth后，当智能体调用该工具时，智能体用户将跳过鉴权", "plugin_edit_tool_output_param_enable_tip": "当设为不可见时，该参数将不会被返回给大模型", "plugin_edit_tool_test_run_cancel_example": "取消勾选并发布后，当前插件示例将被取消", "plugin_edit_tool_test_run_debugging_example": "调试结果", "plugin_edit_tool_test_run_example_tip": "运行示例", "plugin_edit_tool_test_run_save_results_as_example": "保存调试结果为工具使用示例", "plugin_edit_tool_title": "编辑工具", "plugin_edit_tool_view_example": "查看示例", "plugin_empty": "请填写内容", "plugin_empty_desc": "暂无工具", "plugin_empty_description": "使用你的 API 创建工具，并在智能体当中使用", "plugin_exception": "服务异常", "plugin_feedback_entry_button": "提交反馈", "plugin_feedback_entry_tip": "没找到想要的插件？", "plugin_feedback_error_tip_empty_content": "未填写反馈内容", "plugin_feedback_error_tip_no_official_plugin_choosen": "未选择官方插件", "plugin_feedback_modal_choose_official_plugin": "选择官方插件", "plugin_feedback_modal_feedback_content": "反馈内容", "plugin_feedback_modal_feedback_content_placeholder": "请输入反馈内容", "plugin_feedback_modal_request_type": "反馈类型", "plugin_feedback_modal_request_type_feedback_to_existing_plugin": "已有官方插件功能反馈", "plugin_feedback_modal_request_type_official_plugins_not_found": "未找到需要的插件", "plugin_feedback_modal_tip_submission_success": "提交成功，感谢你的反馈！", "plugin_feedback_modal_title": "插件反馈", "plugin_file_max": "上传文件最多不得超过20个", "plugin_file_unknown": "未知类型", "plugin_file_upload": "上传文件", "plugin_file_upload_image": "上传图片", "plugin_file_upload_mention": "请上传文件", "plugin_file_upload_mention_image": "请上传图片", "plugin_file_uploading": "上传中...", "plugin_form_add_child_tooltip": "添加子节点", "plugin_form_no_result_desc": "没有结果", "plugin_imported_successfully": "插件导入成功", "plugin_install_success": "安装成功", "plugin_location_header": "Header", "plugin_location_header1": "意味着秘钥在HTTP请求的头部。示例:在请求头中", "plugin_location_header2": "Authorization: Bearer your_token_here", "plugin_location_info_all": "决定了秘钥应该放在哪里传给服务器", "plugin_location_info_query": "Query", "plugin_location_info_query1": "意味着秘钥作为URL的一部分。示例:", "plugin_location_info_query2": "https://api.example.com/data?key=your_key_here", "plugin_mark_created_by_existing_services": "第三方服务", "plugin_mark_created_by_ide": "Coze 托管", "plugin_metric_average_time": "平均耗时", "plugin_metric_bots_using": "智能体引用量", "plugin_metric_success_rate": "成功率", "plugin_metric_usage_count": "调用量", "plugin_name_conflict_error": "无法添加名称重复的插件工具", "plugin_parameter_create_modal_safe_error": "参数的名称和描述包含不适当的内容，请修改后重新提交。", "plugin_publish_form_version": "版本号", "plugin_publish_form_version_desc": "版本描述", "plugin_quote_tip_1": "插件由第三方应用提供支持，您可以在插件详情页查看插件信息。", "plugin_quote_tip_2": "引用插件后，您的智能体可能会将您和终端用户的对话等自定义指令发送到插件。", "plugin_quote_tip_3": "您的智能体会在跟终端用户的对话中自动选择何时触发插件。", "plugin_s3_Parse": "解析输出参数", "plugin_s3_Parsing": "解析中", "plugin_s3_failed": "输出参数解析失败，请重试", "plugin_s3_running": "运行中", "plugin_s3_success": "输出参数解析完成", "plugin_s4_debug_detail": "原因说明", "plugin_s4_debug_empty": "调试结果将展示在此处，调试通过后，即可进入下一步", "plugin_s4_debug_failed": "调试未通过", "plugin_s4_debug_pass": "调试通过", "plugin_service_status": "服务状态", "plugin_service_status_offline": "下线", "plugin_service_status_online": "在线", "plugin_store_authorized": "已授权", "plugin_store_contact_deployer": "请联系服务部署者配置授权", "plugin_store_install": "安装", "plugin_store_unauthorized": "未授权", "plugin_team_edit_tip_another_user_is_editing": "其他用户正在编辑。请等待编辑完成后再继续你的操作", "plugin_team_edit_tip_unable_to_edit": "当前无法编辑", "plugin_tool_config_auth_modal_auth_required": "该插件需要授权才可运行", "plugin_tool_config_auth_modal_auth_required_desc": "跳转第三方授权页面完成授权", "plugin_tool_config_auth_modal_cancel_confirmation": "确认后可取消授权", "plugin_tool_config_auth_modal_cancel_confirmation_desc": "取消授权后，会删除你的授权数据", "plugin_tool_config_status_authorized": "已授权", "plugin_tool_config_status_unauthorized": "未授权", "plugin_tool_create_modal_safe_error": "工具的名称和描述包含不适当的内容，请修改后重新提交。", "plugin_tool_exists_tips": "已存在{num, plural, =1{1个} other{#个}}具有相同URL的工具", "plugin_tool_import_succes": "工具导入成功", "plugin_tool_replace_success": "工具替换成功", "plugin_tooltip_url": "通过URL请求特定资源，主要用于获取数据。示例：", "plugin_type_app": "APP", "plugin_type_func": "FUNC", "plugin_type_plugin": "PLUGIN", "plugin_type_workflow": "WORKFLOW", "plugin_update_tip": "插件已更改，但在发布之前，智能体将继续使用之前发布版本的插件。", "plugin_usage_limits_modal_got_it_button": "我知道了", "plugin_usage_limits_modal_table_header_plugin": "插件", "plugin_usage_limits_modal_table_header_price": "价格", "plugin_usage_limits_modal_title": "插件调用限制", "plugin_usage_limits_modal_view_details": "查看详情", "plugins_with_limited_calls_added_tip": "添加了有调用上限的 Plugin", "poorly_supported_explain_toolName": "{modelName}对{toolName}的支持效果不佳。建议关闭此功能或切换其他模型\n", "pop_edit_save_confirm": "离开当前页面后，Coze 可能不会保存你所做的更改。", "pop_up_button_refresh": "刷新", "pop_up_description_data_conflict": "很抱歉，你所编辑的内容已被其他用户修改过了，请刷新获取最新内容后再进行操作。", "pop_up_title_data_conflict": "刷新重试", "premium_monetization_config": "变现配置", "privacy_link_placeholder": "www.example.com/privacy", "professional_plan_n_paid_plugins_included_in_bot": "智能体中共包含 {count} 个计费插件。了解更多插件计费信息，请查看 扣子 API", "professional_plan_n_paid_plugins_included_in_workflow": "工作流中共包含 {count} 个计费插件。了解更多插件计费信息，请查看 扣子 API", "profile_entrance": "我的", "profile_memory_sample_description_address": "按省-市-区-街道的格式记录", "profile_memory_sample_description_height": "以“cm”为单位记录", "profile_memory_sample_description_mobile": "记录用户手机号码", "profile_memory_sample_description_name": "记录用户姓名", "profile_memory_sample_description_weight": "以“kg”为单位记录", "profile_settings": "设置", "profilepicture_hover_failed": "生成失败", "profilepicture_hover_generated": "已生成", "profilepicture_hover_generating": "生成中", "profilepicture_popup_async": "如果你不想在这里等待，你可以点击取消离开弹窗。完成时你会看到提示。", "profilepicture_popup_cancel": "取消", "profilepicture_popup_generate": "生成", "profilepicture_popup_generategif_default": "使用图片作为第一帧，然后用提示词生成GIF。请使用完整的句子。例如，一只可爱的松鼠正在冲浪板上跳舞。", "profilepicture_popup_toast_daymax_gif": "你已经达到每日生成10个GIF的限制。", "profilepicture_popup_toast_daymax_image": "你已经达到每日生成20个图片的限制。", "profilepicture_popup_toast_picturemax": "请删除不需要的图片或动图后再继续。", "profilepicture_toast_failed": "生成失败", "profilepicture_toast_generated": "生成成功", "project_241115": "修改描述", "project_conversation_list_batch_delete_btn": "删除选中 {len} 个会话", "project_conversation_list_batch_delete_tooltip": "批量删除会话", "project_conversation_list_batch_delete_tooltip_context": "将会删除 {len} 个动态会话。同时删除会话中的消息记录，此操作不可恢复，确定删除吗？", "project_conversation_list_delete_all_tooltip_context": "删除全部会话节点创建的动态会话，此操作不可恢复", "project_conversation_list_dynamic_title": "会话节点创建", "project_conversation_list_operate_batch_tooltip": "批量操作", "project_conversation_list_static_title": "会话列表", "project_delete_permission_tooltips": "只有项目创建人可删除", "project_ide_cancel": "取消", "project_ide_create_duplicate": "创建副本", "project_ide_delete": "删除", "project_ide_delete_confirm": "是否删除项目？", "project_ide_delete_confirm_describe": "删除后不能恢复，请谨慎操作。如确认删除，请在下方输入项目名称", "project_ide_delete_project": "删除应用", "project_ide_duplicate": "创建副本", "project_ide_duplicate_loading": "副本创建中...", "project_ide_edit_project": "编辑应用", "project_ide_frame_plugin": "插件", "project_ide_frame_publish": "发布", "project_ide_info_created_on": "创建于 {time}", "project_ide_maximize": "全屏", "project_ide_project_name": "项目名称", "project_ide_quit": "退出", "project_ide_restore": "收起", "project_ide_tab_title": "{project_name} - 应用 - 扣子", "project_ide_tabs_close": "关闭", "project_ide_tabs_close_all": "关闭所有", "project_ide_tabs_close_other_tabs": "关闭其他", "project_ide_tabs_open_on_left": "在左侧分屏打开", "project_ide_tabs_open_on_right": "在右侧分屏打开", "project_ide_toast_delete_success": "项目已删除", "project_ide_toast_duplicate_fail": "副本创建失败", "project_ide_toast_duplicate_fail_retry": "重试", "project_ide_toast_duplicate_success": "副本创建成功", "project_ide_toast_duplicate_view": "查看", "project_ide_toast_edit_success": "更新成功", "project_ide_unsaved_changes": "未保存的更改", "project_ide_unsaved_describe": "当前应用有未保存的更改，是否确定要在不保存的情况下退出？", "project_ide_view_document": "查看文档", "project_ide_welcome_db_describ": "预置知识库（RAG）和数据库，让你的 AI 应用集成私有数据", "project_ide_welcome_db_title": "数据库和知识库配置", "project_ide_welcome_describe": "用扣子开始你的旅程，即刻构建一个 AI 应用！", "project_ide_welcome_title": "Hi，欢迎来到扣子应用 IDE", "project_ide_welcome_ui_builder_describe": "预置丰富前端组件，拖拽并绑定工作流即可完成用户交互页面搭建", "project_ide_welcome_ui_builder_title": "UI Builder 快速搭建用户界面", "project_ide_welcome_workflow_describe": "直观灵活的服务端编排方式。支持大模型、三方API集成、流程控制等丰富组件", "project_ide_welcome_workflow_title": "通过工作流搭建服务端", "project_plugin_delete_modal_description": "请谨慎操作，删除后不可恢复", "project_plugin_delete_modal_title": "确认删除 {pluginName} ？\n\n", "project_plugin_delete_success_toast": "插件删除成功", "project_plugin_setup_metadata_cancel": "取消", "project_plugin_setup_metadata_edit": "编辑", "project_plugin_setup_metadata_more_info": "更多信息\n", "project_plugin_setup_metadata_save": " 保存", "project_plugin_testrun": "试运行", "project_publish_select_desc_compliance_new": "在以下平台发布你的应用，即表示你已充分理解并同意遵循{publish_terms_title}（包括但不限于任何隐私政策、社区指南、数据处理协议等）。", "project_release_Please_select": "请选择 Chatflow", "project_release_already_released": "已成功提交发布", "project_release_already_released_desc": "发布过程可以关闭本页面，发布状态可以在应用详情页查看", "project_release_api1": "API 或 SDK", "project_release_api_sdk_desc": "将应用中的工作流发布为 API 或将对话流发布为 SDK ，嵌入到你自己的应用中。", "project_release_cancel": "撤销发布", "project_release_cancel1": "撤销发布？", "project_release_cancel1_desc": "已发布的当前应用将失效", "project_release_channel": "渠道审核与发布", "project_release_chatflow2": "处理消息的对话流", "project_release_chatflow3": "对话流已被删除，请重新选择", "project_release_chatflow4": "请先选择处理消息的对话流", "project_release_chatflow_choose": "处理消息的对话流", "project_release_coze1": "扣子", "project_release_coze_audit": "扣子审核", "project_release_display_label": "展示为", "project_release_download_code": "下载代码", "project_release_example": "版本号示例：v0.0.1", "project_release_example1": "当前版本号：{version}", "project_release_example2": "请输入版本号", "project_release_example3": "版本号已存在，不可重复", "project_release_failed": "发布失败", "project_release_finish": "已完成", "project_release_guide": "安装指引", "project_release_h5_desc": "支持托管发布到抖音/微信小程序平台，或下载代码包，自主上传发布。", "project_release_in_progress": "进行中", "project_release_miniprogram1": "小程序", "project_release_not_pass": "未通过", "project_release_notify": "如果应用没有经过完整的试运行，可能发布结果不合预期。建议经过完整的试运行后再进行发布。", "project_release_open_in_store": "立即使用", "project_release_pack_fail_reason": "以下资源打包失败，请检查后再试：", "project_release_package": "应用打包", "project_release_package_failed": "打包失败", "project_release_pass": "通过", "project_release_select_chatflow": "请选择工作流", "project_release_set_desc": "请先配置/授权", "project_release_social1": "社交平台", "project_release_social_desc1": "将对话流一键发布到社交平台。\n需要选择接收用户消息的对话流。当用户在社交平台发送消息时，将调用该对话流来接收用户消息。应用中所有配置的消息输出节点，均会回复给用户。", "project_release_stage": "发布状态", "project_release_success": "发布成功", "project_release_template_info": "配置模板信息", "project_release_template_info_category": "模板分类", "project_release_template_info_desc": "模板描述", "project_release_template_info_display": "展示方式", "project_release_template_info_info": "模板介绍", "project_release_template_info_name": "模板名称", "project_release_template_info_not": "请输入{template_info_type}", "project_release_template_info_poster": "模板封面", "project_release_ts_desc": "将应用发布到模板或项目商店。", "project_release_version_info": "版本信息", "project_releasing": "发布中", "project_resource_modal_copy_to_project": "复制到应用", "project_resource_modal_library_resources": "资源库{resource}\n", "project_resource_modal_project_resources": "应用{resource}", "project_resource_sidebar_confirm_batch_delete": "确认删除以下 {count} 个文件？请谨慎操作，删除后不可以恢复\n", "project_resource_sidebar_confirm_delete": "确认删除 {resourceName}？请谨慎操作，删除后不可恢复\n", "project_resource_sidebar_copy": "创建副本", "project_resource_sidebar_copy_to_library": "复制到资源库", "project_resource_sidebar_create_new_folder": "新建文件夹", "project_resource_sidebar_create_new_resource": "新建{resource}\n", "project_resource_sidebar_data_section": "数据\n", "project_resource_sidebar_delete": "删除文件", "project_resource_sidebar_disable_resource": "禁用{resource}", "project_resource_sidebar_enable_resource": "启用{resource}", "project_resource_sidebar_import_from_library": "导入资源库文件\n", "project_resource_sidebar_move_to_library": "移动到资源库", "project_resource_sidebar_please_enter": "请输入名称", "project_resource_sidebar_rename": "重命名", "project_resource_sidebar_resource_not_added": "还未添加{resource}\n", "project_resource_sidebar_title": "资源\n", "project_resource_sidebar_warning_empty_key": "空键值", "project_resource_sidebar_warning_label_exists": "{label} 已存在", "project_resource_sidebar_warning_length_exceeds": "长度超出限制", "project_store_search": "应用", "project_toast_copy_failed": "创建副本失败，请重试", "project_toast_copy_successful": "成功创建副本", "project_toast_copying_resource": "正在创建 {resourceName} 的副本", "project_toast_only_published_resources_can_be_imported": "发布后才能导入到应用", "project_toast_successfully_imported_from_library": "成功导入资源库文件", "prompt_detail_copy_prompt": "复制提示词", "prompt_detail_prompt_detail": "提示词详情", "prompt_generate_instruction_validate": "指令为必填项", "prompt_generate_statement_validate": "指令为必填项", "prompt_generate_stop": "停止扩写", "prompt_generate_stop_responding": "停止回答", "prompt_library_edit": "编辑", "prompt_library_empty_describe": "点击下方按钮可立即创建提示词资源", "prompt_library_empty_title": "当前空间暂无可用的提示词资源", "prompt_library_new_prompt": "新建提示词", "prompt_library_prompt_copied_successfully": "提示词复制成功", "prompt_library_prompt_creat_successfully": "提示词创建成功", "prompt_library_prompt_empty": "提示词暂无内容", "prompt_library_prompt_library": "提示词库", "prompt_library_unselected": "请在左侧列表选择想要预览的提示词", "prompt_optimization_button": "优化", "prompt_optimization_button_hover_tooltip": "自动优化提示词", "prompt_resource_delete_describ": "删除后此操作不可撤，但历史使用了本提示词的智能体或工作流不受影响。", "prompt_resource_insert_prompt": "插入提示词", "prompt_resource_personal": "个人", "prompt_resource_recommended": "推荐", "prompt_resource_team": "工作空间", "prompt_resource_view_all": "全部", "prompt_submit": "提交", "prompt_version_submit": "版本提交后即刻生效，确定要提交么", "publish_audit_pop7": "内容不符合[《扣子平台发布规范》](https://www.coze.cn/docs/guides/content_principles)，请修改后重试", "publish_base_configFields": "配置多维表格输入表单", "publish_base_configFields_ unfinished_toast": "请完成所有必填信息", "publish_base_configFields_complete_Information_describe": "请填写发布到飞书多维表格捷径中心所需的必要信息", "publish_base_configFields_complete_Information_edit": "去修改", "publish_base_configFields_complete_Information_fill_out": "去填写", "publish_base_configFields_complete_Information_title": "完善捷径上架信息", "publish_base_configFields_component": "控件", "publish_base_configFields_component_placeholder": "请选择控件", "publish_base_configFields_dataType_placeholder": "请选择数据类型", "publish_base_configFields_field": "字段", "publish_base_configFields_invalid": "失效", "publish_base_configFields_key": "Key", "publish_base_configFields_key_placeholder": "请输入对象中元素的键名（Key）", "publish_base_configFields_placeholder": "占位符", "publish_base_configFields_placeholder_placeholder": "输入占位符", "publish_base_configFields_requiredWarn": "不可为空", "publish_base_configFields_status_completed": "已完成", "publish_base_configFields_title": "标题", "publish_base_configFields_title_placeholder": "请输入表单标题", "publish_base_configStruct_dataType": "数据类型", "publish_base_configStruct_id": "ID", "publish_base_configStruct_primary": "主属性", "publish_base_config_configBaseInfo": "配置多维表格捷径基础信息", "publish_base_config_configFeishuBase": "配置飞书多维表格", "publish_base_config_configOutputType": "捷径输出数据类型", "publish_base_config_needReconfigure": "有变量变更，请调整配置", "publish_base_config_structOutputConfig": "配置对象输出字段", "publish_base_inputFieldConfig_fieldSelector": "字段选择器", "publish_base_inputFieldConfig_maxChars": "最大字符数", "publish_base_inputFieldConfig_multiSelect": "多选", "publish_base_inputFieldConfig_options": "选项", "publish_base_inputFieldConfig_singleSelect": "单选", "publish_base_inputFieldConfig_supports": "支持的数据类型", "publish_base_inputFieldConfig_textInput": "文本输入框", "publish_channel_control_page_channel_set_management": "企业自定义渠道管理", "publish_douyin_config_ing": "授权中", "publish_list_header_status": "状态", "publish_page_no_channel_status_desc": "当前空间管理员暂未配置发布渠道，请联系管理员配置渠道", "publish_page_no_channel_status_title": "暂无发布渠道", "publish_permission_control_page_remove_choose_all": "全选", "publish_permission_control_page_remove_chosen": "已选", "publish_result_all_failed": "发布失败，请重试", "publish_success": "已成功提交发布！", "publish_terms_title": "各发布渠道服务条款", "publish_tooltip_select_category": "请选择在智能体商店的分类", "publish_tooltip_select_platform": "请选择一种发布渠道", "publish_wechat_old_disconnect": "根据微信要求，请尽快将智能体绑定并发布到当前所选的新版微信公众号（服务号）。新版使用微信第三方平台方式接入，配置更简单、授权更加安全可靠。操作前，请先在扣子解绑“【即将下线】微信公众号（服务号）”渠道。", "publish_wechat_old_disconnect_title": "请先解绑原“【即将下线】微信公众号（服务号）”发布渠道", "publisher_market_public_disabled": "暂不支持将包含私有资源的智能体公开配置。私有资源包括在工作空间内创建的知识库、工作流或未提交到商店的插件。", "python": "Python", "query_analytics_bar_chart": "条形图", "query_analytics_intent": "意图", "query_analytics_intent_rank": "意图排名", "query_analytics_level_one": "一级分类", "query_analytics_level_two": "二级分类", "query_analytics_pie_chart": "饼图", "query_analytics_skill_rank": "触发技能排名", "query_card_id": "卡片ID", "query_data_empty": "暂无数据", "query_detail_tip_copy": "复制", "query_detail_title_input": "输入", "query_detail_title_output": "输出", "query_flamethread": "火焰图", "query_latency": "耗时 {duration}", "query_list_loadmore": "加载更多", "query_node_details": "节点详情", "query_query_analytics": "访问分析", "query_run_tree": "调用树", "query_select_batch": "查看批量运行信息", "query_status_all": "全部", "query_status_broken": "损坏", "query_status_completed": "运行完成", "query_status_error": "异常", "query_status_failed": "运行异常", "query_status_success": "成功", "query_status_unknown": "未知", "query_stream_output": "流式输出", "query_tokens_number": "{number} Tokens", "quote_ask_in_chat": "追问", "randomlymode": "随机模式", "read_file_failed_please_retry": "文件读取失败，请重新上传", "real_data": "真实数据", "recall_knowledge_no_related_slices": "没有召回相关的切片", "recommended_failed": "没有推荐的数据表，请手动添加", "reference_graph_entry_button": "引用关系", "reference_graph_modal_subtitle_view_relationship_given_workflow": "按工作流查看", "reference_graph_modal_title": "资源引用", "reference_graph_modal_title_info_hover_tip": "查看工作流中所有资源的引用关系", "reference_graph_modal_title_info_hover_tip_explain": "卡片从 A 指向 B 代表 A 引用了 B", "reference_graph_node_loop_tip": "该节点下方层级循环，循环内容隐藏不再重复展示", "reference_graph_node_open_in_new_tab": "在新标签页打开", "reference_graph_tag_different_version_of_same_resource": "不同版本", "reference_graph_tag_different_version_same_resource": "同一资源", "reference_graph_tip_current_workflow_has_no_reference": "当前工作流暂无引用关系", "reference_graph_tip_fail_to_load": "加载失败", "reference_graph_tip_fail_to_load_retry_needed": "重试", "refresh_project_tags": "刷新", "regenerate": "重新生成", "register": "注册", "register_success": "注册成功", "release_analysis_by_day": "按日", "release_analysis_by_month": "按月", "release_analysis_by_quater": "按季度", "release_analysis_by_week": "按周", "release_analysis_change_time": "昨日数据 10:00 完成更新", "release_analysis_change_time_desc": "请修改页面最上方的数据统计范围后查看", "release_analysis_channel": "渠道分析", "release_analysis_channel_name": "渠道", "release_analysis_chat": "对话数", "release_analysis_days_compare": "相比前{days}天", "release_analysis_lj_chat": "累计对话数", "release_analysis_lj_desc": "*累计指标不受时间筛选影响", "release_analysis_lj_run": "累计运行数", "release_analysis_lj_token": "累计 Token 消耗", "release_analysis_lj_user": "累计用户数", "release_analysis_lj_zhibiao": "累计指标", "release_analysis_new_user": "新用户数", "release_analysis_overview": "概览", "release_analysis_performance": "性能", "release_analysis_performance_average": "平均响应时长", "release_analysis_performance_average_desc": "接口请求的平均运行时长", "release_analysis_performance_error_code": "错误码统计", "release_analysis_performance_error_code_all": "查看全部", "release_analysis_performance_error_code_count": "错误数统计", "release_analysis_performance_error_code_desc": "接口请求的错误码，错误码和错误信息和 [扣子 Open API的错误码规范](coze.cn/open/docs/developer_guides/coze_error_codes) 保持一致。", "release_analysis_performance_error_code_info": "错误信息", "release_analysis_performance_error_code_name": "错误码", "release_analysis_performance_success_rate": "响应成功率", "release_analysis_performance_success_rate_desc": "接口请求的成功率，可在“错误码统计”查看真实错误情况", "release_analysis_run": "运行数", "release_analysis_store/template": "商店/模板", "release_analysis_time": "近{days}天", "release_analysis_token": "Token 消耗量", "release_analysis_use_msg_average": "人均对话数", "release_analysis_use_new_msg_average": "新用户人均对话数", "release_analysis_user": "用户数", "release_analysis_user_active": "用户活跃", "release_analysis_user_active_cnt": "活跃用户数", "release_analysis_user_active_new_cnt": "活跃新用户数", "release_analysis_user_from": "用户来源", "release_analysis_user_new_msg_cnt": "新用户对话数", "release_analysis_user_new_run_average": "新用户人均运行数", "release_analysis_user_new_run_cnt": "新用户运行数", "release_analysis_user_performance": "用户参与度", "release_analysis_user_retention": "用户留存", "release_analysis_user_retention_7_day": "7日用户留存", "release_analysis_user_retention_7_day_DAU": "DAU - 7日新用户留存", "release_analysis_user_retention_7_day_DNU": "DNU - 7日用户留存", "release_analysis_user_retention_month": "月用户留存", "release_analysis_user_retention_month_MAU": "MAU - 月用户留存", "release_analysis_user_retention_month_MNU": "MAU - 月新用户留存", "release_analysis_user_retention_next_day": "次日用户留存", "release_analysis_user_retention_next_day_DAU": "DAU - 次日用户留存率", "release_analysis_user_retention_next_day_DNU": "DNU - 次日新用户留存", "release_analysis_user_run_average": "人均运行数", "release_management": "发布管理", "release_management_1": "管理", "release_management_cannot_removed": "暂时不支持下架", "release_management_connector_released": "已发布平台", "release_management_detail1": "首次发布成功后可以在{button}查看详情。", "release_management_generating": "页面生成中，请稍后查看", "release_management_no_permision": "暂无查看权限", "release_management_no_project": "工作空间中暂无已发布项目", "release_management_only_owner": "仅项目创建人可操作", "release_management_openin": "在渠道中打开", "release_management_project": "项目", "release_management_recent": "最近发布时间", "release_management_removed1": "下架", "release_management_removed2": "将项目从所选{count}个渠道下架", "release_management_removed_sucess": "项目已成功下架", "release_management_removed_sucess1": "项目成功从所选渠道下架", "release_management_removed_sucess2": "已下架", "release_management_search1": "搜索工作流", "release_management_search_app": "搜索应用", "release_management_search_bot": "搜索智能体", "release_management_search_no": "当前筛选状态下未能找到相关结果", "release_management_token": "Token消耗", "release_management_trace": "日志", "release_management_trace_ascend": "点击升序", "release_management_trace_default_rank": "恢复默认排序", "release_management_trace_descend": "点击降序", "release_management_trace_excecute_id": "运行 ID", "release_management_trace_excecute_token": "运行 Token", "release_management_trace_from": "来源", "release_management_trace_trigger_id": "触发器 ID", "release_management_trace_version": "版本", "release_management_trigger": "触发器管理", "release_management_trigger1": "触发器", "release_management_trigger_has": "配置了触发器", "remove_dataset": "取消知识库", "remove_token_1": "移除后会影响所有正在使用API 个人访问令牌的应用", "remove_token_reminder_1": "删除令牌", "replace": "替换", "requests_the_server_to_delete_the_specified_resource__example_": "请求服务器删除指定的资源。示例：", "required": "必填", "resource_copy_move_notify": "如果资源没有经过完整的试运行，复制/移动结果可能不合预期。建议经过完整的试运行后再进行操作。", "resource_move": "迁移", "resource_move_bot_success_toast": "智能体迁移成功", "resource_move_confirm_content": "每个智能体暂时仅支持迁移一次。", "resource_move_confirm_title": "确认将智能体和所有资源迁移到工作空间？", "resource_move_no_team_joined": "暂未加入任何工作空间，无需迁移智能体。", "resource_move_notice": "当前仅支持将智能体从个人空间迁移至工作空间。", "resource_move_target_team": "选择要迁移到的工作空间", "resource_move_title": "迁移智能体至工作空间 - {bot_name}", "resource_move_together": "同时迁移的资源", "resource_move_together_desc": "为保证智能体正常运行，以下智能体使用的资源将同时迁移到新空间。如果原空间其他智能体也在使用下列资源，建议先将资源创建副本后再迁移。此外，智能体迁移后“发布到 API”将失效，迁移后需要重新发布智能体。", "resource_process_modal_cancel_button": "取消", "resource_process_modal_retry_button": "重试", "resource_process_modal_text_copying_process_interrupt_warning": "复制过程中关闭弹窗、刷新网页会打断复制过程\n", "resource_process_modal_text_copying_resource_to_library": "正在复制 {resourceName} 到资源库...\n", "resource_process_modal_text_copying_resource_to_project": "正在将 {resourceName} 复制到应用...", "resource_process_modal_text_moving_process_interrupt_warning": "移动过程中关闭弹窗、刷新网页会打断移动过程\n", "resource_process_modal_text_moving_resource_to_library": "正在移动 {resourceName} 到资源库...", "resource_process_modal_title_copy_resource_to_library": "复制到资源库", "resource_process_modal_title_import_resource_from_library": "从资源库导入", "resource_process_modal_title_move_resource_to_library": "移动到资源库\n", "resource_toast_copy_to_library_fail": "复制失败，请重试", "resource_toast_copy_to_library_success": "已成功复制到资源库", "resource_toast_copy_to_project_fail": "导入失败，请重试\n", "resource_toast_move_to_library_fail": "移动失败，请重试", "resource_toast_move_to_library_success": "已成功移动到资源库", "resource_toast_view_resource": "查看", "resource_type_database": "数据库", "resource_type_knowledge": "知识库", "response": "返回", "retry": "重试", "review_agent_suggestreplyinfo": "用户问题建议", "review_bot_Onboarding_suggested_questions": "开场白预置问题", "review_bot_database": "数据库", "right": "右侧", "role_info.description": "角色描述", "scene_beta_sign": "Beta", "scene_edit_roles_create_name": "角色名称", "scene_edit_roles_list_nickname_empty_seat": "空位", "scene_mkpl_search_title": "场景", "scene_resource_name": "社会场景", "scene_workflow_chat_message_content_placeholder": "请编辑对话内容", "scene_workflow_chat_message_error_content_empty": "对话内容不可为空", "scene_workflow_chat_node_conversation_batch_empty": "你必须添加一个发言人列表", "scene_workflow_chat_node_conversation_content_speaker": "发言人", "scene_workflow_chat_node_conversation_content_speaker_fixed": "固定内容发言", "scene_workflow_chat_node_conversation_content_speaker_fixed_content": "发言内容", "scene_workflow_chat_node_conversation_content_speaker_fixed_placeholder": "您可以使用 {{变量名}} 方法从输入参数中引入变量。", "scene_workflow_chat_node_conversation_content_speaker_generate": "自主生成发言", "scene_workflow_chat_node_conversation_content_speaker_message_type": "发言类型", "scene_workflow_chat_node_conversation_content_speaker_placeholder": "请选择发言人", "scene_workflow_chat_node_conversation_visibility_all": "场景中的所有成员", "scene_workflow_chat_node_conversation_visibility_custom": "自定义", "scene_workflow_chat_node_conversation_visibility_custom_roles": "角色", "scene_workflow_chat_node_conversation_visibility_custom_variable": "昵称变量", "scene_workflow_chat_node_conversation_visibility_speaker": "本次对话中所有要发言的成员", "scene_workflow_chat_node_name": "对话编排", "scene_workflow_chat_node_test_run_button": "继续运行", "scene_workflow_chat_node_test_run_running": "正在运行", "scene_workflow_chat_node_test_run_title": "测试角色调度节点", "scene_workflow_delete_workflow_button": "从{source}移除", "scene_workflow_delete_workflow_popup_subtitle": "移除后，相关{source}中的引用将失效", "scene_workflow_delete_workflow_popup_title": "确认从{source}中移除吗？", "scene_workflow_invalid": "已失效", "scene_workflow_popup_add_forbidden": "发布后才能添加至当前场景", "scene_workflow_popup_delete_confirm_subtitle": "删除后将无法回退", "scene_workflow_popup_delete_confirm_title": "确认删除此工作流吗？", "scene_workflow_popup_list_empty": "此场景还没有创建过工作流", "scene_workflow_popup_search_empty": "没有搜索到对应结果", "scene_workflow_popup_title": "此场景中创建的工作流", "scene_workflow_start_roles": "所有角色信息", "scene_workflow_start_roles_introduce": "成员介绍", "scene_workflow_start_roles_name": "角色名称", "scene_workflow_start_roles_nickname": "成员昵称", "scene_workflow_testrun_nickname_error": "昵称仅支持英文、数字、下划线，且不能重复", "scene_workflow_testrun_nickname_nickname": "昵称", "scene_workflow_testrun_nickname_placeholder": "请输入昵称", "scene_workflow_testrun_title": "完善场景中的空位信息", "scene_workflow_testrun_title_tooltip": "场景角色中存在空位，请先为这些角色预设一个昵称。昵称仅支持英文、数字、下划线，且不能重复", "scope_all": "所有人", "scope_self": "我创建的", "scroll": "滚动", "search_not_found": "未能找到相关结果", "see_more": "查看更多", "select_agent_no_result": "没有可用的智能体", "select_agent_title": "选择智能体用于对比调试", "select_category": "选择分类", "select_expired_time_1": "选择过期时间", "select_later": "稍后选择", "select_team": "选择工作空间", "setting_name_placeholder": "输入用户昵称", "setting_name_save": "保存", "setting_username_empty": "请设置用户名", "settings_api_authorization": "API 授权", "settings_language_en": "英文", "settings_language_zh": "中文", "share_conversation": "分享对话", "shortcut_Illegal_file_format": "无法上传不允许的文件格式", "shortcut_component_type_selector": "选择器", "shortcut_component_type_text": "文本", "shortcut_component_upload_component_placeholder": "点击或拖放", "shortcut_modal_add_plugin_wf_complex_input_error": "不支持添加带有复杂输入参数（数组、对象）的插件/工作流", "shortcut_modal_add_plugin_wf_no_input_error": "请选择带有输入参数的插件/工作流", "shortcut_modal_button_name": "按钮名称", "shortcut_modal_button_name_conflict_error": "该按钮名称已被使用", "shortcut_modal_button_name_input_placeholder": "按钮上显示的名称", "shortcut_modal_button_name_is_required": "按钮名称为必填项", "shortcut_modal_cancel": "取消", "shortcut_modal_component_name": "组件名称", "shortcut_modal_component_plugin_wf_parameter": "对应参数", "shortcut_modal_component_type": "组件类型", "shortcut_modal_components": "组件", "shortcut_modal_components_hover_tip": "如果使用了组件，你需要将组件变量添加到 query 中。例如，使用了组件“主题”和“语言”，query 内容可以输入为：“查看今天主题为{var1}，语言为{var2}的新闻”。使用时只需要输入这两个变量的值即可", "shortcut_modal_components_modal_component_type": "组件类型", "shortcut_modal_components_modal_upload_component": "上传文件", "shortcut_modal_confirm": "确认", "shortcut_modal_fail_to_add_shortcut_error": "添加快捷指令失败", "shortcut_modal_fail_to_delete_shortcut_error": "删除快捷指令失败", "shortcut_modal_fail_to_update_shortcut_error": "更新快捷指令失败", "shortcut_modal_form_to_be_filled_up_tip": "如果需要使用组件变量，请添加组件并填写相应信息", "shortcut_modal_illegal_keyword_detected_error": "审核未通过", "shortcut_modal_max_component_tip": "最多可以添加 {maxCount} 个组件", "shortcut_modal_please_select_file_formats_for_upload_component_tip": "上传文件组件请选择至少一种文件类型", "shortcut_modal_query_content": "指令内容", "shortcut_modal_query_content_input_placeholder": "输入想要发送的信息内容", "shortcut_modal_query_content_is_required": "指令内容为必填项", "shortcut_modal_query_insert_component_tip": "插入组件", "shortcut_modal_query_message_hover_tip_component_mode": "如果使用了组件，你需要将组件变量添加到 query 中。例如，使用了组件“主题”和“语言”，query 内容可以输入为：“查看今天主题为 {var1}，语言为 {var2} 的新闻”。", "shortcut_modal_query_message_hover_tip_component_mode_var1": "主题", "shortcut_modal_query_message_hover_tip_component_mode_var2": "语言", "shortcut_modal_query_message_hover_tip_how_to_insert_components": "组件变量输入方式：点击右上角按钮、在输入框中输入 {", "shortcut_modal_query_message_hover_tip_send_query_mode": "如果没有组件，将直接发送输入的文案", "shortcut_modal_query_message_hover_tip_title": "使用快捷指令后发送给智能体的 query：", "shortcut_modal_query_message_insert_component_button": "插入组件变量", "shortcut_modal_query_message_max_length_reached_error": "指令内容不能超过 3000 个字符", "shortcut_modal_query_message_placeholder": "在指令信息中添加组件名，引用格式为{{name}}，也可以点击输入框右上角的按钮添加\n", "shortcut_modal_remove_plugin_wf_button": "移除", "shortcut_modal_remove_plugin_wf_double_confirm": "移除这个插件吗？", "shortcut_modal_remove_plugin_wf_double_tip": "已添加的组件也会被同时删除", "shortcut_modal_save_shortcut_with_components_unused_modal_desc": "有一些组件({unUsedComponentsNames}) 未在指令内容中引用，可能影响使用效果", "shortcut_modal_save_shortcut_with_components_unused_modal_title": "确认保存指令吗？", "shortcut_modal_selector_component_default_text": "请选择", "shortcut_modal_selector_component_no_options_error": "有选择器组件未配置或添加选项", "shortcut_modal_selector_component_options": "选项", "shortcut_modal_shortcut_action_use_plugin_wf": "直接使用插件、工作流", "shortcut_modal_shortcut_description": "指令描述", "shortcut_modal_shortcut_description_input_placeholder": "输入指令描述", "shortcut_modal_shortcut_name": "指令名称", "shortcut_modal_shortcut_name_conflict_error": "该指令名称已被使用", "shortcut_modal_shortcut_name_input_placeholder": "可以在飞书、微信公众号等渠道使用。请使用字母和下划线，如get_news", "shortcut_modal_shortcut_name_is_required": "指令名称为必填项", "shortcut_modal_skill": "工具\n", "shortcut_modal_skill_has_no_param_tip": "已选工具不包含输入参数，所以无需输入组件。点击本指令时将会直接发送指令内容", "shortcut_modal_skill_select_button": "请在上方选择工具", "shortcut_modal_title": "创建快捷指令", "shortcut_modal_title_edit_shortcut": "编辑快捷指令", "shortcut_modal_upload_component_file_format_audio": "音频", "shortcut_modal_upload_component_file_format_code": "代码", "shortcut_modal_upload_component_file_format_doc": "文档", "shortcut_modal_upload_component_file_format_img": "图片", "shortcut_modal_upload_component_file_format_ppt": "PPT", "shortcut_modal_upload_component_file_format_table": "表格", "shortcut_modal_upload_component_file_format_txt": "TXT", "shortcut_modal_upload_component_file_format_video": "视频", "shortcut_modal_upload_component_file_format_zip": "Zip", "shortcut_modal_upload_component_supported_file_formats": "支持文件类型", "shortcut_modal_use_at_least_one_letter_error": "请使用至少一个字母，示例：img_2_text", "shortcut_modal_use_tool_parameter_default_value": "默认值", "shortcut_modal_use_tool_parameter_default_value_placeholder": "默认值", "shortcut_modal_use_tool_select_button": "选择", "singleagent_LLM_mode": "单 Agent （自主规划模式）", "singleagent_LLM_mode_desc": "用户与大模型进行对话，由一个大模型自主思考决策，适用于较为简单的业务逻辑。", "singleagent_workflow_mode": "单 Agent （对话流模式）", "singleagent_workflow_mode_desc": "该智能体会严格按照对话流编排的流程进行执行，支持保留多轮历史对话记录，适用于结构化或有明确流程的任务。", "skill_role_information": "角色信息", "skillset_241115_01": "选择回答模式", "skillset_241115_02": "不经过模型总结，直接返回变量值", "skillset_241115_03": "返回变量，由模型总结输出", "space": "空格", "spot": "斑点", "stop_generating": "停止生成", "stop_receiving": "不再接收", "store_add_connector_tootip": "可在团队空间-发布管理-发布渠道管理，安装对应渠道", "store_bot_detail_title_mobile": "详情", "store_chat_placeholder_blank": "发送消息...", "store_chat_placeholder_continue": "继续对话...", "store_search_rank_default": "相关性", "store_search_recommend_result2": "插件", "store_search_recommend_result3": "工作流", "store_service_plugin": "端插件", "store_service_plugin_connector": "适用渠道", "store_service_plugin_connector_only": "该插件仅在{connector_names}渠道生效", "submit_data_to_a_specified_resource__often_used_to_submit_forms_or_upload_files_": "向指定资源提交数据，常用于提交表单或上传文件。示例：", "support_poor": "模型支持不佳", "support_poor_explain": "{modelName}对此功能的支持效果不佳。建议关闭此功能或切换其他模型\n", "supports_uploading_json_or_yaml_files": "支持上传使用OpenAPI，Swagger或Postman集合协议的JSON或YAML文件。", "switch": "开关", "switch_to_on_demand_call_warning_notsupported": "切换至{call_method}后，当前所选的模型{modelName}将不支持{toolName}模块", "switch_to_on_demand_call_warning_supportpoor": "切换至{callMethod}后，当前所选的模型{modelName}对{toolName}功能支持效果不佳", "tab": "标签页", "tab_bot_detail": "{bot_name} -智能体", "tab_dataset_detail": "{dataset_name} - 知识库", "tab_dataset_list": "知识库", "tab_explore_bot_detail": "{bot_name} - 探索", "tab_plugin_detail": "{plugin_name} - 插件", "table_view_002": "已选中 {n} 行", "task_preset_timezone": "时区", "task_preset_trigger_time": "触发时间", "team_column_role": "角色", "team_id": "团队ID", "team_management_role_admin": "管理员", "team_management_role_member": "成员", "team_management_role_owner": "所有者", "template_agent": "智能体", "template_buy_paid_agreement_action": "我已阅读并同意", "template_buy_paid_agreement_detail": "《模板付费服务协议》", "template_buy_paid_agreement_toast": "请先阅读并同意协议", "template_name": "模板", "template_workflow": "工作流", "terms_of_service": "使用协议", "text": "文本", "timecapsule_0108_003": "没有记录", "timecapsule_0124_001": "开启", "timecapsule_0124_002": "关闭", "timecapsule_1228_001": "长期记忆", "token_copied_1": "令牌已复制到剪贴板", "token_key_1": "令牌", "tool_load_error": "加载错误", "tool_new_S1_URL_error": "请输入有效URL路径。仅支持英文字母、数字等ASCII字符", "tool_new_S2_feedback_failed": "参数校验未通过，请检查后再试", "tool_para_required": "必填", "tool_updated_check_mockset_compatibility": "工具已更新，请检查模拟集是否兼容", "toolname_used_mockset_mocksetname": "{toolName}已使用模拟集：{mockSetName}", "tools_imported_successfully": "{num, plural, =1 {1个工具} other {#个工具}}成功导入", "type": "类别", "unable_to_access_input_url": "输入的URL无法正确访问", "under_review": "审核中", "unreleased_plugins_tool_cannot_create_mockset": "还未发布的插件工具，不能创建模拟集", "update_required": "需要更新", "upgrade_guide_got_it": "我知道了", "upgrade_guide_next": "下一个", "upload_avatar_success": "上传头像成功", "upload_data_or_resources_to_a_specified_location__often_used_to_update_existing_": "向指定位置上传数据或资源，常用于更新已存在的资源或创建新资源。示例：", "upload_empty_file": "上传文件为空，请检查文件内容后重试", "upload_image": "上传图片", "upload_image_format_requirement": "支持上传 png、jpg、jpeg 格式图片", "upload_image_guide": "点击上传或将图片拖拽到此处", "upload_image_size_limit": "请上传小于 {max_size} 的图片", "upload_success_failed_count": "成功上传{successNum}个文件，{failedNum}个文件上传失败", "uploading_filename": "正在上传 {filename}", "url_add_008": "删除全部", "url_raw_data": "URL 和原始数据", "use_in_bot": "在智能体中使用", "use_in_workflow": "在工作流中使用", "use_template_confirm_ cancel_text": "取消", "use_template_confirm_info": "当前模板数据将覆盖现有变量数据。您确定要使用模板并覆盖它吗？", "use_template_confirm_ok_text": "确认", "use_template_confirm_title": "使用模板", "used_to_delete_the_user_with_id_123_": "用于删除ID为123的用户。", "used_to_obtain_user_information_with_id_123": "用于获取ID为123的用户信息。", "used_to_update_user_information_with_id_123_": "用于更新ID为123的用户信息。", "user_connections_desc": "授权以下平台后，可直接在相应平台发布智能体", "user_info_custom_name": "用户昵称", "user_info_email": "邮箱", "user_info_password": "密码", "user_info_username": "用户名", "user_profile": "变量", "user_profile_intro": "用于保存用户个人信息，让智能体记住用户的特征，使回复更加个性化。", "user_revoke_authorization_title": "撤销授权？", "username_invalid_letter": "只有英文字母（A-Z、a-z）、数字和下划线（_）有效。", "username_placeholder": "输入用户名", "username_too_short": "用户名长度应为 4-20 个字符。", "variable_240407_01": "预设变量用于获取用户数据，如sys_uuid，sys_longitude和sys_feishu_open_id，它们是只读的，不能被修改。", "variable_240416_01": "预设变量是只读的，不支持修改。预设变量的更新会由系统完成。", "variable_240520_03": "变量默认支持在Prompt中访问，取消勾选后将不支持在Prompt中访问（仅能在Workflow中访问）", "variable_Button_reset_variable": "重置数据", "variable_Table_Title_edit_time": "修改时间", "variable_Table_Title_name": "名称", "variable_Table_Title_support_channels": "支持渠道", "variable_Table_Title_type": "类型", "variable_Table_Title_value": "取值", "variable_Tabname_test_data": "测试数据", "variable_app_name": "应用变量", "variable_assignment_node_select_empty": "请选择需要赋值的变量", "variable_assignment_node_select_placeholder": "请选择", "variable_binding_continue": "继续", "variable_binding_please_bind_an_agent_or_app_first": "请先绑定智能体或应用", "variable_binding_please_select_a_variable": "请选择变量", "variable_binding_search_project": "搜索智能体/应用", "variable_binding_there_are_no_variables_in_this_project": "该项目下暂时没有定义变量", "variable_button_input_json": "输入 JSON", "variable_config_change_banner": "修改变量配置可能会影响使用此变量的工作流运行，请同步更新工作流变量相关逻辑", "variable_config_toast_return_button": "去保存", "variable_config_toast_savetips": "当前有未保存的变量设置", "variable_edit_not_pass": "变量的名称、描述和图标包含不适当的内容，请修改后重新提交", "variable_edit_time": "修改时间", "variable_field_name": "字段名", "variable_field_value": "取值", "variable_name": "变量", "variable_name_placeholder": "名称（必填）", "variable_node_offline_toast": "本节点已下线，旧版本的节点不可编辑", "variable_reset": "重置数据", "variable_reset_confirm": "确认重置？", "variable_reset_fail_tips": "重置失败", "variable_reset_no": "取消", "variable_reset_succ_tips": "重置成功", "variable_reset_tips": "所有数据将会重置为默认值", "variable_reset_yes": "确认", "variable_select_empty_appide_tips": "暂无变量，请前往全局设置-变量进行配置", "variable_select_empty_library_tips": "暂无变量，请先前往试运行关联有变量配置的智能体或应用", "variable_select_empty_library_tips_02": "当前没有可选的变量", "variable_system_describtion": "可选择开启你需要获取的，系统在用户在请求自动产生的数据，仅可读不可修改。如用于通过ID识别用户或处理某些渠道特有的功能。", "variable_system_name": "系统变量", "variable_template_demo_desc": "开发人员还可以定义智能体需要保存在“字段”中的其他变量。", "variable_template_demo_text": "模板演示", "variable_template_title": "变量模板", "variable_user_description": "用于存储每个用户使用项目过程中，需要持久化存储和读取的数据，如用户的语言偏好、个性化设置等。", "variable_user_name": "用户变量", "variables_app_name_limit": "只能包含字母、数字或下划线，并且以字母或下划线开头", "variables_json_input_error": "JSON格式错误", "variables_json_input_limit": "超出JSON长度", "variables_json_input_readonly_button": "查看JSON", "variables_json_input_readonly_title": "JSON数据", "variables_user_data_empty": "暂无内容", "version": "发布历史", "vertical": "纵向", "view-all-chat-knowledge-source-header": "查看全部", "view_detailed_information": "查看详细信息", "view_workflow_details": "查看工作流详情", "visible": "可见", "voice_select_library_null": "暂无对应语言的音色", "vpc_plugin_create_plugin_1": "私网连接", "vpc_plugin_create_plugin_2": "不使用私网连接", "vpc_plugin_tooltips": "不使用私网连接：使用公网可访问的服务。\n使用私网连接：使用火山引擎的私网连接服务，需先创建私网连接。", "wf_20241206_001": "必填", "wf_chatflow_01": "用于处理对话类请求，包含特殊的入参USER_INPUT、CONVERSATION_NAME，本次执行会绑定该会话，自动写入消息到该会话、自动从该会话读对话历史。", "wf_chatflow_02": "用于处理功能类的请求，比如：生成一篇文章、获取记录列表", "wf_chatflow_03": "在应用内创建工作流", "wf_chatflow_101": "会话管理", "wf_chatflow_102": "对话类请求常常需要会话能力，保存消息记录、并用作模型上下文。会话管理提供了多会话的管理能力，可以管理静态会话和动态会话。", "wf_chatflow_103": "静态会话", "wf_chatflow_104": "在会话管理中创建的会话，是静态会话，是在功能运行前就创建好的。", "wf_chatflow_106": "应用", "wf_chatflow_107": "智能体", "wf_chatflow_109": "存在同名会话，请使用不同的名称", "wf_chatflow_111": "创建成功", "wf_chatflow_112": "删除成功", "wf_chatflow_114": "请选择", "wf_chatflow_115": "没有找到会话", "wf_chatflow_116": "会话名限制200字符", "wf_chatflow_121": "切换为{flowMode}", "wf_chatflow_122": "对话流描述不能为空", "wf_chatflow_123": "已成功切换为{Chatflow}", "wf_chatflow_124": "会话历史", "wf_chatflow_125": "开启会话历史后，会自动把开始节点入参CONVERSATION_NAME对应的会话上下文信息，发送给模型。", "wf_chatflow_126": "这个应用定义了以下变量：", "wf_chatflow_127": "这个应用还没有定义变量", "wf_chatflow_13": "创建会话所需的输入参数", "wf_chatflow_131": "输出类型", "wf_chatflow_132": "每次对话都会调用该对话流，用户“本轮对话输入”会作为对话流的输入参数 “USER_INPUT” 传入", "wf_chatflow_133": "用户的对话将调用对话流运行，并注入Start节点上的 “USER_INPUT” 系统变量参数。Start节点上除“USER_INPUT”、“CONVERSATION_NAME”以外的其他配置参数都是无效的。", "wf_chatflow_14": "会话名称，请注意会话名称不能重复", "wf_chatflow_141": "无法选择智能体，因为流程内包含会话节点，智能体暂不支持会话节点", "wf_chatflow_142": "无法选择应用，因为应用暂不支持 LTM节点", "wf_chatflow_143": "设置", "wf_chatflow_15": "创建会话的输出信息：成功时isSuccess为true；如果会话已经存在了（重名的时候）isExisted为ture。", "wf_chatflow_151": "该会话存在绑定关系，删除失败", "wf_chatflow_154": "本次请求绑定的会话，会自动写入消息、会从该会话读会话历史。可以在“设置-会话”管理已有会话", "wf_chatflow_155": "暂未配置技能", "wf_chatflow_23": "清空会话历史所需的输入参数", "wf_chatflow_24": "会话名称", "wf_chatflow_25": "清空会话历史的输出信息，成功时isSuccess为true", "wf_chatflow_33": "查询消息列表所需的输入参数", "wf_chatflow_34": "每次查询返回的数据量，取值范围为 1~50", "wf_chatflow_35": "查看指定位置之前的消息。传入空字符串，表示不指定位置。如需向前翻页，则指定为返回结果中的 first_id。", "wf_chatflow_36": "查看指定位置之后的消息。传入空字符串，表示不指定位置。如需向后翻页，则指定为返回结果中的 last_id。", "wf_chatflow_37": "查询消息列表的输出信息", "wf_chatflow_41": "暂时没有动态创建的会话", "wf_chatflow_42": "展示通过“创建会话节点”创建的会话", "wf_chatflow_43": "动态会话", "wf_chatflow_44": "展示通过“创建会话节点”创建的会话，是在运行过程中创建的会话。", "wf_chatflow_51": "删除会话", "wf_chatflow_52": "同时删除会话中的消息记录，此操作不可恢复，确定删除吗？", "wf_chatflow_53": "以下Chatflow绑定了会话：", "wf_chatflow_54": "你可以重新选择要绑定的会话，默认会绑定“默认会话”", "wf_chatflow_55": "删除", "wf_chatflow_56": "取消", "wf_chatflow_61": "会话未创建", "wf_chatflow_62": "请稍后重试", "wf_chatflow_71": "对话流入参配置", "wf_chatflow_72": "关联智能体或者应用", "wf_chatflow_73": "选择你需要的智能体或者应用", "wf_chatflow_74": "选择会话", "wf_chatflow_75": "保存并开始对话调试", "wf_chatflow_76": "对话流", "wf_chatflow_81": "创建对话流", "wf_chatflow_82": "创建同名会话，且该会话会作为对话流的CONVERSATION_NAME入参的默认值", "wf_chatflow_84": "编辑对话流", "wf_chatflow_85": "对话流名称", "wf_chatflow_86": "对话流描述", "wf_chatflow_87": "创建并绑定同名会话", "wf_chatflow_91": "请输入对话流名称", "wf_chatflow_92": "请输入描述，让大模型理解什么情况下应该调用此对话流", "wf_chatflow_93": "对话流名称不能为空", "wf_chatflow_94": "对话流名称只允许字母、数字和下划线，并以字母开头", "wf_chatflow_95": "对话流创建成功", "wf_chatflow_96": "默认值", "wf_chatflow_97": "描述", "wf_chatflow_98": "帮助大模型准确了解参数的作用", "wf_chatflow_99": "参数默认值，在没有传入该参数时，将使用默认值", "wf_history_rounds": "会话轮数", "wf_node_add_wf_modal_tip_must_publish_to_add": "发布之后才能添加至工作流", "wf_node_add_wf_modal_toast_wf_added": "工作流 {workflowName} 已添加", "wf_problem_my_tag": "本流程", "wf_problem_other_tag": "子流程", "wf_role_config_avatar_ai_tooltip": "根据名称和描述，使用 DALL·E-3 自动生成", "wf_role_config_avatar_ai_tooltip_cn": "输入名称和描述后，点击可自动生成头像。", "wf_role_config_error_toast": "生成失败：请检查名称和描述是否不符合规范或表意不明确", "wf_test_run_form_input_collapse_label": "试运行输入", "wf_testrun_ai_button_complete": "AI 补全", "wf_testrun_ai_button_complete_stop": "停止补全", "wf_testrun_ai_button_cover": "AI 覆盖", "wf_testrun_ai_button_cover_stop": "停止覆盖", "wf_testrun_ai_button_popover": "选择补全模式", "wf_testrun_ai_button_popover_complete": "空白字段补全", "wf_testrun_ai_button_popover_complete_extra": "仅补全未填写的空白字段", "wf_testrun_ai_button_popover_cover": "全字段覆盖", "wf_testrun_ai_button_popover_cover_extra": "每次都会将所有入参重新生成并覆盖", "wf_testrun_ai_gen_toast": "生成超时，本次随机填充", "wf_testrun_form_json_group_batch": "批处理数据", "wf_testrun_form_json_group_batch_extra": "试运行所需批处理数据", "wf_testrun_form_json_group_input": "输入数据", "wf_testrun_form_json_group_input_extra": "试运行所需输入", "wf_testrun_form_json_group_settings": "设置数据", "wf_testrun_form_json_group_settings_extra": "试运行所需设置数据", "wf_testrun_form_json_key_hover_no": "未定义的字段", "wf_testrun_form_mode_text": "JSON模式", "wf_testrun_form_related_title": "关联内容", "wf_testrun_log_md_llm_diff_tooltip": "当前节点的大模型输出的对象结构和节点定义的输出结构不一致，建议调整大模型提示词或输出参数描述。", "wf_testrun_log_md_llm_diff_tooltip_a": "详细文档", "wf_testrun_problems_loading": "正在检查", "wget": "wget", "what_is_coze": "什么是扣子？", "wmv_collaborate_collabration_explain": "协作者可以共同编辑、提交修改和发布 Workflow。", "wmv_diff_latest_draft": "最新版本和当前草稿存在一些差异,请选择一个版本覆盖当前草稿", "wmv_draft_version": "草稿版本", "wmv_latest_version": "最新版本", "wmv_merge_versions": "合并版本", "wmv_publish_multibranch_IntroTitle": "启用后，可以在此Workflow上添加其他人员进行协作开发：", "wmv_view_latest_version": "查看最新版本", "workflow_0224_01": "节点", "workflow_0224_02": "来自{source}", "workflow_0224_03": "已收藏插件", "workflow_0224_04": "探索更多", "workflow_0224_05": "展开更多", "workflow_0224_06": "插件商店", "workflow_240218_02": "选择你需要的变量&数据库所在的智能体", "workflow_240218_04": "这个智能体还没有定义数据库", "workflow_240218_05": "这个智能体定义了以下数据库：", "workflow_240218_07": "需要添加的输入变量，SQL中可直接引用此处添加的变量", "workflow_240218_08": "SQL执行后输出的变量，变量名需与SQL中定义的一致，变量类型需要与Table定义的字段类型一致", "workflow_240218_09": "SQL", "workflow_240218_10": "要执行的SQL语句，可以直接使用输入参数中的变量，注意rowNum输出返回的行数或者受影响的行数，outputList中的变量名需与SQL中定义的字段名一致。", "workflow_240218_11": "自动生成", "workflow_240218_12": "可以使用{{变量名}}、{{变量名.子变量名}}、{{变量名[数组索引]}}的方式引用输入参数中的变量", "workflow_240218_15": "查询目标", "workflow_240218_16": "请通过自然语言描述你的查询目标", "workflow_240218_17": "取消", "workflow_240218_18": "使用", "workflow_240221_01": "用户唯一标识，由系统生成", "workflow_240919_01": "未配置", "workflow_240919_02": "问答类型", "workflow_240919_03": "用户不可见", "workflow_241015_01": "输入或引用参数值", "workflow_241111_01": "输出内容", "workflow_241111_02": "返回变量", "workflow_241111_03": "返回文本", "workflow_241119_01": "节点", "workflow_241120_01": "用户输入的信息会存储在对应的变量中", "workflow_250117_01": "极速模式下最多支持10个意图配置", "workflow_250117_02": "完整模式下最多支持50个意图配置", "workflow_250117_03": "极速模式", "workflow_250117_04": "完整模式", "workflow_250117_05": "最多支持 { maxCount } 个", "workflow_250213_01": "reasoning_content是系统字段，请修改变量名", "workflow_250217_01": "推理内容", "workflow_250217_02": "推理内容，支持输出思维链的模型特有", "workflow_250305_001": "暂无相关结果", "workflow_250305_002": "请检查关键词是否正确，或尝试更换关键词搜索", "workflow_250306_01": "搜索节点、插件、工作流", "workflow_250310_02": "已失效", "workflow_250310_03": "该模型不支持绑定技能", "workflow_250310_04": "视觉理解输入", "workflow_250310_05": "所选模型不支持视觉理解", "workflow_250310_06": "技能调用", "workflow_250310_09": "上传", "workflow_250310_10": "输入URL", "workflow_250310_11": "输入", "workflow_250310_12": "输出", "workflow_250310_13": "复制", "workflow_250310_14": "已添加子节点，不再支持输入", "workflow_250310_15": "通过添加子节点进行配置", "workflow_250317_01": "不能和输入重名", "workflow_250317_02": "不能和视觉理解输入重名", "workflow_250320_01": "所选模型不支持图片理解", "workflow_250320_02": "所选模型不支持视频理解", "workflow_250320_03": "用于视觉理解的输入，传入图片url，并在Prompt中引用该输入，举例：“图片{{变量名}}中有什么？”", "workflow_250407_001": "消息自动写入", "workflow_250407_002": "开启后，对话流在运行时会自动写入消息，包括用户发出的消息、返回给用户的消息。", "workflow_250407_005": "会话的名字", "workflow_250407_006": "消息的角色名，限制为user或者assistant。user代表该条消息内容是用户发出的；assistant代表该条消息内容是返回给用户的。", "workflow_250407_007": "消息的内容", "workflow_250407_010": "会话的名字", "workflow_250407_011": "消息的ID", "workflow_250407_012": "修改后的消息内容", "workflow_250407_015": "会话的名字", "workflow_250407_016": "消息的ID", "workflow_250407_019": "要修改的会话的名字", "workflow_250407_020": "修改后的名字", "workflow_250407_023": "会话的名字", "workflow_250407_028": "会话的名字", "workflow_250407_029": "返回的会话轮数，最多支持30轮。", "workflow_250407_201": "异常处理", "workflow_250407_202": "执行异常流程", "workflow_250407_203": "备选模型", "workflow_250407_204": "重试时会使用备选模型", "workflow_250407_205": "超时时间", "workflow_250407_206": "重试次数", "workflow_250407_207": "异常处理方式", "workflow_250407_208": "中断流程", "workflow_250407_209": "发生异常后，中断流程执行。异常信息将会显示在节点卡片上，或者通过调用结果返回。", "workflow_250407_210": "返回设定内容", "workflow_250407_211": "发生异常后，流程不会中断。异常信息会通过isSuccess、errorBody返回。开发者可设定需要返回的内容。", "workflow_250407_212": "执行异常流程", "workflow_250407_213": "发生异常后，流程不会中断。异常信息会通过isSuccess、errorBody返回，同时会新增异常分支。开发者需要完善异常处理流程后，方可运行流程。", "workflow_250407_214": "需要完善节点的异常处理流程", "workflow_250416_01": "异常处理", "workflow_250416_03": "开启流式输出后，一旦开始输出数据，即使出现异常也无法重试或者跳转异常分支。", "workflow_250416_04": "选择备选模型", "workflow_250416_05": "需要先把节点的异常处理方式改为“中断流程”或者“返回设定内容”，才能改为批处理模式。", "workflow_250416_06": "不重试", "workflow_250416_07": "重试1次", "workflow_250416_08": "自定义返回内容", "workflow_250421_01": "异常，执行异常流程", "workflow_250421_02": "异常，返回设定内容", "workflow_250421_03": "可设置异常处理，包括超时、重试、异常处理方式。", "workflow_250421_04": "超时区间", "workflow_250508_01": "端插件不支持超时配置", "workflow_LLM_node_sp_title": "系统提示词", "workflow_abnormal_connection": "连线异常", "workflow_add_common_loading": "加载中", "workflow_add_condition": "添加条件", "workflow_add_create_library": "创建工作流", "workflow_add_created_list_empty_description": "工作流可以为智能体构建复杂的功能逻辑，例如旅行计划、报告分析等", "workflow_add_created_list_empty_title": "工作流", "workflow_add_created_tab_all": "全部", "workflow_add_created_tab_mine": "我创建的", "workflow_add_delete": "删除", "workflow_add_delete_fail": "删除失败", "workflow_add_delete_success": "删除成功", "workflow_add_example": "官方示例", "workflow_add_imageflow_toast_success": "图像流{name}已添加", "workflow_add_input": "新增", "workflow_add_list_add": "添加", "workflow_add_list_added": "已添加", "workflow_add_list_added_fail": "添加至智能体失败", "workflow_add_list_added_id_empty": "获取工作流信息失败", "workflow_add_list_added_success": "已添加至智能体", "workflow_add_list_copy": "创建副本", "workflow_add_list_created": "创建于", "workflow_add_list_empty_title": "暂无相关工作流", "workflow_add_list_publised": "发布于", "workflow_add_list_remove": "从智能体移除", "workflow_add_list_removed_success": "已从智能体移除", "workflow_add_list_unknown": "unknown", "workflow_add_list_updated": "更新于", "workflow_add_navigation_create": "创建工作流", "workflow_add_navigation_explore": "探索工作流", "workflow_add_navigation_my": "我创建的", "workflow_add_navigation_team": "工作空间工作流", "workflow_add_not_allow_before_publish": "发布之后才能添加至智能体", "workflow_add_output": "新增", "workflow_add_parameter_required": "必填", "workflow_add_remove_confirm_content": "删除后，相关智能体中的引用将失效", "workflow_add_remove_confirm_title": "确定从智能体中移除吗？", "workflow_add_search_placeholder": "搜索", "workflow_add_status_published": "已发布", "workflow_add_status_unpublished": "未发布", "workflow_add_title": "添加工作流", "workflow_add_to_workflow": "添加到工作流", "workflow_agent_add": "点击添加对话流", "workflow_agent_configure": "对话流配置", "workflow_agent_dialog_set": "对话设置", "workflow_agent_dialog_set_chathistory": "输入设置", "workflow_agent_dialog_set_desc": "必须在工作流中的“大模型节点”上的输入模块启动“智能体对话历史”参数，对话设置才会生效。", "workflow_ans_content_placeholder": "可以使用{{变量名}}引入输入参数中的变量", "workflow_area_select": "框选", "workflow_batch_canvas_title": "批处理体", "workflow_batch_canvas_tooltips": "用于编排批处理体的逻辑", "workflow_batch_error_items": "错误数", "workflow_batch_error_only": "只看错误", "workflow_batch_inputs": "输入", "workflow_batch_inputs_tooltips": "批处理体中节点要使用的变量", "workflow_batch_no_failed_entries": "无错误", "workflow_batch_outputs": "输出", "workflow_batch_outputs_tooltips": "批处理完成后输出的内容，仅支持引用批处理体中节点的输出变量", "workflow_batch_settings": "批处理设置", "workflow_batch_tab_batch_radio": "批处理", "workflow_batch_tab_single_radio": "单次", "workflow_batch_total_items": "总数", "workflow_chatflow_testrun_conversation_des": "Chatflow调试产生的对话数据，数据不会影响线上数据，发布后请在对应渠道查看线上产生的对话数据", "workflow_chathistory_testrun_nocontent": "这个智能体还没有聊天历史", "workflow_chathistory_testrun_title": "这个智能体包含以下会话历史，来源于智能体中设置的上下文轮数：", "workflow_code_js_illustrate_all": "// 在这里，您可以通过 'params'  获取节点中的输入变量，并通过 'ret' 输出结果\n// 'params' 已经被正确地注入到环境中\n// 下面是一个示例，获取节点输入中参数名为'input'的值：\n// const input = params.input; \n// 下面是一个示例，输出一个包含多种数据类型的 'ret' 对象：\n// const ret = { \"name\": '小明', \"hobbies\": [\"看书\", \"旅游\"] };", "workflow_code_js_illustrate_output": "// 构建输出对象", "workflow_code_js_illustrate_output_arr": "// 输出一个数组", "workflow_code_js_illustrate_output_obj": "// 输出一个Object", "workflow_code_js_illustrate_output_param": "// 拼接两次入参 input 的值", "workflow_code_py_illustrate_all": "# 在这里，您可以通过 'args'  获取节点中的输入变量，并通过 'ret' 输出结果\n# 'args' 已经被正确地注入到环境中\n# 下面是一个示例，首先获取节点的全部输入参数params，其次获取其中参数名为'input'的值：\n# params = args.params; \n# input = params['input'];\n# 下面是一个示例，输出一个包含多种数据类型的 'ret' 对象：\n# ret: Output =  { \"name\": '小明', \"hobbies\": [\"看书\", \"旅游\"] };", "workflow_code_py_illustrate_output": "# 构建输出对象", "workflow_code_py_illustrate_output_arr": "# 输出一个数组", "workflow_code_py_illustrate_output_obj": "# 输出一个Object ", "workflow_code_py_illustrate_output_param": "# 拼接两次入参 input 的值", "workflow_code_testrun_sync": "同步输出", "workflow_condition_empty": "查询条件为空", "workflow_condition_left_placeholder": "请选择", "workflow_condition_obj_contain": "包含键名", "workflow_condition_obj_not_contain": "不包含键名", "workflow_condition_operation_be_false": "为假", "workflow_condition_operation_be_true": "为真", "workflow_condition_operation_equal": "等于", "workflow_condition_operation_greater_equal": "大于等于", "workflow_condition_operation_greater_than": "大于", "workflow_condition_operation_in": "属于", "workflow_condition_operation_is_not_null": "不为空", "workflow_condition_operation_is_null": "为空", "workflow_condition_operation_less_equal": "小于等于", "workflow_condition_operation_less_than": "小于", "workflow_condition_operation_like": "模糊匹配", "workflow_condition_operation_not_equal": "不等于", "workflow_condition_operation_not_in": "不属于", "workflow_condition_operation_not_like": "模糊不匹配", "workflow_confirm_modal_cancel": "取消", "workflow_confirm_modal_ok": "确定", "workflow_connection_delete": "该连线已被删除", "workflow_connection_name": "连线", "workflow_database_delete_confirm_modal_content": "移除后，该节点配置的相关内容均会被删除且无法恢复", "workflow_database_delete_confirm_modal_title": "确认移除该数据表？", "workflow_database_no_fields": "没有可添加的字段", "workflow_database_node_database_empty": "请添加数据表到此节点，仅支持添加一个数据表", "workflow_database_node_database_table_title": "数据表", "workflow_database_query_limit": "最多一次查询 1000 条数据", "workflow_debug_data_save": "运行后自动将此数据保存为测试集", "workflow_debug_run": "运行", "workflow_debug_testonenode": "测试该节点", "workflow_debug_testonenode_group": "{nodeTitle}节点", "workflow_debug_testset_placeholder": "测试集", "workflow_debug_wrong_json": "请输入正确的JSON结构", "workflow_delete_conditon_title": "删除条件", "workflow_derail_node_detail_title_max": "最大长度不可超过 {max}", "workflow_detail_api_input_tooltip": "请输入API的参数，当此节点运行时，会将这些参数传入并调用这个API", "workflow_detail_batch_item_tooltip": "item 将逐次引用 {name} 中的每一项", "workflow_detail_code_code": "代码", "workflow_detail_code_code_tooltip": "参考代码示例编写一个函数的结构，你可以直接使用输入参数中的变量，并通过return一个对象来输出处理结果\n此功能不支持编写多个函数\n即使仅有一个输出值，也务必保持以对象的形式return", "workflow_detail_code_edit_in_ide": "在IDE中编辑", "workflow_detail_code_input_tooltip": "输入需要添加到代码的变量，代码中可以直接引用此处添加的变量", "workflow_detail_code_is_running": "代码正在运行", "workflow_detail_code_is_terminate_execution": "继续操作会终止代码运行", "workflow_detail_code_output_tooltip": "代码运行完成后输出的变量，必须保证此处定义的变量名、变量类型与代码的return对象中完全一致", "workflow_detail_code_view_in_ide": "在IDE中查看", "workflow_detail_condition_and": "且", "workflow_detail_condition_comparison": "比较值", "workflow_detail_condition_condition": "条件", "workflow_detail_condition_condition_empty": "条件不可为空", "workflow_detail_condition_else": "否则", "workflow_detail_condition_error_enter_comparison": "请输入比较值", "workflow_detail_condition_error_refer_empty": "引用变量不可为空", "workflow_detail_condition_or": "或", "workflow_detail_condition_pleaseselect": "请选择", "workflow_detail_condition_reference": "引用变量", "workflow_detail_condition_select": "选择条件", "workflow_detail_condition_select_contain": "包含", "workflow_detail_condition_select_empty": "为空", "workflow_detail_condition_select_equal": "等于", "workflow_detail_condition_select_false": "为false", "workflow_detail_condition_select_greater": "大于", "workflow_detail_condition_select_greater_equal": "大于等于", "workflow_detail_condition_select_less": "小于", "workflow_detail_condition_select_less_equal": "小于等于", "workflow_detail_condition_select_longer": "长度大于", "workflow_detail_condition_select_longer_equal": "长度大于等于", "workflow_detail_condition_select_not_contain": "不包含", "workflow_detail_condition_select_not_empty": "不为空", "workflow_detail_condition_select_not_equal": "不等于", "workflow_detail_condition_select_shorter": "长度小于", "workflow_detail_condition_select_shorter_equal": "长度小于等于", "workflow_detail_condition_select_true": "为true", "workflow_detail_edit_prompt_button": "不再提醒", "workflow_detail_end_answer": "回答内容", "workflow_detail_end_answer_copy": "复制回答内容", "workflow_detail_end_answer_example": "可以使用{{变量名}}、{{变量名.子变量名}}、{{变量名[数组索引]}}的方式引用输出参数中的变量", "workflow_detail_end_answer_tooltip": "编辑智能体的回复内容，即工作流运行完成后，智能体中的LLM将不再组织语言，而是直接用这里编辑的内容原文回复对话。\n可以使用{{变量名}}的方式引用输入参数中的变量", "workflow_detail_end_output": "输出变量", "workflow_detail_end_output_copy": "复制输出变量", "workflow_detail_end_output_entername": "输入变量名", "workflow_detail_end_output_name": "变量名", "workflow_detail_end_output_tooltip": "这些变量将在智能体调用工作流完成后被输出。在“返回变量”模式中，这些变量会被智能体总结后回复用户；在“返回文本”模式中，智能体只会回复你设定的“回答内容”。但在任何模式中，这些变量都可以在配置卡片时使用。", "workflow_detail_end_output_value": "变量值", "workflow_detail_error_interface_initialization": "接口初始化错误", "workflow_detail_error_message": "错误信息：{msg}", "workflow_detail_error_missing_id": "缺少 workflow_id", "workflow_detail_knowledge_error_empty": "知识不可为空", "workflow_detail_knowledge_input_tooltip": "输入需要从知识中匹配的关键信息", "workflow_detail_knowledge_knowledge": "知识库", "workflow_detail_knowledge_knowledge_tooltip": "选择需要匹配的知识范围，仅从选定的知识中召回信息", "workflow_detail_knowledge_output_tooltip": "输出列表在是所有选定知识中召回的与输入参数最匹配的信息", "workflow_detail_knowledge_proceed_with_caution": "请谨慎操作", "workflow_detail_layout_optimization_tooltip": "优化布局", "workflow_detail_llm_input_tooltip": "输入需要添加到提示词的信息，这些信息可以被下方的提示词引用", "workflow_detail_llm_model": "模型", "workflow_detail_llm_output_decription": "请描述变量用途", "workflow_detail_llm_output_decription_title": "描述", "workflow_detail_llm_output_tooltip": "大模型运行完成后生成的内容", "workflow_detail_llm_prompt": "用户提示词", "workflow_detail_llm_prompt_content": "用户提示词，可以使用{{变量名}}、{{变量名.子变量名}}、{{变量名[数组索引]}}的方式引用输入参数中的变量", "workflow_detail_llm_prompt_error_empty": "提示词不可为空", "workflow_detail_llm_prompt_tooltip": "向模型提供用户指令，如查询或任何基于文本输入的提问。", "workflow_detail_llm_sys_prompt_content": "系统提示词，可以使用{{变量名}}、{{变量名.子变量名}}、{{变量名[数组索引]}}的方式引用输入参数中的变量", "workflow_detail_llm_sys_prompt_content_tips": "为对话提供系统级指导，如设定人设和回复逻辑。 ", "workflow_detail_node_batch": "批处理", "workflow_detail_node_batch_tooltip": "批处理模式下节点会多次运行，在每一次运行中，批处理列表将按照顺序将列表中的一项赋值给批处理变量，直到达到批处理上限或列表最大长度", "workflow_detail_node_delete": "删除", "workflow_detail_node_error_empty": "变量值不可为空", "workflow_detail_node_error_format": "只能包含字母、数字或下划线，并且以字母或下划线开头", "workflow_detail_node_error_name_duplicated": "当前变量名 {name} 重复", "workflow_detail_node_error_name_empty": "变量名不可为空", "workflow_detail_node_error_variablename_duplicated": "参数名不能重复", "workflow_detail_node_input": "输入", "workflow_detail_node_input_duplicated": "变量名不可重复", "workflow_detail_node_input_entername": "输入参数名", "workflow_detail_node_input_entervalue": "选择参数值", "workflow_detail_node_input_selectvalue": "输入参数值", "workflow_detail_node_name_default": "输入节点名称", "workflow_detail_node_name_error_empty": "节点名称不可为空", "workflow_detail_node_nodata": "暂无数据", "workflow_detail_node_output": "输出", "workflow_detail_node_output_add_subitem": "新增子项", "workflow_detail_node_output_parsingfailed": "解析失败，请删除该节点重新添加", "workflow_detail_node_parameter_input": "输入", "workflow_detail_node_parameter_name": "变量名", "workflow_detail_node_parameter_reference": "引用", "workflow_detail_node_parameter_value": "变量值", "workflow_detail_node_rename": "重命名", "workflow_detail_node_save_error": "保存错误", "workflow_detail_node_workflows_max": "Display up to {number} recently modified workflows", "workflow_detail_node_workflows_referencing": "{number} 条工作流引用", "workflow_detail_node_workflows_referencing_impact": "发布影响", "workflow_detail_node_workflows_referencing_invalid": "相关工作流可能无效", "workflow_detail_node_workflows_referencing_relationship": "引用关系", "workflow_detail_node_workflows_referencing_tip": "总共 {number} 条工作流已经引用了这个工作流。", "workflow_detail_node_workflows_referencing_update": "此工作流包含以下引用关系，发布更新将产生以下影响。你确定要发布更新吗?", "workflow_detail_redo_tooltip": "重做", "workflow_detail_select_delete_popup_subtitle": "删除后将无法恢复", "workflow_detail_select_delete_popup_title": "是否删除所选节点？", "workflow_detail_start_input_tooltip": "定义启动工作流需要的输入参数，这些内容将在智能体对话过程中被LLM阅读，使LLM可以在合适的时候启动工作流并填入正确的信息", "workflow_detail_start_variable_type": "变量类型", "workflow_detail_sub_workflow_change_banner": "修改并发布变更可能会影响引用此工作流的其他工作流和智能体的运行。", "workflow_detail_testrun_bot": "关联智能体", "workflow_detail_testrun_error_front": "错误：", "workflow_detail_testrun_panel_batch_naviagte_empty": "未执行后续批处理", "workflow_detail_testrun_panel_batch_naviagte_stop": "变量 {variable} 只包含 {len} 项，因此超过 {len} 次后批处理将无法运行", "workflow_detail_testrun_panel_batch_value": "本次批处理变量", "workflow_detail_testrun_panel_final_output": "最终输出", "workflow_detail_testrun_panel_final_output2": "节点输出", "workflow_detail_testrun_panel_raw_output": "原始输出", "workflow_detail_testrun_panel_raw_output_code": "代码输出", "workflow_detail_testrun_panel_raw_output_llm": "大模型输出", "workflow_detail_testrun_variable_node_field": "这个智能体定义了以下变量：", "workflow_detail_testrun_variable_node_nofield": "这个智能体还没有定义变量", "workflow_detail_testrun_variable_node_select": "请选择智能体", "workflow_detail_testrun_warning_front": "警告：", "workflow_detail_title_copy": "创建副本", "workflow_detail_title_cost": "费用", "workflow_detail_title_lastrun_display": "显示上次运行结果", "workflow_detail_title_lastrun_hide": "隐藏运行结果", "workflow_detail_title_previewing": "预览中", "workflow_detail_title_publish": "发布", "workflow_detail_title_publish_toast": "已发布工作流", "workflow_detail_title_published": "已发布", "workflow_detail_title_saved_2": "已自动保存 {time}", "workflow_detail_title_source": "来源", "workflow_detail_title_testrun": "试运行", "workflow_detail_title_testrun_cancel": "取消运行", "workflow_detail_title_testrun_copy_batch": "复制本次批处理变量", "workflow_detail_title_testrun_copyinput": "复制输入值", "workflow_detail_title_testrun_copyoutput": "复制输出值", "workflow_detail_title_testrun_error_input": "请输入{a}", "workflow_detail_title_testrun_failed_node": "运行失败", "workflow_detail_title_testrun_finished": "运行完成", "workflow_detail_title_testrun_process": "运行结果", "workflow_detail_title_testrun_running": "试运行中", "workflow_detail_title_testrun_submit": "提交", "workflow_detail_title_testrun_succeed_node": "运行成功", "workflow_detail_title_token": "Token", "workflow_detail_title_total": "总计", "workflow_detail_toast_createcopy_failed": "创建失败，未返回有效workflow_id", "workflow_detail_toast_createcopy_succeed": "已创建工作流副本", "workflow_detail_toast_validation_failed": "有校验失败的节点，请检查配置好后，再试运行", "workflow_detail_undo_tooltip": "撤销", "workflow_detail_unknown_variable": "未知的变量", "workflow_detail_variable_get_title": "获取变量值", "workflow_detail_variable_input_name": "变量名", "workflow_detail_variable_input_value": "变量值", "workflow_detail_variable_referenced_error": "引用变量不存在", "workflow_detail_variable_set_output_tooltip": "设置Variable的结果。\n如果设置成功，则输出True。\n如果智能体中没有此Variable或因其他原因设置失败，则输出False。", "workflow_detail_variable_set_title": "设置变量值", "workflow_detail_variable_subtitle": "用于在智能体中读取和写入变量，变量名必须与智能体中的变量名匹配。", "workflow_encapsulate_button": "封装工作流", "workflow_encapsulate_button_unable": "无法封装工作流", "workflow_encapsulate_button_unable_connected": "有中间节点连到框选范围外的节点，或有多个节点连到多个框选范围外的节点", "workflow_encapsulate_button_unable_continue_or_teiminate": "框选范围内包含继续循环/终止循环", "workflow_encapsulate_button_unable_loop_or_batch": "循环体或批处理体关联的循环或批处理节点未框选", "workflow_encapsulate_button_unable_start_or_end": "框选范围内包含开始/结束", "workflow_encapsulate_button_unable_uncomplete": "封装不应该包含没有完整连线的节点", "workflow_encapsulate_decapsulate": "解散工作流", "workflow_encapsulate_selecet": "已选中 {length} 个节点", "workflow_encapsulate_toast_batch_or_loop": "在循环/批处理中解散的工作流不能包含循环或批处理节点", "workflow_error_jump_tip": "此工作流发生未知错误。请尝试将其再次添加到智能体。", "workflow_example_succeed": "使用成功", "workflow_exception_ignore_default_output": "默认输出", "workflow_exception_ignore_desc": "忽略异常并在异常发生时使用默认输出替代", "workflow_exception_ignore_format": "格式化", "workflow_exception_ignore_icon_tips": "异常设置", "workflow_exception_ignore_json_error": "异常设置中JSON不合法", "workflow_exception_ignore_tag": "忽略", "workflow_exception_ignore_title": "异常忽略", "workflow_exception_json_error": "输出中有重复变量名,自动生成JSON失败\n", "workflow_inputs_empty": "参数为空", "workflow_intent_advance_set_placeholder": "支持额外的系统提示词，如对意图选项提供更详细的例子以增强用户输入与意图匹配的成功率。", "workflow_intent_advance_set_tooltips": "输入内容将会被追加到系统提示词中", "workflow_intent_input_tooltips": "输入需要做意图识别判断的参数", "workflow_intent_matchlist_else": "其他意图", "workflow_intent_matchlist_error1": "选项内容不能为空", "workflow_intent_matchlist_error2": "选项字符不能大于1000", "workflow_intent_matchlist_placeholder": "请输入用户意图的描述，如售后问题等", "workflow_intent_matchlist_title": "意图匹配", "workflow_intent_matchlist_tooltips": "用于与用户输入匹配的意图选项", "workflow_intent_output_tooltips": "匹配的意图选项将作为变量被其他节点引用。", "workflow_interactive_mode": "交互模式", "workflow_interactive_mode_mouse_friendly": "鼠标友好模式", "workflow_interactive_mode_mouse_friendly_desc": "鼠标左键按住移动画布，滚轮缩放", "workflow_interactive_mode_pad_friendly": "触控板友好模式", "workflow_interactive_mode_pad_friendly_desc": "双指同向滑动移动画布，双指张开捏合缩放", "workflow_interactive_mode_popover_title": "支持两种画布交互模式", "workflow_json_button_node_update": "同步JSON到节点", "workflow_json_import": "JSON 导入", "workflow_json_node_update_tips_content": "当前JSON数据将覆盖节点上的数据", "workflow_json_node_update_tips_title": "同步JSON到节点", "workflow_json_syntax_error": "JSON 语法错误", "workflow_json_windows_title": "请输入JSON数据", "workflow_json_windows_title_tips": "将 JSON 数据转换为节点上的数据结构，输入请遵循一下规则：\n1.key的名字长度最长 20 字符，超出将自动截断\n2.value值不能为 null，否则将自动忽略\n3.嵌套层级最多3 层，超出将自动截断", "workflow_knowledeg_un_exit": "无效知识库", "workflow_knowledeg_unexit_error": "知识库 {id} 不存在", "workflow_knowledge_node_empty": "请添加知识库到此节点", "workflow_list_create_modal_description_label": "工作流描述", "workflow_list_create_modal_description_placeholder": "请输入描述，让大模型理解什么情况下应该调用此工作流", "workflow_list_create_modal_description_rule_required": "请输入描述，让大模型理解什么情况下应该调用此工作流", "workflow_list_create_modal_footer_cancel": "取消", "workflow_list_create_modal_footer_confirm": "确认", "workflow_list_create_modal_name_label": "工作流名称", "workflow_list_create_modal_name_placeholder": "请输入工作流名称", "workflow_list_create_modal_name_rule_reg": "工作流名称只允许字母、数字和下划线，并以字母开头", "workflow_list_create_modal_name_rule_required": "请输入工作流名称", "workflow_list_create_modal_title": "创建工作流", "workflow_list_create_modal_workflow_id_empty": "Workflow id 未提供", "workflow_list_create_success": "工作流创建成功", "workflow_list_edit_modal_title": "编辑工作流", "workflow_list_scope_all": "所有人", "workflow_list_scope_mine": "由我创建", "workflow_list_sort_create_time": "创建时间", "workflow_list_sort_edit_time": "编辑时间", "workflow_list_status_all": "全部", "workflow_list_status_published": "已发布", "workflow_list_status_unpublished": "未发布", "workflow_list_update_success": "更新成功", "workflow_loop_body_canva": "循环体", "workflow_loop_body_canva_tips": "用于编排循环主逻辑", "workflow_loop_count": "循环次数", "workflow_loop_count_tooltips": "输入数值或引用数值类变量", "workflow_loop_loop_times": "循环数组", "workflow_loop_loop_times_tips": "仅支持引用数组，循环次数为数组的长度；若无任何引用则默认无限循环，需配合“终止循环”节点完成循环流程。", "workflow_loop_loop_variables": "中间变量", "workflow_loop_loop_variables_tips": "变量可在多次循环中实现共享，可用于在多次循环中传递变量", "workflow_loop_name_no_index_wrong": "不允许使用index作为变量名", "workflow_loop_nest_tips": "不支持嵌套循环/批量节点", "workflow_loop_onlycanva_tips": "仅支持将其添加到循环体的画布上", "workflow_loop_output": "输出", "workflow_loop_output_tips": "循环完成后输出的内容，仅支持引用循环体中节点的输出变量", "workflow_loop_release_tips": "释放按钮将节点放置在画布上", "workflow_loop_set_variable_loopvariable": "中间变量", "workflow_loop_set_variable_samewrong": "变量不能相同", "workflow_loop_set_variable_set": "设置", "workflow_loop_set_variable_typewrong": "变量类型不一致", "workflow_loop_set_variable_variable": "设置值", "workflow_loop_title": "循环设置", "workflow_loop_type": "循环类型", "workflow_loop_type_array": "使用数组循环", "workflow_loop_type_count": "指定循环次数", "workflow_loop_type_infinite": "无限循环", "workflow_loop_type_tooltips": "如果引用数组，循环次数为数组的长度；如果指定次数，循环次数为指定的次数；如果选择无限循环，需配合“终止循环”节点完成循环流程。", "workflow_maximum_parallel_runs": "并行运行数量", "workflow_maximum_parallel_runs_tips": "并行运行数量，设置为1则全部串行执行", "workflow_maximum_run_count": "批处理次数上限", "workflow_maximum_run_count_tips": "批处理运行总次数不超过该上限", "workflow_message_anwser_tooltips": "编辑智能体的回复内容,即工作流运行过程中，智能体将直接用这里编辑的内容原文回复对话。可以使用{{变量名}}的方式引用输出参数中的变量。", "workflow_message_streaming_name": "流式输出", "workflow_message_streaming_tooltips": "开启时，回复内容中的大语言模型的生成内容将会逐字流式输出；关闭时，回复内容将全部生成后一次性输出。", "workflow_message_variable_tooltips": "这些变量将在智能体调用工作流过程中被输出。智能体只会回复你设定的\"回答内容\"。这些变量都可以在配置卡片时使用。", "workflow_mouse_friendly": "鼠标友好模式", "workflow_mouse_friendly_desc": "鼠标左键拖动画布，滚轮缩放", "workflow_multi_choice_copy_failed": "无法复制开始/结束节点", "workflow_multi_choice_copy_partial_success": "无法复制开始/结束节点，其他节点已复制到剪贴板", "workflow_multi_choice_copy_success": "节点已复制到剪贴板", "workflow_multi_choice_delete_failed": "无法删除开始/结束节点", "workflow_no_change_tooltip": "流程未做修改，无需二次发布", "workflow_node_copy_othercanva": "当前画布类型不一致，无法粘贴", "workflow_node_from_store": "从商店引用", "workflow_node_has_delete": "该节点已被删除", "workflow_node_invalid": "节点已失效", "workflow_node_lose_efficacy": "插件 {name} 已失效", "workflow_node_lose_efficacy_wf": "工作流 {name} 已失效", "workflow_node_stoprun": "终止运行", "workflow_node_title_duplicated": "节点名重复，请修改当前节点名", "workflow_note_bold": "粗体", "workflow_note_bulleted_list": "无序列表", "workflow_note_heading": "标题", "workflow_note_hyperlink": "超链接", "workflow_note_italic": "斜体", "workflow_note_main_text": "正文", "workflow_note_numbered_list": "有序列表", "workflow_note_placeholder": "输入要添加的注释...", "workflow_note_quote": "引用", "workflow_note_strikethrough": "删除线", "workflow_note_underline": "下划线", "workflow_order_by_empty": "还未配置排序方式", "workflow_order_by_title": "排序方式", "workflow_pad_friendly": "触控板友好模式", "workflow_pad_friendly_desc": "双指同向移动拖动，双指张开捏合缩放", "workflow_prompt_editor_expand": "展开", "workflow_prompt_editor_skill": "技能", "workflow_prompt_editor_variable": "变量", "workflow_prompt_editor_view_library": "提示词库", "workflow_publish_multibranch_PleaseSelect": "请选择", "workflow_publish_multibranch_RetainedResult": "保留结果", "workflow_publish_multibranch_auto_saved": "草稿自动保存于{time}", "workflow_publish_multibranch_change_picture": "修改图标", "workflow_publish_multibranch_changes": "变更", "workflow_publish_multibranch_changetype": "变更类型", "workflow_publish_multibranch_default_value": "默认值", "workflow_publish_multibranch_diffNodice": "最新版本与你的草稿存在一些差异。你应该选择一个版本并将其合并到你的草稿中。", "workflow_publish_multibranch_diff_btn": "查看差异", "workflow_publish_multibranch_hidediff": "隐藏差异", "workflow_publish_multibranch_history": "历史", "workflow_publish_multibranch_latest_version": "最新版本", "workflow_publish_multibranch_merge": "合并", "workflow_publish_multibranch_merge_comfirm": "有新的版本", "workflow_publish_multibranch_merge_comfirm_desc": "有人提交了更新的版本，在提交前，你需要拉取并合入到你的草稿。", "workflow_publish_multibranch_merge_retrieve": "拉取", "workflow_publish_multibranch_merge_success": "合并到草稿成功", "workflow_publish_multibranch_merge_to_draft": "合并到草稿", "workflow_publish_multibranch_modify": "修改", "workflow_publish_multibranch_modity_flow": "修改流程", "workflow_publish_multibranch_my_draft": "我的草稿", "workflow_publish_multibranch_no_diff": "最新版本与当前草稿没有冲突。最新版本可以直接合并到当前草稿中。", "workflow_publish_multibranch_nodiff": "无差异", "workflow_publish_multibranch_property": "属性", "workflow_publish_multibranch_publish_btn": "发布", "workflow_publish_multibranch_publish_comfirm_title": "确定要发布吗？", "workflow_publish_multibranch_publish_confirm_content": "发布后，这个版本才会对其他的智能体/工作流生效", "workflow_publish_multibranch_publish_disabled_tooltip": "提交后可以发布", "workflow_publish_multibranch_published": "{name}发布于{time}", "workflow_publish_multibranch_published_title": "发布", "workflow_publish_multibranch_revert_confirm_content": "还原后，将覆盖当前未提交的草稿版本", "workflow_publish_multibranch_revert_confirm_title": "还原为此版本", "workflow_publish_multibranch_revert_success": "恢复成功", "workflow_publish_multibranch_submit_btn": "提交", "workflow_publish_multibranch_submit_comfirm": "提交到工作空间", "workflow_publish_multibranch_submit_comfirm_desc": "提交后，此版本可以被工作空间中的其他成员查看。直接发布前，它不会对其他的智能体/Workflow生效。", "workflow_publish_multibranch_submit_disabled_tooltip_nochange": "没有可提交的修改", "workflow_publish_multibranch_submit_success": "提交到工作空间成功", "workflow_publish_multibranch_submitted": "{name}提交于{time}", "workflow_publish_multibranch_submitted_title": "提交", "workflow_publish_multibranch_view_conflict": "仅查看冲突", "workflow_publish_multibranch_view_lastest_version": "查看最新版本", "workflow_publish_multibranch_viewhistory": "查看历史", "workflow_publish_multibranch_workflow_btn": "工作流", "workflow_publish_multibranch_workflow_describe": "描述", "workflow_publish_multibranch_workflow_flow": "流程结构", "workflow_publish_multibranch_workflow_name": "名称", "workflow_publish_multibranch_workflow_picture": "图标", "workflow_publish_not_testrun_ insist": "坚持发布", "workflow_publish_not_testrun_content": "发布前未进行试运行,建议确保工作流程正常运行后再发布。", "workflow_publish_not_testrun_title": "发布前未试运行", "workflow_query_condition_title": "查询条件", "workflow_query_fields_empty": "请添加查询字段", "workflow_query_fields_name": "字段名", "workflow_query_fields_remove_duplicates": "去重", "workflow_query_fields_title": "查询字段", "workflow_query_limit_title": "查询上限", "workflow_ques_ans_testrun_botname": "机器人", "workflow_ques_ans_testrun_dulpicate": "选项内容不可重复", "workflow_ques_ans_testrun_message_placeholder": "发送一条消息", "workflow_ques_ans_testrun_username": "用户", "workflow_ques_ans_type": "请选择回答类型", "workflow_ques_ans_type_direct": "直接回答", "workflow_ques_ans_type_direct_checkbox": "从回复中提取字段", "workflow_ques_ans_type_direct_checkbox_tooltips": "开启后将从用户输入中提取信息", "workflow_ques_ans_type_direct_context_setting_tooltips": "允许用户回答该问题的最多次数，当从用户的多次回答中获取不到必填的关键字段时，该工作流将会终止运行。\n", "workflow_ques_ans_type_direct_exrtact_context_setting": "最多回答次数设置", "workflow_ques_ans_type_direct_exrtact_title": "设置需要从用户的回答中提取的关键字段", "workflow_ques_ans_type_direct_key_decr": "用户本轮对话输入内容", "workflow_ques_ans_type_option": "选项回答", "workflow_ques_ans_type_option_content": "内容", "workflow_ques_ans_type_option_label": "设置选项内容", "workflow_ques_ans_type_option_other": "其他", "workflow_ques_ans_type_option_other_placeholder": "此选项对用户不可见，当用户回复无关内容时，走此分支", "workflow_ques_ans_type_option_title": "选项", "workflow_ques_content": "提问内容", "workflow_ques_content_placeholder": "可以使用{{变量名}}的方式引用输入参数中的变量", "workflow_ques_content_tooltips": "用于对用户发出提问的具体内容描述", "workflow_ques_input_tooltips": "输入需要添加到问题的参数,这些参数可以被下方的问题引用", "workflow_ques_option_notempty": "选项内容不可为空", "workflow_ques_output_tooltips": "回答结果将作为变量被其他节点引用。", "workflow_question_ dynamic_content": "动态内容", "workflow_question_add_option": "新增选项", "workflow_question_az": "A~Z", "workflow_question_dynamic": "dynamicOption", "workflow_question_fixed_content": "固定内容", "workflow_question_sp": "系统提示词", "workflow_question_sp_placeholder": "支持额外的系统提示词,如设置人设和回复逻辑,使其追问语气更加自然", "workflow_question_sp_setting": "系统提示词设置", "workflow_refer_var_type_same": "建议与引用变量类型（{type}）保持一致，否则可能会转换失败。", "workflow_remove_to_workflow": "从工作流移除", "workflow_role_config_avatar": "角色头像", "workflow_role_config_default_input_tooltip": "不同默认输入方式对应的输入区样式", "workflow_role_config_description_placeheader": "描述一下这个角色", "workflow_role_config_input_title": "用户输入方式", "workflow_role_config_name_tooltip": "每个Chatflow代表一个不同的角色，是在对话中显示的昵称", "workflow_role_config_onboarding": "这里可以随时对角色进行配置", "workflow_role_config_text_2_voice": "文本转语音", "workflow_role_config_title": "角色配置", "workflow_role_config_title_tooltip": "在UI Builder中可以动态配置角色信息，调用时优先采用Builder的配置", "workflow_role_config_voices_title": "Agent 声音", "workflow_running_results": "运行结果", "workflow_running_results_banner": "查看试运行结果", "workflow_running_results_error_code": "代码不能为空", "workflow_running_results_error_executeid": "执行ID", "workflow_running_results_error_node": "节点异常", "workflow_running_results_error_sys": "系统异常", "workflow_running_results_line_error": "并发分支的出线不允许进入另一个并发分支", "workflow_running_results_noresult_content": "点击“试运行”按钮，运行结果将会在这里显示", "workflow_running_results_noresult_title": "暂无运行结果", "workflow_running_results_run_failed": "运行失败", "workflow_saved_database": "调试数据", "workflow_select_and_set_fields_empty": "请先添加字段", "workflow_select_and_set_fields_title": "选择并设置字段", "workflow_select_voice_library": "资源库音色", "workflow_select_voice_official": "预设音色", "workflow_setting_fields": "配置字段", "workflow_start_no_parameter": "未定义输入参数", "workflow_start_trigger_cron_ai": "使用AI生成", "workflow_start_trigger_cron_ai_sample": "示例：0 0 18 * * * 表示每天 18 点执行。", "workflow_start_trigger_cron_cancel": "取消", "workflow_start_trigger_cron_fillin": "填入", "workflow_start_trigger_cron_gen": "AI生成", "workflow_start_trigger_cron_gen_stop": "停止", "workflow_start_trigger_cron_generated": "生成的 Cron 表达式", "workflow_start_trigger_cron_job": "使用Cron表达式", "workflow_start_trigger_cron_option": "选择预设时间", "workflow_start_trigger_setting": "触发器设置", "workflow_start_trigger_setting_tooltips": "选择工作流的启动方式，可以通过预设时间或自定义时间来触发工作流的启动。", "workflow_start_trigger_triggername": "触发器", "workflow_stringprocess_concat_array_desc": "使用以下符号来自动连接数组中的每个项目", "workflow_stringprocess_concat_array_symbol_title": "连接符", "workflow_stringprocess_concat_array_title": "数组连接符设置", "workflow_stringprocess_concat_symbol_comma": "逗号", "workflow_stringprocess_concat_symbol_custom": "自定义", "workflow_stringprocess_concat_symbol_lineBreak": "换行", "workflow_stringprocess_concat_symbol_period": "句号", "workflow_stringprocess_concat_symbol_semicolon": "分号", "workflow_stringprocess_concat_symbol_space": "空格", "workflow_stringprocess_concat_symbol_tab": "制表符", "workflow_stringprocess_concat_tips": "可以使用{{变量名}}、{{变量名.子变量名}}、{{变量名[数组索引]}的方式引用输入参数中的变量", "workflow_stringprocess_delimiter_option": "选择分隔符", "workflow_stringprocess_delimiter_title": "分隔符", "workflow_stringprocess_delimiter_tooltips": "分隔文本的符号（注意区分全半角符号），分隔后将自动变成字符串数组。", "workflow_stringprocess_dulpicate_hover": "存在重复的符号", "workflow_stringprocess_input_tooltips": "请输入字符串类型参数，这些参数可以被用于处理特定格式", "workflow_stringprocess_max_length_item": "最多添加 {maxLength} 个自定义符号", "workflow_stringprocess_node_method": "选择应用", "workflow_stringprocess_node_method_concat": "字符串拼接", "workflow_stringprocess_node_method_sep": "字符串分隔", "workflow_stringprocess_output_tooltips": "处理后的字符串输出结果", "workflow_subcanvas_never_remind": "不再提示", "workflow_subcanvas_pull_out": "按住 {ctrl} 同时拖拽节点即可拖出", "workflow_subcanvas_remove": "移出子画布", "workflow_submit_not_testrun_content": "提交前未进行试运行,建议确保工作流程正常运行后再提交。", "workflow_submit_not_testrun_insist": "坚持提交", "workflow_submit_not_testrun_title": "提交前未试运行", "workflow_subwf_jump_detail": "工作流详情", "workflow_tab_title": "{name} - 工作流 -智能体平台", "workflow_test_nodeerror": "检测到验证失败的节点，请重新配置再创建", "workflow_testrun_check list_batchbody_end_unconnect": "批处理体的结束端口未连线", "workflow_testrun_check list_batchbody_start_unconnect": "批处理体的开始端口未连线", "workflow_testrun_check list_loopbody_end_unconnect": "循环体的结束端口未连线", "workflow_testrun_check list_loopbody_start_unconnect": "循环体的开始端口未连线", "workflow_testrun_form_vailate_error_toast": "无法运行，因为必填项未填写或者有内容报错", "workflow_testrun_hangup_answer": "回复以下问题后，继续试运行", "workflow_testrun_hangup_input": "完成以下输入后，继续试运行", "workflow_testrun_input_form_empty": "本次试运行无需输入", "workflow_testrun_one_node_cancel_run_tooltips": "取消运行", "workflow_testset_aifail": "AI生成失败，请重试", "workflow_testset_aigenerate": "AI生成", "workflow_testset_available": "可用测试集", "workflow_testset_create": "手动创建", "workflow_testset_create_btn": "新建", "workflow_testset_create_tip": "点击按钮去新建测试集", "workflow_testset_create_title": "新建测试集", "workflow_testset_creation_time": "创建于 {xxx}", "workflow_testset_delete_cancel": "取消", "workflow_testset_delete_confirm": "确认", "workflow_testset_delete_tip": "此次删除后不可恢复", "workflow_testset_delete_title": "删除测试集", "workflow_testset_desc": "测试集描述", "workflow_testset_desc_placeholder": "请输入描述信息", "workflow_testset_edit_confirm": "保存", "workflow_testset_edit_title": "编辑测试集", "workflow_testset_edited": "修改于", "workflow_testset_empty": "还未创建测试集", "workflow_testset_generating": "生成中", "workflow_testset_hover_tips": "曾经创建过的测试集，选择后可快速填充测试入参", "workflow_testset_invaild_tip": "{testset_name} 不可使用", "workflow_testset_invalid_tip": "工作流已更新，请检查「{testsetName}」是否正常可用", "workflow_testset_name": "测试集名称", "workflow_testset_name_duplicated": "测试集名称重复", "workflow_testset_name_placeholder": "请输入名称", "workflow_testset_node_data": "节点数据", "workflow_testset_paramempty": "工作流缺少入参，无需创建测试集", "workflow_testset_peedit": "工作流缺少入参，无法编辑", "workflow_testset_please_select": "请选择", "workflow_testset_required_tip": "{param_name}不能为空", "workflow_testset_save": "将本次运行输入保存为测试集或", "workflow_testset_search_empty": "未找到结果", "workflow_testset_start_node": "开始节点", "workflow_testset_stopgen": "停止生成", "workflow_testset_submit_tooltip_for_expert_mode": "保存后将直接发布，对所有协作者可见", "workflow_testset_testruning": "试运行进行中...", "workflow_testset_tilte": "测试集", "workflow_testset_upload_clean": "清空所有文件", "workflow_testset_upload_content": "单个文件大小不得超过 {xx} MB", "workflow_testset_upload_release": "松手开始上传", "workflow_testset_upload_title": "拖拽文件上传 或 点击上传", "workflow_testset_upload_uploaded": "已上传文件", "workflow_testset_vardatabase_node": "关联智能体", "workflow_testset_vardatabase_placeholder": "请选择智能体", "workflow_testset_vardatabase_tip": "选择你需要的智能体", "workflow_testset_view_log": "查看日志", "workflow_text_copy": "文本已复制到剪贴板", "workflow_textprocess_concat_symbol_tips": "预置字符默认为转义字符，自定义字符输入默认为非转义字符", "workflow_textprocess_custom_shading": "请输入连接符", "workflow_toolbar_add_node": "添加节点", "workflow_toolbar_comment_tooltips": "注释", "workflow_toolbar_minimap_tooltips": "缩略图", "workflow_toolbar_zoom_fit": "自适应", "workflow_toolbar_zoom_in": "放大", "workflow_toolbar_zoom_out": "缩小", "workflow_toolbar_zoom_to": "缩放到", "workflow_trigger_bindwf_lib_error": "资源库工作流暂不支持，请先复制到项目中使用。", "workflow_trigger_creat_name_placeholder": "请输入参数值", "workflow_trigger_cron_gen_prompt_placeholder": "示例：每天18点", "workflow_trigger_cron_gen_sample_placeholder": "您可以在提示词中用自然语言如“每天18点”，将会生成对应的Cron表达式 0 0 18 * * * 表示每天 18 点执行。", "workflow_trigger_param_unvalid_cron": "参数为非法Cron表达式", "workflow_trigger_user_create_action": "操作", "workflow_trigger_user_create_advice": "您可以使用触发器节点进行创建", "workflow_trigger_user_create_bind": "绑定工作流", "workflow_trigger_user_create_bind_tooltips": "选择一个工作流，当触发器被激活时，将会执行该工作流。", "workflow_trigger_user_create_close": "关闭", "workflow_trigger_user_create_id": "id", "workflow_trigger_user_create_id_tooltips": "触发器唯一标识：不填写默认新建触发器；填写则为更新，填写 id 须在触发器列表中已存在。", "workflow_trigger_user_create_list": "触发器列表", "workflow_trigger_user_create_list_read": "查看", "workflow_trigger_user_create_name": "名称", "workflow_trigger_user_create_name_tooltips": "触发器名称，可用于标识用途。", "workflow_trigger_user_create_nodata": "暂时没有数据", "workflow_trigger_user_create_refresh": "刷新", "workflow_trigger_user_create_schedula": "触发时间", "workflow_trigger_user_create_time": "创建时间", "workflow_trigger_user_create_userid": "用户id", "workflow_trigger_user_create_userid_tooltips": "用于设置触发器所属用户，可以使用变量-系统变量中的sys_uuid来唯一标识用户", "workflow_unpublish_change": "有尚未发布的修改", "workflow_update_condition_title": "更新条件", "workflow_update_fields": "更新字段", "workflow_user_trigger_banner": "完成设置后需进行发布操作，定时任务才能生效。", "workflow_user_trigger_list_descr": "查看和管理已创建触发器任务", "workflow_var_merge_ strategy_returnnotnull": "返回每个分组中第一个非空的值", "workflow_var_merge_ strategy_tooltips": "对同一组内的变量实施相应聚合策略", "workflow_var_merge_addGroup": "新增分组", "workflow_var_merge_delete_limit": "至少保留一个分组", "workflow_var_merge_group_tooltips": "分组的变量类型默认以第一个变量的类型为主", "workflow_var_merge_name_lengthmax": "名称长度不能超过20个字符", "workflow_var_merge_number_max": "分组最大数量不能超过50个", "workflow_var_merge_output_namedul": "名称重复", "workflow_var_merge_output_tooltips": "分组聚合后返回的结果", "workflow_var_merge_strategy": "聚合策略", "workflow_var_merge_strategy_hovertips": "目前仅支持一种聚合模式", "workflow_var_merge_var_err_noempty": "请选择至少一个变量", "workflow_var_merge_var_err_sametype": "变量类型需要和分组变量类型保持一致", "workflow_var_merge_var_number_max": "变量最大数量不能超过50个", "workflow_var_merge_var_placeholder": "请选择变量", "workflow_var_type_same": "该变量要求类型为{type}，建议类型一致，否则可能会转换失败。", "workflow_variable_ref_placeholder": "引用参数值", "workflow_variable_refer_no_input": "输入参数中无变量", "workflow_variable_refer_no_sub_variable": "该变量无子变量", "workflow_variable_select_voice": "选择音色", "workflow_variable_undefined": "未定义", "workflow_version_add_model_content": "当前工作流中存在 {oldVersion} 版本资源，该资源已有新版本 {newVersion}，添加资源后当前工作流中所有使用该资源的节点均会升级", "workflow_version_number_error1": "版本号格式错误", "workflow_version_number_error2": "不可小于等于当前版本号", "workflow_version_origin_text": "资源库", "workflow_version_origin_tooltips": "从资源库引用", "workflow_version_update_model_content": "当前版本为 {myVersion}，该资源已有新版本 {latestVersion}，更新到新版本后当前工作流中所有使用该资源的节点均会升级。", "workflow_version_update_model_title": "升级到新版本？", "workflow_version_update_placeholder": "请描述本次版本更新内容", "workflow_version_update_tag_tooltips": "有新版本待更新", "workflow_view_data": "查看数据", "workflowstore_card_duplicate": "复制", "workflowstore_category1": "推荐", "workflowstore_continue_editing": "继续编辑", "workflowstore_delete_permission": "此工作流已经在商店上架，请联系工作流创建者下架并删除。", "workflowstore_duplicate_and_add": "复制并添加", "workflowstore_remove_wf": "下架工作流", "workflowstore_submit": "提交上架", "workflowstore_submit_update": "提交更新", "workflowstore_the_workflow_has_been": "此工作流已经在商店上架，请下架并重试", "workflowstore_unable_to_delete_workflow": "无法删除工作流", "workflowstore_workflow_copy_successful": "已创建工作流副本", "worklfow_condition_add_condition": "新增", "worklfow_condition_condition_branch": "条件分支", "worklfow_condition_else_if": "否则如果", "worklfow_condition_if": "如果", "worklfow_condition_priority": "优先级", "worklfow_start_basic_setting": "基础设置", "worklfow_trigger_bind_delete": "绑定的工作流已失效", "worklfow_without_run": "工作流未运行", "workspace_create": "创建", "workspace_develop": "项目开发", "workspace_develop_search_project": "搜索项目", "workspace_library_search": "搜索资源", "workspace_no_permission_access": "无法查看空间", "web_sdk_add_new_conversation": "创建新会话", "store_start_new_chat": "新增对话", "sendFailed": "发送失败", "chat_voice_input_need_focus": "点击当前对话区激活输入框", "web_sdk_create_conversation": "已创建新会话", "web_sdk_conversation_history": "会话历史", "web_sdk_conversation_default_name": "新创建的会话", "web_sdk_delete": "删除", "web_sdk_delete_conversation": "删除会话", "web_sdk_rename_conversation": "重命名会话", "web_sdk_confirm": "确定", "web_sdk_cancel": "取消", "web_sdk_open_conversations": "展开侧栏", "unbind_notification": "提示：智能体已经被解绑", "profile_history_today": "今天", "log_pay_wall_date_filter_30_days": "过去30天", "web_sdk_past": "过往", "404_title": "抱歉，该页面不存在", "404_content": "请检查链接或重试", "overview_bi_assistant_system_error": "系统错误，请稍后再试", "web_sdk_retry_notification": "请稍后重试。", "chatInputPlaceholder": "发送消息", "web_sdk_official_banner": "由 {docs_link} 提供支持，AI生成仅供参考", "web_sdk_official_banner_link": "扣子"}