/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';
import * as base from './base';

export type Int64 = string | number;

export enum AttributeValueType {
  Unknown = 0,
  String = 1,
  Boolean = 2,
  StringList = 11,
  BooleanList = 12,
}

/** coze专业版复制knowledge */
export enum CopyTaskStatus {
  /** 创建 */
  Create = 1,
  /** 执行中 */
  InProgress = 2,
  /** 成功 */
  Success = 3,
  /** 失败 */
  Fail = 4,
}

export enum DatasetScopeType {
  ScopeAll = 1,
  ScopeSelf = 2,
}

export enum DatasetSource {
  SourceSelf = 1,
  SourceExplore = 2,
}

export enum DatasetStatus {
  DatasetProcessing = 0,
  DatasetReady = 1,
  /** 软删 */
  DatasetDeleted = 2,
  /** 不启用 */
  DatasetForbid = 3,
  DatasetFailed = 9,
}

export enum DatasetType {
  Coze = 0,
  Volcano = 1,
}

export enum EntityType {
  Project = 1,
  Bot = 2,
}

export enum EventType {
  Create = 1,
  Update = 2,
  Delete = 3,
}

export enum InstanceStatus {
  /** 创建中, 理论上不会返回该状态 */
  InstanceStatusCreating = 0,
  /** 运行中 */
  Running = 1,
  /** 创建失败, 理论上不会返回该状态 */
  InstanceStatusFailed = 2,
  /** 退订回收 */
  UnsubsRecycled = 3,
  /** 到期关停 */
  ExpiredClosed = 4,
  /** 到期回收 */
  ExpiredRecycled = 5,
  /** 欠费关停 */
  InstanceStatusOverdueShutdown = 6,
  /** 欠费回收 */
  InstanceStatusOverdueRecycled = 7,
  /** 退订关停 */
  InstanceStatusTerminatedShutdown = 8,
}

export enum ResourceType {
  Account = 1,
  Workspace = 2,
  App = 3,
  Bot = 4,
  Plugin = 5,
  Workflow = 6,
  Knowledge = 7,
  PersonalAccessToken = 8,
  Connector = 9,
  Card = 10,
  CardTemplate = 11,
  Conversation = 12,
  File = 13,
  ServicePrincipal = 14,
  Enterprise = 15,
  MigrateTask = 16,
  Prompt = 17,
  UI = 18,
  Project = 19,
}

export enum UserLevel {
  /** 免费版。 */
  Free = 0,
  /** 海外
PremiumLite */
  PremiumLite = 10,
  /** Premium */
  Premium = 15,
  PremiumPlus = 20,
  /** 国内
V1火山专业版 */
  V1ProInstance = 100,
  /** 个人旗舰版 */
  ProPersonal = 110,
  /** 团队版 */
  Team = 120,
  /** 企业版 */
  Enterprise = 130,
}

export interface AttributeValue {
  Type: AttributeValueType;
  Value: string;
}

export interface CopyDatasetList {
  origin_dataset_id: Int64;
  target_dataset_id: Int64;
}

export interface CreateDatasetRequest {
  /** 知识库名称，长度不超过100个字符 */
  name?: string;
  description?: string;
  space_id?: string;
  icon_uri?: string;
  format_type?: common.FormatType;
  /** 开放给第三方的业务标识, coze 传 0 或者不传 */
  biz_id?: string;
  /** 新增project ID */
  project_id?: string;
  /** 存储位置，0: byterag，1: opensearch，2: douyin */
  storage_location?: common.StorageLocation;
  Base?: base.Base;
}

export interface CreateDatasetResponse {
  dataset_id?: string;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface Dataset {
  dataset_id?: string;
  /** 数据集名称 */
  name?: string;
  /** 文件列表 */
  file_list?: Array<string>;
  /** 所有文件大小 */
  all_file_size?: Int64;
  /** 使用Bot数 */
  bot_used_count?: number;
  status?: DatasetStatus;
  /** 处理中的文件名称列表，兼容老逻辑 */
  processing_file_list?: Array<string>;
  /** 更新时间，秒级时间戳 */
  update_time?: number;
  icon_url?: string;
  description?: string;
  icon_uri?: string;
  /** 是否可以编辑 */
  can_edit?: boolean;
  /** 创建时间，秒级时间戳 */
  create_time?: number;
  /** 创建者ID */
  creator_id?: string;
  /** 空间ID */
  space_id?: string;
  creator_name?: string;
  avatar_url?: string;
  /** 处理失败的文件 */
  failed_file_list?: Array<string>;
  format_type?: common.FormatType;
  /** 分段数量 */
  slice_count?: number;
  /** 命中次数 */
  hit_count?: number;
  /** 文档数量 */
  doc_count?: number;
  /** 切片规则 */
  chunk_strategy?: common.ChunkStrategy;
  /** 处理中的文件ID列表 */
  processing_file_id_list?: Array<string>;
  /** 新增project ID */
  project_id?: string;
  /** 存储位置，0: byterag，1: opensearch，2: douyin，目前只有dataset detail接口会返回 */
  storage_location?: common.StorageLocation;
  /** 抖音知识库id */
  dy_knowledge_id?: string;
  /** 存储配置id */
  storage_config_id?: string;
  dy_bot_id?: string;
  /** 0=coze知识库 1=火山知识库 */
  dataset_type?: DatasetType;
  /** storage_config详细信息 */
  storage_config?: StorageConfig;
}

export interface DatasetDetailRequest {
  dataset_ids?: Array<string>;
  space_id?: string;
  /** 新增project ID */
  project_id?: string;
  Base?: base.Base;
}

export interface DatasetDetailResponse {
  dataset_details?: Record<Int64, Dataset>;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface DatasetFilter {
  /** 如果都设置了，And 关系
关键字搜索, 按照名称模糊匹配 */
  name?: string;
  /** deprecated */
  dataset_ids?: Array<string>;
  /** 来源 */
  source_type?: DatasetSource;
  /** 搜索类型 */
  scope_type?: DatasetScopeType;
  /** 类型 */
  format_type?: common.FormatType;
  /** 按coze/火山筛选 */
  dataset_type_list?: Array<DatasetType>;
}

export interface DeleteDatasetRequest {
  dataset_id?: string;
  Base?: base.Base;
}

export interface DeleteDatasetResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetDatasetRefBotsRequest {
  dataset_id?: string;
  Base?: base.Base;
}

export interface GetDatasetRefBotsResponse {
  dataset_id?: string;
  ref_bots?: Array<RefBots>;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetIconRequest {
  format_type?: common.FormatType;
}

export interface GetIconResponse {
  icon?: Icon;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetTreeChunkRecRequest {
  dataset_id?: string;
  tos_uris?: Array<string>;
  Base?: base.Base;
}

export interface GetTreeChunkRecResponse {
  tree_chunk_rec_infos?: Array<TreeChunkRecInfo>;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface Icon {
  url?: string;
  uri?: string;
}

export interface KnowledgeBenefitCheckRequest {
  UserID?: string;
  SpaceID?: string;
  CozeAccountType?: string;
  Base?: base.Base;
}

export interface KnowledgeBenefitCheckResponse {
  /** 用户的订阅等级 */
  UserLevel: UserLevel;
  /** 用户的订阅状态 */
  InstanceStatus: InstanceStatus;
  /** 当前用户的使用上限 */
  UpperBound: number;
  /** 当前用户已使用量 */
  Used: number;
  BaseResp: base.BaseResp;
}

export interface ListDatasetRequest {
  filter?: DatasetFilter;
  page?: number;
  size?: number;
  space_id?: string;
  /** 排序字段 */
  order_field?: common.OrderField;
  /** 排序规则 */
  order_type?: common.OrderType;
  /** 如果传了指定值, 就放开校验 */
  space_auth?: string;
  /** 开放给第三方的业务标识 */
  biz_id?: string;
  /** 是否需要拉取引用bots的数量，会增加响应延时 */
  need_ref_bots?: boolean;
  /** 新增project ID */
  project_id?: string;
  /** 存储位置，0: byterag，1: opensearch，2: douyin */
  storage_location?: common.StorageLocation;
  Base?: base.Base;
}

export interface ListDatasetResponse {
  dataset_list?: Array<Dataset>;
  total?: number;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface RefBots {
  name?: string;
}

export interface ResourceIdentifier {
  /** 资源类型 */
  Type: ResourceType;
  /** 资源Id */
  Id: string;
}

export interface ScriptData {
  DatasetList?: Array<Int64>;
}

export interface SpaceItem {
  UserId?: Int64;
  SpaceId?: Int64;
}

export interface StorageConfig {
  volcano_dataset_config?: common.VolcanoDataset;
}

export interface TreeChunkRecInfo {
  is_rec_tree_chunk?: boolean;
  tos_uri?: string;
}

export interface UpdateDatasetRequest {
  dataset_id?: string;
  name?: string;
  icon_uri?: string;
  description?: string;
  status?: DatasetStatus;
  Base?: base.Base;
}

export interface UpdateDatasetResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
