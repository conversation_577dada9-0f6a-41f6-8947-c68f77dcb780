/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ByteTreeNodeResourceProvider {
  TCE = 1,
  TCC = 2,
  Bernard = 3,
}

export enum DataImportMode {
  Undefined = 0,
  /** 追加 */
  Append = 1,
  /** 覆盖 */
  Overwrite = 2,
}

export enum DataSourceType {
  Undefined = 0,
  TOS = 1,
}

export enum Family {
  Undefined = 0,
  GPT = 1,
  Seed = 2,
  <PERSON> = 3,
  <PERSON> = 4,
  <PERSON> = 5,
  <PERSON>chuan = 6,
  <PERSON>wen = 7,
  <PERSON><PERSON> = 8,
  SkyLark = 9,
  <PERSON>shot = 10,
  Minimax = 11,
  Doubao = 12,
  Baichuan2 = 13,
  DeepSeekV2 = 14,
  DeepSeekCoderV2 = 15,
  DeepseekCoder = 16,
  InternLM2_5 = 17,
  Qwen2 = 18,
  Qwen2_5 = 19,
  Qwen2_5_Coder = 20,
  MiniCPM = 21,
  MiniCPM3 = 22,
  ChatGLM3 = 23,
  Mistral = 24,
  Gemma = 25,
  Gemma2 = 26,
  InternVL2 = 27,
  InternVL2_5 = 28,
  DeepSeekV3 = 29,
  DeepSeekR1 = 30,
}

export enum IDC {
  HL = 1,
  LF = 2,
  LQ = 3,
  YG = 4,
  GL = 5,
  MALIVA = 101,
  SG1 = 201,
  MY = 202,
  MY2 = 203,
  MYCISB = 204,
}

export enum MerlinFramework {
  LLMServerPublic = 1,
  Laplace = 2,
  /** 电商团队专用协议，详见  */
  Mixinfer = 3,
}

export enum MerlinLLMInterface {
  MatxInference = 1,
}

export enum MerlinQuotaPoolType {
  /** 稳定资源 */
  Default = 0,
  /** 潮汐资源 */
  Hybrid = 1,
  /** 常混资源 */
  HybridShare = 2,
  /** 第三方资源ali */
  ALI = 3,
  /** 第三方资源hw */
  HW = 4,
  /** hw arm */
  HWARM = 5,
  /** 弹性售卖资源，随时可能被抢占 */
  Spot = 6,
  /** 可抢占的稳定资源 */
  Preemptible = 20,
}

export enum ModelFilterKey {
  ModelType = 1,
  ModelUserRight = 2,
  ModelFeature = 3,
  ModelFunction = 4,
  ModelScenario = 5,
  Custom = 20,
}

export enum ModelParamType {
  Unknown = 0,
  Float = 1,
  Int = 2,
  Boolean = 3,
  String = 4,
}

export enum ModelStatus {
  Undefined = 0,
  /** 健康可用 */
  Available = 1,
  /** 部署中 */
  Deploying = 2,
  /** 不可用（已下线） */
  Unavailable = 3,
  /** 下线中 */
  Offlining = 4,
}

export enum Provider {
  /** GPT OpenAPI平台 */
  GPTOpenAPI = 1,
  /** 火山方舟 */
  Maas = 2,
  /** 暂时特指seed从bot_engine接入 */
  BotEngine = 3,
  /** merlin平台 */
  Merlin = 4,
  /** merlin-seed平台 */
  MerlinSeed = 5,
}

export enum ProviderAccountType {
  AKSK = 1,
  APIKey = 2,
}

export enum Region {
  CN = 1,
  SG = 2,
  US = 3,
}

export enum RuntimeCustomParamType {
  Unknown = 0,
  StringList = 1,
  IntList = 2,
  FloatList = 3,
  String = 4,
  Int = 5,
  Float = 6,
  Bool = 7,
}

export enum RuntimeHookType {
  Unknown = 0,
  MsgPreHook = 1,
}

export enum SftTaskBaseModelType {
  Foundation = 0,
  Custom = 1,
}

export enum SftTaskErrCode {
  Default = 0,
  /** 前2位代表错误分类，后4位代表具体错误
10 通用错误 */
  InternalErr = 100000,
  /** 训练集上传到平台方时出错 */
  TrainingSetUploadedErr = 100001,
  /** 在平台方创建精调任务出错 */
  CreateProviderSftTaskErr = 100002,
  /** 20 Merlin任务实例整体报错 */
  MerlinTaskInternalError = 200000,
  /** gpu cpu mem 套餐配比错误 */
  MerlinGpuCpuMemRatioBad = 200001,
  /** 21 Merlin训练脚本主动上报的错误 */
  MerlinReportedInternalErr = 210000,
  MerlinOOM = 210001,
  /** 没找到模型训练产物 */
  MerlinNoModelGenerated = 210002,
  /** 30 火山方舟任务失败
方舟通用错误 */
  MaaSInternalErr = 300000,
  /** 验证集比例分割异常 */
  MaaSValidationSetSplitErr = 300001,
  /** 训练数据格式不符合预期 */
  MaaSTrainingSetNoValid = 300002,
  /** 业务方的火山账号没有开通模型服务 */
  MaaSAccountModelServiceIsNotActivated = 300003,
  /** 缺少必要的请求参数。请确认请求参数后重试。 */
  MaaSRequiredParamMissing = 300004,
  /** 请求参数值不合法。请检查参数值的正确性后重试。 */
  MaaSRequestParamInvalid = 300005,
  /** 对象的标签存在重复Key */
  MaaSDuplicateTags = 300006,
  /** 无法同时上传验证集和设置训练集取样为验证集百分比，不支持该操作 */
  MaaSNotSupportedToConfigureValidationSetAndPercentage = 300007,
  /** 您没有权限访问基础模型的配置，不支持该操作 */
  MaaSNotPermittedToAccessModel = 300008,
  /** 模型不支持该训练方法，不支持该操作 */
  MaaSModelNotSupportTheTrainingMethod = 300009,
  /** 基础模型的版本不支持该训练方法，不支持该操作 */
  MaaSFoundationModelNotSupportTheTrainingMethod = 300010,
  /** 您的账单已逾期，不支持该操作。请前往火山费用中心充值 */
  MaaSAccountBalanceOverdue = 300011,
  /** 未知错误，请稍后重试。如果多次尝试仍失败，请提交工单 */
  MaaSUnknownErr = 300012,
}

export enum SftTaskHyperParamType {
  Int = 1,
  Float = 2,
  String = 3,
  Bool = 4,
  Percentage = 5,
}

export enum SftTaskOutputExportType {
  /** 导出到新模型 */
  NewModel = 1,
  /** 导出到已有模型 */
  ExistModel = 2,
}

export enum SftTaskOutputStatus {
  /** 未导出 */
  Available = 1,
  /** 已导出 */
  Exported = 2,
  /** 已过期 */
  Expired = 3,
  /** 导出中 */
  Exporting = 4,
  /** 导出失败 */
  ExportFailed = 5,
}

export enum SftTaskResourceMerlinVersion {
  RemoveAnyGPU = 1,
}

export enum SftTaskRunEventType {
  Undefined = 0,
  ErrorOccured = 1,
  UploadCkpt = 2,
  ReportMetricsURL = 3,
  ReportProgress = 5,
  ReportTrainingStart = 6,
  ReportTrainingFinished = 7,
}

export enum SftTaskStatusPhase {
  Preprocessing = 0,
  Queued = 1,
  Deploying = 2,
  Running = 3,
  Completing = 4,
  Completed = 5,
  Terminating = 6,
  Terminated = 7,
  Failed = 8,
}

export enum SftTaskTrainingMethod {
  LoRA = 1,
  Full = 2,
}

export enum SftTaskTrainingType {
  SftFineTuning = 1,
}

export enum SftTaskValidationSetType {
  SplitFromTrainingSet = 1,
}

export enum TaskStatus {
  Undefined = 0,
  /** 正在初始化 */
  Initializing = 1,
  /** 正在运行 */
  Running = 2,
  /** 成功完成 */
  Done = 3,
  /** 失败 */
  Failed = 4,
  /** 手动终止 */
  Terminated = 5,
  /** 成功完成，但有错误 */
  DoneWithError = 6,
}

export enum TrainingDataFileType {
  Undefined = 0,
  JSONL = 1,
}

export enum TrainingDatasetType {
  Undefined = 0,
  SFTChat = 1,
  SFTFunctionCall = 2,
  SFTMultiModalUnderstanding = 3,
}

export enum TrainingFileOpType {
  Undefined = 0,
  Get = 1,
  Put = 2,
}

export enum UsageScenario {
  /** 默认场景 */
  Default = 1,
  /** 评测场景 */
  Evaluation = 2,
  /** Prompt as a Service调用 */
  PromptAsAService = 3,
  /** AI打标 */
  AIAnnotate = 4,
  /** 质量分 */
  AIScore = 5,
  /** 数据标签 */
  AITag = 6,
}

export enum ValidateStatus {
  Pass = 1,
  Failed = 2,
  Skip = 3,
}

export enum VisibleMode {
  /** 默认（仅模型所属空间可见） */
  Default = 1,
  /** 指定空间可见 */
  Specified = 2,
  /** 所有空间可见 */
  All = 3,
}

export interface Ability {
  /** 最大上下文长度 */
  maxContextTokens?: Int64;
  /** 最大输入长度 */
  maxInputTokens?: Int64;
  /** 最大输出长度 */
  maxOutputTokens?: Int64;
  /** 是否支持函数调用 */
  functionCallEnabled?: boolean;
  /** 是否支持JSON模式 */
  jsonModeEnabled?: boolean;
  /** 是否支持多模态(模型输入) */
  multiModalEnabled?: boolean;
  /** 多模态能力配置(模型输入) */
  multiModalAbility?: MultiModalAbility;
  /** 消息预处理hook */
  messagePreHandleHook?: RuntimeHook;
  /** 模型可用的参数配置，优先级高于原有的maxContextTokens/maxInputTokens/maxOutputTokens */
  modelParams?: Array<ModelParam>;
  /** 是否支持思考能力开关 */
  thinkingSwitchEnabled?: boolean;
  /** 思考能力配置 */
  thinkingAbility?: ThinkingAbility;
  /** 是否支持多模态(模型输出) */
  multiModalOutputEnabled?: boolean;
}

export interface Account {
  /** 账号id */
  id?: Int64;
  /** 是否为公共账号 */
  isPublic?: boolean;
  /** 所属模型id */
  modelID?: Int64;
  /** 所属空间id */
  spaceID?: Int64;
  /** 区域 */
  region?: Region;
  /** 使用场景 */
  usageScenario?: UsageScenario;
  /** 授权认证信息 */
  authorization?: Authorization;
  /** 限流信息 */
  quota?: Quota;
  /** 创建人 */
  createdBy?: string;
  /** 更新人 */
  updatedBy?: string;
  /** 创建时间 */
  createdAt?: Int64;
  /** 更新时间 */
  updatedAt?: Int64;
  /** 编辑版本（用于解决多人同时更新问题） */
  editVersion?: Int64;
  /** 模型平台账号 id, 引用托管在平台的账号时提供， 直接添加 AKSK/Key 时为空。 */
  modelProviderAccountID?: Int64;
}

export interface Authorization {
  gptOpenAPI?: GPTOpenAPIAuth;
  maas?: MaasAuth;
  botEngine?: BotEngineAuth;
}

export interface BotEngineAuth {
  connectorID?: Int64;
}

/** notice: 此处仅包含部分信息 */
export interface ByteTreeNode {
  id?: string;
  name?: string;
  i18nName?: string;
  type?: string;
  path?: string;
  i18nPath?: string;
  levelID?: Int64;
  isLeaf?: boolean;
}

export interface DataSource {
  type?: DataSourceType;
  tosFile?: TOSFile;
}

export interface DeployQuota {
  nodeID?: string;
  nodePath?: string;
  type?: string;
  displayType?: string;
  region?: string;
  zone?: string;
  /** 可用量 */
  available?: Int64;
}

export interface GPTOpenAPIAuth {
  ak?: string;
}

export interface GPTOpenAPIInfo {
  apiBaseURL?: string;
  apiVersion?: string;
}

export interface IDCDeployDetail {
  deployID?: string;
  /** 资源对应服务树节点 id */
  byteNodeID?: string;
  region?: string;
  deviceType?: string;
  deviceDisplayName?: string;
  instance?: string;
}

export interface InstanceResourceOption {
  cpus?: string;
  /** 单位 GB */
  mem?: string;
  gpus?: string;
}

export interface InvalidDataSet {
  datasetID?: string;
  datasetName?: string;
  invalidReason?: string;
}

export interface MaaSAccountValidate {
  akValidate?: ProviderValidateDetail;
  kmsValidate?: ProviderValidateDetail;
  tosValidate?: ProviderValidateDetail;
}

export interface MaasAuth {
  /** 使用多云代理时，ak 可以不传 */
  ak?: string;
  sk?: string;
  /** 2.0控制面鉴权方式 */
  apiKey?: string;
  usedForSFT?: boolean;
  projects?: Array<string>;
  /** 使用多云代理 */
  useMultiCloud?: boolean;
  /** 服务账号名称，使用多云代理时必填 */
  serviceAccountName?: string;
  /** 是否需要迁移至字节云方舟 */
  needMigArk?: boolean;
}

export interface MaasInfo {
  host?: string;
  region?: string;
  /** v3 sdk */
  baseURL?: string;
  /** 精调模型任务的 ID */
  customizationJobsID?: string;
}

export interface MaaSRegionConfig {
  region?: string;
  host?: string;
}

export interface MerlinInfo {
  psm?: string;
  cluster?: string;
  idcs?: Array<IDC>;
  framework?: MerlinFramework;
  instanceResourceOption?: InstanceResourceOption;
  /** 资源配置 */
  deployDetailM?: Partial<Record<IDC, IDCDeployDetail>>;
  /** 服务节点id */
  byteTreeNodeID?: Int64;
  /** 服务树路径 */
  byteTreeNodePath?: string;
  /** 服务详情链接 */
  serviceDetailURL?: string;
  /** LLM 接口信息 */
  llmInterface?: MerlinLLMInterface;
  /** 是否开启 vllm */
  useVLLMServe?: boolean;
  /** 调用 vllm 的 API 路径，仅开启 vllm 时使用 */
  vllmAPIPath?: string;
}

export interface MerlinResourceCluster {
  type?: MerlinQuotaPoolType;
  /** 是否为可占用资源 */
  preemptible?: boolean;
  /** merlin用户组id */
  groupID?: Int64;
  /** merlin用户组名称 */
  groupName?: string;
  /** merlin集群id */
  clusterID?: string;
  /** merlin集群名称 */
  clusterName?: string;
  /** key为可选的gpuv型号，value为该型号剩余的gpu数量 */
  availableGpuvs?: Record<string, Int64>;
  /** 剩余cpu数量 */
  cpuNum?: string;
  /** 剩余内存数量，单位是MB */
  memory?: string;
  /** 机房 */
  dc?: string;
  /** 描述 */
  desc?: string;
  /** key为可选的gpuv型号，value为该型号推荐的套餐 */
  gpuvSets?: Record<string, MerlinResourceGpuSet>;
}

export interface MerlinResourceGpuSet {
  /** gpu个数 */
  gpu?: number;
  /** cpu个数 */
  cpu?: number;
  /** 内存，单位是GB */
  memory?: number;
}

export interface Model {
  /** 模型id, 在saas场景下可为空，如果为空则用identification+provider直接调用模型 */
  id?: Int64;
  /** 模型标识（name，endpoint，与各提供方对齐） */
  identification?: string;
  /** 展示名称 */
  displayName?: string;
  /** 模型描述 */
  description?: string;
  /** 模型家族 */
  family?: Family;
  /** 提供方 */
  provider?: Provider;
  /** 提供方信息 */
  providerInfo?: ProviderInfo;
  /** 是否为公共模型 */
  isPublic?: boolean;
  /** 可见性 */
  visibility?: Visibility;
  /** 所属空间id */
  spaceID?: Int64;
  /** 模型能力 */
  ability?: Ability;
  /** 默认运行时参数 */
  defaultRuntimeParam?: RuntimeParam;
  /** 创建人 */
  createdBy?: string;
  /** 更新人 */
  updatedBy?: string;
  /** 创建时间 */
  createdAt?: Int64;
  /** 更新时间 */
  updatedAt?: Int64;
  /** 编辑版本（用于解决多人同时更新问题） */
  editVersion?: Int64;
  modelStatus?: ModelStatus;
  /** 模型提供方侧的 model id */
  externalModelID?: string;
  /** 从精调模型任务的产出中新建推理点产生的模型 */
  sftTaskID?: Int64;
  /** 模型版本 */
  modelVersion?: string;
  /** 模型头像 */
  modelIcon?: string;
  /** 模型公司 */
  modelVendor?: string;
  /** 模型真实名称（展示在vendor右侧，可能是豆包endpoint或其他厂商的模型名称） */
  actualName?: string;
  /** 模型标签 */
  modelTags?: Array<string>;
}

export interface ModelParam {
  name?: string;
  label?: string;
  desc?: string;
  /** 类型： bool/int/float等 */
  modelParamType?: ModelParamType;
  min?: string;
  max?: string;
  defaultVal?: string;
  /** 枚举 */
  modelParamOptions?: Array<ModelParamOption>;
}

export interface ModelParamOption {
  /** 展示值 */
  label?: string;
  /** 实际值 */
  value?: string;
}

export interface MultiModalAbility {
  /** [1, 99] 图片配置
是否支持图片 */
  imageEnabled?: boolean;
  /** 是否支持二进制图片 */
  binaryImageEnabled?: boolean;
  /** 单张图片大小限制, 范围 [0, 20]MB */
  maxImageSizeInMB?: number;
  /** 最大图片数量, 范围 [-1, 100], -1 表示无限制 */
  maxImageCount?: number;
}

export interface OnlyReadable {
  /** 是否只读态 */
  onlyReadable?: boolean;
  /** 只读态原因 */
  desc?: string;
}

/** 托管的模型平台账号信息 */
export interface ProviderAccount {
  id?: string;
  spaceID?: string;
  provider?: Provider;
  type?: ProviderAccountType;
  name?: string;
  providerRegion?: string;
  maasAccount?: MaasAuth;
  updatedAt?: string;
  createdAt?: string;
  updatedBy?: string;
  createdBy?: string;
}

export interface ProviderAccountRelatedResources {
  sftTaskCount?: string;
  sftTaskIDs?: Array<string>;
  modelCount?: string;
  modelIDs?: Array<string>;
}

export interface ProviderInfo {
  gptOpenAPI?: GPTOpenAPIInfo;
  maas?: MaasInfo;
  merlin?: MerlinInfo;
}

export interface ProviderValidateDetail {
  status?: ValidateStatus;
  invalidatedReason?: string;
  /** 校验不通过时，用户可通过此处的 url 前往指定平台处理 */
  providerURL?: string;
}

export interface Quota {
  qpm?: Int64;
  tpm?: Int64;
}

export interface RuntimeCustomParam {
  name?: string;
  type?: RuntimeCustomParamType;
  value?: string;
}

export interface RuntimeHook {
  type?: RuntimeHookType;
  url?: string;
}

export interface RuntimeParam {
  /** 最大输出长度 */
  maxTokens?: Int64;
  temperature?: number;
  topP?: number;
  topK?: Int64;
  jsonMode?: boolean;
  /** 停止词, 值必须是json序列化后的字符串数组 */
  stopWords?: string;
  /** 运行时自定义参数 */
  runtimeCustomParams?: Array<RuntimeCustomParam>;
  /** 是否默认支持思考能力 */
  thinkingEnabled?: boolean;
  /** 当thinkingEnabled==true时有效，含义为默认thinking max token */
  thinkingBudgetTokens?: Int64;
}

export interface SftTask {
  id?: string;
  name?: string;
  spaceID?: string;
  provider?: Provider;
  accountID?: string;
  accountName?: string;
  createdBy?: string;
  updatedBy?: string;
  createTimeInMS?: string;
  updateTimeInMS?: string;
  trainingType?: SftTaskTrainingType;
  trainingMethod?: SftTaskTrainingMethod;
  /** 选择的模型 */
  sftBasedModel?: SftTaskBaseModel;
  /** 设置的超参 */
  hyperParams?: Array<SftTaskHyperParam>;
  /** 选择的数据集 */
  dataset?: SftTaskDataset;
  /** 输出模型数量上限 */
  modelOutputLimit?: number;
  /** 任务完成时输出的模型 */
  outputs?: Array<SftTaskOutput>;
  /** 任务的状态 */
  status?: SftTaskStatus;
  /** 各种外部平台的链接 */
  providerURLInfo?: SftTaskProviderURLInfo;
  /** 描述 */
  description?: string;
  /** 是否关联了Fornax的模型，如果已关联，不允许删除此任务 */
  isRelatedToFrnModels?: boolean;
  /** 资源配置（目前只适用于merlin） */
  resource?: SftTaskResource;
  /** 训练产物配置（目前只适用于merlin） */
  outputConfig?: SftTaskOutputConfig;
  hyperParamsCategories?: Array<SftTaskHyperParamCategory>;
  /** 是否只读态 */
  onlyReadable?: OnlyReadable;
}

export interface SftTaskBaseModel {
  /** 定制模型 */
  customModel?: SftTaskCustomModel;
  /** 基础模型 */
  foundationModel?: SftTaskFoundationModel;
  /** 模型类型 */
  type?: SftTaskBaseModelType;
}

export interface SftTaskCustomModel {
  id?: string;
  name?: string;
  updateTimeInMS?: string;
  /** 训练此定制模型的任务的超参 */
  hyperParams?: Array<SftTaskHyperParam>;
  /** 此定制模型是通过哪个基础模型训练得到 */
  foundationModel?: SftTaskFoundationModel;
  /** merlin模型仓库版本 */
  version?: string;
  /** merlin模型文件保存的地址 */
  hdfsPath?: string;
}

export interface SftTaskDataset {
  /** 训练集 */
  trainingSet?: SftTaskTrainingSet;
  /** 验证集 */
  validationSet?: SftTaskValidationSet;
}

export interface SftTaskFoundationModel {
  name?: string;
  /** 模型来源（厂商） */
  vendor?: string;
  desc?: string;
  /** 展示名称 */
  displayName?: string;
  version?: string;
  versionUpdateTimeInMS?: string;
  versionDesc?: string;
  versionConfigID?: string;
  /** 系列 */
  family?: Family;
  /** 系列描述 */
  familyName?: string;
  /** 参数量 */
  modelSize?: string;
  /** 上下文长度 */
  contextSize?: string;
  /** 参数量描述 */
  modelFTType?: string;
  /** 模型卡片URL */
  modelCardURL?: string;
  modelTags?: Array<string>;
  ability?: Ability;
  /** 该模型在前端是否不可见（如doubao-embedding模型，因为训练集不支持embedding数据，所以也先不展示这个模型） */
  invisible?: boolean;
}

export interface SftTaskHyperParam {
  name?: string;
  value?: string;
  type?: SftTaskHyperParamType;
  min?: string;
  max?: string;
  defaultValue?: string;
  desc?: string;
  options?: Array<string>;
  /** 选择定制模型进行增量训练时，此参数是否允许锁死不允许用户在创建任务时修改 */
  incrementalLearningLocked?: boolean;
  /** 展示用，如果为空字符串，就用name做兜底 */
  displayName?: string;
  isRequired?: boolean;
}

export interface SftTaskHyperParamCategory {
  name?: string;
  displayName?: string;
  desc?: string;
  isRequired?: boolean;
  defaultValue?: boolean;
  value?: boolean;
  hyperParams?: Array<SftTaskHyperParam>;
  /** 帮助文档链接 */
  helpDocLink?: string;
}

export interface SftTaskOutput {
  index?: number;
  customModelID?: string;
  customModelCreateTimeInMS?: string;
  createTimeInMS?: string;
  name?: string;
  /** 是否已导出 */
  status?: SftTaskOutputStatus;
  /** 在Fornax注册的私有模型信息 */
  registeredModel?: Array<SftTaskRegisteredModel>;
  /** 描述 */
  description?: string;
  /** 过期时间 */
  expireTimeInMS?: string;
  /** 版本号，暂时只支持merlin模型 */
  modelVersion?: string;
  /** 模型路径(暂时只支持merlin模型，为hdfs地址) */
  modelPath?: string;
  /** 此模型(版本)在模型提供方的URL，暂时只支持merlin模型 */
  modelProviderURL?: string;
  /** 状态详情，暂时只有status是导出失败时才会有值 */
  statusDetail?: string;
}

export interface SftTaskOutputConfig {
  /** 导出的模型名称 */
  exportModelName?: string;
  /** 导出方式 */
  exportType?: SftTaskOutputExportType;
}

export interface SftTaskPresetDataset {
  name?: string;
}

export interface SftTaskProgress {
  /** 计划的epoch */
  plannedEpoch?: Int64;
  /** 当前的epoch */
  currentEpoch?: Int64;
  /** 计划的step */
  plannedStep?: Int64;
  /** 当前的step */
  currentStep?: Int64;
  /** 预计剩余时间，秒 */
  eta?: Int64;
  /** 是否训练完成 */
  isTraningFinished?: boolean;
}

export interface SftTaskProviderURLInfo {
  /** 任务详情 */
  TaskDetailURL?: string;
  /** 效果指标 */
  MetricsURL?: string;
  /** 日志 */
  LogURL?: string;
  /** 时间线 */
  TimelineURL?: string;
  /** 模型输出 */
  OutputURL?: string;
  /** 训练时测评 */
  EvaluationURL?: string;
  /** 系统指标 */
  SystemMetricsURL?: string;
}

export interface SftTaskRegisteredModel {
  modelID?: string;
  name?: string;
}

export interface SftTaskResource {
  provider?: Provider;
  merlin?: SftTaskResourceMerlin;
}

export interface SftTaskResourceMerlin {
  type?: MerlinQuotaPoolType;
  /** 用户组id，暂时只支持1个 */
  groupIDs?: Array<string>;
  /** 集群id */
  clusterID?: string;
  /** 是否使用可占用资源 */
  preemptible?: boolean;
  /** 角色配置，暂时只支持1个 */
  roles?: Array<SftTaskResourceMerlinRole>;
  /** key是用户组id，val是用户组名称 */
  groupNames?: Record<Int64, string>;
  /** 集群名称 */
  clusterName?: string;
  /** merlin资源格式的版本，目前唯一作用：由于资源格式不向前兼容，只有指定版本号的资源才能在复制精调任务时被写入到新任务中 */
  version?: SftTaskResourceMerlinVersion;
}

export interface SftTaskResourceMerlinRole {
  /** 实例数，必填1 */
  num?: number;
  /** 虚拟gpu型号 */
  gpuv?: string;
  /** gpu数量 */
  gpu?: number;
  /** cpu数量 */
  cpu?: number;
  /** 内存大小，单位是MB */
  memory?: number;
}

export interface SftTaskStatus {
  /** 状态 */
  phase?: SftTaskStatusPhase;
  /** 到达状态的时间 */
  phaseTimeInMS?: string;
  /** 状态是排队中时的排队位次 */
  queuePosition?: number;
  /** 对该状态的详细说明，如报错信息 */
  detail?: string;
  /** 状态码 */
  code?: number;
}

export interface SftTaskTrainingSet {
  trainingSetID?: string;
  /** 预置数据集 */
  presetDataset?: SftTaskPresetDataset;
  /** 预置数据集占训练集的百分比 */
  presetDatasetPercentage?: number;
  /** 支持精调任务使用多个训练数据集 */
  trainingSetIDs?: Array<string>;
  /** 自定义数据集路径 */
  trainingSetPath?: string;
}

export interface SftTaskValidationSet {
  type?: SftTaskValidationSetType;
  /** 从训练集中分割的百分比 */
  splitPercentage?: number;
}

export interface ThinkingAbility {
  /** 是否默认开启思考能力 */
  switchDefaultEnabled?: boolean;
  /** 思考最大token */
  maxBudgetTokens?: Int64;
  /** 思考最小token，必须大于0 */
  minBudgetTokens?: Int64;
}

export interface TOSFile {
  bucket?: string;
  key?: string;
}

export interface TrainingDataImportTask {
  id?: string;
  datasetID?: string;
  status?: TaskStatus;
  fileType?: TrainingDataFileType;
  dataSource?: DataSource;
  mode?: DataImportMode;
  /** 文件大小，单位：byte */
  totalSize?: number;
  /** 已处理数据大小，单位：byte */
  processedSize?: number;
  /** 已处理的行数 */
  processedLineCount?: number;
  /** 已导入的行数 */
  outputLineCount?: number;
  errLog?: string;
  msg?: string;
  /** Fornax空间ID */
  spaceID?: string;
  /** 创建人ID */
  createdBy?: string;
  /** 创建时间，秒 */
  createdAt?: string;
  /** 更新人ID */
  updatedBy?: string;
  /** 更新时间，秒 */
  updatedAt?: string;
}

export interface Visibility {
  mode?: VisibleMode;
  /** Mode为Specified有效，配置为除模型所属空间外的其他空间 */
  spaceIDs?: Array<Int64>;
}
/* eslint-enable */
