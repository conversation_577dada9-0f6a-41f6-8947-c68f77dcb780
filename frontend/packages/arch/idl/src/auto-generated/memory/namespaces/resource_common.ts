/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ActionKey {
  /** 复制 */
  Copy = 1,
  /** 删除 */
  Delete = 2,
  /** 启用/禁用 */
  EnableSwitch = 3,
  /** 编辑 */
  Edit = 4,
  /** 切换成funcflow */
  SwitchToFuncflow = 8,
  /** 切换成chatflow */
  SwitchToChatflow = 9,
  /** 跨空间复制 */
  CrossSpaceCopy = 10,
}

export enum CopyStatus {
  Successful = 1,
  Processing = 2,
  Failed = 3,
  /** 如果是KeepOrigin，表示该资源不需要做变更，资源方不需要设置Target相关信息；引用方直接忽略即可 */
  KeepOrigin = 4,
  /** 表示回滚到草稿时，资源从有->无的变更 */
  Deleted = 5,
}

export enum ProjectResourceActionKey {
  /** 重命名 */
  Rename = 1,
  /** 创建副本/复制到当前项目 */
  Copy = 2,
  /** 复制到资源库 */
  CopyToLibrary = 3,
  /** 移动到资源库 */
  MoveToLibrary = 4,
  /** 删除 */
  Delete = 5,
  /** 启用 */
  Enable = 6,
  /** 禁用 */
  Disable = 7,
  /** 切换成funcflow */
  SwitchToFuncflow = 8,
  /** 切换成chatflow */
  SwitchToChatflow = 9,
}

export enum ProjectResourceGroupType {
  Workflow = 1,
  Plugin = 2,
  Data = 3,
}

export enum PublishStatus {
  /** 未发布 */
  UnPublished = 1,
  /** 已发布 */
  Published = 2,
}

export enum ResourceCopyScene {
  /** 复制项目内的资源，浅拷贝 */
  CopyProjectResource = 1,
  /** 复制项目资源到Library，复制后要发布 */
  CopyResourceToLibrary = 2,
  /** 移动项目资源到Library，复制后要发布，后置要删除项目资源 */
  MoveResourceToLibrary = 3,
  /** 复制Library资源到项目 */
  CopyResourceFromLibrary = 4,
  /** 复制项目，连带资源要复制。复制当前草稿。 */
  CopyProject = 5,
  /** 项目发布到渠道，连带资源需要发布（含商店）。以当前草稿发布。 */
  PublishProject = 6,
  /** 复制项目模板。 */
  CopyProjectTemplate = 7,
  /** 项目发布到模板，以项目的指定版本发布成临时模板。 */
  PublishProjectTemplate = 8,
  /** 上架模板 */
  LaunchTemplate = 9,
  /** 草稿版本存档 */
  ArchiveProject = 10,
  /** 线上版本加载到草稿，草稿版本加载到草稿 */
  RollbackProject = 11,
  /** 单个资源跨空间复制 */
  CrossSpaceCopy = 12,
  /** 项目跨空间复制 */
  CrossSpaceCopyProject = 13,
}

export enum ResType {
  Plugin = 1,
  Workflow = 2,
  Imageflow = 3,
  Knowledge = 4,
  UI = 5,
  Database = 7,
  Variable = 8,
}

export enum SyncOperation {
  Upsert = 1,
  Delete = 2,
}

export interface ProjectResourceAction {
  /** 一个操作对应一个唯一的key，key由资源侧约束 */
  key: ProjectResourceActionKey;
  /** ture=可以操作该Action，false=置灰 */
  enable: boolean;
  /** enable=false时，提示文案。后端返回Starling Key，注意放在同一个space下。 */
  hint?: string;
}

/** 实现方提供展示信息 */
export interface ProjectResourceInfo {
  /** 资源id */
  res_id?: string;
  /** 资源名称 */
  name?: string;
  /** 不同类型的不同操作按钮，由资源实现方和前端约定。返回则展示，要隐藏某个按钮，则不要返回； */
  actions?: Array<ProjectResourceAction>;
  /** 该用户是否对资源只读
4: bool ReadOnly (go.tag = "json:\"read_only\"", agw.key = "read_only")
资源类型 */
  res_type?: ResType;
  /** 资源子类型，由资源实现方定义。Plugin：1-Http; 2-App; 6-Local；Knowledge：0-text; 1-table; 2-image；UI：1-Card */
  res_sub_type?: number;
  /** 业务携带的扩展信息，以res_type区分，每个res_type定义的schema和含义不一样，使用前需要判断res_type */
  biz_extend?: Record<string, string>;
  /** 资源状态，各类型资源自身定义。前端与各资源方约定。 */
  biz_res_status?: number;
  /** 当前资源的编辑态版本 */
  version_str?: string;
}

export interface RefTreeNode {
  ResID?: Int64;
  ResType?: ResType;
  /** 引用的子资源，子资源在不同层级的引用可以出现多次。A-B, A-C-B, A-D, B可以同时在A和C的ChildrenNodes出现 */
  ChildrenNodes?: Array<RefTreeNode>;
}

export interface ResourceCopyCheckFailedReason {
  ResourceLocator?: ResourceLocator;
  ResName?: string;
  Reason?: string;
}

export interface ResourceCopyEnv {
  Scene?: ResourceCopyScene;
  /** 原项目ID。如果被复制的资源在项目中，则有值。场景：CopyProjectResource、CopyResourceToLibrary、MoveResourceToLibrary、CopyProject */
  OriginProjectID?: Int64;
  /** 目标项目ID。如果复制后的资源要赋值project_id，则有值。场景：CopyProjectResource、CopyResourceFromLibrary、CopyProject */
  TargetProjectID?: Int64;
  /** 被用户选择复制/移动的资源ID。如果操作的目标是资源，则有值。场景：CopyProjectResource、CopyResourceToLibrary、MoveResourceToLibrary、CopyResourceFromLibrary */
  ResourceLocator?: ResourceLocator;
  /** 当次任务的唯一约束，控制幂等。有则传，发起任务时不用传 */
  TaskUniqKey?: string;
  /** 项目发布时，项目版本。场景：PublishProject、PublishProjectTemplate */
  TargetProjectVersion?: Int64;
  OriginProjectSpaceID?: Int64;
  TargetProjectSpaceID?: Int64;
  /** 操作者用户id */
  CurrentUserID?: Int64;
  /** 发布模板时，原项目版本。or 复制模板时，模板的项目版本。 */
  OriginProjectVersion?: Int64;
  /** 0/default-app，默认是app也就是之前的project; 1-bot或者叫agent */
  ProjectType?: number;
}

/** 每个资源的复制结果，包含前后映射信息 */
export interface ResourceCopyResult {
  OriginResourceLocator?: ResourceLocator;
  TargetResourceLocator?: ResourceLocator;
  CopyStatus?: CopyStatus;
  /** 发布版本号或版本名，0.0.1 */
  TargetPublishVersionNum?: string;
  /** 其他的信息，比如plugin的tool映射信息。schema和各资源方约定 */
  TargetResInfo?: string;
}

/** 用于定位一个资源数据的结构：某资源的某版本 */
export interface ResourceLocator {
  ResID?: Int64;
  ResType?: ResType;
  /** 废弃，不要使用 */
  PublishVersion?: Int64;
  /** 资源的当前版本，为nil或空字符串都看作是最新版本。项目发布版本或Library发布版本。 */
  PublishVersionStr?: string;
}
/* eslint-enable */
