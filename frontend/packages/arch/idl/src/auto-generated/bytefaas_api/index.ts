/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as admin from './namespaces/admin';
import * as agent from './namespaces/agent';
import * as alarm from './namespaces/alarm';
import * as async_request from './namespaces/async_request';
import * as build from './namespaces/build';
import * as burst_protector from './namespaces/burst_protector';
import * as cluster from './namespaces/cluster';
import * as code_revision from './namespaces/code_revision';
import * as codeagent_cp from './namespaces/codeagent_cp';
import * as common from './namespaces/common';
import * as consul_trigger from './namespaces/consul_trigger';
import * as debug from './namespaces/debug';
import * as diagnosis from './namespaces/diagnosis';
import * as eventbus from './namespaces/eventbus';
import * as filterplugin from './namespaces/filterplugin';
import * as http_trigger from './namespaces/http_trigger';
import * as image from './namespaces/image';
import * as instance from './namespaces/instance';
import * as mcp_server from './namespaces/mcp_server';
import * as mcp_tool from './namespaces/mcp_tool';
import * as mqevent from './namespaces/mqevent';
import * as other from './namespaces/other';
import * as packages from './namespaces/packages';
import * as pipeline_template from './namespaces/pipeline_template';
import * as plugin_function from './namespaces/plugin_function';
import * as regional_meta from './namespaces/regional_meta';
import * as release from './namespaces/release';
import * as resource from './namespaces/resource';
import * as resource_group from './namespaces/resource_group';
import * as revision from './namespaces/revision';
import * as sandbox from './namespaces/sandbox';
import * as scale_record from './namespaces/scale_record';
import * as scale_setting from './namespaces/scale_setting';
import * as scale_strategies from './namespaces/scale_strategies';
import * as service from './namespaces/service';
import * as services from './namespaces/services';
import * as subscription from './namespaces/subscription';
import * as tce from './namespaces/tce';
import * as template from './namespaces/template';
import * as ticket from './namespaces/ticket';
import * as tickets from './namespaces/tickets';
import * as timer from './namespaces/timer';
import * as tos from './namespaces/tos';
import * as trigger from './namespaces/trigger';
import * as trigger_debug from './namespaces/trigger_debug';
import * as trigger_frozen_active from './namespaces/trigger_frozen_active';
import * as volcengine from './namespaces/volcengine';

export {
  admin,
  agent,
  alarm,
  async_request,
  build,
  burst_protector,
  cluster,
  code_revision,
  codeagent_cp,
  common,
  consul_trigger,
  debug,
  diagnosis,
  eventbus,
  filterplugin,
  http_trigger,
  image,
  instance,
  mcp_server,
  mcp_tool,
  mqevent,
  other,
  packages,
  pipeline_template,
  plugin_function,
  regional_meta,
  release,
  resource,
  resource_group,
  revision,
  sandbox,
  scale_record,
  scale_setting,
  scale_strategies,
  service,
  services,
  subscription,
  tce,
  template,
  ticket,
  tickets,
  timer,
  tos,
  trigger,
  trigger_debug,
  trigger_frozen_active,
  volcengine,
};
export * from './namespaces/admin';
export * from './namespaces/agent';
export * from './namespaces/alarm';
export * from './namespaces/async_request';
export * from './namespaces/build';
export * from './namespaces/burst_protector';
export * from './namespaces/cluster';
export * from './namespaces/code_revision';
export * from './namespaces/codeagent_cp';
export * from './namespaces/common';
export * from './namespaces/consul_trigger';
export * from './namespaces/debug';
export * from './namespaces/diagnosis';
export * from './namespaces/eventbus';
export * from './namespaces/filterplugin';
export * from './namespaces/http_trigger';
export * from './namespaces/image';
export * from './namespaces/instance';
export * from './namespaces/mcp_server';
export * from './namespaces/mcp_tool';
export * from './namespaces/mqevent';
export * from './namespaces/other';
export * from './namespaces/packages';
export * from './namespaces/pipeline_template';
export * from './namespaces/plugin_function';
export * from './namespaces/regional_meta';
export * from './namespaces/release';
export * from './namespaces/resource';
export * from './namespaces/resource_group';
export * from './namespaces/revision';
export * from './namespaces/sandbox';
export * from './namespaces/scale_record';
export * from './namespaces/scale_setting';
export * from './namespaces/scale_strategies';
export * from './namespaces/service';
export * from './namespaces/services';
export * from './namespaces/subscription';
export * from './namespaces/tce';
export * from './namespaces/template';
export * from './namespaces/ticket';
export * from './namespaces/tickets';
export * from './namespaces/timer';
export * from './namespaces/tos';
export * from './namespaces/trigger';
export * from './namespaces/trigger_debug';
export * from './namespaces/trigger_frozen_active';
export * from './namespaces/volcengine';

export type Int64 = string | number;

export default class BytefaasApiService<T> {
  private request: any = () => {
    throw new Error('BytefaasApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/triggers_enabled */
  getTriggersEnabled(
    req: trigger.GetTriggersEnabledRequest,
    options?: T,
  ): Promise<trigger.GetTriggersEnabledResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers_enabled`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/mqtriggers/:trigger_id
   *
   * 更新指定MQ触发器
   */
  updateMQTrigger(
    req: mqevent.UpdateMQTriggerRequest,
    options?: T,
  ): Promise<mqevent.UpdateMQTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/mqtriggers/${_req['trigger_id']}`,
    );
    const method = 'PATCH';
    const data = {
      alarm_params: _req['alarm_params'],
      allow_bytesuite_debug: _req['allow_bytesuite_debug'],
      batch_size: _req['batch_size'],
      cell: _req['cell'],
      deployment_inactive: _req['deployment_inactive'],
      description: _req['description'],
      disable_backoff: _req['disable_backoff'],
      disable_smooth_wrr: _req['disable_smooth_wrr'],
      dynamic_load_balance_type: _req['dynamic_load_balance_type'],
      dynamic_worker_thread: _req['dynamic_worker_thread'],
      enable_backoff: _req['enable_backoff'],
      enable_concurrency_filter: _req['enable_concurrency_filter'],
      enable_congestion_control: _req['enable_congestion_control'],
      enable_dynamic_load_balance: _req['enable_dynamic_load_balance'],
      enable_global_rate_limiter: _req['enable_global_rate_limiter'],
      enable_ipc_mode: _req['enable_ipc_mode'],
      enable_mq_debug: _req['enable_mq_debug'],
      enable_pod_colocate_scheduling: _req['enable_pod_colocate_scheduling'],
      enable_static_membership: _req['enable_static_membership'],
      enable_traffic_priority_scheduling:
        _req['enable_traffic_priority_scheduling'],
      enabled: _req['enabled'],
      envs: _req['envs'],
      function_id: _req['function_id'],
      hot_reload: _req['hot_reload'],
      id: _req['id'],
      image_alias: _req['image_alias'],
      image_version: _req['image_version'],
      initial_offset_start_from: _req['initial_offset_start_from'],
      is_auth_info_updated: _req['is_auth_info_updated'],
      max_retries_from_function_status:
        _req['max_retries_from_function_status'],
      mq_logger_limit_size: _req['mq_logger_limit_size'],
      mq_msg_type: _req['mq_msg_type'],
      mq_region: _req['mq_region'],
      mq_type: _req['mq_type'],
      ms_alarm_id: _req['ms_alarm_id'],
      msg_chan_length: _req['msg_chan_length'],
      name: _req['name'],
      need_auto_sharding: _req['need_auto_sharding'],
      num_of_mq_pod_to_one_func_pod: _req['num_of_mq_pod_to_one_func_pod'],
      options: _req['options'],
      plugin_function_param: _req['plugin_function_param'],
      qps_limit: _req['qps_limit'],
      replica_max_limit: _req['replica_max_limit'],
      replica_min_limit: _req['replica_min_limit'],
      replicas: _req['replicas'],
      request_timeout: _req['request_timeout'],
      resource: _req['resource'],
      runtime_agent_mode: _req['runtime_agent_mode'],
      scale_enabled: _req['scale_enabled'],
      scale_settings: _req['scale_settings'],
      sdk_version: _req['sdk_version'],
      vertical_scale_enabled: _req['vertical_scale_enabled'],
      worker_v2_num_per_half_core: _req['worker_v2_num_per_half_core'],
      workers_per_pod: _req['workers_per_pod'],
      enable_plugin_function: _req['enable_plugin_function'],
      disable_infinite_retry_for_timeout:
        _req['disable_infinite_retry_for_timeout'],
      mirror_region_filter: _req['mirror_region_filter'],
      enable_gctuner: _req['enable_gctuner'],
      gctuner_percent: _req['gctuner_percent'],
      retry_strategy: _req['retry_strategy'],
      max_retry_time: _req['max_retry_time'],
      qps_limit_time_ranges: _req['qps_limit_time_ranges'],
      rate_limit_step_settings: _req['rate_limit_step_settings'],
      enable_step_rate_limit: _req['enable_step_rate_limit'],
      enable_filter_congestion_control:
        _req['enable_filter_congestion_control'],
      enable_congestion_control_cache: _req['enable_congestion_control_cache'],
      image_version_number: _req['image_version_number'],
      host_uniq: _req['host_uniq'],
      in_cell_migration: _req['in_cell_migration'],
      mq_canary_update_params: _req['mq_canary_update_params'],
      pipeline_params: _req['pipeline_params'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /v2/services/:service_id/regions/:region/clusters/:cluster/revisions/:revision_number
   *
   * 删除函数版本
   */
  deleteFunctionRevision(
    req: revision.DeleteFunctionRevisionRequest,
    options?: T,
  ): Promise<revision.DeleteFunctionRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/revisions/${_req['revision_number']}`,
    );
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /**
   * GET /v2/services/:service_id/regions/:region/clusters/:cluster/online_revisions
   *
   * 获取线上函数版本信息
   */
  getOnlineRevision(
    req: revision.GetOnlineRevisionRequest,
    options?: T,
  ): Promise<revision.GetOnlineRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/online_revisions`,
    );
    const method = 'GET';
    const params = { format: _req['format'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/latest_revisions */
  getLatestRevision(
    req: revision.GetLatestRevisionRequest,
    options?: T,
  ): Promise<revision.GetLatestRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/latest_revisions`,
    );
    const method = 'GET';
    const params = { format: _req['format'] };
    return this.request({ url, method, params }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/http_triggers/:trigger_id */
  patchHttpTrigger(
    req: http_trigger.PatchHttpTriggerRequest,
    options?: T,
  ): Promise<http_trigger.PatchHttpTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/http_triggers/${_req['trigger_id']}`,
    );
    const method = 'PATCH';
    const data = {
      bytefaas_error_response_disabled:
        _req['bytefaas_error_response_disabled'],
      bytefaas_response_header_disabled:
        _req['bytefaas_response_header_disabled'],
      description: _req['description'],
      enabled: _req['enabled'],
      name: _req['name'],
      url_prefix: _req['url_prefix'],
      version_type: _req['version_type'],
      version_value: _req['version_value'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/:trigger_type/:trigger_id
   *
   * 获取指定触发器信息
   */
  getMqTriggerByType(
    req: mqevent.GetMqTriggerByTypeRequest,
    options?: T,
  ): Promise<mqevent.GetMqTriggerByTypeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/${_req['trigger_type']}/${_req['trigger_id']}`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/diagnosis/:diagnosis_id */
  getDiagnosisByID(
    req: diagnosis.GetDiagnosisByIDRequest,
    options?: T,
  ): Promise<diagnosis.GetDiagnosisByIDResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/diagnosis/${_req['diagnosis_id']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/tickets */
  getTicketsByFilter(
    req: admin.GetTicketsByFilterRequest,
    options?: T,
  ): Promise<admin.GetTicketsByFilterResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/tickets');
    const method = 'GET';
    const params = {
      category: _req['category'],
      change_type: _req['change_type'],
      cluster: _req['cluster'],
      function_id: _req['function_id'],
      id: _req['id'],
      max_create_time: _req['max_create_time'],
      min_create_time: _req['min_create_time'],
      only_admin_ticket: _req['only_admin_ticket'],
      parent_id: _req['parent_id'],
      region: _req['region'],
      status: _req['status'],
      trigger_id: _req['trigger_id'],
      trigger_type: _req['trigger_type'],
      type: _req['type'],
      limit: _req['limit'],
      offset: _req['offset'],
    };
    return this.request({ url, method, params }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/timers/:timer_id */
  updateTimerTrigger(
    req: timer.UpdateTimerTriggerRequest,
    options?: T,
  ): Promise<timer.UpdateTimerTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/timers/${_req['timer_id']}`,
    );
    const method = 'PATCH';
    const data = {
      cell: _req['cell'],
      concurrency_limit: _req['concurrency_limit'],
      created_at: _req['created_at'],
      cron: _req['cron'],
      description: _req['description'],
      enabled: _req['enabled'],
      name: _req['name'],
      payload: _req['payload'],
      retries: _req['retries'],
      scheduled_at: _req['scheduled_at'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/invoke */
  debugFunction(
    req: debug.DebugFunctionRequest,
    options?: T,
  ): Promise<debug.DebugFunctionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/invoke`,
    );
    const method = 'POST';
    const data = {
      batch: _req['batch'],
      data: _req['data'],
      extensions: _req['extensions'],
      type: _req['type'],
      verbose: _req['verbose'],
      event_name: _req['event_name'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /v2/services/:service_id/code_revisions
   *
   * 获取指定服务的代码版本信息列表
   */
  getCodeRevisions(
    req: code_revision.GetCodeRevisionsRequest,
    options?: T,
  ): Promise<code_revision.GetCodeRevisionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/code_revisions`,
    );
    const method = 'GET';
    const params = { limit: _req['limit'], offset: _req['offset'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/tickets/:ticket_id */
  getTicketDetailByTicketID(
    req: ticket.GetTicketDetailByTicketIDRequest,
    options?: T,
  ): Promise<ticket.GetTicketDetailByTicketIDResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/tickets/${_req['ticket_id']}`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * PATCH /v2/services/:service_id/regions/:region/clusters/:cluster
   *
   * 更新指定集群
   */
  updateCluster(
    req: cluster.UpdateClusterRequest,
    options?: T,
  ): Promise<cluster.UpdateClusterResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}`,
    );
    const method = 'PATCH';
    const data = {
      auth_enable: _req['auth_enable'],
      code_revision_id: _req['code_revision_id'],
      code_revision_number: _req['code_revision_number'],
      cold_start_disabled: _req['cold_start_disabled'],
      cors_enable: _req['cors_enable'],
      enable_colocate_scheduling: _req['enable_colocate_scheduling'],
      enable_scale_strategy: _req['enable_scale_strategy'],
      exclusive_mode: _req['exclusive_mode'],
      format_envs: _req['format_envs'],
      gateway_route_enable: _req['gateway_route_enable'],
      gdpr_enable: _req['gdpr_enable'],
      global_kv_namespace_ids: _req['global_kv_namespace_ids'],
      http_trigger_disable: _req['http_trigger_disable'],
      initializer_sec: _req['initializer_sec'],
      is_ipv6_only: _req['is_ipv6_only'],
      is_this_zone_disabled: _req['is_this_zone_disabled'],
      latency_sec: _req['latency_sec'],
      max_concurrency: _req['max_concurrency'],
      network_mode: _req['network_mode'],
      reserved_dp_enabled: _req['reserved_dp_enabled'],
      resource_limit: _req['resource_limit'],
      revision_id: _req['revision_id'],
      revision_number: _req['revision_number'],
      routing_strategy: _req['routing_strategy'],
      scale_enabled: _req['scale_enabled'],
      scale_threshold: _req['scale_threshold'],
      scale_type: _req['scale_type'],
      trace_enable: _req['trace_enable'],
      zone_throttle_log_bytes_per_sec: _req['zone_throttle_log_bytes_per_sec'],
      zti_enable: _req['zti_enable'],
      throttle_log_bytes_per_sec: _req['throttle_log_bytes_per_sec'],
      throttle_stdout_log_bytes_per_sec:
        _req['throttle_stdout_log_bytes_per_sec'],
      throttle_stderr_log_bytes_per_sec:
        _req['throttle_stderr_log_bytes_per_sec'],
      cold_start_sec: _req['cold_start_sec'],
      async_mode: _req['async_mode'],
      enable_scale_optimise: _req['enable_scale_optimise'],
      enable_runtime_file_log: _req['enable_runtime_file_log'],
      enable_runtime_console_log: _req['enable_runtime_console_log'],
      enable_runtime_stream_log: _req['enable_runtime_stream_log'],
      enable_runtime_es_log: _req['enable_runtime_es_log'],
      enable_runtime_json_log: _req['enable_runtime_json_log'],
      enable_system_stream_log: _req['enable_system_stream_log'],
      enable_system_es_log: _req['enable_system_es_log'],
      runtime_stream_log_bytes_per_sec:
        _req['runtime_stream_log_bytes_per_sec'],
      system_stream_log_bytes_per_sec: _req['system_stream_log_bytes_per_sec'],
      pod_type: _req['pod_type'],
      online_mode: _req['online_mode'],
      enable_reserve_frozen_instance: _req['enable_reserve_frozen_instance'],
      cluster_run_cmd: _req['cluster_run_cmd'],
      disable_service_discovery: _req['disable_service_discovery'],
      async_result_emit_event_bridge: _req['async_result_emit_event_bridge'],
      resource_guarantee: _req['resource_guarantee'],
      mq_trigger_limit: _req['mq_trigger_limit'],
      cell: _req['cell'],
      lazyload: _req['lazyload'],
      image_lazyload: _req['image_lazyload'],
      initializer: _req['initializer'],
      handler: _req['handler'],
      run_cmd: _req['run_cmd'],
      throttle_log_enabled: _req['throttle_log_enabled'],
      adaptive_concurrency_mode: _req['adaptive_concurrency_mode'],
      env_name: _req['env_name'],
      container_runtime: _req['container_runtime'],
      protocol: _req['protocol'],
      overload_protect_enabled: _req['overload_protect_enabled'],
      mq_consumer_meta: _req['mq_consumer_meta'],
      enable_consul_ipv6_register: _req['enable_consul_ipv6_register'],
      enable_sys_mount: _req['enable_sys_mount'],
      disable_mount_jwt_bundles: _req['disable_mount_jwt_bundles'],
      termination_grace_period_seconds:
        _req['termination_grace_period_seconds'],
      enable_consul_register: _req['enable_consul_register'],
      host_uniq: _req['host_uniq'],
      in_cell_migration: _req['in_cell_migration'],
      enable_session_request: _req['enable_session_request'],
      zone_reserved_frozen_replicas: _req['zone_reserved_frozen_replicas'],
      frozen_cpu_milli: _req['frozen_cpu_milli'],
      privileged: _req['privileged'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/consul_triggers */
  createConsulTrigger(
    req: consul_trigger.CreateConsulTriggerRequest,
    options?: T,
  ): Promise<consul_trigger.CreateConsulTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/consul_triggers`,
    );
    const method = 'POST';
    const data = {
      description: _req['description'],
      enabled: _req['enabled'],
      name: _req['name'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/filterplugins/:filter_plugin_id */
  getFilterPluginsDetail(
    req: filterplugin.GetFilterPluginsDetailRequest,
    options?: T,
  ): Promise<filterplugin.GetFilterPluginsDetailResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/filterplugins/${_req['filter_plugin_id']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/regions */
  getDeployedRegions(
    req: services.GetDeployedRegionsRequest,
    options?: T,
  ): Promise<services.GetDeployedRegionsResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/services/${_req['service_id']}/regions`);
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /**
   * DELETE /v2/services/:service_id/regions/:region/clusters/:cluster
   *
   * 删除指定集群
   */
  deleteCluster(
    req: cluster.DeleteClusterRequest,
    options?: T,
  ): Promise<cluster.DeleteClusterResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}`,
    );
    const method = 'DELETE';
    const params = { soft: _req['soft'], reason: _req['reason'] };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/revisions/:revision_number/code.zip */
  downloadRevisionCode(
    req: revision.DownloadRevisionCodeRequest,
    options?: T,
  ): Promise<revision.DownloadRevisionCodeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/revisions/${_req['revision_number']}/code.zip`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/alarms */
  getClusterAlarm(
    req: alarm.GetClusterAlarmRequest,
    options?: T,
  ): Promise<alarm.GetClusterAlarmResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/alarms`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/tos/:region/bucketlist */
  getTosBuckets(
    req: tos.GetTosBucketsRequest,
    options?: T,
  ): Promise<tos.GetTosBucketsResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/tos/${_req['region']}/bucketlist`);
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /**
   * POST /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/timers
   *
   * Create a new Faas timer trigger, and returns the created timer trigger
   */
  createTimerTrigger(
    req: common.CreateTimerTriggerRequest,
    options?: T,
  ): Promise<timer.CreateTimerTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/timers`,
    );
    const method = 'POST';
    const data = {
      cell: _req['cell'],
      concurrency_limit: _req['concurrency_limit'],
      created_at: _req['created_at'],
      cron: _req['cron'],
      description: _req['description'],
      enabled: _req['enabled'],
      name: _req['name'],
      payload: _req['payload'],
      retries: _req['retries'],
      scheduled_at: _req['scheduled_at'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * DELETE /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/:trigger_type/:trigger_id
   *
   * 删除指定触发器
   */
  deleteMqTriggerByType(
    req: mqevent.DeleteMqTriggerByTypeRequest,
    options?: T,
  ): Promise<mqevent.DeleteMqTriggerByTypeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/${_req['trigger_type']}/${_req['trigger_id']}`,
    );
    const method = 'DELETE';
    const params = {
      caller: _req['caller'],
      consumer_group: _req['consumer_group'],
      eventbus_name: _req['eventbus_name'],
      pipeline_template_id: _req['pipeline_template_id'],
      pipeline_template_type: _req['pipeline_template_type'],
      approved_by: _req['approved_by'],
      approved_by_usertype: _req['approved_by_usertype'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /v2/services/:service_id/code_revisions/:revision_number/code.zip */
  downloadCodeRevisionPackage(
    req: code_revision.DownloadCodeRevisionPackageRequest,
    options?: T,
  ): Promise<code_revision.DownloadCodeRevisionPackageResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/code_revisions/${_req['revision_number']}/code.zip`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/consul_triggers/:trigger_id */
  getConsulTrigger(
    req: consul_trigger.GetConsulTriggerRequest,
    options?: T,
  ): Promise<consul_trigger.GetConsulTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/consul_triggers/${_req['trigger_id']}`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/async_request */
  getAsyncRequest(
    req: async_request.GetAsyncRequestRequest,
    options?: T,
  ): Promise<async_request.GetAsyncRequestResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/async_request`,
    );
    const method = 'GET';
    const headers = { 'x-bytefaas-request-id': _req['x-bytefaas-request-id'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/services/:service_id/code_revisions/:revision_number */
  getCodeRevisionByNumber(
    req: code_revision.GetCodeRevisionByNumberRequest,
    options?: T,
  ): Promise<code_revision.GetCodeRevisionByNumberResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/code_revisions/${_req['revision_number']}`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/regional_meta */
  getRegionalMeta(
    req: regional_meta.GetRegionalMetaRequest,
    options?: T,
  ): Promise<regional_meta.GetRegionalMetaResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/regional_meta`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /**
   * GET /v2/services/:service_id
   *
   * 获取服务信息
   */
  getService(
    req: service.GetServiceRequest,
    options?: T,
  ): Promise<service.GetServiceResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/services/${_req['service_id']}`);
    const method = 'GET';
    const params = { region: _req['region'], verbose: _req['verbose'] };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /v2/services/:service_id/subscription
   *
   * Add a function subscription, and returns successful or failed
   */
  subscribeService(
    req: subscription.SubscribeServiceRequest,
    options?: T,
  ): Promise<subscription.SubscribeServiceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/subscription`,
    );
    const method = 'POST';
    const data = { subscribers: _req['subscribers'] };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** DELETE /v2/services/:service_id/regions/:region/clusters/:cluster/http_triggers/:trigger_id */
  deleteHttpTrigger(
    req: http_trigger.DeleteHttpTriggerRequest,
    options?: T,
  ): Promise<http_trigger.DeleteHttpTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/http_triggers/${_req['trigger_id']}`,
    );
    const method = 'DELETE';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** DELETE /v2/services/:service_id/regions/:region/clusters/:cluster/scale_strategies/:strategy_id */
  deleteScaleStrategy(
    req: scale_strategies.DeleteScaleStrategyRequest,
    options?: T,
  ): Promise<scale_strategies.DeleteScaleStrategyResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/scale_strategies/${_req['strategy_id']}`,
    );
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/online_code_revisions */
  getOnlineCodeRevision(
    req: code_revision.GetOnlineCodeRevisionRequest,
    options?: T,
  ): Promise<code_revision.GetOnlineCodeRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/online_code_revisions`,
    );
    const method = 'GET';
    const params = { region: _req['region'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /v2/services/:service_id/tickets
   *
   * 获取服务下工单列表
   */
  getTickets(
    req: release.GetTicketsRequest,
    options?: T,
  ): Promise<release.GetTicketsResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/services/${_req['service_id']}/tickets`);
    const method = 'GET';
    const params = {
      category: _req['category'],
      change_type: _req['change_type'],
      cluster: _req['cluster'],
      id: _req['id'],
      max_create_time: _req['max_create_time'],
      min_create_time: _req['min_create_time'],
      region: _req['region'],
      status: _req['status'],
      trigger_id: _req['trigger_id'],
      trigger_type: _req['trigger_type'],
      type: _req['type'],
      contains_multi_clusters: _req['contains_multi_clusters'],
      offset: _req['offset'],
      limit: _req['limit'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/latest_revisions
   *
   * 更新函数的 Latest 版本
   */
  updateFunctionLatestRevision(
    req: revision.UpdateFunctionLatestRevisionRequest,
    options?: T,
  ): Promise<revision.UpdateFunctionLatestRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/latest_revisions`,
    );
    const method = 'PATCH';
    return this.request({ url, method }, options);
  }

  /** GET /v2/admin */
  getAllAdministrator(
    req?: admin.getAllAdministratorRequest,
    options?: T,
  ): Promise<admin.GetAllAdministratorResponse> {
    const url = this.genBaseURL('/v2/admin');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /v2/services/:service_id/regions/:region/clusters/:cluster/triggers
   *
   * 获取服务下所有触发器信息
   */
  getAllTriggers(
    req: trigger.GetAllTriggersRequest,
    options?: T,
  ): Promise<trigger.GetAllTriggersResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers`,
    );
    const method = 'GET';
    const params = {
      split_eventbus: _req['split_eventbus'],
      with_env_trigger: _req['with_env_trigger'],
      not_show_eb_trigger: _req['not_show_eb_trigger'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/:trigger_type/:trigger_id
   *
   * 更新指定类型的MQ触发器
   */
  patchMqTriggerByType(
    req: mqevent.PatchMqTriggerByTypeRequest,
    options?: T,
  ): Promise<mqevent.PatchMqTriggerByTypeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/${_req['trigger_type']}/${_req['trigger_id']}`,
    );
    const method = 'PATCH';
    const data = {
      alarm_params: _req['alarm_params'],
      allow_bytesuite_debug: _req['allow_bytesuite_debug'],
      batch_size: _req['batch_size'],
      cell: _req['cell'],
      deployment_inactive: _req['deployment_inactive'],
      description: _req['description'],
      disable_backoff: _req['disable_backoff'],
      disable_smooth_wrr: _req['disable_smooth_wrr'],
      dynamic_load_balance_type: _req['dynamic_load_balance_type'],
      dynamic_worker_thread: _req['dynamic_worker_thread'],
      enable_backoff: _req['enable_backoff'],
      enable_concurrency_filter: _req['enable_concurrency_filter'],
      enable_congestion_control: _req['enable_congestion_control'],
      enable_dynamic_load_balance: _req['enable_dynamic_load_balance'],
      enable_global_rate_limiter: _req['enable_global_rate_limiter'],
      enable_ipc_mode: _req['enable_ipc_mode'],
      enable_mq_debug: _req['enable_mq_debug'],
      enable_pod_colocate_scheduling: _req['enable_pod_colocate_scheduling'],
      enable_static_membership: _req['enable_static_membership'],
      enable_traffic_priority_scheduling:
        _req['enable_traffic_priority_scheduling'],
      enabled: _req['enabled'],
      envs: _req['envs'],
      function_id: _req['function_id'],
      hot_reload: _req['hot_reload'],
      id: _req['id'],
      image_alias: _req['image_alias'],
      image_version: _req['image_version'],
      initial_offset_start_from: _req['initial_offset_start_from'],
      is_auth_info_updated: _req['is_auth_info_updated'],
      max_retries_from_function_status:
        _req['max_retries_from_function_status'],
      mq_logger_limit_size: _req['mq_logger_limit_size'],
      mq_msg_type: _req['mq_msg_type'],
      mq_region: _req['mq_region'],
      mq_type: _req['mq_type'],
      ms_alarm_id: _req['ms_alarm_id'],
      msg_chan_length: _req['msg_chan_length'],
      name: _req['name'],
      need_auto_sharding: _req['need_auto_sharding'],
      num_of_mq_pod_to_one_func_pod: _req['num_of_mq_pod_to_one_func_pod'],
      options: _req['options'],
      plugin_function_param: _req['plugin_function_param'],
      qps_limit: _req['qps_limit'],
      replica_max_limit: _req['replica_max_limit'],
      replica_min_limit: _req['replica_min_limit'],
      replicas: _req['replicas'],
      request_timeout: _req['request_timeout'],
      resource: _req['resource'],
      runtime_agent_mode: _req['runtime_agent_mode'],
      scale_enabled: _req['scale_enabled'],
      scale_settings: _req['scale_settings'],
      sdk_version: _req['sdk_version'],
      vertical_scale_enabled: _req['vertical_scale_enabled'],
      worker_v2_num_per_half_core: _req['worker_v2_num_per_half_core'],
      workers_per_pod: _req['workers_per_pod'],
      enable_plugin_function: _req['enable_plugin_function'],
      disable_infinite_retry_for_timeout:
        _req['disable_infinite_retry_for_timeout'],
      mirror_region_filter: _req['mirror_region_filter'],
      enable_gctuner: _req['enable_gctuner'],
      gctuner_percent: _req['gctuner_percent'],
      retry_strategy: _req['retry_strategy'],
      max_retry_time: _req['max_retry_time'],
      qps_limit_time_ranges: _req['qps_limit_time_ranges'],
      rate_limit_step_settings: _req['rate_limit_step_settings'],
      enable_step_rate_limit: _req['enable_step_rate_limit'],
      enable_filter_congestion_control:
        _req['enable_filter_congestion_control'],
      enable_congestion_control_cache: _req['enable_congestion_control_cache'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/function_templates/:template_name */
  getTemplateByName(
    req: template.GetTemplateByNameRequest,
    options?: T,
  ): Promise<template.GetTemplateByNameResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/function_templates/${_req['template_name']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/runtimes */
  getRuntime(
    req?: other.getRuntimeRequest,
    options?: T,
  ): Promise<other.GetRuntimeResponse> {
    const url = this.genBaseURL('/v2/runtimes');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** DELETE /v2/services/:service_id/regions/:region/clusters/:cluster/async_request */
  killAsyncRequests(
    req: async_request.KillAsyncRequestsRequest,
    options?: T,
  ): Promise<async_request.KillAsyncRequestsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/async_request`,
    );
    const method = 'DELETE';
    const headers = { 'x-bytefaas-request-id': _req['x-bytefaas-request-id'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/filterplugins */
  getFilterPlugins(
    req: filterplugin.GetFilterPluginsRequest,
    options?: T,
  ): Promise<filterplugin.GetFilterPluginsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/filterplugins`,
    );
    const method = 'GET';
    const params = { offset: _req['offset'], limit: _req['limit'] };
    return this.request({ url, method, params }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/scale_strategies/:strategy_id */
  patchScaleStrategy(
    req: scale_strategies.PatchScaleStrategyRequest,
    options?: T,
  ): Promise<scale_strategies.PatchScaleStrategyResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/scale_strategies/${_req['strategy_id']}`,
    );
    const method = 'PATCH';
    const data = {
      effective_time: _req['effective_time'],
      enabled: _req['enabled'],
      expired_time: _req['expired_time'],
      function_id: _req['function_id'],
      inner_strategy: _req['inner_strategy'],
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      strategy_name: _req['strategy_name'],
      strategy_type: _req['strategy_type'],
      instance_type: _req['instance_type'],
    };
    const params = { bpm_update_type: _req['bpm_update_type'] };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * POST /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/:trigger_type/:trigger_id/reset_mq_offset
   *
   * 重置指定MQ触发器Offset
   */
  resetMQOffset(
    req: trigger.ResetMQOffsetRequest,
    options?: T,
  ): Promise<trigger.ResetMQOffsetResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/${_req['trigger_type']}/${_req['trigger_id']}/reset_mq_offset`,
    );
    const method = 'POST';
    const data = {
      dryRun: _req['dryRun'],
      force_stop: _req['force_stop'],
      offset: _req['offset'],
      resetType: _req['resetType'],
      reset_details_per_partition_array:
        _req['reset_details_per_partition_array'],
      timestamp: _req['timestamp'],
      whence: _req['whence'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /v2/services/:service_id/regions/:region/clusters/:cluster/mqtriggers
   *
   * 创建MQ触发器
   */
  createMQTrigger(
    req: common.CreateMQTriggerRequest,
    options?: T,
  ): Promise<mqevent.CreateMQTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/:region/clusters/${_req['cluster']}/mqtriggers`,
    );
    const method = 'POST';
    const data = {
      alarm_params: _req['alarm_params'],
      allow_bytesuite_debug: _req['allow_bytesuite_debug'],
      batch_size: _req['batch_size'],
      cell: _req['cell'],
      deployment_inactive: _req['deployment_inactive'],
      description: _req['description'],
      disable_backoff: _req['disable_backoff'],
      disable_smooth_wrr: _req['disable_smooth_wrr'],
      dynamic_load_balance_type: _req['dynamic_load_balance_type'],
      dynamic_worker_thread: _req['dynamic_worker_thread'],
      enable_backoff: _req['enable_backoff'],
      enable_concurrency_filter: _req['enable_concurrency_filter'],
      enable_congestion_control: _req['enable_congestion_control'],
      enable_dynamic_load_balance: _req['enable_dynamic_load_balance'],
      enable_global_rate_limiter: _req['enable_global_rate_limiter'],
      enable_ipc_mode: _req['enable_ipc_mode'],
      enable_mq_debug: _req['enable_mq_debug'],
      enable_pod_colocate_scheduling: _req['enable_pod_colocate_scheduling'],
      enable_static_membership: _req['enable_static_membership'],
      enable_traffic_priority_scheduling:
        _req['enable_traffic_priority_scheduling'],
      enabled: _req['enabled'],
      envs: _req['envs'],
      function_id: _req['function_id'],
      hot_reload: _req['hot_reload'],
      id: _req['id'],
      image_alias: _req['image_alias'],
      image_version: _req['image_version'],
      initial_offset_start_from: _req['initial_offset_start_from'],
      is_auth_info_updated: _req['is_auth_info_updated'],
      max_retries_from_function_status:
        _req['max_retries_from_function_status'],
      mq_logger_limit_size: _req['mq_logger_limit_size'],
      mq_msg_type: _req['mq_msg_type'],
      mq_region: _req['mq_region'],
      mq_type: _req['mq_type'],
      ms_alarm_id: _req['ms_alarm_id'],
      msg_chan_length: _req['msg_chan_length'],
      name: _req['name'],
      need_auto_sharding: _req['need_auto_sharding'],
      num_of_mq_pod_to_one_func_pod: _req['num_of_mq_pod_to_one_func_pod'],
      options: _req['options'],
      plugin_function_param: _req['plugin_function_param'],
      qps_limit: _req['qps_limit'],
      region: _req['region'],
      replica_max_limit: _req['replica_max_limit'],
      replica_min_limit: _req['replica_min_limit'],
      replicas: _req['replicas'],
      request_timeout: _req['request_timeout'],
      resource: _req['resource'],
      runtime_agent_mode: _req['runtime_agent_mode'],
      scale_enabled: _req['scale_enabled'],
      scale_settings: _req['scale_settings'],
      sdk_version: _req['sdk_version'],
      vertical_scale_enabled: _req['vertical_scale_enabled'],
      worker_v2_num_per_half_core: _req['worker_v2_num_per_half_core'],
      workers_per_pod: _req['workers_per_pod'],
      enable_plugin_function: _req['enable_plugin_function'],
      disable_infinite_retry_for_timeout:
        _req['disable_infinite_retry_for_timeout'],
      mirror_region_filter: _req['mirror_region_filter'],
      enable_gctuner: _req['enable_gctuner'],
      gctuner_percent: _req['gctuner_percent'],
      retry_strategy: _req['retry_strategy'],
      max_retry_time: _req['max_retry_time'],
      qps_limit_time_ranges: _req['qps_limit_time_ranges'],
      rate_limit_step_settings: _req['rate_limit_step_settings'],
      enable_step_rate_limit: _req['enable_step_rate_limit'],
      batch_flush_duration_milliseconds:
        _req['batch_flush_duration_milliseconds'],
      replica_force_meet_partition: _req['replica_force_meet_partition'],
      limit_disaster_scenario: _req['limit_disaster_scenario'],
      max_dwell_time_minute: _req['max_dwell_time_minute'],
      enable_canary_update: _req['enable_canary_update'],
      traffic_config: _req['traffic_config'],
      pod_type: _req['pod_type'],
      package: _req['package'],
      qps_auto_limit: _req['qps_auto_limit'],
      enable_filter_congestion_control:
        _req['enable_filter_congestion_control'],
      enable_congestion_control_cache: _req['enable_congestion_control_cache'],
      host_uniq: _req['host_uniq'],
      in_cell_migration: _req['in_cell_migration'],
      pipeline_params: _req['pipeline_params'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/deploy_status */
  getClusterDeployedStatus(
    req: cluster.GetClusterDeployedStatusRequest,
    options?: T,
  ): Promise<cluster.GetClusterDeployedStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/deploy_status`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/resource/services */
  getResource(
    req?: resource.GetResourceRequest,
    options?: T,
  ): Promise<resource.GetResourceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/resource/services');
    const method = 'GET';
    const params = {
      all_region: _req['all_region'],
      env: _req['env'],
      function_id: _req['function_id'],
      psm: _req['psm'],
      region: _req['region'],
    };
    return this.request({ url, method, params }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/revisions/:revision_number */
  updateFunctionRevision(
    req: revision.UpdateFunctionRevisionRequest,
    options?: T,
  ): Promise<revision.UpdateFunctionRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/revisions/${_req['revision_number']}`,
    );
    const method = 'PATCH';
    const data = {
      deploy_method: _req['deploy_method'],
      envs: _req['envs'],
      handler: _req['handler'],
      runtime: _req['runtime'],
      source: _req['source'],
      source_type: _req['source_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/zones/:zone/instances/:podname/webshell */
  getInstancesWebshell(
    req: instance.GetInstancesWebshellRequest,
    options?: T,
  ): Promise<instance.GetInstancesWebshellResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/zones/${_req['zone']}/instances/${_req['podname']}/webshell`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/mqevents/:mq_type/mqclusters */
  getMqClusters(
    req: trigger.GetMqClustersRequest,
    options?: T,
  ): Promise<trigger.GetMqClustersResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mqevents/${_req['mq_type']}/mqclusters`);
    const method = 'GET';
    const params = { region: _req['region'] };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/:trigger_type
   *
   * 创建指定类型的触发器
   */
  createMqTriggerByType(
    req: mqevent.CreateMqTriggerByTypeRequest,
    options?: T,
  ): Promise<mqevent.CreateMqTriggerByTypeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/${_req['trigger_type']}`,
    );
    const method = 'POST';
    const data = {
      batch_size: _req['batch_size'],
      batch_flush_duration_milliseconds:
        _req['batch_flush_duration_milliseconds'],
      description: _req['description'],
      enabled: _req['enabled'],
      envs: _req['envs'],
      function_id: _req['function_id'],
      cell: _req['cell'],
      id: _req['id'],
      image_version: _req['image_version'],
      sdk_version: _req['sdk_version'],
      image_alias: _req['image_alias'],
      ms_alarm_id: _req['ms_alarm_id'],
      mq_type: _req['mq_type'],
      max_retries_from_function_status:
        _req['max_retries_from_function_status'],
      msg_chan_length: _req['msg_chan_length'],
      name: _req['name'],
      need_auto_sharding: _req['need_auto_sharding'],
      num_of_mq_pod_to_one_func_pod: _req['num_of_mq_pod_to_one_func_pod'],
      options: _req['options'],
      qps_limit: _req['qps_limit'],
      region: _req['region'],
      mq_region: _req['mq_region'],
      runtime_agent_mode: _req['runtime_agent_mode'],
      dynamic_worker_thread: _req['dynamic_worker_thread'],
      replica_max_limit: _req['replica_max_limit'],
      replica_min_limit: _req['replica_min_limit'],
      replicas: _req['replicas'],
      resource: _req['resource'],
      scale_enabled: _req['scale_enabled'],
      vertical_scale_enabled: _req['vertical_scale_enabled'],
      enable_static_membership: _req['enable_static_membership'],
      workers_per_pod: _req['workers_per_pod'],
      alarm_params: _req['alarm_params'],
      request_timeout: _req['request_timeout'],
      disable_infinite_retry_for_timeout:
        _req['disable_infinite_retry_for_timeout'],
      initial_offset_start_from: _req['initial_offset_start_from'],
      enable_mq_debug: _req['enable_mq_debug'],
      mq_logger_limit_size: _req['mq_logger_limit_size'],
      enable_backoff: _req['enable_backoff'],
      disable_backoff: _req['disable_backoff'],
      worker_v2_num_per_half_core: _req['worker_v2_num_per_half_core'],
      enable_concurrency_filter: _req['enable_concurrency_filter'],
      enable_ipc_mode: _req['enable_ipc_mode'],
      enable_traffic_priority_scheduling:
        _req['enable_traffic_priority_scheduling'],
      enable_pod_colocate_scheduling: _req['enable_pod_colocate_scheduling'],
      enable_global_rate_limiter: _req['enable_global_rate_limiter'],
      enable_congestion_control: _req['enable_congestion_control'],
      allow_bytesuite_debug: _req['allow_bytesuite_debug'],
      enable_dynamic_load_balance: _req['enable_dynamic_load_balance'],
      disable_smooth_wrr: _req['disable_smooth_wrr'],
      dynamic_load_balance_type: _req['dynamic_load_balance_type'],
      replica_force_meet_partition: _req['replica_force_meet_partition'],
      scale_settings: _req['scale_settings'],
      hot_reload: _req['hot_reload'],
      mq_msg_type: _req['mq_msg_type'],
      status: _req['status'],
      in_releasing: _req['in_releasing'],
      mirror_region_filter: _req['mirror_region_filter'],
      enable_gctuner: _req['enable_gctuner'],
      gctuner_percent: _req['gctuner_percent'],
      retry_strategy: _req['retry_strategy'],
      max_retry_time: _req['max_retry_time'],
      qps_limit_time_ranges: _req['qps_limit_time_ranges'],
      limit_disaster_scenario: _req['limit_disaster_scenario'],
      enable_step_rate_limit: _req['enable_step_rate_limit'],
      rate_limit_step_settings: _req['rate_limit_step_settings'],
      max_dwell_time_minute: _req['max_dwell_time_minute'],
      qps_auto_limit: _req['qps_auto_limit'],
      plugin_function_param: _req['plugin_function_param'],
      enable_plugin_function: _req['enable_plugin_function'],
      enable_canary_update: _req['enable_canary_update'],
      traffic_config: _req['traffic_config'],
      is_auth_info_updated: _req['is_auth_info_updated'],
      pod_type: _req['pod_type'],
      package: _req['package'],
      enable_filter_congestion_control:
        _req['enable_filter_congestion_control'],
      enable_congestion_control_cache: _req['enable_congestion_control_cache'],
      host_uniq: _req['host_uniq'],
      in_cell_migration: _req['in_cell_migration'],
      pipeline_params: _req['pipeline_params'],
    };
    const params = { caller: _req['caller'] };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * GET /v2/services/:service_id/regions/:region/clusters/:cluster
   *
   * 获取指定集群信息
   */
  getCluster(
    req: cluster.GetClusterRequest,
    options?: T,
  ): Promise<cluster.GetClusterResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}`,
    );
    const method = 'GET';
    const params = {
      'use-argos-iframe': _req['use-argos-iframe'],
      verbose: _req['verbose'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /v2/function_templates */
  getFunctionTemplates(
    req?: template.getFunctionTemplatesRequest,
    options?: T,
  ): Promise<template.GetFunctionTemplatesResponse> {
    const url = this.genBaseURL('/v2/function_templates');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** PATCH /v2/services/:service_id */
  updateServiceInfoByServiceID(
    req: service.UpdateServiceInfoByServiceIDRequest,
    options?: T,
  ): Promise<service.UpdateServiceInfoByServiceIDResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/services/${_req['service_id']}`);
    const method = 'PATCH';
    const data = {
      admins: _req['admins'],
      authorizers: _req['authorizers'],
      base_image: _req['base_image'],
      category: _req['category'],
      description: _req['description'],
      name: _req['name'],
      need_approve: _req['need_approve'],
      origin: _req['origin'],
      owner: _req['owner'],
      plugin_name: _req['plugin_name'],
      runtime: _req['runtime'],
      service_level: _req['service_level'],
      service_purpose: _req['service_purpose'],
      subscribers: _req['subscribers'],
      code_file_size_mb: _req['code_file_size_mb'],
      psm: _req['psm'],
      psm_parent_id: _req['psm_parent_id'],
      enable_cluster_run_cmd: _req['enable_cluster_run_cmd'],
      disable_ppe_alarm: _req['disable_ppe_alarm'],
      net_queue: _req['net_queue'],
      ms_service_meta_params: _req['ms_service_meta_params'],
      language: _req['language'],
      mount_info: _req['mount_info'],
      approval_scope: _req['approval_scope'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/revisions/:revision_number/build */
  buildServiceRevision(
    req: build.BuildServiceRevisionRequest,
    options?: T,
  ): Promise<build.BuildServiceRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/revisions/${_req['revision_number']}/build`,
    );
    const method = 'POST';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /**
   * POST /v2/services/:service_id/code_revisions
   *
   * 创建代码版本
   */
  createCodeRevision(
    req: code_revision.CreateCodeRevisionRequest,
    options?: T,
  ): Promise<code_revision.CreateCodeRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/code_revisions`,
    );
    const method = 'POST';
    const data = {
      dependency: _req['dependency'],
      deploy_method: _req['deploy_method'],
      description: _req['description'],
      disable_build_install: _req['disable_build_install'],
      handler: _req['handler'],
      initializer: _req['initializer'],
      lazyload: _req['lazyload'],
      number: _req['number'],
      protocol: _req['protocol'],
      run_cmd: _req['run_cmd'],
      runtime: _req['runtime'],
      runtime_container_port: _req['runtime_container_port'],
      runtime_debug_container_port: _req['runtime_debug_container_port'],
      source: _req['source'],
      source_type: _req['source_type'],
      open_image_lazyload: _req['open_image_lazyload'],
      runtime_other_container_ports: _req['runtime_other_container_ports'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /v2/services
   *
   * 创建服务
   */
  createService(
    req: service.CreateServiceRequest,
    options?: T,
  ): Promise<service.CreateServiceResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/services');
    const method = 'POST';
    const data = {
      admins: _req['admins'],
      async_mode: _req['async_mode'],
      authorizers: _req['authorizers'],
      base_image: _req['base_image'],
      category: _req['category'],
      dependency: _req['dependency'],
      deploy_method: _req['deploy_method'],
      description: _req['description'],
      env_name: _req['env_name'],
      name: _req['name'],
      need_approve: _req['need_approve'],
      origin: _req['origin'],
      owner: _req['owner'],
      protocol: _req['protocol'],
      psm: _req['psm'],
      psm_parent_id: _req['psm_parent_id'],
      runtime: _req['runtime'],
      service_level: _req['service_level'],
      service_purpose: _req['service_purpose'],
      source: _req['source'],
      source_type: _req['source_type'],
      subscribers: _req['subscribers'],
      template_name: _req['template_name'],
      online_mode: _req['online_mode'],
      plugin_scm_path: _req['plugin_scm_path'],
      code_file_size_mb: _req['code_file_size_mb'],
      disable_ppe_alarm: _req['disable_ppe_alarm'],
      language: _req['language'],
      run_cmd: _req['run_cmd'],
      image_lazy_load: _req['image_lazy_load'],
      plugin_name: _req['plugin_name'],
      runtime_container_port: _req['runtime_container_port'],
      runtime_debug_container_port: _req['runtime_debug_container_port'],
      health_check_path: _req['health_check_path'],
      health_check_failure_threshold: _req['health_check_failure_threshold'],
      health_check_period: _req['health_check_period'],
      runtime_other_container_ports: _req['runtime_other_container_ports'],
      overload_protect_enabled: _req['overload_protect_enabled'],
      net_queue: _req['net_queue'],
      ms_service_meta_params: _req['ms_service_meta_params'],
      mount_info: _req['mount_info'],
      disable_build_install: _req['disable_build_install'],
      lazyload: _req['lazyload'],
      approval_scope: _req['approval_scope'],
      mq_type: _req['mq_type'],
      use_gpu: _req['use_gpu'],
      git_group: _req['git_group'],
      git_repo: _req['git_repo'],
      scm_repo: _req['scm_repo'],
      privileged: _req['privileged'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /v2/tickets/:ticket_id/actions
   *
   * v1 upgrade to v2
   */
  updateTicketAction(
    req: ticket.UpdateTicketActionRequest,
    options?: T,
  ): Promise<ticket.UpdateTicketActionResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/tickets/${_req['ticket_id']}/actions`);
    const method = 'POST';
    const data = { action: _req['action'] };
    const params = { service_id: _req['service_id'] };
    return this.request({ url, method, data, params }, options);
  }

  /** GET /v2/check_admin */
  checkUserIsAdministrator(
    req: admin.CheckUserIsAdministratorRequest,
    options?: T,
  ): Promise<admin.CheckUserIsAdministratorResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/check_admin');
    const method = 'GET';
    const params = { user: _req['user'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/filterplugins */
  createFilterPlugins(
    req: filterplugin.CreateFilterPluginsRequest,
    options?: T,
  ): Promise<filterplugin.CreateFilterPluginsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/filterplugins`,
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      zip_file: _req['zip_file'],
      zip_file_size: _req['zip_file_size'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/regional_meta */
  updateRegionalMeta(
    req?: regional_meta.UpdateRegionalMetaRequest,
    options?: T,
  ): Promise<regional_meta.UpdateRegionalMetaResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/regional_meta`,
    );
    const method = 'PATCH';
    const data = {
      function_id: _req['function_id'],
      function_name: _req['function_name'],
      revision_id: _req['revision_id'],
      owner: _req['owner'],
      psm: _req['psm'],
      cell: _req['cell'],
      is_this_zone_disabled: _req['is_this_zone_disabled'],
      zone_throttle_log_bytes_per_sec: _req['zone_throttle_log_bytes_per_sec'],
      gdpr_enable: _req['gdpr_enable'],
      cold_start_disabled: _req['cold_start_disabled'],
      exclusive_mode: _req['exclusive_mode'],
      async_mode: _req['async_mode'],
      online_mode: _req['online_mode'],
      auth_enable: _req['auth_enable'],
      cors_enable: _req['cors_enable'],
      trace_enable: _req['trace_enable'],
      gateway_route_enable: _req['gateway_route_enable'],
      is_ipv6_only: _req['is_ipv6_only'],
      zti_enable: _req['zti_enable'],
      http_trigger_disable: _req['http_trigger_disable'],
      aliases: _req['aliases'],
      runtime: _req['runtime'],
      env_name: _req['env_name'],
      global_kv_namespace_ids: _req['global_kv_namespace_ids'],
      local_cache_namespace_ids: _req['local_cache_namespace_ids'],
      protocol: _req['protocol'],
      latency_sec: _req['latency_sec'],
      net_class_id: _req['net_class_id'],
      envs: _req['envs'],
      in_releasing: _req['in_releasing'],
      reserved_dp_enabled: _req['reserved_dp_enabled'],
      routing_strategy: _req['routing_strategy'],
      bytefaas_error_response_disabled:
        _req['bytefaas_error_response_disabled'],
      bytefaas_response_header_disabled:
        _req['bytefaas_response_header_disabled'],
      enable_colocate_scheduling: _req['enable_colocate_scheduling'],
      network_mode: _req['network_mode'],
      dynamic_load_balancing_data_report_enabled:
        _req['dynamic_load_balancing_data_report_enabled'],
      dynamic_load_balancing_weight_enabled:
        _req['dynamic_load_balancing_weight_enabled'],
      dynamic_load_balancing_enabled_vdcs:
        _req['dynamic_load_balancing_enabled_vdcs'],
      dynamic_load_balance_type: _req['dynamic_load_balance_type'],
      deployment_inactive: _req['deployment_inactive'],
      is_this_zone_deployment_inactive:
        _req['is_this_zone_deployment_inactive'],
      package: _req['package'],
      pod_type: _req['pod_type'],
      plugin_name: _req['plugin_name'],
      allow_cold_start_instance: _req['allow_cold_start_instance'],
      elastic_prefer_cluster: _req['elastic_prefer_cluster'],
      reserved_prefer_cluster: _req['reserved_prefer_cluster'],
      elastic_user_preferred_cluster: _req['elastic_user_preferred_cluster'],
      reserved_user_preferred_cluster: _req['reserved_user_preferred_cluster'],
      elastic_auto_preferred_cluster: _req['elastic_auto_preferred_cluster'],
      reserved_auto_preferred_cluster: _req['reserved_auto_preferred_cluster'],
      temp_preferred_cluster: _req['temp_preferred_cluster'],
      formatted_elastic_prefer_cluster:
        _req['formatted_elastic_prefer_cluster'],
      formatted_reserved_prefer_cluster:
        _req['formatted_reserved_prefer_cluster'],
      runtime_log_writers: _req['runtime_log_writers'],
      system_log_writers: _req['system_log_writers'],
      is_bytepaas_elastic_cluster: _req['is_bytepaas_elastic_cluster'],
      disable_service_discovery: _req['disable_service_discovery'],
      resource_guarantee: _req['resource_guarantee'],
      disable_cgroup_v2: _req['disable_cgroup_v2'],
      async_result_emit_event_bridge: _req['async_result_emit_event_bridge'],
      runtime_stream_log_bytes_per_sec:
        _req['runtime_stream_log_bytes_per_sec'],
      system_stream_log_bytes_per_sec: _req['system_stream_log_bytes_per_sec'],
      throttle_log_bytes_per_sec: _req['throttle_log_bytes_per_sec'],
      throttle_stdout_log_bytes_per_sec:
        _req['throttle_stdout_log_bytes_per_sec'],
      throttle_stderr_log_bytes_per_sec:
        _req['throttle_stderr_log_bytes_per_sec'],
      scale_enabled: _req['scale_enabled'],
      scale_threshold: _req['scale_threshold'],
      scale_type: _req['scale_type'],
      scale_settings: _req['scale_settings'],
      replica_limit: _req['replica_limit'],
      zone_reserved_frozen_replicas: _req['zone_reserved_frozen_replicas'],
      container_runtime: _req['container_runtime'],
      enable_scale_optimise: _req['enable_scale_optimise'],
      schedule_strategy: _req['schedule_strategy'],
      dynamic_overcommit_settings: _req['dynamic_overcommit_settings'],
      overload_protect_enabled: _req['overload_protect_enabled'],
      frozen_cpu_milli: _req['frozen_cpu_milli'],
      enable_fed_on_demand_resource: _req['enable_fed_on_demand_resource'],
      frozen_priority_class: _req['frozen_priority_class'],
      host_uniq: _req['host_uniq'],
      zone_canary_replica_limit: _req['zone_canary_replica_limit'],
      frozen_scale_enabled: _req['frozen_scale_enabled'],
      privileged: _req['privileged'],
      in_cell_migration: _req['in_cell_migration'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/filterplugins/:filter_plugin_id/download */
  downloadFilterPlugins(
    req: filterplugin.DownloadFilterPluginsRequest,
    options?: T,
  ): Promise<filterplugin.DownloadFilterPluginsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/filterplugins/${_req['filter_plugin_id']}/download`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/http_triggers/:trigger_id */
  getHttpTrigger(
    req: http_trigger.GetHttpTriggerRequest,
    options?: T,
  ): Promise<http_trigger.GetHttpTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/http_triggers/${_req['trigger_id']}`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /**
   * GET /v2/psm/:psm/regions/:region/clusters
   *
   * 获取PSM下集群列表
   */
  getClusterListByPsm(
    req: cluster.GetClusterListByPsmRequest,
    options?: T,
  ): Promise<cluster.GetClusterListByPsmResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/psm/${_req['psm']}/regions/${_req['region']}/clusters`,
    );
    const method = 'GET';
    const params = { env: _req['env'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /v2/services/:service_id
   *
   * 删除服务
   */
  deleteService(
    req: service.DeleteServiceRequest,
    options?: T,
  ): Promise<service.DeleteServiceResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/services/${_req['service_id']}`);
    const method = 'DELETE';
    const params = { soft: _req['soft'], reason: _req['reason'] };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /v2/function_templates/:template_name/code.zip */
  downloadTemplateByName(
    req: template.DownloadTemplateByNameRequest,
    options?: T,
  ): Promise<template.DownloadTemplateByNameResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/function_templates/${_req['template_name']}/code.zip`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /v2/tickets/runtime/update
   *
   * v1 upgrade to v2
   */
  ticketRuntimeUpdate(
    req?: ticket.TicketRuntimeUpdateRequest,
    options?: T,
  ): Promise<ticket.TicketRuntimeUpdateResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/tickets/runtime/update');
    const method = 'POST';
    const data = {
      approved_by: _req['approved_by'],
      approved_by_usertype: _req['approved_by_usertype'],
      function_id: _req['function_id'],
      function_meta: _req['function_meta'],
      regional_metas: _req['regional_metas'],
      service_id: _req['service_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /v2/services/:service_id/regions/:region/clusters
   *
   * 在指定服务下创建集群
   */
  createCluster(
    req: cluster.CreateClusterRequest,
    options?: T,
  ): Promise<cluster.CreateClusterResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters`,
    );
    const method = 'POST';
    const data = {
      async_mode: _req['async_mode'],
      auth_enable: _req['auth_enable'],
      cluster: _req['cluster'],
      code_revision_id: _req['code_revision_id'],
      code_revision_number: _req['code_revision_number'],
      cold_start_disabled: _req['cold_start_disabled'],
      cors_enable: _req['cors_enable'],
      enable_colocate_scheduling: _req['enable_colocate_scheduling'],
      enable_scale_strategy: _req['enable_scale_strategy'],
      exclusive_mode: _req['exclusive_mode'],
      format_envs: _req['format_envs'],
      gateway_route_enable: _req['gateway_route_enable'],
      gdpr_enable: _req['gdpr_enable'],
      global_kv_namespace_ids: _req['global_kv_namespace_ids'],
      http_trigger_disable: _req['http_trigger_disable'],
      initializer_sec: _req['initializer_sec'],
      is_ipv6_only: _req['is_ipv6_only'],
      is_this_zone_disabled: _req['is_this_zone_disabled'],
      latency_sec: _req['latency_sec'],
      max_concurrency: _req['max_concurrency'],
      network_mode: _req['network_mode'],
      reserved_dp_enabled: _req['reserved_dp_enabled'],
      revision_id: _req['revision_id'],
      revision_number: _req['revision_number'],
      routing_strategy: _req['routing_strategy'],
      scale_enabled: _req['scale_enabled'],
      scale_threshold: _req['scale_threshold'],
      scale_type: _req['scale_type'],
      trace_enable: _req['trace_enable'],
      zone_throttle_log_bytes_per_sec: _req['zone_throttle_log_bytes_per_sec'],
      zti_enable: _req['zti_enable'],
      online_mode: _req['online_mode'],
      enable_runtime_file_log: _req['enable_runtime_file_log'],
      enable_runtime_console_log: _req['enable_runtime_console_log'],
      enable_runtime_stream_log: _req['enable_runtime_stream_log'],
      enable_runtime_es_log: _req['enable_runtime_es_log'],
      enable_runtime_json_log: _req['enable_runtime_json_log'],
      enable_system_stream_log: _req['enable_system_stream_log'],
      enable_system_es_log: _req['enable_system_es_log'],
      runtime_stream_log_bytes_per_sec:
        _req['runtime_stream_log_bytes_per_sec'],
      system_stream_log_bytes_per_sec: _req['system_stream_log_bytes_per_sec'],
      resource_limit: _req['resource_limit'],
      pod_type: _req['pod_type'],
      enable_reserve_frozen_instance: _req['enable_reserve_frozen_instance'],
      cluster_run_cmd: _req['cluster_run_cmd'],
      disable_service_discovery: _req['disable_service_discovery'],
      async_result_emit_event_bridge: _req['async_result_emit_event_bridge'],
      resource_guarantee: _req['resource_guarantee'],
      mq_trigger_limit: _req['mq_trigger_limit'],
      cell: _req['cell'],
      lazyload: _req['lazyload'],
      image_lazyload: _req['image_lazyload'],
      initializer: _req['initializer'],
      handler: _req['handler'],
      run_cmd: _req['run_cmd'],
      throttle_log_enabled: _req['throttle_log_enabled'],
      adaptive_concurrency_mode: _req['adaptive_concurrency_mode'],
      env_name: _req['env_name'],
      container_runtime: _req['container_runtime'],
      protocol: _req['protocol'],
      overload_protect_enabled: _req['overload_protect_enabled'],
      mq_consumer_meta: _req['mq_consumer_meta'],
      enable_consul_ipv6_register: _req['enable_consul_ipv6_register'],
      enable_sys_mount: _req['enable_sys_mount'],
      disable_mount_jwt_bundles: _req['disable_mount_jwt_bundles'],
      termination_grace_period_seconds:
        _req['termination_grace_period_seconds'],
      enable_consul_register: _req['enable_consul_register'],
      host_uniq: _req['host_uniq'],
      service_level_degrade: _req['service_level_degrade'],
      in_cell_migration: _req['in_cell_migration'],
      zone_reserved_frozen_replicas: _req['zone_reserved_frozen_replicas'],
      frozen_cpu_milli: _req['frozen_cpu_milli'],
      enable_session_request: _req['enable_session_request'],
      privileged: _req['privileged'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /v2/services/:service_id/tickets
   *
   * 创建工单（包括发布 或者 触发器打开等）
   */
  createTicket(
    req: release.CreateTicketRequest,
    options?: T,
  ): Promise<common.CreateTicketResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/services/${_req['service_id']}/tickets`);
    const method = 'POST';
    const data = {
      approved_by: _req['approved_by'],
      approved_by_usertype: _req['approved_by_usertype'],
      cluster: _req['cluster'],
      code_revision_id: _req['code_revision_id'],
      description: _req['description'],
      region: _req['region'],
      release_type: _req['release_type'],
      replica_limit: _req['replica_limit'],
      revision_id: _req['revision_id'],
      rollback: _req['rollback'],
      rolling_step: _req['rolling_step'],
      use_latest_code_revision: _req['use_latest_code_revision'],
      grey_mqevent_config: _req['grey_mqevent_config'],
      code_source: _req['code_source'],
      mqevent_release_type: _req['mqevent_release_type'],
      is_pipeline_ticket: _req['is_pipeline_ticket'],
      pipeline_template_type: _req['pipeline_template_type'],
      pipeline_template_id: _req['pipeline_template_id'],
      rolling_strategy: _req['rolling_strategy'],
      rolling_interval: _req['rolling_interval'],
      min_created_percentage: _req['min_created_percentage'],
      min_ready_percentage: _req['min_ready_percentage'],
    };
    return this.request({ url, method, data }, options);
  }

  /** DELETE /v2/services/:service_id/regions/:region/clusters/:cluster/diagnosis/:diagnosis_id */
  deleteDiagnosisByID(
    req: diagnosis.DeleteDiagnosisByIDRequest,
    options?: T,
  ): Promise<diagnosis.DeleteDiagnosisByIDResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/diagnosis/${_req['diagnosis_id']}`,
    );
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/revisions/:revision_number */
  getFunctionRevision(
    req: revision.GetFunctionRevisionRequest,
    options?: T,
  ): Promise<revision.GetFunctionRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/revisions/${_req['revision_number']}`,
    );
    const method = 'GET';
    const params = { format: _req['format'] };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/zones/:zone/instances/:podname/logs */
  getInstancesLogs(
    req: instance.GetInstancesLogsRequest,
    options?: T,
  ): Promise<instance.GetInstancesLogsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/zones/${_req['zone']}/instances/${_req['podname']}/logs`,
    );
    const method = 'GET';
    const params = { revision_id: _req['revision_id'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /v2/function_templates/:template_name/upload */
  uploadTemplateByName(
    req: template.UploadTemplateByNameRequest,
    options?: T,
  ): Promise<template.UploadTemplateByNameResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/function_templates/${_req['template_name']}/upload`,
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/diagnosis */
  createDiagnosis(
    req: diagnosis.CreateDiagnosisRequest,
    options?: T,
  ): Promise<diagnosis.CreateDiagnosisResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/diagnosis`,
    );
    const method = 'POST';
    const data = {
      diagnosis_id: _req['diagnosis_id'],
      end_at: _req['end_at'],
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      set_time_range: _req['set_time_range'],
      start_at: _req['start_at'],
    };
    return this.request({ url, method, data }, options);
  }

  /** DELETE /v2/services/:service_id/regions/:region/clusters/:cluster/consul_triggers/:trigger_id */
  deleteConsulTrigger(
    req: consul_trigger.DeleteConsulTriggerRequest,
    options?: T,
  ): Promise<consul_trigger.DeleteConsulTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/consul_triggers/${_req['trigger_id']}`,
    );
    const method = 'DELETE';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/services */
  getServicesList(
    req?: service.GetServicesListRequest,
    options?: T,
  ): Promise<service.GetServicesListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/services');
    const method = 'GET';
    const params = {
      all: _req['all'],
      env: _req['env'],
      id: _req['id'],
      limit: _req['limit'],
      name: _req['name'],
      no_worker: _req['no_worker'],
      offset: _req['offset'],
      owner: _req['owner'],
      psm: _req['psm'],
      search: _req['search'],
      search_type: _req['search_type'],
      sort_by: _req['sort_by'],
      search_fields: _req['search_fields'],
      soft_deleted: _req['soft_deleted'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/latest_release */
  getLatestRelease(
    req: release.GetLatestReleaseRequest,
    options?: T,
  ): Promise<release.GetLatestReleaseResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/latest_release`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/regions_enabled */
  getRegionsEnabled(
    req?: other.getRegionsEnabledRequest,
    options?: T,
  ): Promise<other.GetRegionsEnabledResponse> {
    const url = this.genBaseURL('/v2/regions_enabled');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/timers/:timer_id */
  getTimerTrigger(
    req: timer.GetTimerTriggerRequest,
    options?: T,
  ): Promise<timer.GetTimerTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/timers/${_req['timer_id']}`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/http_triggers */
  getHttpTriggers(
    req: http_trigger.GetHttpTriggersRequest,
    options?: T,
  ): Promise<http_trigger.GetHttpTriggersResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/http_triggers`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/instances */
  getInstances(
    req: instance.GetInstancesRequest,
    options?: T,
  ): Promise<instance.GetInstancesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/instances`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/diagnosis */
  getDiagnosis(
    req: diagnosis.GetDiagnosisRequest,
    options?: T,
  ): Promise<diagnosis.GetDiagnosisResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/diagnosis`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/consul_triggers/:trigger_id */
  updateConsulTrigger(
    req: consul_trigger.UpdateConsulTriggerRequest,
    options?: T,
  ): Promise<consul_trigger.UpdateConsulTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/consul_triggers/${_req['trigger_id']}`,
    );
    const method = 'PATCH';
    const data = {
      description: _req['description'],
      enabled: _req['enabled'],
      name: _req['name'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** PUT /v2/services/:service_id/regions/:region/clusters/:cluster/http_triggers/:trigger_id */
  updateHttpTrigger(
    req: http_trigger.UpdateHttpTriggerRequest,
    options?: T,
  ): Promise<http_trigger.UpdateHttpTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/http_triggers/${_req['trigger_id']}`,
    );
    const method = 'PUT';
    const data = {
      bytefaas_error_response_disabled:
        _req['bytefaas_error_response_disabled'],
      bytefaas_response_header_disabled:
        _req['bytefaas_response_header_disabled'],
      description: _req['description'],
      enabled: _req['enabled'],
      name: _req['name'],
      url_prefix: _req['url_prefix'],
      version_type: _req['version_type'],
      version_value: _req['version_value'],
      runtime: _req['runtime'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /v2/services/:service_id/regions/:region/clusters/:cluster/revisions
   *
   * 创建函数版本
   */
  createRevision(
    req: revision.CreateRevisionRequest,
    options?: T,
  ): Promise<revision.CreateRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/revisions`,
    );
    const method = 'POST';
    const data = {
      code_revision_number: _req['code_revision_number'],
      dependency: _req['dependency'],
      deploy_method: _req['deploy_method'],
      description: _req['description'],
      disable_build_install: _req['disable_build_install'],
      envs: _req['envs'],
      format_envs: _req['format_envs'],
      handler: _req['handler'],
      initializer: _req['initializer'],
      lazyload: _req['lazyload'],
      name: _req['name'],
      network_mode: _req['network_mode'],
      run_cmd: _req['run_cmd'],
      runtime: _req['runtime'],
      runtime_container_port: _req['runtime_container_port'],
      runtime_debug_container_port: _req['runtime_debug_container_port'],
      source: _req['source'],
      source_type: _req['source_type'],
      open_image_lazyload: _req['open_image_lazyload'],
      runtime_other_container_ports: _req['runtime_other_container_ports'],
      host_uniq: _req['host_uniq'],
      in_cell_migration: _req['in_cell_migration'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** DELETE /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/timers/:timer_id */
  deleteTimerTrigger(
    req: timer.DeleteTimerTriggerRequest,
    options?: T,
  ): Promise<timer.DeleteTimerTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/timers/${_req['timer_id']}`,
    );
    const method = 'DELETE';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/zones/:zone/instances/:podname/migrate */
  migrateInstances(
    req: instance.MigrateInstancesRequest,
    options?: T,
  ): Promise<instance.MigrateInstancesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/zones/${_req['zone']}/instances/${_req['podname']}/migrate`,
    );
    const method = 'POST';
    const params = { env: _req['env'] };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * PUT /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/:trigger_type/:trigger_id
   *
   * 获取指定触发器信息
   */
  updateMqTriggerByType(
    req: mqevent.UpdateMqTriggerByTypeRequest,
    options?: T,
  ): Promise<mqevent.UpdateMqTriggerByTypeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/${_req['trigger_type']}/${_req['trigger_id']}`,
    );
    const method = 'PUT';
    const data = {
      batch_size: _req['batch_size'],
      batch_flush_duration_milliseconds:
        _req['batch_flush_duration_milliseconds'],
      description: _req['description'],
      enabled: _req['enabled'],
      envs: _req['envs'],
      function_id: _req['function_id'],
      cell: _req['cell'],
      id: _req['id'],
      image_version: _req['image_version'],
      sdk_version: _req['sdk_version'],
      image_alias: _req['image_alias'],
      ms_alarm_id: _req['ms_alarm_id'],
      mq_type: _req['mq_type'],
      max_retries_from_function_status:
        _req['max_retries_from_function_status'],
      msg_chan_length: _req['msg_chan_length'],
      name: _req['name'],
      need_auto_sharding: _req['need_auto_sharding'],
      num_of_mq_pod_to_one_func_pod: _req['num_of_mq_pod_to_one_func_pod'],
      options: _req['options'],
      qps_limit: _req['qps_limit'],
      region: _req['region'],
      mq_region: _req['mq_region'],
      runtime_agent_mode: _req['runtime_agent_mode'],
      dynamic_worker_thread: _req['dynamic_worker_thread'],
      replica_max_limit: _req['replica_max_limit'],
      replica_min_limit: _req['replica_min_limit'],
      replicas: _req['replicas'],
      resource: _req['resource'],
      scale_enabled: _req['scale_enabled'],
      vertical_scale_enabled: _req['vertical_scale_enabled'],
      enable_static_membership: _req['enable_static_membership'],
      workers_per_pod: _req['workers_per_pod'],
      alarm_params: _req['alarm_params'],
      request_timeout: _req['request_timeout'],
      disable_infinite_retry_for_timeout:
        _req['disable_infinite_retry_for_timeout'],
      initial_offset_start_from: _req['initial_offset_start_from'],
      enable_mq_debug: _req['enable_mq_debug'],
      mq_logger_limit_size: _req['mq_logger_limit_size'],
      enable_backoff: _req['enable_backoff'],
      disable_backoff: _req['disable_backoff'],
      worker_v2_num_per_half_core: _req['worker_v2_num_per_half_core'],
      enable_concurrency_filter: _req['enable_concurrency_filter'],
      enable_ipc_mode: _req['enable_ipc_mode'],
      enable_traffic_priority_scheduling:
        _req['enable_traffic_priority_scheduling'],
      enable_pod_colocate_scheduling: _req['enable_pod_colocate_scheduling'],
      enable_global_rate_limiter: _req['enable_global_rate_limiter'],
      enable_congestion_control: _req['enable_congestion_control'],
      allow_bytesuite_debug: _req['allow_bytesuite_debug'],
      enable_dynamic_load_balance: _req['enable_dynamic_load_balance'],
      disable_smooth_wrr: _req['disable_smooth_wrr'],
      dynamic_load_balance_type: _req['dynamic_load_balance_type'],
      replica_force_meet_partition: _req['replica_force_meet_partition'],
      scale_settings: _req['scale_settings'],
      hot_reload: _req['hot_reload'],
      mq_msg_type: _req['mq_msg_type'],
      status: _req['status'],
      in_releasing: _req['in_releasing'],
      mirror_region_filter: _req['mirror_region_filter'],
      enable_gctuner: _req['enable_gctuner'],
      gctuner_percent: _req['gctuner_percent'],
      retry_strategy: _req['retry_strategy'],
      max_retry_time: _req['max_retry_time'],
      qps_limit_time_ranges: _req['qps_limit_time_ranges'],
      limit_disaster_scenario: _req['limit_disaster_scenario'],
      enable_step_rate_limit: _req['enable_step_rate_limit'],
      rate_limit_step_settings: _req['rate_limit_step_settings'],
      max_dwell_time_minute: _req['max_dwell_time_minute'],
      qps_auto_limit: _req['qps_auto_limit'],
      plugin_function_param: _req['plugin_function_param'],
      enable_plugin_function: _req['enable_plugin_function'],
      enable_canary_update: _req['enable_canary_update'],
      traffic_config: _req['traffic_config'],
      is_auth_info_updated: _req['is_auth_info_updated'],
      pod_type: _req['pod_type'],
      package: _req['package'],
      enable_filter_congestion_control:
        _req['enable_filter_congestion_control'],
      enable_congestion_control_cache: _req['enable_congestion_control_cache'],
      image_version_number: _req['image_version_number'],
      host_uniq: _req['host_uniq'],
      in_cell_migration: _req['in_cell_migration'],
      mq_canary_update_params: _req['mq_canary_update_params'],
      pipeline_params: _req['pipeline_params'],
    };
    const params = {
      hot_reload: _req['hot_reload'],
      skip_image_upgrade: _req['skip_image_upgrade'],
      caller: _req['caller'],
      not_update_alarm: _req['not_update_alarm'],
      migrated_by_cli: _req['migrated_by_cli'],
      check: _req['check'],
      confirm: _req['confirm'],
    };
    const headers = {
      'X-Bytefaas-Mqevent-Force-Update':
        _req['X-Bytefaas-Mqevent-Force-Update'],
      'X-ByteFaas-Update-MQ-Image': _req['X-ByteFaas-Update-MQ-Image'],
      'X-Jwt-Token': _req['X-Jwt-Token'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/http_triggers */
  createHttpTrigger(
    req: http_trigger.CreateHttpTriggerRequest,
    options?: T,
  ): Promise<http_trigger.CreateHttpTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/http_triggers`,
    );
    const method = 'POST';
    const data = {
      bytefaas_error_response_disabled:
        _req['bytefaas_error_response_disabled'],
      bytefaas_response_header_disabled:
        _req['bytefaas_response_header_disabled'],
      description: _req['description'],
      enabled: _req['enabled'],
      name: _req['name'],
      url_prefix: _req['url_prefix'],
      version_type: _req['version_type'],
      version_value: _req['version_value'],
      runtime: _req['runtime'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** DELETE /v2/services/:service_id/regions/:region/clusters/:cluster/filterplugins/:filter_plugin_id */
  deleteFilterPlugins(
    req: filterplugin.DeleteFilterPluginsRequest,
    options?: T,
  ): Promise<filterplugin.DeleteFilterPluginsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/filterplugins/${_req['filter_plugin_id']}`,
    );
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/logs/:log_type */
  getLogs(
    req: instance.GetLogsRequest,
    options?: T,
  ): Promise<instance.GetLogsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/logs/${_req['log_type']}`,
    );
    const method = 'GET';
    const params = {
      advanced: _req['advanced'],
      ascend: _req['ascend'],
      from: _req['from'],
      include_system: _req['include_system'],
      pod_ip: _req['pod_ip'],
      pod_name: _req['pod_name'],
      revision_id: _req['revision_id'],
      search: _req['search'],
      size: _req['size'],
      to: _req['to'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /v2/services/:service_id/recover */
  recoverDeletedCluster(
    req: service.RecoverDeletedClusterRequest,
    options?: T,
  ): Promise<service.RecoverDeletedClusterResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/services/${_req['service_id']}/recover`);
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** PUT /v2/services/:service_id/code */
  updateCodeByServiceID(
    req: service.UpdateCodeByServiceIDRequest,
    options?: T,
  ): Promise<service.UpdateCodeByServiceIDResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/services/${_req['service_id']}/code`);
    const method = 'PUT';
    const data = {
      dependency: _req['dependency'],
      deploy_method: _req['deploy_method'],
      disable_build_install: _req['disable_build_install'],
      handler: _req['handler'],
      initializer: _req['initializer'],
      lazyload: _req['lazyload'],
      run_cmd: _req['run_cmd'],
      runtime: _req['runtime'],
      runtime_container_port: _req['runtime_container_port'],
      runtime_debug_container_port: _req['runtime_debug_container_port'],
      source: _req['source'],
      source_type: _req['source_type'],
      zip_file: _req['zip_file'],
      zip_file_size: _req['zip_file_size'],
      open_image_lazyload: _req['open_image_lazyload'],
      runtime_other_container_ports: _req['runtime_other_container_ports'],
      health_check_failure_threshold: _req['health_check_failure_threshold'],
      health_check_period: _req['health_check_period'],
      health_check_path: _req['health_check_path'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/filterplugins/:filter_plugin_id */
  updateFilterPlugins(
    req: filterplugin.UpdateFilterPluginsRequest,
    options?: T,
  ): Promise<filterplugin.UpdateFilterPluginsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/filterplugins/${_req['filter_plugin_id']}`,
    );
    const method = 'PATCH';
    const data = {
      name: _req['name'],
      zip_file: _req['zip_file'],
      zip_file_size: _req['zip_file_size'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters */
  getClustersList(
    req: cluster.GetClustersListRequest,
    options?: T,
  ): Promise<cluster.GetClustersListResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters`,
    );
    const method = 'GET';
    const params = { verbose: _req['verbose'] };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /v2/regions/:region/zone/:zone/prescan/:hours */
  prescan(
    req: other.PrescanRequest,
    options?: T,
  ): Promise<other.PrescanResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/regions/${_req['region']}/zone/${_req['zone']}/prescan/:hours`,
    );
    const method = 'POST';
    const data = { hours: _req['hours'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/async_requests */
  listAsyncRequests(
    req: async_request.ListAsyncRequestsRequest,
    options?: T,
  ): Promise<async_request.ListAsyncRequestsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/async_requests`,
    );
    const method = 'GET';
    const params = {
      begin_time: _req['begin_time'],
      end_time: _req['end_time'],
      limit: _req['limit'],
      offset: _req['offset'],
      request_id: _req['request_id'],
      task_status: _req['task_status'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster_name/mq_permission */
  mqPermission(
    req: trigger.MqPermissionRequest,
    options?: T,
  ): Promise<trigger.MqPermissionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster_name']}/mq_permission`,
    );
    const method = 'POST';
    const data = {
      cluster: _req['cluster'],
      mq_region: _req['mq_region'],
      topic: _req['topic'],
      type: _req['type'],
      auth_type: _req['auth_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /v2/services/:service_id/subscription
   *
   * unsubscribe a single function service
   */
  unsubscribeService(
    req: subscription.UnsubscribeServiceRequest,
    options?: T,
  ): Promise<subscription.UnsubscribeServiceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/subscription`,
    );
    const method = 'DELETE';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/build_latest */
  buildLatestRevision(
    req: build.BuildLatestRevisionRequest,
    options?: T,
  ): Promise<build.BuildLatestRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/build_latest`,
    );
    const method = 'POST';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/revisions */
  getClusterRevisions(
    req: revision.GetClusterRevisionsRequest,
    options?: T,
  ): Promise<revision.GetClusterRevisionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/revisions`,
    );
    const method = 'GET';
    const params = {
      description: _req['description'],
      format: _req['format'],
      limit: _req['limit'],
      offset: _req['offset'],
      with_status: _req['with_status'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/scale_strategies */
  getScaleStrategies(
    req: scale_strategies.GetScaleStrategiesRequest,
    options?: T,
  ): Promise<scale_strategies.GetScaleStrategiesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/scale_strategies`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/alarms */
  updateClusterAlarm(
    req: alarm.UpdateClusterAlarmRequest,
    options?: T,
  ): Promise<alarm.UpdateClusterAlarmResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/alarms`,
    );
    const method = 'PATCH';
    const data = {
      alarm_id: _req['alarm_id'],
      alarm_methods: _req['alarm_methods'],
      function_id: _req['function_id'],
      level: _req['level'],
      rule_alias: _req['rule_alias'],
      rule_format: _req['rule_format'],
      status: _req['status'],
      threshold: _req['threshold'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/:trigger_type/:trigger_id/sync */
  syncMqTriggerData(
    req: mqevent.SyncMqTriggerDataRequest,
    options?: T,
  ): Promise<mqevent.SyncMqTriggerDataResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/${_req['trigger_type']}/${_req['trigger_id']}/sync`,
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/scale_strategies */
  createScaleStrategy(
    req: scale_strategies.CreateScaleStrategyRequest,
    options?: T,
  ): Promise<scale_strategies.CreateScaleStrategyResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/:region/clusters/${_req['cluster']}/scale_strategies`,
    );
    const method = 'POST';
    const data = {
      effective_time: _req['effective_time'],
      enabled: _req['enabled'],
      expired_time: _req['expired_time'],
      function_id: _req['function_id'],
      inner_strategy: _req['inner_strategy'],
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      region: _req['region'],
      strategy_id: _req['strategy_id'],
      strategy_name: _req['strategy_name'],
      strategy_type: _req['strategy_type'],
      instance_type: _req['instance_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/scale_strategies/:strategy_id */
  getScaleStrategy(
    req: scale_strategies.GetScaleStrategyRequest,
    options?: T,
  ): Promise<scale_strategies.GetScaleStrategyResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/scale_strategies/${_req['strategy_id']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/psm/:psm/env/:env_name */
  getServiceByPsmAndEnv(
    req: service.GetServiceByPsmAndEnvRequest,
    options?: T,
  ): Promise<service.GetServiceByPsmAndEnvResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/psm/${_req['psm']}/env/${_req['env_name']}`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/plugin_function_revisions/:id/release */
  createPluginFunctionRelease(
    req: plugin_function.CreatePluginFunctionReleaseRequest,
    options?: T,
  ): Promise<plugin_function.CreatePluginFunctionReleaseResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/plugin_function_revisions/${_req['id']}/release`,
    );
    const method = 'POST';
    const data = { mqevent_ids: _req['mqevent_ids'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/plugin_function_revisions/:id */
  getPluginFunctionRevisionDetail(
    req: plugin_function.GetPluginFunctionRevisionDetailRequest,
    options?: T,
  ): Promise<plugin_function.GetPluginFunctionRevisionDetailResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/plugin_function_revisions/${_req['id']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** DELETE /v2/services/:service_id/regions/:region/clusters/:cluster/plugin_function_revisions/:id */
  deletePluginFunctionRevision(
    req: plugin_function.DeletePluginFunctionRevisionRequest,
    options?: T,
  ): Promise<plugin_function.DeletePluginFunctionRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/plugin_function_revisions/${_req['id']}`,
    );
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/plugin_function_revisions */
  createPluginFunctionRevision(
    req: plugin_function.CreatePluginFunctionRevisionRequest,
    options?: T,
  ): Promise<plugin_function.CreatePluginFunctionRevisionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/plugin_function_revisions`,
    );
    const method = 'POST';
    const data = {
      description: _req['description'],
      environments: _req['environments'],
      init_timeout: _req['init_timeout'],
      plugin_name: _req['plugin_name'],
      plugin_version: _req['plugin_version'],
      request_timeout: _req['request_timeout'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/plugin_functions */
  getPluginFunctions(
    req: plugin_function.GetPluginFunctionsRequest,
    options?: T,
  ): Promise<plugin_function.GetPluginFunctionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/plugin_functions`,
    );
    const method = 'GET';
    const params = { limit: _req['limit'], offset: _req['offset'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/plugin_function_revisions */
  getPluginFunctionRevisions(
    req: plugin_function.GetPluginFunctionRevisionsRequest,
    options?: T,
  ): Promise<plugin_function.GetPluginFunctionRevisionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/plugin_function_revisions`,
    );
    const method = 'GET';
    const params = { limit: _req['limit'], offset: _req['offset'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /v2/services/:service_id/regions/:region/clusters/:cluster/mqtriggers
   *
   * 获取服务下所有MQ触发器信息
   */
  getMQTrigger(
    req: mqevent.GetMQTriggerRequest,
    options?: T,
  ): Promise<mqevent.GetMQTriggerResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/mqtriggers`,
    );
    const method = 'GET';
    const params = {
      enable_plugin_function: _req['enable_plugin_function'],
      plugin_function_version: _req['plugin_function_version'],
    };
    return this.request({ url, method, params }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/release/:release_id */
  patchRelease(
    req: release.PatchReleaseRequest,
    options?: T,
  ): Promise<release.PatchReleaseResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/release/${_req['release_id']}`,
    );
    const method = 'PATCH';
    const data = {
      action: _req['action'],
      alias_name: _req['alias_name'],
      format_target_traffic_config: _req['format_target_traffic_config'],
      format_zone_traffic_config: _req['format_zone_traffic_config'],
      rolling_step: _req['rolling_step'],
      target_traffic_config: _req['target_traffic_config'],
      zone_traffic_config: _req['zone_traffic_config'],
      rolling_strategy: _req['rolling_strategy'],
      rolling_interval: _req['rolling_interval'],
      min_created_percentage: _req['min_created_percentage'],
      min_ready_percentage: _req['min_ready_percentage'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /v2/services/:service_id/regions/:region/clusters/:cluster/release/:release_id
   *
   * 获取发布工单信息
   */
  getReleaseByID(
    req: release.GetReleaseByIDRequest,
    options?: T,
  ): Promise<release.GetReleaseByIDResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/release/${_req['release_id']}`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /**
   * GET /v2/services/:service_id/regions/:region/clusters/:cluster/release
   *
   * 获取服务下发布工单列表
   */
  getRelease(
    req: release.GetReleaseRequest,
    options?: T,
  ): Promise<release.GetReleaseResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/release`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /v2/services/:service_id/regions/:region/clusters/:cluster/release
   *
   * 创建发布工单
   */
  createRelease(
    req: release.CreateReleaseRequest,
    options?: T,
  ): Promise<release.CreateReleaseResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/release`,
    );
    const method = 'POST';
    const data = {
      alias_name: _req['alias_name'],
      rolling_step: _req['rolling_step'],
      target_traffic_config: _req['target_traffic_config'],
      zone_traffic_config: _req['zone_traffic_config'],
      rolling_strategy: _req['rolling_strategy'],
      rolling_interval: _req['rolling_interval'],
      min_created_percentage: _req['min_created_percentage'],
      min_ready_percentage: _req['min_ready_percentage'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/auto_mesh */
  getClusterAutoMesh(
    req: cluster.GetClusterAutoMeshRequest,
    options?: T,
  ): Promise<cluster.GetClusterAutoMeshResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/auto_mesh`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/plugin_functions/:plugin_name/plugin_versions */
  getPluginVersions(
    req: plugin_function.GetPluginVersionsRequest,
    options?: T,
  ): Promise<plugin_function.GetPluginVersionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/plugin_functions/${_req['plugin_name']}/plugin_versions`,
    );
    const method = 'GET';
    const params = { limit: _req['limit'], offset: _req['offset'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/auto_mesh
   *
   * update cluster in auto mesh
   */
  updateClusterAutoMesh(
    req: cluster.UpdateClusterAutoMeshRequest,
    options?: T,
  ): Promise<cluster.UpdateClusterAutoMeshResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/auto_mesh`,
    );
    const method = 'PATCH';
    const data = {
      mesh_enable: _req['mesh_enable'],
      mesh_http_egress: _req['mesh_http_egress'],
      mesh_mongo_egress: _req['mesh_mongo_egress'],
      mesh_mysql_egress: _req['mesh_mysql_egress'],
      mesh_rpc_egress: _req['mesh_rpc_egress'],
      mesh_sidecar_percent: _req['mesh_sidecar_percent'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/clusters */
  getClustersListWithPagination(
    req: cluster.GetClustersListWithPaginationRequest,
    options?: T,
  ): Promise<cluster.GetClustersListWithPaginationResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/services/${_req['service_id']}/clusters`);
    const method = 'GET';
    const params = {
      cluster: _req['cluster'],
      limit: _req['limit'],
      offset: _req['offset'],
      region: _req['region'],
      resource_list: _req['resource_list'],
      search: _req['search'],
      verbose: _req['verbose'],
      soft_deleted: _req['soft_deleted'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /v2/services/psm/:psm
   *
   * 获取PSM下所有服务信息
   */
  getAllServiceByPsm(
    req: service.GetAllServiceByPsmRequest,
    options?: T,
  ): Promise<service.GetAllServiceByPsmResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/services/psm/${_req['psm']}`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/scm/search */
  searchFunctionsBySCM(
    req: service.SearchFunctionsBySCMRequest,
    options?: T,
  ): Promise<service.SearchFunctionsBySCMResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/services/scm/search');
    const method = 'GET';
    const params = {
      limit: _req['limit'],
      offset: _req['offset'],
      scm: _req['scm'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /v2/services/:service_id/tickets/:ticket_id
   *
   * 获取工单信息
   */
  getServiceTicketByID(
    req: release.GetServiceTicketByIDRequest,
    options?: T,
  ): Promise<release.GetServiceTicketByIDResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/tickets/${_req['ticket_id']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/mqtriggers */
  getMQTriggersListWithPagination(
    req: mqevent.GetMQTriggersListWithPaginationRequest,
    options?: T,
  ): Promise<mqevent.GetMQTriggersListWithPaginationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/mqtriggers`,
    );
    const method = 'GET';
    const params = {
      cluster: _req['cluster'],
      limit: _req['limit'],
      offset: _req['offset'],
      region: _req['region'],
      search: _req['search'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/batch_tickets/:id */
  getBatchTicketDetailByID(
    req: ticket.GetBatchTicketDetailByIDRequest,
    options?: T,
  ): Promise<ticket.GetBatchTicketDetailByIDResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/batch_tickets/${_req['id']}`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/mqtriggers/:trigger_id/zones/:zone/instances/:podname/webshell */
  getMqTriggerInstancesWebshell(
    req: instance.GetMqTriggerInstancesWebshellRequest,
    options?: T,
  ): Promise<instance.GetMqTriggerInstancesWebshellResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/mqtriggers/${_req['trigger_id']}/zones/${_req['zone']}/instances/${_req['podname']}/webshell`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/mqtriggers/:trigger_id/instances */
  getMqTriggerInstances(
    req: instance.GetMqTriggerInstancesRequest,
    options?: T,
  ): Promise<instance.GetMqTriggerInstancesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/mqtriggers/${_req['trigger_id']}/instances`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/mqtriggers/:trigger_id/zones/:zone/instances/:podname/migrate */
  migrateMqTriggerInstance(
    req: instance.MigrateMqTriggerInstanceRequest,
    options?: T,
  ): Promise<instance.MigrateMqTriggerInstanceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/mqtriggers/${_req['trigger_id']}/zones/${_req['zone']}/instances/${_req['podname']}/migrate`,
    );
    const method = 'POST';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/resource/services/:service_id/regions/:region/clusters/:cluster/threshold */
  getReservedReplicaThreshold(
    req: resource.GetReservedReplicaThresholdRequest,
    options?: T,
  ): Promise<resource.GetReservedReplicaThresholdResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/resource/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/threshold`,
    );
    const method = 'GET';
    const params = {
      duration_minutes: _req['duration_minutes'],
      hours: _req['hours'],
      minutes: _req['minutes'],
    };
    return this.request({ url, method, params }, options);
  }

  /** PATCH /v2/admin/triggers/rollback */
  adminRollback(
    req?: admin.AdminRollbackRequest,
    options?: T,
  ): Promise<admin.AdminRollbackResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/admin/triggers/rollback');
    const method = 'PATCH';
    const data = { targets: _req['targets'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/admin/notifications/templates */
  getMQTriggerTemplate(
    req?: admin.getMQTriggerTemplateRequest,
    options?: T,
  ): Promise<admin.GetMQTriggerTemplateResponse> {
    const url = this.genBaseURL('/v2/admin/notifications/templates');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /v2/admin/notifications/groups
   *
   * If no message content is provided, it will use a default MQ trigger announcement template
   */
  sendNotificationsToLarkBotGroups(
    req?: admin.SendNotificationsToLarkBotGroupsRequest,
    options?: T,
  ): Promise<admin.SendNotificationsToLarkBotGroupsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/admin/notifications/groups');
    const method = 'POST';
    const data = {
      content: _req['content'],
      receiver_ids: _req['receiver_ids'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/admin/notifications/groups */
  getLarkBotChatGroups(
    req?: admin.getLarkBotChatGroupsRequest,
    options?: T,
  ): Promise<admin.GetLarkBotChatGroupsResponse> {
    const url = this.genBaseURL('/v2/admin/notifications/groups');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/:trigger_type/:trigger_id/rollback */
  rollback(
    req: trigger.RollbackRequest,
    options?: T,
  ): Promise<trigger.RollbackResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/${_req['trigger_type']}/${_req['trigger_id']}/rollback`,
    );
    const method = 'PATCH';
    const data = { targets: _req['targets'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/admin/tickets */
  adminListTickets(
    req: admin.GetTicketsByFilterRequest,
    options?: T,
  ): Promise<admin.GetTicketsByFilterResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/admin/tickets');
    const method = 'GET';
    const params = {
      category: _req['category'],
      change_type: _req['change_type'],
      cluster: _req['cluster'],
      function_id: _req['function_id'],
      id: _req['id'],
      max_create_time: _req['max_create_time'],
      min_create_time: _req['min_create_time'],
      only_admin_ticket: _req['only_admin_ticket'],
      parent_id: _req['parent_id'],
      region: _req['region'],
      status: _req['status'],
      trigger_id: _req['trigger_id'],
      trigger_type: _req['trigger_type'],
      type: _req['type'],
      limit: _req['limit'],
      offset: _req['offset'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/mqtrigger_instances */
  getClusterAllMqTriggerInstances(
    req: instance.GetClusterAllMqTriggerInstancesRequest,
    options?: T,
  ): Promise<instance.GetClusterAllMqTriggerInstancesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/mqtrigger_instances`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /**
   * POST /v2/tickets/:ticket_id/steps/:step_id/actions
   *
   * new api for pipeline type ticket
   */
  updateTicketStepAction(
    req: ticket.UpdateTicketStepActionRequest,
    options?: T,
  ): Promise<ticket.UpdateTicketStepActionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/tickets/${_req['ticket_id']}/steps/${_req['step_id']}/actions`,
    );
    const method = 'POST';
    const data = { action: _req['action'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/resource/services/realtime */
  getRealtimeResourceUsage(
    req?: resource.GetRealtimeResourceUsageRequest,
    options?: T,
  ): Promise<resource.GetRealtimeResourceUsageResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/resource/services/realtime');
    const method = 'GET';
    const params = {
      all_region: _req['all_region'],
      env: _req['env'],
      psm: _req['psm'],
      region: _req['region'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /v2/packages */
  getPackageList(
    req: packages.GetPackageListRequest,
    options?: T,
  ): Promise<packages.GetPackageListResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/packages');
    const method = 'POST';
    const params = { region: _req['region'] };
    return this.request({ url, method, params }, options);
  }

  /** PATCH /v2/admin/batch_tickets/:parent_id/tickets/:id */
  skipCheckForBatchTask(
    req: admin.SkipCheckForBatchTaskRequest,
    options?: T,
  ): Promise<admin.SkipCheckForBatchTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/admin/batch_tickets/${_req['parent_id']}/tickets/${_req['id']}`,
    );
    const method = 'PATCH';
    return this.request({ url, method }, options);
  }

  /** GET /v2/pipeline/templates */
  listPipelineTemplates(
    req?: tickets.ListPipelineTemplatesRequest,
    options?: T,
  ): Promise<tickets.ListPipelineTemplatesResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/pipeline/templates');
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/pipeline/templates/:template_type */
  queryPipelineTemplateByType(
    req: tickets.QueryPipelineTemplateByTypeRequest,
    options?: T,
  ): Promise<tickets.QueryPipelineTemplateByTypeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/pipeline/templates/${_req['template_type']}`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** GET /v2/function_resource_packages */
  getFunctionResourcePackages(
    req?: packages.GetFunctionResourcePackagesRequest,
    options?: T,
  ): Promise<packages.GetFunctionResourcePackagesResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/function_resource_packages');
    const method = 'GET';
    const params = {
      is_plugin_function: _req['is_plugin_function'],
      is_worker: _req['is_worker'],
      runtime: _req['runtime'],
      region: _req['region'],
      is_mq_event_trigger: _req['is_mq_event_trigger'],
      category: _req['category'],
      psm: _req['psm'],
      psm_parent_id: _req['psm_parent_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/release/:release_id/start_info */
  getReleaseStartLogByID(
    req: release.GetReleaseStartInfoByIDRequest,
    options?: T,
  ): Promise<release.GetReleaseStartInfoByIDResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/release/${_req['release_id']}/start_info`,
    );
    const method = 'GET';
    const params = { revision_id: _req['revision_id'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/services/psm/:psm/env/:env_name/regions/:region/clusters/:cluster/released */
  queryReleaseClusterByPsm(
    req?: cluster.QueryReleasedClusterRequest,
    options?: T,
  ): Promise<cluster.QueryReleasedClusterResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/v2/services/psm/${_req['psm']}/env/${_req['env_name']}/regions/${_req['region']}/clusters/${_req['cluster']}/released`,
    );
    const method = 'GET';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/revisions/:revision_number/abort_build */
  abortBuild(
    req: build.AbortBuildRequest,
    options?: T,
  ): Promise<build.AbortBuildResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/revisions/${_req['revision_number']}/abort_build`,
    );
    const method = 'POST';
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, headers }, options);
  }

  /** POST /v2/services/:service_id/tickets/release_clusters */
  releaseMultiClusters(
    req?: release.MultiCusterReleaseTicketRequest,
    options?: T,
  ): Promise<common.CreateTicketResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/tickets/release_clusters`,
    );
    const method = 'POST';
    const data = {
      approved_by: _req['approved_by'],
      approved_by_usertype: _req['approved_by_usertype'],
      code_revision_id: _req['code_revision_id'],
      description: _req['description'],
      rollback: _req['rollback'],
      use_latest_code_revision: _req['use_latest_code_revision'],
      code_source: _req['code_source'],
      mqevent_release_type: _req['mqevent_release_type'],
      pipeline_template_type: _req['pipeline_template_type'],
      clusters: _req['clusters'],
      rollback_revisions: _req['rollback_revisions'],
      pipeline_template_id: _req['pipeline_template_id'],
      insert_quality_check_stages: _req['insert_quality_check_stages'],
      dry_run: _req['dry_run'],
    };
    const headers = {
      'x-bytefaas-as-user': _req['x-bytefaas-as-user'],
      'X-Bytefaas-Globalcp-Ticket': _req['X-Bytefaas-Globalcp-Ticket'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /v2/services/:service_id/tickets/:ticket_id/steps/actions */
  batchUpdateTicketStepAction(
    req: ticket.BatchUpdateTicketStepActionRequest,
    options?: T,
  ): Promise<ticket.UpdateTicketStepActionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/tickets/${_req['ticket_id']}/steps/actions`,
    );
    const method = 'POST';
    const data = { action: _req['action'], step_ids: _req['step_ids'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/migration_records */
  getMigrationRecords(
    req: instance.MigrationRecordsRequest,
    options?: T,
  ): Promise<instance.MigrationRecordsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/migration_records`,
    );
    const method = 'GET';
    const params = {
      region: _req['region'],
      cluster: _req['cluster'],
      delete_by: _req['delete_by'],
      pod_name: _req['pod_name'],
      page_size: _req['page_size'],
      page_num: _req['page_num'],
      start: _req['start'],
      end: _req['end'],
      detector: _req['detector'],
      zone: _req['zone'],
      ip: _req['ip'],
      pod_type: _req['pod_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/resource/services/:service_id/regions/:region/clusters/:cluster/:trigger_type/:trigger_id/threshold */
  getTriggerReservedReplicaThreshold(
    req: resource.GetTriggerReservedReplicaThresholdRequest,
    options?: T,
  ): Promise<resource.GetTriggerReservedReplicaThresholdResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/resource/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/${_req['trigger_type']}/${_req['trigger_id']}/threshold`,
    );
    const method = 'GET';
    const params = {
      duration_minutes: _req['duration_minutes'],
      hours: _req['hours'],
      minutes: _req['minutes'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/autoscale/functions/scale_threshold/setting */
  getFunctionScaleThresholdsSetting(
    req: scale_setting.GetFunctionScaleThresholdsSettingRequest,
    options?: T,
  ): Promise<scale_setting.FuncScaleSettingApiResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/autoscale/functions/scale_threshold/setting`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/autoscale/functions/scale_threshold/setting */
  updateFunctionScaleThresholds(
    req: scale_setting.UpdateScaleThresholdSetRequest,
    options?: T,
  ): Promise<scale_setting.FuncScaleSettingApiResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/autoscale/functions/scale_threshold/setting`,
    );
    const method = 'PATCH';
    const data = {
      scale_set_name: _req['scale_set_name'],
      overload_fast_scale_enabled: _req['overload_fast_scale_enabled'],
      lag_scale_set_name: _req['lag_scale_set_name'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/autoscale/functions/scale_threshold/settings */
  listFunctionScaleThresholdsSettings(
    req: scale_setting.ListFuncScaleSettingApiRequest,
    options?: T,
  ): Promise<scale_setting.ListFuncScaleSettingApiResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/autoscale/functions/scale_threshold/settings`,
    );
    const method = 'GET';
    const params = {
      region: _req['region'],
      cluster: _req['cluster'],
      offset: _req['offset'],
      limit: _req['limit'],
    };
    return this.request({ url, method, params }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/:trigger_type/:trigger_id/restricted_meta */
  patchMqTriggerRestrictedMetaByType(
    req: mqevent.PatchMqTriggerRestrictedMetaByTypeRequest,
    options?: T,
  ): Promise<mqevent.PatchMqTriggerByTypeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/${_req['trigger_type']}/${_req['trigger_id']}/restricted_meta`,
    );
    const method = 'PATCH';
    const data = { cluster: _req['cluster'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/autoscale/functions/emergency_scale */
  createEmergencyScaleStrategy(
    req: scale_setting.EmergencyScaleRequest,
    options?: T,
  ): Promise<scale_setting.EmergencyScaleResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/autoscale/functions/emergency_scale`,
    );
    const method = 'POST';
    const data = {
      min_replicas: _req['min_replicas'],
      scale_duration_minutes: _req['scale_duration_minutes'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/admin/release_overview */
  getReleaseOverview(
    req?: admin.GetReleaseOverviewRequest,
    options?: T,
  ): Promise<admin.GetReleaseOverviewResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/admin/release_overview');
    const method = 'GET';
    const params = {
      start_time: _req['start_time'],
      end_time: _req['end_time'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/autoscale/mqtriggers/:trigger_id/scale_threshold/setting */
  getMQTriggerScaleThresholdsSetting(
    req: scale_setting.GetMQTriggerScaleThresholdSetRequest,
    options?: T,
  ): Promise<scale_setting.GetMQTriggerScaleThresholdSetResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/autoscale/mqtriggers/${_req['trigger_id']}/scale_threshold/setting`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/autoscale/mqtriggers/:trigger_id/emergency_scale */
  createMQTriggerEmergencyScaleStrategy(
    req: scale_setting.MQTriggerEmergencyScaleRequest,
    options?: T,
  ): Promise<scale_setting.MQTriggerEmergencyScaleResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/autoscale/mqtriggers/${_req['trigger_id']}/emergency_scale`,
    );
    const method = 'POST';
    const data = {
      min_replicas: _req['min_replicas'],
      scale_duration_minutes: _req['scale_duration_minutes'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/autoscale/mqtriggers/:trigger_id/scale_threshold/setting */
  patchMQTriggerScaleThresholdsSetting(
    req: scale_setting.PatchMQTriggerScaleThresholdSetRequest,
    options?: T,
  ): Promise<scale_setting.PatchMQTriggerScaleThresholdSetResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/autoscale/mqtriggers/${_req['trigger_id']}/scale_threshold/setting`,
    );
    const method = 'PATCH';
    const data = {
      scale_set_name: _req['scale_set_name'],
      lag_scale_set_name: _req['lag_scale_set_name'],
      vertical_scale_enabled: _req['vertical_scale_enabled'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/autoscale/mqtriggers/scale_threshold/settings */
  listMQTriggerScaleThresholdsSetting(
    req: scale_setting.ListMQTriggerScaleThresholdsSettingRequest,
    options?: T,
  ): Promise<scale_setting.ListMQTriggerScaleThresholdsSettingResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/autoscale/mqtriggers/scale_threshold/settings`,
    );
    const method = 'GET';
    const params = {
      offset: _req['offset'],
      limit: _req['limit'],
      search: _req['search'],
      region: _req['region'],
      cluster: _req['cluster'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/service/:service_id/autoscale/:target/scale_threshold/options */
  scaleThresholdOptions(
    req: scale_setting.ScaleThresholdOptionsRequest,
    options?: T,
  ): Promise<scale_setting.ScaleThresholdOptionsApiResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/service/${_req['service_id']}/autoscale/${_req['target']}/scale_threshold/options`,
    );
    const method = 'GET';
    const params = { mqtrigger_id: _req['mqtrigger_id'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /v2/services/:target_service_id/regions/:target_region/clusters/:target_cluster/copy_triggers */
  copyTriggers(
    req: trigger.CopyTriggersRequest,
    options?: T,
  ): Promise<common.CreateTicketResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['target_service_id']}/regions/${_req['target_region']}/clusters/${_req['target_cluster']}/copy_triggers`,
    );
    const method = 'POST';
    const data = {
      source_service_id: _req['source_service_id'],
      source_region: _req['source_region'],
      source_cluster: _req['source_cluster'],
      source_triggers: _req['source_triggers'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/autoscale/functions/scale_list */
  GetFunctionScaleRecordList(
    req: scale_record.GetFunctionScaleRecordListReq,
    options?: T,
  ): Promise<scale_record.GetFunctionScaleRecordListRes> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/autoscale/functions/scale_list`,
    );
    const method = 'GET';
    const params = {
      offset: _req['offset'],
      limit: _req['limit'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      cluster: _req['cluster'],
      strategy: _req['strategy'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/autoscale/mqtriggers/scale_list */
  GetMQTriggerScaleRecordList(
    req: scale_record.GetMQTriggerScaleRecordListReq,
    options?: T,
  ): Promise<scale_record.GetMQTriggerScaleRecordListRes> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/autoscale/mqtriggers/scale_list`,
    );
    const method = 'GET';
    const params = {
      offset: _req['offset'],
      limit: _req['limit'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      cluster: _req['cluster'],
      strategy: _req['strategy'],
      search: _req['search'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /v2/services/:service_id/debug/trigger/tpl */
  CreateTriggerDebugTpl(
    req: trigger_debug.CreateTriggerDebugTplRequest,
    options?: T,
  ): Promise<trigger_debug.CreateTriggerDebugTplResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/debug/trigger/tpl`,
    );
    const method = 'POST';
    const data = {
      tpl_type: _req['tpl_type'],
      cloud_event: _req['cloud_event'],
      name: _req['name'],
      trigger_type: _req['trigger_type'],
      msg_type: _req['msg_type'],
      native_event: _req['native_event'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /v2/mq/:mq_type/topic/preview */
  MQTopicPreview(
    req: trigger_debug.MQTopicPreviewRequest,
    options?: T,
  ): Promise<trigger_debug.MQTopicPreviewResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mq/${_req['mq_type']}/topic/preview`);
    const method = 'POST';
    const data = {
      mq_region: _req['mq_region'],
      service_id: _req['service_id'],
      region: _req['region'],
      cluster: _req['cluster'],
      is_batch_msg: _req['is_batch_msg'],
      kafka_topic_preview_params: _req['kafka_topic_preview_params'],
      rocket_mq_topic_preview_params: _req['rocket_mq_topic_preview_params'],
      eventbus_topic_preview_params: _req['eventbus_topic_preview_params'],
      is_native_msg: _req['is_native_msg'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/debug/trigger/tpl */
  GetTriggerDebugTpl(
    req: trigger_debug.GetTriggerDebugTplRequest,
    options?: T,
  ): Promise<trigger_debug.GetTriggerDebugTplResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/debug/trigger/tpl`,
    );
    const method = 'GET';
    const params = {
      tpl_type: _req['tpl_type'],
      trigger_type: _req['trigger_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /** PATCH /v2/services/:service_id/debug/trigger/tpl/:tpl_id */
  PatchTriggerDebugTpl(
    req: trigger_debug.PatchTriggerDebugTplRequest,
    options?: T,
  ): Promise<trigger_debug.PatchTriggerDebugTplResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/debug/trigger/tpl/${_req['tpl_id']}`,
    );
    const method = 'PATCH';
    const data = {
      name: _req['name'],
      cloud_event: _req['cloud_event'],
      msg_type: _req['msg_type'],
      native_event: _req['native_event'],
    };
    return this.request({ url, method, data }, options);
  }

  /** DELETE /v2/services/:service_id/debug/trigger/tpl/:tpl_id */
  DeleteTriggerDebugTpl(
    req: trigger_debug.DeleteTriggerDebugTplRequest,
    options?: T,
  ): Promise<trigger_debug.DeleteTriggerDebugTplResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/debug/trigger/tpl/${_req['tpl_id']}`,
    );
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/trigger_debug */
  TriggerDebug(
    req: trigger_debug.TriggerDebugRequest,
    options?: T,
  ): Promise<trigger_debug.TriggerDebugResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/trigger_debug`,
    );
    const method = 'POST';
    const data = {
      zone: _req['zone'],
      trigger_type: _req['trigger_type'],
      cloud_event: _req['cloud_event'],
      is_batch_msg: _req['is_batch_msg'],
      is_native_msg: _req['is_native_msg'],
      native_event: _req['native_event'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/:trigger_type/:trigger_id/restart
   *
   * 重启指定触发器
   */
  RestartMQTrigger(
    req: mqevent.MQTriggerRestartRequest,
    options?: T,
  ): Promise<mqevent.MQTriggerRestartResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/triggers/${_req['trigger_type']}/${_req['trigger_id']}/restart`,
    );
    const method = 'POST';
    const data = { max_surge_percent: _req['max_surge_percent'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/mq/rocketmq/topic/queue-info */
  MQQueueInfo(
    req: trigger_debug.MQQueueInfoRequest,
    options?: T,
  ): Promise<trigger_debug.MQQueueInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/mq/rocketmq/topic/queue-info');
    const method = 'GET';
    const params = {
      mq_region: _req['mq_region'],
      region: _req['region'],
      cluster_name: _req['cluster_name'],
      topic_name: _req['topic_name'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/regions/:region/zones/:zone/pods/:podname */
  getInstancesPodInfo(
    req: instance.GetInstancesPodInfoRequest,
    options?: T,
  ): Promise<instance.GetInstancesPodInfoResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/regions/${_req['region']}/zones/${_req['zone']}/pods/${_req['podname']}`,
    );
    const method = 'GET';
    const params = { cell: _req['cell'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /v2/base_image/:key/version_validation */
  checkImageVersion(
    req?: image.CheckImagesVersionRequest,
    options?: T,
  ): Promise<image.CheckImagesVersionResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/v2/base_image/${_req['key']}/version_validation`,
    );
    const method = 'POST';
    const data = { scm_version: _req['scm_version'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /v2/base_image/:key */
  UpdateBaseImages(
    req?: image.UpdateBaseImagesRequest,
    options?: T,
  ): Promise<image.UpdateBaseImagesResponse> {
    const _req = req || {};
    const url = this.genBaseURL(`/v2/base_image/${_req['key']}`);
    const method = 'POST';
    const headers = { UpdateBaseImages: _req['UpdateBaseImages'] };
    return this.request({ url, method, headers }, options);
  }

  /** POST /v2/image_manager/build/records */
  AddImageCICDRecords(
    req?: image.AddImageCICDRecordsRequest,
    options?: T,
  ): Promise<image.AddImageCICDRecordsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/image_manager/build/records');
    const method = 'POST';
    const data = {
      create_by: _req['create_by'],
      image_type: _req['image_type'],
      app_env: _req['app_env'],
      value: _req['value'],
      old_record_value: _req['old_record_value'],
      description: _req['description'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /v1/base_image/:key */
  UpdateBaseImagesV1(
    req?: image.UpdateBaseImagesRequest,
    options?: T,
  ): Promise<image.UpdateBaseImagesResponse> {
    const _req = req || {};
    const url = this.genBaseURL(`/v1/base_image/${_req['key']}`);
    const method = 'POST';
    const headers = { UpdateBaseImages: _req['UpdateBaseImages'] };
    return this.request({ url, method, headers }, options);
  }

  /** POST /v1/image_manager/build/records */
  AddImageCICDRecordsV1(
    req?: image.AddImageCICDRecordsRequest,
    options?: T,
  ): Promise<image.AddImageCICDRecordsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/image_manager/build/records');
    const method = 'POST';
    const data = {
      create_by: _req['create_by'],
      image_type: _req['image_type'],
      app_env: _req['app_env'],
      value: _req['value'],
      old_record_value: _req['old_record_value'],
      description: _req['description'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PUT /v2/base_image/:key/scm_version */
  updateImageScmVersionV2(
    req?: image.UpdateImageScmVersionRequest,
    options?: T,
  ): Promise<image.UpdateImageScmVersionResponse> {
    const _req = req || {};
    const url = this.genBaseURL(`/v2/base_image/${_req['key']}/scm_version`);
    const method = 'PUT';
    const data = { version: _req['version'], git_commit: _req['git_commit'] };
    return this.request({ url, method, data }, options);
  }

  /** PATCH /v1/base_image/:key */
  preUpdateBaseImageV1(
    req?: image.PreUpdateBaseImagesRequest,
    options?: T,
  ): Promise<image.PreUpdateBaseImagesResponse> {
    const _req = req || {};
    const url = this.genBaseURL(`/v1/base_image/${_req['key']}`);
    const method = 'PATCH';
    const data = { data: _req['data'] };
    return this.request({ url, method, data }, options);
  }

  /** PATCH /v2/base_image/:key */
  preUpdateBaseImage(
    req?: image.PreUpdateBaseImagesRequest,
    options?: T,
  ): Promise<image.PreUpdateBaseImagesResponse> {
    const _req = req || {};
    const url = this.genBaseURL(`/v2/base_image/${_req['key']}`);
    const method = 'PATCH';
    const data = { data: _req['data'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/zones/:zone/instances/:podname/frozen_active */
  activeFunctionFrozenInstance(
    req: instance.ActiveFunctionFrozenInstanceRequest,
    options?: T,
  ): Promise<instance.ActiveFunctionFrozenInstanceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/zones/${_req['zone']}/instances/${_req['podname']}/frozen_active`,
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** GET /v2/resource/services/:service_id/mqevent */
  getMQEventResource(
    req: resource.GetMQEventResourceRequest,
    options?: T,
  ): Promise<resource.GetResourceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/resource/services/${_req['service_id']}/mqevent`,
    );
    const method = 'GET';
    const params = { env: _req['env'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/zones/:zone/instances/:podname/service_discovery */
  updateFunctionInstanceServiceDiscovery(
    req: instance.UpdateFunctionInstanceServiceDiscoveryRequest,
    options?: T,
  ): Promise<instance.UpdateFunctionInstanceServiceDiscoveryResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/zones/${_req['zone']}/instances/${_req['podname']}/service_discovery`,
    );
    const method = 'POST';
    const data = { disabled: _req['disabled'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/plugin_functions/:plugin_name/plugin_versions */
  getGlobalPluginVersions(
    req: plugin_function.GetGlobalPluginVersionsRequest,
    options?: T,
  ): Promise<plugin_function.GetPluginVersionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/plugin_functions/${_req['plugin_name']}/plugin_versions`,
    );
    const method = 'GET';
    const params = { limit: _req['limit'], offset: _req['offset'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/plugin_functions */
  getGlobalPluginFunctions(
    req?: plugin_function.GetGlobalPluginFunctionsRequest,
    options?: T,
  ): Promise<plugin_function.GetPluginFunctionsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/plugin_functions');
    const method = 'GET';
    const params = { limit: _req['limit'], offset: _req['offset'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/volc_signin_token */
  getVolcSigninToken(
    req?: volcengine.GetVolcSigninTokenRequest,
    options?: T,
  ): Promise<volcengine.GetVolcSigninTokenResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/volc_signin_token`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/volc_tls_config */
  getVolcTlsConfig(
    req?: volcengine.GetVolcTlsConfigRequest,
    options?: T,
  ): Promise<volcengine.GetVolcTlsConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/volc_tls_config`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /admin/batch_tasks/function */
  AdminCreateUpdateFunctionBatchTask(
    req: admin.AdminCreateUpdateFunctionBatchTaskRequest,
    options?: T,
  ): Promise<admin.AdminCreateUpdateFunctionBatchTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/admin/batch_tasks/function');
    const method = 'POST';
    const data = {
      clusters: _req['clusters'],
      runtime: _req['runtime'],
      target_image: _req['target_image'],
      strategy: _req['strategy'],
      rolling_step: _req['rolling_step'],
      format_envs: _req['format_envs'],
      critical: _req['critical'],
      auto_start: _req['auto_start'],
      description: _req['description'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /admin/clusters */
  AdminGetClusters(
    req?: admin.AdminGetClustersRequest,
    options?: T,
  ): Promise<admin.AdminGetClustersResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/admin/clusters');
    const method = 'GET';
    const params = {
      service_id: _req['service_id'],
      function_id: _req['function_id'],
      psm: _req['psm'],
      region: _req['region'],
      runtime: _req['runtime'],
      limit: _req['limit'],
      offset: _req['offset'],
      active: _req['active'],
      env_name: _req['env_name'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /admin/batch_tasks */
  AdminGetBatchTask(
    req?: admin.AdminGetBatchTaskRequest,
    options?: T,
  ): Promise<admin.AdminGetBatchTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/admin/batch_tasks');
    const method = 'GET';
    const params = {
      batch_task_id: _req['batch_task_id'],
      task_type: _req['task_type'],
      offset: _req['offset'],
      limit: _req['limit'],
      status: _req['status'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /admin/base_image_desc */
  AdminGetBaseImageByRuntimeAndId(
    req: admin.AdminGetBaseImageByRuntimeAndIdRequest,
    options?: T,
  ): Promise<admin.AdminGetBaseImageByRuntimeAndIdResponse> {
    const _req = req;
    const url = this.genBaseURL('/admin/base_image_desc');
    const method = 'GET';
    const params = { runtime: _req['runtime'], image_id: _req['image_id'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/tce/cluster_list */
  GetTCEClusterList(
    req: tce.GetTCEClusterListRequest,
    options?: T,
  ): Promise<tce.GetTCEClusterListResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/tce/cluster_list');
    const method = 'GET';
    const params = { tce_psm: _req['tce_psm'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/tce/migrate/mq_app_params */
  GetTCEMigrateMQAppParams(
    req: tce.GetTCEMigrateMQAppParamsRequest,
    options?: T,
  ): Promise<tce.GetTCEMigrateMQAppParamsResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/tce/migrate/mq_app_params');
    const method = 'GET';
    const params = {
      tce_psm: _req['tce_psm'],
      tce_cluster_id: _req['tce_cluster_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /v2/images/get_icm_base_image_list
   *
   * @title getICMBaseImageList
   */
  GetICMBaseImageList(
    req?: build.GetICMBaseImagesListRequest,
    options?: T,
  ): Promise<build.GetICMBaseImagesListResponse> {
    const url = this.genBaseURL('/v2/images/get_icm_base_image_list');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** PUT /admin/settings_etcd */
  AdminUpserEtcdSetting(
    req: admin.AdminUpsertEtcdSettingRequest,
    options?: T,
  ): Promise<admin.AdminUpsertEtcdSettingResponse> {
    const _req = req;
    const url = this.genBaseURL('/admin/settings_etcd');
    const method = 'PUT';
    const data = {
      name: _req['name'],
      value: _req['value'],
      cell: _req['cell'],
      region: _req['region'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /admin/settings_etcd/:setting_name */
  AdminGetEtcdSettings(
    req: admin.AdminGetEtcdSettingsRequest,
    options?: T,
  ): Promise<admin.AdminGetEtcdSettingsResponse> {
    const _req = req;
    const url = this.genBaseURL(`/admin/settings_etcd/${_req['setting_name']}`);
    const method = 'GET';
    const params = { cell: _req['cell'], region: _req['region'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /admin/cells */
  AdminGetAvaliableCells(
    req: admin.AdminGetAvailableCellsRequest,
    options?: T,
  ): Promise<admin.AdminGetAvailableCellsResponse> {
    const _req = req;
    const url = this.genBaseURL('/admin/cells');
    const method = 'GET';
    const params = { region: _req['region'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /admin/settings_etcd */
  AdminGetAllEtcdSettings(
    req: admin.AdminGetAllEtcdSettingsRequest,
    options?: T,
  ): Promise<admin.AdminGetAllEtcdSettingsResponse> {
    const _req = req;
    const url = this.genBaseURL('/admin/settings_etcd');
    const method = 'GET';
    const data = { cell: _req['cell'], region: _req['region'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * PATCH /admin/parent_task/:batch_task_id
   *
   * @title AdminUpdateParentTask
   */
  AdminUpdateParentTask(
    req: admin.AdminUpdateParentTaskRequest,
    options?: T,
  ): Promise<admin.AdminUpdateParentTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(`/admin/parent_task/${_req['batch_task_id']}`);
    const method = 'PATCH';
    const data = { status: _req['status'], concurrency: _req['concurrency'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /admin/parent_task
   *
   * @title AdminGetParentTask
   */
  AdminGetParentTask(
    req?: admin.AdminGetParentTaskRequest,
    options?: T,
  ): Promise<admin.AdminGetParentTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/admin/parent_task');
    const method = 'GET';
    const params = {
      status: _req['status'],
      task_type: _req['task_type'],
      limit: _req['limit'],
      offset: _req['offset'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/regions/zones */
  getRegionZones(
    req?: other.getRegionZonesRequest,
    options?: T,
  ): Promise<other.GetRegionZonesResponse> {
    const url = this.genBaseURL('/v2/regions/zones');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/mqevents/advanced_config_setting */
  GetMQeventAdvancedConfig(
    req?: mqevent.GetMQeventAdvancedConfigRequest,
    options?: T,
  ): Promise<mqevent.GetMQeventAdvancedConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/mqevents/advanced_config_setting');
    const method = 'GET';
    const params = { region: _req['region'] };
    return this.request({ url, method, params }, options);
  }

  /** PATCH /admin/parent_task/:parent_task_id/batch_tasks/:batch_task_id */
  AdminUpdateBatchTask(
    req: admin.AdminUpdateBatchTaskRequset,
    options?: T,
  ): Promise<admin.AdminUpdateBatchTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/admin/parent_task/${_req['parent_task_id']}/batch_tasks/${_req['batch_task_id']}`,
    );
    const method = 'PATCH';
    const data = { status: _req['status'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /admin/parent_task/:parent_task_id */
  AdminGetParentTaskDetail(
    req: admin.AdminGetParentTaskDetailRequest,
    options?: T,
  ): Promise<admin.AdminGetParentTaskDetailResponse> {
    const _req = req;
    const url = this.genBaseURL(`/admin/parent_task/${_req['parent_task_id']}`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /spi/services/psm/:psm/env/:env/regions/:region/clusters/:cluster/zones/:zone/trigger_frozen_active */
  TriggerFrozenActive(
    req: trigger_frozen_active.TriggerFrozenActiveRequest,
    options?: T,
  ): Promise<trigger_frozen_active.TriggerFrozenActiveResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/spi/services/psm/${_req['psm']}/env/${_req['env']}/regions/${_req['region']}/clusters/${_req['cluster']}/zones/${_req['zone']}/trigger_frozen_active`,
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/vefaas_traffic_scheduling */
  updateVefaasTrafficScheduling(
    req: cluster.UpdateVefaasTrafficSchedulingRequest,
    options?: T,
  ): Promise<cluster.UpdateVefaasTrafficSchedulingResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/v2/services/:service_id/regions/:region/clusters/:cluster/vefaas_traffic_scheduling',
    );
    const method = 'POST';
    const data = {
      enabled: _req['enabled'],
      psm: _req['psm'],
      cluster: _req['cluster'],
      global_mode: _req['global_mode'],
      global_ratio: _req['global_ratio'],
      trigger_config: _req['trigger_config'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/vefaas_traffic_scheduling */
  getVefaasTrafficScheduling(
    req?: cluster.GetVefaasTrafficSchedulingRequest,
    options?: T,
  ): Promise<cluster.GetVefaasTrafficSchedulingResponse> {
    const url = this.genBaseURL(
      '/v2/services/:service_id/regions/:region/clusters/:cluster/vefaas_traffic_scheduling',
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/psm/:psm/cross_region_migration */
  getCrossRegionMigration(
    req: service.GetCrossRegionMigrationRequest,
    options?: T,
  ): Promise<service.GetCrossRegionMigrationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/psm/${_req['psm']}/cross_region_migration`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** PATCH /v2/services/:service_id/scale_setting */
  updateServiceScaleSettings(
    req: scale_setting.UpdateServiceScaleSettingsRequest,
    options?: T,
  ): Promise<common.ApiResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/scale_setting`,
    );
    const method = 'PATCH';
    const data = {
      service_cpu_scale_settings: _req['service_cpu_scale_settings'],
      cluster_cpu_scale_settings: _req['cluster_cpu_scale_settings'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /admin/service_trees */
  GetServiceTrees(
    req?: admin.GetServiceTreesRequest,
    options?: T,
  ): Promise<admin.GetServiceTreesResponse> {
    const url = this.genBaseURL('/admin/service_trees');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** DELETE /spi/services/psm/:psm/env/:env/regions/:region/clusters/:cluster/burst_protector_configs */
  DeleteBurstProtector(
    req?: burst_protector.DeleteBurstProtectorRequest,
    options?: T,
  ): Promise<burst_protector.DeleteBurstProtectorResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/spi/services/psm/:psm/env/:env/regions/:region/clusters/:cluster/burst_protector_configs',
    );
    const method = 'DELETE';
    const params = {
      is_all: _req['is_all'],
      psms: _req['psms'],
      psm: _req['psm'],
      cluster: _req['cluster'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /spi/services/psm/:psm/env/:env/regions/:region/clusters/:cluster/burst_protector_configs */
  GetBurstProtectorSwitch(
    req?: burst_protector.GetBurstProtectorSwitchRequest,
    options?: T,
  ): Promise<burst_protector.GetBurstProtectorSwitchResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/spi/services/psm/${_req['psm']}/env/:env/regions/:region/clusters/${_req['cluster']}/burst_protector_configs`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** PUT /spi/services/psm/:psm/env/:env/regions/:region/clusters/:cluster/burst_protector_configs */
  PutBurstProtectorSwitch(
    req: burst_protector.PutBurstProtectorSwitchRequest,
    options?: T,
  ): Promise<burst_protector.PutBurstProtectorSwitchResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/spi/services/psm/${_req['psm']}/env/:env/regions/:region/clusters/${_req['cluster']}/burst_protector_configs`,
    );
    const method = 'PUT';
    const data = { config: _req['config'] };
    const params = {
      caller_psm: _req['caller_psm'],
      caller_cluster: _req['caller_cluster'],
      method: _req['method'],
    };
    return this.request({ url, method, data, params }, options);
  }

  /** PATCH /v2/admin/burst_protector/switch */
  SwitchAllBurstProtector(
    req: burst_protector.SwitchBurstProtectorRequest,
    options?: T,
  ): Promise<burst_protector.SwitchBurstProtectorResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/admin/burst_protector/switch');
    const method = 'PATCH';
    const params = {
      is_all: _req['is_all'],
      psms: _req['psms'],
      psm: _req['psm'],
      cluster: _req['cluster'],
      stage: _req['stage'],
      debug: _req['debug'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/services/:service_id/regions/:region/clusters/:cluster/zones/:zone/instances/:podname/stages */
  getZoneInstancesStage(
    req: instance.GetInstancesStageRequest,
    options?: T,
  ): Promise<instance.GetInstancesStageResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/zones/${_req['zone']}/instances/${_req['podname']}/stages`,
    );
    const method = 'GET';
    const params = { revision_id: _req['revision_id'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /v2/services/:service_id/restore */
  restoreService(
    req: service.RestoreServiceRequest,
    options?: T,
  ): Promise<common.ApiResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/services/${_req['service_id']}/restore`);
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/restore */
  restoreCluster(
    req: cluster.RestoreClusterRequest,
    options?: T,
  ): Promise<cluster.RestoreClusterResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/restore`,
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /admin/image_versions */
  CreateImageVersionByAdmin(
    req: admin.CreateImageVersionByAdminRequest,
    options?: T,
  ): Promise<admin.CreateImageVersionByAdminResponse> {
    const _req = req;
    const url = this.genBaseURL('/admin/image_versions');
    const method = 'POST';
    const data = {
      region: _req['region'],
      type: _req['type'],
      status: _req['status'],
      tag: _req['tag'],
      image_id: _req['image_id'],
      publisher: _req['publisher'],
      version: _req['version'],
      changelog: _req['changelog'],
      branch: _req['branch'],
      commit: _req['commit'],
      alias: _req['alias'],
      canary_ratio: _req['canary_ratio'],
      extra: _req['extra'],
      published_at: _req['published_at'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /admin/image_versions */
  GetImageVersions(
    req?: admin.GetImageVersionsRequest,
    options?: T,
  ): Promise<admin.GetImageVersionsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/admin/image_versions');
    const method = 'GET';
    const params = {
      region: _req['region'],
      type: _req['type'],
      tag: _req['tag'],
      limit: _req['limit'],
      offset: _req['offset'],
      with_history_images: _req['with_history_images'],
      version_number: _req['version_number'],
    };
    return this.request({ url, method, params }, options);
  }

  /** PATCH /admin/image_versions/:id */
  UpdateImageVersionByID(
    req: admin.UpdateImageVersionByIDRequest,
    options?: T,
  ): Promise<admin.UpdateImageVersionByIDResponse> {
    const _req = req;
    const url = this.genBaseURL(`/admin/image_versions/${_req['id']}`);
    const method = 'PATCH';
    const data = {
      changelog: _req['changelog'],
      status: _req['status'],
      tag: _req['tag'],
      canary_ratio: _req['canary_ratio'],
      published_at: _req['published_at'],
      alias: _req['alias'],
      extra: _req['extra'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /admin/image_versions/sync */
  SyncImageVersions(
    req: admin.SyncImageVersionsRequest,
    options?: T,
  ): Promise<admin.SyncImageVersionsResponse> {
    const _req = req;
    const url = this.genBaseURL('/admin/image_versions/sync');
    const method = 'POST';
    const data = {
      from_region: _req['from_region'],
      to_regions: _req['to_regions'],
      tags: _req['tags'],
      types: _req['types'],
      insert: _req['insert'],
      image_version_number: _req['image_version_number'],
      image_version_type: _req['image_version_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** DELETE /admin/image_versions/:id */
  DeleteImageVersionByID(
    req: admin.DeleteImageVersionByIDRequest,
    options?: T,
  ): Promise<admin.DeleteImageVersionByIDResponse> {
    const _req = req;
    const url = this.genBaseURL(`/admin/image_versions/${_req['id']}`);
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/inspections */
  getServiceInspectionList(
    req: service.GetServiceInspectionListRequest,
    options?: T,
  ): Promise<service.GetServiceInspectionListResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/inspections`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /v2/services/:service_id/regions/:region/clusters/:cluster/update */
  createUpdateClusterTicket(
    req: cluster.UpdateClusterTicketRequest,
    options?: T,
  ): Promise<common.CreateTicketResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/update`,
    );
    const method = 'POST';
    const data = {
      approved_by: _req['approved_by'],
      approved_by_usertype: _req['approved_by_usertype'],
      params: _req['params'],
      dry_run: _req['dry_run'],
    };
    const headers = {
      'X-Bytefaas-Globalcp-Ticket': _req['X-Bytefaas-Globalcp-Ticket'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** PATCH /v2/services/:service_id/regions/:region/clusters/:cluster/triggers/:trigger_type/:trigger_id/replica_limit */
  PatchMqTriggerReplicaLimit(
    req: mqevent.PatchMqEventReplicaLimitRequest,
    options?: T,
  ): Promise<mqevent.PatchMqEventReplicaLimitResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/regions/:region/clusters/${_req['cluster']}/triggers/${_req['trigger_type']}/${_req['trigger_id']}/replica_limit`,
    );
    const method = 'PATCH';
    const data = {
      region: _req['region'],
      replica_max_limit: _req['replica_max_limit'],
      replica_min_limit: _req['replica_min_limit'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/tce/cluster/migrate/mq_app_params */
  GetTCEClusterMigrateMQAppParams(
    req: tce.GetTCEClusterMigrateMQAppParamsRequest,
    options?: T,
  ): Promise<tce.GetTCEClusterMigrateMQAppParamsResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/tce/cluster/migrate/mq_app_params');
    const method = 'GET';
    const params = {
      tce_psm: _req['tce_psm'],
      tce_cluster_id: _req['tce_cluster_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/tce/service/migrate/mq_app_params */
  GetTCEServiceMigrateMQAppParams(
    req: tce.GetTCEServiceMigrateMQAppParamsRequest,
    options?: T,
  ): Promise<tce.GetTCEServiceMigrateMQAppParamsResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/tce/service/migrate/mq_app_params');
    const method = 'GET';
    const params = { tce_psm: _req['tce_psm'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /admin/image_versions/type/mq_events */
  CreateMqEventImageVersionsByBytecycle(
    req: admin.CreateMqEventImageVersionsByBytecycleRequest,
    options?: T,
  ): Promise<admin.CreateMqEventImageVersionsByBytecycleResponse> {
    const _req = req;
    const url = this.genBaseURL('/admin/image_versions/type/mq_events');
    const method = 'POST';
    const data = {
      trigger_user: _req['trigger_user'],
      faas_release_form_base64: _req['faas_release_form_base64'],
      commit_id: _req['commit_id'],
      git_mr_detail: _req['git_mr_detail'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /admin/image_versions/async_trigger */
  AsyncBuildImageVersions(
    req: admin.AsyncBuildImageVersionsRequest,
    options?: T,
  ): Promise<admin.AsyncBuildImageVersionsResponse> {
    const _req = req;
    const url = this.genBaseURL('/admin/image_versions/async_trigger');
    const method = 'POST';
    const data = {
      faas_release_form_base64: _req['faas_release_form_base64'],
      trigger_user: _req['trigger_user'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/services/:service_id/templates */
  GetServicePipelineTemplate(
    req: pipeline_template.GetServicePipelineTemplateRequest,
    options?: T,
  ): Promise<pipeline_template.GetServicePipelineTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/services/${_req['service_id']}/templates`);
    const method = 'GET';
    const params = {
      operation: _req['operation'],
      include_system_template: _req['include_system_template'],
      include_disabled: _req['include_disabled'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/pipeline/template */
  ListPipelineTemplate(
    req?: pipeline_template.ListPipelineTemplateRequest,
    options?: T,
  ): Promise<pipeline_template.ListPipelineTemplateResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/pipeline/template');
    const method = 'GET';
    const params = {
      base_template: _req['base_template'],
      system_template: _req['system_template'],
      service_id: _req['service_id'],
      bytetree_id: _req['bytetree_id'],
      limit: _req['limit'],
      offset: _req['offset'],
    };
    return this.request({ url, method, params }, options);
  }

  /** DELETE /v2/pipeline/template/:template_id */
  DeletePipelineTemplate(
    req: pipeline_template.DeletePipelineTemplateRequest,
    options?: T,
  ): Promise<pipeline_template.DeletePipelineTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/pipeline/template/${_req['template_id']}`);
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /** POST /v2/pipeline/template */
  CreatePipelineTemplate(
    req: pipeline_template.CreatePipelineTemplateRequest,
    options?: T,
  ): Promise<pipeline_template.CreatePipelineTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/pipeline/template');
    const method = 'POST';
    const data = {
      name: _req['name'],
      type: _req['type'],
      description: _req['description'],
      system_template: _req['system_template'],
      disabled: _req['disabled'],
      stages: _req['stages'],
      match_rules: _req['match_rules'],
      default_template: _req['default_template'],
      admins: _req['admins'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/pipeline/stages */
  ListPipelineStage(
    req?: pipeline_template.ListPipelineStageRequest,
    options?: T,
  ): Promise<pipeline_template.ListPipelineStageResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/pipeline/stages');
    const method = 'GET';
    const params = { system_stage: _req['system_stage'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/pipeline/template/:template_id */
  GetPipelineTemplate(
    req: pipeline_template.GetPipelineTemplateRequest,
    options?: T,
  ): Promise<pipeline_template.GetPipelineTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/pipeline/template/${_req['template_id']}`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /v2/pipeline/template/:template_id */
  UpdatePipelineTemplate(
    req: pipeline_template.UpdatePipelineTemplateRequest,
    options?: T,
  ): Promise<pipeline_template.UpdatePipelineTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/pipeline/template/${_req['template_id']}`);
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      disabled: _req['disabled'],
      stages: _req['stages'],
      match_rules: _req['match_rules'],
      default_template: _req['default_template'],
      admins: _req['admins'],
      created_by: _req['created_by'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PATCH /v2/eventbus/psm/:psm/consumer_meta_status */
  UpdateMqAppConsumerMetaStatus(
    req: eventbus.UpdateMqAppConsumerMetaStatusRequest,
    options?: T,
  ): Promise<eventbus.UpdateMqAppConsumerMetaStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/eventbus/psm/${_req['psm']}/consumer_meta_status`,
    );
    const method = 'PATCH';
    return this.request({ url, method }, options);
  }

  /** GET /v2/eventbus/psm/:psm/consumer_meta_status */
  GetMqAppConsumerMetaStatus(
    req: eventbus.GetMqAppConsumerMetaStatusRequest,
    options?: T,
  ): Promise<eventbus.GetMqAppConsumerMetaStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/eventbus/psm/${_req['psm']}/consumer_meta_status`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/service/:service_id/regions/:region/clusters/:cluster/autoscale/:target/scale_threshold/options */
  getScaleThresholdOptions(
    req: scale_setting.ScaleThresholdOptionsRequestV2,
    options?: T,
  ): Promise<scale_setting.ScaleThresholdOptionsApiResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/service/${_req['service_id']}/regions/${_req['region']}/clusters/${_req['cluster']}/autoscale/${_req['target']}/scale_threshold/options`,
    );
    const method = 'GET';
    const params = { mqtrigger_id: _req['mqtrigger_id'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/eventbus/psm/:psm/consumer_meta */
  GetMqAppConsumerMeta(
    req: eventbus.GetMqAppConsumerMetaRequest,
    options?: T,
  ): Promise<eventbus.GetMqAppConsumerMetaResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/eventbus/psm/${_req['psm']}/consumer_meta`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /v2/eventbus/psm/:psm/check_consumer_meta */
  CheckMqAppConsumerMeta(
    req: eventbus.CheckMqAppConsumerMetaRequest,
    options?: T,
  ): Promise<eventbus.CheckMqAppConsumerMetaResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/eventbus/psm/${_req['psm']}/check_consumer_meta`,
    );
    const method = 'POST';
    const data = {
      mq_cluster: _req['mq_cluster'],
      topic: _req['topic'],
      consumer_group: _req['consumer_group'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /v2/mcp/servers */
  CreateMcpServer(
    req: mcp_server.CreateMcpServerRequest,
    options?: T,
  ): Promise<mcp_server.CreateMcpServerResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/mcp/servers');
    const method = 'POST';
    const data = {
      psm: _req['psm'],
      name: _req['name'],
      description: _req['description'],
      owner: _req['owner'],
      psm_parent_id: _req['psm_parent_id'],
      global_config: _req['global_config'],
      admins: _req['admins'],
      authorizers: _req['authorizers'],
      service_level: _req['service_level'],
      service_purpose: _req['service_purpose'],
      auth_enabled: _req['auth_enabled'],
      allowed_psms: _req['allowed_psms'],
      meta_params: _req['meta_params'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PATCH /v2/mcp/servers/:server_id/tools/:tool_id */
  UpdateMcpTool(
    req: mcp_tool.UpdateMcpToolRequest,
    options?: T,
  ): Promise<mcp_tool.UpdateMcpToolResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/mcp/servers/${_req['server_id']}/tools/${_req['tool_id']}`,
    );
    const method = 'PATCH';
    const data = {
      name: _req['name'],
      tool_name: _req['tool_name'],
      tool_description: _req['tool_description'],
      tool_input_schema: _req['tool_input_schema'],
      enabled: _req['enabled'],
      tool_type: _req['tool_type'],
      tool_config: _req['tool_config'],
    };
    return this.request({ url, method, data }, options);
  }

  /** DELETE /v2/mcp/servers/:server_id/tools/:tool_id */
  DeleteMcpTool(
    req: mcp_tool.DeleteMcpToolRequest,
    options?: T,
  ): Promise<common.ApiResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/mcp/servers/${_req['server_id']}/tools/${_req['tool_id']}`,
    );
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /** PATCH /v2/mcp/servers/:server_id */
  UpdateMcpServer(
    req: mcp_server.UpdateMcpServerRequest,
    options?: T,
  ): Promise<mcp_server.UpdateMcpServerResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mcp/servers/${_req['server_id']}`);
    const method = 'PATCH';
    const data = {
      name: _req['name'],
      description: _req['description'],
      global_config: _req['global_config'],
      owner: _req['owner'],
      admins: _req['admins'],
      authorizers: _req['authorizers'],
      service_level: _req['service_level'],
      service_purpose: _req['service_purpose'],
      auth_enabled: _req['auth_enabled'],
      allowed_psms: _req['allowed_psms'],
      custom_handler: _req['custom_handler'],
      meta_params: _req['meta_params'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/mcp/servers/:server_id/tools */
  ListMcpTools(
    req: mcp_tool.ListMcpToolsRequest,
    options?: T,
  ): Promise<mcp_tool.ListMcpToolsResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mcp/servers/${_req['server_id']}/tools`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** DELETE /v2/mcp/servers/:server_id */
  DeleteMcpServer(
    req: mcp_server.DeleteMcpServerRequest,
    options?: T,
  ): Promise<common.ApiResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mcp/servers/${_req['server_id']}`);
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /** GET /v2/agents */
  ListAgents(
    req?: agent.ListAgentsRequest,
    options?: T,
  ): Promise<agent.ListAgentsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/agents');
    const method = 'GET';
    const params = {
      all: _req['all'],
      env: _req['env'],
      limit: _req['limit'],
      name: _req['name'],
      offset: _req['offset'],
      owner: _req['owner'],
      search: _req['search'],
      search_type: _req['search_type'],
      sort_by: _req['sort_by'],
      search_fields: _req['search_fields'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/mcp/servers/:server_id */
  GetMcpServer(
    req: mcp_server.GetMcpServerRequest,
    options?: T,
  ): Promise<mcp_server.GetMcpServerResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mcp/servers/${_req['server_id']}`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/mcp/servers/:server_id/tools/:tool_id */
  GetMcpTool(
    req: mcp_tool.GetMcpToolRequest,
    options?: T,
  ): Promise<mcp_tool.GetMcpToolResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/mcp/servers/${_req['server_id']}/tools/${_req['tool_id']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /v2/mcp/servers/:server_id/tools */
  CreateMcpTool(
    req: mcp_tool.CreateMcpToolRequest,
    options?: T,
  ): Promise<mcp_tool.CreateMcpToolResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mcp/servers/${_req['server_id']}/tools`);
    const method = 'POST';
    const data = {
      name: _req['name'],
      tool_name: _req['tool_name'],
      tool_description: _req['tool_description'],
      tool_input_schema: _req['tool_input_schema'],
      enabled: _req['enabled'],
      tool_type: _req['tool_type'],
      tool_config: _req['tool_config'],
    };
    return this.request({ url, method, data }, options);
  }

  /** DELETE /v2/agents/:agent_id */
  DeleteAgent(
    req: agent.DeleteAgentRequest,
    options?: T,
  ): Promise<common.ApiResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/agents/${_req['agent_id']}`);
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /** GET /v2/mcp/servers */
  ListMcpServers(
    req?: mcp_server.ListMcpServersRequest,
    options?: T,
  ): Promise<mcp_server.ListMcpServersResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/mcp/servers');
    const method = 'GET';
    const params = {
      all: _req['all'],
      env: _req['env'],
      limit: _req['limit'],
      name: _req['name'],
      offset: _req['offset'],
      owner: _req['owner'],
      search: _req['search'],
      search_type: _req['search_type'],
      sort_by: _req['sort_by'],
      search_fields: _req['search_fields'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/agents/:agent_id */
  GetAgent(
    req: agent.GetAgentRequest,
    options?: T,
  ): Promise<agent.GetAgentResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/agents/${_req['agent_id']}`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /v2/agents */
  CreateAgent(
    req: agent.CreateAgentRequest,
    options?: T,
  ): Promise<agent.CreateAgentResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/agents');
    const method = 'POST';
    const data = {
      psm: _req['psm'],
      name: _req['name'],
      description: _req['description'],
      psm_parent_id: _req['psm_parent_id'],
      owner: _req['owner'],
      region: _req['region'],
      runtime: _req['runtime'],
      format_envs: _req['format_envs'],
      source_type: _req['source_type'],
      source: _req['source'],
      dependency: _req['dependency'],
      admins: _req['admins'],
      authorizers: _req['authorizers'],
      service_level: _req['service_level'],
      service_purpose: _req['service_purpose'],
      git_group: _req['git_group'],
      git_repo: _req['git_repo'],
      scm_repo: _req['scm_repo'],
      template_name: _req['template_name'],
      language: _req['language'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PATCH /v2/agents/:agent_id */
  UpdateAgent(
    req: agent.UpdateAgentRequest,
    options?: T,
  ): Promise<agent.UpdateAgentResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/agents/${_req['agent_id']}`);
    const method = 'PATCH';
    const data = { name: _req['name'], description: _req['description'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /v2/mcp/servers/:server_id/release */
  ReleaseMcpServer(
    req: mcp_server.ReleaseMcpServerRequest,
    options?: T,
  ): Promise<mcp_server.ReleaseMcpServerResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mcp/servers/${_req['server_id']}/release`);
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /v2/async/agents */
  AsyncCreateAgent(
    req: agent.AsyncCreateAgentRequest,
    options?: T,
  ): Promise<agent.AsyncCreateAgentResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/async/agents');
    const method = 'POST';
    const data = {
      psm: _req['psm'],
      name: _req['name'],
      description: _req['description'],
      psm_parent_id: _req['psm_parent_id'],
      owner: _req['owner'],
      region: _req['region'],
      runtime: _req['runtime'],
      format_envs: _req['format_envs'],
      git_group: _req['git_group'],
      git_repo: _req['git_repo'],
      scm_repo: _req['scm_repo'],
      template_name: _req['template_name'],
      admins: _req['admins'],
      authorizers: _req['authorizers'],
      service_level: _req['service_level'],
      service_purpose: _req['service_purpose'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/async/agents/status/:creation_id */
  GetAsyncCreateAgentStatus(
    req: agent.GetAsyncCreateAgentStatusRequest,
    options?: T,
  ): Promise<agent.GetAsyncCreateAgentStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/async/agents/status/${_req['creation_id']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/services/:service_id/default_template */
  GetServiceDefaultPipelineTemplate(
    req: pipeline_template.GetServiceDefaultPipelineTemplateRequest,
    options?: T,
  ): Promise<pipeline_template.GetServiceDefaultPipelineTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/services/${_req['service_id']}/default_template`,
    );
    const method = 'GET';
    const params = { operation: _req['operation'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/mcp/bam/api/:api_id */
  GetBamApi(
    req: mcp_tool.BamApiRequest,
    options?: T,
  ): Promise<mcp_tool.BamApiResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mcp/bam/api/${_req['api_id']}`);
    const method = 'GET';
    const params = { version: _req['version'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /v2/mcp/bam/:psm/apis */
  ListBamApis(
    req: mcp_tool.BamApiListRequest,
    options?: T,
  ): Promise<mcp_tool.BamApiListResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mcp/bam/${_req['psm']}/apis`);
    const method = 'GET';
    const params = { version: _req['version'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /admin/emergency_transfer_metrics */
  GetEmergencyTransferMetrics(
    req?: admin.GetEmergencyTransferMetricsRequest,
    options?: T,
  ): Promise<admin.GetEmergencyTransferMetricsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/admin/emergency_transfer_metrics');
    const method = 'POST';
    const data = {
      cells: _req['cells'],
      cluster_types: _req['cluster_types'],
      regions: _req['regions'],
      value_types: _req['value_types'],
      zones: _req['zones'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PATCH /admin/recover_emergency_transfer */
  RecoverEmergencyTransfer(
    req?: admin.RecoverEmergencyTransferRequest,
    options?: T,
  ): Promise<admin.RecoverEmergencyTransferResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/admin/recover_emergency_transfer');
    const method = 'PATCH';
    const data = {
      function_id: _req['function_id'],
      region: _req['region'],
      resource_type: _req['resource_type'],
      trigger_id: _req['trigger_id'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /admin/unused_resources_metrics */
  GetUnusedResourceMetrics(
    req?: admin.GetUnusedResourceMetricsRequest,
    options?: T,
  ): Promise<admin.GetUnusedResourceMetricsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/admin/unused_resources_metrics');
    const method = 'POST';
    const data = {
      regions: _req['regions'],
      cells: _req['cells'],
      zones: _req['zones'],
      node_levels: _req['node_levels'],
      service_levels: _req['service_levels'],
      item_types: _req['item_types'],
      function_ids: _req['function_ids'],
      trigger_ids: _req['trigger_ids'],
      scale_types: _req['scale_types'],
      strategies: _req['strategies'],
      start_timestamp: _req['start_timestamp'],
      end_timestamp: _req['end_timestamp'],
      top_k: _req['top_k'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PATCH /admin/emergency_transfer */
  UpdateEmergencyTransfer(
    req: admin.UpdateEmergencyTransferRequest,
    options?: T,
  ): Promise<admin.UpdateEmergencyTransferResponse> {
    const _req = req;
    const url = this.genBaseURL('/admin/emergency_transfer');
    const method = 'PATCH';
    const data = {
      function_id: _req['function_id'],
      region: _req['region'],
      resource_type: _req['resource_type'],
      trigger_id: _req['trigger_id'],
      transfer_func_scale_settings: _req['transfer_func_scale_settings'],
      transfer_mq_event_scale_settings:
        _req['transfer_mq_event_scale_settings'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /v2/mcp/servers/:server_id/acl */
  UpdateMcpServerAcl(
    req: mcp_server.UpdateMcpServerAclRequest,
    options?: T,
  ): Promise<common.ApiResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mcp/servers/${_req['server_id']}/acl`);
    const method = 'POST';
    const data = {
      add_psms: _req['add_psms'],
      remove_psms: _req['remove_psms'],
      disable_auth: _req['disable_auth'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /v2/mcp/servers/:server_id/acl_bpm */
  CreateMcpServerAclBpmTicket(
    req: mcp_server.CreateMcpServerAclBpmTicketRequest,
    options?: T,
  ): Promise<mcp_server.CreateMcpServerAclBpmTicketResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mcp/servers/${_req['server_id']}/acl_bpm`);
    const method = 'POST';
    const data = {
      add_psms: _req['add_psms'],
      remove_psms: _req['remove_psms'],
      reason: _req['reason'],
      disable_auth: _req['disable_auth'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/mcp/servers/:server_id/acl */
  GetMcpServerAcl(
    req: mcp_server.GetMcpServerAclRequest,
    options?: T,
  ): Promise<mcp_server.GetMcpServerAclResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mcp/servers/${_req['server_id']}/acl`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/mcp/bam/:psm/versions */
  ListBamVersions(
    req: mcp_tool.BamVersionListRequest,
    options?: T,
  ): Promise<mcp_tool.BamVersionListResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/mcp/bam/${_req['psm']}/versions`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /v1/order_config */
  GetOrderConfig(
    req: other.GetOrderConfigRequest,
    options?: T,
  ): Promise<other.GetOrderConfigResp> {
    const _req = req;
    const url = this.genBaseURL('/v1/order_config');
    const method = 'POST';
    const data = {
      env: _req['env'],
      function_id: _req['function_id'],
      parent_node_id: _req['parent_node_id'],
      psm: _req['psm'],
      service_id: _req['service_id'],
      region: _req['region'],
      cluster: _req['cluster'],
      mq_event: _req['mq_event'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /v2/sandboxes/:sandbox_id
   *
   * Get details of a specific sandbox by its ID
   */
  GetAiSandbox(
    req: sandbox.GetAiSandboxRequest,
    options?: T,
  ): Promise<sandbox.GetAiSandboxResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/sandboxes/${_req['sandbox_id']}`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /v2/sandboxes
   *
   * List sandboxes with filtering and pagination
   */
  ListAiSandboxes(
    req?: sandbox.ListAiSandboxesRequest,
    options?: T,
  ): Promise<sandbox.ListAiSandboxesResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/sandboxes');
    const method = 'GET';
    const params = {
      search: _req['search'],
      owner: _req['owner'],
      subscriber: _req['subscriber'],
      offset: _req['offset'],
      limit: _req['limit'],
      advanced_search: _req['advanced_search'],
      sort_by: _req['sort_by'],
      search_type: _req['search_type'],
      search_fields: _req['search_fields'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /v2/sandboxes/:sandbox_id
   *
   * Delete a specific sandbox by its ID
   */
  DeleteAiSandbox(
    req: sandbox.GetAiSandboxRequest,
    options?: T,
  ): Promise<sandbox.DeleteAiSandboxResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/sandboxes/${_req['sandbox_id']}`);
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /**
   * POST /v2/sandboxes
   *
   * Create a new sandbox with the given configuration
   */
  CreateAiSandbox(
    req: sandbox.CreateAiSandboxRequest,
    options?: T,
  ): Promise<sandbox.CreateAiSandboxResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/sandboxes');
    const method = 'POST';
    const data = {
      psm: _req['psm'],
      name: _req['name'],
      type: _req['type'],
      description: _req['description'],
      psm_parent_id: _req['psm_parent_id'],
      owner: _req['owner'],
      region: _req['region'],
      runtime: _req['runtime'],
      format_envs: _req['format_envs'],
      admins: _req['admins'],
      authorizers: _req['authorizers'],
      service_level: _req['service_level'],
      service_purpose: _req['service_purpose'],
      source_type: _req['source_type'],
      source: _req['source'],
      mcp_server_id: _req['mcp_server_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PATCH /v2/sandboxes/:sandbox_id
   *
   * Update the configuration of a specific sandbox by its ID
   */
  UpdateAiSandbox(
    req: sandbox.UpdateAiSandboxRequest,
    options?: T,
  ): Promise<sandbox.UpdateAiSandboxResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/sandboxes/${_req['sandbox_id']}`);
    const method = 'PATCH';
    const data = {
      name: _req['name'],
      description: _req['description'],
      owner: _req['owner'],
      mcp_server_id: _req['mcp_server_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/mcp/servers/:server_id/sse_token */
  GenerateMcpServerSseToken(
    req: mcp_server.GenerateMcpServerSseTokenRequest,
    options?: T,
  ): Promise<mcp_server.GenerateMcpServerSseTokenResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/mcp/servers/${_req['server_id']}/sse_token`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/mcp/tools */
  GlobalSearchMcpTools(
    req?: mcp_tool.GlobalSearchMcpToolsRequest,
    options?: T,
  ): Promise<mcp_tool.GlobalSearchMcpToolsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/mcp/tools');
    const method = 'GET';
    const params = {
      all: _req['all'],
      env: _req['env'],
      limit: _req['limit'],
      name: _req['name'],
      offset: _req['offset'],
      owner: _req['owner'],
      search: _req['search'],
      search_type: _req['search_type'],
      sort_by: _req['sort_by'],
      search_fields: _req['search_fields'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /v2/resource_groups/:group_id
   *
   * Get the configuration of a resource group by its ID
   */
  GetResourceGroup(
    req: resource_group.GetResourceGroupRequest,
    options?: T,
  ): Promise<resource_group.ResourceGroupResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/resource_groups/${_req['group_id']}`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /v2/resource_groups
   *
   * Get all resource groups
   */
  GetAllResourceGroups(
    req?: resource_group.GetAllResourceGroupsRequest,
    options?: T,
  ): Promise<resource_group.GetAllResourceGroupsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/resource_groups');
    const method = 'GET';
    const params = { region: _req['region'], zone: _req['zone'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PATCH /v2/resource_groups/:group_id/function_list
   *
   * Update the configuration of a resource group by its ID
   */
  UpdateResourceGroup(
    req: resource_group.UpdateResourceGroupRequest,
    options?: T,
  ): Promise<resource_group.ResourceGroupResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/resource_groups/${_req['group_id']}/function_list`,
    );
    const method = 'PATCH';
    const data = {
      disable_transfer: _req['disable_transfer'],
      functions_operation: _req['functions_operation'],
      function_list: _req['function_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /v2/mcp/servers/:server_id/tools/ratelimit */
  UpdateMcpRateLimitRule(
    req: mcp_tool.UpdateMcpRateLimitRulesRequest,
    options?: T,
  ): Promise<common.ApiResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/mcp/servers/${_req['server_id']}/tools/ratelimit`,
    );
    const method = 'POST';
    const data = {
      rules: _req['rules'],
      reason: _req['reason'],
      review_assignee: _req['review_assignee'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /v2/mcp/servers/:server_id/tools/ratelimit_bpm */
  CreateMcpRateLimitRuleBpmTicket(
    req: mcp_tool.UpdateMcpRateLimitRulesRequest,
    options?: T,
  ): Promise<mcp_tool.CreateMcpRateLimitRuleBpmTicketResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/mcp/servers/${_req['server_id']}/tools/ratelimit_bpm`,
    );
    const method = 'POST';
    const data = {
      rules: _req['rules'],
      reason: _req['reason'],
      review_assignee: _req['review_assignee'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/mcp/servers/:server_id/tools/ratelimit_bpm */
  GetMcpRateLimitRuleBpmTicket(
    req: mcp_tool.GetMcpRateLimitRuleBpmTicketRequest,
    options?: T,
  ): Promise<mcp_tool.GetMcpRateLimitRuleBpmTicketResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/mcp/servers/${_req['server_id']}/tools/ratelimit_bpm`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /v2/sandboxes/:sandbox_id/acl */
  GetAiSandboxAcl(
    req: sandbox.GetAiSandboxAclRequest,
    options?: T,
  ): Promise<sandbox.GetAiSandboxAclResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/sandboxes/${_req['sandbox_id']}/acl`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /v2/sandboxes/:sandbox_id/acl_bpm
   *
   * Create a BPM ticket for managing access control lists (ACL) of an AI sandbox
   */
  CreateAiSandboxAclBpmTicket(
    req: sandbox.CreateAiSandboxAclBpmTicketRequest,
    options?: T,
  ): Promise<sandbox.CreateAiSandboxAclBpmTicketResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/sandboxes/${_req['sandbox_id']}/acl_bpm`);
    const method = 'POST';
    const data = {
      allowed_rules: _req['allowed_rules'],
      blocked_rules: _req['blocked_rules'],
      reason: _req['reason'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /v2/sandboxes/:sandbox_id/acl */
  UpdateAiSandboxAcl(
    req: sandbox.UpdateAiSandboxAclRequest,
    options?: T,
  ): Promise<common.ApiResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/sandboxes/${_req['sandbox_id']}/acl`);
    const method = 'POST';
    const data = {
      allowed_rules: _req['allowed_rules'],
      blocked_rules: _req['blocked_rules'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v2/mcp/servers/:server_id/custom_tools */
  ListMcpCustomTools(
    req: mcp_tool.ListMcpCustomToolsRequest,
    options?: T,
  ): Promise<mcp_tool.ListMcpCustomToolsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/mcp/servers/${_req['server_id']}/custom_tools`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /v2/codeagent_cp/projects
   *
   * @title createProject
   *
   * Create a Project in Genius API
   */
  createProject(
    req: codeagent_cp.CreateProjectRequest,
    options?: T,
  ): Promise<codeagent_cp.CreateProjectResponse> {
    const _req = req;
    const url = this.genBaseURL('/v2/codeagent_cp/projects');
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      code_type: _req['code_type'],
      language: _req['language'],
      psm: _req['psm'],
      bytetree_node_id: _req['bytetree_node_id'],
      admins: _req['admins'],
      api_dependencies: _req['api_dependencies'],
      prompt: _req['prompt'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /v2/codeagent_cp/projects/:project_id
   *
   * @title deleteProject
   *
   * Delete a Project in Genius API
   */
  deleteProject(
    req: codeagent_cp.DeleteProjectRequest,
    options?: T,
  ): Promise<codeagent_cp.DeleteProjectResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/codeagent_cp/projects/:project_id`);
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /**
   * GET /v2/codeagent_cp/projects/:project_id
   *
   * @title getProject
   *
   * Get a Project in Genius API
   */
  getProject(
    req: codeagent_cp.GetProjectRequest,
    options?: T,
  ): Promise<codeagent_cp.GetProjectResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/codeagent_cp/projects/${_req['project_id']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /v2/codeagent_cp/projects
   *
   * @title listProjects
   *
   * List Projects in Genius API
   */
  listProjects(
    req?: codeagent_cp.ListProjectsRequest,
    options?: T,
  ): Promise<codeagent_cp.ListProjectsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v2/codeagent_cp/projects');
    const method = 'GET';
    const params = {
      name: _req['name'],
      description: _req['description'],
      code_type: _req['code_type'],
      language: _req['language'],
      psm: _req['psm'],
      page: _req['page'],
      page_size: _req['page_size'],
      id: _req['id'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /v2/codeagent_cp/apis/:psm
   *
   * @title getAPI
   *
   * Get API from the source PSM that can be imported
   */
  getAPI(
    req: codeagent_cp.GetAPIRequest,
    options?: T,
  ): Promise<codeagent_cp.GetAPIResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v2/codeagent_cp/apis/${_req['psm']}`);
    const method = 'GET';
    const params = { version: _req['version'], api_type: _req['api_type'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /v2/codeagent_cp/projects/:project_id
   *
   * @title updateProject
   *
   * Update a Project in Genius API
   */
  updateProject(
    req: codeagent_cp.UpdateProjectRequest,
    options?: T,
  ): Promise<codeagent_cp.UpdateProjectResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/codeagent_cp/projects/${_req['project_id']}`,
    );
    const method = 'PUT';
    const data = {
      name: _req['name'],
      description: _req['description'],
      code_type: _req['code_type'],
      language: _req['language'],
      psm: _req['psm'],
      bytetree_node_id: _req['bytetree_node_id'],
      admins: _req['admins'],
      api_dependencies: _req['api_dependencies'],
      prompt: _req['prompt'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /v2/codeagent_cp/apis/:psm/versions
   *
   * @title listPSMAPIVersions
   *
   * List all PSM API Versions
   */
  listPSMAPIVersions(
    req: codeagent_cp.ListPSMAPIVersionsRequest,
    options?: T,
  ): Promise<codeagent_cp.ListPSMAPIVersionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v2/codeagent_cp/apis/${_req['psm']}/versions`,
    );
    const method = 'GET';
    const params = { api_type: _req['api_type'] };
    return this.request({ url, method, params }, options);
  }
}
/* eslint-enable */
