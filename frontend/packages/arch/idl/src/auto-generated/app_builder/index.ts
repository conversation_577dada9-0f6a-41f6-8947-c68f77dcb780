/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as app_builder_api from './namespaces/app_builder_api';
import * as base from './namespaces/base';
import * as filebox from './namespaces/filebox';
import * as plugin_common from './namespaces/plugin_common';
import * as plugin_develop_common from './namespaces/plugin_develop_common';
import * as starry from './namespaces/starry';
import * as user from './namespaces/user';

export {
  app_builder_api,
  base,
  filebox,
  plugin_common,
  plugin_develop_common,
  starry,
  user,
};
export * from './namespaces/app_builder_api';
export * from './namespaces/base';
export * from './namespaces/filebox';
export * from './namespaces/plugin_common';
export * from './namespaces/plugin_develop_common';
export * from './namespaces/starry';
export * from './namespaces/user';

export type Int64 = string | number;

export default class AppBuilderService<T> {
  private request: any = () => {
    throw new Error('AppBuilderService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * GET /api/agent_app_builder/v1/package/get
   *
   * 获取 package 详情
   */
  GetPackage(
    req?: app_builder_api.GetPackageRequest,
    options?: T,
  ): Promise<app_builder_api.GetPackageResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/package/get');
    const method = 'GET';
    const params = {
      package_name: _req['package_name'],
      version_name: _req['version_name'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/package/list
   *
   * 获取 package 列表
   */
  GetPackageList(
    req?: app_builder_api.GetPackageListRequest,
    options?: T,
  ): Promise<app_builder_api.GetPackageListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/package/list');
    const method = 'GET';
    const params = {
      package_name: _req['package_name'],
      version_name: _req['version_name'],
      page: _req['page'],
      size: _req['size'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/package/delete
   *
   * 删除 package 信息
   */
  DeletePackage(
    req?: app_builder_api.DeletePackageRequest,
    options?: T,
  ): Promise<app_builder_api.DeletePackageResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/package/delete');
    const method = 'POST';
    const data = {
      package_name: _req['package_name'],
      version_name: _req['version_name'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/package/create
   *
   * 创建 / 注册 / 修改 package 信息
   */
  CreatePackage(
    req: app_builder_api.CreatePackageRequest,
    options?: T,
  ): Promise<app_builder_api.CreatePackageResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_app_builder/v1/package/create');
    const method = 'POST';
    const data = {
      package_name: _req['package_name'],
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      version_name: _req['version_name'],
      meta_title: _req['meta_title'],
      meta_desc: _req['meta_desc'],
      meta_contains: _req['meta_contains'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/app/get
   *
   * 获取 app 详情
   */
  GetAgentApp(
    req?: app_builder_api.GetAgentAppRequest,
    options?: T,
  ): Promise<app_builder_api.GetAgentAppResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/app/get');
    const method = 'GET';
    const params = {
      agent_app_id: _req['agent_app_id'],
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      version_name: _req['version_name'],
      app_id: _req['app_id'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/app/create
   *
   * 创建 / 注册 agent app 信息
   */
  CreateAgentApp(
    req?: app_builder_api.CreateAgentAppRequest,
    options?: T,
  ): Promise<app_builder_api.CreateAgentAppResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/app/create');
    const method = 'POST';
    const data = {
      agent_app_name: _req['agent_app_name'],
      icon: _req['icon'],
      type_source_app_id: _req['type_source_app_id'],
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      AppID: _req['AppID'],
      SandboxID: _req['SandboxID'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/app/publish/list
   *
   * 获取发布历史记录
   */
  GetAgentAppPublishList(
    req?: app_builder_api.GetAgentAppPublishListRequest,
    options?: T,
  ): Promise<app_builder_api.GetAgentAppPublishListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/app/publish/list');
    const method = 'GET';
    const params = {
      publish_id: _req['publish_id'],
      agent_app_id: _req['agent_app_id'],
      agent_app_name: _req['agent_app_name'],
      bot_id: _req['bot_id'],
      space_id: _req['space_id'],
      page: _req['page'],
      size: _req['size'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/app/update
   *
   * 修改 agent app 信息
   */
  UpdateAgentApp(
    req?: app_builder_api.UpdateAgentAppRequest,
    options?: T,
  ): Promise<app_builder_api.UpdateAgentAppResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/app/update');
    const method = 'POST';
    const data = {
      agent_app_id: _req['agent_app_id'],
      agent_app_name: _req['agent_app_name'],
      icon: _req['icon'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/app/publish/create
   *
   * 创建发布
   */
  PublishAgentApp(
    req?: app_builder_api.PublishAgentAppRequest,
    options?: T,
  ): Promise<app_builder_api.PublishAgentAppResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/app/publish/create');
    const method = 'POST';
    const data = {
      agent_app_id: _req['agent_app_id'],
      agent_app_name: _req['agent_app_name'],
      remark: _req['remark'],
      version_name: _req['version_name'],
      page_ids: _req['page_ids'],
      to_doubao: _req['to_doubao'],
      Base: _req['Base'],
    };
    const headers = {
      'X-Space-Id': _req['X-Space-Id'],
      Cookie: _req['Cookie'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/app/preview
   *
   * 预览接口
   */
  PreviewAgentApp(
    req?: app_builder_api.PreviewAgentAppRequest,
    options?: T,
  ): Promise<app_builder_api.PreviewAgentAppResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/app/preview');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    const params = { agent_app_id: _req['agent_app_id'] };
    const headers = {
      'use-builder-psm': _req['use-builder-psm'],
      'X-Space-Id': _req['X-Space-Id'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/starry/proxy
   *
   * 星夜接口代理
   */
  AgentAppBuilderProxy(
    req?: app_builder_api.AgentAppBuilderProxyRequest,
    options?: T,
  ): Promise<app_builder_api.AgentAppBuilderProxyResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/starry/proxy');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/package/batch_get
   *
   * 批量获取 package 详情
   */
  BatchGetPackage(
    req?: app_builder_api.BatchGetPackageListRequest,
    options?: T,
  ): Promise<app_builder_api.BatchGetPackageListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/package/batch_get');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    const params = { query: _req['query'] };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/app/ai_generate_content
   *
   * ai builder
   *
   * ai生成内容
   */
  AIGenerateContent(
    req?: app_builder_api.AIGenerateContentRequest,
    options?: T,
  ): Promise<app_builder_api.AIGenerateContentResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/agent_app_builder/v1/app/ai_generate_content',
    );
    const method = 'POST';
    const data = {
      scene: _req['scene'],
      user_id: _req['user_id'],
      string_variables: _req['string_variables'],
      message_variables: _req['message_variables'],
      Base: _req['Base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/app/gen_agent_app
   *
   * 生成agent app
   */
  GenAgentApp(
    req?: app_builder_api.GenAgentAppRequest,
    options?: T,
  ): Promise<app_builder_api.GenAgentAppResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/app/gen_agent_app');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      sandbox: _req['sandbox'],
      name: _req['name'],
      user_id: _req['user_id'],
      agent_type: _req['agent_type'],
      Base: _req['Base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/app/update_agent_bot
   *
   * 更新bot
   */
  UpdateAgentBot(
    req?: app_builder_api.UpdateAgentBotRequest,
    options?: T,
  ): Promise<app_builder_api.UpdateAgentBotResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/agent_app_builder/v1/app/update_agent_bot',
    );
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      bot_workflow_list: _req['bot_workflow_list'],
      system_prompt: _req['system_prompt'],
      user_id: _req['user_id'],
      agent_workflow_list: _req['agent_workflow_list'],
      table_ids: _req['table_ids'],
      Base: _req['Base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/starry/page/get
   *
   * Starry GetPage
   */
  StarryGetPage(
    req?: app_builder_api.StarryGetPageRequest,
    options?: T,
  ): Promise<app_builder_api.StarryGetPageResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/starry/page/get');
    const method = 'GET';
    const params = {
      sandbox_id: _req['sandbox_id'],
      page_id: _req['page_id'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/starry/compsets/get
   *
   * Starry GetCompSetsAndPackageDetail
   */
  StarryGetCompSets(
    req?: app_builder_api.StarryGetCompSetsRequest,
    options?: T,
  ): Promise<app_builder_api.StarryGetCompSetsResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/agent_app_builder/v1/starry/compsets/get',
    );
    const method = 'GET';
    const params = { Base: _req['Base'] };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/starry/snapshot/create
   *
   * Starry CreateSnapshot
   */
  StarryCreateSnapshot(
    req?: app_builder_api.StarryCreateSnapshotRequest,
    options?: T,
  ): Promise<app_builder_api.StarryCreateSnapshotResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/agent_app_builder/v1/starry/snapshot/create',
    );
    const method = 'POST';
    const data = {
      sandbox_id: _req['sandbox_id'],
      desc: _req['desc'],
      page_ids: _req['page_ids'],
      previous_snapshot_id: _req['previous_snapshot_id'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/starry/page/clone
   *
   * Starry ClonePage
   */
  StarryClonePage(
    req: app_builder_api.StarryClonePageRequest,
    options?: T,
  ): Promise<app_builder_api.StarryClonePageResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_app_builder/v1/starry/page/clone');
    const method = 'POST';
    const data = {
      sandbox_id: _req['sandbox_id'],
      app_id: _req['app_id'],
      page_id: _req['page_id'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/starry/page/create
   *
   * Starry CreatePage
   */
  StarryCreatePage(
    req?: app_builder_api.StarryCreatePageRequest,
    options?: T,
  ): Promise<app_builder_api.StarryCreatePageResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/starry/page/create');
    const method = 'POST';
    const data = {
      sandbox_id: _req['sandbox_id'],
      app_id: _req['app_id'],
      page_id: _req['page_id'],
      data: _req['data'],
      exts: _req['exts'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/starry/sandbox/get
   *
   * Starry GetSandbox
   */
  StarryGetSandbox(
    req?: app_builder_api.StarryGetSandboxRequest,
    options?: T,
  ): Promise<app_builder_api.StarryGetSandboxResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/starry/sandbox/get');
    const method = 'GET';
    const params = {
      sandbox_id: _req['sandbox_id'],
      with_app: _req['with_app'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/starry/snapshot/restore
   *
   * Starry RestoreSnapshot
   */
  StarryRestoreSnapshot(
    req: app_builder_api.StarryRestoreSnapshotRequest,
    options?: T,
  ): Promise<app_builder_api.StarryRestoreSnapshotResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/agent_app_builder/v1/starry/snapshot/restore',
    );
    const method = 'POST';
    const data = {
      snapshot_id: _req['snapshot_id'],
      sandbox_id: _req['sandbox_id'],
      action: _req['action'],
      version: _req['version'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/starry/page/delete
   *
   * Starry DeletePage
   */
  StarryDeletePage(
    req?: app_builder_api.StarryDeletePageRequest,
    options?: T,
  ): Promise<app_builder_api.StarryDeletePageResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/starry/page/delete');
    const method = 'POST';
    const data = {
      sandbox_id: _req['sandbox_id'],
      page_id: _req['page_id'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/starry/preview/get
   *
   * Starry GetPreviewData
   */
  StarryGetPreviewData(
    req?: app_builder_api.StarryGetPreviewDataRequest,
    options?: T,
  ): Promise<app_builder_api.StarryGetPreviewDataResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/starry/preview/get');
    const method = 'GET';
    const params = {
      sandbox_id: _req['sandbox_id'],
      page_id: _req['page_id'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/starry/page/update
   *
   * Starry UpdatePage
   */
  StarryUpdatePage(
    req?: app_builder_api.StarryUpdatePageRequest,
    options?: T,
  ): Promise<app_builder_api.StarryUpdatePageResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/starry/page/update');
    const method = 'POST';
    const data = {
      sandbox_id: _req['sandbox_id'],
      page_id: _req['page_id'],
      crdt_history: _req['crdt_history'],
      action: _req['action'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/starry/snapshot/get
   *
   * Starry GetSnapshot
   */
  StarryGetSnapshot(
    req?: app_builder_api.StarryGetSnapshotRequest,
    options?: T,
  ): Promise<app_builder_api.StarryGetSnapshotResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/agent_app_builder/v1/starry/snapshot/get',
    );
    const method = 'GET';
    const params = {
      snapshot_id: _req['snapshot_id'],
      sandbox_id: _req['sandbox_id'],
      version: _req['version'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/starry/sandbox/update
   *
   * Starry UpdateSandbox
   */
  StarryUpdateSandbox(
    req?: app_builder_api.StarryUpdateSandboxRequest,
    options?: T,
  ): Promise<app_builder_api.StarryUpdateSandboxResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/agent_app_builder/v1/starry/sandbox/update',
    );
    const method = 'POST';
    const data = {
      sandbox_id: _req['sandbox_id'],
      crdt_history: _req['crdt_history'],
      action: _req['action'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/app/publish/get
   *
   * 获取发布结果
   */
  GetAgentAppPublish(
    req?: app_builder_api.GetAgentAppPublishRequest,
    options?: T,
  ): Promise<app_builder_api.GetAgentAppPublishResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/app/publish/get');
    const method = 'GET';
    const params = {
      publish_id: _req['publish_id'],
      app_id: _req['app_id'],
      bot_id: _req['bot_id'],
      with_max_version: _req['with_max_version'],
      agent_app_id: _req['agent_app_id'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/plugin/do_action
   *
   * CreateAppResponse CreateApp(1:CreateAppRequest req)                                             (agw.method = 'POST',   agw.uri = '/api/app_builder/v1/app/create')                         // 创建 / 修改 App Widget
   *
   * GetAppResponse GetApp(1:GetAppRequest req)                                                      (agw.method = 'GET',    agw.uri = '/api/app_builder/v1/app/get')                            // 获取 App / Widget 详情
   *
   * GetAppListResponse GetAppList(1:GetAppListRequest req)                                          (agw.method = 'GET',    agw.uri = '/api/app_builder/v1/app/list')                           // 获取 App / Widget 列表
   *
   * PublishAppResponse PublishApp(1:PublishAppRequest req)                                          (agw.method = 'POST',   agw.uri = '/api/app_builder/v1/app/publish/create')                 // 创建发布
   *
   * GetPublishResponse GetAppPublish(1:GetPublishRequest req)                                       (agw.method = 'GET',    agw.uri = '/api/app_builder/v1/app/publish/get')                    // 获取发布结果
   *
   * GetPublishListResponse GetAppPublishList(1:GetPublishListRequest req)                           (agw.method = 'GET',    agw.uri = '/api/app_builder/v1/app/publish/list')                   // 获取发布历史记录
   *
   * PublishCallBackResponse PublishCallBack(1:PublishCallBackRequest req)                           (agw.method = 'POST',   agw.uri = '/api/app_builder/v1/app/publish/callback')               // 获取发布结果
   *
   * RequestGPTResponse RequestGPT(1:RequestGPTRequest req)                                          (agw.method = 'POST',   agw.uri = '/api/app_builder/v1/gpt/request')                        // 请求 Chat-GPT
   *
   * RequestCozeResponse RequestCoze(1:RequestCozeRequest req)                                       (agw.method = 'POST',   agw.uri = '/api/app_builder/v1/coze/request')                       // 请求 Coze Bot
   *
   * 请求 plugin
   */
  DoAction(
    req: app_builder_api.DoActionRequest,
    options?: T,
  ): Promise<app_builder_api.DoActionResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_app_builder/v1/plugin/do_action');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      user_id: _req['user_id'],
      parameters: _req['parameters'],
      message_id: _req['message_id'],
      plugin_name: _req['plugin_name'],
      device_id: _req['device_id'],
      ext: _req['ext'],
      output_token_limit: _req['output_token_limit'],
      section_id: _req['section_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/upload_file
   *
   * Filebox 相关
   *
   * 上传资源
   */
  UploadFile(
    req?: app_builder_api.UploadFileRequest,
    options?: T,
  ): Promise<app_builder_api.UploadFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/upload_file');
    const method = 'POST';
    const data = {
      file_name: _req['file_name'],
      source_uri: _req['source_uri'],
      bot_id: _req['bot_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/agent_app_builder/v1/summary_file */
  SummaryFile(
    req?: app_builder_api.SummaryFileRequest,
    options?: T,
  ): Promise<app_builder_api.SummaryFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/summary_file');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      file_key: _req['file_key'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/app/add_database
   *
   * 新增database
   */
  AddDatabase(
    req?: app_builder_api.AddDatabaseRequest,
    options?: T,
  ): Promise<app_builder_api.AddDatabaseResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_app_builder/v1/app/add_database');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      user_id: _req['user_id'],
      table_list: _req['table_list'],
      Base: _req['Base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/plugin/update
   *
   * 修改 local plugin
   */
  UpdateLocalPlugin(
    req: app_builder_api.UpdateLocalPluginRequest,
    options?: T,
  ): Promise<app_builder_api.UpdateLocalPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_app_builder/v1/plugin/update');
    const method = 'POST';
    const data = {
      agent_app_id: _req['agent_app_id'],
      ai_plugin: _req['ai_plugin'],
      openapi: _req['openapi'],
      client_id: _req['client_id'],
      client_secret: _req['client_secret'],
      service_token: _req['service_token'],
      Base: _req['Base'],
    };
    const headers = {
      'X-Space-Id': _req['X-Space-Id'],
      Cookie: _req['Cookie'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/plugin/create
   *
   * 创建 / 注册 local plugin
   */
  CreateLocalPlugin(
    req: app_builder_api.CreateLocalPluginRequest,
    options?: T,
  ): Promise<app_builder_api.CreateLocalPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_app_builder/v1/plugin/create');
    const method = 'POST';
    const data = {
      agent_app_id: _req['agent_app_id'],
      ai_plugin: _req['ai_plugin'],
      openapi: _req['openapi'],
      Base: _req['Base'],
    };
    const headers = {
      'X-Space-Id': _req['X-Space-Id'],
      Cookie: _req['Cookie'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/agent_app_builder/v1/package/update
   *
   * 修改 package 信息
   */
  UpdatePackage(
    req: app_builder_api.UpdatePackageRequest,
    options?: T,
  ): Promise<app_builder_api.UpdatePackageResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_app_builder/v1/package/update');
    const method = 'POST';
    const data = {
      package_id: _req['package_id'],
      package_name: _req['package_name'],
      version_name: _req['version_name'],
      meta_title: _req['meta_title'],
      meta_desc: _req['meta_desc'],
      meta_contains: _req['meta_contains'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/package/version/list
   *
   * 获取 package version
   */
  GetPackageVersionList(
    req?: app_builder_api.GetPackageVersionListRequest,
    options?: T,
  ): Promise<app_builder_api.GetPackageVersionListResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/agent_app_builder/v1/package/version/list',
    );
    const method = 'GET';
    const params = {
      package_name: _req['package_name'],
      page: _req['page'],
      size: _req['size'],
      Base: _req['Base'],
    };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /api/agent_app_builder/v1/get_file_meta_info */
  GetFileMetaInfo(
    req: app_builder_api.GetFileMetaInfoRequest,
    options?: T,
  ): Promise<app_builder_api.GetFileMetaInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_app_builder/v1/get_file_meta_info');
    const method = 'GET';
    const params = {
      bot_id: _req['bot_id'],
      file_id: _req['file_id'],
      file_uri: _req['file_uri'],
      file_name: _req['file_name'],
      biz_type: _req['biz_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/get_file_list
   *
   * 文件 list 功能
   */
  GetFileList(
    req: app_builder_api.GetFileListRequest,
    options?: T,
  ): Promise<app_builder_api.GetFileListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_app_builder/v1/get_file_list');
    const method = 'GET';
    const params = {
      bot_id: _req['bot_id'],
      file_name: _req['file_name'],
      md_type: _req['md_type'],
      file_format: _req['file_format'],
      begin_time: _req['begin_time'],
      end_time: _req['end_time'],
      page: _req['page'],
      size: _req['size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/search_file
   *
   * 文件 search
   */
  SearchFile(
    req: app_builder_api.SearchFileRequest,
    options?: T,
  ): Promise<app_builder_api.SearchFileResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_app_builder/v1/search_file');
    const method = 'GET';
    const params = {
      bot_id: _req['bot_id'],
      query: _req['query'],
      file_uri_list: _req['file_uri_list'],
      file_name_list: _req['file_name_list'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/get_list_file_chunk
   *
   * 获取单文件 Slice
   */
  GetListFileChunk(
    req: app_builder_api.GetListFileChunkRequest,
    options?: T,
  ): Promise<app_builder_api.GetListFileChunkResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/agent_app_builder/v1/get_list_file_chunk',
    );
    const method = 'GET';
    const params = {
      bot_id: _req['bot_id'],
      biz_type: _req['biz_type'],
      file_id: _req['file_id'],
      file_uri: _req['file_uri'],
      start_chunk_seq_id: _req['start_chunk_seq_id'],
      end_chunk_seq_id: _req['end_chunk_seq_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/file/url
   *
   * 获取 file URL
   */
  GetFileURL(
    req: app_builder_api.GetFileURLRequest,
    options?: T,
  ): Promise<app_builder_api.GetFileURLResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_app_builder/v1/file/url');
    const method = 'GET';
    const params = { uri: _req['uri'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agent_app_builder/v1/starry/sandbox/get_snapshot
   *
   * Starry GetSandboxSnapshot
   */
  StarryGetSandboxSnapshot(
    req?: app_builder_api.StarryGetSandboxSnapshotRequest,
    options?: T,
  ): Promise<app_builder_api.StarryGetSandboxSnapshotResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/agent_app_builder/v1/starry/sandbox/get_snapshot',
    );
    const method = 'GET';
    const params = { sandbox_id: _req['sandbox_id'], Base: _req['Base'] };
    const headers = { 'X-Space-Id': _req['X-Space-Id'] };
    return this.request({ url, method, params, headers }, options);
  }
}
/* eslint-enable */
