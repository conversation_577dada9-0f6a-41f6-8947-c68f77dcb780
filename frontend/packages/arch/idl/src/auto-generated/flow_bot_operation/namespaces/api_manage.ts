/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as common from './common';
import * as open_api from './open_api';

export type Int64 = string | number;

export enum APIStatus {
  Testing = 1,
  Online = 2,
}

export enum District {
  Release = 1,
  Inhouse = 2,
  PPE = 3,
}

export enum FieldType {
  String = 1,
  Int = 2,
  Array = 3,
  Float = 4,
  Object = 5,
}

export enum NameSpace {
  BotPlatform = 1,
  Plugin = 2,
  MarketPlace = 3,
  DataEngine = 4,
  Workflow = 5,
  DevOps = 6,
  CardBuilder = 7,
  Engine = 8,
}

export enum OpenAPISource {
  /** 开放平台 */
  Open = 1,
  /** coze */
  Coze = 2,
}

export enum PermissionScope {
  Workspace = 0,
  Account = 1,
  Enterprise = 2,
}

export enum PermissionType {
  /** 普通权限 */
  Normal = 0,
  /** 渠道使用 */
  Connector = 1,
}

export enum PrincipleType {
  /** deprecated */
  PAT = 1,
  API = 2,
  PATAndAPI = 3,
  /** deprecated */
  BotID = 4,
  /** 对空间(space_id)的限流，将来从 record 迁移过来 */
  PerWorkspace = 5,
  /** 特殊限流, 优先级高于默认限流(会覆盖除 global rate limit(API=2) 之外的限流)
针对特定 user_id 的特殊限流配置 */
  SpecialUser = 100,
  /** 针对特定 space_id 的特殊限流配置 */
  SpecialWorkspace = 101,
  /** 针对特定企业 enterprise_id 的特殊限流配置 */
  SpecialEnterprise = 102,
}

/** 定义同 flow_bot_developer_backend open_api.thrift */
export enum TrafficType {
  /** 默认流量 */
  Default = 0,
  /** 专业版 */
  Professional = 1,
  /** 企业版 */
  Enterprise = 2,
}

export enum VisibleStatus {
  Open = 1,
  Close = 2,
}

export interface APIItem {
  id?: string;
  /** 接口psm */
  psm?: string;
  /** 接口路径 */
  api_path?: string;
  /** 开放来源配置 */
  source_config?: Array<SourceConfig>;
  /** 命名空间 */
  name_space?: string;
  /** api文档 */
  api_doc?: string;
  /** 限流方案 */
  limit_rule?: Array<RequestLimitRule>;
  /** 字段信息 */
  attribute_schema?: AttributeSchemaInfo;
  /** 生效区域 */
  valid_district?: Array<District>;
  /** 绑定的权限信息 */
  permission?: PermissionInfo;
  /** 当前API的状态 1-待上线 2-已上线 */
  status?: APIStatus;
  /** api playground 相关配置 */
  api_playground?: ApiPlayground;
  /** http 方法 */
  http_method?: string;
}

export interface ApiPlayground {
  /** 一级目录，如 Agent, Conversation 等 */
  category?: string;
  /** 接口名，如 Create Agent 等 */
  title?: string;
  /** url key, 对应 playground 页面该接口的 url 的最后一项 */
  url_key?: string;
  /** 生效区域 && 生效版本 */
  available_info?: Array<ApiPlaygroundAvailableInfo>;
  /** 这个接口所有的版本 */
  version_list?: Array<ApiPlaygroundVersionInfo>;
  /** 所有可选的一级目录 */
  all_categories?: Array<string>;
}

export interface ApiPlaygroundAvailableInfo {
  /** 是否生效 */
  available?: boolean;
  /** 版本 */
  version?: string;
  /** boe/ppe/inhouse/release */
  zone_name: string;
}

export interface ApiPlaygroundVersionInfo {
  version?: string;
  /** 这个版本创建的时间（秒时间戳） */
  create_time_unix?: Int64;
}

export interface AttributeSchemaInfo {
  /** 字段id */
  field_id?: number;
  /** 是否选填 */
  optional_type?: boolean;
  name?: string;
  /** 字段类型 */
  field_type?: FieldType;
  /** 如果是array类型，这里返回一个元素，没有field_id，没有name 只有type */
  children?: Array<AttributeSchemaInfo>;
  /** 如果是object类型，这里返回一组字段，有id和name */
  object_struct?: Array<AttributeSchemaInfo>;
  /** 是否可以对用户可见 */
  visibility?: boolean;
  /** 描述 */
  description?: string;
  /** 原始类型 例如int类型字段分为int32 int64 */
  raw_type?: string;
}

export interface BindApiPermissionReq {
  permission_id: string;
  api_id: string;
  Base?: base.Base;
}

export interface BindApiPermissionResp {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
  BaseResp?: base.BaseResp;
}

export interface CreateAPIDocumentRequest {
  /** API id */
  id?: string;
  Base?: base.Base;
}

export interface CreateAPIDocumentResponse {
  code?: number;
  msg?: string;
  data?: string;
  BaseResp?: base.BaseResp;
}

export interface CreatePSMRequest {
  /** namespace名字 */
  ns_name?: string;
  /** psm */
  psm?: string;
  /** 分支 */
  branch?: string;
  Base?: base.Base;
}

export interface CreatePSMResponse {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
  BaseResp?: base.BaseResp;
}

/** 创建限流规则-运营后台api */
export interface CreateRateLimitOperationReq {
  rate_limit: open_api.RateLimitConf;
  Base?: base.Base;
}

export interface CreateRateLimitOperationResp {
  BaseResp?: base.BaseResp;
}

export interface DeletePermissionReq {
  permission_info: PermissionInfo;
  Base?: base.Base;
}

export interface DeletePermissionResp {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
  BaseResp?: base.BaseResp;
}

export interface GetAPIDetailByIdReq {
  api_id: string;
  Base?: base.Base;
}

export interface GetAPIDetailByIdResp {
  code?: number;
  msg?: string;
  data?: APIItem;
  BaseResp?: base.BaseResp;
}

export interface GetAPIListData {
  data?: Array<APIItem>;
  total?: number;
}

export interface GetAPIListRequest {
  /** 第几页 */
  page?: number;
  /** 页容量 */
  page_size?: number;
  /** 命名空间 */
  name_space?: string;
  /** 接口psm */
  psm?: string;
  /** 接口路径 */
  api_path?: string;
  /** 方法名称 */
  function_name?: string;
  /** 开放来源 */
  source?: OpenAPISource;
  /** API id */
  api_id?: string;
  /** 生效区域 */
  valid_district?: Array<District>;
  /** 接口的状态 */
  status?: APIStatus;
  Base?: base.Base;
}

export interface GetAPIListResponse {
  code?: number;
  msg?: string;
  data?: GetAPIListData;
  BaseResp?: base.BaseResp;
}

export interface GetPermissionListByParentIdData {
  data?: Array<PermissionInfo>;
  total?: number;
}

export interface GetPermissionListByParentIdReq {
  /** 如果是一级填0 */
  parent_id: string;
  page_no?: number;
  page_size?: number;
  Base?: base.Base;
}

export interface GetPermissionListByParentIdResp {
  code?: number;
  msg?: string;
  data?: GetPermissionListData;
  BaseResp?: base.BaseResp;
}

export interface GetPermissionListData {
  data?: Array<PermissionInfo>;
  total?: number;
}

export interface GetPermissionListReq {
  key_name?: string;
  permission_id?: string;
  permission_type?: PermissionType;
  page_no?: number;
  page_size?: number;
  Base?: base.Base;
}

export interface GetPermissionListResp {
  code?: number;
  msg?: string;
  data?: GetPermissionListData;
  BaseResp?: base.BaseResp;
}

/** 限流-运营后台 api
 只会返回运营后台配置的限流, 由权益提升的限流配置不会返回 */
export interface GetRateLimitOperationReq {
  path: string;
  http_method: string;
  Base?: base.Base;
}

export interface GetRateLimitOperationResp {
  /** 全局限流 */
  global_limit_list?: Array<open_api.RateLimitConf>;
  /** 基础限流 */
  base_limit_list?: Array<open_api.RateLimitConf>;
  /** 扩容的限流 */
  expand_limit_list?: Array<open_api.RateLimitConf>;
  BaseResp?: base.BaseResp;
}

export interface InitAttributeInfoData {
  data?: Array<AttributeSchemaInfo>;
}

export interface InitAttributeInfoReq {
  api_id?: string;
  Base?: base.Base;
}

export interface InitAttributeInfoResp {
  code?: number;
  msg?: string;
  data?: InitAttributeInfoData;
  BaseResp?: base.BaseResp;
}

export interface PermissionInfo {
  permission_id?: string;
  key?: string;
  description?: string;
  /** 展示名称 */
  display_name?: string;
  /** 子权限点 */
  childrens?: Array<PermissionInfo>;
  /** release是否可见 */
  release_status?: VisibleStatus;
  /** inhouse是否可见 */
  inhouse_status?: VisibleStatus;
  create_time?: string;
  update_time?: string;
  parent_id?: string;
  /** ppe是否可见 */
  ppe_status?: VisibleStatus;
  permission_type?: PermissionType;
  /** 是否是空间相关资源权限 */
  permission_scope?: PermissionScope;
  /** 完整的permission_key；新key字段，打破原先的负责节点拼接得到key的关系 */
  full_permission_key?: string;
}

/** 删除限流规则-运营后台api */
export interface RemoveRateLimitOperationReq {
  /** create 拿到的 id */
  id: string;
  Base?: base.Base;
}

export interface RemoveRateLimitOperationResp {
  BaseResp?: base.BaseResp;
}

export interface RequestLimitRule {
  /** 主体类型 */
  type?: PrincipleType;
  /** 间隔时间 单位s */
  duration?: number;
  /** 限制次数 */
  limit_count?: number;
  /** 普通版/专业版/企业版标识 */
  traffic_type?: TrafficType;
  /** 当 PrincipleType 为特殊限流时，需要传入的特殊限流 id(对应 user_id, space_id, org_id) */
  special_id?: string;
  /** 开始时间（仅对特殊限流有效，单位: 秒） */
  start_time_unix?: Int64;
  /** 结束时间（仅对特殊限流有效，单位: 秒） */
  end_time_unix?: Int64;
  /** 备注信息, 可以写一写关键信息，例如飞书联系人，群号等 */
  remark?: string;
}

export interface SetAPIRequestLimitRuleReq {
  api_id?: string;
  limit_rule?: Array<RequestLimitRule>;
  Base?: base.Base;
}

export interface SetAPIRequestLimitRuleResp {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
  BaseResp?: base.BaseResp;
}

export interface SetAttributeInfoReq {
  api_id?: string;
  attribute_schema?: Array<AttributeSchemaInfo>;
  Base?: base.Base;
}

export interface SetAttributeInfoResp {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
  BaseResp?: base.BaseResp;
}

export interface SourceConfig {
  source?: OpenAPISource;
  visible?: boolean;
}

export interface UpdateAPIDocRequest {
  /** API id */
  id?: string;
  /** 文档内容 */
  doc_content?: string;
  Base?: base.Base;
}

export interface UpdateAPIDocResponse {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
  BaseResp?: base.BaseResp;
}

export interface UpdateApiPlaygroundReq {
  api_id?: string;
  api_playground?: ApiPlayground;
}

export interface UpdateApiPlaygroundResp {
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface UpdateAPIVisibleRequest {
  /** API id */
  id?: string;
  /** 来源 */
  source?: OpenAPISource;
  /** 开启 true 关闭 false */
  open?: boolean;
  /** 生效区域 */
  valid_district?: Array<District>;
  Base?: base.Base;
}

export interface UpdateAPIVisibleResponse {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
  BaseResp?: base.BaseResp;
}

/** 更新限流规则-运营后台api
 entity id 就不让改了，如果想改的话就在平台上新建一条，然后删掉这一条 */
export interface UpdateRateLimitOperationReq {
  id: string;
  /** 间隔时间 单位s */
  duration?: number;
  /** 限制次数 */
  limit_count?: number;
  /** 生效的时间戳（秒） */
  valid_time_start_unix?: Int64;
  /** 失效的时间戳（秒） */
  valid_time_end_unix?: Int64;
  remark?: string;
  Base?: base.Base;
}

export interface UpdateRateLimitOperationResp {
  BaseResp?: base.BaseResp;
}

export interface UpsertPermissionReq {
  permission_info: PermissionInfo;
  Base?: base.Base;
}

export interface UpsertPermissionResp {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
