/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_entity from './flow_devops_evaluation_entity';
import * as flow_devops_evaluation_object_callback from './flow_devops_evaluation_object_callback';
import * as flow_devops_evaluation_callback_common from './flow_devops_evaluation_callback_common';
import * as flow_devops_evaluation_evaluator_callback from './flow_devops_evaluation_evaluator_callback';
import * as flow_devops_evaluation_manual_annotation from './flow_devops_evaluation_manual_annotation';

export type Int64 = string | number;

/** 聚合方式 */
export enum AggregatorMode {
  Unknown = 0,
  /** 按照评测维度标签聚合 */
  EvaluatorTag = 1,
}

/** 聚合器类型 */
export enum AggregatorType {
  Average = 1,
  Sum = 2,
  Max = 3,
  Min = 4,
  /** 使用double类型来表示百分比。例如50.5%设置为0.505 */
  PassingRate = 5,
  ExcellentRate = 6,
  /** 人工选项打分，count 各选项并给出比例分布 */
  Count = 7,
  Pct50 = 8,
  Pct90 = 9,
  Pct99 = 10,
}

export enum BatchTaskRetryMode {
  All = 1,
  Unsuccessful = 2,
}

export enum BitableStatus {
  Unknown = 0,
  /** 任务未开始 */
  Running = 1,
  /** 任务成功 */
  Success = 2,
  /** 任务失败 */
  Failed = 3,
}

/** ChainTask模版类型 */
export enum ChainTaskTemplate {
  ChainTaskTemplate_Unknow = 0,
  ChainTaskTemplate_BotTemplate = 1,
}

/** 分析图表的类型 */
export enum ChartType {
  Unknown = 0,
  /** 指标卡 */
  KPI = 1,
  /** 饼图 */
  Pie = 2,
  /** 条形图 */
  Bar = 3,
  /** 条形堆积图 */
  StackedBar = 4,
  /** 雷达图 */
  Radar = 5,
  /** 折线图 */
  Line = 6,
}

export enum DataType {
  /** 默认，有小数的浮点数值类型 */
  Double = 0,
  JSON = 1,
}

/** 评估器资源 */
export enum EvaluateMode {
  /** 人工 */
  Manual = 0,
  /** 自动 */
  Auto = 1,
  /** 重置 */
  Reset = 2,
}

/** 关联评估器 */
export enum EvaluateScope {
  /** data row 范围 */
  Row = 0,
  /** 全case默认生效 */
  Case = 1,
}

export enum ExecutionPolicy {
  /** error 停止运行 */
  StopOnAnyError = 1,
  /** error 继续执行后续任务 */
  ContinueOnAnyError = 2,
  /** 部分失败继续执行后续任务 */
  OnlyContinueOnPartialError = 3,
}

export enum ExportCSVSourceType {
  /** 后续可能迁移单报告为服务端导出 */
  EvaluationTaskReport = 1,
  ContrastReport = 2,
}

export enum FilterField {
  Unknown = 0,
  /** 得分 */
  Score = 1,
  /** 数值 */
  Value = 2,
  /** 评测结果option value */
  OptionValue = 3,
  /** 标注 */
  Plaintext = 4,
  /** 数据集标签 */
  DataSetTag = 5,
  /** 评测维度,对应于ruleID */
  RuleID = 6,
}

export enum FilterLogicOp {
  Unknown = 0,
  And = 1,
  Or = 2,
}

export enum FilterOperatorType {
  Unknown = 0,
  /** 等于 */
  Equal = 1,
  /** 不等于 */
  NotEqual = 2,
  /** 包含 */
  Contains = 3,
  /** 不包含 */
  NotContains = 4,
  /** 大于 */
  Greater = 5,
  /** 大于等于 */
  GreaterOrEqual = 6,
  /** 小于 */
  Less = 7,
  /** 小于等于 */
  LessOrEqual = 8,
  /** 空 */
  IsNull = 9,
  /** 非空 */
  IsNotNull = 10,
}

export enum FornaxAgentOpenAPIKey {
  Unknown = 0,
  Invoke = 1,
  Stream = 2,
}

export enum FornaxAgentTmplType {
  Unknown = 0,
  ChatBot = 1,
  OpenAPI = 2,
}

export enum GenAnnotationTaskCustomFilterLanguage {
  Unknown = 0,
  Golang = 1,
  Typescript = 2,
  Python = 3,
}

/** 分组方式 */
export enum GroupMode {
  Unknown = 0,
  /** 数据集标签 */
  DataSetTag = 1,
  /** 按照option结果分组 */
  OptionResult = 2,
}

export enum ManualStatus {
  /** 不需要人工标注 */
  NoNeed = 0,
  /** 需要人工标注 */
  Need = 1,
}

export enum ModelResponseFormat {
  Text = 0,
  Markdown = 1,
  JSON = 2,
}

export enum ModelStyle {
  Custom = 0,
  Creative = 1,
  Balance = 2,
  Precise = 3,
}

/** 评估器资源 */
export enum PromptTemplateFormat {
  PromptTemplateFormat_FString = 0,
  PromptTemplateFormat_Jinja2 = 1,
}

export enum RetryMode {
  All = 1,
  /** 重试未成功的rowGroup */
  Unsuccessful = 2,
  /** 指定rowGroupID重试 */
  SpecifyRowGroup = 3,
}

export enum RowGroupRunState {
  Unknown = -1,
  /** 排队中 */
  Queueing = 0,
  /** 执行中 */
  Processing = 1,
  /** 成功 */
  Success = 2,
  /** 失败 */
  Fail = 3,
  /** 结果待评估 */
  Evaluating = 4,
  /** 终止执行 */
  Terminal = 5,
}

export enum RowRunState {
  /** 未开始执行 */
  Queueing = 0,
  /** 执行成功 */
  Success = 1,
  /** 执行失败 */
  Fail = 2,
}

/** 表单展示元素类型 */
export enum ShowEntityType {
  /** 评测对象coze bot，对应使用 11:CozeEntityMap */
  CozeBot = 1,
  /** 评测对象prompt */
  Prompt = 2,
  /** 用户信息 */
  User = 3,
  /** 注册agent */
  Agent = 4,
}

/** SpecialObjectType 后面可单独在 评估对象管理平台上进行动态注册，注册的会动态分配 */
export enum SpecialObjectType {
  CozeBot = 0,
  Prompt = 1,
  ChainTask = 2,
  /** 接入 Fornax 的应用，Eino 框架默认集成 */
  FornaxApp = 3,
  /** CloudIDE 提供的 FornaxAgent */
  FornaxAgent = 4,
  PlaygroundCozeBot = 5,
  PlaygroundCozeBotV2 = 6,
  /** 结果集批量评测类型 */
  EvalResultDatasetCollection = 7,
  PlaygroundCozeModel = 8,
  CiciCrawl = 9,
  /** 自定义评测规则，idgen id 不可枚举, 此 enum 仅用于搜索&过滤的 request */
  Custom = 100,
}

export enum TaskAggrReportGenStatus {
  /** 未生成，任务未执行完成 */
  NotGenerated = 0,
  /** 更新中 */
  Updating = 1,
  /** 运行时可查看报告详情，完成 后展示整体得分和【查看聚合报告可点击】 */
  Generated = 2,
  /** 完成后如果评分有更新，需要重新聚合计算 */
  NeedUpdate = 3,
}

export enum TaskManualStatus {
  NoNeed = 0,
  Need = 1,
  Completed = 2,
}

export enum TaskMode {
  Unknown = 0,
  /** 平台手动运行Case */
  SubmitExec = 1,
  /** 在线评测场景 */
  OnlineSyncExec = 2,
}

export enum TaskStatus {
  /** 评测任务执行中，前端展示【运行中】 */
  Processing = 1,
  /** 评测任务创建中，初始话rowGroup状态等操作，前端展示【创建中】 */
  Creating = 4,
  /** rowgroup持续评测并添加到任务，在线评测场景中使用 */
  Appending = 10,
  /** 执行完成且需要人工评分，前端展示【需要人工评分】 */
  NeedManualEval = 20,
  /** 用户手动取消运行, 前端展示【已终止，用户取消】 */
  Termination = 21,
  /** 系统主动终止任务，前端展示【已终止: 系统异常】 */
  SystemTermination = 22,
  /** 任务执行完成，前端展示【成功】 */
  Completed = 30,
  /** 执行完成，全部 rowGroup 都执行失败，前端展示【失败】 */
  Error = 31,
  /** 执行完成, 部分rowGroup失败。前端展示【部分失败】 */
  PartialFailure = 41,
  /** 任务已创建,等待任务被调度执行 */
  AwaitExecution = 42,
  /** 等待任务被重试 */
  AwaitRetry = 43,
}

export enum UserChangeMode {
  /** 无限制，用户可读写、运行 */
  Default = 0,
  /** 不可编辑、不可触发运行，可读 */
  ReadOnly = 5,
}

export interface BizError {
  err_msg: string;
  err_code: Int64;
}

export interface ChainTask {
  task_id: string;
  task_name?: string;
  /** 评测时候，chainTask的版本，需要前端传 */
  version?: number;
  model_info?: string;
  chain_task_template?: ChainTaskTemplate;
  prompt_template_format?: PromptTemplateFormat;
  app_id?: string;
}

export interface CiciCrawl {
  bot_id: string;
  name?: string;
  avatar_url?: string;
  crawl_project_id?: string;
}

export interface ColumnRuleInfo {
  rule_id: Int64;
  evaluator_type: Int64;
  /** 自定义评估器名称 */
  evaluator_type_name: string;
  /** 规则名称 */
  name?: string;
  /** 用于对话组粒度规则的合并单元格 */
  granularity?: flow_devops_evaluation_entity.EvaluatorGranularity;
}

export interface CozeAgent {
  AgentID: Int64;
  AgentName: string;
  ModelInfo?: flow_devops_evaluation_entity.ModelInfo;
}

/** 评测CozeBot */
export interface CozeBot {
  bot_id?: Int64;
  /** 默认0， 是draft版本
deprecated */
  is_draft?: number;
  /** 创建case version 为空，task实体会携带当前case run 时候 bot version */
  version?: Int64;
  /** Bot类型，默认0， 是draft版本 */
  bot_info_type?: flow_devops_evaluation_object_callback.CozeBotInfoType;
  /** 创建case connector_id 为空，task实体会携带当前case run 时候 bot connector_id */
  connector_id?: string;
  model_info?: flow_devops_evaluation_entity.ModelInfo;
  bot_name?: string;
  avatar_url?: string;
  env?: string;
  bot_version?: string;
  Agents?: Array<CozeAgent>;
}

export interface DashboardRow {
  /** 数据集结构 */
  row_id: Int64;
  /** 数据集cells（包含input, output, variable FIXME MultiCell上线后，废弃 */
  cells: Array<string>;
  /** 评估报告新增
大模型真实输出 FIXME  MultiOutput上线后废弃 */
  output: Output;
  /** 评估器报告 */
  row_eval_cell: RowEvalCell;
  /** 二级详情rule_group_id
deprecated 不为0时表示该行包括了所有通用规则与行级规则的rule_group_id, */
  rule_group_id: Int64;
  /** 含多模态的输出，兼容字段，全量上线后，废弃Output */
  multi_output?: flow_devops_evaluation_object_callback.Output;
  /** 扩展2号字段，数据集多模态展示 */
  multi_cells?: Array<flow_devops_evaluation_callback_common.Content>;
  /** 轨迹信息 */
  trajectory?: flow_devops_evaluation_evaluator_callback.Trajectory;
  run_state?: RowRunState;
  /** deprecated */
  error_message?: string;
  /** Row执行时关联的logID */
  log_id?: string;
  error?: RowRunError;
  /** 存在行级评估器时,行级评估器的group_id */
  row_rule_group_id?: Int64;
  /** 是否跳转评估对象调用 trace */
  direct_object_trace?: boolean;
}

export interface DashboardRowGroup {
  row_group_id?: Int64;
  group_name?: string;
  rows: Array<DashboardRow>;
  run_state?: RowGroupRunState;
  tags?: Array<string>;
  /** rowGroup粒度评测规则ruleID -> 结果 */
  rule_eval_report_map?: Record<Int64, RowEvalReport>;
  serial_num?: number;
}

export interface EvalObject {
  /** 评测对象的类型。每一个 RPC 接口，视为一种类型 */
  object_type: Int64;
  /** ObjectType=0 时，传参此字段。 评测对象为 CozeBot 时, 需要设置 CozeBot 信息 */
  coze_bot?: CozeBot;
  /** ObjectType=1 时，传参此字段。 评测对象为 EvalPrompt 时, 需要设置 Prompt 信息 */
  prompt?: EvalPrompt;
  /** ObjectType 为其他时，传参此字段 */
  object?: flow_devops_evaluation_object_callback.Object;
  /** ObjectType=2 时，传参此字段。 评测对象为 ChainTask 时, 需要设置 ChainTask 信息 */
  chain_task?: ChainTask;
  fornax_app_object?: FornaxAppObject;
  fornax_agent_object?: FornaxAgentObject;
  playground_coze_bot_v2?: PlaygroundCozeBotV2;
  /** EvalResultDatasetCollection 类型对象数据 */
  eval_result_dataset_collection?: EvalResultDatasetCollection;
  playground_coze_model?: PlaygroundCozeModel;
  cici_crawl?: CiciCrawl;
}

export interface EvalPrompt {
  /** 一个prompt的唯一标识 */
  prompt_id: string;
  /** 评测时候，prompt的版本，需要前端传 */
  version?: string;
  name?: string;
}

export interface EvalResultDatasetCollection {
  items: Array<EvalResultDatasetObject>;
}

export interface EvalResultDatasetObject {
  dataset_id: Int64;
  rule_group_id: Int64;
  dataset_name?: string;
}

export interface EvaluateResult {
  /** 打分 */
  score?: number;
  /** 打分过程与结果相关信息 */
  reasoning?: string;
  /** 是否需要人工打分, 当前rule 没有自动评测结果时候， ManualStatus = ManualStatus */
  manual_status?: ManualStatus;
  /** 评估器错误 */
  error?: RowRunError;
  data?: EvaluateResultData;
  /** Row 维度打分范围，如 Coze 场景下由用户 LLM Prompt 决定评估器输出打分范围 */
  scoring_scope?: flow_devops_evaluation_entity.ScoringScope;
}

export interface EvaluateResultData {
  score?: number;
  value?: string;
  option?: flow_devops_evaluation_entity.EvaluateResultOption;
  plain_text?: string;
  data_type?: flow_devops_evaluation_entity.EvaluateResultDataType;
  value_type?: flow_devops_evaluation_entity.EvaluateResultValueType;
}

export interface FornaxAgentAPI {
  open_api_key?: FornaxAgentOpenAPIKey;
}

export interface FornaxAgentObject {
  agent_id: Int64;
  faas_id: string;
  name?: string;
  avatar_url?: string;
  tmpl_type?: FornaxAgentTmplType;
  /** 评测的目标 api */
  api?: FornaxAgentAPI;
}

export interface FornaxAppObject {
  psm: string;
  env: string;
  cluster: string;
  region: string;
  app_id: string;
  client_id: string;
  /** for 二级搜索 */
  object?: flow_devops_evaluation_object_callback.Object;
}

export interface Output {
  prediction: string;
}

/** Coze2.0Bot */
export interface PlaygroundCozeBotV2 {
  bot_id?: Int64;
  /** 创建case version 为空，task实体会携带当前case run 时候 bot version */
  bot_version?: string;
  /** Bot类型，默认0， 是draft版本 */
  bot_info_type?: flow_devops_evaluation_object_callback.CozeBotInfoType;
  model_info?: flow_devops_evaluation_entity.ModelInfo;
  bot_name?: string;
  avatar_url?: string;
}

/** Coze2.0 model */
export interface PlaygroundCozeModel {
  /** 模型id */
  model_id?: string;
  /** 温度，模型输出随机性，值越大越随机，越小越保守(0-1] */
  temperature?: number;
  /** 回复最大Token数 */
  max_tokens?: number;
  /** 另一种模型的输出随机性，值越大越随机[0,1] */
  top_p?: number;
  /** 生成时，采样候选集的大小 */
  top_k?: number;
  /** 频率惩罚，调整生成内容中的单词频率，正值单词越少见[-1.0,1.0] */
  frequency_penalty?: number;
  /** 存在惩罚，调整生成内容中新词语频率，正值避免重复单词，用新词[-1.0,1.0] */
  presence_penalty?: number;
  /** 模型回复内容格式 */
  response_format?: ModelResponseFormat;
  /** 模型名称 */
  model_name?: string;
}

export interface RowEvalCell {
  /** 人工评分, 仅在 ManualStatus =  Completed 有效 */
  manual_result?: EvaluateResult;
  /** key:rule_id, 行级数据，单个规则的评测结果 */
  rule_eval_report_map?: Record<Int64, RowEvalReport>;
  token?: Int64;
  consuming?: Int64;
  create_time?: Int64;
  end_time?: Int64;
  space_id?: Int64;
  /** 该row所有自动评测rule或人工评分维度按权重聚合后的总分 */
  score?: number;
  rule_eval_reports?: Array<RowEvalReport>;
  row_metrics?: Array<RowEvalReport>;
  manual_annotation_reports?: Array<flow_devops_evaluation_manual_annotation.ManualAnnotationLabelTask>;
}

/** 每个评估器对应的评分 */
export interface RowEvalReport {
  /** TODO:后期需要转为真实的evaluatorName，先暂时用EvaluatorType表示一个评估器 */
  evaluator_type?: Int64;
  row_eval_result?: EvaluateResult;
  /** 评估器的名称 */
  evaluator_type_name?: string;
  weight?: Int64;
  /** 如果是行级规则需要在单独的列展示 */
  is_row_evaluator?: boolean;
  name?: string;
}

export interface RowRunError {
  code: Int64;
  message: string;
  /** for prompt platform */
  detail: string;
  BizError?: BizError;
}

export interface ScoringThreshold {
  /** 及格率阈值 */
  pass_threshold?: number;
  /** 优秀率阈值 */
  excellent_threshold?: number;
}

export interface Task {
  id?: Int64;
  status?: TaskStatus;
  dataset_id?: Int64;
  /** run case时候的评测对象实体，包含版本信息 */
  eval_object?: EvalObject;
  /** 运行的测数据行数 */
  row_run_cnt?: Int64;
  /** 任务整体开销 */
  token?: Int64;
  /** 任务整体耗时 */
  consuming?: Int64;
  /** 任务开始执行时间 */
  start_time?: Int64;
  /** 任务执行完成时间 */
  end_time?: Int64;
  creator_id?: Int64;
  /** 执行本次任务的log_id */
  log_id?: string;
  /** 本次执行失败的原因文案 */
  object_output_err?: string;
  /** 任务统计, RowGroup维度 */
  task_stats?: TaskStats;
  /** 任务人工评分统计, Row维度 */
  task_manual_stats?: TaskManualStats;
  /** 任务人工评分状态 */
  task_manual_status?: TaskManualStatus;
  /** 聚合报告生成状态 */
  aggr_report_gen_status?: TaskAggrReportGenStatus;
  score?: number;
  passing_rate?: number;
  excellent_rate?: number;
  /** 及格优秀阈值 */
  threshold?: ScoringThreshold;
  /** deprecated 评测对象整体 token消耗 */
  object_token_usage?: TokenUsage;
  /** 运行时参数，json序列化 */
  runtime_parameter?: string;
  /** 评测对象整体 token 消耗 */
  object_token_cost?: TokenUsage;
  /** 数据集名称 */
  dataset_name?: string;
  /** 原始数据集ID */
  original_dataset_id?: Int64;
  /** 评估器整体 token 消耗 */
  evaluator_token_usage?: TokenUsage;
  credit_cost?: number;
  description?: string;
}

export interface TaskManualStats {
  needed_row_count?: Int64;
  completed_row_count?: Int64;
  /** 需要group粒度人工评分的对话组数 */
  needed_row_group_count?: Int64;
  /** 已完成group粒度人工评分的对话组数 */
  completed_row_group_count?: Int64;
}

export interface TaskStats {
  uncompleted_count?: Int64;
  success_count?: Int64;
  fail_count?: Int64;
  /** 最终执行成功的 row 数量 */
  success_row_count?: Int64;
  /** 最终执行失败的 row 数量 */
  fail_row_count?: Int64;
  /** 执行过的 row 数量总和 */
  total_row_count?: Int64;
}

export interface TokenUsage {
  /** input token消耗 */
  input_token: Int64;
  /** output token消耗 */
  output_token: Int64;
}
/* eslint-enable */
