# PowerShell script to start frontend development
param(
    [string]$FrontendDir = ""
)

# Set default frontend directory
if ([string]::IsNullOrEmpty($FrontendDir)) {
    $ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $FrontendDir = Join-Path $ScriptDir "..\frontend"
}

$CozeStudioDir = Join-Path $FrontendDir "apps\coze-studio"

Write-Host "Entering frontend build output directory: $CozeStudioDir" -ForegroundColor Green

# Check if dist directory exists and is not empty
$DistDir = Join-Path $CozeStudioDir "dist"
if (!(Test-Path $DistDir) -or (Get-ChildItem $DistDir -ErrorAction SilentlyContinue).Count -eq 0) {
    Write-Host "dist directory does not exist or is empty, initializing environment..." -ForegroundColor Yellow
    
    # Check if Node.js is installed
    Write-Host "正在检查 Node.js 是否已安装..." -ForegroundColor Cyan
    try {
        $nodeVersion = node -v
        Write-Host "Node.js 已安装: $nodeVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "错误: 未检测到 Node.js" -ForegroundColor Red
        Write-Host "请安装 Node.js 后再继续。" -ForegroundColor Yellow
        exit 1
    }

    # Check if Rush is installed
    Write-Host "正在检查 Rush 是否已安装..." -ForegroundColor Cyan
    try {
        $rushVersion = rush version
        Write-Host "Rush 已安装: $rushVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "未检测到 Rush，正在为您安装..." -ForegroundColor Yellow
        npm install -g @microsoft/rush
    }

    Write-Host "环境检查完成！" -ForegroundColor Green
    Write-Host "开始安装依赖..." -ForegroundColor Yellow
    
    Push-Location $FrontendDir
    try {
        rush update
        Write-Host "依赖安装完成！" -ForegroundColor Green
    }
    catch {
        Write-Host "依赖安装失败，请检查网络连接或权限" -ForegroundColor Red
        exit 1
    }
    finally {
        Pop-Location
    }
}
else {
    Write-Host "dist directory exists and is not empty, skipping environment initialization" -ForegroundColor Green
}

Write-Host "Starting frontend service..." -ForegroundColor Green
Push-Location $CozeStudioDir
try {
    $env:WEB_SERVER_PORT = "8888"
    npm run dev
}
finally {
    Pop-Location
}
